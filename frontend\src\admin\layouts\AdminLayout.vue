<template>
  <div class="admin-theme admin-layout">
    <!-- Global Loading Overlay -->
    <global-loading :is-loading="isLoading" :message="loadingMessage" />

    <div class="admin-layout-container">
      <!-- Enhanced Sidebar -->
      <div class="sidebar-wrapper" :class="{ 'sidebar-hidden': !isSidebarVisible }">
        <admin-sidebar :is-visible="isSidebarVisible" @toggle-sidebar="toggleSidebar" />
      </div>

      <!-- Main Content Area -->
      <div class="main-content-wrapper" :class="{ 'sidebar-collapsed': !isSidebarVisible }">
        <admin-header @toggle-sidebar="toggleSidebar" />
        <main class="content-area">
          <div class="content-container">
            <error-boundary>
              <router-view v-slot="{ Component }">
                <transition name="fade" mode="out-in" @before-leave="beforeRouteChange" @after-enter="afterRouteChange">
                  <component :is="Component" />
                </transition>
              </router-view>
            </error-boundary>
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import AdminSidebar from '@/admin/components/Sidebar.vue';
import AdminHeader from '@/admin/components/Header.vue';
import GlobalLoading from '@/components/GlobalLoading.vue';
import ErrorBoundary from '@/components/ErrorBoundary.vue';
import apiService from '@/services/api';

// Store and route
const store = useStore();
const route = useRoute();

// Computed properties for loading state
const isLoading = computed(() => store.getters['loading/isLoading']);
const loadingMessage = computed(() => store.getters['loading/loadingMessage']);

// Sidebar visibility state
const isSidebarVisible = ref(true);

// Toggle sidebar visibility
const toggleSidebar = () => {
  isSidebarVisible.value = !isSidebarVisible.value;
};

// Handle responsive behavior
const handleResponsiveLayout = () => {
  if (window.innerWidth >= 1024) { // Desktop
    isSidebarVisible.value = true;
  } else { // Mobile
    isSidebarVisible.value = false;
  }
};

// Route transition handlers
const beforeRouteChange = () => {
  // Cancel any pending requests related to the current route
  apiService.cancelRequestsForRoute(route.path);

  // Start the loading state for route change
  store.dispatch('loading/startRouteChange');
};

const afterRouteChange = () => {
  // End the loading state for route change
  store.dispatch('loading/finishRouteChange');
};

onMounted(() => {
  window.addEventListener('resize', handleResponsiveLayout);
  handleResponsiveLayout();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResponsiveLayout);

  // Cancel any pending requests when the layout is unmounted
  apiService.cancelAllRequests();
});
</script>

<style scoped>
/* Enhanced Admin Layout */
.admin-layout {
  min-height: 100vh;
  background: #f8fafc;
}

.admin-layout-container {
  display: flex;
  min-height: 100vh;
  position: relative;
}

/* Sidebar Wrapper */
.sidebar-wrapper {
  width: 350px;
  flex-shrink: 0;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.sidebar-wrapper.sidebar-hidden {
  transform: translateX(-100%);
}

/* Main Content Wrapper */
.main-content-wrapper {
  flex: 1;
  margin-left: 350px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  background: #f8fafc;
}

.main-content-wrapper.sidebar-collapsed {
  margin-left: 0;
}

/* Content Area */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-container {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: #f8fafc;
}

/* Route transition animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
  .sidebar-wrapper {
    transform: translateX(-100%);
    z-index: 1100;
  }

  .sidebar-wrapper:not(.sidebar-hidden) {
    transform: translateX(0);
  }

  .main-content-wrapper {
    margin-left: 0;
  }

  /* Overlay for mobile */
  .sidebar-wrapper:not(.sidebar-hidden)::after {
    content: '';
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

@media screen and (max-width: 768px) {
  .sidebar-wrapper {
    width: 100%;
  }

  .sidebar-wrapper:not(.sidebar-hidden)::after {
    left: 0;
  }
}
</style>
