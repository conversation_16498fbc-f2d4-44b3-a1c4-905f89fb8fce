namespace Marketplace.Domain.Entities;

public class Setting : IEntity
{
    private string _key;
    private string _value;
    private string _category;
    private string? _description;

    public Guid Id { get; set; }
    
    public string Key
    {
        get => _key;
        set => UpdateKey(value);
    }
    
    public string Value
    {
        get => _value;
        set => UpdateValue(value);
    }
    
    public string Category
    {
        get => _category;
        set => UpdateCategory(value);
    }
    
    public string? Description
    {
        get => _description;
        set => UpdateDescription(value);
    }
    
    public SettingType Type { get; set; }
    public bool IsPublic { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private Setting() { }

    public Setting(
        string key,
        string value,
        string category,
        SettingType type = SettingType.String,
        string? description = null,
        bool isPublic = false
    )
    {
        ValidateCreation(key, value, category);

        Id = Guid.NewGuid();
        Key = key;
        Value = value;
        Category = category;
        Type = type;
        Description = description;
        IsPublic = isPublic;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = null;
    }

    public void Update(
        string? value = null,
        string? category = null,
        SettingType? type = null,
        string? description = null,
        bool? isPublic = null
    )
    {
        if (value != null) UpdateValue(value);
        if (category != null) UpdateCategory(category);
        if (type.HasValue) Type = type.Value;
        if (description != null) UpdateDescription(description);
        if (isPublic.HasValue) IsPublic = isPublic.Value;
        UpdatedAt = DateTime.UtcNow;
    }

    private void UpdateKey(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Setting key cannot be empty.", nameof(key));
        if (key.Length > 100)
            throw new ArgumentException("Setting key cannot exceed 100 characters.", nameof(key));
        _key = key.Trim();
        UpdatedAt = DateTime.UtcNow;
    }

    private void UpdateValue(string value)
    {
        if (value == null)
            throw new ArgumentException("Setting value cannot be null.", nameof(value));
        if (value.Length > 2000)
            throw new ArgumentException("Setting value cannot exceed 2000 characters.", nameof(value));
        _value = value;
        UpdatedAt = DateTime.UtcNow;
    }

    private void UpdateCategory(string category)
    {
        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Setting category cannot be empty.", nameof(category));
        if (category.Length > 50)
            throw new ArgumentException("Setting category cannot exceed 50 characters.", nameof(category));
        _category = category.Trim();
        UpdatedAt = DateTime.UtcNow;
    }

    private void UpdateDescription(string? description)
    {
        if (description != null && description.Length > 500)
            throw new ArgumentException("Setting description cannot exceed 500 characters.", nameof(description));
        _description = description?.Trim();
        UpdatedAt = DateTime.UtcNow;
    }

    private void ValidateCreation(string key, string value, string category)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Setting key cannot be empty.", nameof(key));
        if (value == null)
            throw new ArgumentException("Setting value cannot be null.", nameof(value));
        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Setting category cannot be empty.", nameof(category));
    }

    // Helper methods for type conversion
    public bool GetBoolValue()
    {
        return bool.TryParse(Value, out var result) && result;
    }

    public int GetIntValue()
    {
        return int.TryParse(Value, out var result) ? result : 0;
    }

    public decimal GetDecimalValue()
    {
        return decimal.TryParse(Value, out var result) ? result : 0;
    }

    public DateTime? GetDateTimeValue()
    {
        return DateTime.TryParse(Value, out var result) ? result : null;
    }
}

public enum SettingType
{
    String = 0,
    Boolean = 1,
    Integer = 2,
    Decimal = 3,
    DateTime = 4,
    Json = 5,
    Email = 6,
    Url = 7,
    Password = 8
}
