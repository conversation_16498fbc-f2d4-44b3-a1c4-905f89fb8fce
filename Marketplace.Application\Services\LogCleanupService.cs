using Marketplace.Domain.Entities;
using Marketplace.Infrastructure.Persistence;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Application.Services;

public class LogCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<LogCleanupService> _logger;
    private readonly TimeSpan _period = TimeSpan.FromHours(24); // Запускається щодня

    public LogCleanupService(IServiceProvider serviceProvider, ILogger<LogCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CleanupOldLogsAsync();
                await Task.Delay(_period, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during log cleanup");
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken); // Retry after 30 minutes
            }
        }
    }

    private async Task CleanupOldLogsAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MarketplaceDbContext>();

        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-90); // Зберігаємо логи за останні 90 днів
            var criticalCutoffDate = DateTime.UtcNow.AddDays(-365); // Critical логи зберігаємо рік

            // Видаляємо старі логи (крім критичних)
            var deletedCount = await context.Logs
                .Where(l => l.Timestamp < cutoffDate && l.Level != Domain.Entities.LogLevel.Critical)
                .ExecuteDeleteAsync();

            // Видаляємо дуже старі критичні логи
            var deletedCriticalCount = await context.Logs
                .Where(l => l.Timestamp < criticalCutoffDate && l.Level == Domain.Entities.LogLevel.Critical)
                .ExecuteDeleteAsync();

            _logger.LogInformation("Log cleanup completed. Deleted {DeletedCount} regular logs and {DeletedCriticalCount} critical logs", 
                deletedCount, deletedCriticalCount);

            // Оптимізуємо таблицю після видалення
            await OptimizeLogTableAsync(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during log cleanup");
        }
    }

    private async Task OptimizeLogTableAsync(MarketplaceDbContext context)
    {
        try
        {
            // Для SQL Server можна виконати DBCC SHRINKDATABASE або подібні команди
            // Для простоти просто логуємо статистику
            var totalLogs = await context.Logs.CountAsync();
            var oldestLog = await context.Logs.OrderBy(l => l.Timestamp).FirstOrDefaultAsync();
            var newestLog = await context.Logs.OrderByDescending(l => l.Timestamp).FirstOrDefaultAsync();

            _logger.LogInformation("Log table statistics: Total logs: {TotalLogs}, Oldest: {OldestLog}, Newest: {NewestLog}",
                totalLogs, 
                oldestLog?.Timestamp.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A",
                newestLog?.Timestamp.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting log table statistics");
        }
    }
}




