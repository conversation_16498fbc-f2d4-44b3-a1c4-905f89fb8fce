import{_ as h,h as y,r as g,c as n,o as i,a as e,b as _,d as k,k as w,t as r,w as c,F as b,p as N}from"./index-L-hJxM_5.js";const R={class:"admin-seller-requests"},x={class:"admin-seller-requests__header"},A={class:"admin-seller-requests__title"},C={key:0,class:"admin-seller-requests__count"},V={class:"admin-seller-requests__content"},j={key:0,class:"admin-seller-requests__loading"},$={key:1,class:"admin-seller-requests__empty"},B={key:2,class:"admin-seller-requests__list"},D={class:"admin-seller-requests__item-header"},P={class:"admin-seller-requests__user-info"},S={class:"admin-seller-requests__user-details"},E={class:"admin-seller-requests__user-name"},F={class:"admin-seller-requests__user-email"},I={class:"admin-seller-requests__request-date"},z={class:"admin-seller-requests__date-value"},L={class:"admin-seller-requests__item-content"},O={class:"admin-seller-requests__company-info"},T={class:"admin-seller-requests__company-name"},U={class:"admin-seller-requests__company-description"},G={class:"admin-seller-requests__item-actions"},H=["onClick","title"],J=["onClick","title"],K={__name:"PendingSellerRequestsOptimized",props:{requests:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["approve-request","reject-request"],setup(l,{emit:m}){const o=l,d=m;y(()=>o.requests&&o.requests.length>0);const q=a=>{if(!a)return"Unknown";try{const s=new Date(a);return new Intl.DateTimeFormat("uk-UA",{day:"2-digit",month:"2-digit",year:"numeric"}).format(s)}catch(s){return console.error("Error formatting date:",s),"Invalid date"}},p=a=>{d("approve-request",a)},f=a=>{d("reject-request",a)};return(a,s)=>{const u=g("router-link");return i(),n("div",R,[e("div",x,[e("h3",A,[s[0]||(s[0]=e("i",{class:"admin-seller-requests__icon fas fa-user-check"},null,-1)),s[1]||(s[1]=k(" Pending Seller Requests ")),l.requests&&l.requests.length>0?(i(),n("span",C,r(l.requests.length),1)):w("",!0)]),_(u,{to:"/admin/seller-requests",class:"admin-seller-requests__view-all"},{default:c(()=>s[2]||(s[2]=[e("span",null,"View All",-1),e("i",{class:"fas fa-arrow-right"},null,-1)])),_:1})]),e("div",V,[l.loading?(i(),n("div",j,s[3]||(s[3]=[e("div",{class:"admin-seller-requests__loading-spinner"},[e("i",{class:"fas fa-spinner fa-pulse"})],-1),e("p",{class:"admin-seller-requests__loading-text"},"Loading seller requests...",-1)]))):!l.requests||l.requests.length===0?(i(),n("div",$,s[4]||(s[4]=[e("div",{class:"admin-seller-requests__empty-icon"},[e("i",{class:"fas fa-user-check"})],-1),e("h4",{class:"admin-seller-requests__empty-title"},"No Pending Requests",-1),e("p",{class:"admin-seller-requests__empty-text"}," All seller applications have been processed. New requests will appear here. ",-1)]))):(i(),n("div",B,[(i(!0),n(b,null,N(l.requests,t=>(i(),n("div",{key:t.id,class:"admin-seller-requests__item"},[e("div",D,[e("div",P,[s[5]||(s[5]=e("div",{class:"admin-seller-requests__user-avatar"},[e("i",{class:"fas fa-user"})],-1)),e("div",S,[e("span",E,r(t.userName),1),e("span",F,r(t.userEmail),1)])]),e("div",I,[s[6]||(s[6]=e("span",{class:"admin-seller-requests__date-label"},"Submitted",-1)),e("span",z,r(q(t.createdAt)),1)])]),e("div",L,[e("div",O,[e("h5",T,r(t.companyName),1),e("p",U,r(t.description),1)])]),e("div",G,[e("button",{class:"admin-seller-requests__action-btn admin-seller-requests__action-btn--approve",onClick:v=>p(t.id),title:`Approve ${t.userName}'s request`},s[7]||(s[7]=[e("i",{class:"fas fa-check"},null,-1),e("span",null,"Approve",-1)]),8,H),e("button",{class:"admin-seller-requests__action-btn admin-seller-requests__action-btn--reject",onClick:v=>f(t.id),title:`Reject ${t.userName}'s request`},s[8]||(s[8]=[e("i",{class:"fas fa-times"},null,-1),e("span",null,"Reject",-1)]),8,J),_(u,{to:`/admin/seller-requests/${t.id}`,class:"admin-seller-requests__action-btn admin-seller-requests__action-btn--view",title:`View ${t.userName}'s full application`},{default:c(()=>s[9]||(s[9]=[e("i",{class:"fas fa-eye"},null,-1),e("span",null,"View",-1)])),_:2},1032,["to","title"])])]))),128))]))])])}}},Q=h(K,[["__scopeId","data-v-7cdaa683"]]);export{Q as default};
