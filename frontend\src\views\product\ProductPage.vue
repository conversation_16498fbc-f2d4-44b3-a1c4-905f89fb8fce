<template>
  <div class="product-page">
    <!-- Breadcrumbs -->
    <div class="breadcrumbs">
      <span class="fas fa-house"></span> 
      <span class="separator">/</span> 
      <span>Каталог</span>
      <span v-if="product.categoryName" v-for="categoryName in formatCategoryNames(product.categoryName)" :key="categoryName">
        <span class="separator">/</span> 
        <span>{{ categoryName }}</span>
      </span>
      <span class="separator">/</span>  
      <span v-if="product.name">{{ product.name }}</span>
      <span v-else>Завантаження...</span>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="loader"></div>
      <p>Завантаження товару...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <h2>Помилка завантаження</h2>
      <p>{{ error }}</p>
      <button @click="fetchProduct" class="retry-btn">Спробувати знову</button>
    </div>

    <!-- Product Content -->
    <div v-else-if="product" class="product-content">
      <div class="product-main">
        <!-- Product Images -->
        <div class="product-images">
          <div class="main-image">
            <img
              :src="selectedImage || getProductMainImageUrl()"
              :alt="product.name"
              @error="handleImageError"
              loading="lazy"
            />
            <div class="image-navigation" v-if="productImages.length > 1">
              <button
                @click="previousImage"
                class="nav-btn nav-btn-left"
                :title="`Попереднє зображення (${currentImageIndex + 1}/${productImages.length})`"
              >
                <i class="fas fa-arrow-left"></i>
              </button>
              <button
                @click="nextImage"
                class="nav-btn nav-btn-right"
                :title="`Наступне зображення (${currentImageIndex + 1}/${productImages.length})`"
              >
                <i class="fas fa-arrow-right"></i>
              </button>

              <!-- Індикатор поточного зображення -->
              <div class="image-counter">
                {{ currentImageIndex + 1 }} / {{ productImages.length }}
              </div>
            </div>
          </div>
          
          <div class="thumbnail-images">
            <img
              v-for="(image, index) in productImages"
              :key="`thumb-${index}`"
              :src="image"
              :alt="`${product.name} ${index + 1}`"
              :class="{ active: selectedImage === image }"
              :title="`Зображення ${index + 1} з ${productImages.length}`"
              @click="selectImage(image, index)"
              @error="handleImageError"
              loading="lazy"
            />
          </div>
        </div>

        <!-- Product Info -->
        <div class="product-info">
          <h1 class="product-title">{{ product.name }}</h1>

          <!-- Інформація про продавця та компанію (тільки якщо є реальні дані) -->
          <div class="seller-company-info" v-if="seller || company">
            <!-- Інформація про продавця -->
            <div class="seller-info" v-if="seller">
              <span class="seller-label">Продавець:</span>
              <span v-if="sellerLoading" class="loading-text">Завантаження...</span>
              <span
                v-else
                class="seller-name"
              >
                {{ seller.firstName && seller.lastName ? `${seller.firstName} ${seller.lastName}` : seller.username }}
              </span>
            </div>

            <!-- Інформація про компанію -->
            <div class="company-info" v-if="company">
              <span class="company-label">Компанія:</span>
              <span v-if="companyLoading" class="loading-text">Завантаження...</span>
              <span
                v-else
                class="company-name"
              >
                {{ company.name }}
              </span>
            </div>
          </div>

          <!-- Product Options -->
          <div class="product-options">
            <!-- Dynamic Attribute Selection -->
            <div v-for="(options, attributeName) in selectableAttributes" :key="attributeName" class="option-group">
              <label class="option-label">{{ attributeName }}:</label>
              <div class="attribute-options">
                <button
                  v-for="option in options"
                  :key="option"
                  :class="['attribute-option', { active: selectedAttributes[attributeName] === option }]"
                  @click="selectAttribute(attributeName, option)"
                >
                  {{ option }}
                </button>
              </div>
            </div>
          </div>

          <!-- Price and Actions -->
          <div class="product-actions">
            <div class="price-section">
              <span class="price">{{ formatPrice(product) }}</span>
            </div>
            
            <div class="action-buttons">
              <button 
                @click="addToCart" 
                :disabled="!product.stock > 0"
                class="buy-btn"
              >
                {{ product.stock > 0 ? 'Купити' : 'Немає в наявності' }}
              </button>
              
              <button
                @click="toggleFavorite"
                :class="['favorite-btn', { active: isProductInWishlist }]"
                :title="isProductInWishlist ? 'Видалити з обраного' : 'Додати в обране'"
              >
              <span v-if="!isProductInWishlist">
                <i class="fas fa-heart"></i>
              </span>
              <span v-if="isProductInWishlist">
                <i class="far fa-heart"></i>
              </span>
              </button>
            </div>
          </div>

          <!-- Product Details in Right Column -->
          <div class="product-details-right">
            <!-- Characteristics Section -->
            <div class="details-section">
              <h2>Характеристики</h2>
              <div class="characteristics">
                <div v-for="(value, key) in product.attributes" :key="key" class="characteristic-row">
                  <span class="characteristic-label">{{ key }}</span>
                  <span class="characteristic-value">
                    <template v-if="typeof value === 'string' && value.includes(',')">
                      <span v-for="(item, index) in value.split(',').map(v => v.trim())" :key="index" class="characteristic-value-item">
                        {{ item }}<br v-if="index < value.split(',').length - 1">
                      </span>
                    </template>
                    <template v-else>{{ value }}</template>
                  </span>
                </div>
              </div>
            </div>

            <!-- Reviews Section -->
            <div class="reviews-section">
              <h2>Відгуки</h2>
              <ReviewGrid
                :api-endpoint="`/api/products/${product.slug}/reviews`"
                :auto-fetch="true"
                :show-pagination="true"
                :page-size="5"
                :show-user-info="true"
                empty-message="Поки що немає відгуків про цей товар"
              />
            </div>
          </div>
        </div>
      </div>
      
      <section class="recommended-section" v-if="product.category && product.category.slug">
        <div class="container">
          <h2 class="section-title">Товари з цієї категорії</h2>
          <ProductGrid
            :fetch-params="{ categorySlug: product.category.slug, status: 1, count: 4, random: true }"
            :grid-columns="4"
            empty-message="Рандомні товари поки що недоступні"
          />
        </div>
      </section>


      <section class="recommended-section">
        <div class="container">
          <h2 class="section-title">Разом з цим товаром дивляться ще</h2>
          <ProductGrid
            :fetch-params="{ type: 'recommended', status: 1, pageSize: 8 }"
            :grid-columns="4"
            empty-message="Рекомендовані товари поки що недоступні"
          />
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import productService from '@/services/product.service';
import categoryService from '@/services/category.service';
import cartService from '../../services/cart.service';
import wishlistService from '@/services/wishlist.service';
import ProductGrid from '@/components/common/ProductGrid.vue';
import ReviewGrid from '@/components/common/RatingGrid.vue';
import imageService from '@/services/image.service';
import { useToast } from '@/composables/useToast';

export default {
  name: 'ProductPage',
  components: {
    ProductGrid,
    ReviewGrid
  },
  setup() {
    const { showToast } = useToast();

    return {
      showToast
    };
  },
  data() {
    return {
      product: {},
      loading: true,
      error: null,
      selectedImage: '',
      currentImageIndex: 0,
      selectedAttributes: {},
      placeholderImage: '/placeholder-product.svg',
      wishlistItems: [], // Список ID товарів в обраних
      imageCache: new Map(), // Кеш для URL зображень
      loadingImages: new Set(), // Множина ID зображень, які завантажуються
      productImagesLoaded: false, // Флаг завантаження зображень продукту
      slideshowInterval: null // Інтервал для автоматичного слайд-шоу
    }
  },
  computed: {
    productImages() {
      if (!this.product.images || this.product.images.length === 0) {
        // Якщо немає зображень, спробуємо завантажити через API
        const mainImageUrl = this.getProductMainImageUrl();
        return [mainImageUrl];
      }

      // Повертаємо URL зображень з нової структури
      return this.product.images.map(img => {
        if (img.url) {
          return img.url; // URL вже нормалізований в loadProductMainImage
        }
        return this.placeholderImage;
      });
    },
    selectableAttributes() {
      if (!this.product.attributes) return {}

      const selectable = {}

      // Проходимо по всіх атрибутах товару
      // Приклад структури атрибутів:
      // {
      //   "Колір": "червоний, зелений, синій",
      //   "Пам'ять": "64ГБ, 128ГБ, 256ГБ",
      //   "Діагональ": "6.1 дюйм",  // не буде показано як вибір
      //   "Бренд": "Apple"         // не буде показано як вибір
      // }
      Object.entries(this.product.attributes).forEach(([key, value]) => {
        if (typeof value === 'string' && value.includes(',')) {
          // Якщо значення містить кому, розділяємо на варіанти
          const options = value.split(',').map(option => option.trim()).filter(option => option)
          if (options.length > 1) {
            selectable[key] = options
          }
        } else if (Array.isArray(value) && value.length > 1) {
          // Якщо значення - це масив з кількома елементами
          selectable[key] = value
        }
      })

      return selectable
    },

    // Перевіряє чи товар в обраних
    isProductInWishlist() {
      return this.wishlistItems.includes(this.product.id);
    }
  },
  watch: {
    // Watcher для product для завантаження зображень при зміні продукту
    'product.id': {
      handler(newProductId) {
        if (newProductId && !this.productImagesLoaded) {
          this.$nextTick(() => {
            this.preloadProductImages();
          });
        }
      }
    }
  },
  async mounted() {
    await this.loadWishlistItems();
    await this.fetchProduct();

    // Додаємо обробник клавіатурних подій для навігації зображеннями
    document.addEventListener('keydown', this.handleKeyboardNavigation);
  },

  beforeUnmount() {
    // Видаляємо обробник при знищенні компонента
    document.removeEventListener('keydown', this.handleKeyboardNavigation);
  },
  watch: {
    '$route.params.slug'() {
      if (this.$route.params.slug) {
        this.fetchProduct()
      }
    }
  },
  methods: {
    async fetchProduct() {
      try {
        this.loading = true
        this.error = null

        const slug = this.$route.params.slug
        const response = await productService.getBySlug(slug)

        console.log(response);

        this.product = response.data

        // Завантажуємо зображення продукту
        this.$nextTick(() => {
          this.preloadProductImages();
          // selectedImage буде встановлено в loadProductMainImage після завантаження
        });

        if(!this.product.category && this.product.categoryId) {
          this.fetchCategory(this.product.categoryId)
        }

        // Set default selections for selectable attributes
        this.setDefaultAttributeSelections()

        // isFavorite тепер керується через computed властивість isProductInWishlist

        // Встановлюємо реальну інформацію про компанію та продавця
        this.setRealSellerAndCompanyInfo();
      } catch (err) {
        this.error = 'Не вдалося завантажити товар'
        console.error('Error fetching product:', err)
      } finally {
        this.loading = false
      }
    },

    // Завантажує список ID товарів в обраних
    async loadWishlistItems() {
      try {
        const response = await wishlistService.getWishlist();

        // Обробляємо структуру відповіді
        let wishlistData = null;
        if (response.data.success) {
          wishlistData = response.data.data;
        } else if (response.data.data) {
          wishlistData = response.data.data;
        } else {
          wishlistData = response.data;
        }

        // Витягуємо productId з items
        if (wishlistData && wishlistData.items && Array.isArray(wishlistData.items)) {
          this.wishlistItems = wishlistData.items.map(item => item.productId);
        } else {
          this.wishlistItems = [];
        }
      } catch (error) {
        console.error('ProductPage - Error loading wishlist:', error);
        // Якщо wishlist не існує (404), це нормально
        if (error.response?.status === 404) {
          this.wishlistItems = [];
        } else {
          this.wishlistItems = [];
        }
      }
    },
    async fetchCategory(categoryId) {
      try {
        const response = await categoryService.getById(categoryId);
        this.product.category = response.data;


        console.log('Fetching category:', categoryId)
      } catch (err) {
        console.error('Error fetching category:', err)
      }
    },

    // Встановлює реальну інформацію про продавця та компанію
    setRealSellerAndCompanyInfo() {
      console.log('Product data for seller/company info:', this.product);

      // Інформація про компанію - використовуємо companyName з API
      if (this.product.companyName) {
        this.company = {
          id: this.product.companyId,
          name: this.product.companyName,
          slug: this.product.companyId
        };
        console.log('Company info set:', this.company);
      } else {
        this.company = null; // Не показуємо, якщо немає реальних даних
        console.log('No company name found');
      }

      // Інформація про продавця - поки що не маємо реальних даних
      // Можна буде додати пізніше, коли буде API для отримання продавця
      this.seller = null;

      // Встановлюємо, що завантаження завершено
      this.sellerLoading = false;
      this.companyLoading = false;
    },
    setDefaultAttributeSelections() {
      // Встановлюємо перший варіант як вибраний за замовчуванням для кожного атрибута
      Object.entries(this.selectableAttributes).forEach(([attributeName, options]) => {
        if (options.length > 0) {
          this.selectedAttributes[attributeName] = options[0]
        }
      })
    },
    selectAttribute(attributeName, value) {
      this.selectedAttributes[attributeName] = value
    },
    selectImage(image, index) {
      this.selectedImage = image
      this.currentImageIndex = index
      console.log('ProductPage - Selected image:', index + 1, 'of', this.productImages.length);
    },

    previousImage() {
      if (this.productImages.length <= 1) return;

      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      } else {
        // Циклічна навігація - переходимо до останнього зображення
        this.currentImageIndex = this.productImages.length - 1
      }

      this.selectedImage = this.productImages[this.currentImageIndex]
      console.log('ProductPage - Previous image:', this.currentImageIndex + 1, 'of', this.productImages.length);
    },

    nextImage() {
      if (this.productImages.length <= 1) return;

      if (this.currentImageIndex < this.productImages.length - 1) {
        this.currentImageIndex++
      } else {
        // Циклічна навігація - переходимо до першого зображення
        this.currentImageIndex = 0
      }

      this.selectedImage = this.productImages[this.currentImageIndex]
      console.log('ProductPage - Next image:', this.currentImageIndex + 1, 'of', this.productImages.length);
    },

    // Обробка клавіатурної навігації
    handleKeyboardNavigation(event) {
      // Перевіряємо, чи фокус не на полі введення
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      // Перевіряємо, чи є зображення для навігації
      if (!this.productImages || this.productImages.length <= 1) {
        return;
      }

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          this.previousImage();
          break;
        case 'ArrowRight':
          event.preventDefault();
          this.nextImage();
          break;
        case 'Home':
          event.preventDefault();
          this.selectImage(this.productImages[0], 0);
          break;
        case 'End':
          event.preventDefault();
          const lastIndex = this.productImages.length - 1;
          this.selectImage(this.productImages[lastIndex], lastIndex);
          break;
      }
    },

    // Швидкий перехід до конкретного зображення за номером
    goToImage(imageNumber) {
      const index = imageNumber - 1; // Конвертуємо з 1-based в 0-based
      if (index >= 0 && index < this.productImages.length) {
        this.selectImage(this.productImages[index], index);
      }
    },

    // Автоматичне слайд-шоу (опціонально)
    startSlideshow(intervalMs = 3000) {
      if (this.slideshowInterval) {
        clearInterval(this.slideshowInterval);
      }

      if (this.productImages.length > 1) {
        this.slideshowInterval = setInterval(() => {
          this.nextImage();
        }, intervalMs);
      }
    },

    stopSlideshow() {
      if (this.slideshowInterval) {
        clearInterval(this.slideshowInterval);
        this.slideshowInterval = null;
      }
    },
    handleImageError(event) {
      console.log('ProductPage - Image loading error:', event.target.src);
      event.target.src = this.placeholderImage;
    },

    // Отримує URL головного зображення товару
    getProductMainImageUrl() {
      if (!this.product.id) {
        return this.placeholderImage;
      }

      // Перевіряємо кеш
      const cacheKey = `main_${this.product.id}`;
      if (this.imageCache.has(cacheKey)) {
        return this.imageCache.get(cacheKey);
      }

      // Якщо зображення завантажується, повертаємо placeholder
      if (this.loadingImages.has(cacheKey)) {
        return this.placeholderImage;
      }

      // Завантажуємо асинхронно
      this.$nextTick(() => {
        this.loadProductMainImage();
      });

      return this.placeholderImage;
    },



    // Асинхронно завантажує всі зображення товару через новий API
    async loadProductMainImage() {
      if (!this.product.id) return;

      const cacheKey = `main_${this.product.id}`;

      // Перевіряємо, чи компонент ще існує
      if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
        return;
      }

      if (this.loadingImages.has(cacheKey) || this.imageCache.has(cacheKey)) {
        return; // Вже завантажується або завантажено
      }

      this.loadingImages.add(cacheKey);

      try {
        // Використовуємо новий API роут, який повертає весь список зображень
        const apiUrl = import.meta.env.VUE_APP_API_URL || 'http://localhost:5296';
        const response = await fetch(`${apiUrl}/api/universal/images/product/${this.product.id}`);

        // Перевіряємо, чи компонент ще існує після асинхронної операції
        if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
          return;
        }

        if (response.ok) {
          const data = await response.json();
          console.log('ProductPage - Images API response:', data);

          // Новий API повертає масив зображень
          let mainImageUrl = null;
          let allImages = [];

          if (data.success && data.data && Array.isArray(data.data)) {
            // Обробляємо масив зображень
            allImages = data.data.map(img => ({
              id: img.id,
              url: imageService.normalizeImageUrl(img.url),
              isMain: img.isMain,
              order: img.order || 0
            }));

            // Знаходимо головне зображення
            const mainImage = allImages.find(img => img.isMain === true);
            mainImageUrl = mainImage ? mainImage.url : (allImages[0] ? allImages[0].url : null);

            // Сортуємо зображення за порядком
            allImages.sort((a, b) => a.order - b.order);

            // Зберігаємо всі зображення в продукт
            this.product.images = allImages;

            console.log('ProductPage - Found images:', allImages);
            console.log('ProductPage - Main image:', mainImageUrl);
          }

          if (mainImageUrl) {
            this.imageCache.set(cacheKey, mainImageUrl);
            // Встановлюємо перше зображення як вибране
            this.selectedImage = mainImageUrl;
            this.currentImageIndex = 0;
            // Примусово оновлюємо компонент для показу нового зображення
            this.$forceUpdate();
          } else {
            // Якщо зображення не знайдено, кешуємо placeholder
            this.imageCache.set(cacheKey, this.placeholderImage);
          }
        } else {
          console.warn(`ProductPage - Images not found for product ${this.product.id}: ${response.status}`);
          // Якщо помилка API, кешуємо placeholder
          this.imageCache.set(cacheKey, this.placeholderImage);
          this.selectedImage = this.placeholderImage;
        }
      } catch (error) {
        console.error('ProductPage - Error loading product images:', this.product.id, error);
        // При помилці кешуємо placeholder
        this.imageCache.set(cacheKey, this.placeholderImage);
        this.selectedImage = this.placeholderImage;
      } finally {
        this.loadingImages.delete(cacheKey);
      }
    },



    // Попередньо завантажує всі зображення продукту
    preloadProductImages() {
      if (!this.product.id) return;

      // Новий API завантажує всі зображення одним запитом
      this.loadProductMainImage();

      this.productImagesLoaded = true;
    },
    formatPrice(product) {
      if (!product.priceAmount) return 'Ціна не вказана'
      if (product.priceAmount == 0) return 'Ціна не вказана'
      product.priceAmount = Math.round(product.priceAmount);
      if (!product.priceCurrency) return `${product.priceAmount} ₴`
      else if(product.priceCurrency == "UAH") return `${product.priceAmount} ₴`
      else if(product.priceCurrency == "USD") return `${product.priceAmount} $`
      else if(product.priceCurrency == "EUR") return `${product.priceAmount} €`
      return `${product.priceAmount} ${product.priceCurrency}`
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('uk-UA')
    },
    async addToCart() {
      try {
        // Передаємо атрибути разом з товаром
        const attributes = Object.keys(this.selectedAttributes).length > 0
          ? this.selectedAttributes
          : null;

        await cartService.addToCart(this.product.id, 1, attributes);
        this.showToast('Товар додано до корзини', 'success');
        this.$router.push('/cart');
      } catch (error) {
        console.error('Error adding to cart:', error);
        this.showToast('Помилка при додаванні до корзини', 'error');
      }
      console.log('Adding to cart:', {
        product: this.product,
        selectedAttributes: this.selectedAttributes
      })
    },

    async toggleFavorite() {
      console.log(`ProductPage - toggleFavorite called, current state: ${this.isProductInWishlist}`);
      const wasInWishlist = this.isProductInWishlist;

      try {
        if (wasInWishlist) {

          await wishlistService.removeFromWishlist(this.product.id);
          this.wishlistItems = this.wishlistItems.filter(id => id !== this.product.id);
          this.showToast('Товар видалено з обраних', 'success');
        } else {

          await wishlistService.addToWishlist(this.product.id);
          this.wishlistItems.push(this.product.id);
          this.showToast('Товар додано до обраних', 'success');
        }

        console.log(`ProductPage - toggleFavorite completed, new state: ${this.isProductInWishlist}`);

        // isFavorite автоматично оновиться через computed властивість isProductInWishlist
      } catch (error) {
        console.error('ProductPage - Error toggling wishlist:', error);
        console.error('ProductPage - Error details:', error.response?.data || error.message);

        // При помилці повертаємо кнопку в попередній стан
        this.updateFavoriteButtonDirectly(wasInWishlist);

        this.showToast('Помилка при роботі з обраними', 'error');
      }
    },
    /**
     * Форматує рядок з назвами категорій у масив окремих назв
     *
     * Приклади використання:
     * formatCategoryNames("Електроніка > Смартфони > iPhone")
     * // => ["Електроніка", "Смартфони", "iPhone"]
     *
     * formatCategoryNames("Одяг / Чоловічий / Сорочки")
     * // => ["Одяг", "Чоловічий", "Сорочки"]
     *
     * formatCategoryNames("дім і сад > меблі > столи")
     * // => ["Дім і сад", "Меблі", "Столи"]
     *
     * @param {string} categoryName - Рядок з назвами категорій, розділених символами
     * @returns {Array<string>} - Масив очищених назв категорій
     */
    formatCategoryNames(categoryName) {
      // Перевіряємо, чи є categoryName валідним рядком
      if (!categoryName || typeof categoryName !== 'string') {
        return [];
      }

      // Підтримуємо різні роздільники: '>', ' > ', ' / ', '/'
      let separator = '>';
      if (categoryName.includes(' > ')) {
        separator = ' > ';
      } else if (categoryName.includes(' / ')) {
        separator = ' / ';
      } else if (categoryName.includes('/') && !categoryName.includes('>')) {
        separator = '/';
      }

      // Розділяємо по роздільнику та очищаємо пробіли
      const categoryNamesArray = categoryName
        .split(separator)
        .map(name => {
          // Видаляємо HTML-теги, якщо вони є
          const cleanName = name.replace(/<[^>]*>/g, '').trim();
          return cleanName;
        })
        .filter(name => name.length > 0) // Видаляємо порожні рядки
        .map(name => {
          // Обмежуємо довжину назви категорії (максимум 50 символів)
          if (name.length > 50) {
            name = name.substring(0, 47) + '...';
          }

          // Капіталізуємо першу літеру для кращого відображення, але зберігаємо оригінальний регістр
          // Це корисно для абревіатур та власних назв
          if (name.length > 0 && name === name.toLowerCase()) {
            return name.charAt(0).toUpperCase() + name.slice(1);
          }

          return name;
        });

      return categoryNamesArray;
    }

  }
}
</script>

<style scoped>
.recommended-section {
  margin-bottom: 48px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  text-align: left;
}

.product-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Breadcrumbs */
.breadcrumbs {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.breadcrumbs a {
  color: #007bff;
  text-decoration: none;
}

.breadcrumbs a:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
  color: #ccc;
}

.current {
  color: #333;
  font-weight: 500;
}

/* Loading and Error States */
.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #0056b3;
}

/* Product Content */
.product-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

/* Product Images */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-image {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-navigation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  pointer-events: none; /* Дозволяємо кліки тільки на кнопки */
}

.nav-btn {
  pointer-events: all; /* Відновлюємо кліки для кнопок */
  border: none;
  width: 45px;
  height: 45px;
  color: black;
  font-size: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  transform: scale(1.2);
}

.nav-btn:active {
  transform: scale(0.95);
}

.nav-btn-left {
  margin-right: auto;
}

.nav-btn-right {
  margin-left: auto;
}

/* Лічильник зображень */
.image-counter {
  pointer-events: all;
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.thumbnail-images {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
}

.thumbnail-images img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  opacity: 0.7;
}

.thumbnail-images img:hover {
  border-color: #007bff;
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.thumbnail-images img.active {
  border-color: #007bff;
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

/* Підказка про клавіатурну навігацію */
.keyboard-hint {
  text-align: center;
  margin-top: 10px;
  color: #6c757d;
  font-size: 12px;
}

.keyboard-hint i {
  margin-right: 5px;
  color: #007bff;
}

/* Product Info */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Product Details in Right Column */
.product-details-right {
  margin-top: 30px;
  padding-top: 30px;
  /* border-top: 1px solid #e9ecef; */
}

.product-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.seller-company-info {
  margin-bottom: 16px;
}

.seller-info,
.company-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.seller-label,
.company-label {
  margin-right: 8px;
  font-weight: 500;
}

.seller-name,
.company-name {
  color: #007bff;
  font-weight: 500;
}

.loading-text {
  color: #999;
  font-style: italic;
}

.no-data {
  color: #999;
}

/* Product Options */
.product-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.attribute-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.attribute-option {
  padding: 8px 16px;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  min-width: 60px;
  text-align: center;
}

.attribute-option:hover {
  border-color: #007bff;
}

.attribute-option.active {
  border-color: #007bff;
  background-color: #007bff;
  color: white;
}

/* Product Actions */
.product-actions {
  margin-top: auto;
  padding-top: 20px;
  /* border-top: 1px solid #e9ecef; */
}

.price-section {
  margin-bottom: 20px;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.buy-btn {
  flex: 0.4;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 30px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.buy-btn:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.buy-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.favorite-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 8px;
  font-size: 25px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc3545;
}

.favorite-btn:hover {
  border-color: #dc3545;
  color: #dc3545;
}

.favorite-btn.active {
  color: #dc3545;
}

/* Product Details - moved to right column */

.details-section h2, .reviews-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

/* Characteristics */
.characteristics {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 50px;
}

.characteristic-row {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  /* border-bottom: 1px solid #f1f3f4; */
}

.characteristic-label {
  font-weight: 500;
  color: #666;
  flex: 1;
}

.characteristic-value {
  color: #2F7CDF;
  text-align: right;
  flex: 1;
  white-space: pre-line;
}

.characteristic-value-item {
  color: #2F7CDF;
}

/* Reviews section now uses ReviewGrid component */

/* Responsive Design */
@media (max-width: 768px) {
  .product-main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-details-right {
    margin-top: 20px;
    padding-top: 20px;
  }

  .product-title {
    font-size: 20px;
  }

  .price {
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .favorite-btn {
    width: 100%;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .product-page {
    padding: 15px;
  }

  .thumbnail-images img {
    width: 60px;
    height: 60px;
  }

  .characteristic-row {
    flex-direction: column;
    gap: 4px;
  }

  .characteristic-value {
    text-align: left;
  }
}
</style>
