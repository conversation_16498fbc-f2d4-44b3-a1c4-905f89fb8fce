using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Setting;

public class UpdateSettingsCommandHandler : IRequestHandler<UpdateSettingsCommand, bool>
{
    private readonly ISettingRepository _settingRepository;

    public UpdateSettingsCommandHandler(ISettingRepository settingRepository)
    {
        _settingRepository = settingRepository;
    }

    public async Task<bool> Handle(UpdateSettingsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            foreach (var setting in request.Settings)
            {
                var settingType = DetermineSettingType(setting.Key, setting.Value);
                await _settingRepository.UpsertAsync(
                    setting.Key, 
                    setting.Value, 
                    request.Category, 
                    settingType,
                    cancellationToken: cancellationToken);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    private SettingType DetermineSettingType(string key, string value)
    {
        // Determine type based on key patterns and value format
        if (key.Contains("email") || key.Contains("Email"))
            return SettingType.Email;
        
        if (key.Contains("url") || key.Contains("Url") || key.Contains("link"))
            return SettingType.Url;
        
        if (key.Contains("password") || key.Contains("Password") || key.Contains("secret"))
            return SettingType.Password;
        
        if (key.Contains("enabled") || key.Contains("mode") || value.ToLower() is "true" or "false")
            return SettingType.Boolean;
        
        if (decimal.TryParse(value, out _))
            return SettingType.Decimal;
        
        if (int.TryParse(value, out _))
            return SettingType.Integer;
        
        if (DateTime.TryParse(value, out _))
            return SettingType.DateTime;
        
        return SettingType.String;
    }
}
