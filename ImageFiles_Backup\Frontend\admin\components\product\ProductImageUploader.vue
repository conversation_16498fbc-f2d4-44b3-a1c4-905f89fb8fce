<template>
  <div class="product-image-uploader">
    <!-- Header -->
    <div class="uploader-header">
      <div class="header-content">
        <h3 class="header-title">
          <i class="fas fa-images"></i>
          Product Images
        </h3>
        <p class="header-description">
          Upload and manage up to 10 product images. Drag to reorder, first image becomes main.
        </p>
      </div>
      <div class="header-stats" v-if="totalImageCount > 0">
        <span class="stat-item">
          <i class="fas fa-image"></i>
          {{ totalImageCount }}/10 images
        </span>
        <span class="stat-item" v-if="hasMainImage">
          <i class="fas fa-star"></i>
          Main set
        </span>
      </div>
    </div>

    <!-- Upload Zone -->
    <div 
      v-if="canUploadMore"
      class="upload-zone"
      :class="{ 
        'upload-zone--dragover': isDragOver, 
        'upload-zone--uploading': isUploading,
        'upload-zone--compact': totalImageCount > 0
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <input
        type="file"
        ref="fileInput"
        multiple
        accept="image/*"
        style="display: none"
        @change="handleFileSelect"
      />
      
      <div class="upload-content">
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt" v-if="!isUploading"></i>
          <i class="fas fa-spinner fa-spin" v-else></i>
        </div>
        <div class="upload-text">
          <h4 v-if="!isUploading">
            {{ totalImageCount === 0 ? 'Upload Product Images' : 'Add More Images' }}
          </h4>
          <h4 v-else>Uploading {{ uploadProgress.current }}/{{ uploadProgress.total }}...</h4>
          <p>Drag & drop images or click to browse</p>
          <p class="upload-hint">{{ remainingSlots }} slots remaining • JPG, PNG, WebP up to 5MB each</p>
        </div>
      </div>
      
      <div v-if="isUploading" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: overallProgress + '%' }"></div>
        </div>
      </div>
    </div>

    <!-- Max Images Warning -->
    <div v-if="!canUploadMore" class="max-images-warning">
      <i class="fas fa-info-circle"></i>
      Maximum of 10 images reached. Delete some images to upload more.
    </div>

    <!-- Error Message -->
    <div v-if="error" class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error }}
    </div>

    <!-- Images Grid -->
    <div v-if="totalImageCount > 0" class="images-section">
      <div class="section-header">
        <div>
          <h4 class="section-title">Product Images</h4>
          <p class="section-description">Drag images to reorder • First image is main</p>
        </div>
        <div class="section-actions" v-if="selectedImages.length > 0">
          <button 
            type="button" 
            class="btn-action btn-action--danger"
            @click="deleteSelected"
            :disabled="isUploading"
          >
            <i class="fas fa-trash"></i>
            Delete ({{ selectedImages.length }})
          </button>
        </div>
      </div>

      <!-- Existing Images -->
      <div class="images-grid" v-if="draggableImages.length > 0">
        <draggable
          v-model="draggableImages"
          group="images"
          @start="onDragStart"
          @end="onDragEnd"
          item-key="id"
          class="images-draggable"
          :disabled="isUploading"
        >
          <template #item="{ element: image, index }">
            <div 
              class="image-card"
              :class="{ 
                'image-card--main': index === 0,
                'image-card--selected': isSelected(image.id),
                'image-card--dragging': isDragging
              }"
            >
              <!-- Selection Checkbox -->
              <div class="image-checkbox">
                <input 
                  type="checkbox" 
                  :checked="isSelected(image.id)"
                  @change="toggleSelection(image.id)"
                />
              </div>

              <!-- Main Badge -->
              <div v-if="index === 0" class="main-badge">
                <i class="fas fa-star"></i>
                Main
              </div>

              <!-- Order Badge -->
              <div class="order-badge">{{ index + 1 }}</div>

              <!-- Image -->
              <div class="image-wrapper">
                <img 
                  :src="image.url" 
                  :alt="image.altText || 'Product image'"
                  class="image-preview"
                  @click="openImageModal(image)"
                  @error="handleImageError(image)"
                />
                
                <!-- Actions -->
                <div class="image-actions">
                  <button 
                    type="button" 
                    class="action-btn action-btn--view"
                    @click="openImageModal(image)"
                    title="View full size"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                  <button 
                    type="button" 
                    class="action-btn action-btn--delete"
                    @click="deleteImage(image)"
                    title="Delete image"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>

              <!-- Image Info -->
              <div class="image-info">
                <div class="image-name-container">
                  <input
                    v-if="image.editingAltText"
                    v-model="image.tempAltText"
                    @blur="saveAltText(image)"
                    @keyup.enter="saveAltText(image)"
                    @keyup.escape="cancelAltTextEdit(image)"
                    class="alt-text-input"
                    placeholder="Enter alt text..."
                    ref="altTextInput"
                  />
                  <div
                    v-else
                    class="image-name"
                    @dblclick="startAltTextEdit(image)"
                    :title="'Double-click to edit alt text'"
                  >
                    {{ image.altText || 'Product Image' }}
                    <i class="fas fa-edit edit-icon"></i>
                  </div>
                </div>
                <div class="image-meta">
                  <span class="meta-item">{{ formatFileSize(image.size || 0) }}</span>
                  <span v-if="image.isLocal" class="meta-item meta-item--local">Local</span>
                  <span v-if="image.markedForDeletion" class="meta-item meta-item--deleted">Deleted</span>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>

      <!-- Pending Images -->
      <div class="pending-images" v-if="pendingImages.length > 0">
        <h5 class="pending-title">Uploading...</h5>
        <div class="images-grid">
          <div 
            v-for="(image, index) in pendingImages"
            :key="image.id"
            class="image-card image-card--pending"
          >
            <!-- Upload Progress -->
            <div class="upload-overlay">
              <i class="fas fa-spinner fa-spin upload-spinner"></i>
              <span class="upload-progress-text">{{ image.progress || 0 }}%</span>
            </div>

            <!-- Image -->
            <div class="image-wrapper">
              <img 
                :src="image.url" 
                :alt="image.name"
                class="image-preview"
              />
            </div>

            <!-- Image Info -->
            <div class="image-info">
              <div class="image-name">{{ image.name }}</div>
              <div class="image-meta">
                <span class="meta-item">{{ formatFileSize(image.size) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="modalImage" class="image-modal" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <img :src="modalImage.url" :alt="modalImage.altText || modalImage.name" />
        <button type="button" class="modal-close" @click="closeImageModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'ProductImageUploader',
  components: {
    draggable
  },
  props: {
    entityId: {
      type: [String, Number],
      required: true
    },
    currentImages: {
      type: Array,
      default: () => []
    },
    isCreate: {
      type: Boolean,
      default: false
    }
  },
  emits: ['images-changed', 'images-reordered'],
  data() {
    return {
      existingImages: [],
      pendingImages: [],
      selectedImages: [],
      isDragOver: false,
      isDragging: false,
      isUploading: false,
      uploadProgress: {
        current: 0,
        total: 0
      },
      error: null,
      modalImage: null,
      // Enhanced change tracking
      changeTracker: {
        addedImages: [], // Local images to be uploaded
        deletedImages: [], // Server images to be deleted
        reorderedImages: [], // Images with changed order
        updatedImages: [], // Images with updated properties (alt text, etc.)
        hasOrderChanges: false,
        hasMainImageChange: false
      },
      // Local copy for draggable
      draggableImages: []
    }
  },
  computed: {
    visibleImages() {
      return this.existingImages.filter(img => !img.markedForDeletion)
    },

    totalImageCount() {
      return this.visibleImages.length + this.pendingImages.length
    },

    canUploadMore() {
      return this.totalImageCount < 10
    },

    remainingSlots() {
      return 10 - this.totalImageCount
    },

    hasMainImage() {
      return this.visibleImages.length > 0
    },

    overallProgress() {
      if (this.uploadProgress.total === 0) return 0
      return Math.round((this.uploadProgress.current / this.uploadProgress.total) * 100)
    }
  },
  mounted() {
    this.existingImages = [...(this.currentImages || [])]
    this.updateDraggableImages()
  },
  methods: {
    triggerFileInput() {
      if (this.isUploading || !this.canUploadMore) return
      try {
        const input = this.$refs.fileInput
        if (input) {
          input.click()
        }
      } catch (error) {
        console.error('Error triggering file input:', error)
      }
    },

    handleFileSelect(event) {
      try {
        const input = event.target
        const files = input && input.files

        if (!files || files.length === 0) return

        // Convert FileList to Array and filter valid files
        const fileArray = []
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          if (file && file instanceof File) {
            fileArray.push(file)
          }
        }

        if (fileArray.length > 0) {
          this.processFiles(fileArray)
        }

        // Clear input
        if (input) {
          input.value = ''
        }
      } catch (error) {
        console.error('Error handling file select:', error)
        this.error = 'Error selecting files'
      }
    },

    handleDrop(event) {
      try {
        event.preventDefault()
        this.isDragOver = false

        const dataTransfer = event.dataTransfer
        const files = dataTransfer && dataTransfer.files

        if (!files || files.length === 0) return

        // Convert FileList to Array and filter valid files
        const fileArray = []
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          if (file && file instanceof File) {
            fileArray.push(file)
          }
        }

        if (fileArray.length > 0) {
          this.processFiles(fileArray)
        }
      } catch (error) {
        console.error('Error handling drop:', error)
        this.error = 'Error processing dropped files'
      }
    },

    handleDragOver(event) {
      event.preventDefault()
    },

    handleDragEnter(event) {
      event.preventDefault()
      this.isDragOver = true
    },

    handleDragLeave(event) {
      event.preventDefault()
      this.isDragOver = false
    },

    processFiles(files) {
      try {
        this.error = null

        // Check how many files we can actually upload
        const availableSlots = this.remainingSlots
        if (availableSlots <= 0) {
          this.error = 'Maximum of 10 images allowed'
          return
        }

        // Limit files to available slots
        const filesToProcess = files.slice(0, availableSlots)
        if (files.length > availableSlots) {
          this.error = `Only ${availableSlots} images can be uploaded (${files.length - availableSlots} files skipped)`
        }

        const validFiles = []
        const errors = []

        for (let i = 0; i < filesToProcess.length; i++) {
          const file = filesToProcess[i]

          // Validate file type
          const fileType = file.type || ''
          if (!fileType.startsWith('image/')) {
            errors.push(`${file.name}: Only image files allowed`)
            continue
          }

          // Validate file size (5MB limit)
          const fileSize = file.size || 0
          if (fileSize > 5 * 1024 * 1024) {
            errors.push(`${file.name}: File size must be less than 5MB`)
            continue
          }

          validFiles.push(file)
        }

        if (errors.length > 0) {
          this.error = errors[0] // Show first error
        }

        if (validFiles.length > 0) {
          this.uploadFiles(validFiles)
        }
      } catch (error) {
        console.error('Error processing files:', error)
        this.error = 'Error processing files'
      }
    },

    async uploadFiles(files) {
      if (this.isUploading) return

      try {
        this.isUploading = true
        this.uploadProgress = {
          current: 0,
          total: files.length
        }

        // LOCAL MODE: Create local images immediately without server upload
        const newLocalImages = []
        for (let i = 0; i < files.length; i++) {
          const file = files[i]

          // Create local image object
          const localImage = {
            id: `local-${Date.now()}-${i}`, // Local ID
            url: URL.createObjectURL(file),
            altText: file.name,
            size: file.size,
            order: this.existingImages.length + i,
            isMain: this.existingImages.length === 0 && i === 0,
            file: file, // Store file for later upload
            isLocal: true // Mark as local
          }

          this.existingImages.push(localImage)
          newLocalImages.push(localImage)

          // Track added image
          this.changeTracker.addedImages.push(localImage)

          this.uploadProgress.current++

          // Emit change event
          this.$emit('images-changed', {
            type: 'add',
            image: localImage
          })
        }

        // Update order for all images after adding new ones
        this.updateImageOrder()

        console.log(`Added ${newLocalImages.length} local images. Total changes tracked:`, this.getChangesSnapshot())

      } catch (error) {
        console.error('Error processing files:', error)
        this.error = 'Error processing files'
      } finally {
        this.isUploading = false
        this.uploadProgress = { current: 0, total: 0 }
      }
    },

    // Drag and drop methods
    updateDraggableImages() {
      // Update draggable images array to match visible images
      this.draggableImages = [...this.visibleImages]
    },

    onDragStart() {
      this.isDragging = true
    },

    onDragEnd() {
      this.isDragging = false
      this.updateImageOrderFromDrag()
    },

    updateImageOrderFromDrag() {
      // Update existingImages based on draggableImages order
      let hasChanges = false

      // Update order and main image status based on draggableImages
      this.draggableImages.forEach((dragImg, index) => {
        const existingImg = this.existingImages.find(img => img.id === dragImg.id)
        if (existingImg) {
          const oldOrderValue = existingImg.order
          const oldIsMain = existingImg.isMain

          existingImg.order = index
          existingImg.isMain = index === 0 // First image is always main

          // Track order changes for server images
          if (!existingImg.isLocal && (oldOrderValue !== index || oldIsMain !== existingImg.isMain)) {
            hasChanges = true
            this.changeTracker.hasOrderChanges = true
            if (oldIsMain !== existingImg.isMain) {
              this.changeTracker.hasMainImageChange = true
            }
          }
        }
      })

      // Rebuild existingImages array with new order
      const deletedImages = this.existingImages.filter(img => img.markedForDeletion)
      this.existingImages = [...this.draggableImages, ...deletedImages]

      if (hasChanges) {
        console.log('Image order updated locally. Changes tracked:', this.getChangesSnapshot())
        this.$emit('images-reordered', this.draggableImages)
      }
    },

    updateImageOrder() {
      // Helper method to update order and main image status
      const visibleImages = this.existingImages.filter(img => !img.markedForDeletion)
      visibleImages.forEach((img, index) => {
        const oldOrder = img.order
        const oldIsMain = img.isMain

        img.order = index
        img.isMain = index === 0 // First visible image is always main

        // Track changes for server images
        if (!img.isLocal && (oldOrder !== index || oldIsMain !== img.isMain)) {
          this.changeTracker.hasOrderChanges = true
          if (oldIsMain !== img.isMain) {
            this.changeTracker.hasMainImageChange = true
          }
        }
      })
    },

    // Selection methods
    isSelected(imageId) {
      return this.selectedImages.includes(imageId)
    },

    toggleSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId)
      if (index > -1) {
        this.selectedImages.splice(index, 1)
      } else {
        this.selectedImages.push(imageId)
      }
    },

    deleteSelected() {
      if (this.selectedImages.length === 0) return

      try {
        // LOCAL MODE: Delete selected images locally only
        for (const imageId of this.selectedImages) {
          const image = this.existingImages.find(img => img.id === imageId)
          if (image) {
            this.deleteImage(image, false) // Don't emit individual events
          }
        }

        this.selectedImages = []

        // Emit single change event
        this.$emit('images-changed', {
          type: 'bulk-delete',
          images: this.existingImages
        })

      } catch (error) {
        console.error('Error deleting selected images:', error)
        this.error = 'Failed to delete selected images'
      }
    },

    deleteImage(image, emitEvent = true) {
      try {
        // LOCAL MODE: Mark for deletion or remove immediately if local

        if (image.isLocal) {
          // Clean up blob URL and remove immediately for local images
          if (image.url && image.url.startsWith('blob:')) {
            URL.revokeObjectURL(image.url)
          }

          // Remove from local array
          this.existingImages = this.existingImages.filter(img => img.id !== image.id)

          // Remove from added images tracker if it was recently added
          this.changeTracker.addedImages = this.changeTracker.addedImages.filter(img => img.id !== image.id)
        } else {
          // Mark server images for deletion
          image.markedForDeletion = true

          // Track deleted image
          if (!this.changeTracker.deletedImages.find(img => img.id === image.id)) {
            this.changeTracker.deletedImages.push(image)
          }

          // Hide from UI by filtering out marked images
          // But keep in array for processChanges to handle deletion
        }

        this.selectedImages = this.selectedImages.filter(id => id !== image.id)

        // Update order and main status for remaining visible images
        this.updateImageOrder()

        if (emitEvent) {
          this.$emit('images-changed', {
            type: 'delete',
            image: image
          })
        }

        console.log('Image deleted. Changes tracked:', this.getChangesSnapshot())

      } catch (error) {
        console.error('Error deleting image:', error)
        this.error = 'Failed to delete image'
      }
    },

    // Image property update methods
    updateImageAltText(image, newAltText) {
      if (image.altText !== newAltText) {
        const oldAltText = image.altText
        image.altText = newAltText

        // Track changes for server images
        if (!image.isLocal) {
          const existingUpdate = this.changeTracker.updatedImages.find(img => img.id === image.id)
          if (existingUpdate) {
            existingUpdate.altText = newAltText
          } else {
            this.changeTracker.updatedImages.push({
              id: image.id,
              altText: newAltText,
              originalAltText: oldAltText
            })
          }
        }

        console.log('Alt text updated. Changes tracked:', this.getChangesSnapshot())

        this.$emit('images-changed', {
          type: 'update',
          image: image,
          property: 'altText',
          oldValue: oldAltText,
          newValue: newAltText
        })
      }
    },

    startAltTextEdit(image) {
      // Set editing mode
      this.$set(image, 'editingAltText', true)
      this.$set(image, 'tempAltText', image.altText || '')

      // Focus input on next tick
      this.$nextTick(() => {
        const inputs = this.$refs.altTextInput
        if (inputs) {
          const input = Array.isArray(inputs) ? inputs[inputs.length - 1] : inputs
          if (input) {
            input.focus()
            input.select()
          }
        }
      })
    },

    saveAltText(image) {
      if (image.tempAltText !== undefined) {
        this.updateImageAltText(image, image.tempAltText.trim())
      }
      this.cancelAltTextEdit(image)
    },

    cancelAltTextEdit(image) {
      this.$set(image, 'editingAltText', false)
      this.$set(image, 'tempAltText', '')
    },

    // Modal methods
    openImageModal(image) {
      this.modalImage = image
    },

    closeImageModal() {
      this.modalImage = null
    },

    handleImageError(image) {
      console.error('Image load error:', image)
      if (image) {
        image.error = true
      }
    },

    // Utility methods
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // Enhanced change tracking methods
    hasChanges() {
      // Check if there are any pending changes
      return this.changeTracker.addedImages.length > 0 ||
             this.changeTracker.deletedImages.length > 0 ||
             this.changeTracker.hasOrderChanges ||
             this.changeTracker.hasMainImageChange ||
             this.changeTracker.updatedImages.length > 0 ||
             this.existingImages.some(img => img.isLocal) ||
             this.existingImages.some(img => img.markedForDeletion)
    },

    getChangesSnapshot() {
      return {
        addedImages: this.changeTracker.addedImages.length,
        deletedImages: this.changeTracker.deletedImages.length,
        reorderedImages: this.changeTracker.hasOrderChanges,
        updatedImages: this.changeTracker.updatedImages.length,
        totalChanges: this.changeTracker.addedImages.length +
                     this.changeTracker.deletedImages.length +
                     this.changeTracker.updatedImages.length +
                     (this.changeTracker.hasOrderChanges ? 1 : 0)
      }
    },

    resetChanges() {
      // Reset all change tracking
      this.changeTracker = {
        addedImages: [],
        deletedImages: [],
        reorderedImages: [],
        updatedImages: [],
        hasOrderChanges: false,
        hasMainImageChange: false
      }

      // Clean up blob URLs for local images
      this.existingImages.forEach(img => {
        if (img.isLocal && img.url && img.url.startsWith('blob:')) {
          URL.revokeObjectURL(img.url)
        }
      })

      // Reset to original state
      this.existingImages = [...(this.currentImages || [])]
      this.selectedImages = []
      this.error = null
    },

    async processChanges() {
      if (!this.entityId || this.isCreate) {
        // In create mode, return local images for parent to handle
        return {
          success: true,
          images: this.existingImages.filter(img => !img.markedForDeletion),
          errors: []
        }
      }

      try {
        const { default: imageService } = await import('@/services/image.service')
        const { default: apiClient } = await import('@/services/api.service')
        const errors = []
        const uploadedImages = []

        console.log('Processing changes:', this.getChangesSnapshot())

        // 1. Delete images marked for deletion (only if they exist on server)
        const imagesToDelete = this.changeTracker.deletedImages.filter(img => img.id && !img.isLocal)
        for (const image of imagesToDelete) {
          try {
            console.log('Deleting image from server:', image.altText || 'Unnamed image')
            await imageService.deleteProductImage(image.id, this.entityId)
            console.log('Successfully deleted image:', image.id)
          } catch (error) {
            console.error('Error deleting image:', image.altText || 'Unnamed image', error)
            // Don't fail the entire process for deletion errors
            errors.push(`Failed to delete ${image.altText || 'image'}: ${error.message}`)
          }
        }

        // 2. Upload new local images
        for (const image of this.existingImages) {
          if (image.isLocal && image.file && !image.markedForDeletion) {
            try {
              console.log('Uploading local image:', image.altText)
              const response = await imageService.uploadImage(
                'product',
                this.entityId,
                image.file
              )

              // Update image with server data
              const serverImage = {
                id: response.data?.id || response.id,
                url: response.data?.url || response.url || response.fileUrl,
                altText: image.altText,
                size: image.size,
                order: image.order,
                isMain: image.isMain,
                isLocal: false
              }

              uploadedImages.push(serverImage)

              // Clean up blob URL
              if (image.url && image.url.startsWith('blob:')) {
                URL.revokeObjectURL(image.url)
              }

            } catch (error) {
              console.error('Error uploading image:', image.altText, error)
              errors.push(`Failed to upload ${image.altText}: ${error.message}`)
            }
          } else if (!image.isLocal && !image.markedForDeletion) {
            // Keep existing server images
            uploadedImages.push(image)
          }
        }

        // 2. Update alt text for changed images
        for (const updatedImage of this.changeTracker.updatedImages) {
          try {
            console.log('Updating alt text for image:', updatedImage.id)
            await imageService.updateProductImageAltText(this.entityId, updatedImage.id, updatedImage.altText)
            console.log('Successfully updated alt text for image:', updatedImage.id)
          } catch (error) {
            console.error('Error updating alt text:', error)
            errors.push(`Failed to update alt text for image: ${error.message}`)
          }
        }

        // 3. Update order for all uploaded images (only if they have valid IDs and order changed)
        if (this.changeTracker.hasOrderChanges) {
          const imagesToReorder = uploadedImages.filter(img => img.id && !img.id.toString().startsWith('local-'))
          if (imagesToReorder.length > 0) {
            try {
              await Promise.all(imagesToReorder.map(img =>
                imageService.updateProductImageOrder(this.entityId, img.id, img.order)
              ))
              console.log('Successfully updated image order for', imagesToReorder.length, 'images')
            } catch (error) {
              console.error('Error updating image order:', error)
              // Don't fail the entire process for order update errors
              errors.push('Failed to update image order')
            }
          }
        }

        // 4. Set main image only if there are changes and we have valid images
        // Skip this step if we just uploaded images - the upload handler already sets the first image as main
        if (this.changeTracker.hasMainImageChange && this.changeTracker.addedImages.length === 0) {
          const validImages = uploadedImages.filter(img => img.id && !img.id.toString().startsWith('local-'))
          if (validImages.length > 0) {
            const mainImage = validImages.find(img => img.isMain) || validImages[0]
            try {
              // Use imageService method instead of direct API call
              await imageService.setMainProductImage(this.entityId, mainImage.id)
              console.log('Successfully set main image:', mainImage.id)
            } catch (error) {
              console.error('Error setting main image:', error)
              // Don't fail the entire process for main image errors - this is not critical
              console.warn('Main image setting failed, but this is not critical as upload handler should have set it')
            }
          }
        }

        // Update local state with server images
        this.existingImages = uploadedImages

        // Clear change tracking after successful processing
        if (errors.length === 0) {
          this.changeTracker = {
            addedImages: [],
            deletedImages: [],
            reorderedImages: [],
            updatedImages: [],
            hasOrderChanges: false,
            hasMainImageChange: false
          }
          console.log('All changes processed successfully. Change tracking cleared.')
        }

        return {
          success: errors.length === 0,
          images: uploadedImages,
          errors: errors
        }

      } catch (error) {
        console.error('Error processing changes:', error)
        return {
          success: false,
          images: [],
          errors: [error.message]
        }
      }
    },

    refreshImages(newImages) {
      console.log('ProductImageUploader refreshImages called with:', newImages?.length || 0, 'images');

      try {
        // Clean up existing blob URLs
        this.existingImages.forEach(img => {
          if (img.url && img.url.startsWith('blob:')) {
            URL.revokeObjectURL(img.url)
          }
        })

        // Update with new images
        this.existingImages = [...(newImages || [])];
        this.updateDraggableImages();

        // Reset change tracker since we're syncing with server
        this.changeTracker = {
          addedImages: [],
          deletedImages: [],
          reorderedImages: [],
          updatedImages: [],
          hasOrderChanges: false,
          hasMainImageChange: false
        }

        console.log('ProductImageUploader images refreshed successfully');
      } catch (error) {
        console.error('Error refreshing images:', error);
      }
    },

    resetChanges() {
      console.log('ProductImageUploader resetChanges called');

      try {
        // Reset change tracker
        this.changeTracker = {
          addedImages: [],
          deletedImages: [],
          reorderedImages: [],
          updatedImages: [],
          hasOrderChanges: false,
          hasMainImageChange: false
        }

        console.log('ProductImageUploader changes reset successfully');
      } catch (error) {
        console.error('Error resetting changes:', error);
      }
    },

    reset() {
      try {
        // Clean up blob URLs
        this.pendingImages.forEach(img => {
          if (img.url && img.url.startsWith('blob:')) {
            URL.revokeObjectURL(img.url)
          }
        })

        this.pendingImages = []
        this.selectedImages = []
        this.error = null
        this.modalImage = null
        this.isDragOver = false
        this.isDragging = false
        this.isUploading = false
        this.uploadProgress = { current: 0, total: 0 }
      } catch (error) {
        console.error('Error resetting uploader:', error)
      }
    }
  },

  watch: {
    currentImages: {
      handler(newImages) {
        this.existingImages = [...(newImages || [])]
        this.updateDraggableImages()
      },
      deep: true
    },
    visibleImages: {
      handler() {
        this.updateDraggableImages()
      },
      deep: true
    }
  },

  beforeUnmount() {
    this.reset()
  }
}
</script>

<style scoped>
.product-image-uploader {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* Header */
.uploader-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-content {
  flex: 1;
}

.header-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-description {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
  line-height: 1.4;
}

.header-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

/* Upload Zone */
.upload-zone {
  margin: 1.5rem;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;
  position: relative;
  overflow: hidden;
}

.upload-zone:hover {
  border-color: #667eea;
  background: #f0f4ff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.upload-zone--dragover {
  border-color: #667eea;
  background: #eff6ff;
  transform: scale(1.02);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.2);
}

.upload-zone--uploading {
  cursor: not-allowed;
  opacity: 0.7;
  pointer-events: none;
}

.upload-zone--compact {
  padding: 2rem 1.5rem;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 3rem;
  color: #9ca3af;
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
  color: #667eea;
  transform: scale(1.1);
}

.upload-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.upload-text p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.upload-hint {
  font-size: 0.75rem !important;
  color: #9ca3af !important;
  font-style: italic;
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* Max Images Warning */
.max-images-warning {
  margin: 1rem 1.5rem;
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Error Message */
.error-message {
  margin: 1rem 1.5rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Images Section */
.images-section {
  padding: 0 1.5rem 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.section-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.section-description {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-action {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-action:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-action--danger {
  border-color: #ef4444;
  color: #ef4444;
}

.btn-action--danger:hover {
  background: #ef4444;
  color: white;
}

.btn-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Images Grid */
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.images-draggable {
  display: contents;
}

.image-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: grab;
}

.image-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.image-card--main {
  border-color: #fbbf24;
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.2);
}

.image-card--selected {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.image-card--pending {
  opacity: 0.7;
  cursor: default;
}

.image-card--dragging {
  cursor: grabbing;
  transform: rotate(5deg);
  z-index: 1000;
}

.image-checkbox {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  z-index: 10;
}

.image-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

.main-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.order-badge {
  position: absolute;
  top: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 10;
}

.image-wrapper {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.image-card:hover .image-preview {
  transform: scale(1.05);
}

/* Upload Overlay */
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
}

.upload-spinner {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.upload-progress-text {
  font-weight: 600;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem 0.5rem 0.5rem;
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card:hover .image-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.action-btn--view:hover {
  background: #3b82f6;
  color: white;
}

.action-btn--delete:hover {
  background: #ef4444;
  color: white;
}

.image-info {
  padding: 0.75rem;
  background: #f9fafb;
}

.image-name-container {
  position: relative;
  margin-bottom: 0.25rem;
}

.image-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  position: relative;
}

.image-name:hover {
  background-color: #f3f4f6;
}

.edit-icon {
  opacity: 0;
  margin-left: 0.5rem;
  font-size: 0.75rem;
  transition: opacity 0.2s ease;
}

.image-name:hover .edit-icon {
  opacity: 0.6;
}

.alt-text-input {
  width: 100%;
  padding: 0.25rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.alt-text-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #6b7280;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.meta-item {
  white-space: nowrap;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
}

.meta-item--local {
  background-color: #dbeafe;
  color: #1e40af;
  font-weight: 500;
}

.meta-item--deleted {
  background-color: #fee2e2;
  color: #dc2626;
  font-weight: 500;
}

/* Pending Images */
.pending-images {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.pending-title {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pending-title::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #fbbf24;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Image Modal */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.modal-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: all 0.2s ease;
  z-index: 10;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
  .uploader-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-stats {
    align-items: flex-start;
    flex-direction: row;
    gap: 0.5rem;
  }

  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .section-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .upload-zone {
    margin: 1rem;
    padding: 2rem 1rem;
  }

  .images-section {
    padding: 0 1rem 1rem;
  }
}
</style>
