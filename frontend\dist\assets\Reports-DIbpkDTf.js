const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/auto-_svZoXzF.js","assets/chart-DFPvZH9M.js"])))=>i.map(i=>d[i]);
import{_ as mt,g as k,C as Pt,h as p,i as ht,c as l,a as t,d as b,k as G,x as D,y as H,l as pt,D as ut,F as V,p as I,n as x,t as n,o,q as _t,H as dt,M as X,j as wt,B as gt,W as Ct,L as bt,b as St,r as yt,s as kt,V as $t}from"./index-L-hJxM_5.js";const Tt={name:"ReportFilters",props:{reportType:{type:String,default:""},dateRange:{type:Object,default:()=>({startDate:null,endDate:null})},additionalFilters:{type:Object,default:()=>({})}},emits:["update:reportType","update:dateRange","update:additionalFilters","filtersChanged"],setup(v,{emit:e}){const d=k(v.reportType),a=Pt({startDate:v.dateRange.startDate?M(v.dateRange.startDate):"",endDate:v.dateRange.endDate?M(v.dateRange.endDate):""}),m=Pt({...v.additionalFilters}),C=k(""),s=k([]),u=k([]),R=[{key:"today",label:"Today",days:0},{key:"yesterday",label:"Yesterday",days:1},{key:"last7days",label:"Last 7 Days",days:7},{key:"last30days",label:"Last 30 Days",days:30},{key:"last90days",label:"Last 90 Days",days:90},{key:"thisMonth",label:"This Month",type:"month"},{key:"lastMonth",label:"Last Month",type:"lastMonth"}],E=p(()=>d.value&&a.startDate&&a.endDate),U=p(()=>{let N=0;return d.value&&N++,a.startDate&&a.endDate&&N++,Object.values(m).forEach(z=>{z!==""&&z!==null&&z!==void 0&&N++}),N});function M(N){return N?new Date(N).toISOString().split("T")[0]:""}function q(){Object.keys(m).forEach(N=>{delete m[N]}),e("update:reportType",d.value),e("filtersChanged")}function Y(){C.value="";const N=a.startDate?new Date(a.startDate):null,z=a.endDate?new Date(a.endDate):null;e("update:dateRange",{startDate:N,endDate:z}),e("filtersChanged")}function $(){e("update:additionalFilters",{...m}),e("filtersChanged")}function T(N){C.value=N.key;const z=new Date;let j,Z;N.type==="month"?(j=new Date(z.getFullYear(),z.getMonth(),1),Z=new Date(z.getFullYear(),z.getMonth()+1,0)):N.type==="lastMonth"?(j=new Date(z.getFullYear(),z.getMonth()-1,1),Z=new Date(z.getFullYear(),z.getMonth(),0)):(j=new Date(z.getTime()-N.days*24*60*60*1e3),Z=z),a.startDate=M(j),a.endDate=M(Z),Y()}function ot(){d.value="",a.startDate="",a.endDate="",Object.keys(m).forEach(N=>{delete m[N]}),C.value="",e("update:reportType",""),e("update:dateRange",{startDate:null,endDate:null}),e("update:additionalFilters",{}),e("filtersChanged")}function at(){E.value&&e("filtersChanged")}async function J(){try{s.value=[{id:1,name:"Electronics"},{id:2,name:"Clothing"},{id:3,name:"Books"},{id:4,name:"Home & Garden"},{id:5,name:"Sports"}],u.value=[{id:1,name:"TechStore"},{id:2,name:"FashionHub"},{id:3,name:"BookWorld"},{id:4,name:"HomeDecor"},{id:5,name:"SportsPro"}]}catch(N){console.error("Failed to load filter data:",N)}}return ht(()=>{J()}),{localReportType:d,localDateRange:a,localAdditionalFilters:m,activePreset:C,categories:s,sellers:u,datePresets:R,canApplyFilters:E,activeFiltersCount:U,handleReportTypeChange:q,handleDateChange:Y,handleFiltersChange:$,applyDatePreset:T,resetFilters:ot,applyFilters:at}}},Mt={class:"report-filters"},Ut={class:"filters-header"},Et={class:"filters-grid"},Ot={class:"filter-group"},It={class:"filter-group"},Lt={class:"date-range-container"},Bt={class:"filter-group"},Nt={class:"preset-buttons"},jt=["onClick"],qt={class:"filter-group"},zt={class:"filter-group"},Gt={class:"filter-group"},Ht={class:"amount-range"},Qt={class:"filter-group"},Kt=["value"],Wt={class:"filter-group"},Yt=["value"],Zt={class:"filter-group"},Jt={class:"filter-group"},Xt=["value"],te={class:"filter-group"},ee={class:"amount-range"},se={class:"filter-group"},ae={class:"rating-range"},ne={class:"filter-group"},oe={class:"filter-group"},le={class:"filter-group"},re={class:"filter-group"},ie={class:"filter-group"},de={class:"filter-group"},ce={class:"filter-group"},ue={class:"filters-actions"},ve=["disabled"],fe={key:0,class:"filters-summary"};function ge(v,e,d,a,m,C){return o(),l("div",Mt,[t("div",Ut,[e[47]||(e[47]=t("h3",{class:"filters-title"},[t("i",{class:"fas fa-filter"}),b(" Report Filters ")],-1)),t("button",{onClick:e[0]||(e[0]=(...s)=>a.resetFilters&&a.resetFilters(...s)),class:"reset-btn"},e[46]||(e[46]=[t("i",{class:"fas fa-undo"},null,-1),b(" Reset ")]))]),t("div",Et,[t("div",Ot,[e[49]||(e[49]=t("label",{class:"filter-label"},"Report Type",-1)),D(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>a.localReportType=s),onChange:e[2]||(e[2]=(...s)=>a.handleReportTypeChange&&a.handleReportTypeChange(...s)),class:"filter-select"},e[48]||(e[48]=[pt('<option value="" data-v-3d9c3286>Select Report Type</option><option value="financial" data-v-3d9c3286>Financial Reports</option><option value="sales" data-v-3d9c3286>Sales Reports</option><option value="products" data-v-3d9c3286>Product Reports</option><option value="users" data-v-3d9c3286>User Reports</option><option value="orders" data-v-3d9c3286>Order Reports</option>',6)]),544),[[H,a.localReportType]])]),t("div",It,[e[51]||(e[51]=t("label",{class:"filter-label"},"Date Range",-1)),t("div",Lt,[D(t("input",{type:"date","onUpdate:modelValue":e[3]||(e[3]=s=>a.localDateRange.startDate=s),onChange:e[4]||(e[4]=(...s)=>a.handleDateChange&&a.handleDateChange(...s)),class:"filter-input date-input",placeholder:"Start Date"},null,544),[[ut,a.localDateRange.startDate]]),e[50]||(e[50]=t("span",{class:"date-separator"},"to",-1)),D(t("input",{type:"date","onUpdate:modelValue":e[5]||(e[5]=s=>a.localDateRange.endDate=s),onChange:e[6]||(e[6]=(...s)=>a.handleDateChange&&a.handleDateChange(...s)),class:"filter-input date-input",placeholder:"End Date"},null,544),[[ut,a.localDateRange.endDate]])])]),t("div",Bt,[e[52]||(e[52]=t("label",{class:"filter-label"},"Quick Presets",-1)),t("div",Nt,[(o(!0),l(V,null,I(a.datePresets,s=>(o(),l("button",{key:s.key,onClick:u=>a.applyDatePreset(s),class:x(["preset-btn",{active:a.activePreset===s.key}])},n(s.label),11,jt))),128))])]),a.localReportType?(o(),l(V,{key:0},[a.localReportType==="financial"?(o(),l(V,{key:0},[t("div",qt,[e[54]||(e[54]=t("label",{class:"filter-label"},"Transaction Type",-1)),D(t("select",{"onUpdate:modelValue":e[7]||(e[7]=s=>a.localAdditionalFilters.transactionType=s),onChange:e[8]||(e[8]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[53]||(e[53]=[t("option",{value:""},"All Transactions",-1),t("option",{value:"sale"},"Sales",-1),t("option",{value:"refund"},"Refunds",-1),t("option",{value:"commission"},"Commission",-1)]),544),[[H,a.localAdditionalFilters.transactionType]])]),t("div",zt,[e[56]||(e[56]=t("label",{class:"filter-label"},"Payment Method",-1)),D(t("select",{"onUpdate:modelValue":e[9]||(e[9]=s=>a.localAdditionalFilters.paymentMethod=s),onChange:e[10]||(e[10]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[55]||(e[55]=[t("option",{value:""},"All Methods",-1),t("option",{value:"card"},"Credit Card",-1),t("option",{value:"paypal"},"PayPal",-1),t("option",{value:"bank_transfer"},"Bank Transfer",-1)]),544),[[H,a.localAdditionalFilters.paymentMethod]])]),t("div",Gt,[e[58]||(e[58]=t("label",{class:"filter-label"},"Amount Range",-1)),t("div",Ht,[D(t("input",{type:"number","onUpdate:modelValue":e[11]||(e[11]=s=>a.localAdditionalFilters.minAmount=s),onInput:e[12]||(e[12]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),placeholder:"Min Amount",class:"filter-input amount-input"},null,544),[[ut,a.localAdditionalFilters.minAmount]]),e[57]||(e[57]=t("span",{class:"amount-separator"},"-",-1)),D(t("input",{type:"number","onUpdate:modelValue":e[13]||(e[13]=s=>a.localAdditionalFilters.maxAmount=s),onInput:e[14]||(e[14]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),placeholder:"Max Amount",class:"filter-input amount-input"},null,544),[[ut,a.localAdditionalFilters.maxAmount]])])])],64)):G("",!0),a.localReportType==="sales"?(o(),l(V,{key:1},[t("div",Qt,[e[60]||(e[60]=t("label",{class:"filter-label"},"Category",-1)),D(t("select",{"onUpdate:modelValue":e[15]||(e[15]=s=>a.localAdditionalFilters.categoryId=s),onChange:e[16]||(e[16]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},[e[59]||(e[59]=t("option",{value:""},"All Categories",-1)),(o(!0),l(V,null,I(a.categories,s=>(o(),l("option",{key:s.id,value:s.id},n(s.name),9,Kt))),128))],544),[[H,a.localAdditionalFilters.categoryId]])]),t("div",Wt,[e[62]||(e[62]=t("label",{class:"filter-label"},"Seller",-1)),D(t("select",{"onUpdate:modelValue":e[17]||(e[17]=s=>a.localAdditionalFilters.sellerId=s),onChange:e[18]||(e[18]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},[e[61]||(e[61]=t("option",{value:""},"All Sellers",-1)),(o(!0),l(V,null,I(a.sellers,s=>(o(),l("option",{key:s.id,value:s.id},n(s.name),9,Yt))),128))],544),[[H,a.localAdditionalFilters.sellerId]])]),t("div",Zt,[e[64]||(e[64]=t("label",{class:"filter-label"},"Region",-1)),D(t("select",{"onUpdate:modelValue":e[19]||(e[19]=s=>a.localAdditionalFilters.region=s),onChange:e[20]||(e[20]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[63]||(e[63]=[pt('<option value="" data-v-3d9c3286>All Regions</option><option value="kyiv" data-v-3d9c3286>Kyiv</option><option value="lviv" data-v-3d9c3286>Lviv</option><option value="odesa" data-v-3d9c3286>Odesa</option><option value="kharkiv" data-v-3d9c3286>Kharkiv</option><option value="dnipro" data-v-3d9c3286>Dnipro</option>',6)]),544),[[H,a.localAdditionalFilters.region]])])],64)):G("",!0),a.localReportType==="products"?(o(),l(V,{key:2},[t("div",Jt,[e[66]||(e[66]=t("label",{class:"filter-label"},"Category",-1)),D(t("select",{"onUpdate:modelValue":e[21]||(e[21]=s=>a.localAdditionalFilters.categoryId=s),onChange:e[22]||(e[22]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},[e[65]||(e[65]=t("option",{value:""},"All Categories",-1)),(o(!0),l(V,null,I(a.categories,s=>(o(),l("option",{key:s.id,value:s.id},n(s.name),9,Xt))),128))],544),[[H,a.localAdditionalFilters.categoryId]])]),t("div",te,[e[68]||(e[68]=t("label",{class:"filter-label"},"Price Range",-1)),t("div",ee,[D(t("input",{type:"number","onUpdate:modelValue":e[23]||(e[23]=s=>a.localAdditionalFilters.minPrice=s),onInput:e[24]||(e[24]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),placeholder:"Min Price",class:"filter-input amount-input"},null,544),[[ut,a.localAdditionalFilters.minPrice]]),e[67]||(e[67]=t("span",{class:"amount-separator"},"-",-1)),D(t("input",{type:"number","onUpdate:modelValue":e[25]||(e[25]=s=>a.localAdditionalFilters.maxPrice=s),onInput:e[26]||(e[26]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),placeholder:"Max Price",class:"filter-input amount-input"},null,544),[[ut,a.localAdditionalFilters.maxPrice]])])]),t("div",se,[e[71]||(e[71]=t("label",{class:"filter-label"},"Rating Range",-1)),t("div",ae,[D(t("select",{"onUpdate:modelValue":e[27]||(e[27]=s=>a.localAdditionalFilters.minRating=s),onChange:e[28]||(e[28]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[69]||(e[69]=[pt('<option value="" data-v-3d9c3286>Min Rating</option><option value="1" data-v-3d9c3286>1 Star</option><option value="2" data-v-3d9c3286>2 Stars</option><option value="3" data-v-3d9c3286>3 Stars</option><option value="4" data-v-3d9c3286>4 Stars</option><option value="5" data-v-3d9c3286>5 Stars</option>',6)]),544),[[H,a.localAdditionalFilters.minRating]]),D(t("select",{"onUpdate:modelValue":e[29]||(e[29]=s=>a.localAdditionalFilters.maxRating=s),onChange:e[30]||(e[30]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[70]||(e[70]=[pt('<option value="" data-v-3d9c3286>Max Rating</option><option value="1" data-v-3d9c3286>1 Star</option><option value="2" data-v-3d9c3286>2 Stars</option><option value="3" data-v-3d9c3286>3 Stars</option><option value="4" data-v-3d9c3286>4 Stars</option><option value="5" data-v-3d9c3286>5 Stars</option>',6)]),544),[[H,a.localAdditionalFilters.maxRating]])])]),t("div",ne,[e[73]||(e[73]=t("label",{class:"filter-label"},"Product Status",-1)),D(t("select",{"onUpdate:modelValue":e[31]||(e[31]=s=>a.localAdditionalFilters.productStatus=s),onChange:e[32]||(e[32]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[72]||(e[72]=[t("option",{value:""},"All Statuses",-1),t("option",{value:"active"},"Active",-1),t("option",{value:"inactive"},"Inactive",-1),t("option",{value:"sale"},"On Sale",-1)]),544),[[H,a.localAdditionalFilters.productStatus]])])],64)):G("",!0),a.localReportType==="users"?(o(),l(V,{key:3},[t("div",oe,[e[75]||(e[75]=t("label",{class:"filter-label"},"User Role",-1)),D(t("select",{"onUpdate:modelValue":e[33]||(e[33]=s=>a.localAdditionalFilters.userRole=s),onChange:e[34]||(e[34]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[74]||(e[74]=[pt('<option value="" data-v-3d9c3286>All Roles</option><option value="buyer" data-v-3d9c3286>Buyers</option><option value="seller" data-v-3d9c3286>Sellers</option><option value="seller_owner" data-v-3d9c3286>Seller Owners</option><option value="admin" data-v-3d9c3286>Admins</option>',5)]),544),[[H,a.localAdditionalFilters.userRole]])]),t("div",le,[e[77]||(e[77]=t("label",{class:"filter-label"},"Activity Status",-1)),D(t("select",{"onUpdate:modelValue":e[35]||(e[35]=s=>a.localAdditionalFilters.activityStatus=s),onChange:e[36]||(e[36]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[76]||(e[76]=[t("option",{value:""},"All Users",-1),t("option",{value:"active"},"Active",-1),t("option",{value:"inactive"},"Inactive",-1)]),544),[[H,a.localAdditionalFilters.activityStatus]])]),t("div",re,[e[79]||(e[79]=t("label",{class:"filter-label"},"Region",-1)),D(t("select",{"onUpdate:modelValue":e[37]||(e[37]=s=>a.localAdditionalFilters.region=s),onChange:e[38]||(e[38]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[78]||(e[78]=[pt('<option value="" data-v-3d9c3286>All Regions</option><option value="kyiv" data-v-3d9c3286>Kyiv</option><option value="lviv" data-v-3d9c3286>Lviv</option><option value="odesa" data-v-3d9c3286>Odesa</option><option value="kharkiv" data-v-3d9c3286>Kharkiv</option><option value="dnipro" data-v-3d9c3286>Dnipro</option>',6)]),544),[[H,a.localAdditionalFilters.region]])])],64)):G("",!0),a.localReportType==="orders"?(o(),l(V,{key:4},[t("div",ie,[e[81]||(e[81]=t("label",{class:"filter-label"},"Order Status",-1)),D(t("select",{"onUpdate:modelValue":e[39]||(e[39]=s=>a.localAdditionalFilters.orderStatus=s),onChange:e[40]||(e[40]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[80]||(e[80]=[pt('<option value="" data-v-3d9c3286>All Statuses</option><option value="pending" data-v-3d9c3286>Pending</option><option value="processing" data-v-3d9c3286>Processing</option><option value="shipped" data-v-3d9c3286>Shipped</option><option value="delivered" data-v-3d9c3286>Delivered</option><option value="cancelled" data-v-3d9c3286>Cancelled</option>',6)]),544),[[H,a.localAdditionalFilters.orderStatus]])]),t("div",de,[e[83]||(e[83]=t("label",{class:"filter-label"},"Payment Method",-1)),D(t("select",{"onUpdate:modelValue":e[41]||(e[41]=s=>a.localAdditionalFilters.paymentMethod=s),onChange:e[42]||(e[42]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[82]||(e[82]=[t("option",{value:""},"All Methods",-1),t("option",{value:"card"},"Credit Card",-1),t("option",{value:"paypal"},"PayPal",-1),t("option",{value:"bank_transfer"},"Bank Transfer",-1)]),544),[[H,a.localAdditionalFilters.paymentMethod]])]),t("div",ce,[e[85]||(e[85]=t("label",{class:"filter-label"},"Shipping Method",-1)),D(t("select",{"onUpdate:modelValue":e[43]||(e[43]=s=>a.localAdditionalFilters.shippingMethod=s),onChange:e[44]||(e[44]=(...s)=>a.handleFiltersChange&&a.handleFiltersChange(...s)),class:"filter-select"},e[84]||(e[84]=[t("option",{value:""},"All Methods",-1),t("option",{value:"standard"},"Standard",-1),t("option",{value:"express"},"Express",-1),t("option",{value:"overnight"},"Overnight",-1)]),544),[[H,a.localAdditionalFilters.shippingMethod]])])],64)):G("",!0)],64)):G("",!0)]),t("div",ue,[t("button",{onClick:e[45]||(e[45]=(...s)=>a.applyFilters&&a.applyFilters(...s)),class:"apply-btn",disabled:!a.canApplyFilters},e[86]||(e[86]=[t("i",{class:"fas fa-search"},null,-1),b(" Generate Report ")]),8,ve),a.activeFiltersCount>0?(o(),l("div",fe,n(a.activeFiltersCount)+" filter"+n(a.activeFiltersCount>1?"s":"")+" applied ",1)):G("",!0)])])}const pe=mt(Tt,[["render",ge],["__scopeId","data-v-3d9c3286"]]);class me{constructor(){this.baseUrl="/api/admin/reports"}async getFinancialReport(e={}){try{const d=this._buildQueryParams(e);return(await _t.get(`${this.baseUrl}/financial${d}`)).data}catch(d){throw console.error("Error fetching financial report:",d),d}}async getSalesReport(e={}){try{const d=this._buildQueryParams(e);return(await _t.get(`${this.baseUrl}/sales${d}`)).data}catch(d){throw console.error("Error fetching sales report:",d),d}}async getProductsReport(e={}){try{const d=this._buildQueryParams(e);return(await _t.get(`${this.baseUrl}/products${d}`)).data}catch(d){throw console.error("Error fetching products report:",d),d}}async getUsersReport(e={}){try{const d=this._buildQueryParams(e);return(await _t.get(`${this.baseUrl}/users${d}`)).data}catch(d){throw console.error("Error fetching users report:",d),d}}async getOrdersReport(e={}){try{const d=this._buildQueryParams(e);return(await _t.get(`${this.baseUrl}/orders${d}`)).data}catch(d){throw console.error("Error fetching orders report:",d),d}}async exportReport(e,d,a={}){try{const m=this._buildQueryParams(a);return(await _t.get(`${this.baseUrl}/${e}/export/${d}${m}`,{responseType:"blob"})).data}catch(m){throw console.error(`Error exporting ${e} report as ${d}:`,m),m}}downloadFile(e,d,a="excel"){const m=this.getFileExtension(a),C=d.includes(".")?d:`${d}.${m}`,s=window.URL.createObjectURL(e),u=document.createElement("a");u.href=s,u.download=C,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s)}getFileExtension(e){return{excel:"xlsx",csv:"csv",pdf:"pdf"}[e]||"xlsx"}generateFilename(e,d="excel",a={}){const m=this._formatDate(new Date),C=this.getFileExtension(d);return a.filename?a.filename.includes(".")?a.filename:`${a.filename}.${C}`:`${e.charAt(0).toUpperCase()+e.slice(1)}_Report_${m}.${C}`}async exportFinancialReport(e="excel",d={}){const a=await this.exportReport("financial",e,d),m=`financial-report-${this._formatDate(new Date)}.${e==="excel"?"xlsx":"csv"}`;this.downloadFile(a,m)}async exportSalesReport(e="excel",d={}){const a=await this.exportReport("sales",e,d),m=`sales-report-${this._formatDate(new Date)}.${e==="excel"?"xlsx":"csv"}`;this.downloadFile(a,m)}async exportProductsReport(e="excel",d={}){const a=await this.exportReport("products",e,d),m=`products-report-${this._formatDate(new Date)}.${e==="excel"?"xlsx":"csv"}`;this.downloadFile(a,m)}async exportUsersReport(e="excel",d={}){const a=await this.exportReport("users",e,d),m=`users-report-${this._formatDate(new Date)}.${e==="excel"?"xlsx":"csv"}`;this.downloadFile(a,m)}async exportOrdersReport(e="excel",d={}){const a=await this.exportReport("orders",e,d),m=`orders-report-${this._formatDate(new Date)}.${e==="excel"?"xlsx":"csv"}`;this.downloadFile(a,m)}_buildQueryParams(e){const d=new URLSearchParams;Object.keys(e).forEach(m=>{e[m]!==null&&e[m]!==void 0&&e[m]!==""&&(e[m]instanceof Date?d.append(m,e[m].toISOString()):d.append(m,e[m].toString()))});const a=d.toString();return a?`?${a}`:""}_formatDate(e){return e.toISOString().split("T")[0]}formatCurrency(e,d="UAH"){return typeof e!="number"||isNaN(e)?"0,00 ₴":new Intl.NumberFormat("uk-UA",{style:"currency",currency:d,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}formatNumber(e){return typeof e!="number"||isNaN(e)?"0":new Intl.NumberFormat("uk-UA",{minimumFractionDigits:0,maximumFractionDigits:0}).format(e)}formatPercentage(e){return typeof e!="number"||isNaN(e)?"0%":`${e.toFixed(2)}%`}formatDate(e){if(!e)return"-";const d=typeof e=="string"?new Date(e):e;return isNaN(d.getTime())?"-":d.toLocaleDateString("uk-UA",{year:"numeric",month:"2-digit",day:"2-digit"})}formatFileSize(e){if(typeof e!="number"||e===0)return"0 B";const d=1024,a=["B","KB","MB","GB"],m=Math.floor(Math.log(e)/Math.log(d));return`${parseFloat((e/Math.pow(d,m)).toFixed(2))} ${a[m]}`}formatTimeAgo(e){if(!e)return"-";const d=new Date,a=typeof e=="string"?new Date(e):e;if(isNaN(a.getTime()))return"-";const m=d-a,C=Math.floor(m/6e4),s=Math.floor(C/60),u=Math.floor(s/24);return C<1?"щойно":C<60?`${C} хв тому`:s<24?`${s} год тому`:u<30?`${u} дн тому`:this.formatDate(a)}formatMetricValue(e,d){switch(d){case"currency":return this.formatCurrency(e);case"percentage":return this.formatPercentage(e);case"number":return this.formatNumber(e);case"date":return this.formatDate(e);default:return String(e||"-")}}getChangeIcon(e){return e>0?"fas fa-arrow-up":e<0?"fas fa-arrow-down":"fas fa-minus"}getChangeColor(e){return e>0?"text-success":e<0?"text-danger":"text-muted"}}const O=new me;let Dt=null,At=!1;const Vt=async()=>{if(!Dt){const v=await gt(()=>import("./chart-DFPvZH9M.js"),[]);Dt=v.Chart,At||(Dt.register(...v.registerables),At=!0)}return Dt},ye={name:"ReportPreview",props:{reportType:{type:String,required:!0},data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},emits:["refresh"],setup(v,{emit:e}){const d=k(null),a=k(null),m=k(!1),C=k(""),s=k(""),u=k("asc"),R=k(1),E=k(10);let U=null,M=null;const q=p(()=>({financial:"Financial Report",sales:"Sales Report",products:"Product Report",users:"User Report",orders:"Order Report"})[v.reportType]||"Report"),Y=p(()=>({financial:"fas fa-dollar-sign",sales:"fas fa-chart-line",products:"fas fa-box",users:"fas fa-users",orders:"fas fa-shopping-cart"})[v.reportType]||"fas fa-chart-bar"),$=p(()=>{var _;return(_=v.data)!=null&&_.metrics?v.data.metrics.map(F=>({...F,value:nt(F.value,F.type),change:F.previousValue?ft(F.value,F.previousValue):null,changeIcon:it(F.value,F.previousValue),trend:w(F.value,F.previousValue)})):[]}),T=p(()=>{var _,F,c,r,g,y;return{id:"primary-chart",title:((c=(F=(_=v.data)==null?void 0:_.charts)==null?void 0:F.primary)==null?void 0:c.title)||"Primary Chart",type:k("line"),data:((y=(g=(r=v.data)==null?void 0:r.charts)==null?void 0:g.primary)==null?void 0:y.data)||[]}}),ot=p(()=>{var _,F,c,r,g,y;return{id:"secondary-chart",title:((c=(F=(_=v.data)==null?void 0:_.charts)==null?void 0:F.secondary)==null?void 0:c.title)||"Secondary Chart",data:((y=(g=(r=v.data)==null?void 0:r.charts)==null?void 0:g.secondary)==null?void 0:y.data)||[]}}),at=p(()=>{var _,F;return((F=(_=v.data)==null?void 0:_.table)==null?void 0:F.title)||"Data Table"}),J=p(()=>{var _,F;return((F=(_=v.data)==null?void 0:_.table)==null?void 0:F.columns)||[]}),N=p(()=>J.value.filter(_=>_.sortable)),z=p(()=>{var F,c;let _=((c=(F=v.data)==null?void 0:F.table)==null?void 0:c.data)||[];if(C.value){const r=C.value.toLowerCase();_=_.filter(g=>Object.values(g).some(y=>String(y).toLowerCase().includes(r)))}return s.value&&(_=[..._].sort((r,g)=>{const y=r[s.value],i=g[s.value];if(typeof y=="number"&&typeof i=="number")return u.value==="asc"?y-i:i-y;const h=String(y).toLowerCase(),P=String(i).toLowerCase();return u.value==="asc"?h.localeCompare(P):P.localeCompare(h)})),_}),j=p(()=>Math.ceil(z.value.length/E.value)),Z=p(()=>{const _=(R.value-1)*E.value,F=_+E.value;return z.value.slice(_,F)}),et=p(()=>{var _;return((_=v.data)==null?void 0:_.insights)||[]}),Q=(_,F)=>{if(!_||!F)return"";const c=new Date(_).toLocaleDateString("uk-UA"),r=new Date(F).toLocaleDateString("uk-UA");return`${c} - ${r}`},nt=(_,F)=>{switch(F){case"currency":return O.formatCurrency(_);case"percentage":return O.formatPercentage(_);case"number":return O.formatNumber(_);default:return String(_)}},lt=_=>O.formatCurrency(_),st=_=>O.formatPercentage(_),vt=_=>O.formatNumber(_),rt=_=>new Date(_).toLocaleDateString("uk-UA"),ft=(_,F)=>{const c=O.calculatePercentageChange(_,F);return O.formatPercentage(Math.abs(c))},it=(_,F)=>{if(!F)return"";const c=O.calculatePercentageChange(_,F);return c>0?"fas fa-arrow-up":c<0?"fas fa-arrow-down":"fas fa-minus"},w=(_,F)=>{if(!F)return"";const c=O.calculatePercentageChange(_,F);return c>0?"positive":c<0?"negative":"neutral"},f=_=>{s.value===_?u.value=u.value==="asc"?"desc":"asc":(s.value=_,u.value="asc"),R.value=1},S=()=>{R.value=1},L=()=>{m.value=!0,e("refresh"),setTimeout(()=>{m.value=!1},1e3)},A=async()=>{if(await X(),U&&U.destroy(),!d.value||!T.value.data)return;const _=await Vt(),F=d.value.getContext("2d");U=new _(F,{type:T.value.type.value,data:T.value.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})},B=async()=>{if(await X(),M&&M.destroy(),!a.value||!ot.value.data)return;const _=await Vt(),F=a.value.getContext("2d");M=new _(F,{type:"doughnut",data:ot.value.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})},tt=()=>{A()};return dt(()=>v.data,()=>{X(()=>{A(),B()})},{deep:!0}),dt(C,()=>{R.value=1}),ht(()=>{X(()=>{A(),B()})}),wt(()=>{U&&U.destroy(),M&&M.destroy()}),{primaryChartCanvas:d,secondaryChartCanvas:a,loading:m,searchQuery:C,sortBy:s,currentPage:R,itemsPerPage:E,reportTitle:q,reportIcon:Y,keyMetrics:$,primaryChart:T,secondaryChart:ot,tableTitle:at,tableColumns:J,sortableColumns:N,filteredData:z,totalPages:j,paginatedData:Z,insights:et,formatDateRange:Q,formatCurrency:lt,formatPercentage:st,formatNumber:vt,formatDate:rt,handleSort:f,sortTable:S,refreshReport:L,updatePrimaryChart:tt}}},he={class:"report-preview"},be={class:"report-header"},_e={class:"report-title-section"},Ce={class:"report-title"},ke={class:"report-period"},we={class:"report-actions"},Pe=["disabled"],xe={class:"metrics-summary"},Fe={class:"metrics-grid"},Re={class:"metric-icon"},De={class:"metric-content"},Se={class:"metric-label"},Ae={class:"metric-value"},Ve={key:0,class:"metric-change"},$e={class:"charts-section"},Te={class:"charts-grid"},Me={class:"chart-container primary-chart"},Ue={class:"chart-header"},Ee={class:"chart-title"},Oe={class:"chart-controls"},Ie={class:"chart-content"},Le=["id"],Be={class:"chart-container secondary-chart"},Ne={class:"chart-header"},je={class:"chart-title"},qe={class:"chart-content"},ze=["id"],Ge={class:"data-table-section"},He={class:"table-header"},Qe={class:"table-title"},Ke={class:"table-controls"},We={class:"search-box"},Ye=["value"],Ze={class:"table-container"},Je={class:"data-table"},Xe=["onClick"],ts={key:0,class:"fas fa-sort sort-icon"},es={key:0},ss={key:1},as={key:2},ns={key:3},os={key:5},ls={key:0,class:"pagination"},rs=["disabled"],is=["disabled"],ds={class:"pagination-info"},cs=["disabled"],us=["disabled"],vs={key:0,class:"insights-section"},fs={class:"insights-list"},gs={class:"insight-icon"},ps={class:"insight-content"},ms={class:"insight-title"},ys={class:"insight-description"};function hs(v,e,d,a,m,C){return o(),l("div",he,[t("div",be,[t("div",_e,[t("h2",Ce,[t("i",{class:x(a.reportIcon)},null,2),b(" "+n(a.reportTitle),1)]),t("div",ke,n(a.formatDateRange(d.dateRange.startDate,d.dateRange.endDate)),1)]),t("div",we,[t("button",{onClick:e[0]||(e[0]=(...s)=>a.refreshReport&&a.refreshReport(...s)),class:"refresh-btn",disabled:a.loading},[t("i",{class:x(["fas fa-sync-alt",{"fa-spin":a.loading}])},null,2),e[10]||(e[10]=b(" Refresh "))],8,Pe)])]),t("div",xe,[t("div",Fe,[(o(!0),l(V,null,I(a.keyMetrics,s=>(o(),l("div",{key:s.key,class:x(["metric-card",s.trend])},[t("div",Re,[t("i",{class:x(s.icon)},null,2)]),t("div",De,[t("div",Se,n(s.label),1),t("div",Ae,n(s.value),1),s.change?(o(),l("div",Ve,[t("i",{class:x(s.changeIcon)},null,2),b(" "+n(s.change),1)])):G("",!0)])],2))),128))])]),t("div",$e,[t("div",Te,[t("div",Me,[t("div",Ue,[t("h3",Ee,n(a.primaryChart.title),1),t("div",Oe,[D(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>a.primaryChart.type=s),onChange:e[2]||(e[2]=(...s)=>a.updatePrimaryChart&&a.updatePrimaryChart(...s)),class:"chart-type-select"},e[11]||(e[11]=[t("option",{value:"line"},"Line Chart",-1),t("option",{value:"bar"},"Bar Chart",-1),t("option",{value:"area"},"Area Chart",-1)]),544),[[H,a.primaryChart.type]])])]),t("div",Ie,[t("canvas",{ref:"primaryChartCanvas",id:a.primaryChart.id},null,8,Le)])]),t("div",Be,[t("div",Ne,[t("h3",je,n(a.secondaryChart.title),1)]),t("div",qe,[t("canvas",{ref:"secondaryChartCanvas",id:a.secondaryChart.id},null,8,ze)])])])]),t("div",Ge,[t("div",He,[t("h3",Qe,n(a.tableTitle),1),t("div",Ke,[t("div",We,[e[12]||(e[12]=t("i",{class:"fas fa-search"},null,-1)),D(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.searchQuery=s),type:"text",placeholder:"Search...",class:"search-input"},null,512),[[ut,a.searchQuery]])]),D(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.sortBy=s),onChange:e[5]||(e[5]=(...s)=>a.sortTable&&a.sortTable(...s)),class:"sort-select"},[e[13]||(e[13]=t("option",{value:""},"Sort by...",-1)),(o(!0),l(V,null,I(a.sortableColumns,s=>(o(),l("option",{key:s.key,value:s.key},n(s.label),9,Ye))),128))],544),[[H,a.sortBy]])])]),t("div",Ze,[t("table",Je,[t("thead",null,[t("tr",null,[(o(!0),l(V,null,I(a.tableColumns,s=>(o(),l("th",{key:s.key,onClick:u=>a.handleSort(s.key),class:x({sortable:s.sortable,active:a.sortBy===s.key})},[b(n(s.label)+" ",1),s.sortable?(o(),l("i",ts)):G("",!0)],10,Xe))),128))])]),t("tbody",null,[(o(!0),l(V,null,I(a.paginatedData,s=>(o(),l("tr",{key:s.id,class:"table-row"},[(o(!0),l(V,null,I(a.tableColumns,u=>{var R;return o(),l("td",{key:u.key,class:x(u.class)},[u.type==="currency"?(o(),l("span",es,n(a.formatCurrency(s[u.key])),1)):u.type==="percentage"?(o(),l("span",ss,n(a.formatPercentage(s[u.key])),1)):u.type==="number"?(o(),l("span",as,n(a.formatNumber(s[u.key])),1)):u.type==="date"?(o(),l("span",ns,n(a.formatDate(s[u.key])),1)):u.type==="status"?(o(),l("span",{key:4,class:x(`status-${(R=s[u.key])==null?void 0:R.toLowerCase()}`)},n(s[u.key]),3)):(o(),l("span",os,n(s[u.key]),1))],2)}),128))]))),128))])])]),a.totalPages>1?(o(),l("div",ls,[t("button",{onClick:e[6]||(e[6]=s=>a.currentPage=1),disabled:a.currentPage===1,class:"pagination-btn"},e[14]||(e[14]=[t("i",{class:"fas fa-angle-double-left"},null,-1)]),8,rs),t("button",{onClick:e[7]||(e[7]=s=>a.currentPage--),disabled:a.currentPage===1,class:"pagination-btn"},e[15]||(e[15]=[t("i",{class:"fas fa-angle-left"},null,-1)]),8,is),t("span",ds," Page "+n(a.currentPage)+" of "+n(a.totalPages)+" ("+n(a.filteredData.length)+" items) ",1),t("button",{onClick:e[8]||(e[8]=s=>a.currentPage++),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[16]||(e[16]=[t("i",{class:"fas fa-angle-right"},null,-1)]),8,cs),t("button",{onClick:e[9]||(e[9]=s=>a.currentPage=a.totalPages),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[17]||(e[17]=[t("i",{class:"fas fa-angle-double-right"},null,-1)]),8,us)])):G("",!0)]),a.insights.length>0?(o(),l("div",vs,[e[18]||(e[18]=t("h3",{class:"insights-title"},[t("i",{class:"fas fa-lightbulb"}),b(" Key Insights ")],-1)),t("div",fs,[(o(!0),l(V,null,I(a.insights,s=>(o(),l("div",{key:s.id,class:x(["insight-item",s.type])},[t("div",gs,[t("i",{class:x(s.icon)},null,2)]),t("div",ps,[t("div",ms,n(s.title),1),t("div",ys,n(s.description),1)])],2))),128))])])):G("",!0)])}const bs=mt(ye,[["render",hs],["__scopeId","data-v-97776e4c"]]),_s={name:"ReportExport",props:{reportType:{type:String,required:!0},data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},emits:["exportStarted","exportCompleted","exportError"],setup(v,{emit:e}){const d=k("excel"),a=k(""),m=k(!1),C=k(0),s=k(""),u=k([]),R=Pt({includeCharts:!0,includeRawData:!0,includeSummary:!0,includeFilters:!0,pdfOrientation:"portrait",pdfPageSize:"a4",excelSeparateSheets:!0,excelIncludeFormulas:!0}),E=[{key:"excel",name:"Excel",description:"Spreadsheet with data and charts",icon:"fas fa-file-excel",size:"~2-5 MB"},{key:"pdf",name:"PDF",description:"Formatted report document",icon:"fas fa-file-pdf",size:"~1-3 MB"},{key:"csv",name:"CSV",description:"Raw data in comma-separated format",icon:"fas fa-file-csv",size:"~100-500 KB"}],U=p(()=>d.value&&v.data&&!m.value),M=p(()=>a.value.trim()?a.value.trim():O.generateFilename(v.reportType,v.dateRange.startDate,v.dateRange.endDate)),q=p(()=>{const j=E.find(Z=>Z.key===d.value);return j?j.size:"Unknown"}),Y=p(()=>{var nt,lt,st;const j=d.value==="pdf"?30:d.value==="excel"?20:10,Z=((st=(lt=(nt=v.data)==null?void 0:nt.table)==null?void 0:lt.data)==null?void 0:st.length)>1e3?2:1,et=R.includeCharts?1.5:1,Q=Math.round(j*Z*et);return Q<60?`${Q} seconds`:`${Math.round(Q/60)} minutes`}),$=j=>O.getFileExtension(j),T=j=>new Date(j).toLocaleDateString("uk-UA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),ot=()=>{console.log("Preview export with options:",{format:d.value,options:R,filename:M.value})},at=async()=>{var j,Z;if(U.value){m.value=!0,C.value=0,s.value="Preparing export...";try{e("exportStarted",d.value);const et=setInterval(()=>{C.value<90&&(C.value+=Math.random()*20,C.value<30?s.value="Collecting data...":C.value<60?s.value="Generating charts...":C.value<90&&(s.value="Formatting document..."))},500),Q={reportType:v.reportType,startDate:(j=v.dateRange.startDate)==null?void 0:j.toISOString(),endDate:(Z=v.dateRange.endDate)==null?void 0:Z.toISOString(),filters:v.filters,options:R,filename:M.value},nt=await O.exportReport(v.reportType,d.value,Q);clearInterval(et),C.value=100,s.value="Download starting...";const lt=M.value;O.downloadFile(nt,lt,d.value);const st={id:Date.now(),filename:`${lt}.${$(d.value)}`,format:d.value,size:z(nt.size),date:new Date,blob:nt};u.value.unshift(st),u.value.length>5&&(u.value=u.value.slice(0,5)),e("exportCompleted",d.value,st.filename)}catch(et){console.error("Export error:",et),e("exportError",d.value,et.message)}finally{m.value=!1,C.value=0,s.value=""}}},J=()=>{m.value=!1,C.value=0,s.value=""},N=j=>{O.downloadFile(j.blob,j.filename.replace(/\.[^/.]+$/,""),j.format)},z=j=>{if(j===0)return"0 Bytes";const Z=1024,et=["Bytes","KB","MB","GB"],Q=Math.floor(Math.log(j)/Math.log(Z));return parseFloat((j/Math.pow(Z,Q)).toFixed(2))+" "+et[Q]};return dt(d,j=>{j==="csv"?R.includeCharts=!1:R.includeCharts=!0}),{selectedFormat:d,customFilename:a,isExporting:m,exportProgress:C,exportStatus:s,exportHistory:u,exportOptions:R,exportFormats:E,canExport:U,finalFilename:M,estimatedFileSize:q,estimatedTime:Y,getFileExtension:$,formatDate:T,previewExport:ot,startExport:at,cancelExport:J,downloadAgain:N}}},Cs={class:"report-export"},ks={class:"export-options"},ws={class:"format-selection"},Ps={class:"format-grid"},xs=["onClick"],Fs={class:"format-icon"},Rs={class:"format-info"},Ds={class:"format-name"},Ss={class:"format-description"},As={class:"format-size"},Vs={class:"export-settings"},$s={class:"settings-grid"},Ts={class:"setting-item"},Ms={class:"setting-label"},Us={class:"setting-item"},Es={class:"setting-label"},Os={class:"setting-item"},Is={class:"setting-label"},Ls={class:"setting-item"},Bs={class:"setting-label"},Ns={key:0,class:"pdf-options"},js={class:"pdf-settings"},qs={class:"setting-row"},zs={class:"setting-row"},Gs={key:1,class:"excel-options"},Hs={class:"excel-settings"},Qs={class:"setting-item"},Ks={class:"setting-label"},Ws={class:"setting-item"},Ys={class:"setting-label"},Zs={class:"file-naming"},Js={class:"filename-input-group"},Xs={class:"filename-extension"},ta={class:"filename-preview"},ea={class:"export-actions"},sa={class:"export-info"},aa={class:"estimated-size"},na={class:"export-time"},oa={class:"export-buttons"},la=["disabled"],ra=["disabled"],ia={key:0,class:"export-progress"},da={class:"progress-header"},ca={class:"progress-text"},ua={class:"progress-percentage"},va={class:"progress-bar"},fa={key:1,class:"export-history"},ga={class:"history-list"},pa={class:"history-info"},ma={class:"history-filename"},ya={class:"history-details"},ha={class:"history-actions"},ba=["onClick"];function _a(v,e,d,a,m,C){return o(),l("div",Cs,[e[44]||(e[44]=t("div",{class:"export-header"},[t("h3",{class:"export-title"},[t("i",{class:"fas fa-download"}),b(" Export Report ")]),t("p",{class:"export-description"}," Download your report in various formats for further analysis or sharing. ")],-1)),t("div",ks,[t("div",ws,[e[12]||(e[12]=t("h4",{class:"section-title"},"Select Format",-1)),t("div",Ps,[(o(!0),l(V,null,I(a.exportFormats,s=>(o(),l("div",{key:s.key,class:x(["format-option",{active:a.selectedFormat===s.key}]),onClick:u=>a.selectedFormat=s.key},[t("div",Fs,[t("i",{class:x(s.icon)},null,2)]),t("div",Rs,[t("div",Ds,n(s.name),1),t("div",Ss,n(s.description),1)]),t("div",As,n(s.size),1)],10,xs))),128))])]),t("div",Vs,[e[35]||(e[35]=t("h4",{class:"section-title"},"Export Options",-1)),t("div",$s,[t("div",Ts,[t("label",Ms,[D(t("input",{type:"checkbox","onUpdate:modelValue":e[0]||(e[0]=s=>a.exportOptions.includeCharts=s),class:"setting-checkbox"},null,512),[[Ct,a.exportOptions.includeCharts]]),e[13]||(e[13]=t("span",{class:"checkmark"},null,-1)),e[14]||(e[14]=b(" Include Charts & Visualizations "))]),e[15]||(e[15]=t("p",{class:"setting-description"}," Add charts and graphs to the exported report ",-1))]),t("div",Us,[t("label",Es,[D(t("input",{type:"checkbox","onUpdate:modelValue":e[1]||(e[1]=s=>a.exportOptions.includeRawData=s),class:"setting-checkbox"},null,512),[[Ct,a.exportOptions.includeRawData]]),e[16]||(e[16]=t("span",{class:"checkmark"},null,-1)),e[17]||(e[17]=b(" Include Raw Data Tables "))]),e[18]||(e[18]=t("p",{class:"setting-description"}," Include detailed data tables in the export ",-1))]),t("div",Os,[t("label",Is,[D(t("input",{type:"checkbox","onUpdate:modelValue":e[2]||(e[2]=s=>a.exportOptions.includeSummary=s),class:"setting-checkbox"},null,512),[[Ct,a.exportOptions.includeSummary]]),e[19]||(e[19]=t("span",{class:"checkmark"},null,-1)),e[20]||(e[20]=b(" Include Executive Summary "))]),e[21]||(e[21]=t("p",{class:"setting-description"}," Add key metrics and insights summary ",-1))]),t("div",Ls,[t("label",Bs,[D(t("input",{type:"checkbox","onUpdate:modelValue":e[3]||(e[3]=s=>a.exportOptions.includeFilters=s),class:"setting-checkbox"},null,512),[[Ct,a.exportOptions.includeFilters]]),e[22]||(e[22]=t("span",{class:"checkmark"},null,-1)),e[23]||(e[23]=b(" Include Filter Information "))]),e[24]||(e[24]=t("p",{class:"setting-description"}," Show applied filters and date ranges ",-1))])]),a.selectedFormat==="pdf"?(o(),l("div",Ns,[e[29]||(e[29]=t("h5",{class:"subsection-title"},"PDF Options",-1)),t("div",js,[t("div",qs,[e[26]||(e[26]=t("label",{class:"setting-label-inline"},"Page Orientation:",-1)),D(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.exportOptions.pdfOrientation=s),class:"setting-select"},e[25]||(e[25]=[t("option",{value:"portrait"},"Portrait",-1),t("option",{value:"landscape"},"Landscape",-1)]),512),[[H,a.exportOptions.pdfOrientation]])]),t("div",zs,[e[28]||(e[28]=t("label",{class:"setting-label-inline"},"Page Size:",-1)),D(t("select",{"onUpdate:modelValue":e[5]||(e[5]=s=>a.exportOptions.pdfPageSize=s),class:"setting-select"},e[27]||(e[27]=[t("option",{value:"a4"},"A4",-1),t("option",{value:"letter"},"Letter",-1),t("option",{value:"legal"},"Legal",-1)]),512),[[H,a.exportOptions.pdfPageSize]])])])])):G("",!0),a.selectedFormat==="excel"?(o(),l("div",Gs,[e[34]||(e[34]=t("h5",{class:"subsection-title"},"Excel Options",-1)),t("div",Hs,[t("div",Qs,[t("label",Ks,[D(t("input",{type:"checkbox","onUpdate:modelValue":e[6]||(e[6]=s=>a.exportOptions.excelSeparateSheets=s),class:"setting-checkbox"},null,512),[[Ct,a.exportOptions.excelSeparateSheets]]),e[30]||(e[30]=t("span",{class:"checkmark"},null,-1)),e[31]||(e[31]=b(" Create Separate Sheets for Each Section "))])]),t("div",Ws,[t("label",Ys,[D(t("input",{type:"checkbox","onUpdate:modelValue":e[7]||(e[7]=s=>a.exportOptions.excelIncludeFormulas=s),class:"setting-checkbox"},null,512),[[Ct,a.exportOptions.excelIncludeFormulas]]),e[32]||(e[32]=t("span",{class:"checkmark"},null,-1)),e[33]||(e[33]=b(" Include Formulas and Calculations "))])])])])):G("",!0)]),t("div",Zs,[e[37]||(e[37]=t("h4",{class:"section-title"},"File Name",-1)),t("div",Js,[D(t("input",{type:"text","onUpdate:modelValue":e[8]||(e[8]=s=>a.customFilename=s),placeholder:"Enter custom filename (optional)",class:"filename-input"},null,512),[[ut,a.customFilename]]),t("span",Xs,"."+n(a.getFileExtension(a.selectedFormat)),1)]),t("p",ta,[e[36]||(e[36]=b(" Preview: ")),t("strong",null,n(a.finalFilename),1)])])]),t("div",ea,[t("div",sa,[t("div",aa,[e[38]||(e[38]=t("i",{class:"fas fa-info-circle"},null,-1)),b(" Estimated file size: "+n(a.estimatedFileSize),1)]),t("div",na,[e[39]||(e[39]=t("i",{class:"fas fa-clock"},null,-1)),b(" Estimated time: "+n(a.estimatedTime),1)])]),t("div",oa,[t("button",{onClick:e[9]||(e[9]=(...s)=>a.previewExport&&a.previewExport(...s)),class:"preview-btn",disabled:a.isExporting},e[40]||(e[40]=[t("i",{class:"fas fa-eye"},null,-1),b(" Preview ")]),8,la),t("button",{onClick:e[10]||(e[10]=(...s)=>a.startExport&&a.startExport(...s)),class:"export-btn",disabled:!a.canExport||a.isExporting},[t("i",{class:x(["fas fa-download",{"fa-spin":a.isExporting}])},null,2),b(" "+n(a.isExporting?"Exporting...":"Export Report"),1)],8,ra)])]),a.isExporting?(o(),l("div",ia,[t("div",da,[t("span",ca,n(a.exportStatus),1),t("span",ua,n(a.exportProgress)+"%",1)]),t("div",va,[t("div",{class:"progress-fill",style:bt({width:a.exportProgress+"%"})},null,4)]),t("button",{onClick:e[11]||(e[11]=(...s)=>a.cancelExport&&a.cancelExport(...s)),class:"cancel-btn"},e[41]||(e[41]=[t("i",{class:"fas fa-times"},null,-1),b(" Cancel ")]))])):G("",!0),a.exportHistory.length>0?(o(),l("div",fa,[e[43]||(e[43]=t("h4",{class:"section-title"},"Recent Exports",-1)),t("div",ga,[(o(!0),l(V,null,I(a.exportHistory,s=>(o(),l("div",{key:s.id,class:"history-item"},[t("div",pa,[t("div",ma,n(s.filename),1),t("div",ya,n(s.format.toUpperCase())+" • "+n(s.size)+" • "+n(a.formatDate(s.date)),1)]),t("div",ha,[t("button",{onClick:u=>a.downloadAgain(s),class:"download-again-btn"},e[42]||(e[42]=[t("i",{class:"fas fa-download"},null,-1),b(" Download Again ")]),8,ba)])]))),128))])])):G("",!0)])}const Ca=mt(_s,[["render",_a],["__scopeId","data-v-8616e2f3"]]),ka={name:"FinancialReport",props:{data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},setup(v){const e=k(null),d=k("daily"),a=k(""),m=k(""),C=k(""),s=k(1),u=k(10),R=k("date"),E=k("desc");let U=null;const M=p(()=>{var w,f;return((f=(w=v.data)==null?void 0:w.metrics)==null?void 0:f.items)||[]}),q=p(()=>{var w;return((w=v.data)==null?void 0:w.insights)||[]}),Y=p(()=>{const w=M.value.find(f=>f.key==="totalRevenue");return w?w.value:0}),$=p(()=>{const w=M.value.find(f=>f.key==="totalCommission");return w?w.value:Y.value*.15}),T=p(()=>$.value*.9),ot=p(()=>$.value*.1),at=p(()=>Y.value-Y.value*.05),J=p(()=>at.value-$.value),N=p(()=>{var w,f;return((f=(w=v.data)==null?void 0:w.table)==null?void 0:f.data)||[]}),z=p(()=>{let w=N.value;if(a.value){const f=a.value.toLowerCase();w=w.filter(S=>{var L,A;return((L=S.orderNumber)==null?void 0:L.toLowerCase().includes(f))||((A=S.paymentMethod)==null?void 0:A.toLowerCase().includes(f))})}return m.value&&(w=w.filter(f=>f.status===m.value)),C.value&&(w=w.filter(f=>f.paymentMethod===C.value)),w.sort((f,S)=>{const L=f[R.value],A=S[R.value];if(typeof L=="number"&&typeof A=="number")return E.value==="asc"?L-A:A-L;const B=String(L).toLowerCase(),tt=String(A).toLowerCase();return E.value==="asc"?B.localeCompare(tt):tt.localeCompare(B)}),w}),j=p(()=>Math.ceil(z.value.length/u.value)),Z=p(()=>{const w=(s.value-1)*u.value,f=w+u.value;return z.value.slice(w,f)}),et=(w,f)=>{switch(f){case"currency":return O.formatCurrency(w);case"percentage":return O.formatPercentage(w);case"number":return O.formatNumber(w);default:return String(w)}},Q=w=>O.formatCurrency(w),nt=w=>new Date(w).toLocaleDateString("uk-UA",{year:"numeric",month:"short",day:"numeric"}),lt=w=>w>0?"fas fa-arrow-up":w<0?"fas fa-arrow-down":"fas fa-minus",st=w=>{R.value===w?E.value=E.value==="asc"?"desc":"asc":(R.value=w,E.value="asc"),s.value=1},vt=w=>{console.log("View transaction:",w)},rt=w=>{console.log("Download receipt for:",w)},ft=()=>{it()},it=async()=>{var w,f;if(await X(),U&&U.destroy(),!(!e.value||!((f=(w=v.data)==null?void 0:w.charts)!=null&&f.primary)))try{const{Chart:S}=await gt(async()=>{const{Chart:A}=await import("./auto-_svZoXzF.js");return{Chart:A}},__vite__mapDeps([0,1])),L=e.value.getContext("2d");U=new S(L,{type:"line",data:v.data.charts.primary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1,callbacks:{label:function(A){return A.dataset.label+": "+Q(A.parsed.y)}}}},scales:{x:{display:!0,title:{display:!0,text:"Date"}},y:{display:!0,title:{display:!0,text:"Amount (UAH)"},ticks:{callback:function(A){return Q(A)}}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}})}catch(S){console.error("Error creating revenue chart:",S)}};return dt(()=>v.data,()=>{X(()=>{it()})},{deep:!0}),dt([a,m,C],()=>{s.value=1}),ht(()=>{X(()=>{it()})}),wt(()=>{U&&U.destroy()}),{revenueChartCanvas:e,chartPeriod:d,transactionSearch:a,statusFilter:m,paymentMethodFilter:C,currentPage:s,itemsPerPage:u,financialMetrics:M,financialInsights:q,totalRevenue:Y,totalCommission:$,salesCommission:T,processingFees:ot,netRevenue:at,sellerEarnings:J,filteredTransactions:z,totalPages:j,paginatedTransactions:Z,formatMetricValue:et,formatCurrency:Q,formatDate:nt,getChangeIcon:lt,sortBy:st,viewTransaction:vt,downloadReceipt:rt,updateChart:ft}}},wa={class:"financial-report"},Pa={class:"metrics-section"},xa={class:"metrics-grid"},Fa={class:"metric-header"},Ra={class:"metric-icon"},Da={key:0,class:"metric-change"},Sa={class:"metric-content"},Aa={class:"metric-label"},Va={class:"metric-value"},$a={key:0,class:"metric-comparison"},Ta={class:"chart-section"},Ma={class:"chart-container"},Ua={class:"chart-header"},Ea={class:"chart-controls"},Oa={class:"chart-content"},Ia={ref:"revenueChartCanvas",id:"revenue-chart"},La={class:"insights-section"},Ba={class:"insights-grid"},Na={class:"insight-icon"},ja={class:"insight-content"},qa={class:"insight-title"},za={class:"insight-description"},Ga={class:"insight-priority"},Ha={class:"commission-section"},Qa={class:"commission-grid"},Ka={class:"commission-card"},Wa={class:"commission-content"},Ya={class:"commission-amount"},Za={class:"commission-details"},Ja={class:"detail-item"},Xa={class:"detail-value"},tn={class:"detail-item"},en={class:"detail-value"},sn={class:"commission-card"},an={class:"commission-content"},nn={class:"commission-amount"},on={class:"commission-details"},ln={class:"detail-item"},rn={class:"detail-value"},dn={class:"detail-item"},cn={class:"detail-value"},un={class:"transactions-section"},vn={class:"transactions-table"},fn={class:"table-controls"},gn={class:"search-box"},pn={class:"filter-controls"},mn={class:"table-container"},yn={class:"transactions-table-element"},hn={class:"order-number"},bn={class:"amount"},_n={class:"commission"},Cn={class:"payment-method"},kn={class:"action-buttons"},wn=["onClick"],Pn=["onClick"],xn={key:0,class:"pagination"},Fn=["disabled"],Rn=["disabled"],Dn={class:"pagination-info"},Sn=["disabled"],An=["disabled"];function Vn(v,e,d,a,m,C){return o(),l("div",wa,[t("div",Pa,[e[13]||(e[13]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-chart-line"}),b(" Financial Performance ")],-1)),t("div",xa,[(o(!0),l(V,null,I(a.financialMetrics,s=>(o(),l("div",{key:s.key,class:x(["metric-card",s.trend])},[t("div",Fa,[t("div",Ra,[t("i",{class:x(s.icon)},null,2)]),s.changePercentage?(o(),l("div",Da,[t("i",{class:x(a.getChangeIcon(s.changePercentage))},null,2),t("span",null,n(Math.abs(s.changePercentage).toFixed(1))+"%",1)])):G("",!0)]),t("div",Sa,[t("div",Aa,n(s.label),1),t("div",Va,n(a.formatMetricValue(s.value,s.type)),1),s.previousValue?(o(),l("div",$a," vs "+n(a.formatMetricValue(s.previousValue,s.type))+" last period ",1)):G("",!0)])],2))),128))])]),t("div",Ta,[t("div",Ma,[t("div",Ua,[e[15]||(e[15]=t("h3",{class:"chart-title"},"Revenue Trend",-1)),t("div",Ea,[D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>a.chartPeriod=s),onChange:e[1]||(e[1]=(...s)=>a.updateChart&&a.updateChart(...s)),class:"chart-select"},e[14]||(e[14]=[t("option",{value:"daily"},"Daily",-1),t("option",{value:"weekly"},"Weekly",-1),t("option",{value:"monthly"},"Monthly",-1)]),544),[[H,a.chartPeriod]])])]),t("div",Oa,[t("canvas",Ia,null,512)])])]),e[41]||(e[41]=t("div",{class:"section-divider"},null,-1)),t("div",La,[e[16]||(e[16]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-lightbulb"}),b(" Financial Insights ")],-1)),t("div",Ba,[(o(!0),l(V,null,I(a.financialInsights,s=>(o(),l("div",{key:s.id,class:x(["insight-card",s.type])},[t("div",Na,[t("i",{class:x(s.icon)},null,2)]),t("div",ja,[t("div",qa,n(s.title),1),t("div",za,n(s.description),1)]),t("div",Ga,[t("div",{class:x(["priority-indicator",`priority-${s.priority}`])},n(s.priority),3)])],2))),128))])]),t("div",Ha,[e[23]||(e[23]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-percentage"}),b(" Commission Breakdown ")],-1)),t("div",Qa,[t("div",Ka,[e[19]||(e[19]=t("div",{class:"commission-header"},[t("h4",null,"Platform Commission (15%)")],-1)),t("div",Wa,[t("div",Ya,n(a.formatCurrency(a.totalCommission)),1),t("div",Za,[t("div",Ja,[e[17]||(e[17]=t("span",{class:"detail-label"},"From Sales:",-1)),t("span",Xa,n(a.formatCurrency(a.salesCommission)),1)]),t("div",tn,[e[18]||(e[18]=t("span",{class:"detail-label"},"Processing Fees:",-1)),t("span",en,n(a.formatCurrency(a.processingFees)),1)])])])]),t("div",sn,[e[22]||(e[22]=t("div",{class:"commission-header"},[t("h4",null,"Seller Earnings")],-1)),t("div",an,[t("div",nn,n(a.formatCurrency(a.sellerEarnings)),1),t("div",on,[t("div",ln,[e[20]||(e[20]=t("span",{class:"detail-label"},"Net Revenue:",-1)),t("span",rn,n(a.formatCurrency(a.netRevenue)),1)]),t("div",dn,[e[21]||(e[21]=t("span",{class:"detail-label"},"After Commission:",-1)),t("span",cn,n(a.formatCurrency(a.sellerEarnings)),1)])])])])])]),t("div",un,[e[40]||(e[40]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-list"}),b(" Recent Transactions ")],-1)),t("div",vn,[t("div",fn,[t("div",gn,[e[24]||(e[24]=t("i",{class:"fas fa-search"},null,-1)),D(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>a.transactionSearch=s),type:"text",placeholder:"Search transactions...",class:"search-input"},null,512),[[ut,a.transactionSearch]])]),t("div",pn,[D(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.statusFilter=s),class:"filter-select"},e[25]||(e[25]=[t("option",{value:""},"All Statuses",-1),t("option",{value:"completed"},"Completed",-1),t("option",{value:"pending"},"Pending",-1),t("option",{value:"refunded"},"Refunded",-1)]),512),[[H,a.statusFilter]]),D(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.paymentMethodFilter=s),class:"filter-select"},e[26]||(e[26]=[t("option",{value:""},"All Payment Methods",-1),t("option",{value:"card"},"Credit Card",-1),t("option",{value:"paypal"},"PayPal",-1),t("option",{value:"bank"},"Bank Transfer",-1)]),512),[[H,a.paymentMethodFilter]])])]),t("div",mn,[t("table",yn,[t("thead",null,[t("tr",null,[t("th",{onClick:e[5]||(e[5]=s=>a.sortBy("date")),class:"sortable"},e[27]||(e[27]=[b(" Date "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[6]||(e[6]=s=>a.sortBy("orderNumber")),class:"sortable"},e[28]||(e[28]=[b(" Order # "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[7]||(e[7]=s=>a.sortBy("amount")),class:"sortable"},e[29]||(e[29]=[b(" Amount "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[8]||(e[8]=s=>a.sortBy("commission")),class:"sortable"},e[30]||(e[30]=[b(" Commission "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),e[31]||(e[31]=t("th",null,"Payment Method",-1)),e[32]||(e[32]=t("th",null,"Status",-1)),e[33]||(e[33]=t("th",null,"Actions",-1))])]),t("tbody",null,[(o(!0),l(V,null,I(a.paginatedTransactions,s=>(o(),l("tr",{key:s.id,class:"transaction-row"},[t("td",null,n(a.formatDate(s.date)),1),t("td",null,[t("span",hn,n(s.orderNumber),1)]),t("td",null,[t("span",bn,n(a.formatCurrency(s.amount)),1)]),t("td",null,[t("span",_n,n(a.formatCurrency(s.commission)),1)]),t("td",null,[t("span",Cn,n(s.paymentMethod),1)]),t("td",null,[t("span",{class:x(["status",`status-${s.status}`])},n(s.status),3)]),t("td",null,[t("div",kn,[t("button",{onClick:u=>a.viewTransaction(s),class:"action-btn view-btn"},e[34]||(e[34]=[t("i",{class:"fas fa-eye"},null,-1)]),8,wn),t("button",{onClick:u=>a.downloadReceipt(s),class:"action-btn download-btn"},e[35]||(e[35]=[t("i",{class:"fas fa-download"},null,-1)]),8,Pn)])])]))),128))])])]),a.totalPages>1?(o(),l("div",xn,[t("button",{onClick:e[9]||(e[9]=s=>a.currentPage=1),disabled:a.currentPage===1,class:"pagination-btn"},e[36]||(e[36]=[t("i",{class:"fas fa-angle-double-left"},null,-1)]),8,Fn),t("button",{onClick:e[10]||(e[10]=s=>a.currentPage--),disabled:a.currentPage===1,class:"pagination-btn"},e[37]||(e[37]=[t("i",{class:"fas fa-angle-left"},null,-1)]),8,Rn),t("span",Dn," Page "+n(a.currentPage)+" of "+n(a.totalPages),1),t("button",{onClick:e[11]||(e[11]=s=>a.currentPage++),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[38]||(e[38]=[t("i",{class:"fas fa-angle-right"},null,-1)]),8,Sn),t("button",{onClick:e[12]||(e[12]=s=>a.currentPage=a.totalPages),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[39]||(e[39]=[t("i",{class:"fas fa-angle-double-right"},null,-1)]),8,An)])):G("",!0)])])])}const $n=mt(ka,[["render",Vn],["__scopeId","data-v-828c053a"]]),Tn={name:"SalesReport",emits:["period-changed"],props:{data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},setup(v,{emit:e}){const d=k(null),a=k(null),m=k("daily"),C=k(""),s=k(""),u=k(1),R=k(10),E=k("sales"),U=k("desc");let M=null,q=null;const Y=p(()=>{var f,S;return((S=(f=v.data)==null?void 0:f.metrics)==null?void 0:S.items)||[]}),$=p(()=>{var f;return((f=v.data)==null?void 0:f.insights)||[]}),T=p(()=>{var S,L;return(((L=(S=v.data)==null?void 0:S.table)==null?void 0:L.data)||[]).sort((A,B)=>(B.sales||0)-(A.sales||0)).slice(0,5).map(A=>({id:A.id,name:A.name,category:A.category,sales:A.sales||0,revenue:A.revenue||0}))}),ot=p(()=>{var B,tt,_,F;const f=(tt=(B=v.data)==null?void 0:B.charts)==null?void 0:tt.secondary;if(!((_=f==null?void 0:f.data)!=null&&_.labels))return[];const S=f.data.labels,L=((F=f.data.datasets[0])==null?void 0:F.data)||[],A=L.reduce((c,r)=>c+r,0);return S.map((c,r)=>({category:c,sales:L[r]||0,percentage:A>0?L[r]/A*100:0}))}),at=p(()=>{var L,A;const f=((A=(L=v.data)==null?void 0:L.table)==null?void 0:A.data)||[];return[...new Set(f.map(B=>B.category).filter(Boolean))].sort()}),J=p(()=>{var f,S;return((S=(f=v.data)==null?void 0:f.table)==null?void 0:S.data)||[]}),N=p(()=>{let f=J.value;if(C.value){const S=C.value.toLowerCase();f=f.filter(L=>{var A,B,tt;return((A=L.name)==null?void 0:A.toLowerCase().includes(S))||((B=L.sku)==null?void 0:B.toLowerCase().includes(S))||((tt=L.category)==null?void 0:tt.toLowerCase().includes(S))})}return s.value&&(f=f.filter(S=>S.category===s.value)),f.sort((S,L)=>{const A=S[E.value],B=L[E.value];if(typeof A=="number"&&typeof B=="number")return U.value==="asc"?A-B:B-A;const tt=String(A).toLowerCase(),_=String(B).toLowerCase();return U.value==="asc"?tt.localeCompare(_):_.localeCompare(tt)}),f}),z=p(()=>Math.ceil(N.value.length/R.value)),j=p(()=>{const f=(u.value-1)*R.value,S=f+R.value;return N.value.slice(f,S)}),Z=(f,S)=>{switch(S){case"currency":return O.formatCurrency(f);case"percentage":return O.formatPercentage(f);case"number":return O.formatNumber(f);default:return String(f)}},et=f=>O.formatCurrency(f),Q=f=>O.formatNumber(f),nt=f=>f>0?"fas fa-arrow-up":f<0?"fas fa-arrow-down":"fas fa-minus",lt=f=>{const S=["#667eea","#764ba2","#f093fb","#f5576c","#4facfe","#43e97b","#38f9d7"];return S[f%S.length]},st=f=>f>0?"positive":f<0?"negative":"neutral",vt=f=>f>0?"fas fa-arrow-up":f<0?"fas fa-arrow-down":"fas fa-minus",rt=f=>{E.value===f?U.value=U.value==="asc"?"desc":"asc":(E.value=f,U.value="asc"),u.value=1},ft=()=>{e("period-changed",m.value),it()},it=async()=>{var f,S;if(await X(),M&&M.destroy(),!(!d.value||!((S=(f=v.data)==null?void 0:f.charts)!=null&&S.primary)))try{const{Chart:L}=await gt(async()=>{const{Chart:B}=await import("./auto-_svZoXzF.js");return{Chart:B}},__vite__mapDeps([0,1])),A=d.value.getContext("2d");M=new L(A,{type:"line",data:v.data.charts.primary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1,callbacks:{label:function(B){return B.dataset.label+": "+Q(B.parsed.y)}}}},scales:{x:{display:!0,title:{display:!0,text:"Date"}},y:{display:!0,title:{display:!0,text:"Sales Count"},ticks:{callback:function(B){return Q(B)}}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}})}catch(L){console.error("Error creating sales chart:",L)}},w=async()=>{var f,S;if(await X(),q&&q.destroy(),!(!a.value||!((S=(f=v.data)==null?void 0:f.charts)!=null&&S.secondary)))try{const{Chart:L}=await gt(async()=>{const{Chart:B}=await import("./auto-_svZoXzF.js");return{Chart:B}},__vite__mapDeps([0,1])),A=a.value.getContext("2d");q=new L(A,{type:"doughnut",data:v.data.charts.secondary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{callbacks:{label:function(B){const tt=B.dataset.data.reduce((F,c)=>F+c,0),_=(B.parsed/tt*100).toFixed(1);return B.label+": "+Q(B.parsed)+" sales ("+_+"%)"}}}}}})}catch(L){console.error("Error creating category chart:",L)}};return dt(()=>v.data,()=>{X(()=>{it(),w()})},{deep:!0}),dt([C,s],()=>{u.value=1}),ht(()=>{X(()=>{it(),w()})}),wt(()=>{M&&M.destroy(),q&&q.destroy()}),{salesChartCanvas:d,categoryChartCanvas:a,chartPeriod:m,performanceSearch:C,categoryFilter:s,currentPage:u,itemsPerPage:R,salesMetrics:Y,salesInsights:$,topProducts:T,categoryData:ot,categories:at,filteredPerformance:N,totalPages:z,paginatedPerformance:j,formatMetricValue:Z,formatCurrency:et,formatNumber:Q,getChangeIcon:nt,getCategoryColor:lt,getGrowthClass:st,getGrowthIcon:vt,sortBy:rt,updateChart:ft}}},Mn={class:"sales-report"},Un={class:"metrics-section"},En={class:"metrics-grid"},On={class:"metric-header"},In={class:"metric-icon"},Ln={key:0,class:"metric-change"},Bn={class:"metric-content"},Nn={class:"metric-label"},jn={class:"metric-value"},qn={key:0,class:"metric-comparison"},zn={class:"chart-section"},Gn={class:"chart-container"},Hn={class:"chart-header"},Qn={class:"chart-controls"},Kn={class:"chart-content"},Wn={ref:"salesChartCanvas",id:"sales-chart"},Yn={class:"top-products-section"},Zn={class:"products-grid"},Jn={class:"product-rank"},Xn={class:"rank-number"},to={key:0,class:"fas fa-crown"},eo={key:1,class:"fas fa-medal"},so={key:2,class:"fas fa-award"},ao={key:3,class:"fas fa-star"},no={class:"product-info"},oo={class:"product-name"},lo={class:"product-category"},ro={class:"product-stats"},io={class:"stat-item"},co={class:"stat-value"},uo={class:"stat-item"},vo={class:"stat-value"},fo={class:"category-section"},go={class:"category-container"},po={class:"category-content"},mo={class:"chart-wrapper"},yo={ref:"categoryChartCanvas",id:"category-chart"},ho={class:"category-legend"},bo={class:"legend-info"},_o={class:"legend-label"},Co={class:"legend-value"},ko={class:"insights-section"},wo={class:"insights-grid"},Po={class:"insight-icon"},xo={class:"insight-content"},Fo={class:"insight-title"},Ro={class:"insight-description"},Do={class:"insight-priority"},So={class:"performance-section"},Ao={class:"performance-table"},Vo={class:"table-controls"},$o={class:"search-box"},To={class:"filter-controls"},Mo=["value"],Uo={class:"table-container"},Eo={class:"performance-table-element"},Oo={class:"product-cell"},Io={class:"product-name"},Lo={class:"product-sku"},Bo={class:"category-badge"},No={class:"sales-number"},jo={class:"revenue-amount"},qo={class:"price-amount"},zo={key:0,class:"pagination"},Go=["disabled"],Ho=["disabled"],Qo={class:"pagination-info"},Ko=["disabled"],Wo=["disabled"];function Yo(v,e,d,a,m,C){return o(),l("div",Mn,[t("div",Un,[e[14]||(e[14]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-chart-line"}),b(" Sales Performance ")],-1)),t("div",En,[(o(!0),l(V,null,I(a.salesMetrics,s=>(o(),l("div",{key:s.key,class:x(["metric-card",s.trend])},[t("div",On,[t("div",In,[t("i",{class:x(s.icon)},null,2)]),s.changePercentage?(o(),l("div",Ln,[t("i",{class:x(a.getChangeIcon(s.changePercentage))},null,2),t("span",null,n(Math.abs(s.changePercentage).toFixed(1))+"%",1)])):G("",!0)]),t("div",Bn,[t("div",Nn,n(s.label),1),t("div",jn,n(a.formatMetricValue(s.value,s.type)),1),s.previousValue?(o(),l("div",qn," vs "+n(a.formatMetricValue(s.previousValue,s.type))+" last period ",1)):G("",!0)])],2))),128))])]),t("div",zn,[t("div",Gn,[t("div",Hn,[e[16]||(e[16]=t("h3",{class:"chart-title"},"Sales Trend",-1)),t("div",Qn,[D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>a.chartPeriod=s),onChange:e[1]||(e[1]=(...s)=>a.updateChart&&a.updateChart(...s)),class:"chart-select"},e[15]||(e[15]=[t("option",{value:"daily"},"Daily",-1),t("option",{value:"weekly"},"Weekly",-1),t("option",{value:"monthly"},"Monthly",-1)]),544),[[H,a.chartPeriod]])])]),t("div",Kn,[t("canvas",Wn,null,512)])])]),t("div",Yn,[e[19]||(e[19]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-trophy"}),b(" Top Selling Products ")],-1)),t("div",Zn,[(o(!0),l(V,null,I(a.topProducts,(s,u)=>(o(),l("div",{key:s.id,class:"product-card"},[t("div",Jn,[t("span",Xn,"#"+n(u+1),1),t("div",{class:x(["rank-badge",`rank-${u+1}`])},[u===0?(o(),l("i",to)):u===1?(o(),l("i",eo)):u===2?(o(),l("i",so)):(o(),l("i",ao))],2)]),t("div",no,[t("div",oo,n(s.name),1),t("div",lo,n(s.category),1)]),t("div",ro,[t("div",io,[e[17]||(e[17]=t("span",{class:"stat-label"},"Sales",-1)),t("span",co,n(a.formatNumber(s.sales)),1)]),t("div",uo,[e[18]||(e[18]=t("span",{class:"stat-label"},"Revenue",-1)),t("span",vo,n(a.formatCurrency(s.revenue)),1)])])]))),128))])]),t("div",fo,[t("div",go,[e[20]||(e[20]=t("div",{class:"category-header"},[t("h3",{class:"category-title"},"Sales by Category")],-1)),t("div",po,[t("div",mo,[t("canvas",yo,null,512)]),t("div",ho,[(o(!0),l(V,null,I(a.categoryData,(s,u)=>(o(),l("div",{key:s.category,class:"legend-item"},[t("div",{class:"legend-color",style:bt({backgroundColor:a.getCategoryColor(u)})},null,4),t("div",bo,[t("div",_o,n(s.category),1),t("div",Co,n(a.formatNumber(s.sales))+" sales ("+n(s.percentage.toFixed(1))+"%) ",1)])]))),128))])])])]),t("div",ko,[e[21]||(e[21]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-lightbulb"}),b(" Sales Insights ")],-1)),t("div",wo,[(o(!0),l(V,null,I(a.salesInsights,s=>(o(),l("div",{key:s.id,class:x(["insight-card",s.type])},[t("div",Po,[t("i",{class:x(s.icon)},null,2)]),t("div",xo,[t("div",Fo,n(s.title),1),t("div",Ro,n(s.description),1)]),t("div",Do,[t("div",{class:x(["priority-indicator",`priority-${s.priority}`])},n(s.priority),3)])],2))),128))])]),t("div",So,[e[34]||(e[34]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-table"}),b(" Sales Performance Details ")],-1)),t("div",Ao,[t("div",Vo,[t("div",$o,[e[22]||(e[22]=t("i",{class:"fas fa-search"},null,-1)),D(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>a.performanceSearch=s),type:"text",placeholder:"Search products...",class:"search-input"},null,512),[[ut,a.performanceSearch]])]),t("div",To,[D(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.categoryFilter=s),class:"filter-select"},[e[23]||(e[23]=t("option",{value:""},"All Categories",-1)),(o(!0),l(V,null,I(a.categories,s=>(o(),l("option",{key:s,value:s},n(s),9,Mo))),128))],512),[[H,a.categoryFilter]])])]),t("div",Uo,[t("table",Eo,[t("thead",null,[t("tr",null,[t("th",{onClick:e[4]||(e[4]=s=>a.sortBy("name")),class:"sortable"},e[24]||(e[24]=[b(" Product Name "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[5]||(e[5]=s=>a.sortBy("category")),class:"sortable"},e[25]||(e[25]=[b(" Category "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[6]||(e[6]=s=>a.sortBy("sales")),class:"sortable"},e[26]||(e[26]=[b(" Sales "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[7]||(e[7]=s=>a.sortBy("revenue")),class:"sortable"},e[27]||(e[27]=[b(" Revenue "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[8]||(e[8]=s=>a.sortBy("avgPrice")),class:"sortable"},e[28]||(e[28]=[b(" Avg Price "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[9]||(e[9]=s=>a.sortBy("growth")),class:"sortable"},e[29]||(e[29]=[b(" Growth "),t("i",{class:"fas fa-sort sort-icon"},null,-1)]))])]),t("tbody",null,[(o(!0),l(V,null,I(a.paginatedPerformance,s=>(o(),l("tr",{key:s.id,class:"performance-row"},[t("td",null,[t("div",Oo,[t("div",Io,n(s.name),1),t("div",Lo,"SKU: "+n(s.sku),1)])]),t("td",null,[t("span",Bo,n(s.category),1)]),t("td",null,[t("span",No,n(a.formatNumber(s.sales)),1)]),t("td",null,[t("span",jo,n(a.formatCurrency(s.revenue)),1)]),t("td",null,[t("span",qo,n(a.formatCurrency(s.avgPrice)),1)]),t("td",null,[t("span",{class:x(["growth-indicator",a.getGrowthClass(s.growth)])},[t("i",{class:x(a.getGrowthIcon(s.growth))},null,2),b(" "+n(Math.abs(s.growth).toFixed(1))+"% ",1)],2)])]))),128))])])]),a.totalPages>1?(o(),l("div",zo,[t("button",{onClick:e[10]||(e[10]=s=>a.currentPage=1),disabled:a.currentPage===1,class:"pagination-btn"},e[30]||(e[30]=[t("i",{class:"fas fa-angle-double-left"},null,-1)]),8,Go),t("button",{onClick:e[11]||(e[11]=s=>a.currentPage--),disabled:a.currentPage===1,class:"pagination-btn"},e[31]||(e[31]=[t("i",{class:"fas fa-angle-left"},null,-1)]),8,Ho),t("span",Qo," Page "+n(a.currentPage)+" of "+n(a.totalPages),1),t("button",{onClick:e[12]||(e[12]=s=>a.currentPage++),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[32]||(e[32]=[t("i",{class:"fas fa-angle-right"},null,-1)]),8,Ko),t("button",{onClick:e[13]||(e[13]=s=>a.currentPage=a.totalPages),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[33]||(e[33]=[t("i",{class:"fas fa-angle-double-right"},null,-1)]),8,Wo)])):G("",!0)])])])}const Zo=mt(Tn,[["render",Yo],["__scopeId","data-v-225c0703"]]),Jo={name:"ProductsReport",emits:["period-changed"],props:{data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},setup(v,{emit:e}){const d=k(null),a=k(null),m=k("daily"),C=k("sales"),s=k(""),u=k(""),R=k(""),E=k(1),U=k(10),M=k("sales"),q=k("desc");let Y=null,$=null;const T=[{key:"sales",label:"Sales",icon:"fas fa-shopping-cart"},{key:"views",label:"Views",icon:"fas fa-eye"},{key:"revenue",label:"Revenue",icon:"fas fa-dollar-sign"},{key:"rating",label:"Rating",icon:"fas fa-star"}],ot=p(()=>{var c,r;return((r=(c=v.data)==null?void 0:c.metrics)==null?void 0:r.items)||[]}),at=p(()=>{var c;return((c=v.data)==null?void 0:c.insights)||[]}),J=p(()=>{var c,r;return((r=(c=v.data)==null?void 0:c.table)==null?void 0:r.data)||[]}),N=p(()=>[...new Set(J.value.map(r=>r.category).filter(Boolean))].sort()),z=p(()=>J.value.sort((c,r)=>(r[C.value]||0)-(c[C.value]||0)).slice(0,6)),j=p(()=>{const c=J.value.reduce((i,h)=>i+(h.views||0),0),r=Math.floor(c*.15),g=J.value.reduce((i,h)=>i+(h.sales||0),0);return[{name:"Product Views",value:c,percentage:100},{name:"Add to Cart",value:r,percentage:c>0?r/c*100:0},{name:"Purchases",value:g,percentage:c>0?g/c*100:0}]}),Z=p(()=>{const c=[{range:"< ₴500",min:0,max:500},{range:"₴500 - ₴1,000",min:500,max:1e3},{range:"₴1,000 - ₴2,500",min:1e3,max:2500},{range:"₴2,500 - ₴5,000",min:2500,max:5e3},{range:"> ₴5,000",min:5e3,max:1/0}],r=J.value.length;return c.map(g=>{const y=J.value.filter(i=>{const h=i.price||0;return h>=g.min&&h<g.max}).length;return{...g,count:y,percentage:r>0?y/r*100:0}})}),et=p(()=>{const c=[{status:"In Stock",icon:"fas fa-check-circle",filter:g=>g>10},{status:"Low Stock",icon:"fas fa-exclamation-triangle",filter:g=>g>0&&g<=10},{status:"Out of Stock",icon:"fas fa-times-circle",filter:g=>g===0}],r=J.value.length;return c.map(g=>{const y=J.value.filter(i=>g.filter(i.stock||0)).length;return{...g,count:y,percentage:r>0?y/r*100:0}})}),Q=p(()=>{let c=J.value;if(s.value){const r=s.value.toLowerCase();c=c.filter(g=>{var y,i,h;return((y=g.name)==null?void 0:y.toLowerCase().includes(r))||((i=g.sku)==null?void 0:i.toLowerCase().includes(r))||((h=g.category)==null?void 0:h.toLowerCase().includes(r))})}return u.value&&(c=c.filter(r=>r.category===u.value)),R.value&&(c=c.filter(r=>{switch(R.value){case"active":return r.status==="active";case"inactive":return r.status==="inactive";case"out_of_stock":return(r.stock||0)===0;default:return!0}})),c.sort((r,g)=>{const y=r[M.value],i=g[M.value];if(typeof y=="number"&&typeof i=="number")return q.value==="asc"?y-i:i-y;const h=String(y).toLowerCase(),P=String(i).toLowerCase();return q.value==="asc"?h.localeCompare(P):P.localeCompare(h)}),c}),nt=p(()=>Math.ceil(Q.value.length/U.value)),lt=p(()=>{const c=(E.value-1)*U.value,r=c+U.value;return Q.value.slice(c,r)}),st=(c,r)=>{switch(r){case"currency":return O.formatCurrency(c);case"percentage":return O.formatPercentage(c);case"number":return O.formatNumber(c);default:return String(c)}},vt=c=>O.formatCurrency(c),rt=c=>O.formatNumber(c),ft=c=>c>0?"fas fa-arrow-up":c<0?"fas fa-arrow-down":"fas fa-minus",it=c=>({sales:"Sales",views:"Views",revenue:"Revenue",rating:"Rating"})[c]||c,w=(c,r)=>{switch(r){case"revenue":return vt(c);case"rating":return(c==null?void 0:c.toFixed(1))||"N/A";default:return rt(c)}},f=c=>c===0?"out-of-stock":c<=10?"low-stock":"in-stock",S=c=>{M.value===c?q.value=q.value==="asc"?"desc":"asc":(M.value=c,q.value="asc"),E.value=1},L=()=>{e("period-changed",m.value),_()},A=c=>{console.log("View product:",c)},B=c=>{console.log("Edit product:",c)},tt=c=>{console.log("Handle insight action:",c)},_=async()=>{var c,r;if(await X(),Y&&Y.destroy(),!(!d.value||!((r=(c=v.data)==null?void 0:c.charts)!=null&&r.primary)))try{const{Chart:g}=await gt(async()=>{const{Chart:i}=await import("./auto-_svZoXzF.js");return{Chart:i}},__vite__mapDeps([0,1])),y=d.value.getContext("2d");Y=new g(y,{type:"line",data:v.data.charts.primary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1,callbacks:{label:function(i){return i.dataset.label+": "+rt(i.parsed.y)}}}},scales:{x:{display:!0,title:{display:!0,text:"Date"}},y:{display:!0,title:{display:!0,text:"Count"},ticks:{callback:function(i){return rt(i)}}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}})}catch(g){console.error("Error creating performance chart:",g)}},F=async()=>{var c,r;if(await X(),$&&$.destroy(),!(!a.value||!((r=(c=v.data)==null?void 0:c.charts)!=null&&r.secondary)))try{const{Chart:g}=await gt(async()=>{const{Chart:i}=await import("./auto-_svZoXzF.js");return{Chart:i}},__vite__mapDeps([0,1])),y=a.value.getContext("2d");$=new g(y,{type:"doughnut",data:v.data.charts.secondary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"},tooltip:{callbacks:{label:function(i){const h=i.dataset.data.reduce((W,K)=>W+K,0),P=(i.parsed/h*100).toFixed(1);return i.label+": "+rt(i.parsed)+" products ("+P+"%)"}}}}}})}catch(g){console.error("Error creating category chart:",g)}};return dt(()=>v.data,()=>{X(()=>{_(),F()})},{deep:!0}),dt([s,u,R],()=>{E.value=1}),ht(()=>{X(()=>{_(),F()})}),wt(()=>{Y&&Y.destroy(),$&&$.destroy()}),{performanceChartCanvas:d,categoryChartCanvas:a,chartPeriod:m,activeTab:C,productSearch:s,categoryFilter:u,statusFilter:R,currentPage:E,itemsPerPage:U,performanceTabs:T,productMetrics:ot,productInsights:at,categories:N,topProductsByTab:z,conversionFunnel:j,priceRanges:Z,stockStatus:et,filteredProducts:Q,totalPages:nt,paginatedProducts:lt,formatMetricValue:st,formatCurrency:vt,formatNumber:rt,getChangeIcon:ft,getStatLabel:it,formatStatValue:w,getStockClass:f,sortBy:S,updateChart:L,viewProduct:A,editProduct:B,handleInsightAction:tt}}},Xo={class:"products-report"},tl={class:"metrics-section"},el={class:"metrics-grid"},sl={class:"metric-header"},al={class:"metric-icon"},nl={key:0,class:"metric-change"},ol={class:"metric-content"},ll={class:"metric-label"},rl={class:"metric-value"},il={key:0,class:"metric-comparison"},dl={class:"charts-section"},cl={class:"charts-grid"},ul={class:"chart-container"},vl={class:"chart-header"},fl={class:"chart-controls"},gl={class:"chart-content"},pl={ref:"performanceChartCanvas",id:"performance-chart"},ml={class:"chart-container"},yl={class:"chart-content"},hl={ref:"categoryChartCanvas",id:"category-chart"},bl={class:"top-products-section"},_l={class:"performance-tabs"},Cl=["onClick"],kl={class:"products-grid"},wl={class:"product-rank"},Pl={class:"rank-number"},xl={key:0,class:"fas fa-crown"},Fl={key:1,class:"fas fa-medal"},Rl={key:2,class:"fas fa-award"},Dl={key:3,class:"fas fa-star"},Sl={class:"product-image"},Al=["src","alt"],Vl={class:"product-info"},$l={class:"product-name"},Tl={class:"product-category"},Ml={class:"product-sku"},Ul={class:"product-stats"},El={class:"stat-item"},Ol={class:"stat-label"},Il={class:"stat-value"},Ll={class:"stat-item"},Bl={class:"stat-value rating"},Nl={class:"analytics-section"},jl={class:"analytics-grid"},ql={class:"analytics-card"},zl={class:"funnel-steps"},Gl={class:"step-bar"},Hl={class:"step-info"},Ql={class:"step-name"},Kl={class:"step-value"},Wl={class:"step-percentage"},Yl={class:"analytics-card"},Zl={class:"price-ranges"},Jl={class:"range-label"},Xl={class:"range-bar"},tr={class:"range-stats"},er={class:"range-count"},sr={class:"range-percentage"},ar={class:"analytics-card"},nr={class:"stock-overview"},or={class:"stock-icon"},lr={class:"stock-info"},rr={class:"stock-label"},ir={class:"stock-count"},dr={class:"stock-percentage"},cr={class:"insights-section"},ur={class:"insights-grid"},vr={class:"insight-icon"},fr={class:"insight-content"},gr={class:"insight-title"},pr={class:"insight-description"},mr={key:0,class:"insight-action"},yr=["onClick"],hr={class:"insight-priority"},br={class:"products-table-section"},_r={class:"table-controls"},Cr={class:"search-box"},kr={class:"filter-controls"},wr=["value"],Pr={class:"table-container"},xr={class:"products-table"},Fr={class:"product-cell"},Rr=["src","alt"],Dr={class:"product-details"},Sr={class:"product-name"},Ar={class:"product-sku"},Vr={class:"category-badge"},$r={class:"views-count"},Tr={class:"sales-count"},Mr={class:"revenue-amount"},Ur={class:"rating-display"},Er={class:"action-buttons"},Or=["onClick"],Ir=["onClick"],Lr={key:0,class:"pagination"},Br=["disabled"],Nr=["disabled"],jr={class:"pagination-info"},qr=["disabled"],zr=["disabled"];function Gr(v,e,d,a,m,C){return o(),l("div",Xo,[t("div",tl,[e[16]||(e[16]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-box"}),b(" Product Performance ")],-1)),t("div",el,[(o(!0),l(V,null,I(a.productMetrics,s=>(o(),l("div",{key:s.key,class:x(["metric-card",s.trend])},[t("div",sl,[t("div",al,[t("i",{class:x(s.icon)},null,2)]),s.changePercentage?(o(),l("div",nl,[t("i",{class:x(a.getChangeIcon(s.changePercentage))},null,2),t("span",null,n(Math.abs(s.changePercentage).toFixed(1))+"%",1)])):G("",!0)]),t("div",ol,[t("div",ll,n(s.label),1),t("div",rl,n(a.formatMetricValue(s.value,s.type)),1),s.previousValue?(o(),l("div",il," vs "+n(a.formatMetricValue(s.previousValue,s.type))+" last period ",1)):G("",!0)])],2))),128))])]),t("div",dl,[t("div",cl,[t("div",ul,[t("div",vl,[e[18]||(e[18]=t("h3",{class:"chart-title"},"Product Views vs Sales",-1)),t("div",fl,[D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>a.chartPeriod=s),onChange:e[1]||(e[1]=(...s)=>a.updateChart&&a.updateChart(...s)),class:"chart-select"},e[17]||(e[17]=[t("option",{value:"daily"},"Daily",-1),t("option",{value:"weekly"},"Weekly",-1),t("option",{value:"monthly"},"Monthly",-1)]),544),[[H,a.chartPeriod]])])]),t("div",gl,[t("canvas",pl,null,512)])]),t("div",ml,[e[19]||(e[19]=t("div",{class:"chart-header"},[t("h3",{class:"chart-title"},"Products by Category")],-1)),t("div",yl,[t("canvas",hl,null,512)])])])]),t("div",bl,[e[22]||(e[22]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-star"}),b(" Top Performing Products ")],-1)),t("div",_l,[(o(!0),l(V,null,I(a.performanceTabs,s=>(o(),l("button",{key:s.key,onClick:u=>a.activeTab=s.key,class:x(["tab-button",{active:a.activeTab===s.key}])},[t("i",{class:x(s.icon)},null,2),b(" "+n(s.label),1)],10,Cl))),128))]),t("div",kl,[(o(!0),l(V,null,I(a.topProductsByTab,(s,u)=>{var R;return o(),l("div",{key:s.id,class:"product-card"},[t("div",wl,[t("span",Pl,"#"+n(u+1),1),t("div",{class:x(["rank-badge",`rank-${u+1}`])},[u===0?(o(),l("i",xl)):u===1?(o(),l("i",Fl)):u===2?(o(),l("i",Rl)):(o(),l("i",Dl))],2)]),t("div",Sl,[t("img",{src:s.image||"/placeholder-product.jpg",alt:s.name},null,8,Al)]),t("div",Vl,[t("div",$l,n(s.name),1),t("div",Tl,n(s.category),1),t("div",Ml,"SKU: "+n(s.sku),1)]),t("div",Ul,[t("div",El,[t("span",Ol,n(a.getStatLabel(a.activeTab)),1),t("span",Il,n(a.formatStatValue(s[a.activeTab],a.activeTab)),1)]),t("div",Ll,[e[21]||(e[21]=t("span",{class:"stat-label"},"Rating",-1)),t("span",Bl,[e[20]||(e[20]=t("i",{class:"fas fa-star"},null,-1)),b(" "+n(((R=s.rating)==null?void 0:R.toFixed(1))||"N/A"),1)])])])])}),128))])]),t("div",Nl,[e[26]||(e[26]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-chart-bar"}),b(" Product Analytics ")],-1)),t("div",jl,[t("div",ql,[e[23]||(e[23]=t("div",{class:"analytics-header"},[t("h4",null,"Conversion Funnel"),t("i",{class:"fas fa-funnel-dollar"})],-1)),t("div",zl,[(o(!0),l(V,null,I(a.conversionFunnel,s=>(o(),l("div",{key:s.name,class:"funnel-step"},[t("div",Gl,[t("div",{class:"step-fill",style:bt({width:s.percentage+"%"})},null,4)]),t("div",Hl,[t("span",Ql,n(s.name),1),t("span",Kl,n(a.formatNumber(s.value)),1),t("span",Wl,n(s.percentage.toFixed(1))+"%",1)])]))),128))])]),t("div",Yl,[e[24]||(e[24]=t("div",{class:"analytics-header"},[t("h4",null,"Price Analysis"),t("i",{class:"fas fa-tags"})],-1)),t("div",Zl,[(o(!0),l(V,null,I(a.priceRanges,s=>(o(),l("div",{key:s.range,class:"price-range"},[t("div",Jl,n(s.range),1),t("div",Xl,[t("div",{class:"range-fill",style:bt({width:s.percentage+"%"})},null,4)]),t("div",tr,[t("span",er,n(s.count)+" products",1),t("span",sr,n(s.percentage.toFixed(1))+"%",1)])]))),128))])]),t("div",ar,[e[25]||(e[25]=t("div",{class:"analytics-header"},[t("h4",null,"Stock Status"),t("i",{class:"fas fa-warehouse"})],-1)),t("div",nr,[(o(!0),l(V,null,I(a.stockStatus,s=>(o(),l("div",{key:s.status,class:x(["stock-item",s.status.toLowerCase()])},[t("div",or,[t("i",{class:x(s.icon)},null,2)]),t("div",lr,[t("div",rr,n(s.status),1),t("div",ir,n(s.count)+" products",1),t("div",dr,n(s.percentage.toFixed(1))+"%",1)])],2))),128))])])])]),t("div",cr,[e[27]||(e[27]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-lightbulb"}),b(" Product Insights ")],-1)),t("div",ur,[(o(!0),l(V,null,I(a.productInsights,s=>(o(),l("div",{key:s.id,class:x(["insight-card",s.type])},[t("div",vr,[t("i",{class:x(s.icon)},null,2)]),t("div",fr,[t("div",gr,n(s.title),1),t("div",pr,n(s.description),1),s.action?(o(),l("div",mr,[t("button",{class:"action-btn",onClick:u=>a.handleInsightAction(s)},n(s.action),9,yr)])):G("",!0)]),t("div",hr,[t("div",{class:x(["priority-indicator",`priority-${s.priority}`])},n(s.priority),3)])],2))),128))])]),t("div",br,[e[46]||(e[46]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-table"}),b(" Product Performance Details ")],-1)),t("div",_r,[t("div",Cr,[e[28]||(e[28]=t("i",{class:"fas fa-search"},null,-1)),D(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>a.productSearch=s),type:"text",placeholder:"Search products...",class:"search-input"},null,512),[[ut,a.productSearch]])]),t("div",kr,[D(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.categoryFilter=s),class:"filter-select"},[e[29]||(e[29]=t("option",{value:""},"All Categories",-1)),(o(!0),l(V,null,I(a.categories,s=>(o(),l("option",{key:s,value:s},n(s),9,wr))),128))],512),[[H,a.categoryFilter]]),D(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.statusFilter=s),class:"filter-select"},e[30]||(e[30]=[t("option",{value:""},"All Status",-1),t("option",{value:"active"},"Active",-1),t("option",{value:"inactive"},"Inactive",-1),t("option",{value:"out_of_stock"},"Out of Stock",-1)]),512),[[H,a.statusFilter]])])]),t("div",Pr,[t("table",xr,[t("thead",null,[t("tr",null,[t("th",{onClick:e[5]||(e[5]=s=>a.sortBy("name")),class:"sortable"},e[31]||(e[31]=[b(" Product "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[6]||(e[6]=s=>a.sortBy("category")),class:"sortable"},e[32]||(e[32]=[b(" Category "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[7]||(e[7]=s=>a.sortBy("views")),class:"sortable"},e[33]||(e[33]=[b(" Views "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[8]||(e[8]=s=>a.sortBy("sales")),class:"sortable"},e[34]||(e[34]=[b(" Sales "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[9]||(e[9]=s=>a.sortBy("revenue")),class:"sortable"},e[35]||(e[35]=[b(" Revenue "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[10]||(e[10]=s=>a.sortBy("rating")),class:"sortable"},e[36]||(e[36]=[b(" Rating "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[11]||(e[11]=s=>a.sortBy("stock")),class:"sortable"},e[37]||(e[37]=[b(" Stock "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),e[38]||(e[38]=t("th",null,"Actions",-1))])]),t("tbody",null,[(o(!0),l(V,null,I(a.paginatedProducts,s=>{var u;return o(),l("tr",{key:s.id,class:"product-row"},[t("td",null,[t("div",Fr,[t("img",{src:s.image||"/placeholder-product.jpg",alt:s.name,class:"product-thumbnail"},null,8,Rr),t("div",Dr,[t("div",Sr,n(s.name),1),t("div",Ar,n(s.sku),1)])])]),t("td",null,[t("span",Vr,n(s.category),1)]),t("td",null,[t("span",$r,n(a.formatNumber(s.views)),1)]),t("td",null,[t("span",Tr,n(a.formatNumber(s.sales)),1)]),t("td",null,[t("span",Mr,n(a.formatCurrency(s.revenue)),1)]),t("td",null,[t("div",Ur,[e[39]||(e[39]=t("i",{class:"fas fa-star"},null,-1)),t("span",null,n(((u=s.rating)==null?void 0:u.toFixed(1))||"N/A"),1)])]),t("td",null,[t("span",{class:x(["stock-indicator",a.getStockClass(s.stock)])},n(s.stock),3)]),t("td",null,[t("div",Er,[t("button",{onClick:R=>a.viewProduct(s),class:"action-btn view-btn"},e[40]||(e[40]=[t("i",{class:"fas fa-eye"},null,-1)]),8,Or),t("button",{onClick:R=>a.editProduct(s),class:"action-btn edit-btn"},e[41]||(e[41]=[t("i",{class:"fas fa-edit"},null,-1)]),8,Ir)])])])}),128))])])]),a.totalPages>1?(o(),l("div",Lr,[t("button",{onClick:e[12]||(e[12]=s=>a.currentPage=1),disabled:a.currentPage===1,class:"pagination-btn"},e[42]||(e[42]=[t("i",{class:"fas fa-angle-double-left"},null,-1)]),8,Br),t("button",{onClick:e[13]||(e[13]=s=>a.currentPage--),disabled:a.currentPage===1,class:"pagination-btn"},e[43]||(e[43]=[t("i",{class:"fas fa-angle-left"},null,-1)]),8,Nr),t("span",jr," Page "+n(a.currentPage)+" of "+n(a.totalPages),1),t("button",{onClick:e[14]||(e[14]=s=>a.currentPage++),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[44]||(e[44]=[t("i",{class:"fas fa-angle-right"},null,-1)]),8,qr),t("button",{onClick:e[15]||(e[15]=s=>a.currentPage=a.totalPages),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[45]||(e[45]=[t("i",{class:"fas fa-angle-double-right"},null,-1)]),8,zr)])):G("",!0)])])}const Hr=mt(Jo,[["render",Gr],["__scopeId","data-v-ae85d94e"]]),Qr={name:"UsersReport",props:{data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},setup(v){const e=k(null),d=k(null),a=k("daily"),m=k(""),C=k(""),s=k(""),u=k(1),R=k(10),E=k("registrationDate"),U=k("desc");let M=null,q=null;const Y=p(()=>{var r,g;return((g=(r=v.data)==null?void 0:r.metrics)==null?void 0:g.items)||[]}),$=p(()=>{var r;return((r=v.data)==null?void 0:r.insights)||[]}),T=p(()=>{var r,g;return((g=(r=v.data)==null?void 0:r.table)==null?void 0:g.data)||[]}),ot=p(()=>{const r=[{range:"18-24",min:18,max:24},{range:"25-34",min:25,max:34},{range:"35-44",min:35,max:44},{range:"45-54",min:45,max:54},{range:"55+",min:55,max:100}],g=T.value.length;return r.map(y=>{const i=T.value.filter(h=>{const P=h.age||30;return P>=y.min&&P<=y.max}).length;return{...y,count:i,percentage:g>0?i/g*100:0}})}),at=p(()=>{const r={};T.value.forEach(y=>{const i=y.location||"Unknown";r[i]=(r[i]||0)+1});const g=T.value.length;return Object.entries(r).map(([y,i])=>{const[h,P]=y.split(", ");return{city:h||y,region:P||"",users:i,percentage:g>0?i/g*100:0}}).sort((y,i)=>i.users-y.users).slice(0,5)}),J=p(()=>{const r=[{type:"Buyer",icon:"fas fa-shopping-cart",filter:y=>y.role==="buyer"},{type:"Seller",icon:"fas fa-store",filter:y=>y.role==="seller"},{type:"Admin",icon:"fas fa-user-shield",filter:y=>y.role==="admin"}],g=T.value.length;return r.map(y=>{const i=T.value.filter(y.filter).length;return{...y,count:i,percentage:g>0?i/g*100:0}})}),N=p(()=>{const r=T.value.map(y=>y.sessionDuration||15),g=r.reduce((y,i)=>y+i,0);return r.length>0?(g/r.length).toFixed(1):"0"}),z=p(()=>(parseFloat(N.value)*1.2).toFixed(1)),j=p(()=>(parseFloat(N.value)*.8).toFixed(1)),Z=p(()=>{const r=T.value.map(y=>y.pageViews||5),g=r.reduce((y,i)=>y+i,0);return r.length>0?(g/r.length).toFixed(1):"0"}),et=p(()=>{const r=[{range:"1-3 pages",min:1,max:3},{range:"4-7 pages",min:4,max:7},{range:"8+ pages",min:8,max:100}],g=T.value.length;return r.map(y=>{const i=T.value.filter(h=>{const P=h.pageViews||5;return P>=y.min&&P<=y.max}).length;return{...y,count:i,percentage:g>0?i/g*100:0}})}),Q=p(()=>{const r=T.value.filter(y=>y.isReturning).length,g=T.value.length;return g>0?(r/g*100).toFixed(1):"0"}),nt=p(()=>(parseFloat(Q.value)*.6).toFixed(1)),lt=p(()=>Q.value),st=p(()=>{let r=T.value;if(m.value){const g=m.value.toLowerCase();r=r.filter(y=>{var i,h,P;return((i=y.name)==null?void 0:i.toLowerCase().includes(g))||((h=y.email)==null?void 0:h.toLowerCase().includes(g))||((P=y.id)==null?void 0:P.toString().includes(g))})}return C.value&&(r=r.filter(g=>g.role===C.value)),s.value&&(r=r.filter(g=>g.status===s.value)),r.sort((g,y)=>{const i=g[E.value],h=y[E.value];if(typeof i=="number"&&typeof h=="number")return U.value==="asc"?i-h:h-i;if(E.value.includes("Date")||E.value.includes("Activity")){const K=new Date(i),ct=new Date(h);return U.value==="asc"?K-ct:ct-K}const P=String(i).toLowerCase(),W=String(h).toLowerCase();return U.value==="asc"?P.localeCompare(W):W.localeCompare(P)}),r}),vt=p(()=>Math.ceil(st.value.length/R.value)),rt=p(()=>{const r=(u.value-1)*R.value,g=r+R.value;return st.value.slice(r,g)}),ft=(r,g)=>{switch(g){case"currency":return O.formatCurrency(r);case"percentage":return O.formatPercentage(r);case"number":return O.formatNumber(r);default:return String(r)}},it=r=>O.formatCurrency(r),w=r=>O.formatNumber(r),f=r=>new Date(r).toLocaleDateString("uk-UA",{year:"numeric",month:"short",day:"numeric"}),S=r=>r>0?"fas fa-arrow-up":r<0?"fas fa-arrow-down":"fas fa-minus",L=r=>{E.value===r?U.value=U.value==="asc"?"desc":"asc":(E.value=r,U.value="asc"),u.value=1},A=()=>{F()},B=r=>{console.log("View user:",r)},tt=r=>{console.log("Edit user:",r)},_=r=>{console.log("Handle insight action:",r)},F=async()=>{var r,g;if(await X(),M&&M.destroy(),!(!e.value||!((g=(r=v.data)==null?void 0:r.charts)!=null&&g.primary)))try{const{Chart:y}=await gt(async()=>{const{Chart:h}=await import("./auto-_svZoXzF.js");return{Chart:h}},__vite__mapDeps([0,1])),i=e.value.getContext("2d");M=new y(i,{type:"line",data:v.data.charts.primary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1,callbacks:{label:function(h){return h.dataset.label+": "+w(h.parsed.y)+" users"}}}},scales:{x:{display:!0,title:{display:!0,text:"Date"}},y:{display:!0,title:{display:!0,text:"Users"},ticks:{callback:function(h){return w(h)}}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}})}catch(y){console.error("Error creating registration chart:",y)}},c=async()=>{var r,g;if(await X(),q&&q.destroy(),!(!d.value||!((g=(r=v.data)==null?void 0:r.charts)!=null&&g.secondary)))try{const{Chart:y}=await gt(async()=>{const{Chart:h}=await import("./auto-_svZoXzF.js");return{Chart:h}},__vite__mapDeps([0,1])),i=d.value.getContext("2d");q=new y(i,{type:"bar",data:v.data.charts.secondary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{callbacks:{label:function(h){return h.dataset.label+": "+w(h.parsed.y)+" users"}}}},scales:{x:{display:!0,title:{display:!0,text:"Activity Level"}},y:{display:!0,title:{display:!0,text:"Users"},ticks:{callback:function(h){return w(h)}}}}}})}catch(y){console.error("Error creating activity chart:",y)}};return dt(()=>v.data,()=>{X(()=>{F(),c()})},{deep:!0}),dt([m,C,s],()=>{u.value=1}),ht(()=>{X(()=>{F(),c()})}),wt(()=>{M&&M.destroy(),q&&q.destroy()}),{registrationChartCanvas:e,activityChartCanvas:d,chartPeriod:a,userSearch:m,roleFilter:C,statusFilter:s,currentPage:u,itemsPerPage:R,userMetrics:Y,userInsights:$,ageGroups:ot,topLocations:at,userTypes:J,averageSessionDuration:N,desktopSessionDuration:z,mobileSessionDuration:j,averagePageViews:Z,pageViewRanges:et,returnRate:Q,sevenDayReturn:nt,thirtyDayReturn:lt,filteredUsers:st,totalPages:vt,paginatedUsers:rt,formatMetricValue:ft,formatCurrency:it,formatNumber:w,formatDate:f,getChangeIcon:S,sortBy:L,updateChart:A,viewUser:B,editUser:tt,handleInsightAction:_}}},Kr={class:"users-report"},Wr={class:"metrics-section"},Yr={class:"metrics-grid"},Zr={class:"metric-header"},Jr={class:"metric-icon"},Xr={key:0,class:"metric-change"},ti={class:"metric-content"},ei={class:"metric-label"},si={class:"metric-value"},ai={key:0,class:"metric-comparison"},ni={class:"charts-section"},oi={class:"charts-grid"},li={class:"chart-container"},ri={class:"chart-header"},ii={class:"chart-controls"},di={class:"chart-content"},ci={ref:"registrationChartCanvas",id:"registration-chart"},ui={class:"chart-container"},vi={class:"chart-content"},fi={ref:"activityChartCanvas",id:"activity-chart"},gi={class:"demographics-section"},pi={class:"demographics-grid"},mi={class:"demographic-card"},yi={class:"age-groups"},hi={class:"group-label"},bi={class:"group-bar"},_i={class:"group-stats"},Ci={class:"group-count"},ki={class:"group-percentage"},wi={class:"demographic-card"},Pi={class:"locations-list"},xi={class:"location-info"},Fi={class:"location-name"},Ri={class:"location-region"},Di={class:"location-stats"},Si={class:"location-count"},Ai={class:"location-percentage"},Vi={class:"demographic-card"},$i={class:"user-types"},Ti={class:"type-icon"},Mi={class:"type-info"},Ui={class:"type-label"},Ei={class:"type-count"},Oi={class:"type-percentage"},Ii={class:"engagement-section"},Li={class:"engagement-grid"},Bi={class:"engagement-card"},Ni={class:"session-stats"},ji={class:"session-duration"},qi={class:"duration-value"},zi={class:"session-breakdown"},Gi={class:"breakdown-item"},Hi={class:"breakdown-value"},Qi={class:"breakdown-item"},Ki={class:"breakdown-value"},Wi={class:"engagement-card"},Yi={class:"pageview-stats"},Zi={class:"pageview-average"},Ji={class:"pageview-value"},Xi={class:"pageview-distribution"},td={class:"range-label"},ed={class:"range-bar"},sd={class:"range-percentage"},ad={class:"engagement-card"},nd={class:"return-stats"},od={class:"return-rate"},ld={class:"rate-value"},rd={class:"return-breakdown"},id={class:"return-item"},dd={class:"return-percentage"},cd={class:"return-item"},ud={class:"return-percentage"},vd={class:"insights-section"},fd={class:"insights-grid"},gd={class:"insight-icon"},pd={class:"insight-content"},md={class:"insight-title"},yd={class:"insight-description"},hd={key:0,class:"insight-action"},bd=["onClick"],_d={class:"insight-priority"},Cd={class:"users-table-section"},kd={class:"table-controls"},wd={class:"search-box"},Pd={class:"filter-controls"},xd={class:"table-container"},Fd={class:"users-table"},Rd={class:"user-cell"},Dd=["src","alt"],Sd={class:"user-details"},Ad={class:"user-name"},Vd={class:"user-id"},$d={class:"user-email"},Td={class:"date-text"},Md={class:"date-text"},Ud={class:"orders-count"},Ed={class:"spent-amount"},Od={class:"action-buttons"},Id=["onClick"],Ld=["onClick"],Bd={key:0,class:"pagination"},Nd=["disabled"],jd=["disabled"],qd={class:"pagination-info"},zd=["disabled"],Gd=["disabled"];function Hd(v,e,d,a,m,C){return o(),l("div",Kr,[t("div",Wr,[e[16]||(e[16]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-users"}),b(" User Analytics ")],-1)),t("div",Yr,[(o(!0),l(V,null,I(a.userMetrics,s=>(o(),l("div",{key:s.key,class:x(["metric-card",s.trend])},[t("div",Zr,[t("div",Jr,[t("i",{class:x(s.icon)},null,2)]),s.changePercentage?(o(),l("div",Xr,[t("i",{class:x(a.getChangeIcon(s.changePercentage))},null,2),t("span",null,n(Math.abs(s.changePercentage).toFixed(1))+"%",1)])):G("",!0)]),t("div",ti,[t("div",ei,n(s.label),1),t("div",si,n(a.formatMetricValue(s.value,s.type)),1),s.previousValue?(o(),l("div",ai," vs "+n(a.formatMetricValue(s.previousValue,s.type))+" last period ",1)):G("",!0)])],2))),128))])]),t("div",ni,[t("div",oi,[t("div",li,[t("div",ri,[e[18]||(e[18]=t("h3",{class:"chart-title"},"User Registration Trend",-1)),t("div",ii,[D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>a.chartPeriod=s),onChange:e[1]||(e[1]=(...s)=>a.updateChart&&a.updateChart(...s)),class:"chart-select"},e[17]||(e[17]=[t("option",{value:"daily"},"Daily",-1),t("option",{value:"weekly"},"Weekly",-1),t("option",{value:"monthly"},"Monthly",-1)]),544),[[H,a.chartPeriod]])])]),t("div",di,[t("canvas",ci,null,512)])]),t("div",ui,[e[19]||(e[19]=t("div",{class:"chart-header"},[t("h3",{class:"chart-title"},"User Activity")],-1)),t("div",vi,[t("canvas",fi,null,512)])])])]),t("div",gi,[e[23]||(e[23]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-chart-pie"}),b(" User Demographics ")],-1)),t("div",pi,[t("div",mi,[e[20]||(e[20]=t("div",{class:"demographic-header"},[t("h4",null,"Age Distribution"),t("i",{class:"fas fa-birthday-cake"})],-1)),t("div",yi,[(o(!0),l(V,null,I(a.ageGroups,s=>(o(),l("div",{key:s.range,class:"age-group"},[t("div",hi,n(s.range),1),t("div",bi,[t("div",{class:"group-fill",style:bt({width:s.percentage+"%"})},null,4)]),t("div",_i,[t("span",Ci,n(a.formatNumber(s.count)),1),t("span",ki,n(s.percentage.toFixed(1))+"%",1)])]))),128))])]),t("div",wi,[e[21]||(e[21]=t("div",{class:"demographic-header"},[t("h4",null,"Top Locations"),t("i",{class:"fas fa-map-marker-alt"})],-1)),t("div",Pi,[(o(!0),l(V,null,I(a.topLocations,s=>(o(),l("div",{key:s.city,class:"location-item"},[t("div",xi,[t("div",Fi,n(s.city),1),t("div",Ri,n(s.region),1)]),t("div",Di,[t("div",Si,n(a.formatNumber(s.users)),1),t("div",Ai,n(s.percentage.toFixed(1))+"%",1)])]))),128))])]),t("div",Vi,[e[22]||(e[22]=t("div",{class:"demographic-header"},[t("h4",null,"User Types"),t("i",{class:"fas fa-user-tag"})],-1)),t("div",$i,[(o(!0),l(V,null,I(a.userTypes,s=>(o(),l("div",{key:s.type,class:x(["user-type",s.type.toLowerCase()])},[t("div",Ti,[t("i",{class:x(s.icon)},null,2)]),t("div",Mi,[t("div",Ui,n(s.type),1),t("div",Ei,n(a.formatNumber(s.count))+" users",1),t("div",Oi,n(s.percentage.toFixed(1))+"%",1)])],2))),128))])])])]),t("div",Ii,[e[34]||(e[34]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-chart-line"}),b(" User Engagement ")],-1)),t("div",Li,[t("div",Bi,[e[27]||(e[27]=t("div",{class:"engagement-header"},[t("h4",null,"Average Session Duration"),t("i",{class:"fas fa-clock"})],-1)),t("div",Ni,[t("div",ji,[t("span",qi,n(a.averageSessionDuration),1),e[24]||(e[24]=t("span",{class:"duration-label"},"minutes",-1))]),t("div",zi,[t("div",Gi,[e[25]||(e[25]=t("span",{class:"breakdown-label"},"Desktop:",-1)),t("span",Hi,n(a.desktopSessionDuration)+" min",1)]),t("div",Qi,[e[26]||(e[26]=t("span",{class:"breakdown-label"},"Mobile:",-1)),t("span",Ki,n(a.mobileSessionDuration)+" min",1)])])])]),t("div",Wi,[e[29]||(e[29]=t("div",{class:"engagement-header"},[t("h4",null,"Pages per Session"),t("i",{class:"fas fa-file-alt"})],-1)),t("div",Yi,[t("div",Zi,[t("span",Ji,n(a.averagePageViews),1),e[28]||(e[28]=t("span",{class:"pageview-label"},"pages",-1))]),t("div",Xi,[(o(!0),l(V,null,I(a.pageViewRanges,s=>(o(),l("div",{key:s.range,class:"pageview-range"},[t("span",td,n(s.range),1),t("div",ed,[t("div",{class:"range-fill",style:bt({width:s.percentage+"%"})},null,4)]),t("span",sd,n(s.percentage.toFixed(1))+"%",1)]))),128))])])]),t("div",ad,[e[33]||(e[33]=t("div",{class:"engagement-header"},[t("h4",null,"Return Rate"),t("i",{class:"fas fa-redo"})],-1)),t("div",nd,[t("div",od,[t("span",ld,n(a.returnRate)+"%",1),e[30]||(e[30]=t("span",{class:"rate-label"},"return within 30 days",-1))]),t("div",rd,[t("div",id,[e[31]||(e[31]=t("span",{class:"return-period"},"7 days:",-1)),t("span",dd,n(a.sevenDayReturn)+"%",1)]),t("div",cd,[e[32]||(e[32]=t("span",{class:"return-period"},"30 days:",-1)),t("span",ud,n(a.thirtyDayReturn)+"%",1)])])])])])]),t("div",vd,[e[35]||(e[35]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-lightbulb"}),b(" User Insights ")],-1)),t("div",fd,[(o(!0),l(V,null,I(a.userInsights,s=>(o(),l("div",{key:s.id,class:x(["insight-card",s.type])},[t("div",gd,[t("i",{class:x(s.icon)},null,2)]),t("div",pd,[t("div",md,n(s.title),1),t("div",yd,n(s.description),1),s.action?(o(),l("div",hd,[t("button",{class:"action-btn",onClick:u=>a.handleInsightAction(s)},n(s.action),9,bd)])):G("",!0)]),t("div",_d,[t("div",{class:x(["priority-indicator",`priority-${s.priority}`])},n(s.priority),3)])],2))),128))])]),t("div",Cd,[e[54]||(e[54]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-table"}),b(" User Details ")],-1)),t("div",kd,[t("div",wd,[e[36]||(e[36]=t("i",{class:"fas fa-search"},null,-1)),D(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>a.userSearch=s),type:"text",placeholder:"Search users...",class:"search-input"},null,512),[[ut,a.userSearch]])]),t("div",Pd,[D(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.roleFilter=s),class:"filter-select"},e[37]||(e[37]=[t("option",{value:""},"All Roles",-1),t("option",{value:"buyer"},"Buyers",-1),t("option",{value:"seller"},"Sellers",-1),t("option",{value:"admin"},"Admins",-1)]),512),[[H,a.roleFilter]]),D(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.statusFilter=s),class:"filter-select"},e[38]||(e[38]=[t("option",{value:""},"All Status",-1),t("option",{value:"active"},"Active",-1),t("option",{value:"inactive"},"Inactive",-1),t("option",{value:"suspended"},"Suspended",-1)]),512),[[H,a.statusFilter]])])]),t("div",xd,[t("table",Fd,[t("thead",null,[t("tr",null,[t("th",{onClick:e[5]||(e[5]=s=>a.sortBy("name")),class:"sortable"},e[39]||(e[39]=[b(" User "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[6]||(e[6]=s=>a.sortBy("email")),class:"sortable"},e[40]||(e[40]=[b(" Email "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[7]||(e[7]=s=>a.sortBy("role")),class:"sortable"},e[41]||(e[41]=[b(" Role "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[8]||(e[8]=s=>a.sortBy("registrationDate")),class:"sortable"},e[42]||(e[42]=[b(" Registered "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[9]||(e[9]=s=>a.sortBy("lastActivity")),class:"sortable"},e[43]||(e[43]=[b(" Last Activity "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[10]||(e[10]=s=>a.sortBy("orders")),class:"sortable"},e[44]||(e[44]=[b(" Orders "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[11]||(e[11]=s=>a.sortBy("totalSpent")),class:"sortable"},e[45]||(e[45]=[b(" Total Spent "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),e[46]||(e[46]=t("th",null,"Status",-1)),e[47]||(e[47]=t("th",null,"Actions",-1))])]),t("tbody",null,[(o(!0),l(V,null,I(a.paginatedUsers,s=>(o(),l("tr",{key:s.id,class:"user-row"},[t("td",null,[t("div",Rd,[t("img",{src:s.avatar||"/placeholder-avatar.jpg",alt:s.name,class:"user-avatar"},null,8,Dd),t("div",Sd,[t("div",Ad,n(s.name),1),t("div",Vd,"ID: "+n(s.id),1)])])]),t("td",null,[t("span",$d,n(s.email),1)]),t("td",null,[t("span",{class:x(["role-badge",s.role])},n(s.role),3)]),t("td",null,[t("span",Td,n(a.formatDate(s.registrationDate)),1)]),t("td",null,[t("span",Md,n(a.formatDate(s.lastActivity)),1)]),t("td",null,[t("span",Ud,n(a.formatNumber(s.orders)),1)]),t("td",null,[t("span",Ed,n(a.formatCurrency(s.totalSpent)),1)]),t("td",null,[t("span",{class:x(["status-indicator",s.status])},n(s.status),3)]),t("td",null,[t("div",Od,[t("button",{onClick:u=>a.viewUser(s),class:"action-btn view-btn"},e[48]||(e[48]=[t("i",{class:"fas fa-eye"},null,-1)]),8,Id),t("button",{onClick:u=>a.editUser(s),class:"action-btn edit-btn"},e[49]||(e[49]=[t("i",{class:"fas fa-edit"},null,-1)]),8,Ld)])])]))),128))])])]),a.totalPages>1?(o(),l("div",Bd,[t("button",{onClick:e[12]||(e[12]=s=>a.currentPage=1),disabled:a.currentPage===1,class:"pagination-btn"},e[50]||(e[50]=[t("i",{class:"fas fa-angle-double-left"},null,-1)]),8,Nd),t("button",{onClick:e[13]||(e[13]=s=>a.currentPage--),disabled:a.currentPage===1,class:"pagination-btn"},e[51]||(e[51]=[t("i",{class:"fas fa-angle-left"},null,-1)]),8,jd),t("span",qd," Page "+n(a.currentPage)+" of "+n(a.totalPages),1),t("button",{onClick:e[14]||(e[14]=s=>a.currentPage++),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[52]||(e[52]=[t("i",{class:"fas fa-angle-right"},null,-1)]),8,zd),t("button",{onClick:e[15]||(e[15]=s=>a.currentPage=a.totalPages),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[53]||(e[53]=[t("i",{class:"fas fa-angle-double-right"},null,-1)]),8,Gd)])):G("",!0)])])}const Qd=mt(Qr,[["render",Hd],["__scopeId","data-v-13247512"]]),Kd={name:"OrdersReport",props:{data:{type:Object,required:!0},dateRange:{type:Object,required:!0},filters:{type:Object,default:()=>({})}},setup(v){const e=k(null),d=k(null),a=k("daily"),m=k(""),C=k(""),s=k(""),u=k(1),R=k(10),E=k("date"),U=k("desc");let M=null,q=null;const Y=p(()=>{var i,h;return((h=(i=v.data)==null?void 0:i.metrics)==null?void 0:h.items)||[]}),$=p(()=>{var i;return((i=v.data)==null?void 0:i.insights)||[]}),T=p(()=>{var i,h;return((h=(i=v.data)==null?void 0:i.table)==null?void 0:h.data)||[]}),ot=p(()=>{const i=[{status:"Pending",icon:"fas fa-clock",filter:P=>P.status==="pending"},{status:"Processing",icon:"fas fa-cog",filter:P=>P.status==="processing"},{status:"Shipped",icon:"fas fa-shipping-fast",filter:P=>P.status==="shipped"},{status:"Delivered",icon:"fas fa-check-circle",filter:P=>P.status==="delivered"},{status:"Cancelled",icon:"fas fa-times-circle",filter:P=>P.status==="cancelled"}],h=T.value.length;return i.map(P=>{const W=T.value.filter(P.filter).length;return{...P,count:W,percentage:h>0?W/h*100:0,trend:Math.random()*20-10}})}),at=p(()=>{const i=T.value;return i.length===0?0:i.reduce((P,W)=>P+(W.total||0),0)/i.length}),J=p(()=>at.value*1.05),N=p(()=>at.value*.95),z=p(()=>{const i=[{range:"< ₴500",min:0,max:500},{range:"₴500 - ₴1,500",min:500,max:1500},{range:"₴1,500 - ₴3,000",min:1500,max:3e3},{range:"₴3,000 - ₴5,000",min:3e3,max:5e3},{range:"> ₴5,000",min:5e3,max:1/0}],h=T.value.length;return i.map(P=>{const W=T.value.filter(K=>{const ct=K.total||0;return ct>=P.min&&ct<P.max}).length;return{...P,count:W,percentage:h>0?W/h*100:0}})}),j=p(()=>{const i=[{method:"Credit Card",icon:"fas fa-credit-card",filter:P=>P.paymentMethod==="card"},{method:"PayPal",icon:"fab fa-paypal",filter:P=>P.paymentMethod==="paypal"},{method:"Bank Transfer",icon:"fas fa-university",filter:P=>P.paymentMethod==="bank"}],h=T.value.length;return i.map(P=>{const W=T.value.filter(P.filter).length;return{...P,count:W,percentage:h>0?W/h*100:0}})}),Z=p(()=>"18.5"),et=p(()=>"45"),Q=p(()=>"35"),nt=p(()=>"92.3"),lt=p(()=>"15"),st=p(()=>"77"),vt=p(()=>"8"),rt=p(()=>{let i=T.value;if(m.value){const h=m.value.toLowerCase();i=i.filter(P=>{var W,K,ct;return((W=P.orderNumber)==null?void 0:W.toLowerCase().includes(h))||((K=P.customer)==null?void 0:K.toLowerCase().includes(h))||((ct=P.customerEmail)==null?void 0:ct.toLowerCase().includes(h))})}return C.value&&(i=i.filter(h=>h.status===C.value)),s.value&&(i=i.filter(h=>h.paymentMethod===s.value)),i.sort((h,P)=>{const W=h[E.value],K=P[E.value];if(typeof W=="number"&&typeof K=="number")return U.value==="asc"?W-K:K-W;if(E.value==="date"){const Ft=new Date(W),Rt=new Date(K);return U.value==="asc"?Ft-Rt:Rt-Ft}const ct=String(W).toLowerCase(),xt=String(K).toLowerCase();return U.value==="asc"?ct.localeCompare(xt):xt.localeCompare(ct)}),i}),ft=p(()=>Math.ceil(rt.value.length/R.value)),it=p(()=>{const i=(u.value-1)*R.value,h=i+R.value;return rt.value.slice(i,h)}),w=(i,h)=>{switch(h){case"currency":return O.formatCurrency(i);case"percentage":return O.formatPercentage(i);case"number":return O.formatNumber(i);default:return String(i)}},f=i=>O.formatCurrency(i),S=i=>O.formatNumber(i),L=i=>new Date(i).toLocaleDateString("uk-UA",{year:"numeric",month:"short",day:"numeric"}),A=i=>i>0?"fas fa-arrow-up":i<0?"fas fa-arrow-down":"fas fa-minus",B=i=>i>0?"fas fa-arrow-up":i<0?"fas fa-arrow-down":"fas fa-minus",tt=i=>{E.value===i?U.value=U.value==="asc"?"desc":"asc":(E.value=i,U.value="asc"),u.value=1},_=()=>{g()},F=i=>{console.log("View order:",i)},c=i=>{console.log("Edit order:",i)},r=i=>{console.log("Handle insight action:",i)},g=async()=>{var i,h;if(await X(),M&&M.destroy(),!(!e.value||!((h=(i=v.data)==null?void 0:i.charts)!=null&&h.primary)))try{const{Chart:P}=await gt(async()=>{const{Chart:K}=await import("./auto-_svZoXzF.js");return{Chart:K}},__vite__mapDeps([0,1])),W=e.value.getContext("2d");M=new P(W,{type:"line",data:v.data.charts.primary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1,callbacks:{label:function(K){return K.dataset.label+": "+S(K.parsed.y)+" orders"}}}},scales:{x:{display:!0,title:{display:!0,text:"Date"}},y:{display:!0,title:{display:!0,text:"Orders"},ticks:{callback:function(K){return S(K)}}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}})}catch(P){console.error("Error creating volume chart:",P)}},y=async()=>{var i,h;if(await X(),q&&q.destroy(),!(!d.value||!((h=(i=v.data)==null?void 0:i.charts)!=null&&h.secondary)))try{const{Chart:P}=await gt(async()=>{const{Chart:K}=await import("./auto-_svZoXzF.js");return{Chart:K}},__vite__mapDeps([0,1])),W=d.value.getContext("2d");q=new P(W,{type:"doughnut",data:v.data.charts.secondary.data,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"},tooltip:{callbacks:{label:function(K){const ct=K.dataset.data.reduce((Ft,Rt)=>Ft+Rt,0),xt=(K.parsed/ct*100).toFixed(1);return K.label+": "+S(K.parsed)+" orders ("+xt+"%)"}}}}}})}catch(P){console.error("Error creating status chart:",P)}};return dt(()=>v.data,()=>{X(()=>{g(),y()})},{deep:!0}),dt([m,C,s],()=>{u.value=1}),ht(()=>{X(()=>{g(),y()})}),wt(()=>{M&&M.destroy(),q&&q.destroy()}),{volumeChartCanvas:e,statusChartCanvas:d,chartPeriod:a,orderSearch:m,statusFilter:C,paymentFilter:s,currentPage:u,itemsPerPage:R,orderMetrics:Y,orderInsights:$,orderStatuses:ot,averageOrderValue:at,currentMonthAOV:J,lastMonthAOV:N,orderValueRanges:z,paymentMethods:j,averageProcessingTime:Z,sameDayProcessing:et,nextDayProcessing:Q,onTimeDeliveryRate:nt,earlyDelivery:lt,onTimeDelivery:st,lateDelivery:vt,filteredOrders:rt,totalPages:ft,paginatedOrders:it,formatMetricValue:w,formatCurrency:f,formatNumber:S,formatDate:L,getChangeIcon:A,getTrendIcon:B,sortBy:tt,updateChart:_,viewOrder:F,editOrder:c,handleInsightAction:r}}},Wd={class:"orders-report"},Yd={class:"metrics-section"},Zd={class:"metrics-grid"},Jd={class:"metric-header"},Xd={class:"metric-icon"},tc={key:0,class:"metric-change"},ec={class:"metric-content"},sc={class:"metric-label"},ac={class:"metric-value"},nc={key:0,class:"metric-comparison"},oc={class:"charts-section"},lc={class:"charts-grid"},rc={class:"chart-container"},ic={class:"chart-header"},dc={class:"chart-controls"},cc={class:"chart-content"},uc={ref:"volumeChartCanvas",id:"volume-chart"},vc={class:"chart-container"},fc={class:"chart-content"},gc={ref:"statusChartCanvas",id:"status-chart"},pc={class:"status-section"},mc={class:"status-grid"},yc={class:"status-icon"},hc={class:"status-info"},bc={class:"status-label"},_c={class:"status-count"},Cc={class:"status-percentage"},kc={key:0,class:"status-trend"},wc={class:"value-section"},Pc={class:"value-grid"},xc={class:"value-card"},Fc={class:"value-content"},Rc={class:"value-amount"},Dc={class:"value-breakdown"},Sc={class:"breakdown-item"},Ac={class:"breakdown-value"},Vc={class:"breakdown-item"},$c={class:"breakdown-value"},Tc={class:"value-card"},Mc={class:"value-ranges"},Uc={class:"range-label"},Ec={class:"range-bar"},Oc={class:"range-stats"},Ic={class:"range-count"},Lc={class:"range-percentage"},Bc={class:"value-card"},Nc={class:"payment-methods"},jc={class:"method-icon"},qc={class:"method-info"},zc={class:"method-name"},Gc={class:"method-count"},Hc={class:"method-percentage"},Qc={class:"fulfillment-section"},Kc={class:"fulfillment-grid"},Wc={class:"fulfillment-card"},Yc={class:"processing-stats"},Zc={class:"processing-time"},Jc={class:"time-value"},Xc={class:"processing-breakdown"},tu={class:"breakdown-item"},eu={class:"breakdown-value"},su={class:"breakdown-item"},au={class:"breakdown-value"},nu={class:"fulfillment-card"},ou={class:"delivery-stats"},lu={class:"delivery-rate"},ru={class:"rate-value"},iu={class:"delivery-breakdown"},du={class:"delivery-item"},cu={class:"delivery-percentage"},uu={class:"delivery-item"},vu={class:"delivery-percentage"},fu={class:"delivery-item"},gu={class:"delivery-percentage"},pu={class:"insights-section"},mu={class:"insights-grid"},yu={class:"insight-icon"},hu={class:"insight-content"},bu={class:"insight-title"},_u={class:"insight-description"},Cu={key:0,class:"insight-action"},ku=["onClick"],wu={class:"insight-priority"},Pu={class:"orders-table-section"},xu={class:"table-controls"},Fu={class:"search-box"},Ru={class:"filter-controls"},Du={class:"table-container"},Su={class:"orders-table"},Au={class:"order-number"},Vu={class:"customer-cell"},$u={class:"customer-name"},Tu={class:"customer-email"},Mu={class:"order-date"},Uu={class:"order-total"},Eu={class:"items-count"},Ou={class:"payment-method"},Iu={class:"action-buttons"},Lu=["onClick"],Bu=["onClick"],Nu={key:0,class:"pagination"},ju=["disabled"],qu=["disabled"],zu={class:"pagination-info"},Gu=["disabled"],Hu=["disabled"];function Qu(v,e,d,a,m,C){return o(),l("div",Wd,[t("div",Yd,[e[15]||(e[15]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-shopping-cart"}),b(" Order Analytics ")],-1)),t("div",Zd,[(o(!0),l(V,null,I(a.orderMetrics,s=>(o(),l("div",{key:s.key,class:x(["metric-card",s.trend])},[t("div",Jd,[t("div",Xd,[t("i",{class:x(s.icon)},null,2)]),s.changePercentage?(o(),l("div",tc,[t("i",{class:x(a.getChangeIcon(s.changePercentage))},null,2),t("span",null,n(Math.abs(s.changePercentage).toFixed(1))+"%",1)])):G("",!0)]),t("div",ec,[t("div",sc,n(s.label),1),t("div",ac,n(a.formatMetricValue(s.value,s.type)),1),s.previousValue?(o(),l("div",nc," vs "+n(a.formatMetricValue(s.previousValue,s.type))+" last period ",1)):G("",!0)])],2))),128))])]),t("div",oc,[t("div",lc,[t("div",rc,[t("div",ic,[e[17]||(e[17]=t("h3",{class:"chart-title"},"Order Volume Trend",-1)),t("div",dc,[D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>a.chartPeriod=s),onChange:e[1]||(e[1]=(...s)=>a.updateChart&&a.updateChart(...s)),class:"chart-select"},e[16]||(e[16]=[t("option",{value:"daily"},"Daily",-1),t("option",{value:"weekly"},"Weekly",-1),t("option",{value:"monthly"},"Monthly",-1)]),544),[[H,a.chartPeriod]])])]),t("div",cc,[t("canvas",uc,null,512)])]),t("div",vc,[e[18]||(e[18]=t("div",{class:"chart-header"},[t("h3",{class:"chart-title"},"Order Status")],-1)),t("div",fc,[t("canvas",gc,null,512)])])])]),t("div",pc,[e[19]||(e[19]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-tasks"}),b(" Order Status Overview ")],-1)),t("div",mc,[(o(!0),l(V,null,I(a.orderStatuses,s=>(o(),l("div",{key:s.status,class:x(["status-card",s.status.toLowerCase().replace(" ","-")])},[t("div",yc,[t("i",{class:x(s.icon)},null,2)]),t("div",hc,[t("div",bc,n(s.status),1),t("div",_c,n(a.formatNumber(s.count)),1),t("div",Cc,n(s.percentage.toFixed(1))+"%",1)]),s.trend?(o(),l("div",kc,[t("i",{class:x(a.getTrendIcon(s.trend))},null,2),t("span",null,n(Math.abs(s.trend).toFixed(1))+"%",1)])):G("",!0)],2))),128))])]),t("div",wc,[e[25]||(e[25]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-chart-bar"}),b(" Order Value Analysis ")],-1)),t("div",Pc,[t("div",xc,[e[22]||(e[22]=t("div",{class:"value-header"},[t("h4",null,"Average Order Value"),t("i",{class:"fas fa-calculator"})],-1)),t("div",Fc,[t("div",Rc,n(a.formatCurrency(a.averageOrderValue)),1),t("div",Dc,[t("div",Sc,[e[20]||(e[20]=t("span",{class:"breakdown-label"},"This Month:",-1)),t("span",Ac,n(a.formatCurrency(a.currentMonthAOV)),1)]),t("div",Vc,[e[21]||(e[21]=t("span",{class:"breakdown-label"},"Last Month:",-1)),t("span",$c,n(a.formatCurrency(a.lastMonthAOV)),1)])])])]),t("div",Tc,[e[23]||(e[23]=t("div",{class:"value-header"},[t("h4",null,"Order Value Distribution"),t("i",{class:"fas fa-chart-pie"})],-1)),t("div",Mc,[(o(!0),l(V,null,I(a.orderValueRanges,s=>(o(),l("div",{key:s.range,class:"value-range"},[t("div",Uc,n(s.range),1),t("div",Ec,[t("div",{class:"range-fill",style:bt({width:s.percentage+"%"})},null,4)]),t("div",Oc,[t("span",Ic,n(s.count)+" orders",1),t("span",Lc,n(s.percentage.toFixed(1))+"%",1)])]))),128))])]),t("div",Bc,[e[24]||(e[24]=t("div",{class:"value-header"},[t("h4",null,"Payment Methods"),t("i",{class:"fas fa-credit-card"})],-1)),t("div",Nc,[(o(!0),l(V,null,I(a.paymentMethods,s=>(o(),l("div",{key:s.method,class:"payment-method"},[t("div",jc,[t("i",{class:x(s.icon)},null,2)]),t("div",qc,[t("div",zc,n(s.method),1),t("div",Gc,n(a.formatNumber(s.count))+" orders",1),t("div",Hc,n(s.percentage.toFixed(1))+"%",1)])]))),128))])])])]),t("div",Qc,[e[35]||(e[35]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-shipping-fast"}),b(" Order Fulfillment ")],-1)),t("div",Kc,[t("div",Wc,[e[29]||(e[29]=t("div",{class:"fulfillment-header"},[t("h4",null,"Average Processing Time"),t("i",{class:"fas fa-clock"})],-1)),t("div",Yc,[t("div",Zc,[t("span",Jc,n(a.averageProcessingTime),1),e[26]||(e[26]=t("span",{class:"time-label"},"hours",-1))]),t("div",Xc,[t("div",tu,[e[27]||(e[27]=t("span",{class:"breakdown-label"},"Same Day:",-1)),t("span",eu,n(a.sameDayProcessing)+"%",1)]),t("div",su,[e[28]||(e[28]=t("span",{class:"breakdown-label"},"Next Day:",-1)),t("span",au,n(a.nextDayProcessing)+"%",1)])])])]),t("div",nu,[e[34]||(e[34]=t("div",{class:"fulfillment-header"},[t("h4",null,"Delivery Performance"),t("i",{class:"fas fa-truck"})],-1)),t("div",ou,[t("div",lu,[t("span",ru,n(a.onTimeDeliveryRate)+"%",1),e[30]||(e[30]=t("span",{class:"rate-label"},"on-time delivery",-1))]),t("div",iu,[t("div",du,[e[31]||(e[31]=t("span",{class:"delivery-period"},"Early:",-1)),t("span",cu,n(a.earlyDelivery)+"%",1)]),t("div",uu,[e[32]||(e[32]=t("span",{class:"delivery-period"},"On Time:",-1)),t("span",vu,n(a.onTimeDelivery)+"%",1)]),t("div",fu,[e[33]||(e[33]=t("span",{class:"delivery-period"},"Late:",-1)),t("span",gu,n(a.lateDelivery)+"%",1)])])])])])]),t("div",pu,[e[36]||(e[36]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-lightbulb"}),b(" Order Insights ")],-1)),t("div",mu,[(o(!0),l(V,null,I(a.orderInsights,s=>(o(),l("div",{key:s.id,class:x(["insight-card",s.type])},[t("div",yu,[t("i",{class:x(s.icon)},null,2)]),t("div",hu,[t("div",bu,n(s.title),1),t("div",_u,n(s.description),1),s.action?(o(),l("div",Cu,[t("button",{class:"action-btn",onClick:u=>a.handleInsightAction(s)},n(s.action),9,ku)])):G("",!0)]),t("div",wu,[t("div",{class:x(["priority-indicator",`priority-${s.priority}`])},n(s.priority),3)])],2))),128))])]),t("div",Pu,[e[54]||(e[54]=t("h3",{class:"section-title"},[t("i",{class:"fas fa-table"}),b(" Order Details ")],-1)),t("div",xu,[t("div",Fu,[e[37]||(e[37]=t("i",{class:"fas fa-search"},null,-1)),D(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>a.orderSearch=s),type:"text",placeholder:"Search orders...",class:"search-input"},null,512),[[ut,a.orderSearch]])]),t("div",Ru,[D(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>a.statusFilter=s),class:"filter-select"},e[38]||(e[38]=[pt('<option value="" data-v-6f403178>All Status</option><option value="pending" data-v-6f403178>Pending</option><option value="processing" data-v-6f403178>Processing</option><option value="shipped" data-v-6f403178>Shipped</option><option value="delivered" data-v-6f403178>Delivered</option><option value="cancelled" data-v-6f403178>Cancelled</option>',6)]),512),[[H,a.statusFilter]]),D(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>a.paymentFilter=s),class:"filter-select"},e[39]||(e[39]=[t("option",{value:""},"All Payment Methods",-1),t("option",{value:"card"},"Credit Card",-1),t("option",{value:"paypal"},"PayPal",-1),t("option",{value:"bank"},"Bank Transfer",-1)]),512),[[H,a.paymentFilter]])])]),t("div",Du,[t("table",Su,[t("thead",null,[t("tr",null,[t("th",{onClick:e[5]||(e[5]=s=>a.sortBy("orderNumber")),class:"sortable"},e[40]||(e[40]=[b(" Order # "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[6]||(e[6]=s=>a.sortBy("customer")),class:"sortable"},e[41]||(e[41]=[b(" Customer "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[7]||(e[7]=s=>a.sortBy("date")),class:"sortable"},e[42]||(e[42]=[b(" Date "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[8]||(e[8]=s=>a.sortBy("total")),class:"sortable"},e[43]||(e[43]=[b(" Total "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[9]||(e[9]=s=>a.sortBy("items")),class:"sortable"},e[44]||(e[44]=[b(" Items "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),t("th",{onClick:e[10]||(e[10]=s=>a.sortBy("status")),class:"sortable"},e[45]||(e[45]=[b(" Status "),t("i",{class:"fas fa-sort sort-icon"},null,-1)])),e[46]||(e[46]=t("th",null,"Payment",-1)),e[47]||(e[47]=t("th",null,"Actions",-1))])]),t("tbody",null,[(o(!0),l(V,null,I(a.paginatedOrders,s=>(o(),l("tr",{key:s.id,class:"order-row"},[t("td",null,[t("span",Au,n(s.orderNumber),1)]),t("td",null,[t("div",Vu,[t("div",$u,n(s.customer),1),t("div",Tu,n(s.customerEmail),1)])]),t("td",null,[t("span",Mu,n(a.formatDate(s.date)),1)]),t("td",null,[t("span",Uu,n(a.formatCurrency(s.total)),1)]),t("td",null,[t("span",Eu,n(s.items)+" items",1)]),t("td",null,[t("span",{class:x(["status-badge",s.status])},n(s.status),3)]),t("td",null,[t("span",Ou,n(s.paymentMethod),1)]),t("td",null,[t("div",Iu,[t("button",{onClick:u=>a.viewOrder(s),class:"action-btn view-btn"},e[48]||(e[48]=[t("i",{class:"fas fa-eye"},null,-1)]),8,Lu),t("button",{onClick:u=>a.editOrder(s),class:"action-btn edit-btn"},e[49]||(e[49]=[t("i",{class:"fas fa-edit"},null,-1)]),8,Bu)])])]))),128))])])]),a.totalPages>1?(o(),l("div",Nu,[t("button",{onClick:e[11]||(e[11]=s=>a.currentPage=1),disabled:a.currentPage===1,class:"pagination-btn"},e[50]||(e[50]=[t("i",{class:"fas fa-angle-double-left"},null,-1)]),8,ju),t("button",{onClick:e[12]||(e[12]=s=>a.currentPage--),disabled:a.currentPage===1,class:"pagination-btn"},e[51]||(e[51]=[t("i",{class:"fas fa-angle-left"},null,-1)]),8,qu),t("span",zu," Page "+n(a.currentPage)+" of "+n(a.totalPages),1),t("button",{onClick:e[13]||(e[13]=s=>a.currentPage++),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[52]||(e[52]=[t("i",{class:"fas fa-angle-right"},null,-1)]),8,Gu),t("button",{onClick:e[14]||(e[14]=s=>a.currentPage=a.totalPages),disabled:a.currentPage===a.totalPages,class:"pagination-btn"},e[53]||(e[53]=[t("i",{class:"fas fa-angle-double-right"},null,-1)]),8,Hu)])):G("",!0)])])}const Ku=mt(Kd,[["render",Qu],["__scopeId","data-v-6f403178"]]),Wu={name:"Reports",components:{ReportFilters:pe,ReportPreview:bs,ReportExport:Ca,FinancialReport:$n,SalesReport:Zo,ProductsReport:Hr,UsersReport:Qd,OrdersReport:Ku},setup(){const{showToast:v}=$t(),e=k(!1),d=k(null),a=k(null),m=k("financial"),C=Pt({startDate:new Date(Date.now()-30*24*60*60*1e3),endDate:new Date}),s=Pt({}),u=p(()=>m.value&&C.startDate&&C.endDate),R=async()=>{if(u.value){e.value=!0,d.value=null;try{const $={startDate:C.startDate,endDate:C.endDate,...s};let T;switch(m.value){case"financial":T=await O.getFinancialReport($);break;case"sales":T=await O.getSalesReport($);break;case"products":T=await O.getProductsReport($);break;case"users":T=await O.getUsersReport($);break;case"orders":T=await O.getOrdersReport($);break;default:throw new Error(`Unknown report type: ${m.value}`)}a.value=T,v("Report generated successfully","success")}catch($){d.value=$.message||"Failed to load report data",v("Failed to generate report","error"),console.error("Report loading error:",$)}finally{e.value=!1}}},E=()=>{a.value=null,d.value=null,u.value&&R()},U=$=>{v(`Starting ${$.toUpperCase()} export...`,"info")},M=($,T)=>{v(`${$.toUpperCase()} export completed: ${T}`,"success")},q=($,T)=>{v(`${$.toUpperCase()} export failed: ${T}`,"error")},Y=$=>{s.period=$,u.value&&R()};return dt([m,()=>C.startDate,()=>C.endDate],()=>{E()},{deep:!0}),ht(()=>{u.value&&R()}),{loading:e,error:d,reportData:a,selectedReportType:m,dateRange:C,additionalFilters:s,canGenerateReport:u,loadReportData:R,handleFiltersChange:E,handlePeriodChange:Y,handleExportStarted:U,handleExportCompleted:M,handleExportError:q}}},Yu={class:"reports-page"},Zu={class:"filters-section"},Ju={class:"report-content"},Xu={class:"content-wrapper"},tv={key:0,class:"loading-container"},ev={key:1,class:"error-container"},sv={class:"error-message"},av={key:2,class:"report-preview"},nv={key:3,class:"empty-state"},ov={key:0,class:"export-section"};function lv(v,e,d,a,m,C){const s=yt("ReportFilters"),u=yt("FinancialReport"),R=yt("SalesReport"),E=yt("ProductsReport"),U=yt("UsersReport"),M=yt("OrdersReport"),q=yt("ReportPreview"),Y=yt("ReportExport");return o(),l("div",Yu,[e[9]||(e[9]=pt('<div class="page-header" data-v-2c7f17b4><div class="header-content" data-v-2c7f17b4><h1 class="page-title" data-v-2c7f17b4><i class="fas fa-chart-bar" data-v-2c7f17b4></i> Reports &amp; Analytics </h1><p class="page-description" data-v-2c7f17b4> Comprehensive business intelligence and analytics dashboard for monitoring performance, tracking sales, and generating detailed reports across all business metrics. </p></div></div>',1)),t("div",Zu,[St(s,{reportType:a.selectedReportType,"onUpdate:reportType":e[0]||(e[0]=$=>a.selectedReportType=$),dateRange:a.dateRange,"onUpdate:dateRange":e[1]||(e[1]=$=>a.dateRange=$),additionalFilters:a.additionalFilters,"onUpdate:additionalFilters":e[2]||(e[2]=$=>a.additionalFilters=$),onFiltersChanged:a.handleFiltersChange},null,8,["reportType","dateRange","additionalFilters","onFiltersChanged"])]),t("div",Ju,[t("div",Xu,[a.loading?(o(),l("div",tv,e[4]||(e[4]=[t("div",{class:"loading-spinner"},[t("i",{class:"fas fa-spinner fa-spin"}),t("p",null,"Generating report...")],-1)]))):a.error?(o(),l("div",ev,[t("div",sv,[e[6]||(e[6]=t("i",{class:"fas fa-exclamation-triangle"},null,-1)),e[7]||(e[7]=t("h3",null,"Error Loading Report",-1)),t("p",null,n(a.error),1),t("button",{onClick:e[3]||(e[3]=(...$)=>a.loadReportData&&a.loadReportData(...$)),class:"retry-btn"},e[5]||(e[5]=[t("i",{class:"fas fa-redo"},null,-1),b(" Retry ")]))])])):a.reportData?(o(),l("div",av,[a.selectedReportType==="financial"?(o(),kt(u,{key:0,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters},null,8,["data","dateRange","filters"])):a.selectedReportType==="sales"?(o(),kt(R,{key:1,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters,onPeriodChanged:a.handlePeriodChange},null,8,["data","dateRange","filters","onPeriodChanged"])):a.selectedReportType==="products"?(o(),kt(E,{key:2,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters,onPeriodChanged:a.handlePeriodChange},null,8,["data","dateRange","filters","onPeriodChanged"])):a.selectedReportType==="users"?(o(),kt(U,{key:3,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters},null,8,["data","dateRange","filters"])):a.selectedReportType==="orders"?(o(),kt(M,{key:4,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters},null,8,["data","dateRange","filters"])):(o(),kt(q,{key:5,reportType:a.selectedReportType,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters},null,8,["reportType","data","dateRange","filters"]))])):(o(),l("div",nv,e[8]||(e[8]=[t("div",{class:"empty-message"},[t("i",{class:"fas fa-chart-line"}),t("h3",null,"Select Report Parameters"),t("p",null,"Choose a report type and date range to generate your analytics report.")],-1)])))])]),a.reportData?(o(),l("div",ov,[St(Y,{reportType:a.selectedReportType,data:a.reportData,dateRange:a.dateRange,filters:a.additionalFilters,onExportStarted:a.handleExportStarted,onExportCompleted:a.handleExportCompleted,onExportError:a.handleExportError},null,8,["reportType","data","dateRange","filters","onExportStarted","onExportCompleted","onExportError"])])):G("",!0)])}const iv=mt(Wu,[["render",lv],["__scopeId","data-v-2c7f17b4"]]);export{iv as default};
