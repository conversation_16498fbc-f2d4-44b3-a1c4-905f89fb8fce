<template>
  <div class="toast-container">
    <transition-group name="toast">
      <div
        v-for="toast in toasts"
        :key="toast.id"
        class="toast-message"
        :class="[`toast-${toast.type}`]">
        <span class="toast-icon">
          <i :class="getIconClass(toast.type)"></i>
        </span>
        <span class="toast-text">{{ toast.message }}</span>
        <button class="toast-close" @click="removeToast(toast.id)">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </transition-group>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const toasts = ref([]);
let nextId = 1;

// Add a new toast
const addToast = (message, type = 'info', duration = 5000) => {
  const id = nextId++;
  toasts.value.push({ id, message, type, duration });

  // Auto-remove after duration
  if (duration > 0) {
    setTimeout(() => {
      removeToast(id);
    }, duration);
  }

  return id;
};

// Remove a toast by id
const removeToast = (id) => {
  const index = toasts.value.findIndex(toast => toast.id === id);
  if (index !== -1) {
    toasts.value.splice(index, 1);
  }
};

// Get icon class based on toast type
const getIconClass = (type) => {
  switch (type) {
    case 'success': return 'fas fa-check-circle';
    case 'error': return 'fas fa-exclamation-circle';
    case 'warning': return 'fas fa-exclamation-triangle';
    case 'info':
    default: return 'fas fa-info-circle';
  }
};

// Expose methods for external use
defineExpose({ addToast, removeToast });
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  left: auto;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  pointer-events: none;
}

.toast-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: white;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  min-width: 250px;
  max-width: 350px;
  pointer-events: auto;
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

.toast-icon {
  margin-right: 12px;
  font-size: 18px;
}

.toast-text {
  flex: 1;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #777;
  padding: 0;
  margin-left: 12px;
  font-size: 14px;
}

.toast-success {
  background-color: #ebf7ed;
  border-left: 4px solid #48c774;
}

.toast-success .toast-icon {
  color: #48c774;
}

.toast-error {
  background-color: #feecf0;
  border-left: 4px solid #f14668;
}

.toast-error .toast-icon {
  color: #f14668;
}

.toast-warning {
  background-color: #fff8e6;
  border-left: 4px solid #ffdd57;
}

.toast-warning .toast-icon {
  color: #ffa500;
}

.toast-info {
  background-color: #eef6fc;
  border-left: 4px solid #3e8ed0;
}

.toast-info .toast-icon {
  color: #3e8ed0;
}

/* Animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
