import api from './api.service';

class CouponService {
  /**
   * Валідує купон за кодом
   * @param {string} code - Код купона
   * @returns {Promise} - Результат валідації
   */
  async validateCoupon(code) {
    try {
      const response = await api.get(`/users/me/coupons/validate?code=${encodeURIComponent(code)}`);
      return response.data;
    } catch (error) {
      console.error('Error validating coupon:', error);
      throw error;
    }
  }

  /**
   * Застосовує купон до замовлення
   * @param {string} couponCode - Код купона
   * @param {string} orderId - ID замовлення
   * @returns {Promise} - Результат застосування
   */
  async applyCoupon(couponCode, orderId) {
    try {
      const response = await api.post('/users/me/coupons/apply', {
        couponCode,
        orderId
      });
      return response.data;
    } catch (error) {
      console.error('Error applying coupon:', error);
      throw error;
    }
  }

  /**
   * Розраховує знижку для корзини
   * @param {Object} coupon - Об'єкт купона
   * @param {number} totalAmount - Загальна сума корзини
   * @returns {Object} - Об'єкт з розрахунками знижки
   */
  calculateDiscount(coupon, totalAmount) {
    if (!coupon || !coupon.discount) {
      return {
        discountAmount: 0,
        finalAmount: totalAmount,
        discountPercentage: 0
      };
    }

    let discountAmount = 0;
    let discountPercentage = 0;

    // discountType: 0 = Percentage, 1 = Fixed
    if (coupon.discountType === 0 || coupon.discountType === 'Percentage') {
      // Відсоткова знижка
      discountPercentage = coupon.discount;
      discountAmount = (totalAmount * coupon.discount) / 100;
    } else if (coupon.discountType === 1 || coupon.discountType === 'Fixed') {
      // Фіксована знижка
      discountAmount = Math.min(coupon.discount, totalAmount); // Не більше ніж загальна сума
      discountPercentage = totalAmount > 0 ? (discountAmount / totalAmount) * 100 : 0;
    }

    const finalAmount = Math.max(0, totalAmount - discountAmount);

    return {
      discountAmount: Math.round(discountAmount * 100) / 100, // Округлюємо до 2 знаків
      finalAmount: Math.round(finalAmount * 100) / 100,
      discountPercentage: Math.round(discountPercentage * 100) / 100,
      couponCode: coupon.code,
      discountType: coupon.discountType
    };
  }

  /**
   * Форматує тип знижки для відображення
   * @param {string} discountType - Тип знижки (Percentage/Fixed)
   * @returns {string} - Форматований текст
   */
  formatDiscountType(discountType) {
    const types = {
      0: 'Відсоткова',
      1: 'Фіксована',
      'Percentage': 'Відсоткова',
      'Fixed': 'Фіксована'
    };
    return types[discountType] || 'Невідомий тип';
  }

  /**
   * Форматує знижку для відображення
   * @param {Object} coupon - Об'єкт купона
   * @returns {string} - Форматований текст знижки
   */
  formatDiscount(coupon) {
    if (!coupon) return '';

    // discountType: 0 = Percentage, 1 = Fixed
    if (coupon.discountType === 0 || coupon.discountType === 'Percentage') {
      return coupon.discount; // Повертаємо тільки число для відсоткової знижки
    } else if (coupon.discountType === 1 || coupon.discountType === 'Fixed') {
      return coupon.discount; // Повертаємо тільки число для фіксованої знижки
    }

    return coupon.discount;
  }

  /**
   * Перевіряє чи купон ще дійсний
   * @param {Object} coupon - Об'єкт купона
   * @returns {boolean} - true якщо купон дійсний
   */
  isCouponValid(coupon) {
    if (!coupon) return false;
    
    // Перевіряємо термін дії
    if (coupon.expiresAt) {
      const expiryDate = new Date(coupon.expiresAt);
      const now = new Date();
      if (expiryDate <= now) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Отримує повідомлення про статус купона
   * @param {Object} coupon - Об'єкт купона
   * @returns {string} - Повідомлення про статус
   */
  getCouponStatusMessage(coupon) {
    if (!coupon) return 'Купон не знайдено';
    
    if (!this.isCouponValid(coupon)) {
      return 'Термін дії купона закінчився';
    }
    
    return 'Купон активний';
  }
}

export default new CouponService();
