﻿using FluentValidation;
using Marketplace.Application.Commands.ProductImage;
using Marketplace.Application.Extensions;
using Marketplace.Domain.ValueObjects;

namespace Marketplace.Application.Validators;

public class StoreProductImageRequestValidator : AbstractValidator<StoreProductImageCommand>
{
    public StoreProductImageRequestValidator()
    {
        RuleFor(x => x.ProductId)
            .NotEmpty().WithMessage("Ідентифікатор продукту не може бути пустим.");

        RuleFor(x => x.Image)
            .NotEmpty().WithMessage("Зображення не може бути пустим.")
            .MustBeValueObject(value => new Url(value))
            .WithMessage("Невірний формат зображення.");
    }
}

public class UpdateProductImageRequestValidator : AbstractValidator<UpdateProductImageCommand>
{
    public UpdateProductImageRequestValidator()
    {
        RuleFor(x => x.Image)
            .MustBeValueObject(value => new Url(value))
            .WithMessage("Невірний формат зображення.")
            .When(x => !string.IsNullOrEmpty(x.Image));
    }
}

