using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Product;

public class GetProductWithImagesQueryHandler : IRequestHandler<GetProductWithImagesQuery, ProductWithImagesResponse>
{
    private readonly IProductRepository _productRepository;

    public GetProductWithImagesQueryHandler(IProductRepository productRepository)
    {
        _productRepository = productRepository;
    }

    public async Task<ProductWithImagesResponse> Handle(GetProductWithImagesQuery request, CancellationToken cancellationToken)
    {
        // Включаємо всі необхідні навігаційні властивості
        var includes = new Expression<Func<Domain.Entities.Product, object>>[]
        {
            p => p.Company,
            p => p.Category,
            p => p.Seller,
            p => p.Images
        };

        var product = await _productRepository.GetByIdAsync(request.Id, cancellationToken, includes);
        
        if (product == null)
        {
            throw new InvalidOperationException($"Продукт з ID {request.Id} не знайдено.");
        }

        return new ProductWithImagesResponse
        {
            Id = product.Id,
            Name = product.Name,
            Slug = product.Slug.Value,
            Description = product.Description,
            PriceAmount = product.Price.Amount,
            PriceCurrency = product.Price.Currency.ToString(),
            Stock = product.Stock,
            Status = product.Status.ToString(),
            CompanyId = product.CompanyId,
            CompanyName = product.Company?.Name ?? "Unknown",
            CategoryId = product.CategoryId,
            CategoryName = product.Category?.Name ?? "Unknown",
            Attributes = product.Attributes,
            IsApproved = product.IsApproved,
            ApprovedAt = product.ApprovedAt,
            ApprovedByUserId = product.ApprovedByUserId,
            CreatedAt = product.CreatedAt,
            UpdatedAt = product.UpdatedAt,
            MetaTitle = product.Meta.Title,
            MetaDescription = product.Meta.Description,
            MetaImage = product.Meta.Image?.Value,
            Images = product.Images
                .OrderBy(i => i.Order)
                .ThenBy(i => i.CreatedAt)
                .Select(i => new ProductImageResponse
                {
                    Id = i.Id,
                    ProductId = i.ProductId,
                    Image = i.Image.Value,
                    Order = i.Order,
                    IsMain = i.IsMain,
                    AltText = i.AltText,
                    CreatedAt = i.CreatedAt
                })
                .ToList()
        };
    }
}
