import{a as j}from"./EnhancedCategorySelector-v7wVF3eo.js";import{p as q}from"./products-Bpq90UOX.js";import{_ as z,g as f,H as L,i as R,N as F,s as H,o as v,w as N,d as S,a as t,t as E,n as I,h as T,c as h,x as K,D as k,O as M,F as O,p as P}from"./index-L-hJxM_5.js";const G={class:"company-info"},J={class:"company-name"},Q={__name:"EnhancedCompanySelector",props:{modelValue:{type:[String,Number],default:null},label:{type:String,default:"Company"},placeholder:{type:String,default:"Search companies..."},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},errorMessage:{type:String,default:""},helpText:{type:String,default:""}},emits:["update:modelValue","change","select"],setup(c,{expose:_,emit:V}){const $=c,b=V,u=f([]),n=f(!1),d=f(null),p={data:null,timestamp:null,ttl:5*60*1e3},D=i=>i.isApproved?"✓ Approved":"⏳ Pending",y=(i,s)=>{if(!s)return i;const o=s.toLowerCase();return i.filter(g=>g.name.toLowerCase().includes(o)||g.slug&&g.slug.toLowerCase().includes(o))},A=async(i=!1)=>{if(!i&&p.data&&p.timestamp&&Date.now()-p.timestamp<p.ttl){u.value=p.data;return}try{n.value=!0,d.value&&d.value.abort(),d.value=new AbortController;const s=await q.getCompanies({pageSize:1e3,signal:d.value.signal});let o=[];Array.isArray(s)?o=s:s&&Array.isArray(s.data)?o=s.data:o=[],o.sort((g,B)=>g.name.localeCompare(B.name)),u.value=o,p.data=o,p.timestamp=Date.now()}catch(s){s.name!=="AbortError"&&(console.error("Error loading companies:",s),u.value=[])}finally{n.value=!1}},w=i=>{b("search",i)};return L(()=>$.modelValue,async i=>{i&&u.value.length===0&&await A()},{immediate:!0}),R(async()=>{await A()}),F(()=>{d.value&&d.value.abort()}),_({loadCompanies:A,companies:u}),(i,s)=>(v(),H(j,{"model-value":c.modelValue,"onUpdate:modelValue":s[0]||(s[0]=o=>i.$emit("update:modelValue",o)),onChange:s[1]||(s[1]=o=>i.$emit("change",o)),onSelect:s[2]||(s[2]=o=>i.$emit("select",o)),onSearch:w,label:c.label,placeholder:c.placeholder||"Search companies...",required:c.required,disabled:c.disabled,"error-message":c.errorMessage,"help-text":c.helpText,items:u.value,loading:n.value,"item-key":"id","item-name":"name","item-subtext":D,"filter-function":y,"max-height":"300px","show-on-focus":!0,"min-search-length":0},{item:N(({item:o})=>[t("div",G,[t("span",J,E(o.name),1),t("span",{class:I(["company-status",o.isApproved?"approved":"pending"])},E(o.isApproved?"✓ Approved":"⏳ Pending"),3)])]),empty:N(()=>s[3]||(s[3]=[S(" No companies found ")])),_:1},8,["model-value","label","placeholder","required","disabled","error-message","help-text","items","loading"]))}},ye=z(Q,[["__scopeId","data-v-357eedca"]]),W={class:"enhanced-attributes-editor"},X={class:"admin-add-attribute"},Y={class:"admin-add-form"},Z={class:"admin-field-group"},ee={class:"admin-field-group"},te=["disabled"],ae={key:0,class:"admin-attributes-table"},se={class:"admin-table"},le=["data-key","onDragstart","onDrop"],ne={class:"admin-attribute-key"},oe={class:"admin-key-name"},ie={class:"admin-attribute-values"},re={class:"admin-values-list"},de=["onClick","title"],ue={class:"admin-attribute-actions"},me={class:"admin-action-buttons"},ce=["onClick","title"],pe={key:1,class:"admin-empty-state"},ve={__name:"EnhancedProductAttributesEditor",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(c,{emit:_}){const V=c,$=_,b=f(""),u=f(""),n=f({}),d=f(-1),p=f(null),D=a=>{if(!a)return{};const e={};return Object.keys(a).forEach(l=>{const m=a[l];Array.isArray(m)?e[l]=[...m]:typeof m=="string"?e[l]=m.includes(",")?m.split(",").map(r=>r.trim()).filter(r=>r):[m]:e[l]=[String(m)]}),e};n.value=D(V.modelValue);const y=T(()=>Object.keys(n.value)),A=T(()=>b.value.trim()&&u.value.trim()),w=()=>{const a=b.value.trim(),e=u.value.trim();!a||!e||(n.value[a]?n.value[a].includes(e)||n.value[a].push(e):n.value[a]=[e],b.value="",u.value="",x())},i=(a,e)=>{n.value[a]&&n.value[a].length>e&&(n.value[a].splice(e,1),n.value[a].length===0&&delete n.value[a],x())},s=a=>{confirm(`Are you sure you want to remove the entire '${a}' attribute?`)&&(delete n.value[a],x())},o=(a,e)=>{d.value=e,a.dataTransfer.effectAllowed="move",a.target.style.opacity="0.5"},g=a=>{a.preventDefault(),a.dataTransfer.dropEffect="move"},B=(a,e)=>{if(a.preventDefault(),d.value===-1||d.value===e)return;const l=[...y.value],m=l[d.value];l.splice(d.value,1),l.splice(e,0,m);const r={};l.forEach(C=>{r[C]=n.value[C]}),n.value=r,x()},U=a=>{a.target.style.opacity="",d.value=-1},x=()=>{$("update:modelValue",{...n.value})};return L(()=>V.modelValue,a=>{n.value=D(a)},{deep:!0,immediate:!0}),(a,e)=>(v(),h("div",W,[e[10]||(e[10]=t("div",{class:"admin-attributes-header"},[t("h4",{class:"admin-attributes-title"},[t("i",{class:"fas fa-tags"}),S(" Product Attributes ")])],-1)),t("div",X,[t("div",Y,[t("div",Z,[e[2]||(e[2]=t("label",{class:"admin-field-label"},"Attribute Name",-1)),K(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>b.value=l),type:"text",class:"admin-field-input",placeholder:"e.g., Color, Size, Material",onKeydown:M(w,["enter"])},null,544),[[k,b.value]])]),t("div",ee,[e[3]||(e[3]=t("label",{class:"admin-field-label"},"Value",-1)),K(t("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>u.value=l),type:"text",class:"admin-field-input",placeholder:"Enter attribute value",onKeydown:M(w,["enter"])},null,544),[[k,u.value]])]),t("button",{type:"button",class:"admin-btn admin-btn-primary",onClick:w,disabled:!A.value},e[4]||(e[4]=[t("i",{class:"fas fa-plus"},null,-1),S(" Add Item ")]),8,te)])]),y.value.length>0?(v(),h("div",ae,[t("table",se,[e[8]||(e[8]=t("thead",null,[t("tr",null,[t("th",{class:"drag-column"}),t("th",null,"Attribute Name"),t("th",null,"Values"),t("th",null,"Actions")])],-1)),t("tbody",{ref_key:"tableBody",ref:p},[(v(!0),h(O,null,P(y.value,(l,m)=>(v(),h("tr",{key:l,class:"admin-attribute-row","data-key":l,draggable:"true",onDragstart:r=>o(r,m),onDragover:g,onDrop:r=>B(r,m),onDragend:U},[e[7]||(e[7]=t("td",{class:"drag-column"},[t("i",{class:"fas fa-grip-vertical admin-drag-handle"})],-1)),t("td",ne,[t("span",oe,E(l),1)]),t("td",ie,[t("div",re,[(v(!0),h(O,null,P(n.value[l],(r,C)=>(v(),h("span",{key:`${l}-${C}`,class:"admin-value-tag"},[S(E(r)+" ",1),t("button",{type:"button",class:"admin-value-remove",onClick:fe=>i(l,C),title:`Remove '${r}' from ${l}`},e[5]||(e[5]=[t("i",{class:"fas fa-times"},null,-1)]),8,de)]))),128))])]),t("td",ue,[t("div",me,[t("button",{type:"button",class:"admin-btn admin-btn-xs admin-btn-danger",onClick:r=>s(l),title:`Remove entire '${l}' attribute`},e[6]||(e[6]=[t("i",{class:"fas fa-trash"},null,-1),S(" Remove ")]),8,ce)])])],40,le))),128))],512)])])):(v(),h("div",pe,e[9]||(e[9]=[t("i",{class:"fas fa-tags admin-empty-icon"},null,-1),t("p",{class:"admin-empty-text"},"No attributes added yet",-1),t("p",{class:"admin-empty-subtext"},"Add attributes to describe your product features",-1)])))]))}},Ae=z(ve,[["__scopeId","data-v-9de7c69f"]]);export{ye as E,Ae as a};
