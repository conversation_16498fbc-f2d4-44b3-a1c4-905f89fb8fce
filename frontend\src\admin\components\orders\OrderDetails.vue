<template>
  <div class="order-details">
    <div class="columns">
      <!-- Left Column - Order Information -->
      <div class="column is-8">
        <div class="card">
          <div class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-receipt"></i>
              </span>
              Order Information
            </p>
          </div>
          <div class="card-content">
            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label">Order ID</label>
                  <div class="control">
                    <input class="input" type="text" :value="order.id" readonly>
                  </div>
                </div>
                <div class="field">
                  <label class="label">Customer Name</label>
                  <div class="control">
                    <input class="input" type="text" :value="order.customerName" readonly>
                  </div>
                </div>
                <div class="field">
                  <label class="label">Customer Email</label>
                  <div class="control">
                    <input class="input" type="email" :value="order.customerEmail" readonly>
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">Order Date</label>
                  <div class="control">
                    <input class="input" type="text" :value="formatDate(order.createdAt)" readonly>
                  </div>
                </div>
                <div class="field">
                  <label class="label">Order Time</label>
                  <div class="control">
                    <input class="input" type="text" :value="formatTime(order.createdAt)" readonly>
                  </div>
                </div>
                <div class="field">
                  <label class="label">Total Amount</label>
                  <div class="control">
                    <input class="input" type="text" :value="formatCurrency(order.totalPriceAmount || order.total)" readonly>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Shipping Address -->
        <div class="card mt-4">
          <div class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-shipping-fast"></i>
              </span>
              Shipping Address
            </p>
          </div>
          <div class="card-content">
            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label">Address Line</label>
                  <div class="control">
                    <input class="input" type="text" :value="order.shippingAddress?.address1 || order.shippingAddress?.address || 'N/A'" readonly>
                  </div>
                </div>
                <div class="field">
                  <label class="label">City</label>
                  <div class="control">
                    <input class="input" type="text" :value="order.shippingAddress?.city || 'N/A'" readonly>
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">Country</label>
                  <div class="control">
                    <input class="input" type="text" :value="order.shippingAddress?.country || 'N/A'" readonly>
                  </div>
                </div>
                <div class="field">
                  <label class="label">Shipping Method</label>
                  <div class="control">
                    <input class="input" type="text" :value="order.shippingMethodName || order.shippingMethod || 'N/A'" readonly>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Notes Section -->
        <div class="card mt-4">
          <div class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-sticky-note"></i>
              </span>
              Notes
            </p>
          </div>
          <div class="card-content">
            <div class="field">
              <div class="control">
                <textarea
                  class="textarea"
                  :value="order.notes || 'No notes'"
                  readonly
                  rows="3"
                  placeholder="No notes available for this order"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Status Information -->
      <div class="column is-4">
        <div class="card">
          <div class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-info-circle"></i>
              </span>
              Status Information
            </p>
          </div>
          <div class="card-content">
            <div class="field">
              <label class="label">Order Status</label>
              <div class="control">
                <span class="tag is-medium" :class="getOrderStatusClass(order.status)">
                  {{ getOrderStatusText(order.status) }}
                </span>
              </div>
            </div>
            <div class="field">
              <label class="label">Payment Status</label>
              <div class="control">
                <span class="tag is-medium" :class="getPaymentStatusClass(order.paymentStatus)">
                  {{ getPaymentStatusText(order.paymentStatus) }}
                </span>
              </div>
            </div>
            <div class="field">
              <label class="label">Payment Method</label>
              <div class="control">
                <input class="input" type="text" :value="order.paymentMethodText || order.paymentMethod || 'N/A'" readonly>
              </div>
            </div>
          </div>
        </div>

        <!-- Customer Information -->
        <div class="card mt-4">
          <div class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-user"></i>
              </span>
              Customer Information
            </p>
          </div>
          <div class="card-content">
            <div class="field">
              <label class="label">Customer ID</label>
              <div class="control">
                <input class="input" type="text" :value="order.customerId" readonly>
              </div>
            </div>
            <div class="field">
              <label class="label">Name</label>
              <div class="control">
                <input class="input" type="text" :value="order.customerName" readonly>
              </div>
            </div>
            <div class="field">
              <label class="label">Email</label>
              <div class="control">
                <input class="input" type="email" :value="order.customerEmail" readonly>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  order: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

// Utility functions
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return 'N/A';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// Status functions (matching backend enums)
const getOrderStatusText = (status) => {
  const statusMap = {
    0: 'Processing',
    1: 'Pending',
    2: 'Shipped',
    3: 'Delivered',
    4: 'Cancelled',
    'Processing': 'Processing',
    'Pending': 'Pending',
    'Shipped': 'Shipped',
    'Delivered': 'Delivered',
    'Cancelled': 'Cancelled'
  };
  return statusMap[status] || status || 'Unknown';
};

const getOrderStatusClass = (status) => {
  const classMap = {
    0: 'is-info',
    1: 'is-warning',
    2: 'is-primary',
    3: 'is-success',
    4: 'is-danger',
    'Processing': 'is-info',
    'Pending': 'is-warning',
    'Shipped': 'is-primary',
    'Delivered': 'is-success',
    'Cancelled': 'is-danger'
  };
  return classMap[status] || 'is-light';
};

const getPaymentStatusText = (status) => {
  const statusMap = {
    0: 'Pending',
    1: 'Completed',
    2: 'Refunded',
    3: 'Failed',
    'Pending': 'Pending',
    'Completed': 'Completed',
    'Refunded': 'Refunded',
    'Failed': 'Failed'
  };
  return statusMap[status] || status || 'Unknown';
};

const getPaymentStatusClass = (status) => {
  const classMap = {
    0: 'is-warning',
    1: 'is-success',
    2: 'is-info',
    3: 'is-danger',
    'Pending': 'is-warning',
    'Completed': 'is-success',
    'Refunded': 'is-info',
    'Failed': 'is-danger'
  };
  return classMap[status] || 'is-light';
};
</script>

<style scoped>
.order-details {
  padding: 1rem;
}

.card {
  box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
}

.card-header-title {
  align-items: center;
}

.card-header-title .icon {
  margin-right: 0.5rem;
}

.field:not(:last-child) {
  margin-bottom: 1rem;
}

.tag.is-medium {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}
</style>
