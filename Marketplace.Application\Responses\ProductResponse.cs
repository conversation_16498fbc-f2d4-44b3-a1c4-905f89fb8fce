﻿using Marketplace.Domain.Entities;
namespace Marketplace.Application.Responses;

public class ProductResponse
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string Name { get; set; }
    public string Slug { get; set; }
    public string Description { get; set; }
    public string PriceCurrency { get; set; }
    public decimal PriceAmount { get; set; }
    public uint Stock { get; set; }
    public uint Sales { get; set; }
    public Guid CategoryId { get; set; }
    public string CategoryName { get; set; }
    public Dictionary<string, string> Attributes { get; set; }
    public ProductStatus Status { get; set; }
    public string MetaTitle { get; set; }
    public string MetaDescription { get; set; }
    public string MetaImage { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<ProductImageResponse> Images { get; set; } = new List<ProductImageResponse>();
    public ProductResponse() { }
    public ProductResponse(
        Guid id,
        Guid companyId,
        string companyName,
        string name,
        string slug,
        string description,
        string priceCurrency,
        decimal priceAmount,
        uint stock,
        uint sales,
        Guid categoryId,
        string categoryName,
        Dictionary<string, string> attributes,
        ProductStatus status,
        string metaTitle,
        string metaDescription,
        string metaImage,
        DateTime createdAt,
        DateTime? updatedAt = null,
        List<ProductImageResponse> images = null)
    {
        Id = id;
        CompanyId = companyId;
        CompanyName = companyName;
        Name = name;
        Slug = slug;
        Description = description;
        PriceCurrency = priceCurrency;
        PriceAmount = priceAmount;
        Stock = stock;
        Sales = sales;
        CategoryId = categoryId;
        CategoryName = categoryName;
        Attributes = attributes;
        Status = status;
        MetaTitle = metaTitle;
        MetaDescription = metaDescription;
        MetaImage = metaImage;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        Images = images ?? new List<ProductImageResponse>();
    }
}