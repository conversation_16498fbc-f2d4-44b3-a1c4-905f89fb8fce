import { ref } from 'vue';

// Enhanced event bus for admin components
class EventBus {
  constructor() {
    this.events = {};
    this.eventHistory = [];
    this.maxHistorySize = 100;
    this.debugMode = process.env.NODE_ENV === 'development';
  }

  // Subscribe to an event
  on(event, callback, options = {}) {
    if (!this.events[event]) {
      this.events[event] = [];
    }

    const listener = {
      callback,
      id: this._generateId(),
      once: options.once || false,
      priority: options.priority || 0,
      component: options.component || 'unknown'
    };

    this.events[event].push(listener);

    // Sort by priority (higher priority first)
    this.events[event].sort((a, b) => b.priority - a.priority);

    if (this.debugMode) {
      console.log(`📡 EventBus: Subscribed to '${event}' (component: ${listener.component}, id: ${listener.id})`);
    }

    // Return unsubscribe function
    return () => {
      this.events[event] = this.events[event].filter(l => l.id !== listener.id);
      if (this.debugMode) {
        console.log(`📡 EventBus: Unsubscribed from '${event}' (id: ${listener.id})`);
      }
    };
  }

  // Emit an event
  emit(event, data = {}) {
    const eventData = {
      event,
      data,
      timestamp: Date.now(),
      id: this._generateId()
    };

    // Add to history
    this._addToHistory(eventData);

    if (this.debugMode) {
      console.log(`📡 EventBus: Emitting '${event}'`, eventData);
    }

    if (this.events[event]) {
      const listenersToRemove = [];

      this.events[event].forEach(listener => {
        try {
          listener.callback(eventData);

          // Remove one-time listeners
          if (listener.once) {
            listenersToRemove.push(listener.id);
          }
        } catch (error) {
          console.error(`📡 EventBus: Error in listener for '${event}':`, error);
        }
      });

      // Remove one-time listeners
      if (listenersToRemove.length > 0) {
        this.events[event] = this.events[event].filter(l => !listenersToRemove.includes(l.id));
      }
    }

    return eventData.id;
  }

  // Remove all listeners for an event
  off(event) {
    if (this.debugMode && this.events[event]) {
      console.log(`📡 EventBus: Removing all listeners for '${event}' (${this.events[event].length} listeners)`);
    }
    delete this.events[event];
  }

  // Get event history
  getHistory(eventType = null) {
    if (eventType) {
      return this.eventHistory.filter(e => e.event === eventType);
    }
    return [...this.eventHistory];
  }

  // Clear event history
  clearHistory() {
    this.eventHistory = [];
    if (this.debugMode) {
      console.log('📡 EventBus: Event history cleared');
    }
  }

  // Get active listeners count
  getListenersCount(event = null) {
    if (event) {
      return this.events[event]?.length || 0;
    }
    return Object.values(this.events).reduce((total, listeners) => total + listeners.length, 0);
  }

  // Get debug info
  getDebugInfo() {
    return {
      events: Object.keys(this.events),
      totalListeners: this.getListenersCount(),
      historySize: this.eventHistory.length,
      eventDetails: Object.entries(this.events).map(([event, listeners]) => ({
        event,
        listenerCount: listeners.length,
        listeners: listeners.map(l => ({
          id: l.id,
          component: l.component,
          priority: l.priority,
          once: l.once
        }))
      }))
    };
  }

  // Private methods
  _generateId() {
    return Math.random().toString(36).substr(2, 9);
  }

  _addToHistory(eventData) {
    this.eventHistory.push(eventData);

    // Keep history size manageable
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }
}

// Create global instance
export const eventBus = new EventBus();

// Common events with detailed categorization
export const EVENTS = {
  // Order events
  ORDER_UPDATED: 'order:updated',
  ORDER_STATUS_CHANGED: 'order:status-changed',
  ORDER_PAYMENT_STATUS_CHANGED: 'order:payment-status-changed',
  ORDER_DELETED: 'order:deleted',
  ORDER_CREATED: 'order:created',
  ORDER_ITEM_ADDED: 'order:item-added',
  ORDER_ITEM_UPDATED: 'order:item-updated',
  ORDER_ITEM_REMOVED: 'order:item-removed',
  ORDER_NOTE_ADDED: 'order:note-added',
  ORDER_REFUNDED: 'order:refunded',
  ORDER_CANCELLED: 'order:cancelled',

  // Product events
  PRODUCT_UPDATED: 'product:updated',
  PRODUCT_CREATED: 'product:created',
  PRODUCT_DELETED: 'product:deleted',
  PRODUCT_STOCK_CHANGED: 'product:stock-changed',

  // Category events
  CATEGORY_UPDATED: 'category:updated',
  CATEGORY_CREATED: 'category:created',
  CATEGORY_DELETED: 'category:deleted',

  // Company events
  COMPANY_UPDATED: 'company:updated',
  COMPANY_CREATED: 'company:created',
  COMPANY_DELETED: 'company:deleted',
  COMPANY_STATUS_CHANGED: 'company:status-changed',

  // User events
  USER_UPDATED: 'user:updated',
  USER_CREATED: 'user:created',
  USER_DELETED: 'user:deleted',
  USER_ROLE_CHANGED: 'user:role-changed',

  // System events
  CACHE_INVALIDATED: 'system:cache-invalidated',
  DATA_REFRESH_NEEDED: 'system:data-refresh-needed',
  ERROR_OCCURRED: 'system:error-occurred',
  NOTIFICATION_SHOWN: 'system:notification-shown'
};

// Event helper functions
export const createOrderEvent = (type, orderId, data = {}) => ({
  type,
  orderId,
  timestamp: Date.now(),
  ...data
});

export const createProductEvent = (type, productId, data = {}) => ({
  type,
  productId,
  timestamp: Date.now(),
  ...data
});

export const createCategoryEvent = (type, categoryId, data = {}) => ({
  type,
  categoryId,
  timestamp: Date.now(),
  ...data
});

export const createCompanyEvent = (type, companyId, data = {}) => ({
  type,
  companyId,
  timestamp: Date.now(),
  ...data
});

export const createUserEvent = (type, userId, data = {}) => ({
  type,
  userId,
  timestamp: Date.now(),
  ...data
});

export const createSystemEvent = (type, data = {}) => ({
  type,
  timestamp: Date.now(),
  ...data
});

// Persistent update flags using localStorage
export const updateFlags = {
  // Mark that orders need to be refreshed
  markOrdersForRefresh() {
    localStorage.setItem('orders_need_refresh', 'true');
    localStorage.setItem('orders_refresh_timestamp', Date.now().toString());
  },

  // Check if orders need to be refreshed
  shouldRefreshOrders() {
    return localStorage.getItem('orders_need_refresh') === 'true';
  },

  // Clear the refresh flag
  clearOrdersRefreshFlag() {
    localStorage.removeItem('orders_need_refresh');
    localStorage.removeItem('orders_refresh_timestamp');
  },

  // Get when the refresh was marked
  getRefreshTimestamp() {
    const timestamp = localStorage.getItem('orders_refresh_timestamp');
    return timestamp ? parseInt(timestamp) : null;
  }
};
