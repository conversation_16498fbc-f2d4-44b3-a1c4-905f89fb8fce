using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Review;

public class GetProductReviewsQueryHandler :
    PaginatedQueryHandler<GetProductReviewsQuery, Domain.Entities.Review, ReviewResponse>,
    IRequestHandler<GetProductReviewsQuery, PaginatedResponse<ReviewResponse>>
{
    private readonly IReviewRepository _repository;
    private readonly IProductRepository _productRepository;

    public GetProductReviewsQueryHandler(
        IReviewRepository repository,
        IProductRepository productRepository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _repository = repository;
        _productRepository = productRepository;
    }

    public async Task<PaginatedResponse<ReviewResponse>> Handle(GetProductReviewsQuery request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи існує товар з таким slug
        var product = await _productRepository.GetBySlugAsync(request.ProductSlug, cancellationToken);
        if (product == null)
        {
            return CreateEmptyPaginatedResponse(request, $"api/products/{request.ProductSlug}/reviews");
        }

        // Базовий фільтр - тільки відгуки для цього товару
        Expression<Func<Domain.Entities.Review, bool>> filter = r => r.ProductId == product.Id;

        // Додаємо додатковий фільтр, якщо він є
        if (!string.IsNullOrEmpty(request.Filter))
        {
            var searchFilter = request.Filter.ToLower();
            filter = r => r.ProductId == product.Id && 
                         (r.Comment != null && r.Comment.ToLower().Contains(searchFilter));
        }

        // Отримуємо пагіновані дані
        var pagedResult = await _repository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy ?? "CreatedAt",
            descending: request.OrderBy == null ? true : request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Review, object>>[] {
                r => r.Product,
                r => r.Rating,
                r => r.User
            }
        );

        // Створюємо пагіновану відповідь
        return CreatePaginatedResponse(request, pagedResult, $"api/products/{request.ProductSlug}/reviews");
    }
}
