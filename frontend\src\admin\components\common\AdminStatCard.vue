<template>
  <div class="admin-card admin-stat-card">
    <div class="admin-card-content" :class="color">
      <div class="level is-mobile">
        <div class="level-left">
          <div class="level-item">
            <div>
              <p class="stat-heading">{{ title }}</p>
              <p class="stat-value">{{ value }}</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <span class="stat-icon">
              <i :class="['fas', 'fa-' + icon, 'fa-2x']"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'is-primary'
  }
});
</script>

<style scoped>
.admin-stat-card {
  transition: transform var(--admin-transition-fast), box-shadow var(--admin-transition-fast);
}

.admin-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
}

.admin-card-content {
  border-radius: var(--admin-radius-lg);
}

.admin-card-content.is-primary {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: var(--admin-text-white);
}

.admin-card-content.is-info {
  background: linear-gradient(135deg, var(--admin-info), var(--admin-info-dark));
  color: var(--admin-text-white);
}

.admin-card-content.is-success {
  background: linear-gradient(135deg, var(--admin-success), var(--admin-success-dark));
  color: var(--admin-text-white);
}

.admin-card-content.is-warning {
  background: linear-gradient(135deg, var(--admin-warning), var(--admin-warning-dark));
  color: var(--admin-text-primary);
}

.admin-card-content.is-danger {
  background: linear-gradient(135deg, var(--admin-danger), var(--admin-danger-dark));
  color: var(--admin-text-white);
}

.admin-card-content.is-link {
  background: linear-gradient(135deg, var(--admin-link), var(--admin-link-hover));
  color: var(--admin-text-white);
}

.stat-heading {
  font-size: var(--admin-font-size-base);
  font-weight: var(--admin-font-weight-semibold);
  margin-bottom: var(--admin-spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.9;
}

.stat-value {
  font-size: var(--admin-font-size-2xl);
  font-weight: var(--admin-font-weight-bold);
  line-height: var(--admin-line-height-tight);
  margin: 0;
}

.stat-icon {
  opacity: 0.8;
  font-size: 2rem;
}
</style>
