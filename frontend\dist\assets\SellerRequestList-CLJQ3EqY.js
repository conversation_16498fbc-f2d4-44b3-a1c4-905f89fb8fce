import{_ as me,g as u,C as ve,i as pe,j as fe,c as v,a as e,b as C,l as ge,x,D as Z,z as ee,y as be,F as se,p as ye,t as m,n as D,k as q,w as ae,r as _e,d as f,o as p}from"./index-L-hJxM_5.js";import{s as I}from"./seller-requests-CWXKDmna.js";/* empty css                                                                    */import{P as he}from"./Pagination-DX2plTiq.js";import{C as Re}from"./ConfirmDialog-hd0r6dWx.js";const ke={class:"admin-page"},we={class:"admin-card"},Se={class:"admin-card-content"},Ce={class:"admin-form-grid admin-form-grid-2"},Pe={class:"admin-form-field"},je={class:"admin-form-control"},Ae={class:"admin-form-field"},xe={class:"admin-form-control"},De={class:"admin-card"},qe={class:"admin-card-content"},Ie={key:0,class:"admin-loading-state"},Ne={key:1,class:"admin-empty-state"},Ee={key:2},Fe={class:"admin-seller-requests-grid"},Ve={class:"admin-seller-request-content"},Te={class:"admin-seller-request-header"},Ue={class:"admin-seller-request-avatar"},Le=["src","alt"],Me={class:"admin-seller-request-info"},ze={class:"admin-seller-request-title"},$e={class:"admin-seller-request-email"},Be=["href"],Oe={class:"admin-seller-request-meta"},Ke={class:"admin-seller-request-date"},Qe={class:"admin-seller-request-details"},Ge={class:"admin-info-group"},He={class:"admin-info-value"},Je={class:"admin-info-group"},We={class:"admin-info-value"},Xe={class:"admin-info-group"},Ye={class:"admin-info-value"},Ze={class:"admin-info-group"},es={class:"admin-info-value"},ss={key:0,class:"admin-info-group"},as={class:"admin-info-value"},ts={class:"admin-seller-request-actions"},ns={key:0,class:"admin-seller-request-status-actions"},ls=["onClick","disabled"],os=["onClick","disabled"],is={key:1,class:"admin-seller-request-user-link"},ds={key:2,class:"field mt-4"},rs={class:"has-text-danger"},cs={key:0,class:"mt-2"},us={class:"admin-modal-content"},ms={class:"admin-modal-body"},vs={class:"admin-text-secondary mb-4"},ps={class:"admin-form-field"},fs={class:"admin-form-control"},gs={class:"admin-modal-footer"},bs=["disabled"],ys={__name:"SellerRequestList",setup(_s){const te=(a,s)=>{let r;return function(...c){const n=()=>{clearTimeout(r),a(...c)};clearTimeout(r),r=setTimeout(n,s)}},l=u([]),P=u(!1),y=u(""),b=u(1),j=u(1),N=u(0),E=u(6),_=u(!1),h=u(!1),d=u(null),g=u(""),S=u(!1),R=ve({status:""}),A=async(a=1)=>{var s;P.value=!0,b.value=a;try{const r={page:b.value,pageSize:E.value,filter:y.value,status:R.status};console.log("🔍 Fetching seller requests with params:",r),console.log("🔍 Search query value:",y.value),console.log("🔍 Filter status value:",R.status);const o=await I.getSellerRequests(r);console.log("📥 Raw API response:",o),console.log("📥 Response success:",o==null?void 0:o.success),console.log("📥 Response data keys:",o!=null&&o.data?Object.keys(o.data):"no data");let c=[],n={};if(o&&o.success&&o.data){const i=o.data;console.log("📊 Data structure:",i),console.log("📊 Data keys:",Object.keys(i)),i.data&&Array.isArray(i.data)?(c=i.data,n=i,console.log("📋 Found data in data.data:",c.length,"items")):Array.isArray(i)?(c=i,console.log("📋 Found data directly in data:",c.length,"items")):console.warn("📋 Unexpected data structure:",i)}console.log("📋 Final requests data:",c),console.log("📊 Final pagination data:",n),l.value=c.map(i=>({...i,processing:!1})),j.value=n.lastPage||n.totalPages||Math.ceil((n.total||c.length)/(n.pageSize||E.value)),N.value=n.total||n.totalItems||c.length,b.value=n.currentPage||1,console.log("📊 Pagination details:",{lastPage:n.lastPage,totalPages:n.totalPages,total:n.total,totalItems:n.totalItems,currentPage:n.currentPage,pageSize:n.pageSize}),console.log("✅ Final state:",{requestsCount:l.value.length,totalPages:j.value,totalItems:N.value,currentPage:b.value})}catch(r){console.error("❌ Error fetching seller requests:",r),console.error("❌ Error details:",((s=r.response)==null?void 0:s.data)||r.message)}finally{P.value=!1}},F=()=>{console.log("🔍 Search triggered with query:",y.value),console.log("🔍 Search triggered with status filter:",R.status),b.value=1,A()},V=te(F,300),ne=a=>{A(a)},T=a=>a?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(a)):"",k=a=>{if(typeof a=="string")return a.toLowerCase();switch(a){case 0:return"pending";case 1:return"approved";case 2:return"rejected";default:return"pending"}},le=a=>{switch(k(a)){case"pending":return"admin-badge-warning";case"approved":return"admin-badge-success";case"rejected":return"admin-badge-danger";default:return"admin-badge-secondary"}},oe=a=>{a.target.src="https://via.placeholder.com/64?text=No+Image"},ie=a=>{d.value=a,_.value=!0},de=a=>{d.value=a,g.value="",h.value=!0},w=()=>{console.log("cancelProcess called"),_.value=!1,h.value=!1,d.value=null,g.value="",console.log("Modals closed")},U=a=>{a.key==="Escape"&&(h.value||_.value)&&w()},re=async()=>{if(!d.value)return;const a=l.value.findIndex(s=>s.id===d.value.id);a!==-1&&(l.value[a].processing=!0);try{await I.approveSellerRequest(d.value.id),a!==-1&&(l.value[a].status=1,l.value[a].updatedAt=new Date,l.value[a].processing=!1),_.value=!1,d.value=null}catch(s){console.error("Error approving seller request:",s),a!==-1&&(l.value[a].processing=!1)}},ce=async()=>{if(!d.value)return;S.value=!0;const a=l.value.findIndex(s=>s.id===d.value.id);a!==-1&&(l.value[a].processing=!0);try{await I.rejectSellerRequest(d.value.id,g.value),a!==-1&&(l.value[a].status=2,l.value[a].updatedAt=new Date,l.value[a].rejectionReason=g.value,l.value[a].processing=!1),h.value=!1,d.value=null,g.value=""}catch(s){console.error("Error rejecting seller request:",s),a!==-1&&(l.value[a].processing=!1)}finally{S.value=!1}};return pe(()=>{A(),document.addEventListener("keydown",U)}),fe(()=>{document.removeEventListener("keydown",U)}),(a,s)=>{var o,c,n,i;const r=_e("router-link");return p(),v(se,null,[e("div",ke,[s[20]||(s[20]=ge('<div class="admin-page-header" data-v-2eed987b><div class="admin-page-title-section" data-v-2eed987b><h1 class="admin-page-title" data-v-2eed987b><i class="fas fa-store admin-page-icon" data-v-2eed987b></i> Seller Requests </h1><p class="admin-page-subtitle" data-v-2eed987b>Review and manage seller application requests</p></div></div>',1)),e("div",we,[e("div",Se,[e("div",Ce,[e("div",Pe,[s[4]||(s[4]=e("label",{class:"admin-form-label"},"Search",-1)),e("div",je,[x(e("input",{class:"admin-form-input",type:"text",placeholder:"Search by username, email, or company info...","onUpdate:modelValue":s[0]||(s[0]=t=>y.value=t),onInput:s[1]||(s[1]=(...t)=>ee(V)&&ee(V)(...t))},null,544),[[Z,y.value]])])]),e("div",Ae,[s[6]||(s[6]=e("label",{class:"admin-form-label"},"Status",-1)),e("div",xe,[x(e("select",{class:"admin-form-select","onUpdate:modelValue":s[2]||(s[2]=t=>R.status=t),onChange:F},s[5]||(s[5]=[e("option",{value:""},"All Statuses",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"approved"},"Approved",-1),e("option",{value:"rejected"},"Rejected",-1)]),544),[[be,R.status]])])])])])]),e("div",De,[e("div",qe,[P.value&&!l.value.length?(p(),v("div",Ie,s[7]||(s[7]=[e("div",{class:"admin-spinner"},[e("i",{class:"fas fa-spinner fa-pulse"})],-1),e("p",{class:"admin-loading-text"},"Loading seller requests...",-1)]))):l.value.length?(p(),v("div",Ee,[e("div",Fe,[(p(!0),v(se,null,ye(l.value,t=>{var L,M,z,$,B,O,K,Q,G,H,J,W,X,Y;return p(),v("div",{key:t.id,class:"admin-seller-request-card"},[e("div",Ve,[e("div",Te,[e("div",Ue,[e("img",{src:((L=t.user)==null?void 0:L.avatar)||"https://via.placeholder.com/64",alt:(((M=t.user)==null?void 0:M.firstName)||"")+" "+(((z=t.user)==null?void 0:z.lastName)||""),onError:oe},null,40,Le)]),e("div",Me,[e("h3",ze,m((($=t.user)==null?void 0:$.username)||"Unknown User"),1),e("p",$e,[e("a",{href:`mailto:${((O=(B=t.user)==null?void 0:B.email)==null?void 0:O.value)||((K=t.user)==null?void 0:K.email)}`},m(((G=(Q=t.user)==null?void 0:Q.email)==null?void 0:G.value)||((H=t.user)==null?void 0:H.email)),9,Be)]),e("div",Oe,[e("span",{class:D(["admin-badge",le(t.status)])},m(k(t.status)),3),e("span",Ke,m(T(t.createdAt)),1)])])]),e("div",Qe,[e("div",Ge,[s[9]||(s[9]=e("label",{class:"admin-info-label"},"Company Name",-1)),e("p",He,m(((J=t.companyRequestData)==null?void 0:J.name)||"N/A"),1)]),e("div",Je,[s[10]||(s[10]=e("label",{class:"admin-info-label"},"Company Description",-1)),e("p",We,m(((W=t.companyRequestData)==null?void 0:W.description)||"N/A"),1)]),e("div",Xe,[s[11]||(s[11]=e("label",{class:"admin-info-label"},"Contact Email",-1)),e("p",Ye,m(((X=t.companyRequestData)==null?void 0:X.contactEmail)||"N/A"),1)]),e("div",Ze,[s[12]||(s[12]=e("label",{class:"admin-info-label"},"Contact Phone",-1)),e("p",es,m(((Y=t.companyRequestData)==null?void 0:Y.contactPhone)||"N/A"),1)]),t.additionalInformation?(p(),v("div",ss,[s[13]||(s[13]=e("label",{class:"admin-info-label"},"Additional Information",-1)),e("p",as,m(t.additionalInformation),1)])):q("",!0)]),e("div",ts,[C(r,{to:{name:"AdminSellerRequestDetail",params:{id:t.id}},class:"admin-btn admin-btn-secondary admin-btn-sm"},{default:ae(()=>s[14]||(s[14]=[e("i",{class:"fas fa-eye"},null,-1),e("span",null,"View Details",-1)])),_:2},1032,["to"]),k(t.status)==="pending"?(p(),v("div",ns,[e("button",{class:"admin-btn admin-btn-success admin-btn-sm",onClick:ue=>ie(t),disabled:t.processing},s[15]||(s[15]=[e("i",{class:"fas fa-check"},null,-1),f(" Approve ")]),8,ls),e("button",{class:"admin-btn admin-btn-danger admin-btn-sm",onClick:ue=>de(t),disabled:t.processing},s[16]||(s[16]=[e("i",{class:"fas fa-times"},null,-1),f(" Reject ")]),8,os)])):k(t.status)==="approved"?(p(),v("div",is,[C(r,{to:`/admin/users/${t.userId}`,class:"admin-btn admin-btn-primary admin-btn-sm"},{default:ae(()=>s[17]||(s[17]=[e("i",{class:"fas fa-user"},null,-1),f(" View Seller Profile ")])),_:2},1032,["to"])])):k(t.status)==="rejected"?(p(),v("div",ds,[e("p",rs,[s[18]||(s[18]=e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})],-1)),e("span",null,"Rejected on "+m(T(t.updatedAt)),1)]),t.rejectionReason?(p(),v("p",cs,[s[19]||(s[19]=e("strong",null,"Reason:",-1)),f(" "+m(t.rejectionReason),1)])):q("",!0)])):q("",!0)])])])}),128))])])):(p(),v("div",Ne,s[8]||(s[8]=[e("div",{class:"admin-empty-icon"},[e("i",{class:"fas fa-store"})],-1),e("h3",{class:"admin-empty-title"},"No seller requests found",-1),e("p",{class:"admin-empty-text"},"There are currently no seller requests to review",-1)]))),C(he,{"current-page":b.value,"total-pages":j.value,onPageChanged:ne},null,8,["current-page","total-pages"])])])]),C(Re,{"is-open":_.value,title:"Approve Seller Request",message:`Are you sure you want to approve ${(c=(o=d.value)==null?void 0:o.user)==null?void 0:c.username}'s seller request?`,"confirm-text":"Approve","cancel-text":"Cancel","confirm-button-class":"is-success",onConfirm:re,onCancel:w},null,8,["is-open","message"]),e("div",{class:D(["admin-modal",{"admin-modal-active":h.value}])},[e("div",{class:"admin-modal-backdrop",onClick:w}),e("div",us,[e("div",{class:"admin-modal-header"},[s[22]||(s[22]=e("h3",{class:"admin-modal-title"},[e("i",{class:"fas fa-times-circle"}),f(" Reject Seller Request ")],-1)),e("button",{class:"admin-modal-close",onClick:w},s[21]||(s[21]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",ms,[e("p",vs,[s[23]||(s[23]=f(" Are you sure you want to reject ")),e("strong",null,m((i=(n=d.value)==null?void 0:n.user)==null?void 0:i.username),1),s[24]||(s[24]=f("'s seller request? "))]),e("div",ps,[s[25]||(s[25]=e("label",{class:"admin-form-label"},"Reason for Rejection (Optional)",-1)),e("div",fs,[x(e("textarea",{class:"admin-form-textarea","onUpdate:modelValue":s[3]||(s[3]=t=>g.value=t),placeholder:"Provide a reason for rejection",rows:"4"},"              ",512),[[Z,g.value]])])])]),e("div",gs,[e("button",{class:"admin-btn admin-btn-danger",onClick:ce,disabled:S.value},[e("i",{class:D(["fas fa-times",{"fa-spinner fa-pulse":S.value}])},null,2),s[26]||(s[26]=f(" Reject "))],8,bs),e("button",{class:"admin-btn admin-btn-secondary",onClick:w},s[27]||(s[27]=[e("i",{class:"fas fa-ban"},null,-1),f(" Cancel ")]))])])],2)],64)}}},Cs=me(ys,[["__scopeId","data-v-2eed987b"]]);export{Cs as default};
