import { ref, reactive, computed } from 'vue';
import productService from '@/services/product.service';

export function useProducts() {
  const products = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const totalItems = ref(0);
  const currentPage = ref(1);

  const state = reactive({
    filters: {
      categorySlug: null,
      search: '',
      minPrice: null,
      maxPrice: null,
      inStock: null,
      sortBy: 'name',
      sortOrder: 'asc'
    },
    pagination: {
      page: 1,
      pageSize: 12,
      total: 0
    }
  });

  // Computed properties
  const totalPages = computed(() => {
    return Math.ceil(totalItems.value / state.pagination.pageSize);
  });

  const hasProducts = computed(() => {
    return products.value && products.value.length > 0;
  });

  const isEmpty = computed(() => {
    return !loading.value && !hasProducts.value;
  });

  // Methods
  const fetchProducts = async (options = {}) => {
    try {
      loading.value = true;
      error.value = null;

      const params = {
        page: options.page || state.pagination.page,
        pageSize: options.pageSize || state.pagination.pageSize,
        ...state.filters,
        ...options.filters
      };

      // Remove null/undefined values
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === undefined || params[key] === '') {
          delete params[key];
        }
      });

      const response = await productService.getAll(params);
      
      if (response.data) {
        products.value = response.data.data || response.data;
        totalItems.value = response.data.total || response.data.length || 0;
        
        // Update pagination state
        state.pagination.page = params.page;
        state.pagination.total = totalItems.value;
      }

    } catch (err) {
      error.value = err.message || 'Помилка завантаження товарів';
      console.error('Error fetching products:', err);
    } finally {
      loading.value = false;
    }
  };

  const fetchProductsByCategory = async (categorySlug, options = {}) => {
    try {
      loading.value = true;
      error.value = null;

      const params = {
        page: options.page || 1,
        pageSize: options.pageSize || 12,
        ...options.filters
      };

      const response = await productService.getByCategory(categorySlug, params);
      
      if (response.data) {
        products.value = response.data.data || response.data;
        totalItems.value = response.data.total || response.data.length || 0;
        
        // Update state
        state.filters.categorySlug = categorySlug;
        state.pagination.page = params.page;
        state.pagination.pageSize = params.pageSize;
        state.pagination.total = totalItems.value;
      }

    } catch (err) {
      error.value = err.message || 'Помилка завантаження товарів категорії';
      console.error('Error fetching products by category:', err);
    } finally {
      loading.value = false;
    }
  };

  const fetchTopProducts = async (limit = 8) => {
    try {
      loading.value = true;
      error.value = null;

      const response = await productService.getTopProducts(limit);
      
      if (response.data) {
        products.value = response.data.data || response.data;
        totalItems.value = products.value.length;
      }

    } catch (err) {
      error.value = err.message || 'Помилка завантаження топ товарів';
      console.error('Error fetching top products:', err);
    } finally {
      loading.value = false;
    }
  };

  const fetchRecommendedProducts = async (limit = 8) => {
    try {
      loading.value = true;
      error.value = null;

      const response = await productService.getRecommended(limit);
      
      if (response.data) {
        products.value = response.data.data || response.data;
        totalItems.value = products.value.length;
      }

    } catch (err) {
      error.value = err.message || 'Помилка завантаження рекомендованих товарів';
      console.error('Error fetching recommended products:', err);
    } finally {
      loading.value = false;
    }
  };

  const searchProducts = async (query, options = {}) => {
    try {
      loading.value = true;
      error.value = null;

      const params = {
        search: query,
        page: options.page || 1,
        pageSize: options.pageSize || 12,
        ...options.filters
      };

      const response = await productService.search(params);
      
      if (response.data) {
        products.value = response.data.data || response.data;
        totalItems.value = response.data.total || response.data.length || 0;
        
        // Update state
        state.filters.search = query;
        state.pagination.page = params.page;
        state.pagination.pageSize = params.pageSize;
        state.pagination.total = totalItems.value;
      }

    } catch (err) {
      error.value = err.message || 'Помилка пошуку товарів';
      console.error('Error searching products:', err);
    } finally {
      loading.value = false;
    }
  };

  const setPage = (page) => {
    currentPage.value = page;
    state.pagination.page = page;
  };

  const setPageSize = (pageSize) => {
    state.pagination.pageSize = pageSize;
    state.pagination.page = 1; // Reset to first page
    currentPage.value = 1;
  };

  const setFilters = (filters) => {
    Object.assign(state.filters, filters);
    state.pagination.page = 1; // Reset to first page when filters change
    currentPage.value = 1;
  };

  const clearFilters = () => {
    state.filters = {
      categorySlug: null,
      search: '',
      minPrice: null,
      maxPrice: null,
      inStock: null,
      sortBy: 'name',
      sortOrder: 'asc'
    };
    state.pagination.page = 1;
    currentPage.value = 1;
  };

  const refresh = () => {
    if (state.filters.categorySlug) {
      fetchProductsByCategory(state.filters.categorySlug, {
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        filters: state.filters
      });
    } else if (state.filters.search) {
      searchProducts(state.filters.search, {
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        filters: state.filters
      });
    } else {
      fetchProducts({
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        filters: state.filters
      });
    }
  };

  return {
    // State
    products,
    loading,
    error,
    totalItems,
    currentPage,
    state,
    
    // Computed
    totalPages,
    hasProducts,
    isEmpty,
    
    // Methods
    fetchProducts,
    fetchProductsByCategory,
    fetchTopProducts,
    fetchRecommendedProducts,
    searchProducts,
    setPage,
    setPageSize,
    setFilters,
    clearFilters,
    refresh
  };
}
