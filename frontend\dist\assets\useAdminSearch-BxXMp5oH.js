import{g as i,C as re,h as M,H as B}from"./index-L-hJxM_5.js";function ge(x={}){const{fetchFunction:J,defaultFilters:C={},debounceTime:V=300,defaultPageSize:_=15,clientSideSearch:b=!1,enableVirtualScrolling:le=!1,virtualScrollThreshold:ne=100,enableInfiniteScroll:H=!1,enableOptimisticUpdates:Q=!1}=x,y=i([]),A=i(!1),m=i(null),P=i(!0),D=i(0),$=i(0),g=i(1),f=i(1),p=i(0),I=i(_),S=i(!1),O=i(!1),w=i(new Map),F=i(new Set),n=re({search:"",...C}),T=i("createdAt"),R=i("desc"),Y=M(()=>Object.values(n).some(a=>a!==""&&a!==null&&a!==void 0)),v=M(()=>Object.entries(n).filter(([a,c])=>c!==""&&c!==null&&c!==void 0).reduce((a,[c,u])=>(a[c]=u,a),{})),d=async(a=1,c={})=>{var q,z,L;const{append:u=!1,force:h=!1,silent:E=!1}=c,te=++$.value,U=`${a}-${JSON.stringify(v.value)}-${T.value}-${R.value}`;if(F.value.has(U)&&!h){console.log("🔄 Duplicate request prevented:",U);return}`${a}${JSON.stringify(v.value)}${T.value}${R.value}`;const se=Date.now();if(!h&&D.value&&se-D.value<3e4){console.log("📦 Using cached data");return}F.value.add(U),E||(u?S.value=!0:(P.value||g.value!==a)&&(A.value=!0)),u||(g.value=a),m.value!==void 0&&(m.value=null);try{const t={page:g.value,pageSize:I.value};n.search&&n.search.trim()&&(t.filter=n.search.trim(),t.search=n.search.trim()),b?Object.entries(v.value).forEach(([r,s])=>{r!=="search"&&(r==="sortBy"?t.sortBy=s:r==="sortOrder"?t.sortOrder=s:r==="status"?(console.log("🔄 Converting status filter (client-side):",s),t.status=s):r==="categoryId"?t.categoryId=s:r==="categoryIds"?Array.isArray(s)&&s.length>0&&(t.categoryIds=s.join(",")):r==="stock"?t.stock=s:t[r]=s)}):Object.entries(v.value).forEach(([r,s])=>{r!=="search"&&(r==="sortBy"?t.sortBy=s:r==="sortOrder"?t.sortOrder=s:r==="status"?(console.log("🔄 Converting status filter:",s),t.status=s):r==="categoryId"?t.categoryId=s:r==="categoryIds"?Array.isArray(s)&&s.length>0&&(t.categoryIds=s.join(",")):r==="stock"?t.stock=s:t[r]=s)}),console.log("🔍 useAdminSearch: Fetching data with params:",t);const e=await J(t);if(te!==$.value&&!P.value){console.log("🚫 Request outdated, ignoring response");return}let l=[],o={};if(console.log("Processing response:",e),e&&(e.data&&Array.isArray(e.data)&&e.pagination?(console.log("Using updated service format with pagination"),l=e.data,o={total:e.pagination.total||e.total,page:e.pagination.page||e.currentPage,totalPages:e.pagination.totalPages||e.totalPages,perPage:e.pagination.pageSize||e.pagination.limit}):e.data&&Array.isArray(e.data)&&(e.total!==void 0||e.totalPages!==void 0)?(console.log("Using data array with separate pagination"),l=e.data,o={total:e.total||e.totalItems,page:e.currentPage||e.page,totalPages:e.totalPages||e.lastPage,perPage:e.pageSize||e.perPage}):e.categories&&Array.isArray(e.categories)?(console.log("Using categories service format"),l=e.categories,o={total:e.totalCount||e.total,page:1,totalPages:1,perPage:l.length}):e.items&&Array.isArray(e.items)?(console.log("Using items array format"),l=e.items,o={total:e.total||e.totalItems||l.length,page:e.currentPage||e.page||1,totalPages:e.totalPages||e.lastPage||1,perPage:e.pageSize||e.perPage||l.length}):e.success&&e.data&&e.data.data?(console.log("Using ApiResponse<PaginatedResponse> format"),l=e.data.data,o={total:e.data.total,page:e.data.currentPage,totalPages:e.data.lastPage,perPage:e.data.perPage}):e.users?(console.log("Using Users service format"),l=e.users,o=e.pagination||{}):Array.isArray(e)?(console.log("Using direct array format"),l=e,o={total:e.length,page:1,totalPages:1,perPage:e.length}):e.data?(console.log("Using legacy data format"),Array.isArray(e.data)?(l=e.data,o=e.pagination||{total:e.data.length,page:1,totalPages:1,perPage:e.data.length}):e.data.data&&(l=e.data.data,o=e.data)):(console.warn("Unknown response format:",e),l=[],o={})),b&&n.search){const r=n.search.toLowerCase();l=l.filter(s=>K(s,r))}if(y.value=l,b&&n.search)p.value=l.length,f.value=Math.ceil(p.value/I.value)||1;else{const r=o.total||o.totalItems||o.Total||l.length;p.value=r;const s=o.perPage||o.pageSize||I.value,oe=Math.ceil(r/s);f.value=o.totalPages||o.lastPage||o.LastPage||oe||1,g.value=o.page||o.currentPage||o.CurrentPage||a,console.log("Pagination updated:",{totalItems:p.value,totalPages:f.value,currentPage:g.value,itemsPerPage:s,paginationData:o})}console.log("Data fetched successfully:",{itemsCount:y.value.length,totalItems:p.value,totalPages:f.value,currentPage:g.value})}catch(t){if(t.name==="CanceledError"||t.code==="ERR_CANCELED"){console.log("🚫 Request was canceled, ignoring error");return}console.error("Error fetching data:",t);let e="Failed to load data";t.code==="ECONNREFUSED"||t.message.includes("ECONNREFUSED")?e="Cannot connect to server. Please ensure the backend is running.":((q=t.response)==null?void 0:q.status)===401?e="Authentication required. Please log in.":((z=t.response)==null?void 0:z.status)===403?e="Access denied. You do not have permission to view this data.":((L=t.response)==null?void 0:L.status)===404?e="API endpoint not found.":t.message&&(e=t.message),m.value!==void 0&&(m.value=e),y.value=[],f.value=1,p.value=0}finally{A.value=!1,S.value=!1,F.value.delete(U),P.value&&(P.value=!1)}},G=(a,c)=>{Q&&(w.value.set(a,c),setTimeout(()=>{w.value.delete(a)},3e4))},W=a=>{w.value.delete(a)},X=async()=>{if(!H||S.value||O.value)return;const a=g.value+1;if(a>f.value){O.value=!0;return}await d(a,{append:!0})},Z=async()=>{w.value.clear(),O.value=!1,await d(1,{force:!0})},K=(a,c)=>["name","username","email","title","description","contactEmail","contactPhone","slug","id","customerName","customerEmail","customerPhone","orderId","orderNumber","customerId"].some(h=>{const E=k(a,h);return E&&E.toString().toLowerCase().includes(c)}),k=(a,c)=>c.split(".").reduce((u,h)=>u&&u[h]!==void 0?u[h]:null,a),ee=()=>{Object.keys(n).forEach(a=>{a==="search"?n[a]="":n[a]=C[a]||""}),g.value=1,d(1)},ae=a=>{d(a)};let N=null;const j=()=>{N&&clearTimeout(N),N=setTimeout(()=>{g.value=1,d(1)},V)};return B(()=>n.search,()=>{console.log("Search changed:",n.search),j()}),Object.keys(C).forEach(a=>{a!=="search"&&B(()=>n[a],(c,u)=>{c!==u&&(console.log(`Filter ${a} changed:`,n[a]),g.value=1,d(1))})}),setTimeout(()=>{A.value||d(1)},100),{items:y,loading:A,error:m,isFirstLoad:P,isLoadingMore:S,allItemsLoaded:O,currentPage:g,totalPages:f,totalItems:p,pageSize:I,filters:n,hasFilters:Y,activeFilters:v,sortBy:T,sortOrder:R,fetchData:d,resetFilters:ee,handlePageChange:ae,debouncedSearch:j,loadMore:X,forceRefresh:Z,addOptimisticUpdate:G,removeOptimisticUpdate:W}}export{ge as u};
