<template>
  <div class="admin-users">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-users admin-page-icon"></i>
          Users Management
        </h1>
        <p class="admin-page-subtitle">Manage user accounts, roles, and permissions</p>
      </div>
      <div class="admin-page-actions">
        <router-link
          to="/admin/users/create"
          class="admin-btn admin-btn-primary">
          <i class="fas fa-plus"></i>
          Add User
        </router-link>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-user-filters">
      <search-and-filters
        :search="filters.search"
        :filters="filters"
        :filter-fields="filterOptions"
        :loading="loading"
        @search-changed="handleSearchChange"
        @filter-changed="handleFilterChange"
        @clear-filters="clearFilters"
        @apply-filters="applyFilters">

        <!-- Custom filter slots -->
        <template #custom-filters>
          <div class="admin-filter-group">
            <label class="admin-filter-label">Role</label>
            <select v-model="filters.role" class="admin-filter-select">
              <option value="">All Roles</option>
              <option :value="ROLE_KEYS.ADMIN">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] }}</option>
              <option :value="ROLE_KEYS.MODERATOR">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] }}</option>
              <option :value="ROLE_KEYS.SELLER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] }}</option>
              <option :value="ROLE_KEYS.SELLER_OWNER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] }}</option>
              <option :value="ROLE_KEYS.BUYER">{{ ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }}</option>
            </select>
          </div>
        </template>
      </search-and-filters>
    </div>

    <!-- Loading State -->
    <div v-if="loading && isFirstLoad" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading users...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-error-state">
      <div class="admin-error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="admin-error-title">Error Loading Users</h3>
      <p class="admin-error-message">{{ error }}</p>
      <button @click="fetchData" class="admin-btn admin-btn-primary">
        <i class="fas fa-refresh"></i>
        Try Again
      </button>
    </div>

    <!-- Empty State -->
    <div v-else-if="!users.length" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-users"></i>
      </div>
      <h3 class="admin-empty-title">No Users Found</h3>
      <p class="admin-empty-message">
        {{ filters.search || filters.role ? 'No users match your current filters.' : 'No users have been created yet.' }}
      </p>
      <router-link v-if="!filters.search && !filters.role" to="/admin/users/create" class="admin-btn admin-btn-primary">
        <i class="fas fa-plus"></i>
        Add First User
      </router-link>
    </div>

    <!-- Users Table -->
    <div v-else class="admin-user-table">
      <div class="admin-card-header">
        <h3 class="admin-card-title">
          <i class="fas fa-table"></i>
          Users List
        </h3>
        <div class="admin-card-actions">
          <span class="admin-results-count">{{ totalItems }} users found</span>
        </div>
      </div>
      <div class="admin-card-content">

        <div class="admin-table-container">
          <table class="admin-table">
            <thead class="admin-table-header">
              <tr>
                <th class="admin-table-th">Avatar</th>
                <th class="admin-table-th">Username</th>
                <th class="admin-table-th">Email</th>
                <th class="admin-table-th">Role</th>
                <th class="admin-table-th">Status</th>
                <th class="admin-table-th">Registered</th>
                <th class="admin-table-th admin-table-th-actions">Actions</th>
              </tr>
            </thead>
            <tbody class="admin-table-body">
              <tr v-for="user in users" :key="user.id" class="admin-table-row">
                <td class="admin-table-td">
                  <div class="admin-user-avatar-cell">
                    <div
                      class="admin-user-avatar"
                      :class="{ 'has-image': user.avatarUrl }"
                      @click="openAvatarViewer(user)"
                    >
                      <img
                        v-if="user.avatarUrl"
                        :src="user.avatarUrl"
                        :alt="`${user.username} avatar`"
                        class="avatar-image"
                        @error="handleAvatarError"
                      />
                      <i v-else class="fas fa-user avatar-placeholder"></i>
                    </div>
                  </div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-user-info">
                    <div class="admin-user-details">
                      <div class="admin-user-name">{{ user.username }}</div>
                      <div class="admin-user-id">ID: {{ user.id.substring(0, 8) }}...</div>
                    </div>
                  </div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-email">{{ user.email }}</div>
                </td>
                <td class="admin-table-td">
                  <span class="admin-badge" :class="getRoleClass(user.role)">
                    {{ getRoleDisplayName(user.role) }}
                  </span>
                </td>
                <td class="admin-table-td">
                  <div class="admin-status-badges">
                    <span class="admin-badge admin-badge-xs" :class="user.emailConfirmed ? 'admin-badge-success' : 'admin-badge-warning'">
                      {{ user.emailConfirmed ? 'Email ✓' : 'Email ✗' }}
                    </span>
                    <span class="admin-badge admin-badge-xs" :class="user.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
                      {{ user.isApproved ? 'Approved ✓' : 'Pending ✗' }}
                    </span>
                  </div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-date">{{ formatDate(user.createdAt || user.emailConfirmedAt) }}</div>
                </td>
                <td class="admin-table-td admin-table-td-actions">
                  <div class="admin-table-actions">
                    <router-link
                      :to="`/admin/users/${user.id}`"
                      class="admin-btn admin-btn-sm admin-btn-secondary"
                      title="View Details">
                      <i class="fas fa-eye"></i>
                    </router-link>
                    <router-link
                      v-if="canEditUser(user)"
                      :to="`/admin/users/${user.id}/edit`"
                      class="admin-btn admin-btn-sm admin-btn-primary"
                      title="Edit User">
                      <i class="fas fa-edit"></i>
                    </router-link>

                    <button
                      v-if="canDeleteUser(user)"
                      class="admin-btn admin-btn-sm admin-btn-danger"
                      @click="confirmDelete(user)"
                      title="Delete User">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="handlePageChange" />
    </div>


    <!-- Avatar Viewer Modal -->
    <div
      v-if="showAvatarModal"
      class="modal fade show d-block"
      @click="closeAvatarViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-user me-2"></i>
              {{ selectedUser?.username }} - Avatar
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeAvatarViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <div v-if="selectedUser?.avatarUrl" class="avatar-viewer">
              <img
                :src="selectedUser.avatarUrl"
                :alt="`${selectedUser.username} avatar`"
                class="img-fluid rounded"
                style="max-height: 400px;"
              />
              <div class="mt-3">
                <small class="text-muted">
                  User: {{ selectedUser.username }} ({{ selectedUser.email }})
                </small>
              </div>
            </div>
            <div v-else class="no-avatar-message">
              <i class="fas fa-user-circle fa-5x text-muted mb-3"></i>
              <p class="text-muted">This user doesn't have an avatar</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete User"
      :message="`Are you sure you want to delete ${userToDelete?.username}? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteUser"
      @cancel="cancelDelete" />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useStore } from 'vuex';
import { usersService } from '@/admin/services/users';
import { useAdminSearch } from '@/composables/useAdminSearch';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import {
  getRoleKey,
  getRoleDisplayName,
  getRoleClass,
  compareRoles,
  ROLE_KEYS,
  ROLE_DISPLAY_NAMES
} from '@/admin/services/roles';

// Store
const store = useStore();

// Use the admin search composable
const {
  items: users,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: usersService.getUsers,
  defaultFilters: {
    role: ''
  },
  debounceTime: 300,
  defaultPageSize: 10,
  clientSideSearch: false
});

// useAdminSearch ініціалізовано

// Delete state
const showDeleteModal = ref(false);
const userToDelete = ref(null);

// Avatar viewer state
const showAvatarModal = ref(false);
const selectedUser = ref(null);

// Filter options for SearchAndFilters component
const filterOptions = computed(() => [
  {
    key: 'role',
    label: 'Role',
    type: 'select',
    options: [
      { value: '', label: 'All Roles' },
      { value: ROLE_KEYS.ADMIN, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.ADMIN] },
      { value: ROLE_KEYS.MODERATOR, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.MODERATOR] },
      { value: ROLE_KEYS.SELLER_OWNER, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER_OWNER] },
      { value: ROLE_KEYS.SELLER, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.SELLER] },
      { value: ROLE_KEYS.BUYER, label: ROLE_DISPLAY_NAMES[ROLE_KEYS.BUYER] }
    ]
  }
]);

// Active filters for display
const activeFilters = computed(() => {
  const active = [];
  if (filters.search) {
    active.push({ key: 'search', label: 'Search', value: filters.search });
  }
  if (filters.role) {
    active.push({ key: 'role', label: 'Role', value: getRoleDisplayName(filters.role) });
  }

  return active;
});

// Computed properties
const isAdmin = computed(() => store.getters['auth/isAdmin']);
const isModerator = computed(() => store.getters['auth/isModerator']);

// Функція для перевірки, чи може користувач видалити іншого користувача
const canDeleteUser = (user) => {
  // Адміністратори можуть видаляти всіх
  if (isAdmin.value) return true;

  // Модератори не можуть видаляти адміністраторів
  if (isModerator.value && user.role === 'admin') return false;

  // Модератори можуть видаляти всіх інших
  if (isModerator.value) return true;

  return false;
};

// Функція для перевірки, чи може користувач редагувати іншого користувача
const canEditUser = (user) => {
  // Адміністратори можуть редагувати всіх
  if (isAdmin.value) return true;

  // Модератори не можуть редагувати адміністраторів
  if (isModerator.value && user.role === 'admin') return false;

  // Модератори можуть редагувати всіх інших
  if (isModerator.value) return true;

  return false;
};

// Filter methods
const clearFilters = () => {
  filters.search = '';
  filters.role = '';
  // fetchData автоматично викличеться через реактивність
};

const applyFilters = () => {
  // fetchData автоматично викличеться через реактивність
};

const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
};

// Format role for display
const formatRole = (role) => {
  return ROLE_DISPLAY_NAMES[role] || role;
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Avatar methods
const openAvatarViewer = (user) => {
  selectedUser.value = user;
  showAvatarModal.value = true;
};

const closeAvatarViewer = () => {
  showAvatarModal.value = false;
  selectedUser.value = null;
};

const handleAvatarError = (event) => {
  // Hide broken image and show placeholder
  event.target.style.display = 'none';
  const placeholder = event.target.nextElementSibling;
  if (placeholder) {
    placeholder.style.display = 'block';
  }
};

// Сортування користувачів за роллю виконується в usersService



// Confirm delete
const confirmDelete = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  userToDelete.value = null;
};

// Delete user
const deleteUser = async () => {
  if (!userToDelete.value) return;

  try {
    await usersService.deleteUser(userToDelete.value.id);

    // Оновлюємо список користувачів
    await fetchData();

    showDeleteModal.value = false;
    userToDelete.value = null;
  } catch (error) {
    console.error('Error deleting user:', error);
  }
};

// useAdminSearch автоматично завантажує дані при ініціалізації

// API тестування видалено - useAdminSearch автоматично завантажує дані
</script>

<style scoped>
@import '@/assets/css/admin/pages/users.css';

.admin-status-badges {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.admin-badge-xs {
  font-size: 10px;
  padding: 2px 6px;
  line-height: 1.2;
}

/* Avatar styles */
.admin-user-avatar-cell {
  padding: 8px;
}

.admin-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px solid #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.admin-user-avatar:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.admin-user-avatar.has-image {
  border-color: #28a745;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-placeholder {
  color: #6c757d;
  font-size: 18px;
}

.admin-user-avatar.has-image .avatar-placeholder {
  display: none;
}

/* Modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.avatar-viewer {
  padding: 1rem;
}

.no-avatar-message {
  padding: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-user-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-placeholder {
    font-size: 14px;
  }
}
</style>
