import apiClient from './api.service';

class UserService {
  // Get all users with optional filtering and pagination
  async getAll(params = {}) {
    return apiClient.get('/users', { params });
  }

  // Get user by ID
  async getById(id) {
    return apiClient.get(`/users/${id}`);
  }

  // Create new user
  async create(userData) {
    return apiClient.post('/users', userData);
  }

  // Update existing user
  async update(id, userData) {
    return apiClient.put(`/users/${id}`, userData);
  }

  // Delete user
  async delete(id) {
    return apiClient.delete(`/users/${id}`);
  }

  // Change user role
  async changeRole(id, role) {
    return apiClient.patch(`/users/${id}/role`, { role });
  }

  // Get user statistics
  async getStats() {
    return apiClient.get('/users/stats');
  }

  // Get current user profile
  async getCurrentUser() {
    return apiClient.get('/users/me');
  }

  // Get public user profile by ID
  async getUserPublicProfile(userId) {
    return apiClient.get(`/users/${userId}/public`);
  }
}

export default new UserService();
