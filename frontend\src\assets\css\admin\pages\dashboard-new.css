/* ===================================================================
   Admin Dashboard - Modern BEM-based Styles
   ================================================================== */

/* Dashboard Container */
.admin-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.admin-dashboard__header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 2rem 2rem 3rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.admin-dashboard__header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.admin-dashboard__header-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.admin-dashboard__title-section {
  flex: 1;
}

.admin-dashboard__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.admin-dashboard__title-icon {
  font-size: 2rem;
  color: #60a5fa;
}

.admin-dashboard__subtitle {
  font-size: 1.125rem;
  color: #cbd5e1;
  margin: 0;
  font-weight: 400;
}

.admin-dashboard__actions {
  display: flex;
  gap: 1rem;
}

.admin-dashboard__refresh-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.admin-dashboard__refresh-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.admin-dashboard__refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.admin-dashboard__refresh-btn--loading {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

/* Alert Components */
.admin-dashboard__alert {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin: 0 2rem 2rem;
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.admin-dashboard__alert--error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-left: 4px solid #ef4444;
  color: #991b1b;
}

.admin-dashboard__alert-icon {
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.admin-dashboard__alert-content {
  flex: 1;
}

.admin-dashboard__alert-title {
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 0.5rem 0;
}

.admin-dashboard__alert-message {
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.5;
}

.admin-dashboard__alert-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.admin-dashboard__alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Loading State */
.admin-dashboard__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

.admin-dashboard__loading-content {
  text-align: center;
  max-width: 400px;
}

.admin-dashboard__loading-spinner {
  font-size: 3rem;
  color: #3b82f6;
  margin-bottom: 1.5rem;
}

.admin-dashboard__loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.admin-dashboard__loading-text {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.admin-dashboard__loading-progress {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.admin-dashboard__loading-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }
  50% {
    width: 75%;
    margin-left: 25%;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

.admin-dashboard__loading-retry {
  margin-top: 1rem;
}

.admin-dashboard__retry-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-dashboard__retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Empty State */
.admin-dashboard__empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

.admin-dashboard__empty-content {
  text-align: center;
  max-width: 500px;
}

.admin-dashboard__empty-icon {
  font-size: 4rem;
  color: #9ca3af;
  margin-bottom: 1.5rem;
}

.admin-dashboard__empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.admin-dashboard__empty-text {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
}

.admin-dashboard__empty-reasons {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.admin-dashboard__empty-reasons h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.admin-dashboard__empty-reasons ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.admin-dashboard__empty-reasons li {
  margin-bottom: 0.25rem;
}

.admin-dashboard__empty-retry {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-dashboard__empty-retry:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Main Content */
.admin-dashboard__main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
}

/* Section Headers */
.admin-dashboard__section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.admin-dashboard__section-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.admin-dashboard__section-icon {
  color: #3b82f6;
  font-size: 1.5rem;
}

.admin-dashboard__section-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

/* KPI Section */
.admin-dashboard__kpi-section {
  margin-bottom: 3rem;
}

.admin-dashboard__kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.admin-dashboard__kpi-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border-top: 4px solid transparent;
}

.admin-dashboard__kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

.admin-dashboard__kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.admin-dashboard__kpi-card:hover::before {
  height: 6px;
}

/* KPI Card Variants */
.admin-dashboard__kpi-card--users::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.admin-dashboard__kpi-card--orders::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.admin-dashboard__kpi-card--products::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.admin-dashboard__kpi-card--revenue::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.admin-dashboard__kpi-card--categories::before {
  background: linear-gradient(90deg, #6b7280, #4b5563);
}

.admin-dashboard__kpi-card--profit::before {
  background: linear-gradient(90deg, #06b6d4, #0891b2);
}

.admin-dashboard__kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}

.admin-dashboard__kpi-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.admin-dashboard__kpi-card--users .admin-dashboard__kpi-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.admin-dashboard__kpi-card--orders .admin-dashboard__kpi-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.admin-dashboard__kpi-card--products .admin-dashboard__kpi-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.admin-dashboard__kpi-card--revenue .admin-dashboard__kpi-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.admin-dashboard__kpi-card--categories .admin-dashboard__kpi-icon {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.admin-dashboard__kpi-card--profit .admin-dashboard__kpi-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.admin-dashboard__kpi-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-dashboard__kpi-trend--positive {
  background: #dcfce7;
  color: #166534;
}

.admin-dashboard__kpi-trend--negative {
  background: #fef2f2;
  color: #991b1b;
}

.admin-dashboard__kpi-trend--neutral {
  background: #f3f4f6;
  color: #6b7280;
}

.admin-dashboard__kpi-content {
  text-align: left;
}

.admin-dashboard__kpi-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-dashboard__kpi-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.admin-dashboard__kpi-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Charts Section */
.admin-dashboard__charts-section {
  margin-bottom: 3rem;
}

.admin-dashboard__charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto;
  gap: 1.5rem;
}

.admin-dashboard__chart-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-dashboard__chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: all 0.3s ease;
}

.admin-dashboard__chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.admin-dashboard__chart-card:hover::before {
  height: 6px;
}

.admin-dashboard__chart-card--main {
  grid-row: span 1;
}

.admin-dashboard__chart-card--side {
  grid-row: span 1;
}

.admin-dashboard__chart-card--full {
  grid-column: span 2;
}

.admin-dashboard__chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.admin-dashboard__chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-dashboard__chart-icon {
  color: #3b82f6;
  font-size: 1rem;
}

.admin-dashboard__chart-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.admin-dashboard__period-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.admin-dashboard__period-select:hover {
  border-color: #3b82f6;
}

.admin-dashboard__period-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-dashboard__chart-content {
  min-height: 300px;
  margin-bottom: 1.25rem;
  position: relative;
}

.admin-dashboard__chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6b7280;
  gap: 0.75rem;
}

.admin-dashboard__chart-loading i {
  font-size: 2rem;
  color: #3b82f6;
}

.admin-dashboard__chart-footer {
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.admin-dashboard__chart-stats {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.admin-dashboard__stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.admin-dashboard__stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.admin-dashboard__stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.admin-dashboard__stat-value--positive {
  color: #059669;
}

.admin-dashboard__stat-value--negative {
  color: #dc2626;
}

.admin-dashboard__stat-value--neutral {
  color: #6b7280;
}

.admin-dashboard__status-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-dashboard__legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.admin-dashboard__legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 0.25rem;
  flex-shrink: 0;
}

.admin-dashboard__legend-label {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
}

.admin-dashboard__legend-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
}

.admin-dashboard__profit-breakdown {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.admin-dashboard__breakdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.admin-dashboard__breakdown-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.admin-dashboard__breakdown-value {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

/* Quick Actions Section */
.admin-dashboard__quick-actions-section {
  margin-bottom: 3rem;
}

.admin-dashboard__quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.admin-dashboard__quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  border-radius: 0.75rem;
  text-decoration: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  min-height: 120px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.admin-dashboard__quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.admin-dashboard__quick-action-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  color: white;
  text-decoration: none;
}

.admin-dashboard__quick-action-btn:hover::before {
  opacity: 1;
}

.admin-dashboard__quick-action-btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.admin-dashboard__quick-action-btn--info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.admin-dashboard__quick-action-btn--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.admin-dashboard__quick-action-btn--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.admin-dashboard__quick-action-btn--secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.admin-dashboard__quick-action-btn--dark {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.admin-dashboard__quick-action-icon {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

.admin-dashboard__quick-action-text {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.admin-dashboard__quick-action-description {
  font-size: 0.75rem;
  opacity: 0.9;
  text-align: center;
  line-height: 1.3;
}

.admin-dashboard__quick-action-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Activity Section */
.admin-dashboard__activity-section {
  margin-bottom: 2rem;
}

.admin-dashboard__activity-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.admin-dashboard__activity-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.admin-dashboard__activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.admin-dashboard__activity-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6b7280;
  gap: 0.75rem;
}

.admin-dashboard__activity-loading i {
  font-size: 2rem;
  color: #3b82f6;
}

/* ===================================================================
   Responsive Design
   ================================================================== */

/* Large screens (1200px and up) */
@media screen and (min-width: 1200px) {
  .admin-dashboard__kpi-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .admin-dashboard__quick-actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Medium screens (768px to 1199px) */
@media screen and (max-width: 1199px) {
  .admin-dashboard__header-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .admin-dashboard__title-section {
    flex: none;
  }

  .admin-dashboard__charts-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
  }

  .admin-dashboard__chart-card--full {
    grid-column: span 1;
  }

  .admin-dashboard__chart-stats,
  .admin-dashboard__profit-breakdown {
    flex-direction: column;
    gap: 0.75rem;
  }

  .admin-dashboard__stat-item,
  .admin-dashboard__breakdown-item {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

/* Small screens (768px and down) */
@media screen and (max-width: 768px) {
  .admin-dashboard {
    padding: 0;
  }

  .admin-dashboard__header {
    padding: 1.5rem 1rem 2rem;
    margin-bottom: 1rem;
  }

  .admin-dashboard__title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .admin-dashboard__title-icon {
    font-size: 1.75rem;
  }

  .admin-dashboard__subtitle {
    font-size: 1rem;
  }

  .admin-dashboard__main {
    padding: 0 1rem 1rem;
  }

  .admin-dashboard__alert {
    margin: 0 1rem 1rem;
    padding: 1rem;
  }

  .admin-dashboard__section-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .admin-dashboard__section-icon {
    font-size: 1.25rem;
  }

  .admin-dashboard__kpi-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .admin-dashboard__kpi-card {
    padding: 1.25rem;
  }

  .admin-dashboard__kpi-header {
    margin-bottom: 1rem;
  }

  .admin-dashboard__kpi-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.125rem;
  }

  .admin-dashboard__kpi-value {
    font-size: 1.75rem;
  }

  .admin-dashboard__kpi-trend {
    font-size: 0.6875rem;
    padding: 0.25rem 0.5rem;
  }

  .admin-dashboard__chart-card {
    padding: 1rem;
  }

  .admin-dashboard__chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .admin-dashboard__chart-title {
    font-size: 1rem;
  }

  .admin-dashboard__chart-content {
    min-height: 250px;
    margin-bottom: 1rem;
  }

  .admin-dashboard__period-select {
    width: 100%;
    padding: 0.625rem 0.75rem;
  }

  .admin-dashboard__quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .admin-dashboard__quick-action-btn {
    padding: 1.25rem 0.75rem;
    min-height: 100px;
  }

  .admin-dashboard__quick-action-icon {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .admin-dashboard__quick-action-text {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .admin-dashboard__quick-action-description {
    font-size: 0.6875rem;
  }

  .admin-dashboard__activity-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Extra small screens (480px and down) */
@media screen and (max-width: 480px) {
  .admin-dashboard__header {
    padding: 1rem 0.75rem 1.5rem;
  }

  .admin-dashboard__title {
    font-size: 1.75rem;
  }

  .admin-dashboard__main {
    padding: 0 0.75rem 0.75rem;
  }

  .admin-dashboard__alert {
    margin: 0 0.75rem 0.75rem;
    padding: 0.75rem;
  }

  .admin-dashboard__section-title {
    font-size: 1.25rem;
  }

  .admin-dashboard__kpi-card {
    padding: 1rem;
  }

  .admin-dashboard__kpi-value {
    font-size: 1.5rem;
  }

  .admin-dashboard__chart-card {
    padding: 0.75rem;
  }

  .admin-dashboard__chart-content {
    min-height: 200px;
  }

  .admin-dashboard__quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .admin-dashboard__quick-action-btn {
    padding: 1rem 0.5rem;
    min-height: 80px;
  }

  .admin-dashboard__quick-action-text {
    font-size: 0.8125rem;
  }

  .admin-dashboard__quick-action-description {
    font-size: 0.625rem;
  }
}
