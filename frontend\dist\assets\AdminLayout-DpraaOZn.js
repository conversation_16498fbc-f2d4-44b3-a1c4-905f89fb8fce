import{_ as E,u as j,e as G,f as Z,g as P,h as W,i as I,j as Y,c as S,o as D,k as _,l as te,a as e,n as h,m as k,r as ee,t as O,F as ne,p as ae,b as A,w as N,d as se,q as U,G as ie,T as oe,s as re,v as le,E as de}from"./index-L-hJxM_5.js";const ce={class:"modern-sidebar"},ue={class:"sidebar-nav"},me={class:"nav-section"},fe={class:"nav-items"},ve={class:"nav-section"},he={class:"nav-items"},ge={key:0,class:"nav-section"},be={class:"nav-items"},pe={key:1,class:"nav-section"},ye={class:"nav-items"},we={class:"nav-section"},Me={class:"nav-items"},ke={key:2,class:"nav-section"},De={class:"nav-items"},Se={class:"nav-section nav-section-logout"},Ce={class:"nav-items"},Ae=["disabled"],q=300,Pe={__name:"Sidebar",props:{isVisible:{type:Boolean,default:!0}},emits:["toggle-sidebar"],setup(n,{emit:s}){const t=j(),o=G(),i=Z(),c=P(!1),l=P(!1),r=P(0),p=s,v=W(()=>t.getters["auth/isAdmin"]);W(()=>t.getters["auth/isModerator"]),W(()=>t.getters["auth/isAdminOrModerator"]);const w=()=>{p("toggle-sidebar")},m=u=>i.path===u||i.path.startsWith(`${u}/`),d=u=>{const a=Date.now();if(l.value||a-r.value<q||m(u))return;l.value=!0,r.value=a;const y=t.getters["loading/apiService"];y&&y.cancelRequestsForRoute(i.path);let g="",L={};u==="/admin/dashboard"?g="AdminDashboard":u==="/admin/users"?g="AdminUsers":u==="/admin/products"?g="AdminProducts":u==="/admin/categories"?g="AdminCategories":u==="/admin/orders"?g="AdminOrders":u==="/admin/seller-requests"?g="AdminSellerRequests":u==="/admin/companies"?g="AdminCompanies":u==="/admin/reviews"?g="AdminReviews":u==="/admin/ratings"?g="AdminRatings":u==="/admin/chats"?g="AdminChats":u==="/admin/addresses"?g="AdminAddresses":u==="/admin/settings"?g="AdminSettings":u==="/admin/reports"?g="AdminReports":u==="/admin/security"?g="AdminSecurity":g="",g?o.push({name:g,params:L}).finally(()=>{setTimeout(()=>{l.value=!1},q)}):o.push(u).finally(()=>{setTimeout(()=>{l.value=!1},q)}),c.value&&w()},C=async()=>{if(!l.value){l.value=!0;try{await t.dispatch("auth/logout"),o.push("/login")}finally{setTimeout(()=>{l.value=!1},q)}}},M=()=>{c.value=window.innerWidth<1024};return I(()=>{M(),window.addEventListener("resize",M)}),Y(()=>{window.removeEventListener("resize",M)}),(u,a)=>(D(),S("div",ce,[c.value?(D(),S("button",{key:0,class:"sidebar-close-btn",onClick:w},a[14]||(a[14]=[e("i",{class:"fas fa-times"},null,-1)]))):_("",!0),a[36]||(a[36]=te('<div class="sidebar-header-enhanced" data-v-0e9da258><div class="logo-section" data-v-0e9da258><div class="logo-icon" data-v-0e9da258><i class="fas fa-store" data-v-0e9da258></i></div><div class="logo-text" data-v-0e9da258><h2 class="brand-name" data-v-0e9da258>Klondike</h2><span class="brand-subtitle" data-v-0e9da258>Admin Panel</span></div></div></div>',1)),e("nav",ue,[e("div",me,[a[16]||(a[16]=e("div",{class:"nav-section-header"},[e("i",{class:"fas fa-tachometer-alt"}),e("span",null,"Overview")],-1)),e("div",fe,[e("a",{onClick:a[0]||(a[0]=k(y=>d("/admin/dashboard"),["prevent"])),class:h(["nav-item",{active:m("/admin/dashboard")}])},a[15]||(a[15]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-chart-line nav-item-icon"}),e("span",{class:"nav-item-text"},"Dashboard")],-1)]),2)])]),e("div",ve,[a[20]||(a[20]=e("div",{class:"nav-section-header"},[e("i",{class:"fas fa-users"}),e("span",null,"User Management")],-1)),e("div",he,[v.value?(D(),S("a",{key:0,onClick:a[1]||(a[1]=k(y=>d("/admin/users"),["prevent"])),class:h(["nav-item",{active:m("/admin/users")}])},a[17]||(a[17]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-user nav-item-icon"}),e("span",{class:"nav-item-text"},"Users")],-1)]),2)):_("",!0),e("a",{onClick:a[2]||(a[2]=k(y=>d("/admin/seller-requests"),["prevent"])),class:h(["nav-item",{active:m("/admin/seller-requests")}])},a[18]||(a[18]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-user-plus nav-item-icon"}),e("span",{class:"nav-item-text"},"Seller Applications")],-1)]),2),e("a",{onClick:a[3]||(a[3]=k(y=>d("/admin/companies"),["prevent"])),class:h(["nav-item",{active:m("/admin/companies")}])},a[19]||(a[19]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-building nav-item-icon"}),e("span",{class:"nav-item-text"},"Companies")],-1)]),2)])]),v.value?(D(),S("div",ge,[a[23]||(a[23]=e("div",{class:"nav-section-header"},[e("i",{class:"fas fa-box"}),e("span",null,"Catalog")],-1)),e("div",be,[e("a",{onClick:a[4]||(a[4]=k(y=>d("/admin/products"),["prevent"])),class:h(["nav-item",{active:m("/admin/products")}])},a[21]||(a[21]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-cube nav-item-icon"}),e("span",{class:"nav-item-text"},"Products")],-1)]),2),e("a",{onClick:a[5]||(a[5]=k(y=>d("/admin/categories"),["prevent"])),class:h(["nav-item",{active:m("/admin/categories")}])},a[22]||(a[22]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-tags nav-item-icon"}),e("span",{class:"nav-item-text"},"Categories")],-1)]),2)])])):_("",!0),v.value?(D(),S("div",pe,[a[26]||(a[26]=e("div",{class:"nav-section-header"},[e("i",{class:"fas fa-shopping-cart"}),e("span",null,"Sales")],-1)),e("div",ye,[e("a",{onClick:a[6]||(a[6]=k(y=>d("/admin/orders"),["prevent"])),class:h(["nav-item",{active:m("/admin/orders")}])},a[24]||(a[24]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-receipt nav-item-icon"}),e("span",{class:"nav-item-text"},"Orders")],-1)]),2),e("a",{onClick:a[7]||(a[7]=k(y=>d("/admin/reports"),["prevent"])),class:h(["nav-item",{active:m("/admin/reports")}])},a[25]||(a[25]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-chart-bar nav-item-icon"}),e("span",{class:"nav-item-text"},"Reports")],-1)]),2)])])):_("",!0),e("div",we,[a[30]||(a[30]=e("div",{class:"nav-section-header"},[e("i",{class:"fas fa-comments"}),e("span",null,"Communication")],-1)),e("div",Me,[e("a",{onClick:a[8]||(a[8]=k(y=>d("/admin/reviews"),["prevent"])),class:h(["nav-item",{active:m("/admin/reviews")}])},a[27]||(a[27]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-star nav-item-icon"}),e("span",{class:"nav-item-text"},"Reviews")],-1)]),2),e("a",{onClick:a[9]||(a[9]=k(y=>d("/admin/ratings"),["prevent"])),class:h(["nav-item",{active:m("/admin/ratings")}])},a[28]||(a[28]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-star-half-alt nav-item-icon"}),e("span",{class:"nav-item-text"},"Ratings")],-1)]),2),e("a",{onClick:a[10]||(a[10]=k(y=>d("/admin/chats"),["prevent"])),class:h(["nav-item",{active:m("/admin/chats")}])},a[29]||(a[29]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-comment-dots nav-item-icon"}),e("span",{class:"nav-item-text"},"Chats")],-1)]),2)])]),v.value?(D(),S("div",ke,[a[34]||(a[34]=e("div",{class:"nav-section-header"},[e("i",{class:"fas fa-cogs"}),e("span",null,"System")],-1)),e("div",De,[e("a",{onClick:a[11]||(a[11]=k(y=>d("/admin/addresses"),["prevent"])),class:h(["nav-item",{active:m("/admin/addresses")}])},a[31]||(a[31]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-map-marker-alt nav-item-icon"}),e("span",{class:"nav-item-text"},"Addresses")],-1)]),2),e("a",{onClick:a[12]||(a[12]=k(y=>d("/admin/settings"),["prevent"])),class:h(["nav-item",{active:m("/admin/settings")}])},a[32]||(a[32]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-cog nav-item-icon"}),e("span",{class:"nav-item-text"},"Settings")],-1)]),2),e("a",{onClick:a[13]||(a[13]=k(y=>d("/admin/security"),["prevent"])),class:h(["nav-item",{active:m("/admin/security")}])},a[33]||(a[33]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-shield-alt nav-item-icon"}),e("span",{class:"nav-item-text"},"Security & Logs")],-1)]),2)])])):_("",!0),e("div",Se,[e("div",Ce,[e("button",{class:"logout-btn",onClick:C,disabled:l.value},a[35]||(a[35]=[e("div",{class:"nav-item-content"},[e("i",{class:"fas fa-sign-out-alt nav-item-icon"}),e("span",{class:"nav-item-text"},"Logout")],-1)]),8,Ae)])])])]))}},xe=E(Pe,[["__scopeId","data-v-0e9da258"]]),V=43200,H=1440,Q=Symbol.for("constructDateFrom");function B(n,s){return typeof n=="function"?n(s):n&&typeof n=="object"&&Q in n?n[Q](s):n instanceof Date?new n.constructor(s):new Date(s)}function x(n,s){return B(n,n)}let We={};function $e(){return We}function K(n){const s=x(n),t=new Date(Date.UTC(s.getFullYear(),s.getMonth(),s.getDate(),s.getHours(),s.getMinutes(),s.getSeconds(),s.getMilliseconds()));return t.setUTCFullYear(s.getFullYear()),+n-+t}function J(n,...s){const t=B.bind(null,n||s.find(o=>typeof o=="object"));return s.map(t)}function X(n,s){const t=+x(n)-+x(s);return t<0?-1:t>0?1:t}function _e(n){return B(n,Date.now())}function Re(n,s,t){const[o,i]=J(t==null?void 0:t.in,n,s),c=o.getFullYear()-i.getFullYear(),l=o.getMonth()-i.getMonth();return c*12+l}function Te(n){return s=>{const o=(n?Math[n]:Math.trunc)(s);return o===0?0:o}}function Fe(n,s){return+x(n)-+x(s)}function Ne(n,s){const t=x(n);return t.setHours(23,59,59,999),t}function Le(n,s){const t=x(n),o=t.getMonth();return t.setFullYear(t.getFullYear(),o+1,0),t.setHours(23,59,59,999),t}function Oe(n,s){const t=x(n);return+Ne(t)==+Le(t)}function qe(n,s,t){const[o,i,c]=J(t==null?void 0:t.in,n,n,s),l=X(i,c),r=Math.abs(Re(i,c));if(r<1)return 0;i.getMonth()===1&&i.getDate()>27&&i.setDate(30),i.setMonth(i.getMonth()-l*r);let p=X(i,c)===-l;Oe(o)&&r===1&&X(o,c)===1&&(p=!1);const v=l*(r-+p);return v===0?0:v}function Ve(n,s,t){const o=Fe(n,s)/1e3;return Te(t==null?void 0:t.roundingMethod)(o)}const Xe={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},ze=(n,s,t)=>{let o;const i=Xe[n];return typeof i=="string"?o=i:s===1?o=i.one:o=i.other.replace("{{count}}",s.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+o:o+" ago":o};function z(n){return(s={})=>{const t=s.width?String(s.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const Ee={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},je={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Ie={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ye={date:z({formats:Ee,defaultWidth:"full"}),time:z({formats:je,defaultWidth:"full"}),dateTime:z({formats:Ie,defaultWidth:"full"})},Be={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Je=(n,s,t,o)=>Be[n];function T(n){return(s,t)=>{const o=t!=null&&t.context?String(t.context):"standalone";let i;if(o==="formatting"&&n.formattingValues){const l=n.defaultFormattingWidth||n.defaultWidth,r=t!=null&&t.width?String(t.width):l;i=n.formattingValues[r]||n.formattingValues[l]}else{const l=n.defaultWidth,r=t!=null&&t.width?String(t.width):n.defaultWidth;i=n.values[r]||n.values[l]}const c=n.argumentCallback?n.argumentCallback(s):s;return i[c]}}const Ue={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},He={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Qe={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Ke={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Ge={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Ze={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},et=(n,s)=>{const t=Number(n),o=t%100;if(o>20||o<10)switch(o%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},tt={ordinalNumber:et,era:T({values:Ue,defaultWidth:"wide"}),quarter:T({values:He,defaultWidth:"wide",argumentCallback:n=>n-1}),month:T({values:Qe,defaultWidth:"wide"}),day:T({values:Ke,defaultWidth:"wide"}),dayPeriod:T({values:Ge,defaultWidth:"wide",formattingValues:Ze,defaultFormattingWidth:"wide"})};function F(n){return(s,t={})=>{const o=t.width,i=o&&n.matchPatterns[o]||n.matchPatterns[n.defaultMatchWidth],c=s.match(i);if(!c)return null;const l=c[0],r=o&&n.parsePatterns[o]||n.parsePatterns[n.defaultParseWidth],p=Array.isArray(r)?at(r,m=>m.test(l)):nt(r,m=>m.test(l));let v;v=n.valueCallback?n.valueCallback(p):p,v=t.valueCallback?t.valueCallback(v):v;const w=s.slice(l.length);return{value:v,rest:w}}}function nt(n,s){for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&s(n[t]))return t}function at(n,s){for(let t=0;t<n.length;t++)if(s(n[t]))return t}function st(n){return(s,t={})=>{const o=s.match(n.matchPattern);if(!o)return null;const i=o[0],c=s.match(n.parsePattern);if(!c)return null;let l=n.valueCallback?n.valueCallback(c[0]):c[0];l=t.valueCallback?t.valueCallback(l):l;const r=s.slice(i.length);return{value:l,rest:r}}}const it=/^(\d+)(th|st|nd|rd)?/i,ot=/\d+/i,rt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},lt={any:[/^b/i,/^(a|c)/i]},dt={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},ct={any:[/1/i,/2/i,/3/i,/4/i]},ut={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},mt={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},ft={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},vt={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},ht={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},gt={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},bt={ordinalNumber:st({matchPattern:it,parsePattern:ot,valueCallback:n=>parseInt(n,10)}),era:F({matchPatterns:rt,defaultMatchWidth:"wide",parsePatterns:lt,defaultParseWidth:"any"}),quarter:F({matchPatterns:dt,defaultMatchWidth:"wide",parsePatterns:ct,defaultParseWidth:"any",valueCallback:n=>n+1}),month:F({matchPatterns:ut,defaultMatchWidth:"wide",parsePatterns:mt,defaultParseWidth:"any"}),day:F({matchPatterns:ft,defaultMatchWidth:"wide",parsePatterns:vt,defaultParseWidth:"any"}),dayPeriod:F({matchPatterns:ht,defaultMatchWidth:"any",parsePatterns:gt,defaultParseWidth:"any"})},pt={code:"en-US",formatDistance:ze,formatLong:Ye,formatRelative:Je,localize:tt,match:bt,options:{weekStartsOn:0,firstWeekContainsDate:1}};function yt(n,s,t){const o=$e(),i=(t==null?void 0:t.locale)??o.locale??pt,c=2520,l=X(n,s);if(isNaN(l))throw new RangeError("Invalid time value");const r=Object.assign({},t,{addSuffix:t==null?void 0:t.addSuffix,comparison:l}),[p,v]=J(t==null?void 0:t.in,...l>0?[s,n]:[n,s]),w=Ve(v,p),m=(K(v)-K(p))/1e3,d=Math.round((w-m)/60);let C;if(d<2)return t!=null&&t.includeSeconds?w<5?i.formatDistance("lessThanXSeconds",5,r):w<10?i.formatDistance("lessThanXSeconds",10,r):w<20?i.formatDistance("lessThanXSeconds",20,r):w<40?i.formatDistance("halfAMinute",0,r):w<60?i.formatDistance("lessThanXMinutes",1,r):i.formatDistance("xMinutes",1,r):d===0?i.formatDistance("lessThanXMinutes",1,r):i.formatDistance("xMinutes",d,r);if(d<45)return i.formatDistance("xMinutes",d,r);if(d<90)return i.formatDistance("aboutXHours",1,r);if(d<H){const M=Math.round(d/60);return i.formatDistance("aboutXHours",M,r)}else{if(d<c)return i.formatDistance("xDays",1,r);if(d<V){const M=Math.round(d/H);return i.formatDistance("xDays",M,r)}else if(d<V*2)return C=Math.round(d/V),i.formatDistance("aboutXMonths",C,r)}if(C=qe(v,p),C<12){const M=Math.round(d/V);return i.formatDistance("xMonths",M,r)}else{const M=C%12,u=Math.trunc(C/12);return M<3?i.formatDistance("aboutXYears",u,r):M<9?i.formatDistance("overXYears",u,r):i.formatDistance("almostXYears",u+1,r)}}function wt(n,s){return yt(n,_e(n),s)}const Mt={class:"admin-header"},kt={class:"navbar",role:"navigation","aria-label":"main navigation"},Dt={class:"navbar-end"},St={class:"icon"},Ct={key:0,class:"notification-badge"},At={class:"navbar-dropdown is-right notifications-dropdown"},Pt={key:0,class:"notifications-list"},xt=["onClick"],Wt={class:"notification-icon"},$t={class:"icon"},_t={class:"notification-content"},Rt={class:"notification-text"},Tt={class:"notification-time"},Ft={key:1,class:"notifications-empty"},Nt={class:"notifications-footer"},Lt={class:"navbar-dropdown is-right"},Ot={__name:"Header",emits:["toggle-sidebar"],setup(n,{emit:s}){const t=j(),o=G(),i=W(()=>{var b;return((b=t.getters["auth/user"])==null?void 0:b.username)||"Admin"}),c=P(!1),l=P(!1),r=P(!1),p=P([{id:1,type:"order",message:"New order #12345 has been placed",isRead:!1,createdAt:new Date(Date.now()-1e3*60*30)},{id:2,type:"seller",message:"New seller application from John Doe",isRead:!1,createdAt:new Date(Date.now()-1e3*60*60*2)},{id:3,type:"product",message:'Product "iPhone 13" is low in stock (2 remaining)',isRead:!0,createdAt:new Date(Date.now()-1e3*60*60*5)}]),v=W(()=>p.value.filter(b=>!b.isRead).length),w=s,m=()=>{w("toggle-sidebar")},d=()=>{l.value=!l.value,l.value&&(r.value=!1)},C=()=>{r.value=!r.value,r.value&&(l.value=!1)},M=async()=>{await t.dispatch("auth/logout"),o.push("/login")},u=()=>{p.value.forEach(b=>{b.isRead=!0})},a=b=>{b.isRead=!0,b.type==="order"?o.push("/admin/orders"):b.type==="seller"?o.push("/admin/seller-requests"):b.type==="product"&&o.push("/admin/products"),r.value=!1},y=b=>{switch(b){case"order":return"fas fa-shopping-cart";case"seller":return"fas fa-user-plus";case"product":return"fas fa-box";default:return"fas fa-bell"}},g=b=>wt(new Date(b),{addSuffix:!0}),L=b=>{const f=document.querySelector(".navbar-item.has-dropdown:last-child"),R=document.querySelector(".navbar-item.has-dropdown:first-child");f&&!f.contains(b.target)&&(l.value=!1),R&&!R.contains(b.target)&&(r.value=!1)};return I(()=>{document.addEventListener("click",L)}),Y(()=>{document.removeEventListener("click",L)}),(b,f)=>{const R=ee("router-link");return D(),S("header",Mt,[e("nav",kt,[e("button",{class:"sidebar-toggle is-hidden-desktop",onClick:m},f[0]||(f[0]=[e("span",{class:"icon"},[e("i",{class:"fas fa-bars"})],-1)])),f[9]||(f[9]=e("div",{class:"navbar-brand"},[e("h1",{class:"navbar-item title is-4"},"Klondike Admin")],-1)),e("div",{class:h(["navbar-menu",{"is-active":c.value}])},[e("div",Dt,[e("div",{class:h(["navbar-item has-dropdown",{"is-active":r.value}])},[e("a",{class:"navbar-link notification-link",onClick:C},[e("span",St,[f[1]||(f[1]=e("i",{class:"fas fa-bell"},null,-1)),v.value>0?(D(),S("span",Ct,O(v.value),1)):_("",!0)])]),e("div",At,[e("div",{class:"notifications-header"},[f[2]||(f[2]=e("h3",{class:"title is-6"},"Notifications",-1)),e("a",{onClick:u,class:"is-size-7"},"Mark all as read")]),p.value.length>0?(D(),S("div",Pt,[(D(!0),S(ne,null,ae(p.value,$=>(D(),S("a",{key:$.id,class:h(["navbar-item notification-item",{"is-unread":!$.isRead}]),onClick:It=>a($)},[e("div",Wt,[e("span",$t,[e("i",{class:h(y($.type))},null,2)])]),e("div",_t,[e("p",Rt,O($.message),1),e("p",Tt,O(g($.createdAt)),1)])],10,xt))),128))])):(D(),S("div",Ft,f[3]||(f[3]=[e("p",null,"No notifications",-1)]))),e("div",Nt,[A(R,{to:"/admin/notifications",class:"navbar-item"},{default:N(()=>f[4]||(f[4]=[se(" View all notifications ")])),_:1})])])],2),e("div",{class:h(["navbar-item has-dropdown",{"is-active":l.value}])},[e("a",{class:"navbar-link",onClick:d},[f[5]||(f[5]=e("span",{class:"icon"},[e("i",{class:"fas fa-user"})],-1)),e("span",null,O(i.value),1)]),e("div",Lt,[A(R,{to:"/admin/profile",class:"navbar-item"},{default:N(()=>f[6]||(f[6]=[e("span",{class:"icon"},[e("i",{class:"fas fa-user-circle"})],-1),e("span",null,"Profile",-1)])),_:1}),f[8]||(f[8]=e("hr",{class:"navbar-divider"},null,-1)),e("a",{class:"navbar-item",onClick:M},f[7]||(f[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-sign-out-alt"})],-1),e("span",null,"Logout",-1)]))])],2)])],2)])])}}},qt=E(Ot,[["__scopeId","data-v-fa36f639"]]),Vt={class:"admin-theme admin-layout"},Xt={class:"admin-layout-container"},zt={class:"content-area"},Et={class:"content-container"},jt={__name:"AdminLayout",setup(n){const s=j(),t=Z(),o=W(()=>s.getters["loading/isLoading"]),i=W(()=>s.getters["loading/loadingMessage"]),c=P(!0),l=()=>{c.value=!c.value},r=()=>{window.innerWidth>=1024?c.value=!0:c.value=!1},p=()=>{U.cancelRequestsForRoute(t.path),s.dispatch("loading/startRouteChange")},v=()=>{s.dispatch("loading/finishRouteChange")};return I(()=>{window.addEventListener("resize",r),r()}),Y(()=>{window.removeEventListener("resize",r),U.cancelAllRequests()}),(w,m)=>{const d=ee("router-view");return D(),S("div",Vt,[A(ie,{"is-loading":o.value,message:i.value},null,8,["is-loading","message"]),e("div",Xt,[e("div",{class:h(["sidebar-wrapper",{"sidebar-hidden":!c.value}])},[A(xe,{"is-visible":c.value,onToggleSidebar:l},null,8,["is-visible"])],2),e("div",{class:h(["main-content-wrapper",{"sidebar-collapsed":!c.value}])},[A(qt,{onToggleSidebar:l}),e("main",zt,[e("div",Et,[A(de,null,{default:N(()=>[A(d,null,{default:N(({Component:C})=>[A(oe,{name:"fade",mode:"out-in",onBeforeLeave:p,onAfterEnter:v},{default:N(()=>[(D(),re(le(C)))]),_:2},1024)]),_:1})]),_:1})])])],2)])])}}},Bt=E(jt,[["__scopeId","data-v-d146c798"]]);export{Bt as default};
