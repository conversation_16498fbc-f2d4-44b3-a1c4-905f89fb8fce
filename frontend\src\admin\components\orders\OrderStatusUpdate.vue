<template>
  <div class="order-status-update">
    <!-- Modal -->
    <div class="modal" :class="{ 'is-active': showModal }">
      <div class="modal-background" @click="closeModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-edit"></i>
            </span>
            Update Order Status
          </p>
          <button class="delete" aria-label="close" @click="closeModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Order ID</label>
            <div class="control">
              <input class="input" type="text" :value="orderId" readonly>
            </div>
          </div>

          <div class="field">
            <label class="label">Customer</label>
            <div class="control">
              <input class="input" type="text" :value="customerName || 'N/A'" readonly>
            </div>
          </div>

          <div class="field">
            <label class="label">Current Status</label>
            <div class="control">
              <span class="tag is-medium" :class="getOrderStatusClass(currentOrderStatus)">
                {{ getOrderStatusText(currentOrderStatus) }}
              </span>
            </div>
          </div>

          <div class="field">
            <label class="label">New Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="localOrderStatus" :disabled="loading">
                  <option
                    v-for="status in orderStatusOptions"
                    :key="status.value"
                    :value="status.value"
                  >
                    {{ status.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <div class="field">
            <label class="label">Payment Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="localPaymentStatus" :disabled="loading">
                  <option
                    v-for="status in paymentStatusOptions"
                    :key="status.value"
                    :value="status.value"
                  >
                    {{ status.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-success"
            @click="saveChanges"
            :class="{ 'is-loading': loading }"
            :disabled="loading || !hasChanges"
          >
            Update Status
          </button>
          <button class="button" @click="closeModal" :disabled="loading">
            Cancel
          </button>
        </footer>
      </div>
    </div>

    <!-- Trigger Button -->
    <div class="card">
      <div class="card-header">
        <p class="card-header-title">
          <span class="icon">
            <i class="fas fa-cog"></i>
          </span>
          Order Status
        </p>
      </div>
      <div class="card-content">
        <div class="field">
          <label class="label">Current Status</label>
          <div class="control">
            <span class="tag is-medium" :class="getOrderStatusClass(currentOrderStatus)">
              {{ getOrderStatusText(currentOrderStatus) }}
            </span>
          </div>
        </div>

        <div class="field">
          <label class="label">Payment Status</label>
          <div class="control">
            <span class="tag is-medium" :class="getPaymentStatusClass(currentPaymentStatus)">
              {{ getPaymentStatusText(currentPaymentStatus) }}
            </span>
          </div>
        </div>

        <div class="field">
          <div class="control">
            <button
              class="button is-primary is-fullwidth update-status-btn"
              @click.stop="openModal"
              :disabled="loading"
              type="button"
            >
              <span class="icon">
                <i class="fas fa-edit"></i>
              </span>
              <span>Update Status</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { eventBus, EVENTS, updateFlags } from '@/admin/utils/eventBus';
import {
  ORDER_STATUS_OPTIONS,
  PAYMENT_STATUS_OPTIONS,
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass
} from '@/admin/utils/orderConstants';

// Props
const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true
  },
  currentOrderStatus: {
    type: [String, Number],
    required: true
  },
  currentPaymentStatus: {
    type: [String, Number],
    required: true
  },
  customerName: {
    type: String,
    default: ''
  },
  statusHistory: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'update-order-status',
  'update-payment-status'
]);

// Local state
const showModal = ref(false);
const localOrderStatus = ref(props.currentOrderStatus);
const localPaymentStatus = ref(props.currentPaymentStatus);

// Use standardized status options from constants
const orderStatusOptions = ORDER_STATUS_OPTIONS;
const paymentStatusOptions = PAYMENT_STATUS_OPTIONS;

// Computed properties
const hasChanges = computed(() => {
  return localOrderStatus.value !== props.currentOrderStatus ||
         localPaymentStatus.value !== props.currentPaymentStatus;
});

// Methods
const openModal = () => {
  console.log('OrderStatusUpdate: openModal called');
  showModal.value = true;
  // Reset to current values when opening
  localOrderStatus.value = props.currentOrderStatus;
  localPaymentStatus.value = props.currentPaymentStatus;
  console.log('OrderStatusUpdate: Modal should be open now, showModal =', showModal.value);
};

const closeModal = () => {
  showModal.value = false;
  // Reset changes when closing
  localOrderStatus.value = props.currentOrderStatus;
  localPaymentStatus.value = props.currentPaymentStatus;
};

const saveChanges = async () => {
  try {
    // Emit both status updates if they changed
    if (localOrderStatus.value !== props.currentOrderStatus) {
      emit('update-order-status', props.orderId, localOrderStatus.value);
      // Also emit global event
      eventBus.emit(EVENTS.ORDER_UPDATED, {
        orderId: props.orderId,
        type: 'order_status',
        newValue: localOrderStatus.value
      });
    }

    if (localPaymentStatus.value !== props.currentPaymentStatus) {
      emit('update-payment-status', props.orderId, localPaymentStatus.value);
      // Also emit global event
      eventBus.emit(EVENTS.ORDER_UPDATED, {
        orderId: props.orderId,
        type: 'payment_status',
        newValue: localPaymentStatus.value
      });
    }

    // Mark orders for refresh if any status was changed
    if (localOrderStatus.value !== props.currentOrderStatus ||
        localPaymentStatus.value !== props.currentPaymentStatus) {
      updateFlags.markOrdersForRefresh();
    }

    // Close modal after successful update
    closeModal();
  } catch (error) {
    console.error('Error saving status changes:', error);
  }
};

// Status utility functions are now imported from orderConstants

const getTimelineMarkerClass = (type) => {
  const classMap = {
    'order': 'is-primary',
    'payment': 'is-success',
    'system': 'is-info',
    'user': 'is-warning'
  };
  return classMap[type] || 'is-light';
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Watch for prop changes and update local state
watch(() => props.currentOrderStatus, (newStatus) => {
  localOrderStatus.value = newStatus;
});

watch(() => props.currentPaymentStatus, (newStatus) => {
  localPaymentStatus.value = newStatus;
});
</script>

<style scoped>
.order-status-update {
  margin-bottom: 1rem;
}

.card-header-title {
  align-items: center;
}

.card-header-title .icon {
  margin-right: 0.5rem;
}

.modal-card-title {
  align-items: center;
  display: flex;
}

.modal-card-title .icon {
  margin-right: 0.5rem;
}

.field {
  margin-bottom: 1.5rem;
}

.tag.is-medium {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.button.is-fullwidth {
  justify-content: center;
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

.button .icon {
  margin-right: 0.5rem;
}

/* Ensure button is clickable */
.order-status-update .button {
  cursor: pointer;
  pointer-events: auto;
}

.order-status-update .button:disabled {
  cursor: not-allowed;
  pointer-events: auto;
}

/* Specific styles for update status button */
.update-status-btn {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

.update-status-btn:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}
</style>
