using Marketplace.Application.Responses;
using Marketplace.Infrastructure.Persistence;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Application.Queries.Address;

public class GetAddressByIdQueryHandler : IRequestHandler<GetAddressByIdQuery, AddressResponse>
{
    private readonly MarketplaceDbContext _context;

    public GetAddressByIdQueryHandler(MarketplaceDbContext context)
    {
        _context = context;
    }

    public async Task<AddressResponse> Handle(GetAddressByIdQuery request, CancellationToken cancellationToken)
    {
        var address = await _context.Addresses
            .Include(a => a.User)
            .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

        if (address == null)
        {
            throw new KeyNotFoundException($"Address with ID {request.Id} not found.");
        }

        // Отримуємо статистику використання
        var orderStats = await _context.Orders
            .Where(o => o.ShippingAddressId == address.Id)
            .GroupBy(o => o.ShippingAddressId)
            .Select(g => new { Count = g.Count(), LastUsed = g.Max(o => o.CreatedAt) })
            .FirstOrDefaultAsync(cancellationToken);

        return new AddressResponse(
            id: address.Id,
            region: address.AddressVO.Region,
            city: address.AddressVO.City,
            street: address.AddressVO.Street,
            postalCode: address.AddressVO.PostalCode,
            userId: address.UserId,
            userName: address.User?.Username ?? string.Empty,
            userEmail: address.User?.Email?.Value ?? string.Empty,
            createdAt: address.CreatedAt,
            updatedAt: address.UpdatedAt,
            ordersCount: orderStats?.Count ?? 0,
            lastUsedAt: orderStats?.LastUsed,
            isActive: orderStats != null
        );
    }
}
