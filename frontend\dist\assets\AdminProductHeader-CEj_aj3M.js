import{_ as w,h as y,c as d,o as n,k as l,a,J as g,t as i,n as c,r as E,b as v,w as b,d as r,F as $}from"./index-L-hJxM_5.js";const I={key:0,class:"admin-card-header"},U={class:"admin-card-title"},V={key:0,class:"admin-card-subtitle"},z={key:0,class:"admin-card-actions"},H={key:1,class:"admin-card-footer"},F={__name:"AdminCard",props:{title:{type:String,default:""},subtitle:{type:String,default:""},variant:{type:String,default:"default",validator:t=>["default","primary","success","warning","danger","info"].includes(t)},size:{type:String,default:"default",validator:t=>["sm","default","lg"].includes(t)},shadow:{type:String,default:"default",validator:t=>["none","sm","default","lg"].includes(t)},padding:{type:String,default:"default",validator:t=>["none","sm","default","lg"].includes(t)},hoverable:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},setup(t){const u=t,m=y(()=>({[`admin-card--${u.variant}`]:u.variant!=="default",[`admin-card--${u.size}`]:u.size!=="default",[`admin-card--shadow-${u.shadow}`]:u.shadow!=="default","admin-card--hoverable":u.hoverable,"admin-card--loading":u.loading})),p=y(()=>({[`admin-card-body--padding-${u.padding}`]:u.padding!=="default"}));return(o,k)=>(n(),d("div",{class:c(["admin-card",m.value])},[t.title||o.$slots.header?(n(),d("div",I,[g(o.$slots,"header",{},()=>[a("h3",U,i(t.title),1),t.subtitle?(n(),d("div",V,i(t.subtitle),1)):l("",!0)]),o.$slots.actions?(n(),d("div",z,[g(o.$slots,"actions",{},void 0)])):l("",!0)])):l("",!0),a("div",{class:c(["admin-card-body",p.value])},[g(o.$slots,"default",{},void 0)],2),o.$slots.footer?(n(),d("div",H,[g(o.$slots,"footer",{},void 0)])):l("",!0)],2))}},mt=w(F,[["__scopeId","data-v-8c129c19"]]),O={class:"admin-page-header"},T={class:"admin-page-header-content"},q={class:"admin-page-header-info"},J={class:"admin-page-breadcrumb"},R={class:"admin-breadcrumb-current"},j={class:"admin-page-title"},G={key:0,class:"admin-page-subtitle"},K={key:1,class:"admin-page-meta"},L={class:"admin-meta-item"},W={class:"admin-meta-value"},M={key:0,class:"admin-meta-item"},Q={class:"admin-meta-value"},X={key:1,class:"admin-meta-item"},Y={class:"admin-meta-value"},Z={class:"admin-page-actions"},x=["disabled"],_=["disabled"],tt=["disabled"],at={key:0,class:"admin-page-status-bar"},et={class:"admin-status-items"},st={class:"admin-status-item"},dt={key:0,class:"admin-status-item"},nt={key:1,class:"admin-status-item"},it={class:"admin-status-value"},lt={class:"admin-status-item"},ut={class:"admin-status-value admin-status-value--price"},rt={class:"admin-status-item"},ot={__name:"AdminProductHeader",props:{mode:{type:String,required:!0,validator:t=>["view","edit","create"].includes(t)},product:{type:Object,default:null},title:{type:String,default:""},subtitle:{type:String,default:""},canSave:{type:Boolean,default:!0},canCreate:{type:Boolean,default:!0},saving:{type:Boolean,default:!1},creating:{type:Boolean,default:!1}},emits:["save","create","delete"],setup(t,{emit:u}){const m=t,p=y(()=>{switch(m.mode){case"view":return"fas fa-eye";case"edit":return"fas fa-edit";case"create":return"fas fa-plus";default:return"fas fa-box"}}),o=y(()=>{var s;switch(m.mode){case"view":return((s=m.product)==null?void 0:s.name)||"Product Details";case"edit":return"Edit Product";case"create":return"Create Product";default:return"Product"}}),k=s=>s?new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",A=(s,e="UAH")=>!s&&s!==0?"N/A":new Intl.NumberFormat("uk-UA",{style:"currency",currency:e||"UAH"}).format(s),N=s=>{switch(s){case 0:return"PENDING";case 1:return"APPROVED";case 2:return"REJECTED";default:return"UNKNOWN"}},P=s=>{switch(s){case 0:return"admin-status-badge--warning";case 1:return"admin-status-badge--success";case 2:return"admin-status-badge--danger";default:return"admin-status-badge--secondary"}},D=s=>{switch(s){case 0:return"fa-clock";case 1:return"fa-check-circle";case 2:return"fa-times-circle";default:return"fa-question-circle"}},B=s=>s===0?"admin-status-value--danger":s<10?"admin-status-value--warning":"admin-status-value--success";return(s,e)=>{const f=E("router-link");return n(),d("div",O,[a("div",T,[a("div",q,[a("div",J,[v(f,{to:"/admin/products",class:"admin-breadcrumb-link"},{default:b(()=>e[3]||(e[3]=[a("i",{class:"fas fa-box"},null,-1),r(" Products ")])),_:1}),e[4]||(e[4]=a("i",{class:"fas fa-chevron-right admin-breadcrumb-separator"},null,-1)),a("span",R,i(o.value),1)]),a("h1",j,[a("i",{class:c([p.value,"admin-page-title-icon"])},null,2),r(" "+i(t.title),1)]),t.subtitle?(n(),d("div",G,i(t.subtitle),1)):l("",!0),t.product?(n(),d("div",K,[a("div",L,[e[5]||(e[5]=a("span",{class:"admin-meta-label"},"ID:",-1)),a("span",W,i(t.product.id),1)]),t.product.createdAt?(n(),d("div",M,[e[6]||(e[6]=a("span",{class:"admin-meta-label"},"Created:",-1)),a("span",Q,i(k(t.product.createdAt)),1)])):l("",!0),t.product.updatedAt?(n(),d("div",X,[e[7]||(e[7]=a("span",{class:"admin-meta-label"},"Updated:",-1)),a("span",Y,i(k(t.product.updatedAt)),1)])):l("",!0)])):l("",!0)]),a("div",Z,[g(s.$slots,"actions",{},()=>{var h,C;return[t.mode==="view"?(n(),d($,{key:0},[v(f,{to:`/admin/products/${(h=t.product)==null?void 0:h.id}/edit`,class:"admin-btn admin-btn-primary"},{default:b(()=>e[8]||(e[8]=[a("i",{class:"fas fa-edit"},null,-1),r(" Edit Product ")])),_:1},8,["to"]),a("button",{type:"button",class:"admin-btn admin-btn-danger",onClick:e[0]||(e[0]=S=>s.$emit("delete")),disabled:!t.product},e[9]||(e[9]=[a("i",{class:"fas fa-trash"},null,-1),r(" Delete ")]),8,x)],64)):t.mode==="edit"?(n(),d($,{key:1},[a("button",{type:"button",class:c(["admin-btn admin-btn-success",{"admin-btn--loading":t.saving}]),onClick:e[1]||(e[1]=S=>s.$emit("save")),disabled:!t.canSave},[e[10]||(e[10]=a("i",{class:"fas fa-save"},null,-1)),r(" "+i(t.saving?"Saving...":"Save Changes"),1)],10,_),v(f,{to:(C=t.product)!=null&&C.id?`/admin/products/${t.product.id}`:"/admin/products",class:"admin-btn admin-btn-secondary"},{default:b(()=>e[11]||(e[11]=[a("i",{class:"fas fa-times"},null,-1),r(" Cancel ")])),_:1},8,["to"])],64)):t.mode==="create"?(n(),d($,{key:2},[a("button",{type:"button",class:c(["admin-btn admin-btn-success",{"admin-btn--loading":t.creating}]),onClick:e[2]||(e[2]=S=>s.$emit("create")),disabled:!t.canCreate},[e[12]||(e[12]=a("i",{class:"fas fa-plus"},null,-1)),r(" "+i(t.creating?"Creating...":"Create Product"),1)],10,tt),v(f,{to:"/admin/products",class:"admin-btn admin-btn-secondary"},{default:b(()=>e[13]||(e[13]=[a("i",{class:"fas fa-times"},null,-1),r(" Cancel ")])),_:1})],64)):l("",!0)]})])]),t.product&&t.mode==="view"?(n(),d("div",at,[a("div",et,[a("div",st,[e[14]||(e[14]=a("span",{class:"admin-status-label"},"Status:",-1)),a("span",{class:c(["admin-status-badge",P(t.product.status)])},[a("i",{class:c(["fas",D(t.product.status)])},null,2),r(" "+i(N(t.product.status)),1)],2)]),t.product.companyName?(n(),d("div",dt,[e[15]||(e[15]=a("span",{class:"admin-status-label"},"Company:",-1)),v(f,{to:`/admin/companies/${t.product.companyId}`,class:"admin-status-link"},{default:b(()=>[r(i(t.product.companyName),1)]),_:1},8,["to"])])):l("",!0),t.product.categoryName?(n(),d("div",nt,[e[16]||(e[16]=a("span",{class:"admin-status-label"},"Category:",-1)),a("span",it,i(t.product.categoryName),1)])):l("",!0),a("div",lt,[e[17]||(e[17]=a("span",{class:"admin-status-label"},"Price:",-1)),a("span",ut,i(A(t.product.priceAmount,t.product.priceCurrency)),1)]),a("div",rt,[e[18]||(e[18]=a("span",{class:"admin-status-label"},"Stock:",-1)),a("span",{class:c(["admin-status-value",B(t.product.stock)])},i(t.product.stock||0),3)])])])):l("",!0)])}}},ft=w(ot,[["__scopeId","data-v-dd356214"]]);export{mt as A,ft as a};
