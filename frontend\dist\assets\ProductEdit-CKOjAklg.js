import{_ as ue,h as R,c as d,o as u,k as v,a,J as ge,t as p,n as x,g,d as D,m as ye,L as be,F as ve,p as pe,M as J,f as we,i as _e,H as fe,N as Ce,j as ke,b as C,w as V,x as z,D as B,y as Pe,e as Me}from"./index-L-hJxM_5.js";import{A as $e,a as Ae}from"./AdminProductHeader-CEj_aj3M.js";import{E as Ue,a as xe}from"./EnhancedProductAttributesEditor-lcyAKYjd.js";import{E as De}from"./EnhancedCategorySelector-v7wVF3eo.js";import{i as de}from"./image.service-DOD4lHqw.js";import{E as Se}from"./EntityMetaImageManager-oIAk4B-a.js";import{p as X}from"./products-Bpq90UOX.js";const Ee={key:0,class:"admin-form-section-header"},Te={key:0,class:"admin-form-section-title"},Fe={key:1,class:"admin-form-section-description"},ze={class:"admin-form-section-content"},Ve={__name:"AdminFormSection",props:{title:{type:String,default:""},description:{type:String,default:""},variant:{type:String,default:"default",validator:c=>["default","bordered","card"].includes(c)},spacing:{type:String,default:"default",validator:c=>["compact","default","relaxed"].includes(c)},required:{type:Boolean,default:!1}},setup(c){const E=c,S=R(()=>({[`admin-form-section--${E.variant}`]:E.variant!=="default",[`admin-form-section--${E.spacing}`]:E.spacing!=="default","admin-form-section--required":E.required}));return(k,y)=>(u(),d("div",{class:x(["admin-form-section",S.value])},[c.title||c.description||k.$slots.header?(u(),d("div",Ee,[ge(k.$slots,"header",{},()=>[c.title?(u(),d("h3",Te,p(c.title),1)):v("",!0),c.description?(u(),d("p",Fe,p(c.description),1)):v("",!0)])])):v("",!0),a("div",ze,[ge(k.$slots,"default",{},void 0)])],2))}},q=ue(Ve,[["__scopeId","data-v-a1f90b28"]]),Be={class:"product-images-manager"},Ne={class:"upload-section mb-5"},Oe=["disabled"],Le={class:"drop-zone-content"},qe={key:0,class:"fas fa-cloud-upload-alt upload-icon"},je={key:1,class:"fas fa-spinner fa-spin upload-icon"},He={class:"upload-text"},Re={key:0},Je={key:1},Ge={key:0,class:"upload-progress mb-5"},We={class:"progress-info"},Ke={class:"progress-label"},Qe={class:"progress-percentage"},Ye={class:"progress-bar"},Xe={key:1,class:"current-images mb-5"},Ze={class:"section-label"},ea={class:"images-grid"},aa={class:"image-card"},ta={class:"image-wrapper"},la=["src","alt","onClick"],sa={class:"image-overlay"},na=["onClick"],oa=["onClick"],ia=["onClick"],ra={key:0,class:"main-badge"},da={key:2,class:"pending-images mb-5"},ua={class:"section-label"},ca={class:"images-grid"},ma={class:"image-card"},ga={class:"image-wrapper"},va=["src","alt"],pa={class:"image-overlay"},fa=["onClick"],ya=["onClick"],ha={key:0,class:"main-badge"},Ia={class:"image-info"},ba={class:"image-name"},wa={class:"image-size"},_a={key:3,class:"empty-state"},Ca={key:4,class:"modal is-active"},ka={class:"modal-content"},Pa={class:"image"},Ma=["src","alt"],$a={key:5,class:"modal is-active"},Aa={class:"modal-card"},Ua={class:"modal-card-foot"},xa=["disabled"],Da={key:0},Sa={key:1},Ea={__name:"ProductImagesManager",props:{productId:{type:String,default:null},images:{type:Array,default:()=>[]},isCreate:{type:Boolean,default:!1}},emits:["images-updated","main-image-changed","image-uploaded","image-deleted","pending-images-changed"],setup(c,{expose:E,emit:S}){const k=c,y=S,m=g([]),b=g(!1),$=g(!1),h=g({current:0,total:0}),o=g(!1),f=g(!1),P=g(!1),A=g(""),t=g(""),w=g(null),T=g(null),N=g([]),G=()=>{var n;b.value||(n=T.value)==null||n.click()},Z=n=>{const s=Array.from(n.target.files);O(s),n.target.value=""},ee=n=>{if(n.preventDefault(),n.stopPropagation(),o.value=!1,b.value)return;const s=Array.from(n.dataTransfer.files).filter(i=>i.type.startsWith("image/"));O(s)},U=n=>{n.preventDefault(),n.stopPropagation(),o.value=!0},ae=()=>{o.value=!1},O=async n=>{if(n.length===0)return;const s=n.filter(i=>i.size>5*1024*1024?(alert(`File ${i.name} is too large. Maximum size is 5MB.`),!1):["image/jpeg","image/jpg","image/png","image/gif","image/webp","image/jfif"].includes(i.type)?!0:(alert(`File ${i.name} has invalid type. Please select a valid image file.`),!1));s.length!==0&&(N.value=s,s.forEach((i,I)=>{const Y=new FileReader;Y.onload=async oe=>{const ie={id:`temp-${Date.now()}-${Math.random()}-${I}`,file:i,name:i.name,preview:oe.target.result,size:i.size,isTemp:!0,isMain:m.value.length===0&&k.images.length===0};m.value.push(ie),await J(),y("pending-images-changed",m.value)},Y.readAsDataURL(i)}))},W=async n=>{if(k.productId)try{const s=await de.setMainImage("product",k.productId,n);s&&s.success&&(y("main-image-changed",n),y("images-updated"))}catch(s){console.error("Error setting main image:",s),alert("Failed to set main image. Please try again.")}},te=n=>{m.value.forEach(s=>{s.isMain=s.id===n}),y("pending-images-changed",m.value)},j=n=>{w.value=n,P.value=!0},L=()=>{P.value=!1,w.value=null},H=async()=>{if(!(!w.value||!k.productId)){$.value=!0;try{const n=await de.deleteImage(w.value.id);n&&n.success&&(y("image-deleted",w.value.id),y("images-updated"),P.value=!1,w.value=null)}catch(n){console.error("Error deleting image:",n),alert("Failed to delete image. Please try again.")}finally{$.value=!1}}},le=n=>{const s=m.value.findIndex(i=>i.id===n);s!==-1&&(m.value.splice(s,1),y("pending-images-changed",m.value))},K=n=>{A.value=n.imageUrl||n.url||n.preview,t.value=n.altText||n.name||"Product image",f.value=!0},Q=()=>{f.value=!1,A.value="",t.value=""},se=n=>{n.target.src="/placeholder-image.png"},ne=n=>{if(n===0)return"0 Bytes";const s=1024,i=["Bytes","KB","MB","GB"],I=Math.floor(Math.log(n)/Math.log(s));return parseFloat((n/Math.pow(s,I)).toFixed(2))+" "+i[I]};return E({uploadPendingImages:async n=>{if(!(m.value.length===0||!n)){b.value=!0,h.value={current:0,total:m.value.length};try{for(const s of m.value){if(!s.file)continue;const i=await de.uploadImage("product",n,s.file,I=>{console.log(`Upload progress for ${s.file.name}: ${I}%`)});s.isMain&&i.data&&i.data.id&&await W(i.data.id),h.value.current++,y("image-uploaded",i.data)}m.value=[],y("pending-images-changed",m.value),y("images-updated")}catch(s){console.error("Error uploading images:",s),alert("Failed to upload some images. Please try again.")}finally{b.value=!1,h.value={current:0,total:0}}}},getPendingImages:()=>m.value,clearPendingImages:()=>{m.value=[],y("pending-images-changed",m.value)}}),(n,s)=>(u(),d("div",Be,[s[14]||(s[14]=a("div",{class:"admin-section-header"},[a("h4",{class:"admin-section-title"},[a("i",{class:"fas fa-images"}),D(" Product Images ")]),a("p",{class:"admin-section-description"}," Upload and manage product images. You can set one image as the main image for the product. ")],-1)),a("div",Ne,[a("div",{class:x(["drop-zone",{"drop-zone--dragover":o.value,"drop-zone--disabled":b.value}]),onDrop:ee,onDragover:ye(U,["prevent"]),onDragleave:ae,onClick:G},[a("input",{ref_key:"fileInput",ref:T,type:"file",accept:"image/*,.jfif",multiple:"",onChange:Z,disabled:b.value,style:{display:"none"}},null,40,Oe),a("div",Le,[b.value?(u(),d("i",je)):(u(),d("i",qe)),a("p",He,[b.value?(u(),d("strong",Je,"Uploading images...")):(u(),d("strong",Re,"Drop images here or click to browse"))]),s[0]||(s[0]=a("p",{class:"upload-subtext"}," Supports: JPG, PNG, GIF, WebP, JFIF (max 5MB each) ",-1))])],34)]),b.value?(u(),d("div",Ge,[a("div",We,[a("span",Ke,[s[1]||(s[1]=a("i",{class:"fas fa-spinner fa-spin"},null,-1)),D(" Uploading "+p(h.value.current)+" of "+p(h.value.total)+" images... ",1)]),a("span",Qe,p(Math.round(h.value.current/h.value.total*100))+"% ",1)]),a("div",Ye,[a("div",{class:"progress-fill",style:be({width:h.value.current/h.value.total*100+"%"})},null,4)])])):v("",!0),c.images.length>0?(u(),d("div",Xe,[a("label",Ze,"Current Images ("+p(c.images.length)+")",1),a("div",ea,[(u(!0),d(ve,null,pe(c.images,i=>(u(),d("div",{key:i.id,class:"image-item"},[a("div",aa,[a("div",ta,[a("img",{src:i.imageUrl||i.url,alt:i.altText||"Product image",class:"image-preview",onError:se,onClick:I=>K(i)},null,40,la),a("div",sa,[a("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",onClick:I=>K(i),title:"View full size"},s[2]||(s[2]=[a("i",{class:"fas fa-search-plus"},null,-1)]),8,na),i.isMain?v("",!0):(u(),d("button",{key:0,class:"admin-btn admin-btn-sm admin-btn-primary",onClick:I=>W(i.id),title:"Set as main image"},s[3]||(s[3]=[a("i",{class:"fas fa-star"},null,-1)]),8,oa)),a("button",{class:"admin-btn admin-btn-sm admin-btn-danger",onClick:I=>j(i),title:"Delete image"},s[4]||(s[4]=[a("i",{class:"fas fa-trash"},null,-1)]),8,ia)]),i.isMain?(u(),d("div",ra,s[5]||(s[5]=[a("i",{class:"fas fa-star"},null,-1),D(" Main ")]))):v("",!0)])])]))),128))])])):v("",!0),m.value.length>0?(u(),d("div",da,[a("label",ua,"Images to Upload ("+p(m.value.length)+")",1),s[9]||(s[9]=a("div",{class:"notification is-warning is-light"},[a("span",{class:"icon-text"},[a("span",{class:"icon"},[a("i",{class:"fas fa-info-circle"})]),a("span",null,"These images will be uploaded when you save the product.")])],-1)),a("div",ca,[(u(!0),d(ve,null,pe(m.value,i=>(u(),d("div",{key:i.id,class:"image-item"},[a("div",ma,[a("div",ga,[a("img",{src:i.preview,alt:i.name,class:"image-preview"},null,8,va),a("div",pa,[i.isMain?v("",!0):(u(),d("button",{key:0,class:"admin-btn admin-btn-sm admin-btn-primary",onClick:I=>te(i.id),title:"Set as main image"},s[6]||(s[6]=[a("i",{class:"fas fa-star"},null,-1)]),8,fa)),a("button",{class:"admin-btn admin-btn-sm admin-btn-danger",onClick:I=>le(i.id),title:"Remove image"},s[7]||(s[7]=[a("i",{class:"fas fa-trash"},null,-1)]),8,ya)]),i.isMain?(u(),d("div",ha,s[8]||(s[8]=[a("i",{class:"fas fa-star"},null,-1),D(" Main ")]))):v("",!0)]),a("div",Ia,[a("span",ba,p(i.name),1),a("span",wa,p(ne(i.size)),1)])])]))),128))])])):v("",!0),c.images.length===0&&m.value.length===0?(u(),d("div",_a,s[10]||(s[10]=[a("i",{class:"fas fa-images empty-icon"},null,-1),a("p",{class:"empty-text"},"No images uploaded",-1),a("p",{class:"empty-subtext"},"Upload images to showcase your product",-1)]))):v("",!0),f.value?(u(),d("div",Ca,[a("div",{class:"modal-background",onClick:Q}),a("div",ka,[a("p",Pa,[a("img",{src:A.value,alt:t.value},null,8,Ma)])]),a("button",{class:"modal-close is-large",onClick:Q})])):v("",!0),P.value?(u(),d("div",$a,[a("div",{class:"modal-background",onClick:L}),a("div",Aa,[a("header",{class:"modal-card-head"},[s[11]||(s[11]=a("p",{class:"modal-card-title"},"Confirm Delete",-1)),a("button",{class:"delete",onClick:L})]),s[13]||(s[13]=a("section",{class:"modal-card-body"},[a("p",null,"Are you sure you want to delete this image? This action cannot be undone.")],-1)),a("footer",Ua,[a("button",{class:"button is-danger",onClick:H,disabled:$.value},[$.value?(u(),d("span",Sa,s[12]||(s[12]=[a("i",{class:"fas fa-spinner fa-spin"},null,-1),D(" Deleting... ")]))):(u(),d("span",Da,"Delete"))],8,xa),a("button",{class:"button",onClick:L},"Cancel")])])])):v("",!0)]))}},Ta=ue(Ea,[["__scopeId","data-v-7755a164"]]),Fa={class:"admin-page-container"},za={key:0,class:"admin-loading-state"},Va={key:1,class:"admin-error-state"},Ba={class:"admin-error-content"},Na={class:"admin-error-message"},Oa={key:2,class:"admin-page-content"},La={class:"admin-form-grid"},qa={key:0,class:"admin-form-row"},ja={class:"admin-form-col"},Ha={class:"admin-form-col"},Ra={key:1,class:"admin-form-row"},Ja={class:"admin-form-col"},Ga={key:0,class:"admin-form-error"},Wa={class:"admin-form-row"},Ka={class:"admin-form-col"},Qa={key:0,class:"admin-form-error"},Ya={class:"admin-form-row"},Xa={class:"admin-form-col"},Za={key:0,class:"admin-form-error"},et={class:"admin-form-row"},at={class:"admin-form-col admin-form-col--half"},tt={class:"admin-input-group"},lt={key:0,class:"admin-form-error"},st={class:"admin-form-col admin-form-col--half"},nt={key:0,class:"admin-form-error"},ot={class:"admin-form-row"},it={class:"admin-form-col"},rt={class:"admin-form-help"},dt={class:"admin-form-row"},ut={class:"admin-form-col"},ct={class:"admin-form-help"},mt={__name:"ProductEdit",props:{productId:{type:[String,Number],default:null},isCreate:{type:Boolean,default:!1}},emits:["save","cancel","created","updated"],setup(c,{emit:E}){const S=c,k=E,y=we(),m=Me(),b=g(!1),$=g(!1),h=g(null),o=g({}),f=g(!1),P=g(null),A=g(null),t=g({name:"",slug:"",description:"",companyId:null,categoryId:null,priceAmount:null,priceCurrency:"UAH",stock:0,attributes:{},images:[],metaTitle:"",metaDescription:"",metaImage:"",metaImageArray:[],status:0}),w=()=>{t.value||(t.value={});const l={name:"",slug:"",description:"",companyId:null,categoryId:null,priceAmount:null,priceCurrency:"UAH",stock:0,attributes:{},images:[],metaTitle:"",metaDescription:"",metaImage:"",status:0};Object.keys(l).forEach(e=>{t.value[e]===void 0&&(t.value[e]=l[e])})},T=g({}),N=R(()=>S.productId||y.params.id),G=R(()=>t.value?t.value.name&&t.value.companyId&&t.value.categoryId&&t.value.priceAmount!==null&&!$.value:!1),Z=R(()=>G.value&&S.isCreate),ee=R(()=>S.isCreate?!0:!t.value||!T.value?!1:JSON.stringify(t.value)!==JSON.stringify(T.value)),U=async()=>{try{if(!f.value)return;if(w(),S.isCreate){t.value={name:"",slug:"",description:"",companyId:null,categoryId:null,priceAmount:null,priceCurrency:"UAH",stock:0,attributes:{},images:[],metaTitle:"",metaDescription:"",metaImage:"",status:0},T.value={...t.value},await J(),f.value&&w();return}b.value=!0,h.value=null;const l=await X.getProductByIdWithImages(N.value);if(!f.value)return;const e=l.data||l;t.value={...e,priceAmount:e.priceAmount||null,priceCurrency:e.priceCurrency||"UAH",stock:e.stock||0,attributes:W(e.attributes),images:te(e.images||[]),metaTitle:e.metaTitle||"",metaDescription:e.metaDescription||"",metaImage:e.metaImage||"",metaImageArray:e.metaImage?[{url:e.metaImage,name:"Meta Image"}]:[]},T.value={...t.value},await J(),f.value&&w()}catch(l){console.error("Error loading product:",l),f.value&&(h.value=l.message||"Failed to load product",w())}finally{f.value&&(b.value=!1)}},ae=()=>{var l;return o.value={},(l=t.value.name)!=null&&l.trim()||(o.value.name="Product name is required"),t.value.companyId||(o.value.companyId="Company selection is required"),t.value.categoryId||(o.value.categoryId="Category selection is required"),(!t.value.priceAmount||t.value.priceAmount<=0)&&(o.value.priceAmount="Valid price is required"),t.value.slug&&!/^[a-z0-9-]+$/.test(t.value.slug)&&(o.value.slug="Slug can only contain lowercase letters, numbers, and hyphens"),t.value.metaImage&&!j(t.value.metaImage)&&(o.value.metaImage="Meta image must be a valid HTTP/HTTPS URL"),Object.keys(o.value).length===0},O=l=>l.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),W=l=>{if(!l)return{};let e=l;if(typeof l=="string")try{e=JSON.parse(l)}catch(F){return console.error("Error parsing attributes:",F),{}}const r={};return Object.keys(e).forEach(F=>{const _=e[F];typeof _=="string"?r[F]=_.split(",").map(M=>M.trim()).filter(M=>M):Array.isArray(_)?r[F]=_:r[F]=[String(_)]}),r},te=l=>Array.isArray(l)?l.map(e=>({id:e.id||e.Id,url:e.image||e.Image||e.imageUrl||e.ImageUrl,name:e.altText||e.AltText||`Image ${e.order||e.Order||1}`,uploaded:!0,isMain:e.isMain||e.IsMain||!1,order:e.order||e.Order||0,altText:e.altText||e.AltText||""})):[],j=l=>{if(!l||typeof l!="string")return!1;try{const e=new URL(l);return e.protocol==="http:"||e.protocol==="https:"}catch{return!1}},L=async l=>{try{if(console.log("Uploading pending images for product:",l),t.value.images&&t.value.images.length>0){for(const e of t.value.images)if(e.file&&!e.uploaded){console.log("Uploading product image:",e.file.name);try{const r=await X.uploadProductImageSingle(l,e.file);console.log("Product image uploaded successfully:",r),e.uploaded=!0,r&&r.success&&r.data&&(e.id=r.data)}catch(r){console.error("Error uploading product image:",r)}}}console.log("Finished uploading pending images")}catch(e){console.error("Error in uploadPendingImages:",e)}},H=async()=>{var l;if(ae())try{$.value=!0,!t.value.slug&&t.value.name&&(t.value.slug=O(t.value.name));const e={};t.value.attributes&&typeof t.value.attributes=="object"&&Object.keys(t.value.attributes).forEach(M=>{const re=t.value.attributes[M];Array.isArray(re)?e[M]=re.join(", "):e[M]=String(re)});let r=t.value.metaImage||"";r&&!j(r)&&(console.warn("Invalid metaImage URL, setting to empty:",r),r="");const F={...t.value,attributes:e,metaImage:r};let _;if(S.isCreate){_=await X.createProduct(F),k("created",_);const M=((l=_.data)==null?void 0:l.id)||_.id;M&&(await L(M),P.value&&P.value.uploadPendingImages&&await P.value.uploadPendingImages(M),A.value&&A.value.uploadPendingFile&&await A.value.uploadPendingFile()),m.push(`/admin/products/${M}/edit`)}else _=await X.updateProduct(N.value,F),k("updated",_),await L(N.value),P.value&&P.value.uploadPendingImages&&await P.value.uploadPendingImages(N.value),A.value&&A.value.uploadPendingFile&&await A.value.uploadPendingFile(),T.value={...t.value};k("save",_)}catch(e){console.error("Error saving product:",e),h.value=e.message||"Failed to save product"}finally{$.value=!1}},le=()=>{var l;if(!t.value){console.error("FormData not initialized when handling name change");return}(!t.value.slug||t.value.slug===O(((l=T.value)==null?void 0:l.name)||""))&&(t.value.slug=O(t.value.name||"")),t.value.metaTitle||(t.value.metaTitle=t.value.name||""),o.value&&o.value.name&&delete o.value.name},K=()=>{o.value.slug&&delete o.value.slug},Q=l=>{if(!t.value){console.error("FormData not initialized when handling company change");return}t.value.companyId=l,o.value&&o.value.companyId&&delete o.value.companyId},se=l=>{if(!t.value){console.error("FormData not initialized when handling category change");return}t.value.categoryId=l,o.value&&o.value.categoryId&&delete o.value.categoryId},ne=l=>{t.value.attributes=l},ce=async()=>{await U()},n=l=>{console.log("Main image changed:",l),U()},s=l=>{console.log("Image uploaded:",l),U()},i=l=>{console.log("Image deleted:",l),U()},I=l=>{console.log("Pending images changed:",l)},Y=l=>{console.log("Meta image uploaded:",l),t.value.metaImage=l.fileUrl,U()},oe=()=>{console.log("Meta image deleted"),t.value.metaImage=null,U()},ie=l=>{console.log("Pending meta image changed:",l),l&&console.log("Meta image will be uploaded after product creation")},he=l=>{const e=(l||"").length;return e<=50?"admin-text-success":e<=60?"admin-text-warning":"admin-text-danger"},Ie=l=>{const e=(l||"").length;return e<=140?"admin-text-success":e<=160?"admin-text-warning":"admin-text-danger"};_e(async()=>{try{if(f.value=!0,w(),await J(),!f.value)return;await U(),window.addEventListener("beforeunload",me)}catch(l){console.error("Error in ProductEdit onMounted:",l),f.value&&w()}}),fe(()=>y.params.id,async l=>{l&&!S.isCreate&&f.value&&(await J(),await U())}),fe(()=>t.value.metaImageArray,l=>{if(l&&l.length>0){const e=l[0].url||l[0].src||"";e&&j(e)?t.value.metaImage=e:t.value.metaImage=""}else t.value.metaImage=""},{deep:!0});const me=l=>{ee.value&&!$.value&&(l.preventDefault(),l.returnValue="")};return Ce(()=>{f.value=!1}),ke(()=>{window.removeEventListener("beforeunload",me)}),(l,e)=>(u(),d("div",Fa,[b.value?(u(),d("div",za,e[11]||(e[11]=[a("div",{class:"admin-loading-spinner"},[a("i",{class:"fas fa-spinner fa-spin"})],-1),a("p",{class:"admin-loading-text"},"Loading product data...",-1)]))):h.value?(u(),d("div",Va,[C($e,{variant:"danger",title:"Error Loading Product"},{default:V(()=>[a("div",Ba,[e[13]||(e[13]=a("i",{class:"fas fa-exclamation-triangle admin-error-icon"},null,-1)),a("p",Na,p(h.value),1),a("button",{class:"admin-btn admin-btn-primary",onClick:U},e[12]||(e[12]=[a("i",{class:"fas fa-redo"},null,-1),D(" Try Again ")]))])]),_:1})])):f.value?(u(),d("div",Oa,[C(Ae,{mode:c.isCreate?"create":"edit",product:t.value,title:c.isCreate?"Create New Product":`Edit Product: ${t.value.name||"Untitled"}`,subtitle:c.isCreate?"Add a new product to your catalog":"Update product information and settings","can-save":G.value,"can-create":Z.value,saving:$.value,creating:$.value&&c.isCreate,onSave:H,onCreate:H},null,8,["mode","product","title","subtitle","can-save","can-create","saving","creating"]),a("form",{onSubmit:ye(H,["prevent"]),class:"admin-form"},[a("div",La,[C(q,{title:"Basic Information",description:"Enter the core product details",variant:"card",required:!0},{default:V(()=>[t.value&&!b.value?(u(),d("div",qa,[a("div",ja,[C(Ue,{"model-value":t.value.companyId,"onUpdate:modelValue":e[0]||(e[0]=r=>t.value.companyId=r),label:"Company",placeholder:"Select company...",required:!0,"error-message":o.value.companyId||"","help-text":"Select the company that owns this product",onChange:Q},null,8,["model-value","error-message"])]),a("div",Ha,[C(De,{"model-value":t.value.categoryId,"onUpdate:modelValue":e[1]||(e[1]=r=>t.value.categoryId=r),label:"Category",placeholder:"Select category...",required:!0,"error-message":o.value.categoryId||"","help-text":"Choose the most appropriate category",onChange:se},null,8,["model-value","error-message"])])])):v("",!0),t.value?(u(),d("div",Ra,[a("div",Ja,[e[15]||(e[15]=a("label",{class:"admin-form-label admin-form-label--required"},"Product Name",-1)),z(a("input",{"onUpdate:modelValue":e[2]||(e[2]=r=>t.value.name=r),type:"text",class:x(["admin-form-input",{"admin-form-input--error":o.value.name}]),placeholder:"Enter product name",required:"",onInput:le},null,34),[[B,t.value.name]]),o.value.name?(u(),d("div",Ga,[e[14]||(e[14]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),D(" "+p(o.value.name),1)])):v("",!0)])])):v("",!0),a("div",Wa,[a("div",Ka,[e[17]||(e[17]=a("label",{class:"admin-form-label"},"Product Slug",-1)),z(a("input",{"onUpdate:modelValue":e[3]||(e[3]=r=>t.value.slug=r),type:"text",class:x(["admin-form-input",{"admin-form-input--error":o.value.slug}]),placeholder:"Auto-generated from name",onInput:K},null,34),[[B,t.value.slug]]),o.value.slug?(u(),d("div",Qa,[e[16]||(e[16]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),D(" "+p(o.value.slug),1)])):v("",!0),e[18]||(e[18]=a("div",{class:"admin-form-help"}," Leave empty to auto-generate from product name ",-1))])]),a("div",Ya,[a("div",Xa,[e[20]||(e[20]=a("label",{class:"admin-form-label"},"Description",-1)),z(a("textarea",{"onUpdate:modelValue":e[4]||(e[4]=r=>t.value.description=r),class:x(["admin-form-textarea",{"admin-form-textarea--error":o.value.description}]),placeholder:"Enter product description",rows:"4"},null,2),[[B,t.value.description]]),o.value.description?(u(),d("div",Za,[e[19]||(e[19]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),D(" "+p(o.value.description),1)])):v("",!0)])])]),_:1}),C(q,{title:"Pricing & Inventory",description:"Set product pricing and stock information",variant:"card"},{default:V(()=>[a("div",et,[a("div",at,[e[23]||(e[23]=a("label",{class:"admin-form-label admin-form-label--required"},"Price",-1)),a("div",tt,[z(a("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>t.value.priceAmount=r),type:"number",step:"0.01",min:"0",class:x(["admin-form-input",{"admin-form-input--error":o.value.priceAmount}]),placeholder:"0.00",required:""},null,2),[[B,t.value.priceAmount]]),z(a("select",{"onUpdate:modelValue":e[6]||(e[6]=r=>t.value.priceCurrency=r),class:"admin-form-select admin-form-select--addon"},e[21]||(e[21]=[a("option",{value:"UAH"},"UAH",-1),a("option",{value:"USD"},"USD",-1),a("option",{value:"EUR"},"EUR",-1)]),512),[[Pe,t.value.priceCurrency]])]),o.value.priceAmount?(u(),d("div",lt,[e[22]||(e[22]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),D(" "+p(o.value.priceAmount),1)])):v("",!0)]),a("div",st,[e[25]||(e[25]=a("label",{class:"admin-form-label"},"Stock Quantity",-1)),z(a("input",{"onUpdate:modelValue":e[7]||(e[7]=r=>t.value.stock=r),type:"number",min:"0",class:x(["admin-form-input",{"admin-form-input--error":o.value.stock}]),placeholder:"0"},null,2),[[B,t.value.stock]]),o.value.stock?(u(),d("div",nt,[e[24]||(e[24]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),D(" "+p(o.value.stock),1)])):v("",!0),e[26]||(e[26]=a("div",{class:"admin-form-help"}," Number of items available in stock ",-1))])])]),_:1}),C(q,{title:"Product Attributes",description:"Add custom attributes like color, size, material, etc.",variant:"card"},{default:V(()=>[C(xe,{modelValue:t.value.attributes,"onUpdate:modelValue":[e[8]||(e[8]=r=>t.value.attributes=r),ne]},null,8,["modelValue"])]),_:1}),C(q,{title:"Product Images",description:"Upload and manage product images",variant:"card"},{default:V(()=>[C(Ta,{ref_key:"productImagesManager",ref:P,"product-id":c.productId,images:t.value.images||[],"is-create":!1,onImagesUpdated:ce,onMainImageChanged:n,onImageUploaded:s,onImageDeleted:i,onPendingImagesChanged:I},null,8,["product-id","images"])]),_:1}),C(q,{title:"Meta Image (SEO)",description:"Upload a meta image for SEO and social media sharing",variant:"card"},{default:V(()=>[C(Se,{ref_key:"metaImageManager",ref:A,"entity-type":"product","entity-id":c.productId,"current-image":t.value.metaImage,"social-preview-title":t.value.name||"Product Name","social-preview-description":t.value.metaDescription||t.value.description||"Product Description","social-preview-url":`https://marketplace.com/products/${t.value.slug||c.productId}`,"local-mode":c.isCreate,onMetaImageUploaded:Y,onMetaImageRemoved:oe,onMetaImageChanged:ie},null,8,["entity-id","current-image","social-preview-title","social-preview-description","social-preview-url","local-mode"])]),_:1}),C(q,{title:"SEO & Metadata",description:"Optimize your product for search engines",variant:"card"},{default:V(()=>[a("div",ot,[a("div",it,[e[27]||(e[27]=a("label",{class:"admin-form-label"},"Meta Title",-1)),z(a("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>t.value.metaTitle=r),type:"text",class:x(["admin-form-input",{"admin-form-input--error":o.value.metaTitle}]),placeholder:"SEO title for search engines",maxlength:"60"},null,2),[[B,t.value.metaTitle]]),a("div",rt,[a("span",{class:x(he(t.value.metaTitle))},p((t.value.metaTitle||"").length)+"/60 characters ",3)])])]),a("div",dt,[a("div",ut,[e[28]||(e[28]=a("label",{class:"admin-form-label"},"Meta Description",-1)),z(a("textarea",{"onUpdate:modelValue":e[10]||(e[10]=r=>t.value.metaDescription=r),class:x(["admin-form-textarea",{"admin-form-textarea--error":o.value.metaDescription}]),placeholder:"Brief description for search engine results",rows:"3",maxlength:"160"},null,2),[[B,t.value.metaDescription]]),a("div",ct,[a("span",{class:x(Ie(t.value.metaDescription))},p((t.value.metaDescription||"").length)+"/160 characters ",3)])])])]),_:1})])],32)])):v("",!0)]))}},bt=ue(mt,[["__scopeId","data-v-ff4c63b9"]]);export{bt as default};
