<template>
  <div class="modern-sidebar">
    <!-- Mobile Close Button -->
    <button
      class="sidebar-close-btn"
      @click="toggleSidebar"
      v-if="isMobile">
      <i class="fas fa-times"></i>
    </button>

    <!-- Enhanced Header -->
    <div class="sidebar-header-enhanced">
      <div class="logo-section">
        <div class="logo-icon">
          <i class="fas fa-store"></i>
        </div>
        <div class="logo-text">
          <h2 class="brand-name">Klondike</h2>
          <span class="brand-subtitle">Admin Panel</span>
        </div>
      </div>
    </div>

    <!-- Enhanced Navigation -->
    <nav class="sidebar-nav">
      <!-- Dashboard Section -->
      <div class="nav-section">
        <div class="nav-section-header">
          <i class="fas fa-tachometer-alt"></i>
          <span>Overview</span>
        </div>
        <div class="nav-items">
          <a @click.prevent="navigateTo('/admin/dashboard')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/dashboard') }">
            <div class="nav-item-content">
              <i class="fas fa-chart-line nav-item-icon"></i>
              <span class="nav-item-text">Dashboard</span>
            </div>
          </a>
        </div>
      </div>

      <!-- Users Section -->
      <div class="nav-section">
        <div class="nav-section-header">
          <i class="fas fa-users"></i>
          <span>User Management</span>
        </div>
        <div class="nav-items">
          <a v-if="isAdmin"
             @click.prevent="navigateTo('/admin/users')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/users') }">
            <div class="nav-item-content">
              <i class="fas fa-user nav-item-icon"></i>
              <span class="nav-item-text">Users</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/seller-requests')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/seller-requests') }">
            <div class="nav-item-content">
              <i class="fas fa-user-plus nav-item-icon"></i>
              <span class="nav-item-text">Seller Applications</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/companies')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/companies') }">
            <div class="nav-item-content">
              <i class="fas fa-building nav-item-icon"></i>
              <span class="nav-item-text">Companies</span>
            </div>
          </a>
        </div>
      </div>

      <!-- Catalog Section - only for admins -->
      <div v-if="isAdmin" class="nav-section">
        <div class="nav-section-header">
          <i class="fas fa-box"></i>
          <span>Catalog</span>
        </div>
        <div class="nav-items">
          <a @click.prevent="navigateTo('/admin/products')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/products') }">
            <div class="nav-item-content">
              <i class="fas fa-cube nav-item-icon"></i>
              <span class="nav-item-text">Products</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/categories')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/categories') }">
            <div class="nav-item-content">
              <i class="fas fa-tags nav-item-icon"></i>
              <span class="nav-item-text">Categories</span>
            </div>
          </a>
        </div>
      </div>

      <!-- Sales Section - only for admins -->
      <div v-if="isAdmin" class="nav-section">
        <div class="nav-section-header">
          <i class="fas fa-shopping-cart"></i>
          <span>Sales</span>
        </div>
        <div class="nav-items">
          <a @click.prevent="navigateTo('/admin/orders')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/orders') }">
            <div class="nav-item-content">
              <i class="fas fa-receipt nav-item-icon"></i>
              <span class="nav-item-text">Orders</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/reports')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/reports') }">
            <div class="nav-item-content">
              <i class="fas fa-chart-bar nav-item-icon"></i>
              <span class="nav-item-text">Reports</span>
            </div>
          </a>
        </div>
      </div>

      <!-- Communication Section -->
      <div class="nav-section">
        <div class="nav-section-header">
          <i class="fas fa-comments"></i>
          <span>Communication</span>
        </div>
        <div class="nav-items">
          <a @click.prevent="navigateTo('/admin/reviews')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/reviews') }">
            <div class="nav-item-content">
              <i class="fas fa-star nav-item-icon"></i>
              <span class="nav-item-text">Reviews</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/ratings')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/ratings') }">
            <div class="nav-item-content">
              <i class="fas fa-star-half-alt nav-item-icon"></i>
              <span class="nav-item-text">Ratings</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/chats')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/chats') }">
            <div class="nav-item-content">
              <i class="fas fa-comment-dots nav-item-icon"></i>
              <span class="nav-item-text">Chats</span>
            </div>
          </a>
        </div>
      </div>

      <!-- System Section - only for admins -->
      <div v-if="isAdmin" class="nav-section">
        <div class="nav-section-header">
          <i class="fas fa-cogs"></i>
          <span>System</span>
        </div>
        <div class="nav-items">
          <a @click.prevent="navigateTo('/admin/addresses')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/addresses') }">
            <div class="nav-item-content">
              <i class="fas fa-map-marker-alt nav-item-icon"></i>
              <span class="nav-item-text">Addresses</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/settings')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/settings') }">
            <div class="nav-item-content">
              <i class="fas fa-cog nav-item-icon"></i>
              <span class="nav-item-text">Settings</span>
            </div>
          </a>
          <a @click.prevent="navigateTo('/admin/security')"
             class="nav-item"
             :class="{ 'active': isActive('/admin/security') }">
            <div class="nav-item-content">
              <i class="fas fa-shield-alt nav-item-icon"></i>
              <span class="nav-item-text">Security & Logs</span>
            </div>
          </a>
        </div>
      </div>

      <!-- Logout Section -->
      <div class="nav-section nav-section-logout">
        <div class="nav-items">
          <button class="logout-btn" @click="logout" :disabled="isNavigating">
            <div class="nav-item-content">
              <i class="fas fa-sign-out-alt nav-item-icon"></i>
              <span class="nav-item-text">Logout</span>
            </div>
          </button>
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';

const store = useStore();
const router = useRouter();
const route = useRoute();
const isMobile = ref(false);
const isNavigating = ref(false);
const lastNavigationTime = ref(0);
const navigationDebounceTime = 300; // ms

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits(['toggle-sidebar']);

// Computed properties
const isAdmin = computed(() => store.getters['auth/isAdmin']);
const isModerator = computed(() => store.getters['auth/isModerator']);
const isAdminOrModerator = computed(() => store.getters['auth/isAdminOrModerator']);

const toggleSidebar = () => {
  emit('toggle-sidebar');
};

// Check if a route is active
const isActive = (path) => {
  return route.path === path || route.path.startsWith(`${path}/`);
};

// Debounced navigation
const navigateTo = (path) => {
  const now = Date.now();

  // Prevent rapid navigation clicks
  if (isNavigating.value || (now - lastNavigationTime.value < navigationDebounceTime)) {
    return;
  }

  // Don't navigate if we're already on this page
  if (isActive(path)) {
    return;
  }

  isNavigating.value = true;
  lastNavigationTime.value = now;

  // Cancel any pending API requests before navigation
  const apiService = store.getters['loading/apiService'];
  if (apiService) {
    apiService.cancelRequestsForRoute(route.path);
  }

  // Map paths to route names for more reliable navigation
  let routeName = '';
  let params = {};

  // Map common paths to their route names
  if (path === '/admin/dashboard') routeName = 'AdminDashboard';
  else if (path === '/admin/users') routeName = 'AdminUsers';
  else if (path === '/admin/products') routeName = 'AdminProducts';
  else if (path === '/admin/categories') routeName = 'AdminCategories';
  else if (path === '/admin/orders') routeName = 'AdminOrders';
  else if (path === '/admin/seller-requests') routeName = 'AdminSellerRequests';
  else if (path === '/admin/companies') routeName = 'AdminCompanies';
  else if (path === '/admin/reviews') routeName = 'AdminReviews';
  else if (path === '/admin/ratings') routeName = 'AdminRatings';
  else if (path === '/admin/chats') routeName = 'AdminChats';
  else if (path === '/admin/addresses') routeName = 'AdminAddresses';
  else if (path === '/admin/settings') routeName = 'AdminSettings';
  else if (path === '/admin/reports') routeName = 'AdminReports';
  else if (path === '/admin/security') routeName = 'AdminSecurity';
  else routeName = ''; // Default empty for path-based navigation

  // Navigate to the new route
  if (routeName) {
    // Use named route navigation
    router.push({ name: routeName, params }).finally(() => {
      // Reset navigation state after a delay
      setTimeout(() => {
        isNavigating.value = false;
      }, navigationDebounceTime);
    });
  } else {
    // Fallback to path-based navigation
    router.push(path).finally(() => {
      // Reset navigation state after a delay
      setTimeout(() => {
        isNavigating.value = false;
      }, navigationDebounceTime);
    });
  }

  // Close sidebar on mobile after navigation
  if (isMobile.value) {
    toggleSidebar();
  }
};

const logout = async () => {
  if (isNavigating.value) return;

  isNavigating.value = true;
  try {
    await store.dispatch('auth/logout');
    router.push('/login');
  } finally {
    setTimeout(() => {
      isNavigating.value = false;
    }, navigationDebounceTime);
  }
};

// Check if mobile view
const checkMobile = () => {
  isMobile.value = window.innerWidth < 1024;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>

<style scoped>
/* Modern Sidebar Styles */
.modern-sidebar {
  background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
  height: 100vh;
  width: 350px;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Header */
.sidebar-header-enhanced {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.brand-name {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

.brand-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Enhanced Navigation */
.sidebar-nav {
  padding: 1.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.nav-section {
  margin-bottom: 0.5rem;
}

.nav-section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 0.5rem;
}

.nav-section-header i {
  font-size: 0.875rem;
  width: 16px;
  text-align: center;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 1rem;
}

.nav-item {
  display: block;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleY(0);
  transition: transform 0.2s ease;
}

.nav-item:hover::before,
.nav-item.active::before {
  transform: scaleY(1);
}

.nav-item-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  transition: all 0.2s ease;
}

.nav-item:hover .nav-item-content {
  background: rgba(255, 255, 255, 0.08);
  color: white;
  transform: translateX(4px);
}

.nav-item.active .nav-item-content {
  background: rgba(255, 255, 255, 0.12);
  color: white;
  font-weight: 600;
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.nav-item-text {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Logout Section */
.nav-section-logout {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  width: 100%;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.logout-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #ff6b6b;
}

.logout-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.logout-btn .nav-item-content {
  padding: 1rem;
}

/* Mobile Close Button */
.sidebar-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.sidebar-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
  .modern-sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
  }
}

@media screen and (max-width: 768px) {
  .sidebar-header-enhanced {
    padding: 1.5rem 1rem;
  }

  .logo-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .brand-name {
    font-size: 1.25rem;
  }

  .nav-items {
    padding: 0 0.75rem;
  }

  .nav-item-content {
    padding: 0.75rem 0.875rem;
  }
}
</style>
