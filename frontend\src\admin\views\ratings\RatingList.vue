<template>
  <div class="admin-page-content">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-page-title">
        <h1 class="admin-page-heading">
          <i class="fas fa-star"></i>
          Ratings Management
        </h1>
        <nav class="admin-breadcrumb">
          <router-link to="/admin" class="admin-breadcrumb-item">Dashboard</router-link>
          <span class="admin-breadcrumb-separator">/</span>
          <span class="admin-breadcrumb-item admin-breadcrumb-current">Ratings</span>
        </nav>
      </div>
      <div class="admin-page-actions">
        <button
          v-if="selectedRatings.length > 0"
          class="admin-btn admin-btn-danger admin-btn-sm"
          @click="bulkDelete"
          :disabled="actionLoading">
          <i class="fas fa-trash"></i>
          Delete Selected ({{ selectedRatings.length }})
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Ratings"
      search-placeholder="Search by product name, user name, or comment..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="ratings"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading ratings...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchRatings">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Ratings Table -->
    <div class="admin-table-container" v-else>
      <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
        <table class="admin-table admin-table-striped">
            <thead>
              <tr>
                <th style="width: 50px;">
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="allSelected"
                      :indeterminate="someSelected"
                    />
                  </label>
                </th>
                <th style="width: 200px;">Product</th>
                <th style="width: 150px;">User</th>
                <th style="width: 100px;">Service</th>
                <th style="width: 100px;">Delivery</th>
                <th style="width: 100px;">Accuracy</th>
                <th style="width: 120px;">Average</th>
                <th style="width: 120px;">Created</th>
                <th style="width: 180px;">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="rating in ratings" :key="rating.id" class="rating-row">
                <td>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      :value="rating.id"
                      v-model="selectedRatings"
                    />
                  </label>
                </td>
                <td>
                  <div class="product-info">
                    <router-link
                      v-if="rating.productId"
                      :to="{ name: 'AdminProductDetail', params: { id: rating.productId } }"
                      class="product-link">
                      <strong>{{ rating.productName || 'Unknown Product' }}</strong>
                    </router-link>
                    <strong v-else class="has-text-grey">{{ rating.productName || 'Unknown Product' }}</strong>
                    <div v-if="rating.productSlug" class="product-slug">
                      <small class="has-text-grey">{{ rating.productSlug }}</small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="user-info">
                    <router-link
                      v-if="rating.userId"
                      :to="{ name: 'AdminUserDetail', params: { id: rating.userId } }"
                      class="user-link">
                      {{ rating.userName || 'Unknown User' }}
                    </router-link>
                    <span v-else class="has-text-grey">{{ rating.userName || 'Unknown User' }}</span>
                    <div v-if="rating.userEmail" class="user-email">
                      <small class="has-text-grey">{{ rating.userEmail }}</small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="rating-cell">
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.service }">
                        ★
                      </span>
                    </div>
                    <div class="rating-value">{{ rating.service }}/5</div>
                  </div>
                </td>
                <td>
                  <div class="rating-cell">
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.deliveryTime }">
                        ★
                      </span>
                    </div>
                    <div class="rating-value">{{ rating.deliveryTime }}/5</div>
                  </div>
                </td>
                <td>
                  <div class="rating-cell">
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.accuracy }">
                        ★
                      </span>
                    </div>
                    <div class="rating-value">{{ rating.accuracy }}/5</div>
                  </div>
                </td>
                <td>
                  <div class="rating-cell average-rating" :class="getRatingColorClass(rating.averageRating)">
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= Math.round(rating.averageRating) }">
                        ★
                      </span>
                    </div>
                    <div class="rating-value">{{ rating.averageRating ? rating.averageRating.toFixed(1) : '0.0' }}/5</div>
                  </div>
                </td>
                <td>
                  <div class="date-info">
                    <div>{{ formatDate(rating.createdAt) }}</div>
                    <div v-if="rating.updatedAt && rating.updatedAt !== rating.createdAt">
                      <small class="has-text-grey">Updated: {{ formatDate(rating.updatedAt) }}</small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="buttons is-small">

                    <a
                      v-if="rating.productSlug"
                      :href="`/products/${rating.productSlug}`"
                      target="_blank"
                      class="button is-small is-link"
                      title="View Product">
                      <span class="icon">
                        <i class="fas fa-external-link-alt"></i>
                      </span>
                    </a>
                    <button
                      class="button is-small is-warning"
                      @click="editRating(rating.id)"
                      :disabled="actionLoading"
                      title="Edit Rating">
                      <span class="icon">
                        <i class="fas fa-edit"></i>
                      </span>
                    </button>
                    <button
                      class="button is-small is-danger"
                      @click="deleteRating(rating.id)"
                      :disabled="actionLoading"
                      title="Delete Rating">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-changed="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import ratingsService from '@/admin/services/ratings';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import { useAdminSearch } from '@/composables/useAdminSearch';

const router = useRouter();

// Filter configuration
const filterFields = [
  {
    key: 'minRating',
    label: 'Min Rating',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: '', label: 'Any' },
      { value: '1', label: '1+' },
      { value: '2', label: '2+' },
      { value: '3', label: '3+' },
      { value: '4', label: '4+' },
      { value: '5', label: '5' }
    ]
  },
  {
    key: 'maxRating',
    label: 'Max Rating',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: '', label: 'Any' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' }
    ]
  },
  {
    key: 'productSlug',
    label: 'Product',
    type: 'text',
    columnClass: 'is-2',
    placeholder: 'Product slug...'
  },
  {
    key: 'userEmail',
    label: 'User Email',
    type: 'text',
    columnClass: 'is-2',
    placeholder: 'User email...'
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: 'CreatedAt', label: 'Date Created' },
      { value: 'UpdatedAt', label: 'Date Updated' },
      { value: 'AverageRating', label: 'Average Rating' },
      { value: 'Service', label: 'Service Rating' },
      { value: 'DeliveryTime', label: 'Delivery Rating' },
      { value: 'Accuracy', label: 'Accuracy Rating' },
      { value: 'ProductName', label: 'Product' },
      { value: 'UserName', label: 'User' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items: ratings,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: ratingsService.getRatingsWithFilters,
  defaultFilters: {
    minRating: '',
    maxRating: '',
    productSlug: '',
    userEmail: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Additional reactive data
const actionLoading = ref(false);
const selectedRatings = ref([]);

// Computed
const allSelected = computed(() => {
  return ratings.value.length > 0 && selectedRatings.value.length === ratings.value.length;
});

const someSelected = computed(() => {
  return selectedRatings.value.length > 0 && selectedRatings.value.length < ratings.value.length;
});

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else {
      filters[key] = filterFields.find(f => f.key === key)?.options?.[0]?.value || '';
    }
  });
  fetchData(1);
};

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedRatings.value = [];
  } else {
    selectedRatings.value = ratings.value.map(rating => rating.id);
  }
};

const deleteRating = async (id) => {
  if (!confirm('Are you sure you want to delete this rating?')) {
    return;
  }

  actionLoading.value = true;
  try {
    await ratingsService.deleteRating(id);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete rating';
  } finally {
    actionLoading.value = false;
  }
};

const bulkDelete = async () => {
  if (!confirm(`Are you sure you want to delete ${selectedRatings.value.length} selected ratings?`)) {
    return;
  }

  actionLoading.value = true;
  try {
    await ratingsService.bulkDeleteRatings(selectedRatings.value);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete ratings';
  } finally {
    actionLoading.value = false;
  }
};



const editRating = (id) => {
  router.push({ name: 'AdminRatingEdit', params: { id } });
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('uk-UA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getRatingColorClass = (rating) => {
  if (rating >= 4.5) return 'rating-excellent';
  if (rating >= 3.5) return 'rating-good';
  if (rating >= 2.5) return 'rating-average';
  return 'rating-poor';
};
</script>

<style scoped>
.rating-list {
  padding: 1rem;
}

.title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.table th {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.table td {
  border-color: var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: rgba(50, 115, 220, 0.1);
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1em;
}

.star.is-filled {
  color: #ffd700;
}

.checkbox input[type="checkbox"] {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

.button.is-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.button.is-danger:hover {
  background-color: var(--danger-color-dark);
}

.level {
  margin-bottom: 1.5rem;
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}

/* Покращені стилі для таблиці */
.rating-row {
  transition: background-color 0.2s ease;
}

.rating-row:hover {
  background-color: rgba(50, 115, 220, 0.1) !important;
}

.product-info, .user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-info {
  color: #000 !important;
}

.user-info .user-link {
  color: #000 !important;
}

.user-info .user-email {
  color: #666 !important;
}

.product-link, .user-link {
  color: var(--link-color);
  font-weight: 600;
  text-decoration: none;
}

.product-link:hover, .user-link:hover {
  color: var(--link-color-dark);
  text-decoration: underline;
}

.product-slug, .user-email {
  font-size: 0.85em;
}

.rating-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.rating-value {
  font-size: 0.85em;
  font-weight: 500;
  color: var(--text-secondary);
}

.average-rating {
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
}

.rating-excellent {
  background-color: rgba(72, 187, 120, 0.1);
  border: 1px solid rgba(72, 187, 120, 0.3);
}

.rating-good {
  background-color: rgba(56, 178, 172, 0.1);
  border: 1px solid rgba(56, 178, 172, 0.3);
}

.rating-average {
  background-color: rgba(237, 137, 54, 0.1);
  border: 1px solid rgba(237, 137, 54, 0.3);
}

.rating-poor {
  background-color: rgba(245, 101, 101, 0.1);
  border: 1px solid rgba(245, 101, 101, 0.3);
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.9em;
}

.buttons.is-small {
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
}

.button.is-link {
  background-color: #000 !important;
  border-color: #000 !important;
  color: white !important;
}

.button.is-link:hover {
  background-color: #333 !important;
  border-color: #333 !important;
}

.button.is-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.button.is-warning:hover {
  background-color: var(--warning-color-dark);
}

/* Responsive design */
@media (max-width: 1200px) {
  .table-container {
    font-size: 0.9em;
  }

  .rating-value {
    display: none;
  }

  .product-slug,
  .user-email {
    display: none;
  }
}

@media (max-width: 768px) {
  .table-container {
    font-size: 0.8em;
  }

  .stars {
    gap: 1px;
  }

  .star {
    font-size: 0.9em;
  }

  .buttons.is-small .button span:not(.icon) {
    display: none;
  }

  .date-info small {
    display: none;
  }
}
</style>

<style scoped>
/* Import admin styles */
@import '@/assets/css/admin/admin.css';
</style>
