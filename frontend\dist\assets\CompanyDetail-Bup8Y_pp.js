import{_ as D,g as h,h as A,c as d,o as l,a as s,k as b,d as p,t as c,n as C,m as J,F as E,p as F,i as L,j as K,r as V,b as S,w as N,l as Q,s as j,x as X,D as Y,f as Z}from"./index-L-hJxM_5.js";import{c as P}from"./companies-B97mkaMy.js";const ss={class:"admin-card"},as={class:"admin-card-content"},es={key:0,class:"company-image-section"},ts={class:"image-container"},ns={class:"image-wrapper"},ls=["src","alt"],os={class:"image-overlay"},is=["href"],ds={class:"admin-info-grid"},cs={class:"admin-info-group"},rs={class:"admin-info-value"},us={class:"admin-info-group"},ms={class:"admin-info-value admin-info-value-code"},vs={class:"admin-info-group"},fs={class:"admin-info-value"},ps=["href"],ys={key:1,class:"admin-text-muted"},gs={class:"admin-info-group"},bs={class:"admin-info-value"},_s=["href"],$s={key:1,class:"admin-text-muted"},hs={class:"admin-info-group admin-info-group-full"},ks={class:"admin-info-value admin-info-value-multiline"},Cs={class:"admin-info-group admin-info-group-full"},ws={class:"admin-info-value"},Is={key:0,class:"address-display"},As={key:0,class:"address-line"},xs={key:1,class:"address-line"},Ss={key:1,class:"admin-text-muted"},Ts={key:1,class:"meta-section"},Ns={class:"admin-info-grid"},Ps={key:0,class:"admin-info-group"},Ds={class:"admin-info-value"},Os={key:1,class:"admin-info-group admin-info-group-full"},Us={class:"admin-info-value admin-info-value-multiline"},Rs={key:2,class:"admin-info-group admin-info-group-full"},Ms={class:"admin-info-value"},Es={class:"meta-image-container"},Fs={class:"meta-image-wrapper"},js=["src","alt"],Ls={class:"meta-image-overlay"},Vs=["href"],Bs={class:"status-badges"},zs={key:0,class:"admin-badge admin-badge-info"},qs={class:"image-modal-header"},Ws={class:"image-modal-title"},Hs={class:"image-modal-content"},Js=["src","alt"],Ks={class:"image-modal-footer"},Gs=["href"],Qs={__name:"CompanyInfoCard",props:{company:{type:Object,required:!0,default:()=>({})}},setup(u){const y=u,r=h(!1),v=h(""),o=h(""),k=A(()=>y.company.addressStreet||y.company.addressCity||y.company.addressRegion||y.company.addressPostalCode),_=A(()=>[y.company.addressCity,y.company.addressRegion,y.company.addressPostalCode].filter(Boolean).join(", ")),g=A(()=>y.company.imageUrl&&!y.company.imageUrl.includes("example.com")?y.company.imageUrl:null),w=A(()=>{const n=y.company.metaImage||y.company.metaImageUrl;return n&&!n.includes("example.com")?n:null}),x=(n,t)=>{v.value=n,o.value=t,r.value=!0},$=()=>{r.value=!1,v.value="",o.value=""};return(n,t)=>(l(),d("div",ss,[t[27]||(t[27]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-building admin-card-icon"}),p(" Company Information ")])],-1)),s("div",as,[g.value?(l(),d("div",es,[s("div",ts,[s("div",ns,[s("img",{src:g.value,alt:u.company.name,class:"company-image"},null,8,ls),s("div",os,[s("button",{onClick:t[0]||(t[0]=I=>x(g.value,u.company.name+" Logo")),class:"image-action-btn",title:"View full size"},t[3]||(t[3]=[s("i",{class:"fas fa-expand"},null,-1)])),s("a",{href:g.value,target:"_blank",class:"image-action-btn",title:"Open in new tab"},t[4]||(t[4]=[s("i",{class:"fas fa-external-link-alt"},null,-1)]),8,is)])]),t[5]||(t[5]=s("div",{class:"image-info"},[s("span",{class:"image-label"},[s("i",{class:"fas fa-image"}),p(" Company Logo ")])],-1))])])):b("",!0),s("div",ds,[s("div",cs,[t[6]||(t[6]=s("label",{class:"admin-info-label"},"Company Name",-1)),s("div",rs,c(u.company.name||"N/A"),1)]),s("div",us,[t[7]||(t[7]=s("label",{class:"admin-info-label"},"Slug",-1)),s("div",ms,c(u.company.slug||"N/A"),1)]),s("div",vs,[t[9]||(t[9]=s("label",{class:"admin-info-label"},"Contact Email",-1)),s("div",fs,[u.company.contactEmail?(l(),d("a",{key:0,href:`mailto:${u.company.contactEmail}`,class:"admin-link"},[t[8]||(t[8]=s("i",{class:"fas fa-envelope"},null,-1)),p(" "+c(u.company.contactEmail),1)],8,ps)):(l(),d("span",ys,"N/A"))])]),s("div",gs,[t[11]||(t[11]=s("label",{class:"admin-info-label"},"Contact Phone",-1)),s("div",bs,[u.company.contactPhone?(l(),d("a",{key:0,href:`tel:${u.company.contactPhone}`,class:"admin-link"},[t[10]||(t[10]=s("i",{class:"fas fa-phone"},null,-1)),p(" "+c(u.company.contactPhone),1)],8,_s)):(l(),d("span",$s,"N/A"))])])]),s("div",hs,[t[12]||(t[12]=s("label",{class:"admin-info-label"},"Description",-1)),s("div",ks,c(u.company.description||"No description provided"),1)]),s("div",Cs,[t[15]||(t[15]=s("label",{class:"admin-info-label"},"Address",-1)),s("div",ws,[k.value?(l(),d("div",Is,[u.company.addressStreet?(l(),d("div",As,[t[13]||(t[13]=s("i",{class:"fas fa-map-marker-alt"},null,-1)),p(" "+c(u.company.addressStreet),1)])):b("",!0),_.value?(l(),d("div",xs,[t[14]||(t[14]=s("i",{class:"fas fa-city"},null,-1)),p(" "+c(_.value),1)])):b("",!0)])):(l(),d("span",Ss,"No address provided"))])]),u.company.metaTitle||u.company.metaDescription?(l(),d("div",Ts,[t[22]||(t[22]=s("h4",{class:"meta-section-title"},[s("i",{class:"fas fa-tags"}),p(" SEO Meta Information ")],-1)),s("div",Ns,[u.company.metaTitle?(l(),d("div",Ps,[t[16]||(t[16]=s("label",{class:"admin-info-label"},"Meta Title",-1)),s("div",Ds,c(u.company.metaTitle),1)])):b("",!0),u.company.metaDescription?(l(),d("div",Os,[t[17]||(t[17]=s("label",{class:"admin-info-label"},"Meta Description",-1)),s("div",Us,c(u.company.metaDescription),1)])):b("",!0),w.value?(l(),d("div",Rs,[t[21]||(t[21]=s("label",{class:"admin-info-label"},"Meta Image (SEO)",-1)),s("div",Ms,[s("div",Es,[s("div",Fs,[s("img",{src:w.value,alt:u.company.name+" Meta Image",class:"meta-image"},null,8,js),s("div",Ls,[s("button",{onClick:t[1]||(t[1]=I=>x(w.value,u.company.name+" Meta Image")),class:"image-action-btn",title:"View full size"},t[18]||(t[18]=[s("i",{class:"fas fa-expand"},null,-1)])),s("a",{href:w.value,target:"_blank",class:"image-action-btn",title:"Open in new tab"},t[19]||(t[19]=[s("i",{class:"fas fa-external-link-alt"},null,-1)]),8,Vs)])]),t[20]||(t[20]=s("div",{class:"meta-image-info"},[s("span",{class:"image-label"},[s("i",{class:"fas fa-tags"}),p(" SEO Meta Image ")]),s("span",{class:"image-description"},"Used for social media sharing and search results")],-1))])])])):b("",!0)])])):b("",!0),s("div",Bs,[s("span",{class:C(["admin-badge",u.company.isApproved?"admin-badge-success":"admin-badge-warning"])},[s("i",{class:C(["fas",u.company.isApproved?"fa-check":"fa-clock"])},null,2),p(" "+c(u.company.isApproved?"Approved":"Pending Approval"),1)],2),u.company.isFeatured?(l(),d("span",zs,t[23]||(t[23]=[s("i",{class:"fas fa-star"},null,-1),p(" Featured ")]))):b("",!0)])]),r.value?(l(),d("div",{key:0,class:"image-modal-overlay",onClick:$},[s("div",{class:"image-modal",onClick:t[2]||(t[2]=J(()=>{},["stop"]))},[s("div",qs,[s("h3",Ws,c(o.value),1),s("button",{class:"image-modal-close",onClick:$},t[24]||(t[24]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",Hs,[s("img",{src:v.value,alt:o.value,class:"modal-image"},null,8,Js)]),s("div",Ks,[s("a",{href:v.value,target:"_blank",class:"admin-btn admin-btn-secondary"},t[25]||(t[25]=[s("i",{class:"fas fa-external-link-alt"},null,-1),p(" Open in New Tab ")]),8,Gs),s("button",{class:"admin-btn admin-btn-primary",onClick:$},t[26]||(t[26]=[s("i",{class:"fas fa-times"},null,-1),p(" Close ")]))])])])):b("",!0)]))}},Xs=D(Qs,[["__scopeId","data-v-********"]]),Ys={class:"admin-card"},Zs={class:"admin-card-content"},sa={class:"finance-section"},aa={class:"admin-info-grid"},ea={class:"admin-info-group"},ta={class:"admin-info-value"},na={class:"admin-info-group"},la={class:"admin-info-value admin-info-value-code"},oa={class:"admin-info-group admin-info-group-full"},ia={class:"admin-info-value admin-info-value-account"},da={key:0,class:"account-number"},ca={key:1,class:"admin-text-muted"},ra={class:"finance-section"},ua={class:"admin-info-grid"},ma={class:"admin-info-group"},va={class:"admin-info-value admin-info-value-code"},fa={key:0,class:"finance-section"},pa={class:"admin-info-group"},ya={class:"admin-info-value admin-info-value-multiline"},ga={class:"finance-timestamps"},ba={class:"timestamp-item"},_a={class:"timestamp-value"},$a={key:0,class:"timestamp-item"},ha={class:"timestamp-value"},ka={__name:"CompanyFinanceCard",props:{finance:{type:Object,required:!0,default:()=>({})}},setup(u){const y=v=>v?v.replace(/\s/g,"").replace(/(.{4})/g,"$1 ").trim():"N/A",r=v=>v?new Date(v).toLocaleString("uk-UA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A";return(v,o)=>(l(),d("div",Ys,[o[10]||(o[10]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-credit-card admin-card-icon"}),p(" Financial Information ")])],-1)),s("div",Zs,[s("div",sa,[o[3]||(o[3]=s("h4",{class:"finance-section-title"},[s("i",{class:"fas fa-university"}),p(" Banking Details ")],-1)),s("div",aa,[s("div",ea,[o[0]||(o[0]=s("label",{class:"admin-info-label"},"Bank Name",-1)),s("div",ta,c(u.finance.bankName||"N/A"),1)]),s("div",na,[o[1]||(o[1]=s("label",{class:"admin-info-label"},"Bank Code",-1)),s("div",la,c(u.finance.bankCode||"N/A"),1)]),s("div",oa,[o[2]||(o[2]=s("label",{class:"admin-info-label"},"Bank Account",-1)),s("div",ia,[u.finance.bankAccount?(l(),d("span",da,c(y(u.finance.bankAccount)),1)):(l(),d("span",ca,"N/A"))])])])]),s("div",ra,[o[5]||(o[5]=s("h4",{class:"finance-section-title"},[s("i",{class:"fas fa-receipt"}),p(" Tax Information ")],-1)),s("div",ua,[s("div",ma,[o[4]||(o[4]=s("label",{class:"admin-info-label"},"Tax ID",-1)),s("div",va,c(u.finance.taxId||"N/A"),1)])])]),u.finance.paymentDetails?(l(),d("div",fa,[o[6]||(o[6]=s("h4",{class:"finance-section-title"},[s("i",{class:"fas fa-money-check-alt"}),p(" Payment Details ")],-1)),s("div",pa,[s("div",ya,c(u.finance.paymentDetails),1)])])):b("",!0),s("div",ga,[s("div",ba,[o[7]||(o[7]=s("label",{class:"timestamp-label"},"Created",-1)),s("span",_a,c(r(u.finance.createdAt)),1)]),u.finance.updatedAt?(l(),d("div",$a,[o[8]||(o[8]=s("label",{class:"timestamp-label"},"Last Updated",-1)),s("span",ha,c(r(u.finance.updatedAt)),1)])):b("",!0)]),o[9]||(o[9]=s("div",{class:"security-notice"},[s("i",{class:"fas fa-shield-alt"}),s("span",null,"Financial information is encrypted and securely stored")],-1))])]))}},Ca=D(ka,[["__scopeId","data-v-eff65f4b"]]),wa={class:"admin-card"},Ia={class:"admin-card-content"},Aa={class:"schedule-table-wrapper"},xa={class:"admin-table schedule-table"},Sa={class:"day-cell"},Ta={class:"day-info"},Na={class:"day-name"},Pa={class:"day-short"},Da={class:"status-cell"},Oa={class:"time-cell"},Ua={key:0,class:"time-range"},Ra={class:"time-start"},Ma={class:"time-end"},Ea={key:1,class:"closed-text"},Fa={class:"current-status"},ja={class:"current-status-content"},La={key:0,class:"next-change"},Va={__name:"CompanyScheduleTable",props:{schedule:{type:Array,required:!0,default:()=>[]}},setup(u){const y=u,r=[{value:0,name:"Monday",short:"Mon"},{value:1,name:"Tuesday",short:"Tue"},{value:2,name:"Wednesday",short:"Wed"},{value:3,name:"Thursday",short:"Thu"},{value:4,name:"Friday",short:"Fri"},{value:5,name:"Saturday",short:"Sat"},{value:6,name:"Sunday",short:"Sun"}],v=A(()=>{const a={};return y.schedule.forEach(e=>{a[e.day]=e}),a}),o=a=>{const e=v.value[a];return!e||e.isClosed},k=a=>{const e=v.value[a];return(e==null?void 0:e.openTime)||"00:00:00"},_=a=>{const e=v.value[a];return(e==null?void 0:e.closeTime)||"00:00:00"},g=a=>o(a)?"status-closed":"status-open",w=a=>o(a)?"fa-times-circle":"fa-check-circle",x=a=>o(a)?"Closed":"Open",$=a=>{if(!a)return"--:--";const e=a.split(":");if(e.length>=2){const i=parseInt(e[0]),m=e[1];return`${i.toString().padStart(2,"0")}:${m}`}return a},n=A(()=>{const a=new Date,e=(a.getDay()+6)%7,i=a.getHours()*60+a.getMinutes(),m=v.value[e];if(!m||m.isClosed)return{isOpen:!1,reason:"closed_today"};const T=t(m.openTime),M=t(m.closeTime);return i>=T&&i<M?{isOpen:!0,closeTime:M}:i<T?{isOpen:!1,reason:"not_open_yet",openTime:T}:{isOpen:!1,reason:"closed_for_day"}}),t=a=>{if(!a)return 0;const e=a.split(":");return parseInt(e[0])*60+parseInt(e[1])},I=a=>{const e=Math.floor(a/60),i=a%60;return`${e.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`},O=A(()=>n.value.isOpen?"status-open":"status-closed"),U=A(()=>n.value.isOpen?"fa-check-circle":"fa-times-circle"),R=A(()=>{if(n.value.isOpen)return"Currently Open";switch(n.value.reason){case"closed_today":return"Closed Today";case"not_open_yet":return"Opens Later Today";case"closed_for_day":return"Closed for Today";default:return"Closed"}}),f=A(()=>n.value.isOpen&&n.value.closeTime?`Closes at ${I(n.value.closeTime)}`:n.value.reason==="not_open_yet"&&n.value.openTime?`Opens at ${I(n.value.openTime)}`:null);return(a,e)=>(l(),d("div",wa,[e[3]||(e[3]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-clock admin-card-icon"}),p(" Working Schedule ")])],-1)),s("div",Ia,[s("div",Aa,[s("table",xa,[e[1]||(e[1]=s("thead",null,[s("tr",null,[s("th",{class:"day-column"},"Day"),s("th",{class:"status-column"},"Status"),s("th",{class:"time-column"},"Working Hours")])],-1)),s("tbody",null,[(l(),d(E,null,F(r,i=>s("tr",{key:i.value,class:"schedule-row"},[s("td",Sa,[s("div",Ta,[s("span",Na,c(i.name),1),s("span",Pa,c(i.short),1)])]),s("td",Da,[s("span",{class:C(["status-badge",g(i.value)])},[s("i",{class:C(["fas",w(i.value)])},null,2),p(" "+c(x(i.value)),1)],2)]),s("td",Oa,[o(i.value)?(l(),d("span",Ea,"Closed")):(l(),d("div",Ua,[s("span",Ra,c($(k(i.value))),1),e[0]||(e[0]=s("span",{class:"time-separator"},"—",-1)),s("span",Ma,c($(_(i.value))),1)]))])])),64))])])]),s("div",Fa,[e[2]||(e[2]=s("div",{class:"current-status-header"},[s("i",{class:"fas fa-info-circle"}),s("span",null,"Current Status")],-1)),s("div",ja,[s("span",{class:C(["status-badge",O.value])},[s("i",{class:C(["fas",U.value])},null,2),p(" "+c(R.value),1)],2),f.value?(l(),d("span",La,c(f.value),1)):b("",!0)])])])]))}},Ba=D(Va,[["__scopeId","data-v-93904998"]]),za={class:"admin-card"},qa={class:"admin-card-header"},Wa={class:"admin-card-actions"},Ha=["disabled"],Ja={class:"admin-card-content"},Ka={key:0,class:"loading-state"},Ga={class:"skeleton-table"},Qa={key:1,class:"error-state"},Xa=["disabled"],Ya={key:2,class:"empty-state"},Za={key:3,class:"users-table-wrapper"},se={class:"admin-table users-table"},ae={class:"user-cell"},ee={class:"user-info"},te={class:"user-details"},ne={class:"user-name"},le={class:"user-email"},oe={class:"role-cell"},ie={class:"status-cell"},de={class:"date-cell"},ce={class:"date-info"},re={class:"date-primary"},ue={class:"date-secondary"},me={class:"actions-cell"},ve={class:"action-buttons"},fe=["onClick"],pe=["onClick"],ye={key:4,class:"users-summary"},ge={class:"summary-item"},be={class:"summary-value"},_e={class:"summary-item"},$e={class:"summary-value"},he={class:"summary-item"},ke={class:"summary-value"},Ce={__name:"CompanyUsersTable",props:{companyId:{type:String,required:!0}},setup(u){const y=u,r=h([]),v=h(!1),o=h(null),k=A(()=>r.value.filter(f=>f.isApproved).length),_=A(()=>r.value.filter(f=>!f.isApproved).length),g=async(f=1,a="")=>{v.value=!0,o.value=null;try{const e={page:f,pageSize:15};a.trim()&&(e.filter=a.trim());const i=await P.getCompanyUsers(y.companyId,e);console.log("CompanyUsersTable received response:",i),i&&i.success&&i.data?(r.value=i.data.data||[],console.log("Users loaded:",r.value.length)):(console.error("Invalid response structure:",i),r.value=[],o.value="Invalid response format from server")}catch(e){console.error("Error fetching users:",e),o.value=e.message||"Failed to load company users",r.value=[]}finally{v.value=!1}},w=f=>{if(f==null)return"Unknown";switch(f){case 0:return"Buyer";case 1:return"Seller";case 2:return"Seller Owner";case 3:return"Moderator";case 4:return"Admin";default:return"Unknown"}},x=f=>{switch(f){case 4:return"role-admin";case 3:return"role-moderator";case 2:return"role-owner";case 1:return"role-seller";case 0:return"role-buyer";default:return"role-unknown"}},$=f=>{switch(f){case 4:return"fa-crown";case 3:return"fa-shield-alt";case 2:return"fa-user-tie";case 1:return"fa-store";case 0:return"fa-user";default:return"fa-question"}},n=f=>f?"status-active":"status-pending",t=f=>f?"fa-check-circle":"fa-clock",I=f=>f?new Date(f).toLocaleDateString("uk-UA"):"N/A",O=f=>f?new Date(f).toLocaleTimeString("uk-UA",{hour:"2-digit",minute:"2-digit"}):"",U=f=>{console.log("Edit role for user:",f)},R=f=>{confirm(`Are you sure you want to remove ${f.username} from this company?`)&&console.log("Remove user:",f)};return L(()=>{g()}),K(()=>{P.clearCache(`/api/admin/companies/${y.companyId}/users`)}),(f,a)=>{const e=V("router-link");return l(),d("div",za,[s("div",qa,[a[1]||(a[1]=s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-users admin-card-icon"}),p(" Company Users ")],-1)),s("div",Wa,[s("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",onClick:g,disabled:v.value},[s("i",{class:C(["fas fa-sync-alt",{"fa-spin":v.value}])},null,2)],8,Ha)])]),s("div",Ja,[v.value?(l(),d("div",Ka,[s("div",Ga,[(l(),d(E,null,F(5,i=>s("div",{class:"skeleton-row",key:i},a[2]||(a[2]=[s("div",{class:"skeleton-cell skeleton-avatar"},null,-1),s("div",{class:"skeleton-cell skeleton-text"},null,-1),s("div",{class:"skeleton-cell skeleton-text-short"},null,-1),s("div",{class:"skeleton-cell skeleton-text-short"},null,-1)]))),64))])])):o.value?(l(),d("div",Qa,[a[4]||(a[4]=s("i",{class:"fas fa-exclamation-triangle"},null,-1)),s("span",null,c(o.value),1),s("button",{onClick:a[0]||(a[0]=i=>g()),class:"admin-btn admin-btn-sm admin-btn-primary retry-btn",disabled:v.value},a[3]||(a[3]=[s("i",{class:"fas fa-redo"},null,-1),p(" Retry ")]),8,Xa)])):r.value.length?(l(),d("div",Za,[s("table",se,[a[10]||(a[10]=s("thead",null,[s("tr",null,[s("th",null,"User"),s("th",null,"Role"),s("th",null,"Status"),s("th",null,"Joined"),s("th",null,"Actions")])],-1)),s("tbody",null,[(l(!0),d(E,null,F(r.value,i=>{var m,T,M,B,z,q,W,H;return l(),d("tr",{key:i.id,class:"user-row"},[s("td",ae,[s("div",ee,[a[6]||(a[6]=s("div",{class:"user-avatar"},[s("i",{class:"fas fa-user"})],-1)),s("div",te,[s("div",ne,c(((m=i.user)==null?void 0:m.username)||"N/A"),1),s("div",le,c(((T=i.user)==null?void 0:T.email)||"N/A"),1)])])]),s("td",oe,[s("span",{class:C(["role-badge",x((M=i.user)==null?void 0:M.role)])},[s("i",{class:C(["fas",$((B=i.user)==null?void 0:B.role)])},null,2),p(" "+c(w((z=i.user)==null?void 0:z.role)),1)],2)]),s("td",ie,[s("span",{class:C(["status-badge",n((q=i.user)==null?void 0:q.isApproved)])},[s("i",{class:C(["fas",t((W=i.user)==null?void 0:W.isApproved)])},null,2),p(" "+c((H=i.user)!=null&&H.isApproved?"Active":"Pending"),1)],2)]),s("td",de,[s("div",ce,[s("div",re,c(I(i.createdAt)),1),s("div",ue,c(O(i.createdAt)),1)])]),s("td",me,[s("div",ve,[S(e,{to:`/admin/users/${i.userId}`,class:"admin-btn admin-btn-xs admin-btn-primary",title:"View User"},{default:N(()=>a[7]||(a[7]=[s("i",{class:"fas fa-eye"},null,-1)])),_:2},1032,["to"]),s("button",{class:"admin-btn admin-btn-xs admin-btn-secondary",onClick:G=>U(i),title:"Edit Role"},a[8]||(a[8]=[s("i",{class:"fas fa-edit"},null,-1)]),8,fe),i.isOwner?b("",!0):(l(),d("button",{key:0,class:"admin-btn admin-btn-xs admin-btn-danger",onClick:G=>R(i),title:"Remove User"},a[9]||(a[9]=[s("i",{class:"fas fa-times"},null,-1)]),8,pe))])])])}),128))])])])):(l(),d("div",Ya,a[5]||(a[5]=[s("i",{class:"fas fa-user-slash"},null,-1),s("span",null,"No users found for this company",-1)]))),r.value.length?(l(),d("div",ye,[s("div",ge,[a[11]||(a[11]=s("span",{class:"summary-label"},"Total Users:",-1)),s("span",be,c(r.value.length),1)]),s("div",_e,[a[12]||(a[12]=s("span",{class:"summary-label"},"Active:",-1)),s("span",$e,c(k.value),1)]),s("div",he,[a[13]||(a[13]=s("span",{class:"summary-label"},"Pending:",-1)),s("span",ke,c(_.value),1)])])):b("",!0)])])}}},we=D(Ce,[["__scopeId","data-v-328235a5"]]),Ie={class:"admin-card"},Ae={class:"admin-card-header"},xe={class:"admin-card-actions"},Se=["disabled"],Te={class:"admin-card-content"},Ne={key:0,class:"loading-state"},Pe={class:"skeleton-table"},De={key:1,class:"error-state"},Oe=["disabled"],Ue={key:2,class:"empty-state"},Re={key:3,class:"products-table-wrapper"},Me={class:"admin-table products-table"},Ee={class:"product-cell"},Fe={class:"product-info"},je={class:"product-image"},Le=["src","alt"],Ve={key:1,class:"fas fa-image placeholder-icon"},Be={class:"product-details"},ze={class:"product-name"},qe={class:"product-sku"},We={class:"category-cell"},He={class:"category-badge"},Je={class:"price-cell"},Ke={class:"price-info"},Ge={class:"price-current"},Qe={class:"stock-cell"},Xe={class:"stock-info"},Ye={class:"stock-status"},Ze={class:"status-cell"},st={class:"actions-cell"},at={class:"action-buttons"},et={key:4,class:"pagination-wrapper"},tt={class:"pagination"},nt=["disabled"],lt={class:"pagination-info"},ot=["disabled"],it={key:5,class:"products-summary"},dt={class:"summary-item"},ct={class:"summary-value"},rt={class:"summary-item"},ut={class:"summary-value"},mt={class:"summary-item"},vt={class:"summary-value"},ft=10,pt={__name:"CompanyProductsTable",props:{companyId:{type:String,required:!0}},setup(u){const y=u,r=h([]),v=h(!1),o=h(null),k=h(1),_=h(1),g=h(0),w=A(()=>r.value.filter(a=>a.isActive).length),x=A(()=>r.value.filter(a=>a.stockQuantity<10).length),$=async(a=1,e="")=>{v.value=!0,o.value=null;try{const i={page:a,pageSize:ft};e.trim()&&(i.filter=e.trim());const m=await P.getCompanyProducts(y.companyId,i);console.log("CompanyProductsTable received response:",m),m&&m.success&&m.data?(r.value=m.data.data||[],_.value=m.data.lastPage||1,g.value=m.data.total||0,k.value=a,console.log("Products loaded:",r.value.length)):(console.error("Invalid response structure:",m),r.value=[],_.value=1,g.value=0,o.value="Invalid response format from server")}catch(i){console.error("Error fetching products:",i),o.value=i.message||"Failed to load company products",r.value=[],_.value=1,g.value=0}finally{v.value=!1}},n=a=>{a>=1&&a<=_.value&&$(a)},t=(a,e="UAH")=>!a&&a!==0?"N/A":new Intl.NumberFormat("uk-UA",{style:"currency",currency:e||"UAH"}).format(a),I=a=>a===0?"stock-out":a<10?"stock-low":"stock-good",O=a=>a===0?"Out of Stock":a<10?"Low Stock":"In Stock",U=a=>{switch(a){case 0:return"status-pending";case 1:return"status-active";case 2:return"status-inactive";default:return"status-pending"}},R=a=>{switch(a){case 0:return"fa-clock";case 1:return"fa-check-circle";case 2:return"fa-times-circle";default:return"fa-clock"}},f=a=>{switch(a){case 0:return"PENDING";case 1:return"APPROVED";case 2:return"REJECTED";default:return"UNKNOWN"}};return L(()=>{$()}),K(()=>{P.clearCache(`/api/admin/companies/${y.companyId}/products`)}),(a,e)=>{const i=V("router-link");return l(),d("div",Ie,[s("div",Ae,[e[4]||(e[4]=s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-box admin-card-icon"}),p(" Company Products ")],-1)),s("div",xe,[s("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",onClick:$,disabled:v.value},[s("i",{class:C(["fas fa-sync-alt",{"fa-spin":v.value}])},null,2)],8,Se),S(i,{to:`/admin/products?companyId=${u.companyId}`,class:"admin-btn admin-btn-sm admin-btn-primary"},{default:N(()=>e[3]||(e[3]=[s("i",{class:"fas fa-external-link-alt"},null,-1),p(" View All ")])),_:1},8,["to"])])]),s("div",Te,[v.value?(l(),d("div",Ne,[s("div",Pe,[(l(),d(E,null,F(6,m=>s("div",{class:"skeleton-row",key:m},e[5]||(e[5]=[Q('<div class="skeleton-cell skeleton-image" data-v-144cdb0c></div><div class="skeleton-cell skeleton-text" data-v-144cdb0c></div><div class="skeleton-cell skeleton-text-short" data-v-144cdb0c></div><div class="skeleton-cell skeleton-text-short" data-v-144cdb0c></div><div class="skeleton-cell skeleton-text-short" data-v-144cdb0c></div>',5)]))),64))])])):o.value?(l(),d("div",De,[e[7]||(e[7]=s("i",{class:"fas fa-exclamation-triangle"},null,-1)),s("span",null,c(o.value),1),s("button",{onClick:e[0]||(e[0]=m=>$()),class:"admin-btn admin-btn-sm admin-btn-primary retry-btn",disabled:v.value},e[6]||(e[6]=[s("i",{class:"fas fa-redo"},null,-1),p(" Retry ")]),8,Oe)])):r.value.length?(l(),d("div",Re,[s("table",Me,[e[11]||(e[11]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Category"),s("th",null,"Price"),s("th",null,"Stock"),s("th",null,"Status"),s("th",null,"Actions")])],-1)),s("tbody",null,[(l(!0),d(E,null,F(r.value,m=>(l(),d("tr",{key:m.id,class:"product-row"},[s("td",Ee,[s("div",Fe,[s("div",je,[m.metaImage?(l(),d("img",{key:0,src:m.metaImage,alt:m.name,class:"product-thumbnail"},null,8,Le)):(l(),d("i",Ve))]),s("div",Be,[s("div",ze,c(m.name||"N/A"),1),s("div",qe,"SKU: "+c(m.id||"N/A"),1)])])]),s("td",We,[s("span",He,c(m.categoryName||"Uncategorized"),1)]),s("td",Je,[s("div",Ke,[s("div",Ge,c(t(m.priceAmount,m.priceCurrency)),1)])]),s("td",Qe,[s("div",Xe,[s("span",{class:C(["stock-badge",I(m.stock)])},c(m.stock||0),3),s("span",Ye,c(O(m.stock)),1)])]),s("td",Ze,[s("span",{class:C(["status-badge",U(m.status)])},[s("i",{class:C(["fas",R(m.status)])},null,2),p(" "+c(f(m.status)),1)],2)]),s("td",st,[s("div",at,[S(i,{to:`/admin/products/${m.id}`,class:"admin-btn admin-btn-xs admin-btn-primary",title:"View Product"},{default:N(()=>e[9]||(e[9]=[s("i",{class:"fas fa-eye"},null,-1)])),_:2},1032,["to"]),S(i,{to:`/admin/products/${m.id}/edit`,class:"admin-btn admin-btn-xs admin-btn-secondary",title:"Edit Product"},{default:N(()=>e[10]||(e[10]=[s("i",{class:"fas fa-edit"},null,-1)])),_:2},1032,["to"])])])]))),128))])])])):(l(),d("div",Ue,e[8]||(e[8]=[s("i",{class:"fas fa-box-open"},null,-1),s("span",null,"No products found for this company",-1)]))),_.value>1?(l(),d("div",et,[s("div",tt,[s("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",onClick:e[1]||(e[1]=m=>n(k.value-1)),disabled:k.value<=1},e[12]||(e[12]=[s("i",{class:"fas fa-chevron-left"},null,-1)]),8,nt),s("span",lt," Page "+c(k.value)+" of "+c(_.value),1),s("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",onClick:e[2]||(e[2]=m=>n(k.value+1)),disabled:k.value>=_.value},e[13]||(e[13]=[s("i",{class:"fas fa-chevron-right"},null,-1)]),8,ot)])])):b("",!0),r.value.length?(l(),d("div",it,[s("div",dt,[e[14]||(e[14]=s("span",{class:"summary-label"},"Total Products:",-1)),s("span",ct,c(g.value),1)]),s("div",rt,[e[15]||(e[15]=s("span",{class:"summary-label"},"Active:",-1)),s("span",ut,c(w.value),1)]),s("div",mt,[e[16]||(e[16]=s("span",{class:"summary-label"},"Low Stock:",-1)),s("span",vt,c(x.value),1)])])):b("",!0)])])}}},yt=D(pt,[["__scopeId","data-v-144cdb0c"]]),gt={class:"admin-company-detail"},bt={class:"admin-page-header"},_t={class:"admin-header-content"},$t={class:"admin-header-left"},ht={class:"admin-breadcrumb"},kt={class:"admin-breadcrumb-item admin-breadcrumb-current"},Ct={class:"admin-page-title"},wt={class:"admin-header-right"},It=["disabled"],At={key:0,class:"admin-loading-state"},xt={key:1,class:"admin-alert admin-alert-danger"},St={class:"admin-alert-content"},Tt={class:"admin-alert-message"},Nt={key:2,class:"admin-company-content"},Pt={class:"admin-company-layout"},Dt={class:"admin-modal-header"},Ot={class:"admin-modal-content"},Ut={class:"admin-form-group"},Rt={class:"admin-modal-footer"},Mt=["disabled"],Et={__name:"CompanyDetail",setup(u){const y=Z(),r=h({}),v=h(!1),o=h(null),k=h(!1),_=h(!1),g=h(""),w=async()=>{v.value=!0,o.value=null;try{const $=await P.getDetailedCompany(y.params.id);r.value=$.data}catch($){o.value=$.message||"Failed to load company details"}finally{v.value=!1}},x=async()=>{if(!g.value.trim()){o.value="Please provide a rejection reason";return}k.value=!0;try{await P.rejectCompany(r.value.id,g.value),_.value=!1,g.value="",await w()}catch($){o.value=$.message||"Failed to reject company"}finally{k.value=!1}};return L(()=>{w()}),($,n)=>{const t=V("router-link");return l(),d("div",gt,[s("div",bt,[s("div",_t,[s("div",$t,[s("nav",ht,[S(t,{to:"/admin/companies",class:"admin-breadcrumb-item"},{default:N(()=>n[6]||(n[6]=[s("i",{class:"fas fa-building"},null,-1),p(" Companies ")])),_:1}),n[7]||(n[7]=s("span",{class:"admin-breadcrumb-separator"},"/",-1)),s("span",kt,c(r.value.name||"Company Details"),1)]),s("h1",Ct,[n[8]||(n[8]=s("i",{class:"fas fa-building"},null,-1)),p(" "+c(r.value.name||"Company Details"),1)])]),s("div",wt,[s("button",{class:"admin-btn admin-btn-secondary",onClick:w,disabled:v.value},[s("i",{class:C(["fas fa-sync-alt",{"fa-spin":v.value}])},null,2),n[9]||(n[9]=s("span",null,"Refresh",-1))],8,It),r.value.id?(l(),j(t,{key:0,to:`/admin/companies/${r.value.id}/edit`,class:"admin-btn admin-btn-primary"},{default:N(()=>n[10]||(n[10]=[s("i",{class:"fas fa-edit"},null,-1),s("span",null,"Edit Company",-1)])),_:1},8,["to"])):b("",!0)])])]),v.value&&!r.value.id?(l(),d("div",At,n[11]||(n[11]=[s("div",{class:"admin-spinner"},[s("i",{class:"fas fa-spinner fa-pulse"})],-1),s("p",{class:"admin-loading-text"},"Loading company details...",-1)]))):o.value?(l(),d("div",xt,[n[13]||(n[13]=s("div",{class:"admin-alert-icon"},[s("i",{class:"fas fa-exclamation-circle"})],-1)),s("div",St,[s("div",Tt,c(o.value),1)]),s("button",{class:"admin-alert-close",onClick:n[0]||(n[0]=I=>o.value=null)},n[12]||(n[12]=[s("i",{class:"fas fa-times"},null,-1)]))])):r.value.id?(l(),d("div",Nt,[s("div",Pt,[S(Xs,{company:r.value,class:"admin-company-section"},null,8,["company"]),r.value.finance?(l(),j(Ca,{key:0,finance:r.value.finance,class:"admin-company-section"},null,8,["finance"])):b("",!0),S(yt,{companyId:r.value.id,class:"admin-company-section"},null,8,["companyId"]),S(we,{companyId:r.value.id,class:"admin-company-section"},null,8,["companyId"]),r.value.schedule&&r.value.schedule.length>0?(l(),j(Ba,{key:1,schedule:r.value.schedule,class:"admin-company-section"},null,8,["schedule"])):b("",!0)])])):b("",!0),_.value?(l(),d("div",{key:3,class:"admin-modal-overlay",onClick:n[5]||(n[5]=I=>_.value=!1)},[s("div",{class:"admin-modal",onClick:n[4]||(n[4]=J(()=>{},["stop"]))},[s("div",Dt,[n[15]||(n[15]=s("h3",{class:"admin-modal-title"},"Reject Company",-1)),s("button",{class:"admin-modal-close",onClick:n[1]||(n[1]=I=>_.value=!1)},n[14]||(n[14]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",Ot,[s("div",Ut,[n[16]||(n[16]=s("label",{class:"admin-form-label"},"Rejection Reason *",-1)),X(s("textarea",{"onUpdate:modelValue":n[2]||(n[2]=I=>g.value=I),class:"admin-form-control",rows:"4",placeholder:"Please provide a reason for rejection...",required:""},"            ",512),[[Y,g.value]])])]),s("div",Rt,[s("button",{class:"admin-btn admin-btn-secondary",onClick:n[3]||(n[3]=I=>_.value=!1)}," Cancel "),s("button",{class:"admin-btn admin-btn-danger",onClick:x,disabled:!g.value.trim()||k.value},n[17]||(n[17]=[s("i",{class:"fas fa-times"},null,-1),p(" Reject Company ")]),8,Mt)])])])):b("",!0)])}}},Lt=D(Et,[["__scopeId","data-v-56318320"]]);export{Lt as default};
