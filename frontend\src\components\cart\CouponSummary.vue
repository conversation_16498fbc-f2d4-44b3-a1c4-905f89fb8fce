<template>
  <div v-if="couponDiscount && couponDiscount.discountAmount > 0" class="coupon-summary">
    <div class="discount-row">
      <div class="discount-label">
        <span>Знижка ({{ couponDiscount.couponCode }})</span>
        <span class="discount-type">{{ formatDiscountType(couponDiscount.discountType) }}</span>
      </div>
      <div class="discount-amount">
        -{{ Math.round(formatPrice(couponDiscount.discountAmount)) }} ₴
      </div>
    </div>
    
    <div v-if="couponDiscount.discountPercentage > 0" class="discount-percentage">
      Економія: {{ couponDiscount.discountPercentage }}%
    </div>
  </div>
</template>

<script>
import couponService from '@/services/coupon.service';

export default {
  name: 'CouponSummary',
  
  props: {
    couponDiscount: {
      type: Object,
      default: null
    }
  },

  methods: {
    formatPrice(amount) {
      if (typeof amount !== 'number') return '0.00';
      return amount.toFixed(2);
    },

    formatDiscountType(discountType) {
      return couponService.formatDiscountType(discountType);
    }
  }
};
</script>

<style scoped>
.coupon-summary {
  border-top: 1px solid #e9ecef;
  padding-top: 12px;
  margin-top: 12px;
}

.discount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.discount-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #28a745;
  font-weight: 500;
}

.discount-label i {
  color: #28a745;
}

.discount-type {
  font-size: 12px;
  background: #e8f5e8;
  color: #155724;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: normal;
}

.discount-amount {
  font-weight: 600;
  color: #28a745;
  font-size: 16px;
}

.discount-percentage {
  font-size: 12px;
  color: #28a745;
  text-align: right;
  font-style: italic;
}
</style>
