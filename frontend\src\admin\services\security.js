import api from '@/services/api';

export const securityService = {
  async getLogs(params = {}) {
    try {
      // Очищаємо пусті параметри
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== null && value !== undefined && value !== '')
      );

      const response = await api.get('/api/admin/security/logs', { params: cleanParams });
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error fetching logs:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.errors?.[0] ||
                          'Failed to load security logs';
      throw new Error(errorMessage);
    }
  },

  async getLogsWithFilters(filters = {}) {
    try {
      const params = {
        filter: filters.search,
        orderBy: filters.sortBy || 'Timestamp',
        descending: filters.sortOrder === 'desc',
        page: filters.page,
        pageSize: filters.pageSize,
        level: filters.level,
        category: filters.category,
        userId: filters.userId,
        userEmail: filters.userEmail,
        entityId: filters.entityId,
        entityType: filters.entityType,
        ipAddress: filters.ipAddress,
        fromDate: filters.fromDate,
        toDate: filters.toDate
      };

      return await this.getLogs(params);
    } catch (error) {
      console.error('Error fetching logs with filters:', error);
      throw error;
    }
  },

  async getAuditLogs(params = {}) {
    try {
      const response = await api.get('/api/admin/security/audit-logs', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to load audit logs');
    }
  },

  async getLoginAttempts(params = {}) {
    try {
      const response = await api.get('/api/admin/security/login-attempts', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching login attempts:', error);
      throw new Error(error.response?.data?.message || 'Failed to load login attempts');
    }
  },

  async getSecuritySettings() {
    try {
      const response = await api.get('/api/admin/security/security-settings');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching security settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security settings');
    }
  },

  async updateSecuritySettings(settings) {
    try {
      const response = await api.put('/api/admin/security/security-settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating security settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to update security settings');
    }
  },

  async getSecurityStats() {
    try {
      const response = await api.get('/api/admin/security/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching security stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security statistics');
    }
  },

  async blockIpAddress(ipAddress, reason) {
    try {
      const response = await api.post('/api/admin/security/block-ip', { ipAddress, reason });
      return response.data;
    } catch (error) {
      console.error('Error blocking IP address:', error);
      throw new Error(error.response?.data?.message || 'Failed to block IP address');
    }
  },

  async unblockIpAddress(ipAddress) {
    try {
      const response = await api.post('/api/admin/security/unblock-ip', { ipAddress });
      return response.data;
    } catch (error) {
      console.error('Error unblocking IP address:', error);
      throw new Error(error.response?.data?.message || 'Failed to unblock IP address');
    }
  },

  async getBlockedIps(params = {}) {
    try {
      const response = await api.get('/api/admin/security/blocked-ips', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching blocked IPs:', error);
      throw new Error(error.response?.data?.message || 'Failed to load blocked IP addresses');
    }
  },

  async exportLogs(params = {}) {
    try {
      const response = await api.get('/api/admin/security/export-logs', { 
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to export logs');
    }
  },

  async clearLogs(logType, olderThan) {
    try {
      const response = await api.post('/api/admin/security/clear-logs', { logType, olderThan });
      return response.data;
    } catch (error) {
      console.error('Error clearing logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to clear logs');
    }
  },

  async runSecurityScan() {
    try {
      const response = await api.post('/api/admin/security/scan');
      return response.data;
    } catch (error) {
      console.error('Error running security scan:', error);
      throw new Error(error.response?.data?.message || 'Failed to run security scan');
    }
  },

  async getSecurityScanResults() {
    try {
      const response = await api.get('/api/admin/security/scan-results');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching security scan results:', error);
      throw new Error(error.response?.data?.message || 'Failed to load security scan results');
    }
  },

  async getSecurityStatistics(fromDate = null, toDate = null) {
    try {
      const params = {};
      if (fromDate) params.fromDate = fromDate;
      if (toDate) params.toDate = toDate;

      const response = await api.get('/api/admin/security/statistics', { params });
      return response.data.data || response.data;
    } catch (error) {
      throw this.handleApiError(error, 'Failed to load security statistics');
    }
  },

  async exportLogs(filters = {}, format = 'csv') {
    try {
      const params = {
        level: filters.level,
        category: filters.category,
        fromDate: filters.fromDate,
        toDate: filters.toDate,
        format: format
      };

      const response = await api.get('/api/admin/security/export-logs', {
        params,
        responseType: 'blob'
      });

      // Створюємо URL для завантаження файлу
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `security-logs-${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true, message: 'Logs exported successfully' };
    } catch (error) {
      throw this.handleApiError(error, 'Failed to export logs');
    }
  },

  // Покращена обробка помилок
  handleApiError(error, defaultMessage = 'Operation failed') {
    console.error('API Error:', error);

    if (error.response) {
      const message = error.response.data?.message ||
                     error.response.data?.errors?.[0] ||
                     `Server error: ${error.response.status}`;
      return new Error(message);
    } else if (error.request) {
      return new Error('Network error: No response from server');
    } else {
      return new Error(error.message || defaultMessage);
    }
  },

  // Утилітарні методи для роботи з логами
  getLogLevelColor(level) {
    switch (level?.toUpperCase()) {
      case 'ERROR': return 'danger';
      case 'WARNING': return 'warning';
      case 'INFO': return 'info';
      case 'DEBUG': return 'light';
      default: return 'light';
    }
  },

  getLogLevelIcon(level) {
    switch (level?.toUpperCase()) {
      case 'ERROR': return 'fas fa-exclamation-triangle';
      case 'WARNING': return 'fas fa-exclamation-circle';
      case 'INFO': return 'fas fa-info-circle';
      case 'DEBUG': return 'fas fa-bug';
      default: return 'fas fa-file-alt';
    }
  },

  formatLogMessage(message, maxLength = 100) {
    if (!message) return 'No message';
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  },

  formatTimestamp(timestamp) {
    if (!timestamp) return 'N/A';

    const date = new Date(timestamp);
    return date.toLocaleString('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }
};
