using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;

namespace Marketplace.Application.Queries.Review;

public record GetProductReviewsQuery(
    string ProductSlug,
    string? Filter = null,
    string? OrderBy = null,
    bool Descending = false,
    int? Page = null,
    int? PageSize = null
    ) : PaginatedQuery<ReviewResponse>(Filter, OrderBy, Descending, Page, PageSize);
