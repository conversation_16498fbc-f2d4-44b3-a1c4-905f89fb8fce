const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/NotFoundPage-Brt4A09q.js","assets/NotFoundPage-CYYAFLUo.css","assets/AdminLayout-DpraaOZn.js","assets/AdminLayout-RVk4xuMe.css","assets/Dashboard-DgHbdAL-.js","assets/StatusBadge-CWG2IOJy.js","assets/StatusBadge-D-4w7UZc.css","assets/Dashboard-Ba7vEhLR.css","assets/UserList-tkPJnQJH.js","assets/users-D6yG63l9.js","assets/useAdminSearch-BxXMp5oH.js","assets/SearchAndFilters-B3kez0yT.js","assets/SearchAndFilters-0BDgJlP1.css","assets/Pagination-DX2plTiq.js","assets/Pagination-C35Qx2jr.css","assets/ConfirmDialog-hd0r6dWx.js","assets/ConfirmDialog-C9PHu2vW.css","assets/roles-D6TbD4pL.js","assets/UserList-DvGPl9zV.css","assets/UserDetail-NN8NtXT4.js","assets/UserDetail-DesW200K.css","assets/UserEdit-BCZPRHCl.js","assets/EntityImageManager-DFnfDtsC.js","assets/image.service-DOD4lHqw.js","assets/EntityImageManager-1GpiTuze.css","assets/UploadProgress-DTyUnfEW.js","assets/UploadProgress-Bohj58P1.css","assets/UserEdit-D002bS_y.css","assets/UserCreate-OIhHJp4Z.js","assets/UserCreate-DMDaHPCC.css","assets/Products-COuEVauK.js","assets/products-Bpq90UOX.js","assets/Products-C7xIy4zh.css","assets/ProductView-BpUvZ2Qe.js","assets/AdminProductHeader-CEj_aj3M.js","assets/AdminProductHeader-BsqWuI1n.css","assets/ProductView-3k49Q68x.css","assets/ProductEdit-CKOjAklg.js","assets/EnhancedProductAttributesEditor-lcyAKYjd.js","assets/EnhancedCategorySelector-v7wVF3eo.js","assets/EnhancedCategorySelector-5c9OOuv8.css","assets/EnhancedProductAttributesEditor-BjrykAoO.css","assets/EntityMetaImageManager-oIAk4B-a.js","assets/EntityMetaImageManager-CbeLzDCk.css","assets/ProductEdit-Bb31Lz0m.css","assets/ProductCreate-Bg9PcwCl.js","assets/ProductCreate-tn0RQdqM.css","assets/ApiTestPage-Bvb5UueQ.js","assets/ApiTestPage-T2J-Yyhr.css","assets/ProductEditTest-CCU73LQB.js","assets/ProductEditTest-C5MRHY3Y.css","assets/EnhancedComponentsTest-D8wXYWjM.js","assets/EnhancedComponentsTest-Jaz2UfCn.css","assets/Categories-CuG-s9dF.js","assets/Categories-BFuW_Osu.css","assets/CategoryDetailNew-9HvPVQmN.js","assets/CategoryDetailNew-Su3hPtGe.css","assets/CategoryCreateNew-CvIC3KrT.js","assets/CategoryCreateNew-DFFU3j22.css","assets/CategoryEditNew-DIdJB-uH.js","assets/CategoryEditNew-09SrNrLy.css","assets/CategoryTestPage-DVmVnQ-d.js","assets/CategoryTestPage-RhGHnwmm.css","assets/OrderList-C3xZpsf3.js","assets/orders-DGdj4xZm.js","assets/OrderList-D57ASGao.css","assets/OrderDetailNew-DJImyosd.js","assets/OrderDetailNew-Beid3ZoK.css","assets/OrderEditNew-BoYhkpeE.js","assets/OrderEditNew-BmjcLuz7.css","assets/SellerRequestList-CLJQ3EqY.js","assets/seller-requests-CWXKDmna.js","assets/SellerRequestList-B5gJlQzO.css","assets/SellerRequestDetail-CqkMEWun.js","assets/SellerRequestDetail-Rj2jzRGr.css","assets/SellerApplication-w_T-DTxV.js","assets/SellerApplication-CYIt-5dX.css","assets/CompanyList-DNfj7Av0.js","assets/companies-B97mkaMy.js","assets/CompanyList-Bws24OhE.css","assets/CompanyDetail-Bup8Y_pp.js","assets/CompanyDetail-BGF8GWTM.css","assets/CompanyEdit-QaSJxYHz.js","assets/CompanyEdit-C3THiwuU.css","assets/ReviewList-D3cwVwIF.js","assets/reviews-C_F7wLvS.js","assets/ReviewList-Ck0hdCzD.css","assets/ReviewDetail-vPuO7niG.js","assets/ReviewDetail-0SgbnoGX.css","assets/RatingList-DvIiSCuV.js","assets/ratings-D2bTWLA4.js","assets/RatingList-DSRv0dKX.css","assets/ChatList-DMwJj2nL.js","assets/chats-Dp7kITFn.js","assets/ChatList-BmUHcI0C.css","assets/ChatDetail-BleKqTSQ.js","assets/ChatDetail-BKvrnliU.css","assets/AddressList-BX7Date4.js","assets/addresses-DL07ASCS.js","assets/AddressList-BMCSl4tG.css","assets/ApiTest-CQcRMlzN.js","assets/ApiTest-C0k86QHm.css","assets/Settings-DCg3LFqK.js","assets/Settings-BTzJV434.css","assets/Security-CGibd6P8.js","assets/Security-DSYzLotB.css","assets/Reports-DIbpkDTf.js","assets/Reports-B66abjzn.css","assets/TopProductsPage-G019V0t4.js","assets/TopProductsPage-6n0bVOFi.css","assets/RatingDetail-CdHRwgvs.js","assets/RatingDetail-LbRRODaM.css","assets/AddressCreate-v28eHgUk.js","assets/AddressCreate-CZ8VG6Hp.css","assets/AddressDetail-NMBm9l2Y.js","assets/AddressDetail-jKIMuC_d.css","assets/AddressEdit-B8F_mV7e.js","assets/AddressEdit-p-0FatHZ.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function r(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(n){if(n.ep)return;n.ep=!0;const o=r(n);fetch(n.href,o)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Gi(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const ze={},ys=[],qt=()=>{},kf=()=>!1,po=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),qi=e=>e.startsWith("onUpdate:"),at=Object.assign,Hi=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},Rf=Object.prototype.hasOwnProperty,Ue=(e,t)=>Rf.call(e,t),he=Array.isArray,bs=e=>hn(e)==="[object Map]",Fs=e=>hn(e)==="[object Set]",Fa=e=>hn(e)==="[object Date]",_e=e=>typeof e=="function",Qe=e=>typeof e=="string",zt=e=>typeof e=="symbol",He=e=>e!==null&&typeof e=="object",Cc=e=>(He(e)||_e(e))&&_e(e.then)&&_e(e.catch),Pc=Object.prototype.toString,hn=e=>Pc.call(e),Of=e=>hn(e).slice(8,-1),Tc=e=>hn(e)==="[object Object]",zi=e=>Qe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ks=Gi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ho=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},If=/-(\w)/g,Vt=ho(e=>e.replace(If,(t,r)=>r?r.toUpperCase():"")),xf=/\B([A-Z])/g,Lr=ho(e=>e.replace(xf,"-$1").toLowerCase()),go=ho(e=>e.charAt(0).toUpperCase()+e.slice(1)),No=ho(e=>e?`on${go(e)}`:""),xr=(e,t)=>!Object.is(e,t),Dn=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},kc=(e,t,r,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:r})},qn=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Rc=e=>{const t=Qe(e)?Number(e):NaN;return isNaN(t)?e:t};let Da;const mo=()=>Da||(Da=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ki(e){if(he(e)){const t={};for(let r=0;r<e.length;r++){const s=e[r],n=Qe(s)?Nf(s):Ki(s);if(n)for(const o in n)t[o]=n[o]}return t}else if(Qe(e)||He(e))return e}const $f=/;(?![^(]*\))/g,Ff=/:([^]+)/,Df=/\/\*[^]*?\*\//g;function Nf(e){const t={};return e.replace(Df,"").split($f).forEach(r=>{if(r){const s=r.split(Ff);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ce(e){let t="";if(Qe(e))t=e;else if(he(e))for(let r=0;r<e.length;r++){const s=Ce(e[r]);s&&(t+=s+" ")}else if(He(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}const Mf="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Lf=Gi(Mf);function Oc(e){return!!e||e===""}function Vf(e,t){if(e.length!==t.length)return!1;let r=!0;for(let s=0;r&&s<e.length;s++)r=gn(e[s],t[s]);return r}function gn(e,t){if(e===t)return!0;let r=Fa(e),s=Fa(t);if(r||s)return r&&s?e.getTime()===t.getTime():!1;if(r=zt(e),s=zt(t),r||s)return e===t;if(r=he(e),s=he(t),r||s)return r&&s?Vf(e,t):!1;if(r=He(e),s=He(t),r||s){if(!r||!s)return!1;const n=Object.keys(e).length,o=Object.keys(t).length;if(n!==o)return!1;for(const i in e){const a=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(a&&!l||!a&&l||!gn(e[i],t[i]))return!1}}return String(e)===String(t)}function Wi(e,t){return e.findIndex(r=>gn(r,t))}const Ic=e=>!!(e&&e.__v_isRef===!0),j=e=>Qe(e)?e:e==null?"":he(e)||He(e)&&(e.toString===Pc||!_e(e.toString))?Ic(e)?j(e.value):JSON.stringify(e,xc,2):String(e),xc=(e,t)=>Ic(t)?xc(e,t.value):bs(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[s,n],o)=>(r[Mo(s,o)+" =>"]=n,r),{})}:Fs(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>Mo(r))}:zt(t)?Mo(t):He(t)&&!he(t)&&!Tc(t)?String(t):t,Mo=(e,t="")=>{var r;return zt(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $t;class $c{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=$t,!t&&$t&&(this.index=($t.scopes||($t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=$t;try{return $t=this,t()}finally{$t=r}}}on(){$t=this}off(){$t=this.parent}stop(t){if(this._active){this._active=!1;let r,s;for(r=0,s=this.effects.length;r<s;r++)this.effects[r].stop();for(this.effects.length=0,r=0,s=this.cleanups.length;r<s;r++)this.cleanups[r]();if(this.cleanups.length=0,this.scopes){for(r=0,s=this.scopes.length;r<s;r++)this.scopes[r].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function jf(e){return new $c(e)}function Uf(){return $t}let Je;const Lo=new WeakSet;class Fc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,$t&&$t.active&&$t.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Lo.has(this)&&(Lo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Nc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Na(this),Mc(this);const t=Je,r=Ht;Je=this,Ht=!0;try{return this.fn()}finally{Lc(this),Je=t,Ht=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Yi(t);this.deps=this.depsTail=void 0,Na(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Lo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ci(this)&&this.run()}get dirty(){return ci(this)}}let Dc=0,Ws,Js;function Nc(e,t=!1){if(e.flags|=8,t){e.next=Js,Js=e;return}e.next=Ws,Ws=e}function Ji(){Dc++}function Xi(){if(--Dc>0)return;if(Js){let t=Js;for(Js=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;Ws;){let t=Ws;for(Ws=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=r}}if(e)throw e}function Mc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Lc(e){let t,r=e.depsTail,s=r;for(;s;){const n=s.prevDep;s.version===-1?(s===r&&(r=n),Yi(s),Bf(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=n}e.deps=t,e.depsTail=r}function ci(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Vc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Vc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===rn))return;e.globalVersion=rn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!ci(e)){e.flags&=-3;return}const r=Je,s=Ht;Je=e,Ht=!0;try{Mc(e);const n=e.fn(e._value);(t.version===0||xr(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{Je=r,Ht=s,Lc(e),e.flags&=-3}}function Yi(e,t=!1){const{dep:r,prevSub:s,nextSub:n}=e;if(s&&(s.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=s,e.nextSub=void 0),r.subs===e&&(r.subs=s,!s&&r.computed)){r.computed.flags&=-5;for(let o=r.computed.deps;o;o=o.nextDep)Yi(o,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function Bf(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let Ht=!0;const jc=[];function Vr(){jc.push(Ht),Ht=!1}function jr(){const e=jc.pop();Ht=e===void 0?!0:e}function Na(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=Je;Je=void 0;try{t()}finally{Je=r}}}let rn=0;class Gf{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Zi{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Je||!Ht||Je===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==Je)r=this.activeLink=new Gf(Je,this),Je.deps?(r.prevDep=Je.depsTail,Je.depsTail.nextDep=r,Je.depsTail=r):Je.deps=Je.depsTail=r,Uc(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const s=r.nextDep;s.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=s),r.prevDep=Je.depsTail,r.nextDep=void 0,Je.depsTail.nextDep=r,Je.depsTail=r,Je.deps===r&&(Je.deps=s)}return r}trigger(t){this.version++,rn++,this.notify(t)}notify(t){Ji();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{Xi()}}}function Uc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Uc(s)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const Hn=new WeakMap,Yr=Symbol(""),ui=Symbol(""),sn=Symbol("");function mt(e,t,r){if(Ht&&Je){let s=Hn.get(e);s||Hn.set(e,s=new Map);let n=s.get(r);n||(s.set(r,n=new Zi),n.map=s,n.key=r),n.track()}}function mr(e,t,r,s,n,o){const i=Hn.get(e);if(!i){rn++;return}const a=l=>{l&&l.trigger()};if(Ji(),t==="clear")i.forEach(a);else{const l=he(e),u=l&&zi(r);if(l&&r==="length"){const d=Number(s);i.forEach((f,h)=>{(h==="length"||h===sn||!zt(h)&&h>=d)&&a(f)})}else switch((r!==void 0||i.has(void 0))&&a(i.get(r)),u&&a(i.get(sn)),t){case"add":l?u&&a(i.get("length")):(a(i.get(Yr)),bs(e)&&a(i.get(ui)));break;case"delete":l||(a(i.get(Yr)),bs(e)&&a(i.get(ui)));break;case"set":bs(e)&&a(i.get(Yr));break}}Xi()}function qf(e,t){const r=Hn.get(e);return r&&r.get(t)}function ds(e){const t=Fe(e);return t===e?t:(mt(t,"iterate",sn),Lt(e)?t:t.map(vt))}function vo(e){return mt(e=Fe(e),"iterate",sn),e}const Hf={__proto__:null,[Symbol.iterator](){return Vo(this,Symbol.iterator,vt)},concat(...e){return ds(this).concat(...e.map(t=>he(t)?ds(t):t))},entries(){return Vo(this,"entries",e=>(e[1]=vt(e[1]),e))},every(e,t){return lr(this,"every",e,t,void 0,arguments)},filter(e,t){return lr(this,"filter",e,t,r=>r.map(vt),arguments)},find(e,t){return lr(this,"find",e,t,vt,arguments)},findIndex(e,t){return lr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return lr(this,"findLast",e,t,vt,arguments)},findLastIndex(e,t){return lr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return lr(this,"forEach",e,t,void 0,arguments)},includes(...e){return jo(this,"includes",e)},indexOf(...e){return jo(this,"indexOf",e)},join(e){return ds(this).join(e)},lastIndexOf(...e){return jo(this,"lastIndexOf",e)},map(e,t){return lr(this,"map",e,t,void 0,arguments)},pop(){return js(this,"pop")},push(...e){return js(this,"push",e)},reduce(e,...t){return Ma(this,"reduce",e,t)},reduceRight(e,...t){return Ma(this,"reduceRight",e,t)},shift(){return js(this,"shift")},some(e,t){return lr(this,"some",e,t,void 0,arguments)},splice(...e){return js(this,"splice",e)},toReversed(){return ds(this).toReversed()},toSorted(e){return ds(this).toSorted(e)},toSpliced(...e){return ds(this).toSpliced(...e)},unshift(...e){return js(this,"unshift",e)},values(){return Vo(this,"values",vt)}};function Vo(e,t,r){const s=vo(e),n=s[t]();return s!==e&&!Lt(e)&&(n._next=n.next,n.next=()=>{const o=n._next();return o.value&&(o.value=r(o.value)),o}),n}const zf=Array.prototype;function lr(e,t,r,s,n,o){const i=vo(e),a=i!==e&&!Lt(e),l=i[t];if(l!==zf[t]){const f=l.apply(e,o);return a?vt(f):f}let u=r;i!==e&&(a?u=function(f,h){return r.call(this,vt(f),h,e)}:r.length>2&&(u=function(f,h){return r.call(this,f,h,e)}));const d=l.call(i,u,s);return a&&n?n(d):d}function Ma(e,t,r,s){const n=vo(e);let o=r;return n!==e&&(Lt(e)?r.length>3&&(o=function(i,a,l){return r.call(this,i,a,l,e)}):o=function(i,a,l){return r.call(this,i,vt(a),l,e)}),n[t](o,...s)}function jo(e,t,r){const s=Fe(e);mt(s,"iterate",sn);const n=s[t](...r);return(n===-1||n===!1)&&ra(r[0])?(r[0]=Fe(r[0]),s[t](...r)):n}function js(e,t,r=[]){Vr(),Ji();const s=Fe(e)[t].apply(e,r);return Xi(),jr(),s}const Kf=Gi("__proto__,__v_isRef,__isVue"),Bc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(zt));function Wf(e){zt(e)||(e=String(e));const t=Fe(this);return mt(t,"has",e),t.hasOwnProperty(e)}class Gc{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,s){if(r==="__v_skip")return t.__v_skip;const n=this._isReadonly,o=this._isShallow;if(r==="__v_isReactive")return!n;if(r==="__v_isReadonly")return n;if(r==="__v_isShallow")return o;if(r==="__v_raw")return s===(n?o?np:Kc:o?zc:Hc).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=he(t);if(!n){let l;if(i&&(l=Hf[r]))return l;if(r==="hasOwnProperty")return Wf}const a=Reflect.get(t,r,rt(t)?t:s);return(zt(r)?Bc.has(r):Kf(r))||(n||mt(t,"get",r),o)?a:rt(a)?i&&zi(r)?a:a.value:He(a)?n?ea(a):or(a):a}}class qc extends Gc{constructor(t=!1){super(!1,t)}set(t,r,s,n){let o=t[r];if(!this._isShallow){const l=es(o);if(!Lt(s)&&!es(s)&&(o=Fe(o),s=Fe(s)),!he(t)&&rt(o)&&!rt(s))return l?!1:(o.value=s,!0)}const i=he(t)&&zi(r)?Number(r)<t.length:Ue(t,r),a=Reflect.set(t,r,s,rt(t)?t:n);return t===Fe(n)&&(i?xr(s,o)&&mr(t,"set",r,s):mr(t,"add",r,s)),a}deleteProperty(t,r){const s=Ue(t,r);t[r];const n=Reflect.deleteProperty(t,r);return n&&s&&mr(t,"delete",r,void 0),n}has(t,r){const s=Reflect.has(t,r);return(!zt(r)||!Bc.has(r))&&mt(t,"has",r),s}ownKeys(t){return mt(t,"iterate",he(t)?"length":Yr),Reflect.ownKeys(t)}}class Jf extends Gc{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const Xf=new qc,Yf=new Jf,Zf=new qc(!0);const di=e=>e,An=e=>Reflect.getPrototypeOf(e);function Qf(e,t,r){return function(...s){const n=this.__v_raw,o=Fe(n),i=bs(o),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,u=n[e](...s),d=r?di:t?fi:vt;return!t&&mt(o,"iterate",l?ui:Yr),{next(){const{value:f,done:h}=u.next();return h?{value:f,done:h}:{value:a?[d(f[0]),d(f[1])]:d(f),done:h}},[Symbol.iterator](){return this}}}}function Cn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ep(e,t){const r={get(n){const o=this.__v_raw,i=Fe(o),a=Fe(n);e||(xr(n,a)&&mt(i,"get",n),mt(i,"get",a));const{has:l}=An(i),u=t?di:e?fi:vt;if(l.call(i,n))return u(o.get(n));if(l.call(i,a))return u(o.get(a));o!==i&&o.get(n)},get size(){const n=this.__v_raw;return!e&&mt(Fe(n),"iterate",Yr),Reflect.get(n,"size",n)},has(n){const o=this.__v_raw,i=Fe(o),a=Fe(n);return e||(xr(n,a)&&mt(i,"has",n),mt(i,"has",a)),n===a?o.has(n):o.has(n)||o.has(a)},forEach(n,o){const i=this,a=i.__v_raw,l=Fe(a),u=t?di:e?fi:vt;return!e&&mt(l,"iterate",Yr),a.forEach((d,f)=>n.call(o,u(d),u(f),i))}};return at(r,e?{add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear")}:{add(n){!t&&!Lt(n)&&!es(n)&&(n=Fe(n));const o=Fe(this);return An(o).has.call(o,n)||(o.add(n),mr(o,"add",n,n)),this},set(n,o){!t&&!Lt(o)&&!es(o)&&(o=Fe(o));const i=Fe(this),{has:a,get:l}=An(i);let u=a.call(i,n);u||(n=Fe(n),u=a.call(i,n));const d=l.call(i,n);return i.set(n,o),u?xr(o,d)&&mr(i,"set",n,o):mr(i,"add",n,o),this},delete(n){const o=Fe(this),{has:i,get:a}=An(o);let l=i.call(o,n);l||(n=Fe(n),l=i.call(o,n)),a&&a.call(o,n);const u=o.delete(n);return l&&mr(o,"delete",n,void 0),u},clear(){const n=Fe(this),o=n.size!==0,i=n.clear();return o&&mr(n,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(n=>{r[n]=Qf(n,e,t)}),r}function Qi(e,t){const r=ep(e,t);return(s,n,o)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?s:Reflect.get(Ue(r,n)&&n in s?r:s,n,o)}const tp={get:Qi(!1,!1)},rp={get:Qi(!1,!0)},sp={get:Qi(!0,!1)};const Hc=new WeakMap,zc=new WeakMap,Kc=new WeakMap,np=new WeakMap;function op(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ip(e){return e.__v_skip||!Object.isExtensible(e)?0:op(Of(e))}function or(e){return es(e)?e:ta(e,!1,Xf,tp,Hc)}function Wc(e){return ta(e,!1,Zf,rp,zc)}function ea(e){return ta(e,!0,Yf,sp,Kc)}function ta(e,t,r,s,n){if(!He(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=n.get(e);if(o)return o;const i=ip(e);if(i===0)return e;const a=new Proxy(e,i===2?s:r);return n.set(e,a),a}function _s(e){return es(e)?_s(e.__v_raw):!!(e&&e.__v_isReactive)}function es(e){return!!(e&&e.__v_isReadonly)}function Lt(e){return!!(e&&e.__v_isShallow)}function ra(e){return e?!!e.__v_raw:!1}function Fe(e){const t=e&&e.__v_raw;return t?Fe(t):e}function ap(e){return!Ue(e,"__v_skip")&&Object.isExtensible(e)&&kc(e,"__v_skip",!0),e}const vt=e=>He(e)?or(e):e,fi=e=>He(e)?ea(e):e;function rt(e){return e?e.__v_isRef===!0:!1}function oe(e){return Xc(e,!1)}function Jc(e){return Xc(e,!0)}function Xc(e,t){return rt(e)?e:new lp(e,t)}class lp{constructor(t,r){this.dep=new Zi,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=r?t:Fe(t),this._value=r?t:vt(t),this.__v_isShallow=r}get value(){return this.dep.track(),this._value}set value(t){const r=this._rawValue,s=this.__v_isShallow||Lt(t)||es(t);t=s?t:Fe(t),xr(t,r)&&(this._rawValue=t,this._value=s?t:vt(t),this.dep.trigger())}}function Ie(e){return rt(e)?e.value:e}function ge(e){return _e(e)?e():Ie(e)}const cp={get:(e,t,r)=>t==="__v_raw"?e:Ie(Reflect.get(e,t,r)),set:(e,t,r,s)=>{const n=e[t];return rt(n)&&!rt(r)?(n.value=r,!0):Reflect.set(e,t,r,s)}};function Yc(e){return _s(e)?e:new Proxy(e,cp)}class up{constructor(t,r,s){this._object=t,this._key=r,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return qf(Fe(this._object),this._key)}}class dp{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Kr(e,t,r){return rt(e)?e:_e(e)?new dp(e):He(e)&&arguments.length>1?fp(e,t,r):oe(e)}function fp(e,t,r){const s=e[t];return rt(s)?s:new up(e,t,r)}class pp{constructor(t,r,s){this.fn=t,this.setter=r,this._value=void 0,this.dep=new Zi(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=rn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&Je!==this)return Nc(this,!0),!0}get value(){const t=this.dep.track();return Vc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function hp(e,t,r=!1){let s,n;return _e(e)?s=e:(s=e.get,n=e.set),new pp(s,n,r)}const Pn={},zn=new WeakMap;let Hr;function gp(e,t=!1,r=Hr){if(r){let s=zn.get(r);s||zn.set(r,s=[]),s.push(e)}}function mp(e,t,r=ze){const{immediate:s,deep:n,once:o,scheduler:i,augmentJob:a,call:l}=r,u=I=>n?I:Lt(I)||n===!1||n===0?vr(I,1):vr(I);let d,f,h,g,y=!1,E=!1;if(rt(e)?(f=()=>e.value,y=Lt(e)):_s(e)?(f=()=>u(e),y=!0):he(e)?(E=!0,y=e.some(I=>_s(I)||Lt(I)),f=()=>e.map(I=>{if(rt(I))return I.value;if(_s(I))return u(I);if(_e(I))return l?l(I,2):I()})):_e(e)?t?f=l?()=>l(e,2):e:f=()=>{if(h){Vr();try{h()}finally{jr()}}const I=Hr;Hr=d;try{return l?l(e,3,[g]):e(g)}finally{Hr=I}}:f=qt,t&&n){const I=f,R=n===!0?1/0:n;f=()=>vr(I(),R)}const A=Uf(),m=()=>{d.stop(),A&&A.active&&Hi(A.effects,d)};if(o&&t){const I=t;t=(...R)=>{I(...R),m()}}let C=E?new Array(e.length).fill(Pn):Pn;const $=I=>{if(!(!(d.flags&1)||!d.dirty&&!I))if(t){const R=d.run();if(n||y||(E?R.some((M,L)=>xr(M,C[L])):xr(R,C))){h&&h();const M=Hr;Hr=d;try{const L=[R,C===Pn?void 0:E&&C[0]===Pn?[]:C,g];l?l(t,3,L):t(...L),C=R}finally{Hr=M}}}else d.run()};return a&&a($),d=new Fc(f),d.scheduler=i?()=>i($,!1):$,g=I=>gp(I,!1,d),h=d.onStop=()=>{const I=zn.get(d);if(I){if(l)l(I,4);else for(const R of I)R();zn.delete(d)}},t?s?$(!0):C=d.run():i?i($.bind(null,!0),!0):d.run(),m.pause=d.pause.bind(d),m.resume=d.resume.bind(d),m.stop=m,m}function vr(e,t=1/0,r){if(t<=0||!He(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,rt(e))vr(e.value,t,r);else if(he(e))for(let s=0;s<e.length;s++)vr(e[s],t,r);else if(Fs(e)||bs(e))e.forEach(s=>{vr(s,t,r)});else if(Tc(e)){for(const s in e)vr(e[s],t,r);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&vr(e[s],t,r)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function mn(e,t,r,s){try{return s?e(...s):e()}catch(n){Ds(n,t,r)}}function Kt(e,t,r,s){if(_e(e)){const n=mn(e,t,r,s);return n&&Cc(n)&&n.catch(o=>{Ds(o,t,r)}),n}if(he(e)){const n=[];for(let o=0;o<e.length;o++)n.push(Kt(e[o],t,r,s));return n}}function Ds(e,t,r,s=!0){const n=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ze;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${r}`;for(;a;){const d=a.ec;if(d){for(let f=0;f<d.length;f++)if(d[f](e,l,u)===!1)return}a=a.parent}if(o){Vr(),mn(o,null,10,[e,l,u]),jr();return}}vp(e,r,n,s,i)}function vp(e,t,r,s=!0,n=!1){if(n)throw e;console.error(e)}const wt=[];let rr=-1;const ws=[];let Cr=null,gs=0;const Zc=Promise.resolve();let Kn=null;function Ft(e){const t=Kn||Zc;return e?t.then(this?e.bind(this):e):t}function yp(e){let t=rr+1,r=wt.length;for(;t<r;){const s=t+r>>>1,n=wt[s],o=nn(n);o<e||o===e&&n.flags&2?t=s+1:r=s}return t}function sa(e){if(!(e.flags&1)){const t=nn(e),r=wt[wt.length-1];!r||!(e.flags&2)&&t>=nn(r)?wt.push(e):wt.splice(yp(t),0,e),e.flags|=1,Qc()}}function Qc(){Kn||(Kn=Zc.then(tu))}function Wn(e){he(e)?ws.push(...e):Cr&&e.id===-1?Cr.splice(gs+1,0,e):e.flags&1||(ws.push(e),e.flags|=1),Qc()}function La(e,t,r=rr+1){for(;r<wt.length;r++){const s=wt[r];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;wt.splice(r,1),r--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function eu(e){if(ws.length){const t=[...new Set(ws)].sort((r,s)=>nn(r)-nn(s));if(ws.length=0,Cr){Cr.push(...t);return}for(Cr=t,gs=0;gs<Cr.length;gs++){const r=Cr[gs];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}Cr=null,gs=0}}const nn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function tu(e){try{for(rr=0;rr<wt.length;rr++){const t=wt[rr];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),mn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;rr<wt.length;rr++){const t=wt[rr];t&&(t.flags&=-2)}rr=-1,wt.length=0,eu(),Kn=null,(wt.length||ws.length)&&tu()}}let dt=null,ru=null;function Jn(e){const t=dt;return dt=e,ru=e&&e.type.__scopeId||null,t}function ot(e,t=dt,r){if(!t||e._n)return e;const s=(...n)=>{s._d&&el(-1);const o=Jn(t);let i;try{i=e(...n)}finally{Jn(o),s._d&&el(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function qe(e,t){if(dt===null)return e;const r=So(dt),s=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[o,i,a,l=ze]=t[n];o&&(_e(o)&&(o={mounted:o,updated:o}),o.deep&&vr(i),s.push({dir:o,instance:r,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function Br(e,t,r,s){const n=e.dirs,o=t&&t.dirs;for(let i=0;i<n.length;i++){const a=n[i];o&&(a.oldValue=o[i].value);let l=a.dir[s];l&&(Vr(),Kt(l,r,8,[e.el,a,e,t]),jr())}}const su=Symbol("_vte"),nu=e=>e.__isTeleport,Xs=e=>e&&(e.disabled||e.disabled===""),Va=e=>e&&(e.defer||e.defer===""),ja=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ua=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,pi=(e,t)=>{const r=e&&e.to;return Qe(r)?t?t(r):null:r},ou={name:"Teleport",__isTeleport:!0,process(e,t,r,s,n,o,i,a,l,u){const{mc:d,pc:f,pbc:h,o:{insert:g,querySelector:y,createText:E,createComment:A}}=u,m=Xs(t.props);let{shapeFlag:C,children:$,dynamicChildren:I}=t;if(e==null){const R=t.el=E(""),M=t.anchor=E("");g(R,r,s),g(M,r,s);const L=(x,X)=>{C&16&&(n&&n.isCE&&(n.ce._teleportTarget=x),d($,x,X,n,o,i,a,l))},k=()=>{const x=t.target=pi(t.props,y),X=iu(x,t,E,g);x&&(i!=="svg"&&ja(x)?i="svg":i!=="mathml"&&Ua(x)&&(i="mathml"),m||(L(x,X),Nn(t,!1)))};m&&(L(r,M),Nn(t,!0)),Va(t.props)?_t(()=>{k(),t.el.__isMounted=!0},o):k()}else{if(Va(t.props)&&!e.el.__isMounted){_t(()=>{ou.process(e,t,r,s,n,o,i,a,l,u),delete e.el.__isMounted},o);return}t.el=e.el,t.targetStart=e.targetStart;const R=t.anchor=e.anchor,M=t.target=e.target,L=t.targetAnchor=e.targetAnchor,k=Xs(e.props),x=k?r:M,X=k?R:L;if(i==="svg"||ja(M)?i="svg":(i==="mathml"||Ua(M))&&(i="mathml"),I?(h(e.dynamicChildren,I,x,n,o,i,a),la(e,t,!0)):l||f(e,t,x,X,n,o,i,a,!1),m)k?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Tn(t,r,R,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const q=t.target=pi(t.props,y);q&&Tn(t,q,null,u,0)}else k&&Tn(t,M,L,u,1);Nn(t,m)}},remove(e,t,r,{um:s,o:{remove:n}},o){const{shapeFlag:i,children:a,anchor:l,targetStart:u,targetAnchor:d,target:f,props:h}=e;if(f&&(n(u),n(d)),o&&n(l),i&16){const g=o||!Xs(h);for(let y=0;y<a.length;y++){const E=a[y];s(E,t,r,g,!!E.dynamicChildren)}}},move:Tn,hydrate:bp};function Tn(e,t,r,{o:{insert:s},m:n},o=2){o===0&&s(e.targetAnchor,t,r);const{el:i,anchor:a,shapeFlag:l,children:u,props:d}=e,f=o===2;if(f&&s(i,t,r),(!f||Xs(d))&&l&16)for(let h=0;h<u.length;h++)n(u[h],t,r,2);f&&s(a,t,r)}function bp(e,t,r,s,n,o,{o:{nextSibling:i,parentNode:a,querySelector:l,insert:u,createText:d}},f){const h=t.target=pi(t.props,l);if(h){const g=Xs(t.props),y=h._lpa||h.firstChild;if(t.shapeFlag&16)if(g)t.anchor=f(i(e),t,a(e),r,s,n,o),t.targetStart=y,t.targetAnchor=y&&i(y);else{t.anchor=i(e);let E=y;for(;E;){if(E&&E.nodeType===8){if(E.data==="teleport start anchor")t.targetStart=E;else if(E.data==="teleport anchor"){t.targetAnchor=E,h._lpa=t.targetAnchor&&i(t.targetAnchor);break}}E=i(E)}t.targetAnchor||iu(h,t,d,u),f(y&&i(y),t,h,r,s,n,o)}Nn(t,g)}return t.anchor&&i(t.anchor)}const wT=ou;function Nn(e,t){const r=e.ctx;if(r&&r.ut){let s,n;for(t?(s=e.el,n=e.anchor):(s=e.targetStart,n=e.targetAnchor);s&&s!==n;)s.nodeType===1&&s.setAttribute("data-v-owner",r.uid),s=s.nextSibling;r.ut()}}function iu(e,t,r,s){const n=t.targetStart=r(""),o=t.targetAnchor=r("");return n[su]=o,e&&(s(n,e),s(o,e)),o}const Pr=Symbol("_leaveCb"),kn=Symbol("_enterCb");function au(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ir(()=>{e.isMounted=!0}),ia(()=>{e.isUnmounting=!0}),e}const Nt=[Function,Array],lu={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Nt,onEnter:Nt,onAfterEnter:Nt,onEnterCancelled:Nt,onBeforeLeave:Nt,onLeave:Nt,onAfterLeave:Nt,onLeaveCancelled:Nt,onBeforeAppear:Nt,onAppear:Nt,onAfterAppear:Nt,onAppearCancelled:Nt},cu=e=>{const t=e.subTree;return t.component?cu(t.component):t},_p={name:"BaseTransition",props:lu,setup(e,{slots:t}){const r=os(),s=au();return()=>{const n=t.default&&na(t.default(),!0);if(!n||!n.length)return;const o=uu(n),i=Fe(e),{mode:a}=i;if(s.isLeaving)return Uo(o);const l=Ba(o);if(!l)return Uo(o);let u=on(l,i,s,r,f=>u=f);l.type!==pt&&ts(l,u);let d=r.subTree&&Ba(r.subTree);if(d&&d.type!==pt&&!nr(l,d)&&cu(r).type!==pt){let f=on(d,i,s,r);if(ts(d,f),a==="out-in"&&l.type!==pt)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,r.job.flags&8||r.update(),delete f.afterLeave,d=void 0},Uo(o);a==="in-out"&&l.type!==pt?f.delayLeave=(h,g,y)=>{const E=du(s,d);E[String(d.key)]=d,h[Pr]=()=>{g(),h[Pr]=void 0,delete u.delayedLeave,d=void 0},u.delayedLeave=()=>{y(),delete u.delayedLeave,d=void 0}}:d=void 0}else d&&(d=void 0);return o}}};function uu(e){let t=e[0];if(e.length>1){for(const r of e)if(r.type!==pt){t=r;break}}return t}const wp=_p;function du(e,t){const{leavingVNodes:r}=e;let s=r.get(t.type);return s||(s=Object.create(null),r.set(t.type,s)),s}function on(e,t,r,s,n){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:d,onEnterCancelled:f,onBeforeLeave:h,onLeave:g,onAfterLeave:y,onLeaveCancelled:E,onBeforeAppear:A,onAppear:m,onAfterAppear:C,onAppearCancelled:$}=t,I=String(e.key),R=du(r,e),M=(x,X)=>{x&&Kt(x,s,9,X)},L=(x,X)=>{const q=X[1];M(x,X),he(x)?x.every(z=>z.length<=1)&&q():x.length<=1&&q()},k={mode:i,persisted:a,beforeEnter(x){let X=l;if(!r.isMounted)if(o)X=A||l;else return;x[Pr]&&x[Pr](!0);const q=R[I];q&&nr(e,q)&&q.el[Pr]&&q.el[Pr](),M(X,[x])},enter(x){let X=u,q=d,z=f;if(!r.isMounted)if(o)X=m||u,q=C||d,z=$||f;else return;let ue=!1;const Ee=x[kn]=Oe=>{ue||(ue=!0,Oe?M(z,[x]):M(q,[x]),k.delayedLeave&&k.delayedLeave(),x[kn]=void 0)};X?L(X,[x,Ee]):Ee()},leave(x,X){const q=String(e.key);if(x[kn]&&x[kn](!0),r.isUnmounting)return X();M(h,[x]);let z=!1;const ue=x[Pr]=Ee=>{z||(z=!0,X(),Ee?M(E,[x]):M(y,[x]),x[Pr]=void 0,R[q]===e&&delete R[q])};R[q]=e,g?L(g,[x,ue]):ue()},clone(x){const X=on(x,t,r,s,n);return n&&n(X),X}};return k}function Uo(e){if(vn(e))return e=Dr(e),e.children=null,e}function Ba(e){if(!vn(e))return nu(e.type)&&e.children?uu(e.children):e;const{shapeFlag:t,children:r}=e;if(r){if(t&16)return r[0];if(t&32&&_e(r.default))return r.default()}}function ts(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ts(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function na(e,t=!1,r){let s=[],n=0;for(let o=0;o<e.length;o++){let i=e[o];const a=r==null?i.key:String(r)+String(i.key!=null?i.key:o);i.type===be?(i.patchFlag&128&&n++,s=s.concat(na(i.children,t,a))):(t||i.type!==pt)&&s.push(a!=null?Dr(i,{key:a}):i)}if(n>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Ns(e,t){return _e(e)?at({name:e.name},t,{setup:e}):e}function oa(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Xn(e,t,r,s,n=!1){if(he(e)){e.forEach((y,E)=>Xn(y,t&&(he(t)?t[E]:t),r,s,n));return}if(Es(s)&&!n){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Xn(e,t,r,s.component.subTree);return}const o=s.shapeFlag&4?So(s.component):s.el,i=n?null:o,{i:a,r:l}=e,u=t&&t.r,d=a.refs===ze?a.refs={}:a.refs,f=a.setupState,h=Fe(f),g=f===ze?()=>!1:y=>Ue(h,y);if(u!=null&&u!==l&&(Qe(u)?(d[u]=null,g(u)&&(f[u]=null)):rt(u)&&(u.value=null)),_e(l))mn(l,a,12,[i,d]);else{const y=Qe(l),E=rt(l);if(y||E){const A=()=>{if(e.f){const m=y?g(l)?f[l]:d[l]:l.value;n?he(m)&&Hi(m,o):he(m)?m.includes(o)||m.push(o):y?(d[l]=[o],g(l)&&(f[l]=d[l])):(l.value=[o],e.k&&(d[e.k]=l.value))}else y?(d[l]=i,g(l)&&(f[l]=i)):E&&(l.value=i,e.k&&(d[e.k]=i))};i?(A.id=-1,_t(A,r)):A()}}}const Ga=e=>e.nodeType===8;mo().requestIdleCallback;mo().cancelIdleCallback;function Ep(e,t){if(Ga(e)&&e.data==="["){let r=1,s=e.nextSibling;for(;s;){if(s.nodeType===1){if(t(s)===!1)break}else if(Ga(s))if(s.data==="]"){if(--r===0)break}else s.data==="["&&r++;s=s.nextSibling}}else t(e)}const Es=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function ET(e){_e(e)&&(e={loader:e});const{loader:t,loadingComponent:r,errorComponent:s,delay:n=200,hydrate:o,timeout:i,suspensible:a=!0,onError:l}=e;let u=null,d,f=0;const h=()=>(f++,u=null,g()),g=()=>{let y;return u||(y=u=t().catch(E=>{if(E=E instanceof Error?E:new Error(String(E)),l)return new Promise((A,m)=>{l(E,()=>A(h()),()=>m(E),f+1)});throw E}).then(E=>y!==u&&u?u:(E&&(E.__esModule||E[Symbol.toStringTag]==="Module")&&(E=E.default),d=E,E)))};return Ns({name:"AsyncComponentWrapper",__asyncLoader:g,__asyncHydrate(y,E,A){const m=o?()=>{const C=o(A,$=>Ep(y,$));C&&(E.bum||(E.bum=[])).push(C)}:A;d?m():g().then(()=>!E.isUnmounted&&m())},get __asyncResolved(){return d},setup(){const y=ut;if(oa(y),d)return()=>Bo(d,y);const E=$=>{u=null,Ds($,y,13,!s)};if(a&&y.suspense||Rs)return g().then($=>()=>Bo($,y)).catch($=>(E($),()=>s?ce(s,{error:$}):null));const A=oe(!1),m=oe(),C=oe(!!n);return n&&setTimeout(()=>{C.value=!1},n),i!=null&&setTimeout(()=>{if(!A.value&&!m.value){const $=new Error(`Async component timed out after ${i}ms.`);E($),m.value=$}},i),g().then(()=>{A.value=!0,y.parent&&vn(y.parent.vnode)&&y.parent.update()}).catch($=>{E($),m.value=$}),()=>{if(A.value&&d)return Bo(d,y);if(m.value&&s)return ce(s,{error:m.value});if(r&&!C.value)return ce(r)}}})}function Bo(e,t){const{ref:r,props:s,children:n,ce:o}=t.vnode,i=ce(e,s,n);return i.ref=r,i.ce=o,delete t.vnode.ce,i}const vn=e=>e.type.__isKeepAlive;function Sp(e,t){fu(e,"a",t)}function Ap(e,t){fu(e,"da",t)}function fu(e,t,r=ut){const s=e.__wdc||(e.__wdc=()=>{let n=r;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(yo(t,s,r),r){let n=r.parent;for(;n&&n.parent;)vn(n.parent.vnode)&&Cp(s,t,r,n),n=n.parent}}function Cp(e,t,r,s){const n=yo(t,e,s,!0);bo(()=>{Hi(s[t],n)},r)}function yo(e,t,r=ut,s=!1){if(r){const n=r[e]||(r[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Vr();const a=yn(r),l=Kt(t,r,e,i);return a(),jr(),l});return s?n.unshift(o):n.push(o),o}}const wr=e=>(t,r=ut)=>{(!Rs||e==="sp")&&yo(e,(...s)=>t(...s),r)},Pp=wr("bm"),ir=wr("m"),pu=wr("bu"),hu=wr("u"),ia=wr("bum"),bo=wr("um"),Tp=wr("sp"),kp=wr("rtg"),Rp=wr("rtc");function gu(e,t=ut){yo("ec",e,t)}const mu="components";function Ke(e,t){return yu(mu,e,!0,t)||e}const vu=Symbol.for("v-ndc");function _o(e){return Qe(e)?yu(mu,e,!1)||e:e||vu}function yu(e,t,r=!0,s=!1){const n=dt||ut;if(n){const o=n.type;{const a=Eh(o,!1);if(a&&(a===t||a===Vt(t)||a===go(Vt(t))))return o}const i=qa(n[e]||o[e],t)||qa(n.appContext[e],t);return!i&&s?o:i}}function qa(e,t){return e&&(e[t]||e[Vt(t)]||e[go(Vt(t))])}function Ne(e,t,r,s){let n;const o=r,i=he(e);if(i||Qe(e)){const a=i&&_s(e);let l=!1;a&&(l=!Lt(e),e=vo(e)),n=new Array(e.length);for(let u=0,d=e.length;u<d;u++)n[u]=t(l?vt(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,o)}else if(He(e))if(e[Symbol.iterator])n=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);n=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const d=a[l];n[l]=t(e[d],d,l,o)}}else n=[];return n}function Op(e,t,r={},s,n){if(dt.ce||dt.parent&&Es(dt.parent)&&dt.parent.ce)return t!=="default"&&(r.name=t),w(),br(be,null,[ce("slot",r,s&&s())],64);let o=e[t];o&&o._c&&(o._d=!1),w();const i=o&&bu(o(r)),a=r.key||i&&i.key,l=br(be,{key:(a&&!zt(a)?a:`_${t}`)+(!i&&s?"_fb":"")},i||(s?s():[]),i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),l}function bu(e){return e.some(t=>ks(t)?!(t.type===pt||t.type===be&&!bu(t.children)):!0)?e:null}const hi=e=>e?ju(e)?So(e):hi(e.parent):null,Ys=at(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>hi(e.parent),$root:e=>hi(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>wu(e),$forceUpdate:e=>e.f||(e.f=()=>{sa(e.update)}),$nextTick:e=>e.n||(e.n=Ft.bind(e.proxy)),$watch:e=>Zp.bind(e)}),Go=(e,t)=>e!==ze&&!e.__isScriptSetup&&Ue(e,t),Ip={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:s,data:n,props:o,accessCache:i,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return n[t];case 4:return r[t];case 3:return o[t]}else{if(Go(s,t))return i[t]=1,s[t];if(n!==ze&&Ue(n,t))return i[t]=2,n[t];if((u=e.propsOptions[0])&&Ue(u,t))return i[t]=3,o[t];if(r!==ze&&Ue(r,t))return i[t]=4,r[t];gi&&(i[t]=0)}}const d=Ys[t];let f,h;if(d)return t==="$attrs"&&mt(e.attrs,"get",""),d(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(r!==ze&&Ue(r,t))return i[t]=4,r[t];if(h=l.config.globalProperties,Ue(h,t))return h[t]},set({_:e},t,r){const{data:s,setupState:n,ctx:o}=e;return Go(n,t)?(n[t]=r,!0):s!==ze&&Ue(s,t)?(s[t]=r,!0):Ue(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:s,appContext:n,propsOptions:o}},i){let a;return!!r[i]||e!==ze&&Ue(e,i)||Go(t,i)||(a=o[0])&&Ue(a,i)||Ue(s,i)||Ue(Ys,i)||Ue(n.config.globalProperties,i)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:Ue(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function Ha(e){return he(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let gi=!0;function xp(e){const t=wu(e),r=e.proxy,s=e.ctx;gi=!1,t.beforeCreate&&za(t.beforeCreate,e,"bc");const{data:n,computed:o,methods:i,watch:a,provide:l,inject:u,created:d,beforeMount:f,mounted:h,beforeUpdate:g,updated:y,activated:E,deactivated:A,beforeDestroy:m,beforeUnmount:C,destroyed:$,unmounted:I,render:R,renderTracked:M,renderTriggered:L,errorCaptured:k,serverPrefetch:x,expose:X,inheritAttrs:q,components:z,directives:ue,filters:Ee}=t;if(u&&$p(u,s,null),i)for(const se in i){const Te=i[se];_e(Te)&&(s[se]=Te.bind(r))}if(n){const se=n.call(r,r);He(se)&&(e.data=or(se))}if(gi=!0,o)for(const se in o){const Te=o[se],st=_e(Te)?Te.bind(r,r):_e(Te.get)?Te.get.bind(r,r):qt,lt=!_e(Te)&&_e(Te.set)?Te.set.bind(r):qt,ct=le({get:st,set:lt});Object.defineProperty(s,se,{enumerable:!0,configurable:!0,get:()=>ct.value,set:nt=>ct.value=nt})}if(a)for(const se in a)_u(a[se],s,r,se);if(l){const se=_e(l)?l.call(r):l;Reflect.ownKeys(se).forEach(Te=>{$r(Te,se[Te])})}d&&za(d,e,"c");function ye(se,Te){he(Te)?Te.forEach(st=>se(st.bind(r))):Te&&se(Te.bind(r))}if(ye(Pp,f),ye(ir,h),ye(pu,g),ye(hu,y),ye(Sp,E),ye(Ap,A),ye(gu,k),ye(Rp,M),ye(kp,L),ye(ia,C),ye(bo,I),ye(Tp,x),he(X))if(X.length){const se=e.exposed||(e.exposed={});X.forEach(Te=>{Object.defineProperty(se,Te,{get:()=>r[Te],set:st=>r[Te]=st})})}else e.exposed||(e.exposed={});R&&e.render===qt&&(e.render=R),q!=null&&(e.inheritAttrs=q),z&&(e.components=z),ue&&(e.directives=ue),x&&oa(e)}function $p(e,t,r=qt){he(e)&&(e=mi(e));for(const s in e){const n=e[s];let o;He(n)?"default"in n?o=kt(n.from||s,n.default,!0):o=kt(n.from||s):o=kt(n),rt(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function za(e,t,r){Kt(he(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,r)}function _u(e,t,r,s){let n=s.includes(".")?$u(r,s):()=>r[s];if(Qe(e)){const o=t[e];_e(o)&&Et(n,o)}else if(_e(e))Et(n,e.bind(r));else if(He(e))if(he(e))e.forEach(o=>_u(o,t,r,s));else{const o=_e(e.handler)?e.handler.bind(r):t[e.handler];_e(o)&&Et(n,o,e)}}function wu(e){const t=e.type,{mixins:r,extends:s}=t,{mixins:n,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:!n.length&&!r&&!s?l=t:(l={},n.length&&n.forEach(u=>Yn(l,u,i,!0)),Yn(l,t,i)),He(t)&&o.set(t,l),l}function Yn(e,t,r,s=!1){const{mixins:n,extends:o}=t;o&&Yn(e,o,r,!0),n&&n.forEach(i=>Yn(e,i,r,!0));for(const i in t)if(!(s&&i==="expose")){const a=Fp[i]||r&&r[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Fp={data:Ka,props:Wa,emits:Wa,methods:Hs,computed:Hs,beforeCreate:bt,created:bt,beforeMount:bt,mounted:bt,beforeUpdate:bt,updated:bt,beforeDestroy:bt,beforeUnmount:bt,destroyed:bt,unmounted:bt,activated:bt,deactivated:bt,errorCaptured:bt,serverPrefetch:bt,components:Hs,directives:Hs,watch:Np,provide:Ka,inject:Dp};function Ka(e,t){return t?e?function(){return at(_e(e)?e.call(this,this):e,_e(t)?t.call(this,this):t)}:t:e}function Dp(e,t){return Hs(mi(e),mi(t))}function mi(e){if(he(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function bt(e,t){return e?[...new Set([].concat(e,t))]:t}function Hs(e,t){return e?at(Object.create(null),e,t):t}function Wa(e,t){return e?he(e)&&he(t)?[...new Set([...e,...t])]:at(Object.create(null),Ha(e),Ha(t??{})):t}function Np(e,t){if(!e)return t;if(!t)return e;const r=at(Object.create(null),e);for(const s in t)r[s]=bt(e[s],t[s]);return r}function Eu(){return{app:null,config:{isNativeTag:kf,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Mp=0;function Lp(e,t){return function(s,n=null){_e(s)||(s=at({},s)),n!=null&&!He(n)&&(n=null);const o=Eu(),i=new WeakSet,a=[];let l=!1;const u=o.app={_uid:Mp++,_component:s,_props:n,_container:null,_context:o,_instance:null,version:Ah,get config(){return o.config},set config(d){},use(d,...f){return i.has(d)||(d&&_e(d.install)?(i.add(d),d.install(u,...f)):_e(d)&&(i.add(d),d(u,...f))),u},mixin(d){return o.mixins.includes(d)||o.mixins.push(d),u},component(d,f){return f?(o.components[d]=f,u):o.components[d]},directive(d,f){return f?(o.directives[d]=f,u):o.directives[d]},mount(d,f,h){if(!l){const g=u._ceVNode||ce(s,n);return g.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),e(g,d,h),l=!0,u._container=d,d.__vue_app__=u,So(g.component)}},onUnmount(d){a.push(d)},unmount(){l&&(Kt(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(d,f){return o.provides[d]=f,u},runWithContext(d){const f=Ss;Ss=u;try{return d()}finally{Ss=f}}};return u}}let Ss=null;function $r(e,t){if(ut){let r=ut.provides;const s=ut.parent&&ut.parent.provides;s===r&&(r=ut.provides=Object.create(s)),r[e]=t}}function kt(e,t,r=!1){const s=ut||dt;if(s||Ss){const n=Ss?Ss._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return r&&_e(t)?t.call(s&&s.proxy):t}}const Su={},Au=()=>Object.create(Su),Cu=e=>Object.getPrototypeOf(e)===Su;function Vp(e,t,r,s=!1){const n={},o=Au();e.propsDefaults=Object.create(null),Pu(e,t,n,o);for(const i in e.propsOptions[0])i in n||(n[i]=void 0);r?e.props=s?n:Wc(n):e.type.props?e.props=n:e.props=o,e.attrs=o}function jp(e,t,r,s){const{props:n,attrs:o,vnode:{patchFlag:i}}=e,a=Fe(n),[l]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const d=e.vnode.dynamicProps;for(let f=0;f<d.length;f++){let h=d[f];if(wo(e.emitsOptions,h))continue;const g=t[h];if(l)if(Ue(o,h))g!==o[h]&&(o[h]=g,u=!0);else{const y=Vt(h);n[y]=vi(l,a,y,g,e,!1)}else g!==o[h]&&(o[h]=g,u=!0)}}}else{Pu(e,t,n,o)&&(u=!0);let d;for(const f in a)(!t||!Ue(t,f)&&((d=Lr(f))===f||!Ue(t,d)))&&(l?r&&(r[f]!==void 0||r[d]!==void 0)&&(n[f]=vi(l,a,f,void 0,e,!0)):delete n[f]);if(o!==a)for(const f in o)(!t||!Ue(t,f))&&(delete o[f],u=!0)}u&&mr(e.attrs,"set","")}function Pu(e,t,r,s){const[n,o]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(Ks(l))continue;const u=t[l];let d;n&&Ue(n,d=Vt(l))?!o||!o.includes(d)?r[d]=u:(a||(a={}))[d]=u:wo(e.emitsOptions,l)||(!(l in s)||u!==s[l])&&(s[l]=u,i=!0)}if(o){const l=Fe(r),u=a||ze;for(let d=0;d<o.length;d++){const f=o[d];r[f]=vi(n,l,f,u[f],e,!Ue(u,f))}}return i}function vi(e,t,r,s,n,o){const i=e[r];if(i!=null){const a=Ue(i,"default");if(a&&s===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&_e(l)){const{propsDefaults:u}=n;if(r in u)s=u[r];else{const d=yn(n);s=u[r]=l.call(null,t),d()}}else s=l;n.ce&&n.ce._setProp(r,s)}i[0]&&(o&&!a?s=!1:i[1]&&(s===""||s===Lr(r))&&(s=!0))}return s}const Up=new WeakMap;function Tu(e,t,r=!1){const s=r?Up:t.propsCache,n=s.get(e);if(n)return n;const o=e.props,i={},a=[];let l=!1;if(!_e(e)){const d=f=>{l=!0;const[h,g]=Tu(f,t,!0);at(i,h),g&&a.push(...g)};!r&&t.mixins.length&&t.mixins.forEach(d),e.extends&&d(e.extends),e.mixins&&e.mixins.forEach(d)}if(!o&&!l)return He(e)&&s.set(e,ys),ys;if(he(o))for(let d=0;d<o.length;d++){const f=Vt(o[d]);Ja(f)&&(i[f]=ze)}else if(o)for(const d in o){const f=Vt(d);if(Ja(f)){const h=o[d],g=i[f]=he(h)||_e(h)?{type:h}:at({},h),y=g.type;let E=!1,A=!0;if(he(y))for(let m=0;m<y.length;++m){const C=y[m],$=_e(C)&&C.name;if($==="Boolean"){E=!0;break}else $==="String"&&(A=!1)}else E=_e(y)&&y.name==="Boolean";g[0]=E,g[1]=A,(E||Ue(g,"default"))&&a.push(f)}}const u=[i,a];return He(e)&&s.set(e,u),u}function Ja(e){return e[0]!=="$"&&!Ks(e)}const ku=e=>e[0]==="_"||e==="$stable",aa=e=>he(e)?e.map(Ut):[Ut(e)],Bp=(e,t,r)=>{if(t._n)return t;const s=ot((...n)=>aa(t(...n)),r);return s._c=!1,s},Ru=(e,t,r)=>{const s=e._ctx;for(const n in e){if(ku(n))continue;const o=e[n];if(_e(o))t[n]=Bp(n,o,s);else if(o!=null){const i=aa(o);t[n]=()=>i}}},Ou=(e,t)=>{const r=aa(t);e.slots.default=()=>r},Iu=(e,t,r)=>{for(const s in t)(r||s!=="_")&&(e[s]=t[s])},Gp=(e,t,r)=>{const s=e.slots=Au();if(e.vnode.shapeFlag&32){const n=t._;n?(Iu(s,t,r),r&&kc(s,"_",n,!0)):Ru(t,s)}else t&&Ou(e,t)},qp=(e,t,r)=>{const{vnode:s,slots:n}=e;let o=!0,i=ze;if(s.shapeFlag&32){const a=t._;a?r&&a===1?o=!1:Iu(n,t,r):(o=!t.$stable,Ru(t,n)),i=t}else t&&(Ou(e,t),i={default:1});if(o)for(const a in n)!ku(a)&&i[a]==null&&delete n[a]},_t=uh;function Hp(e){return zp(e)}function zp(e,t){const r=mo();r.__VUE__=!0;const{insert:s,remove:n,patchProp:o,createElement:i,createText:a,createComment:l,setText:u,setElementText:d,parentNode:f,nextSibling:h,setScopeId:g=qt,insertStaticContent:y}=e,E=(v,_,T,V=null,B=null,U=null,te=void 0,Y=null,Z=!!_.dynamicChildren)=>{if(v===_)return;v&&!nr(v,_)&&(V=O(v),nt(v,B,U,!0),v=null),_.patchFlag===-2&&(Z=!1,_.dynamicChildren=null);const{type:W,ref:pe,shapeFlag:re}=_;switch(W){case Eo:A(v,_,T,V);break;case pt:m(v,_,T,V);break;case Zs:v==null&&C(_,T,V,te);break;case be:z(v,_,T,V,B,U,te,Y,Z);break;default:re&1?R(v,_,T,V,B,U,te,Y,Z):re&6?ue(v,_,T,V,B,U,te,Y,Z):(re&64||re&128)&&W.process(v,_,T,V,B,U,te,Y,Z,ie)}pe!=null&&B&&Xn(pe,v&&v.ref,U,_||v,!_)},A=(v,_,T,V)=>{if(v==null)s(_.el=a(_.children),T,V);else{const B=_.el=v.el;_.children!==v.children&&u(B,_.children)}},m=(v,_,T,V)=>{v==null?s(_.el=l(_.children||""),T,V):_.el=v.el},C=(v,_,T,V)=>{[v.el,v.anchor]=y(v.children,_,T,V,v.el,v.anchor)},$=({el:v,anchor:_},T,V)=>{let B;for(;v&&v!==_;)B=h(v),s(v,T,V),v=B;s(_,T,V)},I=({el:v,anchor:_})=>{let T;for(;v&&v!==_;)T=h(v),n(v),v=T;n(_)},R=(v,_,T,V,B,U,te,Y,Z)=>{_.type==="svg"?te="svg":_.type==="math"&&(te="mathml"),v==null?M(_,T,V,B,U,te,Y,Z):x(v,_,B,U,te,Y,Z)},M=(v,_,T,V,B,U,te,Y)=>{let Z,W;const{props:pe,shapeFlag:re,transition:de,dirs:me}=v;if(Z=v.el=i(v.type,U,pe&&pe.is,pe),re&8?d(Z,v.children):re&16&&k(v.children,Z,null,V,B,qo(v,U),te,Y),me&&Br(v,null,V,"created"),L(Z,v,v.scopeId,te,V),pe){for(const Ve in pe)Ve!=="value"&&!Ks(Ve)&&o(Z,Ve,null,pe[Ve],U,V);"value"in pe&&o(Z,"value",null,pe.value,U),(W=pe.onVnodeBeforeMount)&&Qt(W,V,v)}me&&Br(v,null,V,"beforeMount");const Re=Kp(B,de);Re&&de.beforeEnter(Z),s(Z,_,T),((W=pe&&pe.onVnodeMounted)||Re||me)&&_t(()=>{W&&Qt(W,V,v),Re&&de.enter(Z),me&&Br(v,null,V,"mounted")},B)},L=(v,_,T,V,B)=>{if(T&&g(v,T),V)for(let U=0;U<V.length;U++)g(v,V[U]);if(B){let U=B.subTree;if(_===U||Du(U.type)&&(U.ssContent===_||U.ssFallback===_)){const te=B.vnode;L(v,te,te.scopeId,te.slotScopeIds,B.parent)}}},k=(v,_,T,V,B,U,te,Y,Z=0)=>{for(let W=Z;W<v.length;W++){const pe=v[W]=Y?Tr(v[W]):Ut(v[W]);E(null,pe,_,T,V,B,U,te,Y)}},x=(v,_,T,V,B,U,te)=>{const Y=_.el=v.el;let{patchFlag:Z,dynamicChildren:W,dirs:pe}=_;Z|=v.patchFlag&16;const re=v.props||ze,de=_.props||ze;let me;if(T&&Gr(T,!1),(me=de.onVnodeBeforeUpdate)&&Qt(me,T,_,v),pe&&Br(_,v,T,"beforeUpdate"),T&&Gr(T,!0),(re.innerHTML&&de.innerHTML==null||re.textContent&&de.textContent==null)&&d(Y,""),W?X(v.dynamicChildren,W,Y,T,V,qo(_,B),U):te||Te(v,_,Y,null,T,V,qo(_,B),U,!1),Z>0){if(Z&16)q(Y,re,de,T,B);else if(Z&2&&re.class!==de.class&&o(Y,"class",null,de.class,B),Z&4&&o(Y,"style",re.style,de.style,B),Z&8){const Re=_.dynamicProps;for(let Ve=0;Ve<Re.length;Ve++){const $e=Re[Ve],ft=re[$e],F=de[$e];(F!==ft||$e==="value")&&o(Y,$e,ft,F,B,T)}}Z&1&&v.children!==_.children&&d(Y,_.children)}else!te&&W==null&&q(Y,re,de,T,B);((me=de.onVnodeUpdated)||pe)&&_t(()=>{me&&Qt(me,T,_,v),pe&&Br(_,v,T,"updated")},V)},X=(v,_,T,V,B,U,te)=>{for(let Y=0;Y<_.length;Y++){const Z=v[Y],W=_[Y],pe=Z.el&&(Z.type===be||!nr(Z,W)||Z.shapeFlag&70)?f(Z.el):T;E(Z,W,pe,null,V,B,U,te,!0)}},q=(v,_,T,V,B)=>{if(_!==T){if(_!==ze)for(const U in _)!Ks(U)&&!(U in T)&&o(v,U,_[U],null,B,V);for(const U in T){if(Ks(U))continue;const te=T[U],Y=_[U];te!==Y&&U!=="value"&&o(v,U,Y,te,B,V)}"value"in T&&o(v,"value",_.value,T.value,B)}},z=(v,_,T,V,B,U,te,Y,Z)=>{const W=_.el=v?v.el:a(""),pe=_.anchor=v?v.anchor:a("");let{patchFlag:re,dynamicChildren:de,slotScopeIds:me}=_;me&&(Y=Y?Y.concat(me):me),v==null?(s(W,T,V),s(pe,T,V),k(_.children||[],T,pe,B,U,te,Y,Z)):re>0&&re&64&&de&&v.dynamicChildren?(X(v.dynamicChildren,de,T,B,U,te,Y),(_.key!=null||B&&_===B.subTree)&&la(v,_,!0)):Te(v,_,T,pe,B,U,te,Y,Z)},ue=(v,_,T,V,B,U,te,Y,Z)=>{_.slotScopeIds=Y,v==null?_.shapeFlag&512?B.ctx.activate(_,T,V,te,Z):Ee(_,T,V,B,U,te,Z):Oe(v,_,Z)},Ee=(v,_,T,V,B,U,te)=>{const Y=v.component=vh(v,V,B);if(vn(v)&&(Y.ctx.renderer=ie),yh(Y,!1,te),Y.asyncDep){if(B&&B.registerDep(Y,ye,te),!v.el){const Z=Y.subTree=ce(pt);m(null,Z,_,T)}}else ye(Y,v,_,T,B,U,te)},Oe=(v,_,T)=>{const V=_.component=v.component;if(nh(v,_,T))if(V.asyncDep&&!V.asyncResolved){se(V,_,T);return}else V.next=_,V.update();else _.el=v.el,V.vnode=_},ye=(v,_,T,V,B,U,te)=>{const Y=()=>{if(v.isMounted){let{next:re,bu:de,u:me,parent:Re,vnode:Ve}=v;{const b=xu(v);if(b){re&&(re.el=Ve.el,se(v,re,te)),b.asyncDep.then(()=>{v.isUnmounted||Y()});return}}let $e=re,ft;Gr(v,!1),re?(re.el=Ve.el,se(v,re,te)):re=Ve,de&&Dn(de),(ft=re.props&&re.props.onVnodeBeforeUpdate)&&Qt(ft,Re,re,Ve),Gr(v,!0);const F=Ya(v),p=v.subTree;v.subTree=F,E(p,F,f(p.el),O(p),v,B,U),re.el=F.el,$e===null&&ua(v,F.el),me&&_t(me,B),(ft=re.props&&re.props.onVnodeUpdated)&&_t(()=>Qt(ft,Re,re,Ve),B)}else{let re;const{el:de,props:me}=_,{bm:Re,m:Ve,parent:$e,root:ft,type:F}=v,p=Es(_);Gr(v,!1),Re&&Dn(Re),!p&&(re=me&&me.onVnodeBeforeMount)&&Qt(re,$e,_),Gr(v,!0);{ft.ce&&ft.ce._injectChildStyle(F);const b=v.subTree=Ya(v);E(null,b,T,V,v,B,U),_.el=b.el}if(Ve&&_t(Ve,B),!p&&(re=me&&me.onVnodeMounted)){const b=_;_t(()=>Qt(re,$e,b),B)}(_.shapeFlag&256||$e&&Es($e.vnode)&&$e.vnode.shapeFlag&256)&&v.a&&_t(v.a,B),v.isMounted=!0,_=T=V=null}};v.scope.on();const Z=v.effect=new Fc(Y);v.scope.off();const W=v.update=Z.run.bind(Z),pe=v.job=Z.runIfDirty.bind(Z);pe.i=v,pe.id=v.uid,Z.scheduler=()=>sa(pe),Gr(v,!0),W()},se=(v,_,T)=>{_.component=v;const V=v.vnode.props;v.vnode=_,v.next=null,jp(v,_.props,V,T),qp(v,_.children,T),Vr(),La(v),jr()},Te=(v,_,T,V,B,U,te,Y,Z=!1)=>{const W=v&&v.children,pe=v?v.shapeFlag:0,re=_.children,{patchFlag:de,shapeFlag:me}=_;if(de>0){if(de&128){lt(W,re,T,V,B,U,te,Y,Z);return}else if(de&256){st(W,re,T,V,B,U,te,Y,Z);return}}me&8?(pe&16&&ne(W,B,U),re!==W&&d(T,re)):pe&16?me&16?lt(W,re,T,V,B,U,te,Y,Z):ne(W,B,U,!0):(pe&8&&d(T,""),me&16&&k(re,T,V,B,U,te,Y,Z))},st=(v,_,T,V,B,U,te,Y,Z)=>{v=v||ys,_=_||ys;const W=v.length,pe=_.length,re=Math.min(W,pe);let de;for(de=0;de<re;de++){const me=_[de]=Z?Tr(_[de]):Ut(_[de]);E(v[de],me,T,null,B,U,te,Y,Z)}W>pe?ne(v,B,U,!0,!1,re):k(_,T,V,B,U,te,Y,Z,re)},lt=(v,_,T,V,B,U,te,Y,Z)=>{let W=0;const pe=_.length;let re=v.length-1,de=pe-1;for(;W<=re&&W<=de;){const me=v[W],Re=_[W]=Z?Tr(_[W]):Ut(_[W]);if(nr(me,Re))E(me,Re,T,null,B,U,te,Y,Z);else break;W++}for(;W<=re&&W<=de;){const me=v[re],Re=_[de]=Z?Tr(_[de]):Ut(_[de]);if(nr(me,Re))E(me,Re,T,null,B,U,te,Y,Z);else break;re--,de--}if(W>re){if(W<=de){const me=de+1,Re=me<pe?_[me].el:V;for(;W<=de;)E(null,_[W]=Z?Tr(_[W]):Ut(_[W]),T,Re,B,U,te,Y,Z),W++}}else if(W>de)for(;W<=re;)nt(v[W],B,U,!0),W++;else{const me=W,Re=W,Ve=new Map;for(W=Re;W<=de;W++){const K=_[W]=Z?Tr(_[W]):Ut(_[W]);K.key!=null&&Ve.set(K.key,W)}let $e,ft=0;const F=de-Re+1;let p=!1,b=0;const P=new Array(F);for(W=0;W<F;W++)P[W]=0;for(W=me;W<=re;W++){const K=v[W];if(ft>=F){nt(K,B,U,!0);continue}let Q;if(K.key!=null)Q=Ve.get(K.key);else for($e=Re;$e<=de;$e++)if(P[$e-Re]===0&&nr(K,_[$e])){Q=$e;break}Q===void 0?nt(K,B,U,!0):(P[Q-Re]=W+1,Q>=b?b=Q:p=!0,E(K,_[Q],T,null,B,U,te,Y,Z),ft++)}const G=p?Wp(P):ys;for($e=G.length-1,W=F-1;W>=0;W--){const K=Re+W,Q=_[K],ae=K+1<pe?_[K+1].el:V;P[W]===0?E(null,Q,T,ae,B,U,te,Y,Z):p&&($e<0||W!==G[$e]?ct(Q,T,ae,2):$e--)}}},ct=(v,_,T,V,B=null)=>{const{el:U,type:te,transition:Y,children:Z,shapeFlag:W}=v;if(W&6){ct(v.component.subTree,_,T,V);return}if(W&128){v.suspense.move(_,T,V);return}if(W&64){te.move(v,_,T,ie);return}if(te===be){s(U,_,T);for(let re=0;re<Z.length;re++)ct(Z[re],_,T,V);s(v.anchor,_,T);return}if(te===Zs){$(v,_,T);return}if(V!==2&&W&1&&Y)if(V===0)Y.beforeEnter(U),s(U,_,T),_t(()=>Y.enter(U),B);else{const{leave:re,delayLeave:de,afterLeave:me}=Y,Re=()=>s(U,_,T),Ve=()=>{re(U,()=>{Re(),me&&me()})};de?de(U,Re,Ve):Ve()}else s(U,_,T)},nt=(v,_,T,V=!1,B=!1)=>{const{type:U,props:te,ref:Y,children:Z,dynamicChildren:W,shapeFlag:pe,patchFlag:re,dirs:de,cacheIndex:me}=v;if(re===-2&&(B=!1),Y!=null&&Xn(Y,null,T,v,!0),me!=null&&(_.renderCache[me]=void 0),pe&256){_.ctx.deactivate(v);return}const Re=pe&1&&de,Ve=!Es(v);let $e;if(Ve&&($e=te&&te.onVnodeBeforeUnmount)&&Qt($e,_,v),pe&6)Zt(v.component,T,V);else{if(pe&128){v.suspense.unmount(T,V);return}Re&&Br(v,null,_,"beforeUnmount"),pe&64?v.type.remove(v,_,T,ie,V):W&&!W.hasOnce&&(U!==be||re>0&&re&64)?ne(W,_,T,!1,!0):(U===be&&re&384||!B&&pe&16)&&ne(Z,_,T),V&&jt(v)}(Ve&&($e=te&&te.onVnodeUnmounted)||Re)&&_t(()=>{$e&&Qt($e,_,v),Re&&Br(v,null,_,"unmounted")},T)},jt=v=>{const{type:_,el:T,anchor:V,transition:B}=v;if(_===be){Ze(T,V);return}if(_===Zs){I(v);return}const U=()=>{n(T),B&&!B.persisted&&B.afterLeave&&B.afterLeave()};if(v.shapeFlag&1&&B&&!B.persisted){const{leave:te,delayLeave:Y}=B,Z=()=>te(T,U);Y?Y(v.el,U,Z):Z()}else U()},Ze=(v,_)=>{let T;for(;v!==_;)T=h(v),n(v),v=T;n(_)},Zt=(v,_,T)=>{const{bum:V,scope:B,job:U,subTree:te,um:Y,m:Z,a:W}=v;Xa(Z),Xa(W),V&&Dn(V),B.stop(),U&&(U.flags|=8,nt(te,v,_,T)),Y&&_t(Y,_),_t(()=>{v.isUnmounted=!0},_),_&&_.pendingBranch&&!_.isUnmounted&&v.asyncDep&&!v.asyncResolved&&v.suspenseId===_.pendingId&&(_.deps--,_.deps===0&&_.resolve())},ne=(v,_,T,V=!1,B=!1,U=0)=>{for(let te=U;te<v.length;te++)nt(v[te],_,T,V,B)},O=v=>{if(v.shapeFlag&6)return O(v.component.subTree);if(v.shapeFlag&128)return v.suspense.next();const _=h(v.anchor||v.el),T=_&&_[su];return T?h(T):_};let J=!1;const H=(v,_,T)=>{v==null?_._vnode&&nt(_._vnode,null,null,!0):E(_._vnode||null,v,_,null,null,null,T),_._vnode=v,J||(J=!0,La(),eu(),J=!1)},ie={p:E,um:nt,m:ct,r:jt,mt:Ee,mc:k,pc:Te,pbc:X,n:O,o:e};return{render:H,hydrate:void 0,createApp:Lp(H)}}function qo({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function Gr({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Kp(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function la(e,t,r=!1){const s=e.children,n=t.children;if(he(s)&&he(n))for(let o=0;o<s.length;o++){const i=s[o];let a=n[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[o]=Tr(n[o]),a.el=i.el),!r&&a.patchFlag!==-2&&la(i,a)),a.type===Eo&&(a.el=i.el)}}function Wp(e){const t=e.slice(),r=[0];let s,n,o,i,a;const l=e.length;for(s=0;s<l;s++){const u=e[s];if(u!==0){if(n=r[r.length-1],e[n]<u){t[s]=n,r.push(s);continue}for(o=0,i=r.length-1;o<i;)a=o+i>>1,e[r[a]]<u?o=a+1:i=a;u<e[r[o]]&&(o>0&&(t[s]=r[o-1]),r[o]=s)}}for(o=r.length,i=r[o-1];o-- >0;)r[o]=i,i=t[i];return r}function xu(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:xu(t)}function Xa(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Jp=Symbol.for("v-scx"),Xp=()=>kt(Jp);function Yp(e,t){return ca(e,null,t)}function Et(e,t,r){return ca(e,t,r)}function ca(e,t,r=ze){const{immediate:s,deep:n,flush:o,once:i}=r,a=at({},r),l=t&&s||!t&&o!=="post";let u;if(Rs){if(o==="sync"){const g=Xp();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!l){const g=()=>{};return g.stop=qt,g.resume=qt,g.pause=qt,g}}const d=ut;a.call=(g,y,E)=>Kt(g,d,y,E);let f=!1;o==="post"?a.scheduler=g=>{_t(g,d&&d.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(g,y)=>{y?g():sa(g)}),a.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,d&&(g.id=d.uid,g.i=d))};const h=mp(e,t,a);return Rs&&(u?u.push(h):l&&h()),h}function Zp(e,t,r){const s=this.proxy,n=Qe(e)?e.includes(".")?$u(s,e):()=>s[e]:e.bind(s,s);let o;_e(t)?o=t:(o=t.handler,r=t);const i=yn(this),a=ca(n,o.bind(s),r);return i(),a}function $u(e,t){const r=t.split(".");return()=>{let s=e;for(let n=0;n<r.length&&s;n++)s=s[r[n]];return s}}const Qp=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Vt(t)}Modifiers`]||e[`${Lr(t)}Modifiers`];function eh(e,t,...r){if(e.isUnmounted)return;const s=e.vnode.props||ze;let n=r;const o=t.startsWith("update:"),i=o&&Qp(s,t.slice(7));i&&(i.trim&&(n=r.map(d=>Qe(d)?d.trim():d)),i.number&&(n=r.map(qn)));let a,l=s[a=No(t)]||s[a=No(Vt(t))];!l&&o&&(l=s[a=No(Lr(t))]),l&&Kt(l,e,6,n);const u=s[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Kt(u,e,6,n)}}function Fu(e,t,r=!1){const s=t.emitsCache,n=s.get(e);if(n!==void 0)return n;const o=e.emits;let i={},a=!1;if(!_e(e)){const l=u=>{const d=Fu(u,t,!0);d&&(a=!0,at(i,d))};!r&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(He(e)&&s.set(e,null),null):(he(o)?o.forEach(l=>i[l]=null):at(i,o),He(e)&&s.set(e,i),i)}function wo(e,t){return!e||!po(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ue(e,t[0].toLowerCase()+t.slice(1))||Ue(e,Lr(t))||Ue(e,t))}function Ya(e){const{type:t,vnode:r,proxy:s,withProxy:n,propsOptions:[o],slots:i,attrs:a,emit:l,render:u,renderCache:d,props:f,data:h,setupState:g,ctx:y,inheritAttrs:E}=e,A=Jn(e);let m,C;try{if(r.shapeFlag&4){const I=n||s,R=I;m=Ut(u.call(R,I,d,f,g,h,y)),C=a}else{const I=t;m=Ut(I.length>1?I(f,{attrs:a,slots:i,emit:l}):I(f,null)),C=t.props?a:rh(a)}}catch(I){Qs.length=0,Ds(I,e,1),m=ce(pt)}let $=m;if(C&&E!==!1){const I=Object.keys(C),{shapeFlag:R}=$;I.length&&R&7&&(o&&I.some(qi)&&(C=sh(C,o)),$=Dr($,C,!1,!0))}return r.dirs&&($=Dr($,null,!1,!0),$.dirs=$.dirs?$.dirs.concat(r.dirs):r.dirs),r.transition&&ts($,r.transition),m=$,Jn(A),m}function th(e,t=!0){let r;for(let s=0;s<e.length;s++){const n=e[s];if(ks(n)){if(n.type!==pt||n.children==="v-if"){if(r)return;r=n}}else return}return r}const rh=e=>{let t;for(const r in e)(r==="class"||r==="style"||po(r))&&((t||(t={}))[r]=e[r]);return t},sh=(e,t)=>{const r={};for(const s in e)(!qi(s)||!(s.slice(9)in t))&&(r[s]=e[s]);return r};function nh(e,t,r){const{props:s,children:n,component:o}=e,{props:i,children:a,patchFlag:l}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&l>=0){if(l&1024)return!0;if(l&16)return s?Za(s,i,u):!!i;if(l&8){const d=t.dynamicProps;for(let f=0;f<d.length;f++){const h=d[f];if(i[h]!==s[h]&&!wo(u,h))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:s===i?!1:s?i?Za(s,i,u):!0:!!i;return!1}function Za(e,t,r){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let n=0;n<s.length;n++){const o=s[n];if(t[o]!==e[o]&&!wo(r,o))return!0}return!1}function ua({vnode:e,parent:t},r){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=r,t=t.parent;else break}}const Du=e=>e.__isSuspense;let yi=0;const oh={name:"Suspense",__isSuspense:!0,process(e,t,r,s,n,o,i,a,l,u){if(e==null)ih(t,r,s,n,o,i,a,l,u);else{if(o&&o.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}ah(e,t,r,s,n,i,a,l,u)}},hydrate:lh,normalize:ch},ST=oh;function an(e,t){const r=e.props&&e.props[t];_e(r)&&r()}function ih(e,t,r,s,n,o,i,a,l){const{p:u,o:{createElement:d}}=l,f=d("div"),h=e.suspense=Nu(e,n,s,t,f,r,o,i,a,l);u(null,h.pendingBranch=e.ssContent,f,null,s,h,o,i),h.deps>0?(an(e,"onPending"),an(e,"onFallback"),u(null,e.ssFallback,t,r,s,null,o,i),As(h,e.ssFallback)):h.resolve(!1,!0)}function ah(e,t,r,s,n,o,i,a,{p:l,um:u,o:{createElement:d}}){const f=t.suspense=e.suspense;f.vnode=t,t.el=e.el;const h=t.ssContent,g=t.ssFallback,{activeBranch:y,pendingBranch:E,isInFallback:A,isHydrating:m}=f;if(E)f.pendingBranch=h,nr(h,E)?(l(E,h,f.hiddenContainer,null,n,f,o,i,a),f.deps<=0?f.resolve():A&&(m||(l(y,g,r,s,n,null,o,i,a),As(f,g)))):(f.pendingId=yi++,m?(f.isHydrating=!1,f.activeBranch=E):u(E,n,f),f.deps=0,f.effects.length=0,f.hiddenContainer=d("div"),A?(l(null,h,f.hiddenContainer,null,n,f,o,i,a),f.deps<=0?f.resolve():(l(y,g,r,s,n,null,o,i,a),As(f,g))):y&&nr(h,y)?(l(y,h,r,s,n,f,o,i,a),f.resolve(!0)):(l(null,h,f.hiddenContainer,null,n,f,o,i,a),f.deps<=0&&f.resolve()));else if(y&&nr(h,y))l(y,h,r,s,n,f,o,i,a),As(f,h);else if(an(t,"onPending"),f.pendingBranch=h,h.shapeFlag&512?f.pendingId=h.component.suspenseId:f.pendingId=yi++,l(null,h,f.hiddenContainer,null,n,f,o,i,a),f.deps<=0)f.resolve();else{const{timeout:C,pendingId:$}=f;C>0?setTimeout(()=>{f.pendingId===$&&f.fallback(g)},C):C===0&&f.fallback(g)}}function Nu(e,t,r,s,n,o,i,a,l,u,d=!1){const{p:f,m:h,um:g,n:y,o:{parentNode:E,remove:A}}=u;let m;const C=dh(e);C&&t&&t.pendingBranch&&(m=t.pendingId,t.deps++);const $=e.props?Rc(e.props.timeout):void 0,I=o,R={vnode:e,parent:t,parentComponent:r,namespace:i,container:s,hiddenContainer:n,deps:0,pendingId:yi++,timeout:typeof $=="number"?$:-1,activeBranch:null,pendingBranch:null,isInFallback:!d,isHydrating:d,isUnmounted:!1,effects:[],resolve(M=!1,L=!1){const{vnode:k,activeBranch:x,pendingBranch:X,pendingId:q,effects:z,parentComponent:ue,container:Ee}=R;let Oe=!1;R.isHydrating?R.isHydrating=!1:M||(Oe=x&&X.transition&&X.transition.mode==="out-in",Oe&&(x.transition.afterLeave=()=>{q===R.pendingId&&(h(X,Ee,o===I?y(x):o,0),Wn(z))}),x&&(E(x.el)===Ee&&(o=y(x)),g(x,ue,R,!0)),Oe||h(X,Ee,o,0)),As(R,X),R.pendingBranch=null,R.isInFallback=!1;let ye=R.parent,se=!1;for(;ye;){if(ye.pendingBranch){ye.effects.push(...z),se=!0;break}ye=ye.parent}!se&&!Oe&&Wn(z),R.effects=[],C&&t&&t.pendingBranch&&m===t.pendingId&&(t.deps--,t.deps===0&&!L&&t.resolve()),an(k,"onResolve")},fallback(M){if(!R.pendingBranch)return;const{vnode:L,activeBranch:k,parentComponent:x,container:X,namespace:q}=R;an(L,"onFallback");const z=y(k),ue=()=>{R.isInFallback&&(f(null,M,X,z,x,null,q,a,l),As(R,M))},Ee=M.transition&&M.transition.mode==="out-in";Ee&&(k.transition.afterLeave=ue),R.isInFallback=!0,g(k,x,null,!0),Ee||ue()},move(M,L,k){R.activeBranch&&h(R.activeBranch,M,L,k),R.container=M},next(){return R.activeBranch&&y(R.activeBranch)},registerDep(M,L,k){const x=!!R.pendingBranch;x&&R.deps++;const X=M.vnode.el;M.asyncDep.catch(q=>{Ds(q,M,0)}).then(q=>{if(M.isUnmounted||R.isUnmounted||R.pendingId!==M.suspenseId)return;M.asyncResolved=!0;const{vnode:z}=M;_i(M,q),X&&(z.el=X);const ue=!X&&M.subTree.el;L(M,z,E(X||M.subTree.el),X?null:y(M.subTree),R,i,k),ue&&A(ue),ua(M,z.el),x&&--R.deps===0&&R.resolve()})},unmount(M,L){R.isUnmounted=!0,R.activeBranch&&g(R.activeBranch,r,M,L),R.pendingBranch&&g(R.pendingBranch,r,M,L)}};return R}function lh(e,t,r,s,n,o,i,a,l){const u=t.suspense=Nu(t,s,r,e.parentNode,document.createElement("div"),null,n,o,i,a,!0),d=l(e,u.pendingBranch=t.ssContent,r,u,o,i);return u.deps===0&&u.resolve(!1,!0),d}function ch(e){const{shapeFlag:t,children:r}=e,s=t&32;e.ssContent=Qa(s?r.default:r),e.ssFallback=s?Qa(r.fallback):ce(pt)}function Qa(e){let t;if(_e(e)){const r=Ts&&e._c;r&&(e._d=!1,w()),e=e(),r&&(e._d=!0,t=Tt,Mu())}return he(e)&&(e=th(e)),e=Ut(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(r=>r!==e)),e}function uh(e,t){t&&t.pendingBranch?he(e)?t.effects.push(...e):t.effects.push(e):Wn(e)}function As(e,t){e.activeBranch=t;const{vnode:r,parentComponent:s}=e;let n=t.el;for(;!n&&t.component;)t=t.component.subTree,n=t.el;r.el=n,s&&s.subTree===r&&(s.vnode.el=n,ua(s,n))}function dh(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const be=Symbol.for("v-fgt"),Eo=Symbol.for("v-txt"),pt=Symbol.for("v-cmt"),Zs=Symbol.for("v-stc"),Qs=[];let Tt=null;function w(e=!1){Qs.push(Tt=e?null:[])}function Mu(){Qs.pop(),Tt=Qs[Qs.length-1]||null}let Ts=1;function el(e,t=!1){Ts+=e,e<0&&Tt&&t&&(Tt.hasOnce=!0)}function Lu(e){return e.dynamicChildren=Ts>0?Tt||ys:null,Mu(),Ts>0&&Tt&&Tt.push(e),e}function S(e,t,r,s,n,o){return Lu(c(e,t,r,s,n,o,!0))}function br(e,t,r,s,n){return Lu(ce(e,t,r,s,n,!0))}function ks(e){return e?e.__v_isVNode===!0:!1}function nr(e,t){return e.type===t.type&&e.key===t.key}const Vu=({key:e})=>e??null,Mn=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?Qe(e)||rt(e)||_e(e)?{i:dt,r:e,k:t,f:!!r}:e:null);function c(e,t=null,r=null,s=0,n=null,o=e===be?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Vu(t),ref:t&&Mn(t),scopeId:ru,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:dt};return a?(da(l,r),o&128&&e.normalize(l)):r&&(l.shapeFlag|=Qe(r)?8:16),Ts>0&&!i&&Tt&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&Tt.push(l),l}const ce=fh;function fh(e,t=null,r=null,s=0,n=null,o=!1){if((!e||e===vu)&&(e=pt),ks(e)){const a=Dr(e,t,!0);return r&&da(a,r),Ts>0&&!o&&Tt&&(a.shapeFlag&6?Tt[Tt.indexOf(e)]=a:Tt.push(a)),a.patchFlag=-2,a}if(Sh(e)&&(e=e.__vccOpts),t){t=ph(t);let{class:a,style:l}=t;a&&!Qe(a)&&(t.class=Ce(a)),He(l)&&(ra(l)&&!he(l)&&(l=at({},l)),t.style=Ki(l))}const i=Qe(e)?1:Du(e)?128:nu(e)?64:He(e)?4:_e(e)?2:0;return c(e,t,r,s,n,i,o,!0)}function ph(e){return e?ra(e)||Cu(e)?at({},e):e:null}function Dr(e,t,r=!1,s=!1){const{props:n,ref:o,patchFlag:i,children:a,transition:l}=e,u=t?hh(n||{},t):n,d={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Vu(u),ref:t&&t.ref?r&&o?he(o)?o.concat(Mn(t)):[o,Mn(t)]:Mn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==be?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Dr(e.ssContent),ssFallback:e.ssFallback&&Dr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&ts(d,l.clone(d)),d}function ve(e=" ",t=0){return ce(Eo,null,e,t)}function Nr(e,t){const r=ce(Zs,null,e);return r.staticCount=t,r}function ee(e="",t=!1){return t?(w(),br(pt,null,e)):ce(pt,null,e)}function Ut(e){return e==null||typeof e=="boolean"?ce(pt):he(e)?ce(be,null,e.slice()):ks(e)?Tr(e):ce(Eo,null,String(e))}function Tr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Dr(e)}function da(e,t){let r=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(he(t))r=16;else if(typeof t=="object")if(s&65){const n=t.default;n&&(n._c&&(n._d=!1),da(e,n()),n._c&&(n._d=!0));return}else{r=32;const n=t._;!n&&!Cu(t)?t._ctx=dt:n===3&&dt&&(dt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else _e(t)?(t={default:t,_ctx:dt},r=32):(t=String(t),s&64?(r=16,t=[ve(t)]):r=8);e.children=t,e.shapeFlag|=r}function hh(...e){const t={};for(let r=0;r<e.length;r++){const s=e[r];for(const n in s)if(n==="class")t.class!==s.class&&(t.class=Ce([t.class,s.class]));else if(n==="style")t.style=Ki([t.style,s.style]);else if(po(n)){const o=t[n],i=s[n];i&&o!==i&&!(he(o)&&o.includes(i))&&(t[n]=o?[].concat(o,i):i)}else n!==""&&(t[n]=s[n])}return t}function Qt(e,t,r,s=null){Kt(e,t,7,[r,s])}const gh=Eu();let mh=0;function vh(e,t,r){const s=e.type,n=(t?t.appContext:e.appContext)||gh,o={uid:mh++,vnode:e,type:s,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new $c(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Tu(s,n),emitsOptions:Fu(s,n),emit:null,emitted:null,propsDefaults:ze,inheritAttrs:s.inheritAttrs,ctx:ze,data:ze,props:ze,attrs:ze,slots:ze,refs:ze,setupState:ze,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=eh.bind(null,o),e.ce&&e.ce(o),o}let ut=null;const os=()=>ut||dt;let Zn,bi;{const e=mo(),t=(r,s)=>{let n;return(n=e[r])||(n=e[r]=[]),n.push(s),o=>{n.length>1?n.forEach(i=>i(o)):n[0](o)}};Zn=t("__VUE_INSTANCE_SETTERS__",r=>ut=r),bi=t("__VUE_SSR_SETTERS__",r=>Rs=r)}const yn=e=>{const t=ut;return Zn(e),e.scope.on(),()=>{e.scope.off(),Zn(t)}},tl=()=>{ut&&ut.scope.off(),Zn(null)};function ju(e){return e.vnode.shapeFlag&4}let Rs=!1;function yh(e,t=!1,r=!1){t&&bi(t);const{props:s,children:n}=e.vnode,o=ju(e);Vp(e,s,o,t),Gp(e,n,r);const i=o?bh(e,t):void 0;return t&&bi(!1),i}function bh(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ip);const{setup:s}=r;if(s){Vr();const n=e.setupContext=s.length>1?wh(e):null,o=yn(e),i=mn(s,e,0,[e.props,n]),a=Cc(i);if(jr(),o(),(a||e.sp)&&!Es(e)&&oa(e),a){if(i.then(tl,tl),t)return i.then(l=>{_i(e,l)}).catch(l=>{Ds(l,e,0)});e.asyncDep=i}else _i(e,i)}else Uu(e)}function _i(e,t,r){_e(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:He(t)&&(e.setupState=Yc(t)),Uu(e)}function Uu(e,t,r){const s=e.type;e.render||(e.render=s.render||qt);{const n=yn(e);Vr();try{xp(e)}finally{jr(),n()}}}const _h={get(e,t){return mt(e,"get",""),e[t]}};function wh(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,_h),slots:e.slots,emit:e.emit,expose:t}}function So(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Yc(ap(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in Ys)return Ys[r](e)},has(t,r){return r in t||r in Ys}})):e.proxy}function Eh(e,t=!0){return _e(e)?e.displayName||e.name:e.name||t&&e.__name}function Sh(e){return _e(e)&&"__vccOpts"in e}const le=(e,t)=>hp(e,t,Rs);function rs(e,t,r){const s=arguments.length;return s===2?He(t)&&!he(t)?ks(t)?ce(e,null,[t]):ce(e,t):ce(e,null,t):(s>3?r=Array.prototype.slice.call(arguments,2):s===3&&ks(r)&&(r=[r]),ce(e,t,r))}const Ah="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let wi;const rl=typeof window<"u"&&window.trustedTypes;if(rl)try{wi=rl.createPolicy("vue",{createHTML:e=>e})}catch{}const Bu=wi?e=>wi.createHTML(e):e=>e,Ch="http://www.w3.org/2000/svg",Ph="http://www.w3.org/1998/Math/MathML",pr=typeof document<"u"?document:null,sl=pr&&pr.createElement("template"),Th={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,s)=>{const n=t==="svg"?pr.createElementNS(Ch,e):t==="mathml"?pr.createElementNS(Ph,e):r?pr.createElement(e,{is:r}):pr.createElement(e);return e==="select"&&s&&s.multiple!=null&&n.setAttribute("multiple",s.multiple),n},createText:e=>pr.createTextNode(e),createComment:e=>pr.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>pr.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,s,n,o){const i=r?r.previousSibling:t.lastChild;if(n&&(n===o||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),r),!(n===o||!(n=n.nextSibling)););else{sl.innerHTML=Bu(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=sl.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,r)}return[i?i.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},Er="transition",Us="animation",Os=Symbol("_vtc"),Gu={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},qu=at({},lu,Gu),kh=e=>(e.displayName="Transition",e.props=qu,e),Hu=kh((e,{slots:t})=>rs(wp,zu(e),t)),qr=(e,t=[])=>{he(e)?e.forEach(r=>r(...t)):e&&e(...t)},nl=e=>e?he(e)?e.some(t=>t.length>1):e.length>1:!1;function zu(e){const t={};for(const z in e)z in Gu||(t[z]=e[z]);if(e.css===!1)return t;const{name:r="v",type:s,duration:n,enterFromClass:o=`${r}-enter-from`,enterActiveClass:i=`${r}-enter-active`,enterToClass:a=`${r}-enter-to`,appearFromClass:l=o,appearActiveClass:u=i,appearToClass:d=a,leaveFromClass:f=`${r}-leave-from`,leaveActiveClass:h=`${r}-leave-active`,leaveToClass:g=`${r}-leave-to`}=e,y=Rh(n),E=y&&y[0],A=y&&y[1],{onBeforeEnter:m,onEnter:C,onEnterCancelled:$,onLeave:I,onLeaveCancelled:R,onBeforeAppear:M=m,onAppear:L=C,onAppearCancelled:k=$}=t,x=(z,ue,Ee,Oe)=>{z._enterCancelled=Oe,Ar(z,ue?d:a),Ar(z,ue?u:i),Ee&&Ee()},X=(z,ue)=>{z._isLeaving=!1,Ar(z,f),Ar(z,g),Ar(z,h),ue&&ue()},q=z=>(ue,Ee)=>{const Oe=z?L:C,ye=()=>x(ue,z,Ee);qr(Oe,[ue,ye]),ol(()=>{Ar(ue,z?l:o),tr(ue,z?d:a),nl(Oe)||il(ue,s,E,ye)})};return at(t,{onBeforeEnter(z){qr(m,[z]),tr(z,o),tr(z,i)},onBeforeAppear(z){qr(M,[z]),tr(z,l),tr(z,u)},onEnter:q(!1),onAppear:q(!0),onLeave(z,ue){z._isLeaving=!0;const Ee=()=>X(z,ue);tr(z,f),z._enterCancelled?(tr(z,h),Ei()):(Ei(),tr(z,h)),ol(()=>{z._isLeaving&&(Ar(z,f),tr(z,g),nl(I)||il(z,s,A,Ee))}),qr(I,[z,Ee])},onEnterCancelled(z){x(z,!1,void 0,!0),qr($,[z])},onAppearCancelled(z){x(z,!0,void 0,!0),qr(k,[z])},onLeaveCancelled(z){X(z),qr(R,[z])}})}function Rh(e){if(e==null)return null;if(He(e))return[Ho(e.enter),Ho(e.leave)];{const t=Ho(e);return[t,t]}}function Ho(e){return Rc(e)}function tr(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.add(r)),(e[Os]||(e[Os]=new Set)).add(t)}function Ar(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const r=e[Os];r&&(r.delete(t),r.size||(e[Os]=void 0))}function ol(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Oh=0;function il(e,t,r,s){const n=e._endId=++Oh,o=()=>{n===e._endId&&s()};if(r!=null)return setTimeout(o,r);const{type:i,timeout:a,propCount:l}=Ku(e,t);if(!i)return s();const u=i+"end";let d=0;const f=()=>{e.removeEventListener(u,h),o()},h=g=>{g.target===e&&++d>=l&&f()};setTimeout(()=>{d<l&&f()},a+1),e.addEventListener(u,h)}function Ku(e,t){const r=window.getComputedStyle(e),s=y=>(r[y]||"").split(", "),n=s(`${Er}Delay`),o=s(`${Er}Duration`),i=al(n,o),a=s(`${Us}Delay`),l=s(`${Us}Duration`),u=al(a,l);let d=null,f=0,h=0;t===Er?i>0&&(d=Er,f=i,h=o.length):t===Us?u>0&&(d=Us,f=u,h=l.length):(f=Math.max(i,u),d=f>0?i>u?Er:Us:null,h=d?d===Er?o.length:l.length:0);const g=d===Er&&/\b(transform|all)(,|$)/.test(s(`${Er}Property`).toString());return{type:d,timeout:f,propCount:h,hasTransform:g}}function al(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((r,s)=>ll(r)+ll(e[s])))}function ll(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ei(){return document.body.offsetHeight}function Ih(e,t,r){const s=e[Os];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const Qn=Symbol("_vod"),Wu=Symbol("_vsh"),xh={beforeMount(e,{value:t},{transition:r}){e[Qn]=e.style.display==="none"?"":e.style.display,r&&t?r.beforeEnter(e):Bs(e,t)},mounted(e,{value:t},{transition:r}){r&&t&&r.enter(e)},updated(e,{value:t,oldValue:r},{transition:s}){!t!=!r&&(s?t?(s.beforeEnter(e),Bs(e,!0),s.enter(e)):s.leave(e,()=>{Bs(e,!1)}):Bs(e,t))},beforeUnmount(e,{value:t}){Bs(e,t)}};function Bs(e,t){e.style.display=t?e[Qn]:"none",e[Wu]=!t}const Ju=Symbol("");function AT(e){const t=os();if(!t)return;const r=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(o=>eo(o,n))},s=()=>{const n=e(t.proxy);t.ce?eo(t.ce,n):Si(t.subTree,n),r(n)};pu(()=>{Wn(s)}),ir(()=>{Et(s,qt,{flush:"post"});const n=new MutationObserver(s);n.observe(t.subTree.el.parentNode,{childList:!0}),bo(()=>n.disconnect())})}function Si(e,t){if(e.shapeFlag&128){const r=e.suspense;e=r.activeBranch,r.pendingBranch&&!r.isHydrating&&r.effects.push(()=>{Si(r.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)eo(e.el,t);else if(e.type===be)e.children.forEach(r=>Si(r,t));else if(e.type===Zs){let{el:r,anchor:s}=e;for(;r&&(eo(r,t),r!==s);)r=r.nextSibling}}function eo(e,t){if(e.nodeType===1){const r=e.style;let s="";for(const n in t)r.setProperty(`--${n}`,t[n]),s+=`--${n}: ${t[n]};`;r[Ju]=s}}const $h=/(^|;)\s*display\s*:/;function Fh(e,t,r){const s=e.style,n=Qe(r);let o=!1;if(r&&!n){if(t)if(Qe(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();r[a]==null&&Ln(s,a,"")}else for(const i in t)r[i]==null&&Ln(s,i,"");for(const i in r)i==="display"&&(o=!0),Ln(s,i,r[i])}else if(n){if(t!==r){const i=s[Ju];i&&(r+=";"+i),s.cssText=r,o=$h.test(r)}}else t&&e.removeAttribute("style");Qn in e&&(e[Qn]=o?s.display:"",e[Wu]&&(s.display="none"))}const cl=/\s*!important$/;function Ln(e,t,r){if(he(r))r.forEach(s=>Ln(e,t,s));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const s=Dh(e,t);cl.test(r)?e.setProperty(Lr(s),r.replace(cl,""),"important"):e[s]=r}}const ul=["Webkit","Moz","ms"],zo={};function Dh(e,t){const r=zo[t];if(r)return r;let s=Vt(t);if(s!=="filter"&&s in e)return zo[t]=s;s=go(s);for(let n=0;n<ul.length;n++){const o=ul[n]+s;if(o in e)return zo[t]=o}return t}const dl="http://www.w3.org/1999/xlink";function fl(e,t,r,s,n,o=Lf(t)){s&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(dl,t.slice(6,t.length)):e.setAttributeNS(dl,t,r):r==null||o&&!Oc(r)?e.removeAttribute(t):e.setAttribute(t,o?"":zt(r)?String(r):r)}function pl(e,t,r,s,n){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?Bu(r):r);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=r==null?e.type==="checkbox"?"on":"":String(r);(a!==l||!("_value"in e))&&(e.value=l),r==null&&e.removeAttribute(t),e._value=r;return}let i=!1;if(r===""||r==null){const a=typeof e[t];a==="boolean"?r=Oc(r):r==null&&a==="string"?(r="",i=!0):a==="number"&&(r=0,i=!0)}try{e[t]=r}catch{}i&&e.removeAttribute(n||t)}function Or(e,t,r,s){e.addEventListener(t,r,s)}function Nh(e,t,r,s){e.removeEventListener(t,r,s)}const hl=Symbol("_vei");function Mh(e,t,r,s,n=null){const o=e[hl]||(e[hl]={}),i=o[t];if(s&&i)i.value=s;else{const[a,l]=Lh(t);if(s){const u=o[t]=Uh(s,n);Or(e,a,u,l)}else i&&(Nh(e,a,i,l),o[t]=void 0)}}const gl=/(?:Once|Passive|Capture)$/;function Lh(e){let t;if(gl.test(e)){t={};let s;for(;s=e.match(gl);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Lr(e.slice(2)),t]}let Ko=0;const Vh=Promise.resolve(),jh=()=>Ko||(Vh.then(()=>Ko=0),Ko=Date.now());function Uh(e,t){const r=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=r.attached)return;Kt(Bh(s,r.value),t,5,[s])};return r.value=e,r.attached=jh(),r}function Bh(e,t){if(he(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(s=>n=>!n._stopped&&s&&s(n))}else return t}const ml=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Gh=(e,t,r,s,n,o)=>{const i=n==="svg";t==="class"?Ih(e,s,i):t==="style"?Fh(e,r,s):po(t)?qi(t)||Mh(e,t,r,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):qh(e,t,s,i))?(pl(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&fl(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Qe(s))?pl(e,Vt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),fl(e,t,s,i))};function qh(e,t,r,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ml(t)&&_e(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return ml(t)&&Qe(r)?!1:t in e}const Xu=new WeakMap,Yu=new WeakMap,to=Symbol("_moveCb"),vl=Symbol("_enterCb"),Hh=e=>(delete e.props.mode,e),zh=Hh({name:"TransitionGroup",props:at({},qu,{tag:String,moveClass:String}),setup(e,{slots:t}){const r=os(),s=au();let n,o;return hu(()=>{if(!n.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!Yh(n[0].el,r.vnode.el,i))return;n.forEach(Wh),n.forEach(Jh);const a=n.filter(Xh);Ei(),a.forEach(l=>{const u=l.el,d=u.style;tr(u,i),d.transform=d.webkitTransform=d.transitionDuration="";const f=u[to]=h=>{h&&h.target!==u||(!h||/transform$/.test(h.propertyName))&&(u.removeEventListener("transitionend",f),u[to]=null,Ar(u,i))};u.addEventListener("transitionend",f)})}),()=>{const i=Fe(e),a=zu(i);let l=i.tag||be;if(n=[],o)for(let u=0;u<o.length;u++){const d=o[u];d.el&&d.el instanceof Element&&(n.push(d),ts(d,on(d,a,s,r)),Xu.set(d,d.el.getBoundingClientRect()))}o=t.default?na(t.default()):[];for(let u=0;u<o.length;u++){const d=o[u];d.key!=null&&ts(d,on(d,a,s,r))}return ce(l,null,o)}}}),Kh=zh;function Wh(e){const t=e.el;t[to]&&t[to](),t[vl]&&t[vl]()}function Jh(e){Yu.set(e,e.el.getBoundingClientRect())}function Xh(e){const t=Xu.get(e),r=Yu.get(e),s=t.left-r.left,n=t.top-r.top;if(s||n){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${n}px)`,o.transitionDuration="0s",e}}function Yh(e,t,r){const s=e.cloneNode(),n=e[Os];n&&n.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),r.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:i}=Ku(s);return o.removeChild(s),i}const Is=e=>{const t=e.props["onUpdate:modelValue"]||!1;return he(t)?r=>Dn(t,r):t};function Zh(e){e.target.composing=!0}function yl(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const _r=Symbol("_assign"),gt={created(e,{modifiers:{lazy:t,trim:r,number:s}},n){e[_r]=Is(n);const o=s||n.props&&n.props.type==="number";Or(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;r&&(a=a.trim()),o&&(a=qn(a)),e[_r](a)}),r&&Or(e,"change",()=>{e.value=e.value.trim()}),t||(Or(e,"compositionstart",Zh),Or(e,"compositionend",yl),Or(e,"change",yl))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:r,modifiers:{lazy:s,trim:n,number:o}},i){if(e[_r]=Is(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?qn(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===r||n&&e.value.trim()===l)||(e.value=l))}},Zu={deep:!0,created(e,t,r){e[_r]=Is(r),Or(e,"change",()=>{const s=e._modelValue,n=ln(e),o=e.checked,i=e[_r];if(he(s)){const a=Wi(s,n),l=a!==-1;if(o&&!l)i(s.concat(n));else if(!o&&l){const u=[...s];u.splice(a,1),i(u)}}else if(Fs(s)){const a=new Set(s);o?a.add(n):a.delete(n),i(a)}else i(Qu(e,o))})},mounted:bl,beforeUpdate(e,t,r){e[_r]=Is(r),bl(e,t,r)}};function bl(e,{value:t,oldValue:r},s){e._modelValue=t;let n;if(he(t))n=Wi(t,s.props.value)>-1;else if(Fs(t))n=t.has(s.props.value);else{if(t===r)return;n=gn(t,Qu(e,!0))}e.checked!==n&&(e.checked=n)}const hr={deep:!0,created(e,{value:t,modifiers:{number:r}},s){const n=Fs(t);Or(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>r?qn(ln(i)):ln(i));e[_r](e.multiple?n?new Set(o):o:o[0]),e._assigning=!0,Ft(()=>{e._assigning=!1})}),e[_r]=Is(s)},mounted(e,{value:t}){_l(e,t)},beforeUpdate(e,t,r){e[_r]=Is(r)},updated(e,{value:t}){e._assigning||_l(e,t)}};function _l(e,t){const r=e.multiple,s=he(t);if(!(r&&!s&&!Fs(t))){for(let n=0,o=e.options.length;n<o;n++){const i=e.options[n],a=ln(i);if(r)if(s){const l=typeof a;l==="string"||l==="number"?i.selected=t.some(u=>String(u)===String(a)):i.selected=Wi(t,a)>-1}else i.selected=t.has(a);else if(gn(ln(i),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!r&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function ln(e){return"_value"in e?e._value:e.value}function Qu(e,t){const r=t?"_trueValue":"_falseValue";return r in e?e[r]:t}const Qh=["ctrl","shift","alt","meta"],eg={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Qh.some(r=>e[`${r}Key`]&&!t.includes(r))},Ir=(e,t)=>{const r=e._withMods||(e._withMods={}),s=t.join(".");return r[s]||(r[s]=(n,...o)=>{for(let i=0;i<t.length;i++){const a=eg[t[i]];if(a&&a(n,t))return}return e(n,...o)})},tg={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},CT=(e,t)=>{const r=e._withKeys||(e._withKeys={}),s=t.join(".");return r[s]||(r[s]=n=>{if(!("key"in n))return;const o=Lr(n.key);if(t.some(i=>i===o||tg[i]===o))return e(n)})},rg=at({patchProp:Gh},Th);let wl;function sg(){return wl||(wl=Hp(rg))}const ng=(...e)=>{const t=sg().createApp(...e),{mount:r}=t;return t.mount=s=>{const n=ig(s);if(!n)return;const o=t._component;!_e(o)&&!o.render&&!o.template&&(o.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const i=r(n,!1,og(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),i},t};function og(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ig(e){return Qe(e)?document.querySelector(e):e}function ag(){return ed().__VUE_DEVTOOLS_GLOBAL_HOOK__}function ed(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const lg=typeof Proxy=="function",cg="devtools-plugin:setup",ug="plugin:settings:set";let fs,Ai;function dg(){var e;return fs!==void 0||(typeof window<"u"&&window.performance?(fs=!0,Ai=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(fs=!0,Ai=globalThis.perf_hooks.performance):fs=!1),fs}function fg(){return dg()?Ai.now():Date.now()}class pg{constructor(t,r){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=r;const s={};if(t.settings)for(const i in t.settings){const a=t.settings[i];s[i]=a.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let o=Object.assign({},s);try{const i=localStorage.getItem(n),a=JSON.parse(i);Object.assign(o,a)}catch{}this.fallbacks={getSettings(){return o},setSettings(i){try{localStorage.setItem(n,JSON.stringify(i))}catch{}o=i},now(){return fg()}},r&&r.on(ug,(i,a)=>{i===this.plugin.id&&this.fallbacks.setSettings(a)}),this.proxiedOn=new Proxy({},{get:(i,a)=>this.target?this.target.on[a]:(...l)=>{this.onQueue.push({method:a,args:l})}}),this.proxiedTarget=new Proxy({},{get:(i,a)=>this.target?this.target[a]:a==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(a)?(...l)=>(this.targetQueue.push({method:a,args:l,resolve:()=>{}}),this.fallbacks[a](...l)):(...l)=>new Promise(u=>{this.targetQueue.push({method:a,args:l,resolve:u})})})}async setRealTarget(t){this.target=t;for(const r of this.onQueue)this.target.on[r.method](...r.args);for(const r of this.targetQueue)r.resolve(await this.target[r.method](...r.args))}}function hg(e,t){const r=e,s=ed(),n=ag(),o=lg&&r.enableEarlyProxy;if(n&&(s.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!o))n.emit(cg,e,t);else{const i=o?new pg(r,n):null;(s.__VUE_DEVTOOLS_PLUGINS__=s.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:r,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}}/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */var td="store";function bn(e){return e===void 0&&(e=null),kt(e!==null?e:td)}function Ms(e,t){Object.keys(e).forEach(function(r){return t(e[r],r)})}function gg(e){return e!==null&&typeof e=="object"}function mg(e){return e&&typeof e.then=="function"}function vg(e,t){return function(){return e(t)}}function rd(e,t,r){return t.indexOf(e)<0&&(r&&r.prepend?t.unshift(e):t.push(e)),function(){var s=t.indexOf(e);s>-1&&t.splice(s,1)}}function sd(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var r=e.state;Ao(e,r,[],e._modules.root,!0),fa(e,r,t)}function fa(e,t,r){var s=e._state,n=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var o=e._wrappedGetters,i={},a={},l=jf(!0);l.run(function(){Ms(o,function(u,d){i[d]=vg(u,e),a[d]=le(function(){return i[d]()}),Object.defineProperty(e.getters,d,{get:function(){return a[d].value},enumerable:!0})})}),e._state=or({data:t}),e._scope=l,e.strict&&Eg(e),s&&r&&e._withCommit(function(){s.data=null}),n&&n.stop()}function Ao(e,t,r,s,n){var o=!r.length,i=e._modules.getNamespace(r);if(s.namespaced&&(e._modulesNamespaceMap[i],e._modulesNamespaceMap[i]=s),!o&&!n){var a=pa(t,r.slice(0,-1)),l=r[r.length-1];e._withCommit(function(){a[l]=s.state})}var u=s.context=yg(e,i,r);s.forEachMutation(function(d,f){var h=i+f;bg(e,h,d,u)}),s.forEachAction(function(d,f){var h=d.root?f:i+f,g=d.handler||d;_g(e,h,g,u)}),s.forEachGetter(function(d,f){var h=i+f;wg(e,h,d,u)}),s.forEachChild(function(d,f){Ao(e,t,r.concat(f),d,n)})}function yg(e,t,r){var s=t==="",n={dispatch:s?e.dispatch:function(o,i,a){var l=ro(o,i,a),u=l.payload,d=l.options,f=l.type;return(!d||!d.root)&&(f=t+f),e.dispatch(f,u)},commit:s?e.commit:function(o,i,a){var l=ro(o,i,a),u=l.payload,d=l.options,f=l.type;(!d||!d.root)&&(f=t+f),e.commit(f,u,d)}};return Object.defineProperties(n,{getters:{get:s?function(){return e.getters}:function(){return nd(e,t)}},state:{get:function(){return pa(e.state,r)}}}),n}function nd(e,t){if(!e._makeLocalGettersCache[t]){var r={},s=t.length;Object.keys(e.getters).forEach(function(n){if(n.slice(0,s)===t){var o=n.slice(s);Object.defineProperty(r,o,{get:function(){return e.getters[n]},enumerable:!0})}}),e._makeLocalGettersCache[t]=r}return e._makeLocalGettersCache[t]}function bg(e,t,r,s){var n=e._mutations[t]||(e._mutations[t]=[]);n.push(function(i){r.call(e,s.state,i)})}function _g(e,t,r,s){var n=e._actions[t]||(e._actions[t]=[]);n.push(function(i){var a=r.call(e,{dispatch:s.dispatch,commit:s.commit,getters:s.getters,state:s.state,rootGetters:e.getters,rootState:e.state},i);return mg(a)||(a=Promise.resolve(a)),e._devtoolHook?a.catch(function(l){throw e._devtoolHook.emit("vuex:error",l),l}):a})}function wg(e,t,r,s){e._wrappedGetters[t]||(e._wrappedGetters[t]=function(o){return r(s.state,s.getters,o.state,o.getters)})}function Eg(e){Et(function(){return e._state.data},function(){},{deep:!0,flush:"sync"})}function pa(e,t){return t.reduce(function(r,s){return r[s]},e)}function ro(e,t,r){return gg(e)&&e.type&&(r=t,t=e,e=e.type),{type:e,payload:t,options:r}}var Sg="vuex bindings",El="vuex:mutations",Wo="vuex:actions",ps="vuex",Ag=0;function Cg(e,t){hg({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:[Sg]},function(r){r.addTimelineLayer({id:El,label:"Vuex Mutations",color:Sl}),r.addTimelineLayer({id:Wo,label:"Vuex Actions",color:Sl}),r.addInspector({id:ps,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),r.on.getInspectorTree(function(s){if(s.app===e&&s.inspectorId===ps)if(s.filter){var n=[];ld(n,t._modules.root,s.filter,""),s.rootNodes=n}else s.rootNodes=[ad(t._modules.root,"")]}),r.on.getInspectorState(function(s){if(s.app===e&&s.inspectorId===ps){var n=s.nodeId;nd(t,n),s.state=kg(Og(t._modules,n),n==="root"?t.getters:t._makeLocalGettersCache,n)}}),r.on.editInspectorState(function(s){if(s.app===e&&s.inspectorId===ps){var n=s.nodeId,o=s.path;n!=="root"&&(o=n.split("/").filter(Boolean).concat(o)),t._withCommit(function(){s.set(t._state.data,o,s.state.value)})}}),t.subscribe(function(s,n){var o={};s.payload&&(o.payload=s.payload),o.state=n,r.notifyComponentUpdate(),r.sendInspectorTree(ps),r.sendInspectorState(ps),r.addTimelineEvent({layerId:El,event:{time:Date.now(),title:s.type,data:o}})}),t.subscribeAction({before:function(s,n){var o={};s.payload&&(o.payload=s.payload),s._id=Ag++,s._time=Date.now(),o.state=n,r.addTimelineEvent({layerId:Wo,event:{time:s._time,title:s.type,groupId:s._id,subtitle:"start",data:o}})},after:function(s,n){var o={},i=Date.now()-s._time;o.duration={_custom:{type:"duration",display:i+"ms",tooltip:"Action duration",value:i}},s.payload&&(o.payload=s.payload),o.state=n,r.addTimelineEvent({layerId:Wo,event:{time:Date.now(),title:s.type,groupId:s._id,subtitle:"end",data:o}})}})})}var Sl=8702998,Pg=6710886,Tg=16777215,od={label:"namespaced",textColor:Tg,backgroundColor:Pg};function id(e){return e&&e!=="root"?e.split("/").slice(-2,-1)[0]:"Root"}function ad(e,t){return{id:t||"root",label:id(t),tags:e.namespaced?[od]:[],children:Object.keys(e._children).map(function(r){return ad(e._children[r],t+r+"/")})}}function ld(e,t,r,s){s.includes(r)&&e.push({id:s||"root",label:s.endsWith("/")?s.slice(0,s.length-1):s||"Root",tags:t.namespaced?[od]:[]}),Object.keys(t._children).forEach(function(n){ld(e,t._children[n],r,s+n+"/")})}function kg(e,t,r){t=r==="root"?t:t[r];var s=Object.keys(t),n={state:Object.keys(e.state).map(function(i){return{key:i,editable:!0,value:e.state[i]}})};if(s.length){var o=Rg(t);n.getters=Object.keys(o).map(function(i){return{key:i.endsWith("/")?id(i):i,editable:!1,value:Ci(function(){return o[i]})}})}return n}function Rg(e){var t={};return Object.keys(e).forEach(function(r){var s=r.split("/");if(s.length>1){var n=t,o=s.pop();s.forEach(function(i){n[i]||(n[i]={_custom:{value:{},display:i,tooltip:"Module",abstract:!0}}),n=n[i]._custom.value}),n[o]=Ci(function(){return e[r]})}else t[r]=Ci(function(){return e[r]})}),t}function Og(e,t){var r=t.split("/").filter(function(s){return s});return r.reduce(function(s,n,o){var i=s[n];if(!i)throw new Error('Missing module "'+n+'" for path "'+t+'".');return o===r.length-1?i:i._children},t==="root"?e:e.root._children)}function Ci(e){try{return e()}catch(t){return t}}var Xt=function(t,r){this.runtime=r,this._children=Object.create(null),this._rawModule=t;var s=t.state;this.state=(typeof s=="function"?s():s)||{}},cd={namespaced:{configurable:!0}};cd.namespaced.get=function(){return!!this._rawModule.namespaced};Xt.prototype.addChild=function(t,r){this._children[t]=r};Xt.prototype.removeChild=function(t){delete this._children[t]};Xt.prototype.getChild=function(t){return this._children[t]};Xt.prototype.hasChild=function(t){return t in this._children};Xt.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)};Xt.prototype.forEachChild=function(t){Ms(this._children,t)};Xt.prototype.forEachGetter=function(t){this._rawModule.getters&&Ms(this._rawModule.getters,t)};Xt.prototype.forEachAction=function(t){this._rawModule.actions&&Ms(this._rawModule.actions,t)};Xt.prototype.forEachMutation=function(t){this._rawModule.mutations&&Ms(this._rawModule.mutations,t)};Object.defineProperties(Xt.prototype,cd);var is=function(t){this.register([],t,!1)};is.prototype.get=function(t){return t.reduce(function(r,s){return r.getChild(s)},this.root)};is.prototype.getNamespace=function(t){var r=this.root;return t.reduce(function(s,n){return r=r.getChild(n),s+(r.namespaced?n+"/":"")},"")};is.prototype.update=function(t){ud([],this.root,t)};is.prototype.register=function(t,r,s){var n=this;s===void 0&&(s=!0);var o=new Xt(r,s);if(t.length===0)this.root=o;else{var i=this.get(t.slice(0,-1));i.addChild(t[t.length-1],o)}r.modules&&Ms(r.modules,function(a,l){n.register(t.concat(l),a,s)})};is.prototype.unregister=function(t){var r=this.get(t.slice(0,-1)),s=t[t.length-1],n=r.getChild(s);n&&n.runtime&&r.removeChild(s)};is.prototype.isRegistered=function(t){var r=this.get(t.slice(0,-1)),s=t[t.length-1];return r?r.hasChild(s):!1};function ud(e,t,r){if(t.update(r),r.modules)for(var s in r.modules){if(!t.getChild(s))return;ud(e.concat(s),t.getChild(s),r.modules[s])}}function Ig(e){return new It(e)}var It=function(t){var r=this;t===void 0&&(t={});var s=t.plugins;s===void 0&&(s=[]);var n=t.strict;n===void 0&&(n=!1);var o=t.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new is(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=o;var i=this,a=this,l=a.dispatch,u=a.commit;this.dispatch=function(h,g){return l.call(i,h,g)},this.commit=function(h,g,y){return u.call(i,h,g,y)},this.strict=n;var d=this._modules.root.state;Ao(this,d,[],this._modules.root),fa(this,d),s.forEach(function(f){return f(r)})},ha={state:{configurable:!0}};It.prototype.install=function(t,r){t.provide(r||td,this),t.config.globalProperties.$store=this;var s=this._devtools!==void 0?this._devtools:!1;s&&Cg(t,this)};ha.state.get=function(){return this._state.data};ha.state.set=function(e){};It.prototype.commit=function(t,r,s){var n=this,o=ro(t,r,s),i=o.type,a=o.payload,l={type:i,payload:a},u=this._mutations[i];u&&(this._withCommit(function(){u.forEach(function(f){f(a)})}),this._subscribers.slice().forEach(function(d){return d(l,n.state)}))};It.prototype.dispatch=function(t,r){var s=this,n=ro(t,r),o=n.type,i=n.payload,a={type:o,payload:i},l=this._actions[o];if(l){try{this._actionSubscribers.slice().filter(function(d){return d.before}).forEach(function(d){return d.before(a,s.state)})}catch{}var u=l.length>1?Promise.all(l.map(function(d){return d(i)})):l[0](i);return new Promise(function(d,f){u.then(function(h){try{s._actionSubscribers.filter(function(g){return g.after}).forEach(function(g){return g.after(a,s.state)})}catch{}d(h)},function(h){try{s._actionSubscribers.filter(function(g){return g.error}).forEach(function(g){return g.error(a,s.state,h)})}catch{}f(h)})})}};It.prototype.subscribe=function(t,r){return rd(t,this._subscribers,r)};It.prototype.subscribeAction=function(t,r){var s=typeof t=="function"?{before:t}:t;return rd(s,this._actionSubscribers,r)};It.prototype.watch=function(t,r,s){var n=this;return Et(function(){return t(n.state,n.getters)},r,Object.assign({},s))};It.prototype.replaceState=function(t){var r=this;this._withCommit(function(){r._state.data=t})};It.prototype.registerModule=function(t,r,s){s===void 0&&(s={}),typeof t=="string"&&(t=[t]),this._modules.register(t,r),Ao(this,this.state,t,this._modules.get(t),s.preserveState),fa(this,this.state)};It.prototype.unregisterModule=function(t){var r=this;typeof t=="string"&&(t=[t]),this._modules.unregister(t),this._withCommit(function(){var s=pa(r.state,t.slice(0,-1));delete s[t[t.length-1]]}),sd(this)};It.prototype.hasModule=function(t){return typeof t=="string"&&(t=[t]),this._modules.isRegistered(t)};It.prototype.hotUpdate=function(t){this._modules.update(t),sd(this,!0)};It.prototype._withCommit=function(t){var r=this._committing;this._committing=!0,t(),this._committing=r};Object.defineProperties(It.prototype,ha);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const ms=typeof document<"u";function dd(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function xg(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&dd(e.default)}const je=Object.assign;function Jo(e,t){const r={};for(const s in t){const n=t[s];r[s]=Wt(n)?n.map(e):e(n)}return r}const en=()=>{},Wt=Array.isArray,fd=/#/g,$g=/&/g,Fg=/\//g,Dg=/=/g,Ng=/\?/g,pd=/\+/g,Mg=/%5B/g,Lg=/%5D/g,hd=/%5E/g,Vg=/%60/g,gd=/%7B/g,jg=/%7C/g,md=/%7D/g,Ug=/%20/g;function ga(e){return encodeURI(""+e).replace(jg,"|").replace(Mg,"[").replace(Lg,"]")}function Bg(e){return ga(e).replace(gd,"{").replace(md,"}").replace(hd,"^")}function Pi(e){return ga(e).replace(pd,"%2B").replace(Ug,"+").replace(fd,"%23").replace($g,"%26").replace(Vg,"`").replace(gd,"{").replace(md,"}").replace(hd,"^")}function Gg(e){return Pi(e).replace(Dg,"%3D")}function qg(e){return ga(e).replace(fd,"%23").replace(Ng,"%3F")}function Hg(e){return e==null?"":qg(e).replace(Fg,"%2F")}function cn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const zg=/\/$/,Kg=e=>e.replace(zg,"");function Xo(e,t,r="/"){let s,n={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),n=e(o)),a>-1&&(s=s||t.slice(0,a),i=t.slice(a,t.length)),s=Yg(s??t,r),{fullPath:s+(o&&"?")+o+i,path:s,query:n,hash:cn(i)}}function Wg(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function Al(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Jg(e,t,r){const s=t.matched.length-1,n=r.matched.length-1;return s>-1&&s===n&&xs(t.matched[s],r.matched[n])&&vd(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function xs(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function vd(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Xg(e[r],t[r]))return!1;return!0}function Xg(e,t){return Wt(e)?Cl(e,t):Wt(t)?Cl(t,e):e===t}function Cl(e,t){return Wt(t)?e.length===t.length&&e.every((r,s)=>r===t[s]):e.length===1&&e[0]===t}function Yg(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),s=e.split("/"),n=s[s.length-1];(n===".."||n===".")&&s.push("");let o=r.length-1,i,a;for(i=0;i<s.length;i++)if(a=s[i],a!==".")if(a==="..")o>1&&o--;else break;return r.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const Sr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var un;(function(e){e.pop="pop",e.push="push"})(un||(un={}));var tn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(tn||(tn={}));function Zg(e){if(!e)if(ms){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Kg(e)}const Qg=/^[^#]+#/;function em(e,t){return e.replace(Qg,"#")+t}function tm(e,t){const r=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-r.left-(t.left||0),top:s.top-r.top-(t.top||0)}}const Co=()=>({left:window.scrollX,top:window.scrollY});function rm(e){let t;if("el"in e){const r=e.el,s=typeof r=="string"&&r.startsWith("#"),n=typeof r=="string"?s?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!n)return;t=tm(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Pl(e,t){return(history.state?history.state.position-t:-1)+e}const Ti=new Map;function sm(e,t){Ti.set(e,t)}function nm(e){const t=Ti.get(e);return Ti.delete(e),t}let om=()=>location.protocol+"//"+location.host;function yd(e,t){const{pathname:r,search:s,hash:n}=t,o=e.indexOf("#");if(o>-1){let a=n.includes(e.slice(o))?e.slice(o).length:1,l=n.slice(a);return l[0]!=="/"&&(l="/"+l),Al(l,"")}return Al(r,e)+s+n}function im(e,t,r,s){let n=[],o=[],i=null;const a=({state:h})=>{const g=yd(e,location),y=r.value,E=t.value;let A=0;if(h){if(r.value=g,t.value=h,i&&i===y){i=null;return}A=E?h.position-E.position:0}else s(g);n.forEach(m=>{m(r.value,y,{delta:A,type:un.pop,direction:A?A>0?tn.forward:tn.back:tn.unknown})})};function l(){i=r.value}function u(h){n.push(h);const g=()=>{const y=n.indexOf(h);y>-1&&n.splice(y,1)};return o.push(g),g}function d(){const{history:h}=window;h.state&&h.replaceState(je({},h.state,{scroll:Co()}),"")}function f(){for(const h of o)h();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",d)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",d,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function Tl(e,t,r,s=!1,n=!1){return{back:e,current:t,forward:r,replaced:s,position:window.history.length,scroll:n?Co():null}}function am(e){const{history:t,location:r}=window,s={value:yd(e,r)},n={value:t.state};n.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,u,d){const f=e.indexOf("#"),h=f>-1?(r.host&&document.querySelector("base")?e:e.slice(f))+l:om()+e+l;try{t[d?"replaceState":"pushState"](u,"",h),n.value=u}catch(g){console.error(g),r[d?"replace":"assign"](h)}}function i(l,u){const d=je({},t.state,Tl(n.value.back,l,n.value.forward,!0),u,{position:n.value.position});o(l,d,!0),s.value=l}function a(l,u){const d=je({},n.value,t.state,{forward:l,scroll:Co()});o(d.current,d,!0);const f=je({},Tl(s.value,l,null),{position:d.position+1},u);o(l,f,!1),s.value=l}return{location:s,state:n,push:a,replace:i}}function lm(e){e=Zg(e);const t=am(e),r=im(e,t.state,t.location,t.replace);function s(o,i=!0){i||r.pauseListeners(),history.go(o)}const n=je({location:"",base:e,go:s,createHref:em.bind(null,e)},t,r);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function cm(e){return typeof e=="string"||e&&typeof e=="object"}function bd(e){return typeof e=="string"||typeof e=="symbol"}const _d=Symbol("");var kl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(kl||(kl={}));function $s(e,t){return je(new Error,{type:e,[_d]:!0},t)}function cr(e,t){return e instanceof Error&&_d in e&&(t==null||!!(e.type&t))}const Rl="[^/]+?",um={sensitive:!1,strict:!1,start:!0,end:!0},dm=/[.+*?^${}()[\]/\\]/g;function fm(e,t){const r=je({},um,t),s=[];let n=r.start?"^":"";const o=[];for(const u of e){const d=u.length?[]:[90];r.strict&&!u.length&&(n+="/");for(let f=0;f<u.length;f++){const h=u[f];let g=40+(r.sensitive?.25:0);if(h.type===0)f||(n+="/"),n+=h.value.replace(dm,"\\$&"),g+=40;else if(h.type===1){const{value:y,repeatable:E,optional:A,regexp:m}=h;o.push({name:y,repeatable:E,optional:A});const C=m||Rl;if(C!==Rl){g+=10;try{new RegExp(`(${C})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${y}" (${C}): `+I.message)}}let $=E?`((?:${C})(?:/(?:${C}))*)`:`(${C})`;f||($=A&&u.length<2?`(?:/${$})`:"/"+$),A&&($+="?"),n+=$,g+=20,A&&(g+=-8),E&&(g+=-20),C===".*"&&(g+=-50)}d.push(g)}s.push(d)}if(r.strict&&r.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}r.strict||(n+="/?"),r.end?n+="$":r.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const i=new RegExp(n,r.sensitive?"":"i");function a(u){const d=u.match(i),f={};if(!d)return null;for(let h=1;h<d.length;h++){const g=d[h]||"",y=o[h-1];f[y.name]=g&&y.repeatable?g.split("/"):g}return f}function l(u){let d="",f=!1;for(const h of e){(!f||!d.endsWith("/"))&&(d+="/"),f=!1;for(const g of h)if(g.type===0)d+=g.value;else if(g.type===1){const{value:y,repeatable:E,optional:A}=g,m=y in u?u[y]:"";if(Wt(m)&&!E)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const C=Wt(m)?m.join("/"):m;if(!C)if(A)h.length<2&&(d.endsWith("/")?d=d.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);d+=C}}return d||"/"}return{re:i,score:s,keys:o,parse:a,stringify:l}}function pm(e,t){let r=0;for(;r<e.length&&r<t.length;){const s=t[r]-e[r];if(s)return s;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function wd(e,t){let r=0;const s=e.score,n=t.score;for(;r<s.length&&r<n.length;){const o=pm(s[r],n[r]);if(o)return o;r++}if(Math.abs(n.length-s.length)===1){if(Ol(s))return 1;if(Ol(n))return-1}return n.length-s.length}function Ol(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const hm={type:0,value:""},gm=/[a-zA-Z0-9_]/;function mm(e){if(!e)return[[]];if(e==="/")return[[hm]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${r})/"${u}": ${g}`)}let r=0,s=r;const n=[];let o;function i(){o&&n.push(o),o=[]}let a=0,l,u="",d="";function f(){u&&(r===0?o.push({type:0,value:u}):r===1||r===2||r===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:d,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function h(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&r!==2){s=r,r=4;continue}switch(r){case 0:l==="/"?(u&&f(),i()):l===":"?(f(),r=1):h();break;case 4:h(),r=s;break;case 1:l==="("?r=2:gm.test(l)?h():(f(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?d[d.length-1]=="\\"?d=d.slice(0,-1)+l:r=3:d+=l;break;case 3:f(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,d="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),n}function vm(e,t,r){const s=fm(mm(e.path),r),n=je(s,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function ym(e,t){const r=[],s=new Map;t=Fl({strict:!1,end:!0,sensitive:!1},t);function n(f){return s.get(f)}function o(f,h,g){const y=!g,E=xl(f);E.aliasOf=g&&g.record;const A=Fl(t,f),m=[E];if("alias"in f){const I=typeof f.alias=="string"?[f.alias]:f.alias;for(const R of I)m.push(xl(je({},E,{components:g?g.record.components:E.components,path:R,aliasOf:g?g.record:E})))}let C,$;for(const I of m){const{path:R}=I;if(h&&R[0]!=="/"){const M=h.record.path,L=M[M.length-1]==="/"?"":"/";I.path=h.record.path+(R&&L+R)}if(C=vm(I,h,A),g?g.alias.push(C):($=$||C,$!==C&&$.alias.push(C),y&&f.name&&!$l(C)&&i(f.name)),Ed(C)&&l(C),E.children){const M=E.children;for(let L=0;L<M.length;L++)o(M[L],C,g&&g.children[L])}g=g||C}return $?()=>{i($)}:en}function i(f){if(bd(f)){const h=s.get(f);h&&(s.delete(f),r.splice(r.indexOf(h),1),h.children.forEach(i),h.alias.forEach(i))}else{const h=r.indexOf(f);h>-1&&(r.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return r}function l(f){const h=wm(f,r);r.splice(h,0,f),f.record.name&&!$l(f)&&s.set(f.record.name,f)}function u(f,h){let g,y={},E,A;if("name"in f&&f.name){if(g=s.get(f.name),!g)throw $s(1,{location:f});A=g.record.name,y=je(Il(h.params,g.keys.filter($=>!$.optional).concat(g.parent?g.parent.keys.filter($=>$.optional):[]).map($=>$.name)),f.params&&Il(f.params,g.keys.map($=>$.name))),E=g.stringify(y)}else if(f.path!=null)E=f.path,g=r.find($=>$.re.test(E)),g&&(y=g.parse(E),A=g.record.name);else{if(g=h.name?s.get(h.name):r.find($=>$.re.test(h.path)),!g)throw $s(1,{location:f,currentLocation:h});A=g.record.name,y=je({},h.params,f.params),E=g.stringify(y)}const m=[];let C=g;for(;C;)m.unshift(C.record),C=C.parent;return{name:A,path:E,params:y,matched:m,meta:_m(m)}}e.forEach(f=>o(f));function d(){r.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:d,getRoutes:a,getRecordMatcher:n}}function Il(e,t){const r={};for(const s of t)s in e&&(r[s]=e[s]);return r}function xl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:bm(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function bm(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const s in e.components)t[s]=typeof r=="object"?r[s]:r;return t}function $l(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _m(e){return e.reduce((t,r)=>je(t,r.meta),{})}function Fl(e,t){const r={};for(const s in e)r[s]=s in t?t[s]:e[s];return r}function wm(e,t){let r=0,s=t.length;for(;r!==s;){const o=r+s>>1;wd(e,t[o])<0?s=o:r=o+1}const n=Em(e);return n&&(s=t.lastIndexOf(n,s-1)),s}function Em(e){let t=e;for(;t=t.parent;)if(Ed(t)&&wd(e,t)===0)return t}function Ed({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sm(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<s.length;++n){const o=s[n].replace(pd," "),i=o.indexOf("="),a=cn(i<0?o:o.slice(0,i)),l=i<0?null:cn(o.slice(i+1));if(a in t){let u=t[a];Wt(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function Dl(e){let t="";for(let r in e){const s=e[r];if(r=Gg(r),s==null){s!==void 0&&(t+=(t.length?"&":"")+r);continue}(Wt(s)?s.map(o=>o&&Pi(o)):[s&&Pi(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+r,o!=null&&(t+="="+o))})}return t}function Am(e){const t={};for(const r in e){const s=e[r];s!==void 0&&(t[r]=Wt(s)?s.map(n=>n==null?null:""+n):s==null?s:""+s)}return t}const Cm=Symbol(""),Nl=Symbol(""),Po=Symbol(""),ma=Symbol(""),ki=Symbol("");function Gs(){let e=[];function t(s){return e.push(s),()=>{const n=e.indexOf(s);n>-1&&e.splice(n,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function kr(e,t,r,s,n,o=i=>i()){const i=s&&(s.enterCallbacks[n]=s.enterCallbacks[n]||[]);return()=>new Promise((a,l)=>{const u=h=>{h===!1?l($s(4,{from:r,to:t})):h instanceof Error?l(h):cm(h)?l($s(2,{from:t,to:h})):(i&&s.enterCallbacks[n]===i&&typeof h=="function"&&i.push(h),a())},d=o(()=>e.call(s&&s.instances[n],t,r,u));let f=Promise.resolve(d);e.length<3&&(f=f.then(u)),f.catch(h=>l(h))})}function Yo(e,t,r,s,n=o=>o()){const o=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(dd(l)){const d=(l.__vccOpts||l)[t];d&&o.push(kr(d,r,s,i,a,n))}else{let u=l();o.push(()=>u.then(d=>{if(!d)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=xg(d)?d.default:d;i.mods[a]=d,i.components[a]=f;const g=(f.__vccOpts||f)[t];return g&&kr(g,r,s,i,a,n)()}))}}return o}function Ml(e){const t=kt(Po),r=kt(ma),s=le(()=>{const l=Ie(e.to);return t.resolve(l)}),n=le(()=>{const{matched:l}=s.value,{length:u}=l,d=l[u-1],f=r.matched;if(!d||!f.length)return-1;const h=f.findIndex(xs.bind(null,d));if(h>-1)return h;const g=Ll(l[u-2]);return u>1&&Ll(d)===g&&f[f.length-1].path!==g?f.findIndex(xs.bind(null,l[u-2])):h}),o=le(()=>n.value>-1&&Om(r.params,s.value.params)),i=le(()=>n.value>-1&&n.value===r.matched.length-1&&vd(r.params,s.value.params));function a(l={}){if(Rm(l)){const u=t[Ie(e.replace)?"replace":"push"](Ie(e.to)).catch(en);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:le(()=>s.value.href),isActive:o,isExactActive:i,navigate:a}}function Pm(e){return e.length===1?e[0]:e}const Tm=Ns({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ml,setup(e,{slots:t}){const r=or(Ml(e)),{options:s}=kt(Po),n=le(()=>({[Vl(e.activeClass,s.linkActiveClass,"router-link-active")]:r.isActive,[Vl(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const o=t.default&&Pm(t.default(r));return e.custom?o:rs("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:n.value},o)}}}),km=Tm;function Rm(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Om(e,t){for(const r in t){const s=t[r],n=e[r];if(typeof s=="string"){if(s!==n)return!1}else if(!Wt(n)||n.length!==s.length||s.some((o,i)=>o!==n[i]))return!1}return!0}function Ll(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Vl=(e,t,r)=>e??t??r,Im=Ns({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const s=kt(ki),n=le(()=>e.route||s.value),o=kt(Nl,0),i=le(()=>{let u=Ie(o);const{matched:d}=n.value;let f;for(;(f=d[u])&&!f.components;)u++;return u}),a=le(()=>n.value.matched[i.value]);$r(Nl,le(()=>i.value+1)),$r(Cm,a),$r(ki,n);const l=oe();return Et(()=>[l.value,a.value,e.name],([u,d,f],[h,g,y])=>{d&&(d.instances[f]=u,g&&g!==d&&u&&u===h&&(d.leaveGuards.size||(d.leaveGuards=g.leaveGuards),d.updateGuards.size||(d.updateGuards=g.updateGuards))),u&&d&&(!g||!xs(d,g)||!h)&&(d.enterCallbacks[f]||[]).forEach(E=>E(u))},{flush:"post"}),()=>{const u=n.value,d=e.name,f=a.value,h=f&&f.components[d];if(!h)return jl(r.default,{Component:h,route:u});const g=f.props[d],y=g?g===!0?u.params:typeof g=="function"?g(u):g:null,A=rs(h,je({},y,t,{onVnodeUnmounted:m=>{m.component.isUnmounted&&(f.instances[d]=null)},ref:l}));return jl(r.default,{Component:A,route:u})||A}}});function jl(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const xm=Im;function $m(e){const t=ym(e.routes,e),r=e.parseQuery||Sm,s=e.stringifyQuery||Dl,n=e.history,o=Gs(),i=Gs(),a=Gs(),l=Jc(Sr);let u=Sr;ms&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const d=Jo.bind(null,O=>""+O),f=Jo.bind(null,Hg),h=Jo.bind(null,cn);function g(O,J){let H,ie;return bd(O)?(H=t.getRecordMatcher(O),ie=J):ie=O,t.addRoute(ie,H)}function y(O){const J=t.getRecordMatcher(O);J&&t.removeRoute(J)}function E(){return t.getRoutes().map(O=>O.record)}function A(O){return!!t.getRecordMatcher(O)}function m(O,J){if(J=je({},J||l.value),typeof O=="string"){const T=Xo(r,O,J.path),V=t.resolve({path:T.path},J),B=n.createHref(T.fullPath);return je(T,V,{params:h(V.params),hash:cn(T.hash),redirectedFrom:void 0,href:B})}let H;if(O.path!=null)H=je({},O,{path:Xo(r,O.path,J.path).path});else{const T=je({},O.params);for(const V in T)T[V]==null&&delete T[V];H=je({},O,{params:f(T)}),J.params=f(J.params)}const ie=t.resolve(H,J),ke=O.hash||"";ie.params=d(h(ie.params));const v=Wg(s,je({},O,{hash:Bg(ke),path:ie.path})),_=n.createHref(v);return je({fullPath:v,hash:ke,query:s===Dl?Am(O.query):O.query||{}},ie,{redirectedFrom:void 0,href:_})}function C(O){return typeof O=="string"?Xo(r,O,l.value.path):je({},O)}function $(O,J){if(u!==O)return $s(8,{from:J,to:O})}function I(O){return L(O)}function R(O){return I(je(C(O),{replace:!0}))}function M(O){const J=O.matched[O.matched.length-1];if(J&&J.redirect){const{redirect:H}=J;let ie=typeof H=="function"?H(O):H;return typeof ie=="string"&&(ie=ie.includes("?")||ie.includes("#")?ie=C(ie):{path:ie},ie.params={}),je({query:O.query,hash:O.hash,params:ie.path!=null?{}:O.params},ie)}}function L(O,J){const H=u=m(O),ie=l.value,ke=O.state,v=O.force,_=O.replace===!0,T=M(H);if(T)return L(je(C(T),{state:typeof T=="object"?je({},ke,T.state):ke,force:v,replace:_}),J||H);const V=H;V.redirectedFrom=J;let B;return!v&&Jg(s,ie,H)&&(B=$s(16,{to:V,from:ie}),ct(ie,ie,!0,!1)),(B?Promise.resolve(B):X(V,ie)).catch(U=>cr(U)?cr(U,2)?U:lt(U):Te(U,V,ie)).then(U=>{if(U){if(cr(U,2))return L(je({replace:_},C(U.to),{state:typeof U.to=="object"?je({},ke,U.to.state):ke,force:v}),J||V)}else U=z(V,ie,!0,_,ke);return q(V,ie,U),U})}function k(O,J){const H=$(O,J);return H?Promise.reject(H):Promise.resolve()}function x(O){const J=Ze.values().next().value;return J&&typeof J.runWithContext=="function"?J.runWithContext(O):O()}function X(O,J){let H;const[ie,ke,v]=Fm(O,J);H=Yo(ie.reverse(),"beforeRouteLeave",O,J);for(const T of ie)T.leaveGuards.forEach(V=>{H.push(kr(V,O,J))});const _=k.bind(null,O,J);return H.push(_),ne(H).then(()=>{H=[];for(const T of o.list())H.push(kr(T,O,J));return H.push(_),ne(H)}).then(()=>{H=Yo(ke,"beforeRouteUpdate",O,J);for(const T of ke)T.updateGuards.forEach(V=>{H.push(kr(V,O,J))});return H.push(_),ne(H)}).then(()=>{H=[];for(const T of v)if(T.beforeEnter)if(Wt(T.beforeEnter))for(const V of T.beforeEnter)H.push(kr(V,O,J));else H.push(kr(T.beforeEnter,O,J));return H.push(_),ne(H)}).then(()=>(O.matched.forEach(T=>T.enterCallbacks={}),H=Yo(v,"beforeRouteEnter",O,J,x),H.push(_),ne(H))).then(()=>{H=[];for(const T of i.list())H.push(kr(T,O,J));return H.push(_),ne(H)}).catch(T=>cr(T,8)?T:Promise.reject(T))}function q(O,J,H){a.list().forEach(ie=>x(()=>ie(O,J,H)))}function z(O,J,H,ie,ke){const v=$(O,J);if(v)return v;const _=J===Sr,T=ms?history.state:{};H&&(ie||_?n.replace(O.fullPath,je({scroll:_&&T&&T.scroll},ke)):n.push(O.fullPath,ke)),l.value=O,ct(O,J,H,_),lt()}let ue;function Ee(){ue||(ue=n.listen((O,J,H)=>{if(!Zt.listening)return;const ie=m(O),ke=M(ie);if(ke){L(je(ke,{replace:!0,force:!0}),ie).catch(en);return}u=ie;const v=l.value;ms&&sm(Pl(v.fullPath,H.delta),Co()),X(ie,v).catch(_=>cr(_,12)?_:cr(_,2)?(L(je(C(_.to),{force:!0}),ie).then(T=>{cr(T,20)&&!H.delta&&H.type===un.pop&&n.go(-1,!1)}).catch(en),Promise.reject()):(H.delta&&n.go(-H.delta,!1),Te(_,ie,v))).then(_=>{_=_||z(ie,v,!1),_&&(H.delta&&!cr(_,8)?n.go(-H.delta,!1):H.type===un.pop&&cr(_,20)&&n.go(-1,!1)),q(ie,v,_)}).catch(en)}))}let Oe=Gs(),ye=Gs(),se;function Te(O,J,H){lt(O);const ie=ye.list();return ie.length?ie.forEach(ke=>ke(O,J,H)):console.error(O),Promise.reject(O)}function st(){return se&&l.value!==Sr?Promise.resolve():new Promise((O,J)=>{Oe.add([O,J])})}function lt(O){return se||(se=!O,Ee(),Oe.list().forEach(([J,H])=>O?H(O):J()),Oe.reset()),O}function ct(O,J,H,ie){const{scrollBehavior:ke}=e;if(!ms||!ke)return Promise.resolve();const v=!H&&nm(Pl(O.fullPath,0))||(ie||!H)&&history.state&&history.state.scroll||null;return Ft().then(()=>ke(O,J,v)).then(_=>_&&rm(_)).catch(_=>Te(_,O,J))}const nt=O=>n.go(O);let jt;const Ze=new Set,Zt={currentRoute:l,listening:!0,addRoute:g,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:A,getRoutes:E,resolve:m,options:e,push:I,replace:R,go:nt,back:()=>nt(-1),forward:()=>nt(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:ye.add,isReady:st,install(O){const J=this;O.component("RouterLink",km),O.component("RouterView",xm),O.config.globalProperties.$router=J,Object.defineProperty(O.config.globalProperties,"$route",{enumerable:!0,get:()=>Ie(l)}),ms&&!jt&&l.value===Sr&&(jt=!0,I(n.location).catch(ke=>{}));const H={};for(const ke in Sr)Object.defineProperty(H,ke,{get:()=>l.value[ke],enumerable:!0});O.provide(Po,J),O.provide(ma,Wc(H)),O.provide(ki,l);const ie=O.unmount;Ze.add(O),O.unmount=function(){Ze.delete(O),Ze.size<1&&(u=Sr,ue&&ue(),ue=null,l.value=Sr,jt=!1,se=!1),ie()}}};function ne(O){return O.reduce((J,H)=>J.then(()=>x(H)),Promise.resolve())}return Zt}function Fm(e,t){const r=[],s=[],n=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(u=>xs(u,a))?s.push(a):r.push(a));const l=e.matched[i];l&&(t.matched.find(u=>xs(u,l))||n.push(l))}return[r,s,n]}function _n(){return kt(Po)}function va(e){return kt(ma)}const et=(e,t)=>{const r=e.__vccOpts||e;for(const[s,n]of t)r[s]=n;return r},Dm={key:0,class:"global-loading"},Nm={class:"loading-container"},Mm={class:"loading-text"},Lm={__name:"GlobalLoading",props:{isLoading:{type:Boolean,default:!1},message:{type:String,default:""}},setup(e){return(t,r)=>(w(),br(Hu,{name:"fade"},{default:ot(()=>[e.isLoading?(w(),S("div",Dm,[c("div",Nm,[r[0]||(r[0]=c("div",{class:"spinner"},null,-1)),c("p",Mm,j(e.message||"Loading..."),1)])])):ee("",!0)]),_:1}))}},Vm=et(Lm,[["__scopeId","data-v-548b1915"]]),jm={key:0,class:"error-boundary"},Um={class:"error-container"},Bm={class:"error-message"},Gm={key:0,class:"error-details"},qm={__name:"ErrorBoundary",props:{fallback:{type:Function,default:null}},setup(e){const t=e,r=_n(),s=oe(null),n=oe(null),o=oe(!1),i=le(()=>{var f,h,g,y,E;return s.value?(f=s.value.message)!=null&&f.includes("Network Error")?"Network error. Please check your internet connection and try again.":((h=s.value.response)==null?void 0:h.status)===404?"The requested resource was not found.":((g=s.value.response)==null?void 0:g.status)===403?"You do not have permission to access this resource.":((y=s.value.response)==null?void 0:y.status)===401?"Your session has expired. Please log in again.":((E=s.value.response)==null?void 0:E.status)>=500?"Server error. Please try again later.":s.value.message||"An unexpected error occurred.":""}),a=le(()=>{if(!s.value)return"";let f="";return s.value.stack&&(f+=`Error Stack:
${s.value.stack}

`),n.value&&(f+=`Component: ${n.value.component}
`,f+=`Props: ${JSON.stringify(n.value.props,null,2)}
`),s.value.response&&(f+=`Response Status: ${s.value.response.status}
`,f+=`Response Data: ${JSON.stringify(s.value.response.data,null,2)}
`),f}),l=()=>{o.value=!o.value},u=()=>{s.value=null,n.value=null,t.fallback&&typeof t.fallback=="function"?t.fallback():r.go(0)},d=()=>{s.value=null,n.value=null,r.push("/admin/dashboard")};return gu((f,h,g)=>{var y;return console.error("Error captured by boundary:",f),s.value=f,n.value={component:((y=h==null?void 0:h.$options)==null?void 0:y.name)||"Unknown",props:(h==null?void 0:h.$props)||{},info:g},!1}),$r("errorBoundary",{setError:f=>{s.value=f},clearError:()=>{s.value=null,n.value=null}}),(f,h)=>(w(),S("div",null,[s.value?(w(),S("div",jm,[c("div",Um,[h[2]||(h[2]=c("div",{class:"error-icon"},[c("i",{class:"fas fa-exclamation-triangle"})],-1)),h[3]||(h[3]=c("h2",{class:"error-title"},"Something went wrong",-1)),c("p",Bm,j(i.value),1),c("div",{class:"error-actions"},[c("button",{class:"button is-primary",onClick:u},h[0]||(h[0]=[c("span",{class:"icon"},[c("i",{class:"fas fa-sync-alt"})],-1),c("span",null,"Try Again",-1)])),c("button",{class:"button is-light",onClick:d},h[1]||(h[1]=[c("span",{class:"icon"},[c("i",{class:"fas fa-home"})],-1),c("span",null,"Go to Dashboard",-1)]))]),o.value?(w(),S("div",Gm,[c("pre",null,j(a.value),1)])):ee("",!0),c("button",{class:"button is-small is-text",onClick:l},j(o.value?"Hide Details":"Show Details"),1)])])):Op(f.$slots,"default",{key:1},void 0)]))}},Hm=et(qm,[["__scopeId","data-v-7ec835ac"]]);function Sd(e,t){return function(){return e.apply(t,arguments)}}const{toString:zm}=Object.prototype,{getPrototypeOf:ya}=Object,{iterator:To,toStringTag:Ad}=Symbol,ko=(e=>t=>{const r=zm.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Yt=e=>(e=e.toLowerCase(),t=>ko(t)===e),Ro=e=>t=>typeof t===e,{isArray:Ls}=Array,dn=Ro("undefined");function Km(e){return e!==null&&!dn(e)&&e.constructor!==null&&!dn(e.constructor)&&Rt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Cd=Yt("ArrayBuffer");function Wm(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Cd(e.buffer),t}const Jm=Ro("string"),Rt=Ro("function"),Pd=Ro("number"),Oo=e=>e!==null&&typeof e=="object",Xm=e=>e===!0||e===!1,Vn=e=>{if(ko(e)!=="object")return!1;const t=ya(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ad in e)&&!(To in e)},Ym=Yt("Date"),Zm=Yt("File"),Qm=Yt("Blob"),ev=Yt("FileList"),tv=e=>Oo(e)&&Rt(e.pipe),rv=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Rt(e.append)&&((t=ko(e))==="formdata"||t==="object"&&Rt(e.toString)&&e.toString()==="[object FormData]"))},sv=Yt("URLSearchParams"),[nv,ov,iv,av]=["ReadableStream","Request","Response","Headers"].map(Yt),lv=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function wn(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let s,n;if(typeof e!="object"&&(e=[e]),Ls(e))for(s=0,n=e.length;s<n;s++)t.call(null,e[s],s,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(s=0;s<i;s++)a=o[s],t.call(null,e[a],a,e)}}function Td(e,t){t=t.toLowerCase();const r=Object.keys(e);let s=r.length,n;for(;s-- >0;)if(n=r[s],t===n.toLowerCase())return n;return null}const Wr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,kd=e=>!dn(e)&&e!==Wr;function Ri(){const{caseless:e}=kd(this)&&this||{},t={},r=(s,n)=>{const o=e&&Td(t,n)||n;Vn(t[o])&&Vn(s)?t[o]=Ri(t[o],s):Vn(s)?t[o]=Ri({},s):Ls(s)?t[o]=s.slice():t[o]=s};for(let s=0,n=arguments.length;s<n;s++)arguments[s]&&wn(arguments[s],r);return t}const cv=(e,t,r,{allOwnKeys:s}={})=>(wn(t,(n,o)=>{r&&Rt(n)?e[o]=Sd(n,r):e[o]=n},{allOwnKeys:s}),e),uv=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),dv=(e,t,r,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},fv=(e,t,r,s)=>{let n,o,i;const a={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),o=n.length;o-- >0;)i=n[o],(!s||s(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=r!==!1&&ya(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},pv=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const s=e.indexOf(t,r);return s!==-1&&s===r},hv=e=>{if(!e)return null;if(Ls(e))return e;let t=e.length;if(!Pd(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},gv=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ya(Uint8Array)),mv=(e,t)=>{const s=(e&&e[To]).call(e);let n;for(;(n=s.next())&&!n.done;){const o=n.value;t.call(e,o[0],o[1])}},vv=(e,t)=>{let r;const s=[];for(;(r=e.exec(t))!==null;)s.push(r);return s},yv=Yt("HTMLFormElement"),bv=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,n){return s.toUpperCase()+n}),Ul=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),_v=Yt("RegExp"),Rd=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),s={};wn(r,(n,o)=>{let i;(i=t(n,o,e))!==!1&&(s[o]=i||n)}),Object.defineProperties(e,s)},wv=e=>{Rd(e,(t,r)=>{if(Rt(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=e[r];if(Rt(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Ev=(e,t)=>{const r={},s=n=>{n.forEach(o=>{r[o]=!0})};return Ls(e)?s(e):s(String(e).split(t)),r},Sv=()=>{},Av=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Cv(e){return!!(e&&Rt(e.append)&&e[Ad]==="FormData"&&e[To])}const Pv=e=>{const t=new Array(10),r=(s,n)=>{if(Oo(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[n]=s;const o=Ls(s)?[]:{};return wn(s,(i,a)=>{const l=r(i,n+1);!dn(l)&&(o[a]=l)}),t[n]=void 0,o}}return s};return r(e,0)},Tv=Yt("AsyncFunction"),kv=e=>e&&(Oo(e)||Rt(e))&&Rt(e.then)&&Rt(e.catch),Od=((e,t)=>e?setImmediate:t?((r,s)=>(Wr.addEventListener("message",({source:n,data:o})=>{n===Wr&&o===r&&s.length&&s.shift()()},!1),n=>{s.push(n),Wr.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Rt(Wr.postMessage)),Rv=typeof queueMicrotask<"u"?queueMicrotask.bind(Wr):typeof process<"u"&&process.nextTick||Od,Ov=e=>e!=null&&Rt(e[To]),D={isArray:Ls,isArrayBuffer:Cd,isBuffer:Km,isFormData:rv,isArrayBufferView:Wm,isString:Jm,isNumber:Pd,isBoolean:Xm,isObject:Oo,isPlainObject:Vn,isReadableStream:nv,isRequest:ov,isResponse:iv,isHeaders:av,isUndefined:dn,isDate:Ym,isFile:Zm,isBlob:Qm,isRegExp:_v,isFunction:Rt,isStream:tv,isURLSearchParams:sv,isTypedArray:gv,isFileList:ev,forEach:wn,merge:Ri,extend:cv,trim:lv,stripBOM:uv,inherits:dv,toFlatObject:fv,kindOf:ko,kindOfTest:Yt,endsWith:pv,toArray:hv,forEachEntry:mv,matchAll:vv,isHTMLForm:yv,hasOwnProperty:Ul,hasOwnProp:Ul,reduceDescriptors:Rd,freezeMethods:wv,toObjectSet:Ev,toCamelCase:bv,noop:Sv,toFiniteNumber:Av,findKey:Td,global:Wr,isContextDefined:kd,isSpecCompliantForm:Cv,toJSONObject:Pv,isAsyncFn:Tv,isThenable:kv,setImmediate:Od,asap:Rv,isIterable:Ov};function Pe(e,t,r,s,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),s&&(this.request=s),n&&(this.response=n,this.status=n.status?n.status:null)}D.inherits(Pe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.status}}});const Id=Pe.prototype,xd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{xd[e]={value:e}});Object.defineProperties(Pe,xd);Object.defineProperty(Id,"isAxiosError",{value:!0});Pe.from=(e,t,r,s,n,o)=>{const i=Object.create(Id);return D.toFlatObject(e,i,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),Pe.call(i,e.message,t,r,s,n),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Iv=null;function Oi(e){return D.isPlainObject(e)||D.isArray(e)}function $d(e){return D.endsWith(e,"[]")?e.slice(0,-2):e}function Bl(e,t,r){return e?e.concat(t).map(function(n,o){return n=$d(n),!r&&o?"["+n+"]":n}).join(r?".":""):t}function xv(e){return D.isArray(e)&&!e.some(Oi)}const $v=D.toFlatObject(D,{},null,function(t){return/^is[A-Z]/.test(t)});function Io(e,t,r){if(!D.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=D.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,A){return!D.isUndefined(A[E])});const s=r.metaTokens,n=r.visitor||d,o=r.dots,i=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(t);if(!D.isFunction(n))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(D.isDate(y))return y.toISOString();if(!l&&D.isBlob(y))throw new Pe("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(y)||D.isTypedArray(y)?l&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function d(y,E,A){let m=y;if(y&&!A&&typeof y=="object"){if(D.endsWith(E,"{}"))E=s?E:E.slice(0,-2),y=JSON.stringify(y);else if(D.isArray(y)&&xv(y)||(D.isFileList(y)||D.endsWith(E,"[]"))&&(m=D.toArray(y)))return E=$d(E),m.forEach(function($,I){!(D.isUndefined($)||$===null)&&t.append(i===!0?Bl([E],I,o):i===null?E:E+"[]",u($))}),!1}return Oi(y)?!0:(t.append(Bl(A,E,o),u(y)),!1)}const f=[],h=Object.assign($v,{defaultVisitor:d,convertValue:u,isVisitable:Oi});function g(y,E){if(!D.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+E.join("."));f.push(y),D.forEach(y,function(m,C){(!(D.isUndefined(m)||m===null)&&n.call(t,m,D.isString(C)?C.trim():C,E,h))===!0&&g(m,E?E.concat(C):[C])}),f.pop()}}if(!D.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Gl(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function ba(e,t){this._pairs=[],e&&Io(e,this,t)}const Fd=ba.prototype;Fd.append=function(t,r){this._pairs.push([t,r])};Fd.toString=function(t){const r=t?function(s){return t.call(this,s,Gl)}:Gl;return this._pairs.map(function(n){return r(n[0])+"="+r(n[1])},"").join("&")};function Fv(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Dd(e,t,r){if(!t)return e;const s=r&&r.encode||Fv;D.isFunction(r)&&(r={serialize:r});const n=r&&r.serialize;let o;if(n?o=n(t,r):o=D.isURLSearchParams(t)?t.toString():new ba(t,r).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class ql{constructor(){this.handlers=[]}use(t,r,s){return this.handlers.push({fulfilled:t,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){D.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Nd={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Dv=typeof URLSearchParams<"u"?URLSearchParams:ba,Nv=typeof FormData<"u"?FormData:null,Mv=typeof Blob<"u"?Blob:null,Lv={isBrowser:!0,classes:{URLSearchParams:Dv,FormData:Nv,Blob:Mv},protocols:["http","https","file","blob","url","data"]},_a=typeof window<"u"&&typeof document<"u",Ii=typeof navigator=="object"&&navigator||void 0,Vv=_a&&(!Ii||["ReactNative","NativeScript","NS"].indexOf(Ii.product)<0),jv=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Uv=_a&&window.location.href||"http://localhost",Bv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:_a,hasStandardBrowserEnv:Vv,hasStandardBrowserWebWorkerEnv:jv,navigator:Ii,origin:Uv},Symbol.toStringTag,{value:"Module"})),yt={...Bv,...Lv};function Gv(e,t){return Io(e,new yt.classes.URLSearchParams,Object.assign({visitor:function(r,s,n,o){return yt.isNode&&D.isBuffer(r)?(this.append(s,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function qv(e){return D.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Hv(e){const t={},r=Object.keys(e);let s;const n=r.length;let o;for(s=0;s<n;s++)o=r[s],t[o]=e[o];return t}function Md(e){function t(r,s,n,o){let i=r[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),l=o>=r.length;return i=!i&&D.isArray(n)?n.length:i,l?(D.hasOwnProp(n,i)?n[i]=[n[i],s]:n[i]=s,!a):((!n[i]||!D.isObject(n[i]))&&(n[i]=[]),t(r,s,n[i],o)&&D.isArray(n[i])&&(n[i]=Hv(n[i])),!a)}if(D.isFormData(e)&&D.isFunction(e.entries)){const r={};return D.forEachEntry(e,(s,n)=>{t(qv(s),n,r,0)}),r}return null}function zv(e,t,r){if(D.isString(e))try{return(t||JSON.parse)(e),D.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(e)}const En={transitional:Nd,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const s=r.getContentType()||"",n=s.indexOf("application/json")>-1,o=D.isObject(t);if(o&&D.isHTMLForm(t)&&(t=new FormData(t)),D.isFormData(t))return n?JSON.stringify(Md(t)):t;if(D.isArrayBuffer(t)||D.isBuffer(t)||D.isStream(t)||D.isFile(t)||D.isBlob(t)||D.isReadableStream(t))return t;if(D.isArrayBufferView(t))return t.buffer;if(D.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Gv(t,this.formSerializer).toString();if((a=D.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Io(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||n?(r.setContentType("application/json",!1),zv(t)):t}],transformResponse:[function(t){const r=this.transitional||En.transitional,s=r&&r.forcedJSONParsing,n=this.responseType==="json";if(D.isResponse(t)||D.isReadableStream(t))return t;if(t&&D.isString(t)&&(s&&!this.responseType||n)){const i=!(r&&r.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?Pe.from(a,Pe.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:yt.classes.FormData,Blob:yt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};D.forEach(["delete","get","head","post","put","patch"],e=>{En.headers[e]={}});const Kv=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Wv=e=>{const t={};let r,s,n;return e&&e.split(`
`).forEach(function(i){n=i.indexOf(":"),r=i.substring(0,n).trim().toLowerCase(),s=i.substring(n+1).trim(),!(!r||t[r]&&Kv[r])&&(r==="set-cookie"?t[r]?t[r].push(s):t[r]=[s]:t[r]=t[r]?t[r]+", "+s:s)}),t},Hl=Symbol("internals");function qs(e){return e&&String(e).trim().toLowerCase()}function jn(e){return e===!1||e==null?e:D.isArray(e)?e.map(jn):String(e)}function Jv(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(e);)t[s[1]]=s[2];return t}const Xv=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Zo(e,t,r,s,n){if(D.isFunction(s))return s.call(this,t,r);if(n&&(t=r),!!D.isString(t)){if(D.isString(s))return t.indexOf(s)!==-1;if(D.isRegExp(s))return s.test(t)}}function Yv(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,s)=>r.toUpperCase()+s)}function Zv(e,t){const r=D.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+r,{value:function(n,o,i){return this[s].call(this,t,n,o,i)},configurable:!0})})}let Ot=class{constructor(t){t&&this.set(t)}set(t,r,s){const n=this;function o(a,l,u){const d=qs(l);if(!d)throw new Error("header name must be a non-empty string");const f=D.findKey(n,d);(!f||n[f]===void 0||u===!0||u===void 0&&n[f]!==!1)&&(n[f||l]=jn(a))}const i=(a,l)=>D.forEach(a,(u,d)=>o(u,d,l));if(D.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(D.isString(t)&&(t=t.trim())&&!Xv(t))i(Wv(t),r);else if(D.isObject(t)&&D.isIterable(t)){let a={},l,u;for(const d of t){if(!D.isArray(d))throw TypeError("Object iterator must return a key-value pair");a[u=d[0]]=(l=a[u])?D.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}i(a,r)}else t!=null&&o(r,t,s);return this}get(t,r){if(t=qs(t),t){const s=D.findKey(this,t);if(s){const n=this[s];if(!r)return n;if(r===!0)return Jv(n);if(D.isFunction(r))return r.call(this,n,s);if(D.isRegExp(r))return r.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=qs(t),t){const s=D.findKey(this,t);return!!(s&&this[s]!==void 0&&(!r||Zo(this,this[s],s,r)))}return!1}delete(t,r){const s=this;let n=!1;function o(i){if(i=qs(i),i){const a=D.findKey(s,i);a&&(!r||Zo(s,s[a],a,r))&&(delete s[a],n=!0)}}return D.isArray(t)?t.forEach(o):o(t),n}clear(t){const r=Object.keys(this);let s=r.length,n=!1;for(;s--;){const o=r[s];(!t||Zo(this,this[o],o,t,!0))&&(delete this[o],n=!0)}return n}normalize(t){const r=this,s={};return D.forEach(this,(n,o)=>{const i=D.findKey(s,o);if(i){r[i]=jn(n),delete r[o];return}const a=t?Yv(o):String(o).trim();a!==o&&delete r[o],r[a]=jn(n),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return D.forEach(this,(s,n)=>{s!=null&&s!==!1&&(r[n]=t&&D.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const s=new this(t);return r.forEach(n=>s.set(n)),s}static accessor(t){const s=(this[Hl]=this[Hl]={accessors:{}}).accessors,n=this.prototype;function o(i){const a=qs(i);s[a]||(Zv(n,i),s[a]=!0)}return D.isArray(t)?t.forEach(o):o(t),this}};Ot.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.reduceDescriptors(Ot.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[r]=s}}});D.freezeMethods(Ot);function Qo(e,t){const r=this||En,s=t||r,n=Ot.from(s.headers);let o=s.data;return D.forEach(e,function(a){o=a.call(r,o,n.normalize(),t?t.status:void 0)}),n.normalize(),o}function Ld(e){return!!(e&&e.__CANCEL__)}function Vs(e,t,r){Pe.call(this,e??"canceled",Pe.ERR_CANCELED,t,r),this.name="CanceledError"}D.inherits(Vs,Pe,{__CANCEL__:!0});function Vd(e,t,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?e(r):t(new Pe("Request failed with status code "+r.status,[Pe.ERR_BAD_REQUEST,Pe.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Qv(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function ey(e,t){e=e||10;const r=new Array(e),s=new Array(e);let n=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),d=s[o];i||(i=u),r[n]=l,s[n]=u;let f=o,h=0;for(;f!==n;)h+=r[f++],f=f%e;if(n=(n+1)%e,n===o&&(o=(o+1)%e),u-i<t)return;const g=d&&u-d;return g?Math.round(h*1e3/g):void 0}}function ty(e,t){let r=0,s=1e3/t,n,o;const i=(u,d=Date.now())=>{r=d,n=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const d=Date.now(),f=d-r;f>=s?i(u,d):(n=u,o||(o=setTimeout(()=>{o=null,i(n)},s-f)))},()=>n&&i(n)]}const so=(e,t,r=3)=>{let s=0;const n=ey(50,250);return ty(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,l=i-s,u=n(l),d=i<=a;s=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&d?(a-i)/u:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},r)},zl=(e,t)=>{const r=e!=null;return[s=>t[0]({lengthComputable:r,total:e,loaded:s}),t[1]]},Kl=e=>(...t)=>D.asap(()=>e(...t)),ry=yt.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,yt.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(yt.origin),yt.navigator&&/(msie|trident)/i.test(yt.navigator.userAgent)):()=>!0,sy=yt.hasStandardBrowserEnv?{write(e,t,r,s,n,o){const i=[e+"="+encodeURIComponent(t)];D.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),D.isString(s)&&i.push("path="+s),D.isString(n)&&i.push("domain="+n),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ny(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function oy(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function jd(e,t,r){let s=!ny(t);return e&&(s||r==!1)?oy(e,t):t}const Wl=e=>e instanceof Ot?{...e}:e;function ss(e,t){t=t||{};const r={};function s(u,d,f,h){return D.isPlainObject(u)&&D.isPlainObject(d)?D.merge.call({caseless:h},u,d):D.isPlainObject(d)?D.merge({},d):D.isArray(d)?d.slice():d}function n(u,d,f,h){if(D.isUndefined(d)){if(!D.isUndefined(u))return s(void 0,u,f,h)}else return s(u,d,f,h)}function o(u,d){if(!D.isUndefined(d))return s(void 0,d)}function i(u,d){if(D.isUndefined(d)){if(!D.isUndefined(u))return s(void 0,u)}else return s(void 0,d)}function a(u,d,f){if(f in t)return s(u,d);if(f in e)return s(void 0,u)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(u,d,f)=>n(Wl(u),Wl(d),f,!0)};return D.forEach(Object.keys(Object.assign({},e,t)),function(d){const f=l[d]||n,h=f(e[d],t[d],d);D.isUndefined(h)&&f!==a||(r[d]=h)}),r}const Ud=e=>{const t=ss({},e);let{data:r,withXSRFToken:s,xsrfHeaderName:n,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=Ot.from(i),t.url=Dd(jd(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(D.isFormData(r)){if(yt.hasStandardBrowserEnv||yt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...d]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...d].join("; "))}}if(yt.hasStandardBrowserEnv&&(s&&D.isFunction(s)&&(s=s(t)),s||s!==!1&&ry(t.url))){const u=n&&o&&sy.read(o);u&&i.set(n,u)}return t},iy=typeof XMLHttpRequest<"u",ay=iy&&function(e){return new Promise(function(r,s){const n=Ud(e);let o=n.data;const i=Ot.from(n.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=n,d,f,h,g,y;function E(){g&&g(),y&&y(),n.cancelToken&&n.cancelToken.unsubscribe(d),n.signal&&n.signal.removeEventListener("abort",d)}let A=new XMLHttpRequest;A.open(n.method.toUpperCase(),n.url,!0),A.timeout=n.timeout;function m(){if(!A)return;const $=Ot.from("getAllResponseHeaders"in A&&A.getAllResponseHeaders()),R={data:!a||a==="text"||a==="json"?A.responseText:A.response,status:A.status,statusText:A.statusText,headers:$,config:e,request:A};Vd(function(L){r(L),E()},function(L){s(L),E()},R),A=null}"onloadend"in A?A.onloadend=m:A.onreadystatechange=function(){!A||A.readyState!==4||A.status===0&&!(A.responseURL&&A.responseURL.indexOf("file:")===0)||setTimeout(m)},A.onabort=function(){A&&(s(new Pe("Request aborted",Pe.ECONNABORTED,e,A)),A=null)},A.onerror=function(){s(new Pe("Network Error",Pe.ERR_NETWORK,e,A)),A=null},A.ontimeout=function(){let I=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const R=n.transitional||Nd;n.timeoutErrorMessage&&(I=n.timeoutErrorMessage),s(new Pe(I,R.clarifyTimeoutError?Pe.ETIMEDOUT:Pe.ECONNABORTED,e,A)),A=null},o===void 0&&i.setContentType(null),"setRequestHeader"in A&&D.forEach(i.toJSON(),function(I,R){A.setRequestHeader(R,I)}),D.isUndefined(n.withCredentials)||(A.withCredentials=!!n.withCredentials),a&&a!=="json"&&(A.responseType=n.responseType),u&&([h,y]=so(u,!0),A.addEventListener("progress",h)),l&&A.upload&&([f,g]=so(l),A.upload.addEventListener("progress",f),A.upload.addEventListener("loadend",g)),(n.cancelToken||n.signal)&&(d=$=>{A&&(s(!$||$.type?new Vs(null,e,A):$),A.abort(),A=null)},n.cancelToken&&n.cancelToken.subscribe(d),n.signal&&(n.signal.aborted?d():n.signal.addEventListener("abort",d)));const C=Qv(n.url);if(C&&yt.protocols.indexOf(C)===-1){s(new Pe("Unsupported protocol "+C+":",Pe.ERR_BAD_REQUEST,e));return}A.send(o||null)})},ly=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let s=new AbortController,n;const o=function(u){if(!n){n=!0,a();const d=u instanceof Error?u:this.reason;s.abort(d instanceof Pe?d:new Vs(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new Pe(`timeout ${t} of ms exceeded`,Pe.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:l}=s;return l.unsubscribe=()=>D.asap(a),l}},cy=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let s=0,n;for(;s<r;)n=s+t,yield e.slice(s,n),s=n},uy=async function*(e,t){for await(const r of dy(e))yield*cy(r,t)},dy=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:s}=await t.read();if(r)break;yield s}}finally{await t.cancel()}},Jl=(e,t,r,s)=>{const n=uy(e,t);let o=0,i,a=l=>{i||(i=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:u,value:d}=await n.next();if(u){a(),l.close();return}let f=d.byteLength;if(r){let h=o+=f;r(h)}l.enqueue(new Uint8Array(d))}catch(u){throw a(u),u}},cancel(l){return a(l),n.return()}},{highWaterMark:2})},xo=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Bd=xo&&typeof ReadableStream=="function",fy=xo&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Gd=(e,...t)=>{try{return!!e(...t)}catch{return!1}},py=Bd&&Gd(()=>{let e=!1;const t=new Request(yt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Xl=64*1024,xi=Bd&&Gd(()=>D.isReadableStream(new Response("").body)),no={stream:xi&&(e=>e.body)};xo&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!no[t]&&(no[t]=D.isFunction(e[t])?r=>r[t]():(r,s)=>{throw new Pe(`Response type '${t}' is not supported`,Pe.ERR_NOT_SUPPORT,s)})})})(new Response);const hy=async e=>{if(e==null)return 0;if(D.isBlob(e))return e.size;if(D.isSpecCompliantForm(e))return(await new Request(yt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(D.isArrayBufferView(e)||D.isArrayBuffer(e))return e.byteLength;if(D.isURLSearchParams(e)&&(e=e+""),D.isString(e))return(await fy(e)).byteLength},gy=async(e,t)=>{const r=D.toFiniteNumber(e.getContentLength());return r??hy(t)},my=xo&&(async e=>{let{url:t,method:r,data:s,signal:n,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:h}=Ud(e);u=u?(u+"").toLowerCase():"text";let g=ly([n,o&&o.toAbortSignal()],i),y;const E=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let A;try{if(l&&py&&r!=="get"&&r!=="head"&&(A=await gy(d,s))!==0){let R=new Request(t,{method:"POST",body:s,duplex:"half"}),M;if(D.isFormData(s)&&(M=R.headers.get("content-type"))&&d.setContentType(M),R.body){const[L,k]=zl(A,so(Kl(l)));s=Jl(R.body,Xl,L,k)}}D.isString(f)||(f=f?"include":"omit");const m="credentials"in Request.prototype;y=new Request(t,{...h,signal:g,method:r.toUpperCase(),headers:d.normalize().toJSON(),body:s,duplex:"half",credentials:m?f:void 0});let C=await fetch(y);const $=xi&&(u==="stream"||u==="response");if(xi&&(a||$&&E)){const R={};["status","statusText","headers"].forEach(x=>{R[x]=C[x]});const M=D.toFiniteNumber(C.headers.get("content-length")),[L,k]=a&&zl(M,so(Kl(a),!0))||[];C=new Response(Jl(C.body,Xl,L,()=>{k&&k(),E&&E()}),R)}u=u||"text";let I=await no[D.findKey(no,u)||"text"](C,e);return!$&&E&&E(),await new Promise((R,M)=>{Vd(R,M,{data:I,headers:Ot.from(C.headers),status:C.status,statusText:C.statusText,config:e,request:y})})}catch(m){throw E&&E(),m&&m.name==="TypeError"&&/Load failed|fetch/i.test(m.message)?Object.assign(new Pe("Network Error",Pe.ERR_NETWORK,e,y),{cause:m.cause||m}):Pe.from(m,m&&m.code,e,y)}}),$i={http:Iv,xhr:ay,fetch:my};D.forEach($i,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Yl=e=>`- ${e}`,vy=e=>D.isFunction(e)||e===null||e===!1,qd={getAdapter:e=>{e=D.isArray(e)?e:[e];const{length:t}=e;let r,s;const n={};for(let o=0;o<t;o++){r=e[o];let i;if(s=r,!vy(r)&&(s=$i[(i=String(r)).toLowerCase()],s===void 0))throw new Pe(`Unknown adapter '${i}'`);if(s)break;n[i||"#"+o]=s}if(!s){const o=Object.entries(n).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Yl).join(`
`):" "+Yl(o[0]):"as no adapter specified";throw new Pe("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:$i};function ei(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Vs(null,e)}function Zl(e){return ei(e),e.headers=Ot.from(e.headers),e.data=Qo.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),qd.getAdapter(e.adapter||En.adapter)(e).then(function(s){return ei(e),s.data=Qo.call(e,e.transformResponse,s),s.headers=Ot.from(s.headers),s},function(s){return Ld(s)||(ei(e),s&&s.response&&(s.response.data=Qo.call(e,e.transformResponse,s.response),s.response.headers=Ot.from(s.response.headers))),Promise.reject(s)})}const Hd="1.9.0",$o={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{$o[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Ql={};$o.transitional=function(t,r,s){function n(o,i){return"[Axios v"+Hd+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,a)=>{if(t===!1)throw new Pe(n(i," has been removed"+(r?" in "+r:"")),Pe.ERR_DEPRECATED);return r&&!Ql[i]&&(Ql[i]=!0,console.warn(n(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,i,a):!0}};$o.spelling=function(t){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function yy(e,t,r){if(typeof e!="object")throw new Pe("options must be an object",Pe.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let n=s.length;for(;n-- >0;){const o=s[n],i=t[o];if(i){const a=e[o],l=a===void 0||i(a,o,e);if(l!==!0)throw new Pe("option "+o+" must be "+l,Pe.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Pe("Unknown option "+o,Pe.ERR_BAD_OPTION)}}const Un={assertOptions:yy,validators:$o},er=Un.validators;let Zr=class{constructor(t){this.defaults=t||{},this.interceptors={request:new ql,response:new ql}}async request(t,r){try{return await this._request(t,r)}catch(s){if(s instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const o=n.stack?n.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=ss(this.defaults,r);const{transitional:s,paramsSerializer:n,headers:o}=r;s!==void 0&&Un.assertOptions(s,{silentJSONParsing:er.transitional(er.boolean),forcedJSONParsing:er.transitional(er.boolean),clarifyTimeoutError:er.transitional(er.boolean)},!1),n!=null&&(D.isFunction(n)?r.paramsSerializer={serialize:n}:Un.assertOptions(n,{encode:er.function,serialize:er.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Un.assertOptions(r,{baseUrl:er.spelling("baseURL"),withXsrfToken:er.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=o&&D.merge(o.common,o[r.method]);o&&D.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),r.headers=Ot.concat(i,o);const a=[];let l=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(r)===!1||(l=l&&E.synchronous,a.unshift(E.fulfilled,E.rejected))});const u=[];this.interceptors.response.forEach(function(E){u.push(E.fulfilled,E.rejected)});let d,f=0,h;if(!l){const y=[Zl.bind(this),void 0];for(y.unshift.apply(y,a),y.push.apply(y,u),h=y.length,d=Promise.resolve(r);f<h;)d=d.then(y[f++],y[f++]);return d}h=a.length;let g=r;for(f=0;f<h;){const y=a[f++],E=a[f++];try{g=y(g)}catch(A){E.call(this,A);break}}try{d=Zl.call(this,g)}catch(y){return Promise.reject(y)}for(f=0,h=u.length;f<h;)d=d.then(u[f++],u[f++]);return d}getUri(t){t=ss(this.defaults,t);const r=jd(t.baseURL,t.url,t.allowAbsoluteUrls);return Dd(r,t.params,t.paramsSerializer)}};D.forEach(["delete","get","head","options"],function(t){Zr.prototype[t]=function(r,s){return this.request(ss(s||{},{method:t,url:r,data:(s||{}).data}))}});D.forEach(["post","put","patch"],function(t){function r(s){return function(o,i,a){return this.request(ss(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Zr.prototype[t]=r(),Zr.prototype[t+"Form"]=r(!0)});let by=class zd{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const s=this;this.promise.then(n=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](n);s._listeners=null}),this.promise.then=n=>{let o;const i=new Promise(a=>{s.subscribe(a),o=a}).then(n);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,a){s.reason||(s.reason=new Vs(o,i,a),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=s=>{t.abort(s)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new zd(function(n){t=n}),cancel:t}}};function _y(e){return function(r){return e.apply(null,r)}}function wy(e){return D.isObject(e)&&e.isAxiosError===!0}const Fi={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Fi).forEach(([e,t])=>{Fi[t]=e});function Kd(e){const t=new Zr(e),r=Sd(Zr.prototype.request,t);return D.extend(r,Zr.prototype,t,{allOwnKeys:!0}),D.extend(r,t,null,{allOwnKeys:!0}),r.create=function(n){return Kd(ss(e,n))},r}const Be=Kd(En);Be.Axios=Zr;Be.CanceledError=Vs;Be.CancelToken=by;Be.isCancel=Ld;Be.VERSION=Hd;Be.toFormData=Io;Be.AxiosError=Pe;Be.Cancel=Be.CanceledError;Be.all=function(t){return Promise.all(t)};Be.spread=_y;Be.isAxiosError=wy;Be.mergeConfig=ss;Be.AxiosHeaders=Ot;Be.formToJSON=e=>Md(D.isHTMLForm(e)?new FormData(e):e);Be.getAdapter=qd.getAdapter;Be.HttpStatusCode=Fi;Be.default=Be;const{Axios:kT,AxiosError:RT,CanceledError:OT,isCancel:IT,CancelToken:xT,VERSION:$T,all:FT,Cancel:DT,isAxiosError:NT,spread:MT,toFormData:LT,AxiosHeaders:VT,HttpStatusCode:jT,formToJSON:UT,getAdapter:BT,mergeConfig:GT}=Be,we=Be.create({baseURL:"/api",headers:{"Content-Type":"application/json"},timeout:3e4});we.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));we.interceptors.response.use(e=>(console.log(`API Response [${e.config.method.toUpperCase()}] ${e.config.url}:`,e.status),e),async e=>{if(console.error("API Error:",e),e.response){console.error(`API Error Response [${e.config.method.toUpperCase()}] ${e.config.url}:`,{status:e.response.status,statusText:e.response.statusText,data:e.response.data});const t=e.config;e.response.status===401&&!t._retry&&(t._retry=!0,localStorage.removeItem("user"),localStorage.removeItem("token"),window.location.href="/login")}else e.request?console.error("API Error: No response received",e.request):console.error("API Error: Request setup error",e.message);return Promise.reject(e)});class Ey{async getAll(t={}){try{return await we.get("/categories/all",{params:t})}catch(r){if(console.error("Error fetching categories:",r),r.response&&r.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await we.get("/admin/categories",{params:t});throw r}}async getAllRootCategories(t={}){try{return await we.get("/categories/root",{params:t})}catch(r){if(console.error("Error fetching categories:",r),r.response&&r.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await we.get("/admin/categories",{params:t});throw r}}async getSubCategories(t,r={}){try{return await we.get(`/categories/${t}/subcategories`,{params:r})}catch(s){if(console.error("Error fetching subcategories:",s),s.response&&s.response.status===404)return console.warn("subcategories endpoint not found, trying admin endpoint"),await we.get("/admin/categories",{params:r});throw s}}async getProducts(t,r={}){try{return await we.get(`/categories/${t}/products`,{params:r})}catch(s){if(console.error("Error fetching categories:",s),s.response&&s.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await we.get(`/admin/categories/${t}/products`,{params:r});throw s}}async getRandomProducts(t,r={}){try{return await we.get(`/categories/${t}/products/random`,{params:r})}catch(s){throw console.error("Error fetching random products:",s),s}}async getById(t){try{return await we.get(`/categories/${t}`)}catch(r){if(console.error(`Error fetching category ${t}:`,r),r.response&&r.response.status===404)return console.warn("Category endpoint not found, trying admin endpoint"),await we.get(`/admin/categories/${t}`);throw r}}async getBySlug(t){try{return await we.get(`/categories/slug/${t}`)}catch(r){throw console.error(`Error fetching category ${t}:`,r),r}}async create(t){try{return await we.post("/categories",t)}catch(r){if(console.error("Error creating category:",r),r.response&&r.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await we.post("/admin/categories",t);throw r}}async update(t,r){try{return await we.put(`/categories/${t}`,r)}catch(s){if(console.error(`Error updating category ${t}:`,s),s.response&&s.response.status===404)return console.warn("Category endpoint not found, trying admin endpoint"),await we.put(`/admin/categories/${t}`,r);throw s}}async delete(t){try{return await we.delete(`/categories/${t}`)}catch(r){if(console.error(`Error deleting category ${t}:`,r),r.response&&r.response.status===404)return console.warn("Category endpoint not found, trying admin endpoint"),await we.delete(`/admin/categories/${t}`);throw r}}async getStats(){try{return await we.get("/categories/stats")}catch(t){if(console.error("Error fetching category stats:",t),t.response&&t.response.status===404)return console.warn("Category stats endpoint not found, trying admin endpoint"),await we.get("/admin/categories/stats");throw t}}}const Mt=new Ey,Sy=[{id:1,name:"Ноутбуки та комп'ютери",slug:"laptops-computers",parentId:null,image:null},{id:2,name:"Смартфони, ТВ і електроніка",slug:"smartphones-tv-electronics",parentId:null,image:null},{id:3,name:"Побутова техніка",slug:"household-appliances",parentId:null,image:null},{id:4,name:"Товари для дому",slug:"home-goods",parentId:null,image:null},{id:5,name:"Дача, сад і город",slug:"garden",parentId:null,image:null},{id:6,name:"Спорт і хобі",slug:"sports-hobbies",parentId:null,image:null},{id:7,name:"Краса та здоров'я",slug:"beauty-health",parentId:null,image:null},{id:8,name:"Одяг, взуття та прикраси",slug:"clothing-shoes-jewelry",parentId:null,image:null},{id:101,name:"Ноутбуки",slug:"laptops",parentId:1,image:null},{id:102,name:"Комп'ютери",slug:"computers",parentId:1,image:null},{id:103,name:"Комплектуючі",slug:"components",parentId:1,image:null},{id:1001,name:"Apple Macbook",slug:"apple-macbook",parentId:101,image:null},{id:1002,name:"Acer",slug:"acer",parentId:101,image:null},{id:1003,name:"ASUS",slug:"asus",parentId:101,image:null},{id:1004,name:"Lenovo",slug:"lenovo",parentId:101,image:null},{id:1005,name:"HP (Hewlett Packard)",slug:"hp",parentId:101,image:null},{id:1006,name:"Dell",slug:"dell",parentId:101,image:null},{id:1007,name:"Всі ноутбуки",slug:"all-laptops",parentId:101,image:null},{id:2001,name:"Системні блоки (ПК)",slug:"desktop-pc",parentId:102,image:null},{id:2002,name:"Монітори",slug:"monitors",parentId:102,image:null},{id:2003,name:"Клавіатури та миші",slug:"keyboards-mice",parentId:102,image:null},{id:2004,name:"Комп'ютерні колонки",slug:"pc-speakers",parentId:102,image:null},{id:2005,name:"Програмне забезпечення",slug:"software",parentId:102,image:null},{id:201,name:"Смартфони",slug:"smartphones",parentId:2,image:null},{id:202,name:"Планшети",slug:"tablets",parentId:2,image:null},{id:203,name:"Телевізори",slug:"tvs",parentId:2,image:null},{id:3001,name:"Apple iPad",slug:"apple-ipad",parentId:202,image:null},{id:3002,name:"Samsung",slug:"samsung-tablets",parentId:202,image:null},{id:3003,name:"Lenovo",slug:"lenovo-tablets",parentId:202,image:null},{id:3004,name:"Xiaomi",slug:"xiaomi-tablets",parentId:202,image:null},{id:3005,name:"Усі планшети",slug:"all-tablets",parentId:202,image:null}],Ay={name:"CategoryMenu",props:{isOpen:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){const r=oe([]),s=oe(null),n=oe(!1),o=oe(null),i=async()=>{n.value=!0,o.value=null;try{const g={pageSize:152},y=await Mt.getAll(g);r.value=y.data.data||[],r.value.length>0&&!s.value&&(s.value=r.value[0])}catch(g){console.warn("Using mock category data as fallback:",g),r.value=Sy,r.value.length>0&&!s.value&&(s.value=r.value[0]),o.value=null}finally{n.value=!1}},a=le(()=>r.value.filter(g=>!g.parentId)),l=le(()=>s.value?r.value.filter(g=>g.parentId===s.value.id):[]),u=le(()=>{if(!s.value)return{};const g={};return r.value.filter(E=>E.parentId===s.value.id).forEach(E=>{const A=r.value.filter(m=>m.parentId===E.id);A.length>0&&(g[E.name]=A)}),g}),d=g=>{s.value=g},f=()=>{t("close")},h=g=>({"Ноутбуки та комп'ютери":"fas fa-laptop","Смартфони, ТВ і електроніка":"fas fa-mobile-alt","Побутова техніка":"fas fa-blender","Товари для дому":"fas fa-home","Дача, сад і город":"fas fa-seedling","Спорт і хобі":"fas fa-running",Спорт:"fas fa-running","Краса та здоров'я":"fas fa-heartbeat","Одяг, взуття та прикраси":"fas fa-tshirt",Одяг:"fas fa-tshirt"})[g.name]||"fas fa-folder";return ir(i),{categories:r,mainCategories:a,selectedCategory:s,subcategories:l,groupedSubcategories:u,isLoading:n,error:o,selectCategory:d,closeMenu:f,getCategoryIcon:h}}},Cy={class:"category-menu-content"},Py={class:"category-menu-sidebar"},Ty={class:"category-list"},ky=["onMouseenter"],Ry={class:"category-icon"},Oy=["href"],Iy={class:"category-menu-subcategories"},xy={key:0,class:"subcategories-container"},$y={class:"subcategory-group-title"},Fy={class:"subcategory-list"},Dy=["href"];function Ny(e,t,r,s,n,o){return r.isOpen?(w(),S("div",{key:0,class:"category-menu-overlay",onClick:t[2]||(t[2]=(...i)=>s.closeMenu&&s.closeMenu(...i))},[c("div",{class:"category-menu-container",onClick:t[1]||(t[1]=Ir(()=>{},["stop"]))},[c("div",Cy,[c("div",Py,[c("ul",Ty,[(w(!0),S(be,null,Ne(s.mainCategories,i=>(w(),S("li",{key:i.id,class:Ce(["category-item",{active:s.selectedCategory===i}]),onMouseenter:a=>s.selectCategory(i)},[c("div",Ry,[c("i",{class:Ce(s.getCategoryIcon(i))},null,2)]),c("a",{href:`/catalog/${i.slug} `,class:"category-label"},[ve(j(i.name)+" ",1),t[3]||(t[3]=c("i",{class:"fas fa-chevron-right category-arrow"},null,-1))],8,Oy)],42,ky))),128))])]),c("div",Iy,[c("button",{class:"close-button",onClick:t[0]||(t[0]=(...i)=>s.closeMenu&&s.closeMenu(...i))},t[4]||(t[4]=[c("i",{class:"fas fa-times"},null,-1)])),s.selectedCategory?(w(),S("div",xy,[(w(!0),S(be,null,Ne(s.groupedSubcategories,(i,a)=>(w(),S("div",{key:a,class:"subcategory-group"},[c("h3",$y,j(a),1),c("ul",Fy,[(w(!0),S(be,null,Ne(i,l=>(w(),S("li",{key:l.id,class:"subcategory-item"},[c("a",{href:`/catalog/${l.slug}`,class:"subcategory-link"},j(l.name),9,Dy)]))),128))])]))),128))])):ee("",!0)])])])])):ee("",!0)}const My=et(Ay,[["render",Ny],["__scopeId","data-v-caf4cade"]]),Ly="data:image/svg+xml,%3csvg%20width='33'%20height='33'%20fill='none'%20viewBox='0%200%2033%2033'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M13.6248%202.45789H2.73926V13.3434H13.6248V2.45789Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M30.7306%202.45789H19.8451V13.3434H30.7306V2.45789Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M30.7306%2019.5637H19.8451V30.4492H30.7306V19.5637Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13.6248%2019.5637H2.73926V30.4492H13.6248V19.5637Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",Vy={name:"CategoryMenuButton",components:{CategoryMenu:My},setup(){const e=oe(!1);return{isMenuOpen:e,toggleMenu:()=>{e.value=!e.value},closeMenu:()=>{e.value=!1}}}},jy={class:"category-menu-Button"};function Uy(e,t,r,s,n,o){const i=Ke("category-menu");return w(),S("div",jy,[c("button",{class:"catalog-btn",onClick:t[0]||(t[0]=(...a)=>s.toggleMenu&&s.toggleMenu(...a))},t[1]||(t[1]=[c("img",{src:Ly,alt:"Klondike",class:"catalog-pic"},null,-1),ve(" Каталог ")])),ce(i,{"is-open":s.isMenuOpen,onClose:s.closeMenu},null,8,["is-open","onClose"])])}const By=et(Vy,[["render",Uy],["__scopeId","data-v-c1d8e211"]]),ur=oe([]);let Gy=0;function wa(){const e=(s,n="info",o=5e3)=>{const i=Gy++,a={id:i,message:s,type:n,visible:!1};return ur.value.push(a),setTimeout(()=>{const l=ur.value.findIndex(u=>u.id===i);l!==-1&&(ur.value[l].visible=!0)},10),setTimeout(()=>{t(i)},o),i},t=s=>{const n=ur.value.findIndex(o=>o.id===s);n!==-1&&(ur.value[n].visible=!1,setTimeout(()=>{ur.value=ur.value.filter(o=>o.id!==s)},300))};return{toasts:ur,showToast:e,hideToast:t,clearToasts:()=>{ur.value=[]}}}const qy={class:"toast-container"},Hy=["onClick"],zy={class:"toast-icon"},Ky={class:"toast-content"},Wy={class:"toast-message"},Jy=["onClick"],Xy={__name:"Toast",setup(e){const{toasts:t,hideToast:r}=wa(),s=n=>{switch(n){case"success":return"fas fa-check-circle";case"error":return"fas fa-exclamation-circle";case"warning":return"fas fa-exclamation-triangle";case"info":default:return"fas fa-info-circle"}};return(n,o)=>(w(),S("div",qy,[ce(Kh,{name:"toast"},{default:ot(()=>[(w(!0),S(be,null,Ne(Ie(t),i=>(w(),S("div",{key:i.id,class:Ce(["toast",[`toast-${i.type}`,{"toast-visible":i.visible}]]),onClick:a=>Ie(r)(i.id)},[c("div",zy,[c("i",{class:Ce(s(i.type))},null,2)]),c("div",Ky,[c("p",Wy,j(i.message),1)]),c("button",{class:"toast-close",onClick:Ir(a=>Ie(r)(i.id),["stop"])},o[0]||(o[0]=[c("i",{class:"fas fa-times"},null,-1)]),8,Jy)],10,Hy))),128))]),_:1})]))}},Yy=et(Xy,[["__scopeId","data-v-c52d5360"]]),oo="/logo.svg",Zy={name:"App",components:{GlobalLoading:Vm,ErrorBoundary:Hm,CategoryMenuButton:By,Toast:Yy},setup(){const e=bn(),t=_n(),r=va(),s=le(()=>e.getters["auth/isLoggedIn"]),n=le(()=>e.getters["auth/user"]),o=le(()=>e.getters["loading/isLoading"]),i=le(()=>e.getters["loading/loadingMessage"]),a=le(()=>!["Login","Register"].includes(r.name)),l=le(()=>!["Login","Register"].includes(r.name)),u=le(()=>{if(!n.value)return"/";const f=n.value.role;let h=!1;typeof f=="string"?h=f==="Admin":typeof f=="number"?h=f===4:f&&typeof f=="object"&&(f.hasOwnProperty("value")&&(h=f.value==="Admin"||f.value===4),f.hasOwnProperty("name")&&(h=f.name==="Admin"));const g=e.getters["auth/isAdmin"],y=h||g;return console.log("App.vue - User role:",n.value.role),console.log("App.vue - Is admin by role?",h),console.log("App.vue - Is admin by getter?",g),y?"/admin/dashboard":"/dashboard"});return{isLoggedIn:s,currentUser:n,dashboardLink:u,logout:async()=>{await e.dispatch("auth/logout"),t.push("/login")},showHeader:a,showFooter:l,isLoading:o,loadingMessage:i}}},Qy={class:"app-container"},e0={key:0,class:"header"},t0={class:"header-content"},r0={class:"logo-container"},s0={class:"header-left"},n0={class:"header-actions"},o0={key:1,class:"footer"};function i0(e,t,r,s,n,o){const i=Ke("global-loading"),a=Ke("router-link"),l=Ke("category-menu-button"),u=Ke("router-view"),d=Ke("error-boundary"),f=Ke("Toast");return w(),S("div",Qy,[ce(i,{"is-loading":s.isLoading,message:s.loadingMessage},null,8,["is-loading","message"]),s.showHeader?(w(),S("header",e0,[c("div",t0,[c("div",r0,[ce(a,{to:"/"},{default:ot(()=>t[0]||(t[0]=[c("img",{src:oo,alt:"Klondike",class:"logo"},null,-1)])),_:1})]),c("div",s0,[ce(l)]),t[5]||(t[5]=c("div",{class:"search-container"},[c("input",{type:"text",class:"search-input",placeholder:"Пошук"}),c("button",{class:"search-btn"},[c("i",{class:"fas fa-search"})])],-1)),c("div",n0,[ce(a,{to:"/wishlist",class:"header-action-btn"},{default:ot(()=>t[1]||(t[1]=[c("i",{class:"far fa-heart"},null,-1)])),_:1}),s.isLoggedIn?(w(),br(a,{key:0,to:"/user/profile",class:"header-action-btn"},{default:ot(()=>t[2]||(t[2]=[c("i",{class:"far fa-user"},null,-1)])),_:1})):(w(),br(a,{key:1,to:"/login",class:"header-action-btn"},{default:ot(()=>t[3]||(t[3]=[c("i",{class:"far fa-user"},null,-1)])),_:1})),ce(a,{to:"/cart",class:"header-action-btn cart-btn"},{default:ot(()=>t[4]||(t[4]=[c("i",{class:"fas fa-shopping-cart"},null,-1)])),_:1})])])])):ee("",!0),ce(d,null,{default:ot(()=>[ce(u,null,{default:ot(({Component:h})=>[ce(Hu,{name:"fade",mode:"out-in"},{default:ot(()=>[(w(),br(_o(h),{key:e.$route.fullPath}))]),_:2},1024)]),_:1})]),_:1}),s.showFooter?(w(),S("footer",o0,t[6]||(t[6]=[Nr('<div class="footer-content"><div class="footer-logo"><img src="'+oo+'" alt="Klondike" class="logo"></div><div class="footer-section"><h3 class="footer-title">Інформація про компанію</h3><ul class="footer-links"><li><a href="#">Про нас</a></li><li><a href="#">Контакти</a></li><li><a href="#">Магазини</a></li><li><a href="#">Вакансії</a></li></ul></div><div class="footer-section"><h3 class="footer-title">Допомога покупцеві</h3><ul class="footer-links"><li><a href="#">Центр допомоги клієнтам</a></li><li><a href="#">Доставка та оплата</a></li><li><a href="#">Обмін і повернення товару</a></li><li><a href="#">Гарантії</a></li><li><a href="#">Сервісні центри</a></li></ul></div><div class="footer-section"><h3 class="footer-title">Номер телефону</h3><ul class="footer-links"><li><a href="#">Пошта</a></li></ul><div class="social-links"><a href="#" class="social-link"><i class="fab fa-telegram"></i></a><a href="#" class="social-link"><i class="fab fa-youtube"></i></a><a href="#" class="social-link"><i class="fab fa-instagram"></i></a><a href="#" class="social-link"><i class="fab fa-facebook"></i></a></div></div></div><div class="copyright"> Всі права захищені </div>',2)]))):ee("",!0),ce(f)])}const a0=et(Zy,[["render",i0]]),l0="modulepreload",c0=function(e){return"/"+e},ec={},Ae=function(t,r,s){let n=Promise.resolve();if(r&&r.length>0){let i=function(u){return Promise.all(u.map(d=>Promise.resolve(d).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),l=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));n=i(r.map(u=>{if(u=c0(u),u in ec)return;ec[u]=!0;const d=u.endsWith(".css"),f=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const h=document.createElement("link");if(h.rel=d?"stylesheet":l0,d||(h.as="script"),h.crossOrigin="",h.href=u,l&&h.setAttribute("nonce",l),document.head.appendChild(h),d)return new Promise((g,y)=>{h.addEventListener("load",g),h.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return n.then(i=>{for(const a of i||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})},Bt=new Map,u0=(e,t)=>`${t}:${e}`,d0=()=>{Bt.forEach((e,t)=>{e.abort(),Bt.delete(t)})},f0=e=>{Bt.forEach((t,r)=>{r.includes(e)&&(t.abort(),Bt.delete(r))})},p0=async(e,t,r={})=>{const s=r.method||"GET",n=u0(t,s);Bt.has(n)&&(Bt.get(n).abort(),Bt.delete(n));const o=new AbortController;Bt.set(n,o);try{const i=await e(t,{...r,signal:o.signal});return Bt.delete(n),i}catch(i){if(Bt.delete(n),!Be.isCancel(i)&&i.name!=="AbortError")throw i;return{aborted:!0}}},We={cancelAllRequests:d0,cancelRequestsForRoute:f0,createCancellableRequest:p0,pendingRequests:Bt},gr=Be.create({baseURL:"http://localhost:5296",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:3e4});gr.interceptors.request.use(e=>{it.dispatch("loading/startRequest");const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(it.dispatch("loading/finishRequest"),Promise.reject(e)));gr.interceptors.response.use(e=>(it.dispatch("loading/finishRequest"),e),e=>{var t,r,s;return it.dispatch("loading/finishRequest"),Be.isCancel(e)?(console.log("Request cancelled:",e.message),Promise.reject(e)):(e.response&&e.response.status===401&&(localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.pathname!=="/login"&&(window.location.href="/login")),e.response&&e.response.status===403&&console.error("Permission denied:",e.response.data.message||"You do not have permission to perform this action"),e.response&&e.response.status===404&&console.error("Resource not found:",e.response.data.message||"The requested resource was not found"),e.response&&e.response.status===422&&console.error("Validation error:",e.response.data.errors||e.response.data.message||"Validation failed"),e.response&&e.response.status>=500&&console.error("Server error:",e.response.data.message||"An unexpected server error occurred"),e.message==="Network Error"&&console.error("Network error: Please check your internet connection"),e.code==="ECONNABORTED"&&(console.error("Request timeout: The server took too long to respond"),e.isTimeout=!0),console.error("API Error:",{url:(t=e.config)==null?void 0:t.url,method:(r=e.config)==null?void 0:r.method,status:(s=e.response)==null?void 0:s.status,message:e.message,code:e.code}),Promise.reject(e))});const Se={async get(e,t={}){try{const r=new AbortController,s={...t,signal:r.signal},n=`GET:${e}`;We.pendingRequests.has(n)&&We.pendingRequests.get(n).abort(),We.pendingRequests.set(n,r);const o=await gr.get(e,s);return We.pendingRequests.delete(n),o}catch(r){throw Be.isCancel(r)||console.error(`GET ${e} error:`,r),r}},async post(e,t={},r={}){try{const s=new AbortController,n={...r,signal:s.signal},o=`POST:${e}`;We.pendingRequests.has(o)&&We.pendingRequests.get(o).abort(),We.pendingRequests.set(o,s);const i=await gr.post(e,t,n);return We.pendingRequests.delete(o),i}catch(s){throw Be.isCancel(s)||console.error(`POST ${e} error:`,s),s}},async put(e,t={},r={}){try{const s=new AbortController,n={...r,signal:s.signal},o=`PUT:${e}`;We.pendingRequests.has(o)&&We.pendingRequests.get(o).abort(),We.pendingRequests.set(o,s);const i=await gr.put(e,t,n);return We.pendingRequests.delete(o),i}catch(s){throw Be.isCancel(s)||console.error(`PUT ${e} error:`,s),s}},async patch(e,t={},r={}){try{const s=new AbortController,n={...r,signal:s.signal},o=`PATCH:${e}`;We.pendingRequests.has(o)&&We.pendingRequests.get(o).abort(),We.pendingRequests.set(o,s);const i=await gr.patch(e,t,n);return We.pendingRequests.delete(o),i}catch(s){throw Be.isCancel(s)||console.error(`PATCH ${e} error:`,s),s}},async delete(e,t={}){try{const r=new AbortController,s={...t,signal:r.signal},n=`DELETE:${e}`;We.pendingRequests.has(n)&&We.pendingRequests.get(n).abort(),We.pendingRequests.set(n,r);const o=await gr.delete(e,s);return We.pendingRequests.delete(n),o}catch(r){throw Be.isCancel(r)||console.error(`DELETE ${e} error:`,r),r}},async upload(e,t,r=null,s={}){try{const n=new AbortController,o={headers:{"Content-Type":"multipart/form-data"},...s,signal:n.signal};r&&(o.onUploadProgress=r);const i=`UPLOAD:${e}`;We.pendingRequests.has(i)&&We.pendingRequests.get(i).abort(),We.pendingRequests.set(i,n);const a=await gr.post(e,t,o);return We.pendingRequests.delete(i),a}catch(n){throw Be.isCancel(n)||console.error(`UPLOAD ${e} error:`,n),n}},cancelAllRequests(){We.cancelAllRequests()},cancelRequestsForRoute(e){We.cancelRequestsForRoute(e)}},h0=Object.freeze(Object.defineProperty({__proto__:null,api:gr,default:Se},Symbol.toStringTag,{value:"Module"})),ti="/api/auth/";class g0{async login(t,r){try{console.log("Attempting login with email:",t);const s=await Se.post(ti+"login",{email:t,password:r});if(console.log("Full login response:",s),console.log("Response data:",s.data),!s||!s.data)throw console.error("Empty response or no data"),new Error("Empty response from server");const n=s.data;let o,i;if(n.success&&n.data?(o=n.data.token,i=n.data.user,console.log("Found token and user in ApiResponse.data format")):(o=n.token,i=n.user,console.log("Found token and user in direct format")),!o)throw console.error("No token in response:",n),new Error("No authentication token received");if(!i)throw console.error("No user data in response:",n),new Error("No user data received");return console.log("User data from login:",i),console.log("User role:",i.role),localStorage.setItem("user",JSON.stringify(i)),localStorage.setItem("token",o),{token:o,user:i}}catch(s){throw console.error("Login error:",s),s.response&&(console.error("Error response data:",s.response.data),console.error("Error response status:",s.response.status)),localStorage.removeItem("user"),localStorage.removeItem("token"),s}}async googleLogin(t){try{console.log("Attempting Google login with token");const r=await Se.post(ti+"google-login",{idToken:t});if(console.log("Full Google login response:",r),console.log("Google login response data:",r.data),!r||!r.data)throw console.error("Empty response or no data from Google login"),new Error("Empty response from server");const s=r.data;let n,o;if(s.success&&s.data?(n=s.data.token,o=s.data.user,console.log("Found token and user in ApiResponse.data format for Google login")):(n=s.token,o=s.user,console.log("Found token and user in direct format for Google login")),!n)throw console.error("No token in Google login response:",s),new Error("No authentication token received");if(!o)throw console.error("No user data in Google login response:",s),new Error("No user data received");return console.log("User data from Google login:",o),console.log("User role from Google login:",o.role),localStorage.setItem("user",JSON.stringify(o)),localStorage.setItem("token",n),{token:n,user:o}}catch(r){throw console.error("Google login error:",r),r.response&&(console.error("Error response data:",r.response.data),console.error("Error response status:",r.response.status)),localStorage.removeItem("user"),localStorage.removeItem("token"),r}}logout(){localStorage.removeItem("user"),localStorage.removeItem("token")}async register(t){return console.log("Registering user:",t),Se.post(ti+"register",{username:t.username||t.email.split("@")[0],email:t.email,password:t.password})}}const Rn=new g0,tc=JSON.parse(localStorage.getItem("user")),m0=tc?{status:{loggedIn:!0},user:tc}:{status:{loggedIn:!1},user:null},v0={namespaced:!0,state:m0,getters:{isLoggedIn:e=>e.status.loggedIn,isAuthenticated:e=>e.status.loggedIn,user:e=>e.user,isAdmin:e=>{if(!e.user)return!1;const t=e.user.role;if(console.log("Role in isAdmin getter:",t),console.log("Role type:",typeof t),typeof t=="string")return t==="Admin";if(typeof t=="number")return t===4;if(t&&typeof t=="object"){if(t.hasOwnProperty("value"))return t.value==="Admin"||t.value===4;if(t.hasOwnProperty("name"))return t.name==="Admin"}return!1},isModerator:e=>{if(!e.user)return!1;const t=e.user.role;if(typeof t=="string")return t==="Moderator";if(typeof t=="number")return t===3;if(t&&typeof t=="object"){if(t.hasOwnProperty("value"))return t.value==="Moderator"||t.value===3;if(t.hasOwnProperty("name"))return t.name==="Moderator"}return!1},isAdminOrModerator:e=>{if(!e.user)return!1;const t=e.user.role;if(typeof t=="string")return t==="Admin"||t==="Moderator";if(typeof t=="number")return t===3||t===4;if(t&&typeof t=="object"){if(t.hasOwnProperty("value"))return t.value==="Admin"||t.value==="Moderator"||t.value===3||t.value===4;if(t.hasOwnProperty("name"))return t.name==="Admin"||t.name==="Moderator"}return!1}},actions:{async login({commit:e},{username:t,password:r}){try{const s=t;if(!s||!r)throw new Error("Email and password are required");const n=await Rn.login(s,r);if(console.log("Auth data received from service:",n),!n||!n.token||!n.user)throw console.error("Invalid auth data from service:",n),e("loginFailure"),new Error("Authentication failed");return e("loginSuccess",n),Promise.resolve(n)}catch(s){return console.error("Login action error:",s),e("loginFailure"),Promise.reject(s)}},async googleLogin({commit:e},t){try{if(!t)throw new Error("Google ID token is required");console.log("Attempting Google login with token in Vuex");const r=await Rn.googleLogin(t);if(console.log("Google auth data received from service:",r),!r||!r.token||!r.user)throw console.error("Invalid Google auth data from service:",r),e("loginFailure"),new Error("Google authentication failed");return e("loginSuccess",r),Promise.resolve(r)}catch(r){return console.error("Google login action error:",r),e("loginFailure"),Promise.reject(r)}},async register({commit:e},t){try{if(console.log("Register action in Vuex with user data:",t),!t.email||!t.password)throw new Error("Email and password are required");t.username||(t.username=t.email.split("@")[0]);const r=await Rn.register(t);return console.log("Register response in Vuex:",r),e("registerSuccess"),Promise.resolve(r.data)}catch(r){return console.error("Register error in Vuex:",r),r.response&&(console.error("Error response in Vuex:",r.response),console.error("Error response data in Vuex:",r.response.data)),e("registerFailure"),Promise.reject(r)}},logout({commit:e}){Rn.logout(),e("logout")}},mutations:{loginSuccess(e,t){if(console.log("Login success mutation with data:",t),!t||!t.user){console.error("Missing user data in loginSuccess mutation");return}e.status.loggedIn=!0,e.user=t.user,console.log("Updated auth state:",e)},loginFailure(e){e.status.loggedIn=!1,e.user=null},registerSuccess(e){e.status.loggedIn=!1},registerFailure(e){e.status.loggedIn=!1},logout(e){e.status.loggedIn=!1,e.user=null}}},y0={namespaced:!0,state:{isLoading:!1,loadingMessage:"",pendingRequests:0,routeChanging:!1},getters:{isLoading:e=>e.isLoading||e.routeChanging,loadingMessage:e=>e.loadingMessage,hasPendingRequests:e=>e.pendingRequests>0,apiService:()=>Se},mutations:{SET_LOADING(e,t){e.isLoading=t},SET_LOADING_MESSAGE(e,t){e.loadingMessage=t},INCREMENT_PENDING_REQUESTS(e){e.pendingRequests++},DECREMENT_PENDING_REQUESTS(e){e.pendingRequests=Math.max(0,e.pendingRequests-1)},RESET_PENDING_REQUESTS(e){e.pendingRequests=0},SET_ROUTE_CHANGING(e,t){e.routeChanging=t}},actions:{startLoading({commit:e},t=""){e("SET_LOADING",!0),e("SET_LOADING_MESSAGE",t)},stopLoading({commit:e}){e("SET_LOADING",!1),e("SET_LOADING_MESSAGE","")},startRequest({commit:e,state:t}){e("INCREMENT_PENDING_REQUESTS"),t.pendingRequests===1&&e("SET_LOADING",!0)},finishRequest({commit:e,state:t}){e("DECREMENT_PENDING_REQUESTS"),t.pendingRequests===0&&e("SET_LOADING",!1)},resetRequests({commit:e}){e("RESET_PENDING_REQUESTS"),e("SET_LOADING",!1)},startRouteChange({commit:e},t="Loading page..."){e("SET_ROUTE_CHANGING",!0),e("SET_LOADING_MESSAGE",t)},finishRouteChange({commit:e}){e("SET_ROUTE_CHANGING",!1),e("SET_LOADING_MESSAGE","")}}},fe={categories:null,categoryTree:null,categoryDetails:{},lastFetched:{categories:null,categoryTree:null},cacheTimeout:5*60*1e3},rc=e=>fe.lastFetched[e]?new Date().getTime()-fe.lastFetched[e]<fe.cacheTimeout:!1,zr={async getAll(e={}){return zr.getCategories(e)},async getCategories(e={}){const{signal:t,...r}=e,s=Object.keys(r).length>0;if(!s&&rc("categories")&&fe.categories)return fe.categories;try{try{console.log("Fetching categories with params:",r);const n={params:r};t&&(n.signal=t);const o=await Se.get("/api/categories/all",n);console.log("Categories API response:",o.data);let i={};return o.data&&(o.data.data&&Array.isArray(o.data.data)?i={data:o.data.data,total:o.data.total||0,currentPage:o.data.currentPage||1,totalPages:o.data.lastPage||1,totalItems:o.data.total||0,pageSize:o.data.perPage||15,from:o.data.from||0,to:o.data.to||0,categories:o.data.data,totalCount:o.data.total||0}:Array.isArray(o.data)?i={data:o.data,total:o.data.length,currentPage:1,totalPages:1,totalItems:o.data.length,pageSize:o.data.length,from:o.data.length>0?1:0,to:o.data.length,categories:o.data,totalCount:o.data.length}:i={data:[],total:0,currentPage:1,totalPages:1,totalItems:0,pageSize:15,from:0,to:0,categories:[],totalCount:0}),s||(fe.categories=i,fe.lastFetched.categories=new Date().getTime()),i}catch(n){console.warn("Admin categories endpoint failed, falling back to public endpoint:",n.message);try{const o=await Se.get("/api/categories/all",{params:e});console.log("Fallback categories API response:",o.data);let i={};return o.data&&(o.data.data&&Array.isArray(o.data.data)?i={data:o.data.data,total:o.data.total||0,currentPage:o.data.currentPage||1,totalPages:o.data.lastPage||1,totalItems:o.data.total||0,pageSize:o.data.perPage||15,categories:o.data.data,totalCount:o.data.total||0}:Array.isArray(o.data)&&(i={data:o.data,total:o.data.length,currentPage:1,totalPages:1,totalItems:o.data.length,pageSize:o.data.length,categories:o.data,totalCount:o.data.length})),s||(fe.categories=i,fe.lastFetched.categories=new Date().getTime()),i}catch(o){return console.error("Both admin and public categories endpoints failed:",o.message),{data:[],total:0,categories:[],totalCount:0}}}}catch(n){throw console.error("Error fetching categories:",n),n}},async getCategoryById(e){if(fe.categoryDetails[e])return fe.categoryDetails[e];try{const t=await Se.get(`/api/admin/categories/${e}`);return fe.categoryDetails[e]=t.data,t.data}catch(t){throw console.error(`Error fetching category ${e}:`,t),t}},async getById(e){try{const t=await Se.get(`/api/admin/categories/${e}`);return fe.categoryDetails[e]&&(fe.categoryDetails[e]=t.data),t.data}catch(t){throw console.error(`Error fetching category ${e}:`,t),t}},async getBySlug(e){try{return(await Se.get(`/api/categories/slug/${e}`)).data}catch(t){throw console.error(`Error fetching category by slug ${e}:`,t),t}},async createCategory(e){try{const t={...e};t.image===""&&(t.image=null),t.metaImage===""&&(t.metaImage=null),t.description===""&&(t.description=null),t.metaTitle===""&&(t.metaTitle=null),t.metaDescription===""&&(t.metaDescription=null);const r=await Se.post("/api/admin/categories",t);return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,r.data}catch(t){throw console.error("Error creating category:",t),t}},async updateCategory(e,t){try{const r={...t};r.image===""&&(r.image=null),r.metaImage===""&&(r.metaImage=null),r.description===""&&(r.description=null),r.metaTitle===""&&(r.metaTitle=null),r.metaDescription===""&&(r.metaDescription=null);const s=await Se.put(`/api/admin/categories/${e}`,r);return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&(fe.categoryDetails[e]=s.data.category||s.data),s.data}catch(r){throw console.error(`Error updating category ${e}:`,r),r}},async deleteCategory(e){try{const t=await Se.delete(`/api/admin/categories/${e}`);return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&delete fe.categoryDetails[e],t.data}catch(t){throw console.error(`Error deleting category ${e}:`,t),t}},async delete(e){return this.deleteCategory(e)},async uploadCategoryImage(e,t){try{const r=new FormData;r.append("image",t);const s=await Se.post(`/api/admin/categories/${e}/image`,r,{headers:{"Content-Type":"multipart/form-data"}});return fe.categoryDetails[e]&&delete fe.categoryDetails[e],s.data}catch(r){throw console.error(`Error uploading image for category ${e}:`,r),r}},async getCategoryTree(){var e,t,r;if(rc("categoryTree")&&fe.categoryTree)return fe.categoryTree;try{try{const s=filter?{filter}:{},n=await Se.get("/api/admin/categories/tree",{params:s});return fe.categoryTree=n.data,fe.lastFetched.categoryTree=new Date().getTime(),n.data}catch(s){console.warn("Admin categories tree endpoint failed, building tree from regular categories:",s.message);const o=(await this.getCategories()).categories||[],i=this.buildCategoryTree(o);return fe.categoryTree=i,fe.lastFetched.categoryTree=new Date().getTime(),i}}catch(s){console.error("Error fetching category tree:",s);const n=((e=s.response)==null?void 0:e.status)===404?"Category tree endpoint not found. Please check API configuration.":((t=s.response)==null?void 0:t.status)===403?"You do not have permission to access the category tree.":((r=s.response)==null?void 0:r.status)===401?"Authentication required. Please log in again.":s.message||"Failed to fetch category tree",o=new Error(n);throw o.originalError=s,o}},buildCategoryTree(e){const t={};e.forEach(s=>{t[s.id]={...s,children:[]}});const r=[];return e.forEach(s=>{const n=t[s.id];s.parentId&&t[s.parentId]?t[s.parentId].children.push(n):r.push(n)}),r},async moveCategory(e,t){try{const r=await Se.patch(`/api/admin/categories/${e}/move`,{parentId:t});return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&delete fe.categoryDetails[e],r.data}catch(r){throw console.error(`Error moving category ${e}:`,r),r}},async getStats(){try{return(await Se.get("/api/admin/categories/stats")).data}catch(e){throw console.error("Error fetching category stats:",e),e}},async getCategoryProducts(e,t={}){try{return(await Se.get("/api/admin/products",{params:{...t,categoryId:e}})).data}catch(r){throw console.error(`Error fetching products for category ${e}:`,r),r}},async getSubcategories(e,t={}){try{return(await Se.get("/api/admin/categories",{params:{...t,parentId:e,pageSize:100}})).data}catch(r){throw console.error(`Error fetching subcategories for category ${e}:`,r),r}},async bulkUpdateProductsCategory(e,t){try{const r=await Se.post(`/api/admin/categories/${e}/bulk-update-products-category`,{toCategoryId:t});return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&delete fe.categoryDetails[e],fe.categoryDetails[t]&&delete fe.categoryDetails[t],r.data}catch(r){throw console.error(`Error bulk updating products category from ${e} to ${t}:`,r),r}},clearCache(){fe.categories=null,fe.categoryTree=null,fe.categoryDetails={},fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null}},b0={namespaced:!0,state:{categories:[],categoryTree:[],categoryMap:{},loading:!1,error:null,lastFetched:null,cacheTimeout:5*60*1e3},getters:{allCategories:e=>e.categories,categoryById:e=>t=>e.categoryMap[t]||null,categoryTree:e=>e.categoryTree,rootCategories:e=>e.categories.filter(t=>!t.parentId),isLoading:e=>e.loading,hasError:e=>!!e.error,errorMessage:e=>e.error,isCacheValid:e=>e.lastFetched?new Date().getTime()-e.lastFetched<e.cacheTimeout:!1},mutations:{SET_CATEGORIES(e,t){e.categories=t,e.categoryMap={},t.forEach(r=>{e.categoryMap[r.id]=r}),e.lastFetched=new Date().getTime()},SET_CATEGORY_TREE(e,t){e.categoryTree=t},ADD_CATEGORY(e,t){e.categories.push(t),e.categoryMap[t.id]=t},UPDATE_CATEGORY(e,t){const r=e.categories.findIndex(s=>s.id===t.id);r!==-1&&(e.categories.splice(r,1,t),e.categoryMap[t.id]=t)},REMOVE_CATEGORY(e,t){e.categories=e.categories.filter(r=>r.id!==t),delete e.categoryMap[t]},SET_LOADING(e,t){e.loading=t},SET_ERROR(e,t){e.error=t},CLEAR_ERROR(e){e.error=null},INVALIDATE_CACHE(e){e.lastFetched=null}},actions:{async fetchCategories({commit:e,state:t,getters:r},s={}){if(!(Object.keys(s).length>0)&&r.isCacheValid&&t.categories.length>0)return{categories:t.categories};e("SET_LOADING",!0),e("CLEAR_ERROR");try{const o=await zr.getCategories(s);return o&&o.categories&&e("SET_CATEGORIES",o.categories),o}catch(o){const i=o.message||"Failed to fetch categories";e("SET_ERROR",i);const a=new Error(i);throw a.originalError=o.originalError||o,a}finally{e("SET_LOADING",!1)}},async fetchCategoryTree({commit:e,state:t,getters:r}){if(r.isCacheValid&&t.categoryTree.length>0)return t.categoryTree;e("SET_LOADING",!0),e("CLEAR_ERROR");try{const s=await zr.getCategoryTree();return e("SET_CATEGORY_TREE",s),s}catch(s){const n=s.message||"Failed to fetch category tree";e("SET_ERROR",n);const o=new Error(n);throw o.originalError=s.originalError||s,o}finally{e("SET_LOADING",!1)}},async fetchCategoryById({commit:e,getters:t},r){const s=t.categoryById(r);if(s)return s;e("SET_LOADING",!0),e("CLEAR_ERROR");try{const n=await zr.getCategoryById(r);return e("UPDATE_CATEGORY",n),n}catch(n){throw e("SET_ERROR",n.message||`Failed to fetch category ${r}`),n}finally{e("SET_LOADING",!1)}},async createCategory({commit:e},t){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const r=await zr.createCategory(t);return r.success&&r.category&&(e("ADD_CATEGORY",r.category),e("INVALIDATE_CACHE")),r}catch(r){throw e("SET_ERROR",r.message||"Failed to create category"),r}finally{e("SET_LOADING",!1)}},async updateCategory({commit:e},{id:t,categoryData:r}){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const s=await zr.updateCategory(t,r);return s.success&&s.category&&(e("UPDATE_CATEGORY",s.category),e("INVALIDATE_CACHE")),s}catch(s){throw e("SET_ERROR",s.message||`Failed to update category ${t}`),s}finally{e("SET_LOADING",!1)}},async deleteCategory({commit:e},t){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const r=await zr.deleteCategory(t);return r.success&&(e("REMOVE_CATEGORY",t),e("INVALIDATE_CACHE")),r}catch(r){throw e("SET_ERROR",r.message||`Failed to delete category ${t}`),r}finally{e("SET_LOADING",!1)}}}},it=Ig({modules:{auth:v0,loading:y0,categories:b0}});class _0{async getCart(t={}){try{return await we.get("/users/me/cart",{params:t})}catch(r){throw console.error("Error fetching cart:",r),r}}async addToCart(t,r={}){try{return await we.post("/users/me/cart/items",r={productId:t})}catch(s){throw console.error("Error adding item:",s),s}}async changeItemCount(t,r,s={}){try{return await we.put(`/users/me/cart/items/${t}`,s={quantity:r})}catch(n){throw console.error("Error increasing item count:",n),n}}async deleteItem(t){try{return await we.delete(`/users/me/cart/items/${t}`)}catch(r){throw console.error("Error increasing item count:",r),r}}async deleteCart(){try{return await we.delete("/users/me/cart")}catch(t){throw console.error("Error deleting cart:",t),t}}async checkout(){try{return await we.post("/users/me/cart/checkout")}catch(t){throw console.error("Error initiating payment:",t),t}}}const Rr=new _0;class w0{async getWishlist(t={}){try{return await we.get("/users/me/wishlist",{params:t})}catch(r){throw console.error("Error fetching Wishlist:",r),r}}async addToWishlist(t,r={}){try{return await we.post("/users/me/wishlist/items",r={productId:t})}catch(s){throw console.error("Error adding item:",s),s}}async removeFromWishlist(t){try{return await we.delete(`/users/me/wishlist/items/${t}`,{params:{itemId:t}})}catch(r){throw console.error("Error removing item:",r),r}}async deleteWishlist(t){try{return await we.delete(`/users/me/wishlist/${t}`)}catch(r){throw console.error("Error deleting Wishlist:",r),r}}}const Di=new w0,E0={name:"Pagination",props:{currentPage:{type:Number,required:!0},totalItems:{type:Number,required:!0},itemsPerPage:{type:Number,default:10},maxVisiblePages:{type:Number,default:5}},computed:{totalPages(){return Math.ceil(this.totalItems/this.itemsPerPage)||1},visiblePages(){const e=[],t=Math.floor(this.maxVisiblePages/2);let r=Math.max(1,this.currentPage-t);const s=Math.min(this.totalPages,r+this.maxVisiblePages-1);s-r+1<this.maxVisiblePages&&(r=Math.max(1,s-this.maxVisiblePages+1));for(let n=r;n<=s;n++)e.push(n);return e}},methods:{goToPage(e){e>=1&&e<=this.totalPages&&e!==this.currentPage&&this.$emit("page-changed",e)}}},S0={class:"pagination","aria-label":"Pagination"},A0=["disabled"],C0=["aria-label"],P0=["aria-label"],T0=["onClick","aria-current","aria-label"],k0=["aria-label"],R0=["aria-label"],O0=["disabled"];function I0(e,t,r,s,n,o){return w(),S("nav",S0,[c("button",{class:"pagination-btn-prev disable-styles",disabled:r.currentPage===1,onClick:t[0]||(t[0]=i=>o.goToPage(r.currentPage-1)),"aria-label":"Previous page"},t[6]||(t[6]=[c("span",{class:"fas fa-arrow-left"},null,-1)]),8,A0),o.visiblePages.indexOf(1)==-1?(w(),S("button",{key:0,class:"pagination-btn",onClick:t[1]||(t[1]=i=>o.goToPage(1)),"aria-label":`Go to page ${e.page}`}," 1 ",8,C0)):ee("",!0),o.visiblePages.indexOf(1)==-1?(w(),S("button",{key:1,class:"pagination-btn",onClick:t[2]||(t[2]=i=>o.goToPage(1)),"aria-label":`Go to page ${e.page}`},t[7]||(t[7]=[c("span",null,"...",-1)]),8,P0)):ee("",!0),(w(!0),S(be,null,Ne(o.visiblePages,i=>(w(),S("button",{key:i,class:Ce(["pagination-btn",{active:i===r.currentPage}]),onClick:a=>o.goToPage(i),"aria-current":i===r.currentPage?"page":null,"aria-label":`Go to page ${i}`},j(i),11,T0))),128)),o.visiblePages.indexOf(o.totalPages)==-1?(w(),S("button",{key:2,class:"pagination-btn",onClick:t[3]||(t[3]=i=>o.goToPage(1)),"aria-label":`Go to page ${e.page}`},t[8]||(t[8]=[c("span",null,"...",-1)]),8,k0)):ee("",!0),o.visiblePages.indexOf(o.totalPages)==-1?(w(),S("button",{key:3,class:"pagination-btn",onClick:t[4]||(t[4]=i=>o.goToPage(o.totalPages)),"aria-label":`Go to page ${e.page}`},j(o.totalPages),9,R0)):ee("",!0),c("button",{class:"pagination-btn-next disable-styles",disabled:r.currentPage===o.totalPages,onClick:t[5]||(t[5]=i=>o.goToPage(r.currentPage+1)),"aria-label":"Next page"},t[9]||(t[9]=[c("span",{class:"fas fa-arrow-right"},null,-1)]),8,O0)])}const Wd=et(E0,[["render",I0],["__scopeId","data-v-fa8e4778"]]);class x0{async getAll(t={}){return await we.get("/products",{params:t})}async getBySlug(t){return await we.get(`/products/slug/${t}`)}async getById(t){return await we.get(`/products/${t}`)}async getTopProducts(t={}){const r={limit:t.pageSize||t.limit||10,orderBy:t.orderBy||"Sales",descending:t.descending!==!1};return t.categorySlug&&(r.categorySlug=t.categorySlug),t.status!==void 0&&(r.status=t.status),await we.get("/products/top",{params:r})}async getTopProductsByCategory(t={}){const r={categorySlug:t.categorySlug,limit:t.pageSize||t.limit||10,orderBy:t.orderBy||"Sales",descending:t.descending!==!1};return t.status!==void 0&&(r.status=t.status),await we.get("/products/top",{params:r})}async getRecommendedProducts(t={}){const r={limit:t.pageSize||t.limit||10};return t.status!==void 0&&(r.status=t.status),await we.get("/products/recommended",{params:r})}async create(t){return await we.post("/products",t)}async update(t,r){return await we.put(`/products/${t}`,r)}async delete(t){return await we.delete(`/products/${t}`)}async uploadImage(t,r){return await we.post(`/productimages/${t}`,r,{headers:{"Content-Type":"multipart/form-data"}})}async deleteImage(t){return await we.delete(`/productimages/${t}`)}async getStats(){return await we.get("/products/stats")}async getFilters(t){return await we.get(`/products/filters/${t}`)}}const Jr=new x0,$0={name:"ProductGrid",components:{Pagination:Wd},setup(){const{showToast:e}=wa();return{showToast:e}},props:{fetchParams:{type:Object,default:()=>({})},products:{type:Array,default:null},loading:{type:Boolean,default:!1},error:{type:String,default:null},currentPage:{type:Number,default:1},totalItems:{type:Number,default:0},pageSize:{type:Number,default:12},emptyMessage:{type:String,default:"Товари не знайдено"},showActions:{type:Boolean,default:!0},showPagination:{type:Boolean,default:!1},gridColumns:{type:Number,default:4},autoFetch:{type:Boolean,default:!0},sellerMode:{type:Boolean,default:!1},showAddCard:{type:Boolean,default:!1},onProductClick:{type:Function,default:null}},data(){return{placeholderImage:"/placeholder-product.svg",internalProducts:[],internalLoading:!1,internalError:null,internalCurrentPage:1,internalTotalItems:0,internalPageSize:12}},computed:{displayProducts(){return this.products!==null?this.products:this.internalProducts},displayLoading(){return this.products!==null?this.loading:this.internalLoading},displayError(){return this.products!==null?this.error:this.internalError},displayCurrentPage(){return this.products!==null?this.currentPage:this.internalCurrentPage},displayTotalItems(){return this.products!==null?this.totalItems:this.internalTotalItems},displayPageSize(){return this.products!==null?this.pageSize:this.internalPageSize},totalPages(){return Math.ceil(this.displayTotalItems/this.displayPageSize)},gridClass(){return`grid-cols-${this.gridColumns}`}},async mounted(){this.fetchParams.pageSize&&(this.internalPageSize=this.fetchParams.pageSize),this.autoFetch&&await this.fetchProducts()},watch:{fetchParams:{handler(e){e.pageSize&&e.pageSize!==this.internalPageSize&&(this.internalPageSize=e.pageSize,this.internalCurrentPage=1),this.autoFetch&&this.fetchProducts()},deep:!0}},methods:{handleProductClick(e){if(this.onProductClick)this.onProductClick(e);else if(this.sellerMode){if(e.status!=="Approved")return;this.goToProduct(e.slug||e.productSlug||e.id)}else this.goToProduct(e.slug||e.productSlug||e.id)},getProductStatusText(e){return{Pending:"На розгляді",Approved:"Схвалено",Rejected:"Відхилено",Draft:"Чернетка"}[e]||e||"На розгляді"},async addToCart(e){try{await Rr.addToCart(e),this.showToast("Товар додано до корзини","success"),this.$emit("product-added-to-cart",e)}catch(t){console.error("Error adding to cart:",t),this.showToast("Помилка при додаванні до корзини","error")}},async addToWishlist(e){try{await Di.addToWishlist(e),this.showToast("Товар додано до обраних","success"),this.$emit("product-added-to-wishlist",e)}catch(t){console.error("Error adding to wishlist:",t),this.showToast("Помилка при додаванні до обраних","error")}},async removeFromWishlist(e){try{await Di.removeFromWishlist(e),this.showToast("Товар видалено з обраних","success"),this.$emit("product-removed-from-wishlist",e)}catch(t){console.error("Error removing from wishlist:",t),this.showToast("Помилка при видаленні з обраних","error")}},goToProduct(e){this.$router.push(`/product/${e}`)},handleImageError(e){e.target.src=this.placeholderImage},async fetchProducts(){this.internalLoading=!0,this.internalError=null;try{const e=this.fetchParams.pageSize||this.internalPageSize,t={pageSize:e,page:this.internalCurrentPage,...this.fetchParams};let r;t.categorySlug&&t.random?r=await Mt.getRandomProducts(t.categorySlug,t):t.categorySlug?r=await Mt.getProducts(t.categorySlug,t):t.type==="top"&&t.categorySlug?r=await Jr.getTopProductsByCategory(t):t.type==="top"?r=await Jr.getTopProducts(t):t.type==="recommended"?r=await Jr.getRecommendedProducts(t):r=await Jr.getAll(t),t.categorySlug&&t.random?(this.internalProducts=r.data||[],this.internalTotalItems=this.internalProducts.length):(this.internalProducts=r.data.data||r.data||[],this.internalTotalItems=r.data.total||r.total||this.internalProducts.length),console.log("ProductGrid fetch result:",{products:this.internalProducts.length,totalItems:this.internalTotalItems,currentPage:this.internalCurrentPage,pageSize:e,totalPages:Math.ceil(this.internalTotalItems/e),response:r.data})}catch(e){console.error("Error fetching products:",e),this.internalError="Помилка завантаження товарів. Спробуйте ще раз.",this.internalProducts=[],this.internalTotalItems=0}finally{this.internalLoading=!1}},onPageChange(e){console.log("Page change requested:",e),this.products!==null?this.$emit("page-changed",e):(this.internalCurrentPage=e,this.fetchProducts())},formatPrice(e){if(!e.priceAmount&&!e.price)return"Ціна не вказана";const t=e.priceAmount||e.price;if(t==0)return"Ціна не вказана";const r=Math.round(t),s=e.priceCurrency||e.currency;return!s||s==="UAH"?`${r} ₴`:s==="USD"?`${r} $`:s==="EUR"?`${r} €`:`${r} ${s}`}}},F0={class:"product-grid-container"},D0={key:0,class:"loading-container"},N0={key:1,class:"error-container"},M0={key:2,class:"empty-container"},L0=["onClick"],V0={key:0,class:"product-badge"},j0={class:"product-image"},U0=["src","alt"],B0={class:"product-info"},G0={class:"product-name"},q0={key:1,class:"product-availability"},H0={key:2,class:"product-unavailability"},z0={class:"product-price"},K0={class:"price"},W0={key:3,class:"product-actions"},J0=["onClick","disabled"],X0=["onClick"],Y0=["onClick"],Z0=["onClick"],Q0={key:4,class:"pagination-container"};function eb(e,t,r,s,n,o){const i=Ke("Pagination");return w(),S("div",F0,[o.displayLoading?(w(),S("div",D0,t[3]||(t[3]=[c("div",{class:"loader"},null,-1),c("p",null,"Завантаження товарів...",-1)]))):o.displayError?(w(),S("div",N0,[t[4]||(t[4]=c("h3",null,"Помилка завантаження",-1)),c("p",null,j(o.displayError),1),r.autoFetch?(w(),S("button",{key:0,onClick:t[0]||(t[0]=(...a)=>o.fetchProducts&&o.fetchProducts(...a)),class:"retry-btn"},"Спробувати знову")):ee("",!0)])):!o.displayProducts||o.displayProducts.length===0?(w(),S("div",M0,[t[5]||(t[5]=c("h3",null,"Товари не знайдено",-1)),c("p",null,j(r.emptyMessage||"На жаль, товарів у цій категорії поки немає"),1)])):ee("",!0),o.displayProducts.length>0||r.showAddCard?(w(),S("div",{key:3,class:Ce(["products-grid",o.gridClass])},[r.showAddCard?(w(),S("div",{key:0,class:"product-card add-product-card",onClick:t[1]||(t[1]=a=>e.$emit("add-product"))},t[6]||(t[6]=[c("div",{class:"add-product-content"},[c("div",{class:"add-product-icon"},"+"),c("div",{class:"add-product-text"},"Додати товар")],-1)]))):ee("",!0),(w(!0),S(be,null,Ne(o.displayProducts,a=>(w(),S("div",{key:a.id,class:Ce(["product-card",{"seller-product-card":r.sellerMode}]),onClick:l=>o.handleProductClick(a)},[a.badge?(w(),S("div",V0,j(a.badge),1)):ee("",!0),c("div",j0,[c("img",{src:a.image||a.mainImage||n.placeholderImage,alt:a.name||a.productName||"Product Image",onError:t[2]||(t[2]=(...l)=>o.handleImageError&&o.handleImageError(...l))},null,40,U0)]),c("div",B0,[c("h3",G0,j(a.name||a.productName),1),r.sellerMode&&a.status?(w(),S("div",{key:0,class:Ce(["product-status","status-"+(a.status||"pending").toLowerCase()])},j(o.getProductStatusText(a.status)),3)):!r.sellerMode&&(a.productStock>0||a.stock>0||a.inStock)?(w(),S("div",q0,t[7]||(t[7]=[c("span",{class:"availability-icon"},"✓",-1),c("span",{class:"availability-text"},"В наявності",-1)]))):r.sellerMode?ee("",!0):(w(),S("div",H0,t[8]||(t[8]=[c("span",{class:"availability-icon"},"✖",-1),c("span",{class:"availability-text"},"Немає в наявності",-1)]))),c("div",z0,[c("span",K0,j(o.formatPrice(a)),1)]),r.showActions&&!r.sellerMode?(w(),S("div",W0,[c("button",{onClick:Ir(l=>o.addToCart(a.productId||a.id),["stop"]),disabled:!(a.productStock>0||a.stock>0||a.inStock),class:"add-to-cart-btn"},t[9]||(t[9]=[c("i",{class:"fas fa-shopping-cart"},null,-1)]),8,J0),a.productId?(w(),S("button",{key:0,onClick:Ir(l=>o.removeFromWishlist(a.id),["stop"]),class:"add-to-wishlist-btn"},t[10]||(t[10]=[c("span",{class:"availability-icon"},"✖",-1)]),8,X0)):(w(),S("button",{key:1,onClick:Ir(l=>o.addToWishlist(a.id),["stop"]),class:"add-to-wishlist-btn"},t[11]||(t[11]=[c("i",{class:"fas fa-heart"},null,-1)]),8,Y0))])):ee("",!0),r.sellerMode?(w(),S("button",{key:4,onClick:Ir(l=>e.$emit("edit-product",a),["stop"]),class:"seller-edit-btn",title:"Редагувати товар"},t[12]||(t[12]=[c("i",{class:"fas fa-edit"},null,-1),ve(" Редагувати ")]),8,Z0)):ee("",!0)])],10,L0))),128))],2)):ee("",!0),r.showPagination&&o.totalPages>1?(w(),S("div",Q0,[ce(i,{currentPage:o.displayCurrentPage,"total-items":o.displayTotalItems,"items-per-page":o.displayPageSize,"max-visible-pages":10,onPageChanged:o.onPageChange},null,8,["currentPage","total-items","items-per-page","onPageChanged"])])):ee("",!0)])}const Mr=et($0,[["render",eb],["__scopeId","data-v-a233efb3"]]),tb={props:{categories:{type:Array,default:()=>[]}}},rb={class:"categories-section"},sb={class:"container"},nb={key:0,class:"categories-grid"},ob={class:"category-icon"},ib=["src","alt"],ab=["href"],lb={key:1};function cb(e,t,r,s,n,o){return w(),S("section",rb,[c("div",sb,[r.categories?(w(),S("div",nb,[(w(!0),S(be,null,Ne(r.categories,i=>(w(),S("div",{key:i.id,class:"category-item"},[c("div",ob,[i.image?(w(),S("img",{key:0,src:i.image||"@assets/images/icons/placeholder-icon.svg",alt:i.name},null,8,ib)):ee("",!0)]),i.name?(w(),S("a",{key:0,href:`/catalog/${i.slug}`,class:"category-name"},j(i.name),9,ab)):ee("",!0)]))),128))])):(w(),S("div",lb,"Loading categories..."))])])}const ub=et(tb,[["render",cb],["__scopeId","data-v-483768f0"]]),db={name:"RandomProducts",components:{ProductGrid:Mr},props:{title:{type:String,default:"Випадкові товари"},categorySlug:{type:String,required:!0},count:{type:Number,default:6},status:{type:Number,default:1},gridColumns:{type:Number,default:3},emptyMessage:{type:String,default:"Рандомні товари поки що недоступні"}},computed:{fetchParams(){return{categorySlug:this.categorySlug,status:this.status,count:this.count,random:!0}}}},fb={class:"random-products-section"},pb={class:"container"},hb={class:"section-title"};function gb(e,t,r,s,n,o){const i=Ke("ProductGrid");return w(),S("section",fb,[c("div",pb,[c("h2",hb,j(r.title),1),ce(i,{"fetch-params":o.fetchParams,"grid-columns":r.gridColumns,"empty-message":r.emptyMessage,onProductAddedToCart:t[0]||(t[0]=a=>e.$emit("product-added-to-cart",a)),onProductAddedToWishlist:t[1]||(t[1]=a=>e.$emit("product-added-to-wishlist",a))},null,8,["fetch-params","grid-columns","empty-message"])])])}const Jd=et(db,[["render",gb],["__scopeId","data-v-2c641b51"]]),mb={class:"top-products-container"},vb={class:"section-header"},yb={class:"section-title"},bb={key:0,class:"section-controls"},_b=["value"],wb={key:0,class:"loading"},Eb={key:1,class:"error"},Sb={key:2,class:"empty"},Ab={key:3,class:"products-grid"},Cb=["onClick"],Pb={class:"rank-badge"},Tb={class:"product-image"},kb=["src","alt"],Rb={class:"product-info"},Ob={class:"product-name"},Ib={class:"product-category"},xb={class:"product-stats"},$b={class:"stat"},Fb={class:"stat-value sales"},Db={class:"stat"},Nb={class:"stat-value stock"},Mb={class:"product-price"},Lb={class:"price"},Vb={key:4,class:"view-all-container"},jb={__name:"TopProductsGrid",props:{title:{type:String,default:"Топ товари за продажами"},categorySlug:{type:String,default:null},limit:{type:Number,default:10},orderBy:{type:String,default:"Sales"},showControls:{type:Boolean,default:!1},showViewAll:{type:Boolean,default:!0},autoLoad:{type:Boolean,default:!0}},setup(e,{expose:t}){const r=e,s=_n(),n=oe([]),o=oe([]),i=oe(!1),a=oe(null),l=oe(r.categorySlug||""),u=oe(r.orderBy),d=oe(r.limit),f=le(()=>{const m=new URLSearchParams;return l.value&&m.append("category",l.value),m.append("sortBy",u.value),`/products/top?${m.toString()}`}),h=async()=>{try{i.value=!0,a.value=null;const m=new URLSearchParams;l.value&&m.append("categorySlug",l.value),m.append("limit",d.value.toString()),m.append("orderBy",u.value),m.append("descending","true");const C=await fetch(`/api/products/top?${m.toString()}`);if(!C.ok)throw new Error(`HTTP error! status: ${C.status}`);const $=await C.json();$.success&&$.data?n.value=$.data:n.value=$}catch(m){console.error("Error loading top products:",m),a.value="Помилка завантаження топ товарів"}finally{i.value=!1}},g=async()=>{try{const C=await(await fetch("/api/categories")).json();C.success&&C.data?o.value=C.data:o.value=C}catch(m){console.error("Error loading categories:",m)}},y=m=>{s.push(`/products/${m.slug}`)},E=m=>{m.target.src="/placeholder-product.jpg"},A=m=>new Intl.NumberFormat("uk-UA").format(m);return Et(()=>r.categorySlug,m=>{l.value=m||"",r.autoLoad&&h()}),ir(async()=>{r.showControls&&await g(),r.autoLoad&&await h()}),t({loadTopProducts:h,refresh:h}),(m,C)=>{const $=Ke("router-link");return w(),S("div",mb,[c("div",vb,[c("h2",yb,j(e.title),1),e.showControls?(w(),S("div",bb,[qe(c("select",{"onUpdate:modelValue":C[0]||(C[0]=I=>l.value=I),onChange:h,class:"category-select"},[C[3]||(C[3]=c("option",{value:""},"Всі категорії",-1)),(w(!0),S(be,null,Ne(o.value,I=>(w(),S("option",{key:I.slug,value:I.slug},j(I.name),9,_b))),128))],544),[[hr,l.value]]),qe(c("select",{"onUpdate:modelValue":C[1]||(C[1]=I=>u.value=I),onChange:h,class:"sort-select"},C[4]||(C[4]=[c("option",{value:"Sales"},"За продажами",-1),c("option",{value:"Stock"},"За наявністю",-1),c("option",{value:"PriceAmount"},"За ціною",-1)]),544),[[hr,u.value]]),qe(c("select",{"onUpdate:modelValue":C[2]||(C[2]=I=>d.value=I),onChange:h,class:"limit-select"},C[5]||(C[5]=[c("option",{value:"5"},"5 товарів",-1),c("option",{value:"10"},"10 товарів",-1),c("option",{value:"15"},"15 товарів",-1),c("option",{value:"20"},"20 товарів",-1)]),544),[[hr,d.value]])])):ee("",!0)]),i.value?(w(),S("div",wb,C[6]||(C[6]=[c("div",{class:"spinner"},null,-1),c("p",null,"Завантаження топ товарів...",-1)]))):a.value?(w(),S("div",Eb,[c("p",null,j(a.value),1),c("button",{onClick:h,class:"retry-btn"},"Спробувати знову")])):n.value.length===0?(w(),S("div",Sb,C[7]||(C[7]=[c("p",null,"Товари не знайдено",-1)]))):(w(),S("div",Ab,[(w(!0),S(be,null,Ne(n.value,(I,R)=>(w(),S("div",{key:I.id,class:"product-card",onClick:M=>y(I)},[c("div",Pb," #"+j(R+1),1),c("div",Tb,[c("img",{src:I.imageUrl||"/placeholder-product.jpg",alt:I.name,onError:E},null,40,kb)]),c("div",Rb,[c("h3",Ob,j(I.name),1),c("p",Ib,j(I.categoryName),1),c("div",xb,[c("div",$b,[C[8]||(C[8]=c("span",{class:"stat-label"},"Продано:",-1)),c("span",Fb,j(I.sales),1)]),c("div",Db,[C[9]||(C[9]=c("span",{class:"stat-label"},"В наявності:",-1)),c("span",Nb,j(I.stock),1)])]),c("div",Mb,[c("span",Lb,j(A(I.priceAmount))+" "+j(I.priceCurrency),1)])])],8,Cb))),128))])),e.showViewAll&&!e.showControls?(w(),S("div",Vb,[ce($,{to:f.value,class:"view-all-btn"},{default:ot(()=>C[10]||(C[10]=[ve(" Переглянути всі топ товари ")])),_:1},8,["to"])])):ee("",!0)])}}},Ub=et(jb,[["__scopeId","data-v-7877eea6"]]),Bb="/assets/spring-banner-WHY_UVRU.jpg",Gb={components:{ProductGrid:Mr,HomeCategories:ub,RandomProducts:Jd,TopProductsGrid:Ub},data(){return{categories:[],error:null}},async mounted(){await this.fetchCategories()},methods:{async fetchCategories(e={pageSize:18}){try{const t=await Mt.getAllRootCategories(e);console.log(t),this.categories=t.data.data,this.error=null}catch(t){this.error="Failed to load categories. Please try again.",console.error(t)}}}},qb={class:"home-page"},Hb={class:"categories-section"},zb={class:"container"},Kb={class:"featured-section"},Wb={class:"container"},Jb={class:"appliances-section"},Xb={class:"container"},Yb={class:"recommended-section"},Zb={class:"container"};function Qb(e,t,r,s,n,o){const i=Ke("HomeCategories"),a=Ke("ProductGrid");return w(),S("div",qb,[t[4]||(t[4]=Nr('<div class="hero-banner" data-v-00c96916><div class="banner-container" data-v-00c96916><img src="'+Bb+'" alt="Spring Banner" class="banner-image" data-v-00c96916><div class="banner-text" data-v-00c96916><h2 data-v-00c96916>ЗУСТРІЧАЙ ВЕШНЮ ПРАВИЛЬНО</h2></div><button class="banner-nav-btn banner-next" data-v-00c96916><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-00c96916><path d="M9 18l6-6-6-6" data-v-00c96916></path></svg></button></div></div>',1)),c("section",Hb,[c("div",zb,[t[0]||(t[0]=c("h2",{class:"section-title"},"Розділи на сервісі",-1)),ce(i,{categories:n.categories},null,8,["categories"])])]),c("section",Kb,[c("div",Wb,[t[1]||(t[1]=c("h2",{class:"section-title"},"ТОП товари",-1)),ce(a,{"fetch-params":{type:"top",status:1,pageSize:4},"grid-columns":4,"empty-message":"Топ товари поки що недоступні"})])]),c("section",Jb,[c("div",Xb,[t[2]||(t[2]=c("div",{class:"section-header"},[c("h2",{class:"section-title"},"Побутова техніка"),c("a",{href:"/catalog/category-460bfd9b",class:"view-all"},"Більше товарів з категорії")],-1)),ce(a,{"fetch-params":{categorySlug:"category-460bfd9b",status:1,pageSize:4},"grid-columns":4,"empty-message":"Побутова техніка поки що недоступна"})])]),c("section",Yb,[c("div",Zb,[t[3]||(t[3]=c("h2",{class:"section-title"},"Рекомендації на основі ваших переглядів",-1)),ce(a,{"fetch-params":{type:"recommended",status:1,pageSize:4},"grid-columns":4,"empty-message":"Рекомендовані товари поки що недоступні"})])])])}const e_=et(Gb,[["render",Qb],["__scopeId","data-v-00c96916"]]);/**
  * vee-validate v4.15.0
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */function ht(e){return typeof e=="function"}function Xd(e){return e==null}const ns=e=>e!==null&&!!e&&typeof e=="object"&&!Array.isArray(e);function Ea(e){return Number(e)>=0}function t_(e){const t=parseFloat(e);return isNaN(t)?e:t}function r_(e){return typeof e=="object"&&e!==null}function s_(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}function sc(e){if(!r_(e)||s_(e)!=="[object Object]")return!1;if(Object.getPrototypeOf(e)===null)return!0;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function fn(e,t){return Object.keys(t).forEach(r=>{if(sc(t[r])&&sc(e[r])){e[r]||(e[r]={}),fn(e[r],t[r]);return}e[r]=t[r]}),e}function zs(e){const t=e.split(".");if(!t.length)return"";let r=String(t[0]);for(let s=1;s<t.length;s++){if(Ea(t[s])){r+=`[${t[s]}]`;continue}r+=`.${t[s]}`}return r}const n_={};function o_(e){return n_[e]}function nc(e,t,r){typeof r.value=="object"&&(r.value=Le(r.value)),!r.enumerable||r.get||r.set||!r.configurable||!r.writable||t==="__proto__"?Object.defineProperty(e,t,r):e[t]=r.value}function Le(e){if(typeof e!="object")return e;var t=0,r,s,n,o=Object.prototype.toString.call(e);if(o==="[object Object]"?n=Object.create(e.__proto__||null):o==="[object Array]"?n=Array(e.length):o==="[object Set]"?(n=new Set,e.forEach(function(i){n.add(Le(i))})):o==="[object Map]"?(n=new Map,e.forEach(function(i,a){n.set(Le(a),Le(i))})):o==="[object Date]"?n=new Date(+e):o==="[object RegExp]"?n=new RegExp(e.source,e.flags):o==="[object DataView]"?n=new e.constructor(Le(e.buffer)):o==="[object ArrayBuffer]"?n=e.slice(0):o.slice(-6)==="Array]"&&(n=new e.constructor(e)),n){for(s=Object.getOwnPropertySymbols(e);t<s.length;t++)nc(n,s[t],Object.getOwnPropertyDescriptor(e,s[t]));for(t=0,s=Object.getOwnPropertyNames(e);t<s.length;t++)Object.hasOwnProperty.call(n,r=s[t])&&n[r]===e[r]||nc(n,r,Object.getOwnPropertyDescriptor(e,r))}return n||e}const Fo=Symbol("vee-validate-form"),i_=Symbol("vee-validate-form-context"),a_=Symbol("vee-validate-field-instance"),io=Symbol("Default empty value"),l_=typeof window<"u";function Ni(e){return ht(e)&&!!e.__locatorRef}function Gt(e){return!!e&&ht(e.parse)&&e.__type==="VVTypedSchema"}function ao(e){return!!e&&ht(e.validate)}function Sn(e){return e==="checkbox"||e==="radio"}function c_(e){return ns(e)||Array.isArray(e)}function u_(e){return Array.isArray(e)?e.length===0:ns(e)&&Object.keys(e).length===0}function Do(e){return/^\[.+\]$/i.test(e)}function d_(e){return Yd(e)&&e.multiple}function Yd(e){return e.tagName==="SELECT"}function f_(e,t){const r=![!1,null,void 0,0].includes(t.multiple)&&!Number.isNaN(t.multiple);return e==="select"&&"multiple"in t&&r}function p_(e,t){return!f_(e,t)&&t.type!=="file"&&!Sn(t.type)}function Zd(e){return Sa(e)&&e.target&&"submit"in e.target}function Sa(e){return e?!!(typeof Event<"u"&&ht(Event)&&e instanceof Event||e&&e.srcElement):!1}function oc(e,t){return t in e&&e[t]!==io}function St(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var r,s,n;if(Array.isArray(e)){if(r=e.length,r!=t.length)return!1;for(s=r;s--!==0;)if(!St(e[s],t[s]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(s of e.entries())if(!t.has(s[0]))return!1;for(s of e.entries())if(!St(s[1],t.get(s[0])))return!1;return!0}if(ac(e)&&ac(t))return!(e.size!==t.size||e.name!==t.name||e.lastModified!==t.lastModified||e.type!==t.type);if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(s of e.entries())if(!t.has(s[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(r=e.length,r!=t.length)return!1;for(s=r;s--!==0;)if(e[s]!==t[s])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(n=Object.keys(e),r=n.length-ic(e,n),r!==Object.keys(t).length-ic(t,Object.keys(t)))return!1;for(s=r;s--!==0;)if(!Object.prototype.hasOwnProperty.call(t,n[s]))return!1;for(s=r;s--!==0;){var o=n[s];if(!St(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function ic(e,t){let r=0;for(let n=t.length;n--!==0;){var s=t[n];e[s]===void 0&&r++}return r}function ac(e){return l_?e instanceof File:!1}function Aa(e){return Do(e)?e.replace(/\[|\]/gi,""):e}function Pt(e,t,r){return e?Do(t)?e[Aa(t)]:(t||"").split(/\.|\[(\d+)\]/).filter(Boolean).reduce((n,o)=>c_(n)&&o in n?n[o]:r,e):r}function fr(e,t,r){if(Do(t)){e[Aa(t)]=r;return}const s=t.split(/\.|\[(\d+)\]/).filter(Boolean);let n=e;for(let o=0;o<s.length;o++){if(o===s.length-1){n[s[o]]=r;return}(!(s[o]in n)||Xd(n[s[o]]))&&(n[s[o]]=Ea(s[o+1])?[]:{}),n=n[s[o]]}}function ri(e,t){if(Array.isArray(e)&&Ea(t)){e.splice(Number(t),1);return}ns(e)&&delete e[t]}function lc(e,t){if(Do(t)){delete e[Aa(t)];return}const r=t.split(/\.|\[(\d+)\]/).filter(Boolean);let s=e;for(let o=0;o<r.length;o++){if(o===r.length-1){ri(s,r[o]);break}if(!(r[o]in s)||Xd(s[r[o]]))break;s=s[r[o]]}const n=r.map((o,i)=>Pt(e,r.slice(0,i).join(".")));for(let o=n.length-1;o>=0;o--)if(u_(n[o])){if(o===0){ri(e,r[0]);continue}ri(n[o-1],r[o-1])}}function Dt(e){return Object.keys(e)}function Qd(e,t=void 0){const r=os();return(r==null?void 0:r.provides[e])||kt(e,t)}function cc(e,t,r){if(Array.isArray(e)){const s=[...e],n=s.findIndex(o=>St(o,t));return n>=0?s.splice(n,1):s.push(t),s}return St(e,t)?r:t}function uc(e,t=0){let r=null,s=[];return function(...n){return r&&clearTimeout(r),r=setTimeout(()=>{const o=e(...n);s.forEach(i=>i(o)),s=[]},t),new Promise(o=>s.push(o))}}function h_(e,t){return ns(t)&&t.number?t_(e):e}function Mi(e,t){let r;return async function(...n){const o=e(...n);r=o;const i=await o;return o!==r?i:(r=void 0,t(i,n))}}function Li(e){return Array.isArray(e)?e:e?[e]:[]}function On(e,t){const r={};for(const s in e)t.includes(s)||(r[s]=e[s]);return r}function g_(e){let t=null,r=[];return function(...s){const n=Ft(()=>{if(t!==n)return;const o=e(...s);r.forEach(i=>i(o)),r=[],t=null});return t=n,new Promise(o=>r.push(o))}}function Ca(e,t,r){return t.slots.default?typeof e=="string"||!e?t.slots.default(r()):{default:()=>{var s,n;return(n=(s=t.slots).default)===null||n===void 0?void 0:n.call(s,r())}}:t.slots.default}function si(e){if(ef(e))return e._value}function ef(e){return"_value"in e}function m_(e){return e.type==="number"||e.type==="range"?Number.isNaN(e.valueAsNumber)?e.value:e.valueAsNumber:e.value}function lo(e){if(!Sa(e))return e;const t=e.target;if(Sn(t.type)&&ef(t))return si(t);if(t.type==="file"&&t.files){const r=Array.from(t.files);return t.multiple?r:r[0]}if(d_(t))return Array.from(t.options).filter(r=>r.selected&&!r.disabled).map(si);if(Yd(t)){const r=Array.from(t.options).find(s=>s.selected);return r?si(r):t.value}return m_(t)}function tf(e){const t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?ns(e)&&e._$$isNormalized?e:ns(e)?Object.keys(e).reduce((r,s)=>{const n=v_(e[s]);return e[s]!==!1&&(r[s]=dc(n)),r},t):typeof e!="string"?t:e.split("|").reduce((r,s)=>{const n=y_(s);return n.name&&(r[n.name]=dc(n.params)),r},t):t}function v_(e){return e===!0?[]:Array.isArray(e)||ns(e)?e:[e]}function dc(e){const t=r=>typeof r=="string"&&r[0]==="@"?b_(r.slice(1)):r;return Array.isArray(e)?e.map(t):e instanceof RegExp?[e]:Object.keys(e).reduce((r,s)=>(r[s]=t(e[s]),r),{})}const y_=e=>{let t=[];const r=e.split(":")[0];return e.includes(":")&&(t=e.split(":").slice(1).join(":").split(",")),{name:r,params:t}};function b_(e){const t=r=>{var s;return(s=Pt(r,e))!==null&&s!==void 0?s:r[e]};return t.__locatorRef=e,t}function __(e){return Array.isArray(e)?e.filter(Ni):Dt(e).filter(t=>Ni(e[t])).map(t=>e[t])}const w_={generateMessage:({field:e})=>`${e} is not valid.`,bails:!0,validateOnBlur:!0,validateOnChange:!0,validateOnInput:!1,validateOnModelUpdate:!0};let E_=Object.assign({},w_);const Xr=()=>E_;async function rf(e,t,r={}){const s=r==null?void 0:r.bails,n={name:(r==null?void 0:r.name)||"{field}",rules:t,label:r==null?void 0:r.label,bails:s??!0,formData:(r==null?void 0:r.values)||{}},o=await S_(n,e);return Object.assign(Object.assign({},o),{valid:!o.errors.length})}async function S_(e,t){const r=e.rules;if(Gt(r)||ao(r))return C_(t,Object.assign(Object.assign({},e),{rules:r}));if(ht(r)||Array.isArray(r)){const a={field:e.label||e.name,name:e.name,label:e.label,form:e.formData,value:t},l=Array.isArray(r)?r:[r],u=l.length,d=[];for(let f=0;f<u;f++){const h=l[f],g=await h(t,a);if(!(typeof g!="string"&&!Array.isArray(g)&&g)){if(Array.isArray(g))d.push(...g);else{const E=typeof g=="string"?g:nf(a);d.push(E)}if(e.bails)return{errors:d}}}return{errors:d}}const s=Object.assign(Object.assign({},e),{rules:tf(r)}),n=[],o=Object.keys(s.rules),i=o.length;for(let a=0;a<i;a++){const l=o[a],u=await P_(s,t,{name:l,params:s.rules[l]});if(u.error&&(n.push(u.error),e.bails))return{errors:n}}return{errors:n}}function A_(e){return!!e&&e.name==="ValidationError"}function sf(e){return{__type:"VVTypedSchema",async parse(r,s){var n;try{return{output:await e.validate(r,{abortEarly:!1,context:(s==null?void 0:s.formData)||{}}),errors:[]}}catch(o){if(!A_(o))throw o;if(!(!((n=o.inner)===null||n===void 0)&&n.length)&&o.errors.length)return{errors:[{path:o.path,errors:o.errors}]};const i=o.inner.reduce((a,l)=>{const u=l.path||"";return a[u]||(a[u]={errors:[],path:u}),a[u].errors.push(...l.errors),a},{});return{errors:Object.values(i)}}}}}async function C_(e,t){const s=await(Gt(t.rules)?t.rules:sf(t.rules)).parse(e,{formData:t.formData}),n=[];for(const o of s.errors)o.errors.length&&n.push(...o.errors);return{value:s.value,errors:n}}async function P_(e,t,r){const s=o_(r.name);if(!s)throw new Error(`No such validator '${r.name}' exists.`);const n=T_(r.params,e.formData),o={field:e.label||e.name,name:e.name,label:e.label,value:t,form:e.formData,rule:Object.assign(Object.assign({},r),{params:n})},i=await s(t,n,o);return typeof i=="string"?{error:i}:{error:i?void 0:nf(o)}}function nf(e){const t=Xr().generateMessage;return t?t(e):"Field is invalid"}function T_(e,t){const r=s=>Ni(s)?s(t):s;return Array.isArray(e)?e.map(r):Object.keys(e).reduce((s,n)=>(s[n]=r(e[n]),s),{})}async function k_(e,t){const s=await(Gt(e)?e:sf(e)).parse(Le(t),{formData:Le(t)}),n={},o={};for(const i of s.errors){const a=i.errors,l=(i.path||"").replace(/\["(\d+)"\]/g,(u,d)=>`[${d}]`);n[l]={valid:!a.length,errors:a},a.length&&(o[l]=a[0])}return{valid:!s.errors.length,results:n,errors:o,values:s.value,source:"schema"}}async function R_(e,t,r){const n=Dt(e).map(async u=>{var d,f,h;const g=(d=r==null?void 0:r.names)===null||d===void 0?void 0:d[u],y=await rf(Pt(t,u),e[u],{name:(g==null?void 0:g.name)||u,label:g==null?void 0:g.label,values:t,bails:(h=(f=r==null?void 0:r.bailsMap)===null||f===void 0?void 0:f[u])!==null&&h!==void 0?h:!0});return Object.assign(Object.assign({},y),{path:u})});let o=!0;const i=await Promise.all(n),a={},l={};for(const u of i)a[u.path]={valid:u.valid,errors:u.errors},u.valid||(o=!1,l[u.path]=u.errors[0]);return{valid:o,results:a,errors:l,source:"schema"}}let fc=0;function O_(e,t){const{value:r,initialValue:s,setInitialValue:n}=I_(e,t.modelValue,t.form);if(!t.form){let l=function(g){var y;"value"in g&&(r.value=g.value),"errors"in g&&d(g.errors),"touched"in g&&(h.touched=(y=g.touched)!==null&&y!==void 0?y:h.touched),"initialValue"in g&&n(g.initialValue)};const{errors:u,setErrors:d}=F_(),f=fc>=Number.MAX_SAFE_INTEGER?0:++fc,h=$_(r,s,u,t.schema);return{id:f,path:e,value:r,initialValue:s,meta:h,flags:{pendingUnmount:{[f]:!1},pendingReset:!1},errors:u,setState:l}}const o=t.form.createPathState(e,{bails:t.bails,label:t.label,type:t.type,validate:t.validate,schema:t.schema}),i=le(()=>o.errors);function a(l){var u,d,f;"value"in l&&(r.value=l.value),"errors"in l&&((u=t.form)===null||u===void 0||u.setFieldError(Ie(e),l.errors)),"touched"in l&&((d=t.form)===null||d===void 0||d.setFieldTouched(Ie(e),(f=l.touched)!==null&&f!==void 0?f:!1)),"initialValue"in l&&n(l.initialValue)}return{id:Array.isArray(o.id)?o.id[o.id.length-1]:o.id,path:e,value:r,errors:i,meta:o,initialValue:s,flags:o.__flags,setState:a}}function I_(e,t,r){const s=oe(Ie(t));function n(){return r?Pt(r.initialValues.value,Ie(e),Ie(s)):Ie(s)}function o(u){if(!r){s.value=u;return}r.setFieldInitialValue(Ie(e),u,!0)}const i=le(n);if(!r)return{value:oe(n()),initialValue:i,setInitialValue:o};const a=x_(t,r,i,e);return r.stageInitialValue(Ie(e),a,!0),{value:le({get(){return Pt(r.values,Ie(e))},set(u){r.setFieldValue(Ie(e),u,!1)}}),initialValue:i,setInitialValue:o}}function x_(e,t,r,s){return rt(e)?Ie(e):e!==void 0?e:Pt(t.values,Ie(s),Ie(r))}function $_(e,t,r,s){const n=le(()=>{var i,a,l;return(l=(a=(i=ge(s))===null||i===void 0?void 0:i.describe)===null||a===void 0?void 0:a.call(i).required)!==null&&l!==void 0?l:!1}),o=or({touched:!1,pending:!1,valid:!0,required:n,validated:!!Ie(r).length,initialValue:le(()=>Ie(t)),dirty:le(()=>!St(Ie(e),Ie(t)))});return Et(r,i=>{o.valid=!i.length},{immediate:!0,flush:"sync"}),o}function F_(){const e=oe([]);return{errors:e,setErrors:t=>{e.value=Li(t)}}}function pn(e,t,r){return Sn(r==null?void 0:r.type)?N_(e,t,r):of(e,t,r)}function of(e,t,r){const{initialValue:s,validateOnMount:n,bails:o,type:i,checkedValue:a,label:l,validateOnValueUpdate:u,uncheckedValue:d,controlled:f,keepValueOnUnmount:h,syncVModel:g,form:y}=D_(r),E=f?Qd(Fo):void 0,A=y||E,m=le(()=>zs(ge(e))),C=le(()=>{if(ge(A==null?void 0:A.schema))return;const O=Ie(t);return ao(O)||Gt(O)||ht(O)||Array.isArray(O)?O:tf(O)}),$=!ht(C.value)&&Gt(ge(t)),{id:I,value:R,initialValue:M,meta:L,setState:k,errors:x,flags:X}=O_(m,{modelValue:s,form:A,bails:o,label:l,type:i,validate:C.value?ye:void 0,schema:$?t:void 0}),q=le(()=>x.value[0]);g&&M_({value:R,prop:g,handleChange:se,shouldValidate:()=>u&&!X.pendingReset});const z=(ne,O=!1)=>{L.touched=!0,O&&Ee()};async function ue(ne){var O,J;if(A!=null&&A.validateSchema){const{results:H}=await A.validateSchema(ne);return(O=H[ge(m)])!==null&&O!==void 0?O:{valid:!0,errors:[]}}return C.value?rf(R.value,C.value,{name:ge(m),label:ge(l),values:(J=A==null?void 0:A.values)!==null&&J!==void 0?J:{},bails:o}):{valid:!0,errors:[]}}const Ee=Mi(async()=>(L.pending=!0,L.validated=!0,ue("validated-only")),ne=>(X.pendingUnmount[Ze.id]||(k({errors:ne.errors}),L.pending=!1,L.valid=ne.valid),ne)),Oe=Mi(async()=>ue("silent"),ne=>(L.valid=ne.valid,ne));function ye(ne){return(ne==null?void 0:ne.mode)==="silent"?Oe():Ee()}function se(ne,O=!0){const J=lo(ne);ct(J,O)}ir(()=>{if(n)return Ee();(!A||!A.validateSchema)&&Oe()});function Te(ne){L.touched=ne}function st(ne){var O;const J=ne&&"value"in ne?ne.value:M.value;k({value:Le(J),initialValue:Le(J),touched:(O=ne==null?void 0:ne.touched)!==null&&O!==void 0?O:!1,errors:(ne==null?void 0:ne.errors)||[]}),L.pending=!1,L.validated=!1,Oe()}const lt=os();function ct(ne,O=!0){R.value=lt&&g?h_(ne,lt.props.modelModifiers):ne,(O?Ee:Oe)()}function nt(ne){k({errors:Array.isArray(ne)?ne:[ne]})}const jt=le({get(){return R.value},set(ne){ct(ne,u)}}),Ze={id:I,name:m,label:l,value:jt,meta:L,errors:x,errorMessage:q,type:i,checkedValue:a,uncheckedValue:d,bails:o,keepValueOnUnmount:h,resetField:st,handleReset:()=>st(),validate:ye,handleChange:se,handleBlur:z,setState:k,setTouched:Te,setErrors:nt,setValue:ct};if($r(a_,Ze),rt(t)&&typeof Ie(t)!="function"&&Et(t,(ne,O)=>{St(ne,O)||(L.validated?Ee():Oe())},{deep:!0}),!A)return Ze;const Zt=le(()=>{const ne=C.value;return!ne||ht(ne)||ao(ne)||Gt(ne)||Array.isArray(ne)?{}:Object.keys(ne).reduce((O,J)=>{const H=__(ne[J]).map(ie=>ie.__locatorRef).reduce((ie,ke)=>{const v=Pt(A.values,ke)||A.values[ke];return v!==void 0&&(ie[ke]=v),ie},{});return Object.assign(O,H),O},{})});return Et(Zt,(ne,O)=>{if(!Object.keys(ne).length)return;!St(ne,O)&&(L.validated?Ee():Oe())}),ia(()=>{var ne;const O=(ne=ge(Ze.keepValueOnUnmount))!==null&&ne!==void 0?ne:ge(A.keepValuesOnUnmount),J=ge(m);if(O||!A||X.pendingUnmount[Ze.id]){A==null||A.removePathState(J,I);return}X.pendingUnmount[Ze.id]=!0;const H=A.getPathState(J);if(Array.isArray(H==null?void 0:H.id)&&(H!=null&&H.multiple)?H!=null&&H.id.includes(Ze.id):(H==null?void 0:H.id)===Ze.id){if(H!=null&&H.multiple&&Array.isArray(H.value)){const ke=H.value.findIndex(v=>St(v,ge(Ze.checkedValue)));if(ke>-1){const v=[...H.value];v.splice(ke,1),A.setFieldValue(J,v)}Array.isArray(H.id)&&H.id.splice(H.id.indexOf(Ze.id),1)}else A.unsetPathValue(ge(m));A.removePathState(J,I)}}),Ze}function D_(e){const t=()=>({initialValue:void 0,validateOnMount:!1,bails:!0,label:void 0,validateOnValueUpdate:!0,keepValueOnUnmount:void 0,syncVModel:!1,controlled:!0}),r=!!(e!=null&&e.syncVModel),s=typeof(e==null?void 0:e.syncVModel)=="string"?e.syncVModel:(e==null?void 0:e.modelPropName)||"modelValue",n=r&&!("initialValue"in(e||{}))?Vi(os(),s):e==null?void 0:e.initialValue;if(!e)return Object.assign(Object.assign({},t()),{initialValue:n});const o="valueProp"in e?e.valueProp:e.checkedValue,i="standalone"in e?!e.standalone:e.controlled,a=(e==null?void 0:e.modelPropName)||(e==null?void 0:e.syncVModel)||!1;return Object.assign(Object.assign(Object.assign({},t()),e||{}),{initialValue:n,controlled:i??!0,checkedValue:o,syncVModel:a})}function N_(e,t,r){const s=r!=null&&r.standalone?void 0:Qd(Fo),n=r==null?void 0:r.checkedValue,o=r==null?void 0:r.uncheckedValue;function i(a){const l=a.handleChange,u=le(()=>{const f=ge(a.value),h=ge(n);return Array.isArray(f)?f.findIndex(g=>St(g,h))>=0:St(h,f)});function d(f,h=!0){var g,y;if(u.value===((g=f==null?void 0:f.target)===null||g===void 0?void 0:g.checked)){h&&a.validate();return}const E=ge(e),A=s==null?void 0:s.getPathState(E),m=lo(f);let C=(y=ge(n))!==null&&y!==void 0?y:m;s&&(A!=null&&A.multiple)&&A.type==="checkbox"?C=cc(Pt(s.values,E)||[],C,void 0):(r==null?void 0:r.type)==="checkbox"&&(C=cc(ge(a.value),C,ge(o))),l(C,h)}return Object.assign(Object.assign({},a),{checked:u,checkedValue:n,uncheckedValue:o,handleChange:d})}return i(of(e,t,r))}function M_({prop:e,value:t,handleChange:r,shouldValidate:s}){const n=os();if(!n||!e)return;const o=typeof e=="string"?e:"modelValue",i=`update:${o}`;o in n.props&&(Et(t,a=>{St(a,Vi(n,o))||n.emit(i,a)}),Et(()=>Vi(n,o),a=>{if(a===io&&t.value===void 0)return;const l=a===io?void 0:a;St(l,t.value)||r(l,s())}))}function Vi(e,t){if(e)return e.props[t]}const L_=Ns({name:"Field",inheritAttrs:!1,props:{as:{type:[String,Object],default:void 0},name:{type:String,required:!0},rules:{type:[Object,String,Function],default:void 0},validateOnMount:{type:Boolean,default:!1},validateOnBlur:{type:Boolean,default:void 0},validateOnChange:{type:Boolean,default:void 0},validateOnInput:{type:Boolean,default:void 0},validateOnModelUpdate:{type:Boolean,default:void 0},bails:{type:Boolean,default:()=>Xr().bails},label:{type:String,default:void 0},uncheckedValue:{type:null,default:void 0},modelValue:{type:null,default:io},modelModifiers:{type:null,default:()=>({})},"onUpdate:modelValue":{type:null,default:void 0},standalone:{type:Boolean,default:!1},keepValue:{type:Boolean,default:void 0}},setup(e,t){const r=Kr(e,"rules"),s=Kr(e,"name"),n=Kr(e,"label"),o=Kr(e,"uncheckedValue"),i=Kr(e,"keepValue"),{errors:a,value:l,errorMessage:u,validate:d,handleChange:f,handleBlur:h,setTouched:g,resetField:y,handleReset:E,meta:A,checked:m,setErrors:C,setValue:$}=pn(s,r,{validateOnMount:e.validateOnMount,bails:e.bails,standalone:e.standalone,type:t.attrs.type,initialValue:j_(e,t),checkedValue:t.attrs.value,uncheckedValue:o,label:n,validateOnValueUpdate:e.validateOnModelUpdate,keepValueOnUnmount:i,syncVModel:!0}),I=function(X,q=!0){f(X,q)},R=le(()=>{const{validateOnInput:x,validateOnChange:X,validateOnBlur:q,validateOnModelUpdate:z}=V_(e);function ue(se){h(se,q),ht(t.attrs.onBlur)&&t.attrs.onBlur(se)}function Ee(se){I(se,x),ht(t.attrs.onInput)&&t.attrs.onInput(se)}function Oe(se){I(se,X),ht(t.attrs.onChange)&&t.attrs.onChange(se)}const ye={name:e.name,onBlur:ue,onInput:Ee,onChange:Oe};return ye["onUpdate:modelValue"]=se=>I(se,z),ye}),M=le(()=>{const x=Object.assign({},R.value);Sn(t.attrs.type)&&m&&(x.checked=m.value);const X=pc(e,t);return p_(X,t.attrs)&&(x.value=l.value),x}),L=le(()=>Object.assign(Object.assign({},R.value),{modelValue:l.value}));function k(){return{field:M.value,componentField:L.value,value:l.value,meta:A,errors:a.value,errorMessage:u.value,validate:d,resetField:y,handleChange:I,handleInput:x=>I(x,!1),handleReset:E,handleBlur:R.value.onBlur,setTouched:g,setErrors:C,setValue:$}}return t.expose({value:l,meta:A,errors:a,errorMessage:u,setErrors:C,setTouched:g,setValue:$,reset:y,validate:d,handleChange:f}),()=>{const x=_o(pc(e,t)),X=Ca(x,t,k);return x?rs(x,Object.assign(Object.assign({},t.attrs),M.value),X):X}}});function pc(e,t){let r=e.as||"";return!e.as&&!t.slots.default&&(r="input"),r}function V_(e){var t,r,s,n;const{validateOnInput:o,validateOnChange:i,validateOnBlur:a,validateOnModelUpdate:l}=Xr();return{validateOnInput:(t=e.validateOnInput)!==null&&t!==void 0?t:o,validateOnChange:(r=e.validateOnChange)!==null&&r!==void 0?r:i,validateOnBlur:(s=e.validateOnBlur)!==null&&s!==void 0?s:a,validateOnModelUpdate:(n=e.validateOnModelUpdate)!==null&&n!==void 0?n:l}}function j_(e,t){return Sn(t.attrs.type)?oc(e,"modelValue")?e.modelValue:void 0:oc(e,"modelValue")?e.modelValue:t.attrs.value}const af=L_;let U_=0;const In=["bails","fieldsCount","id","multiple","type","validate"];function lf(e){const t=(e==null?void 0:e.initialValues)||{},r=Object.assign({},ge(t)),s=Ie(e==null?void 0:e.validationSchema);return s&&Gt(s)&&ht(s.cast)?Le(s.cast(r)||{}):Le(r)}function B_(e){var t;const r=U_++,s=(e==null?void 0:e.name)||"Form";let n=0;const o=oe(!1),i=oe(!1),a=oe(0),l=[],u=or(lf(e)),d=oe([]),f=oe({}),h=oe({}),g=g_(()=>{h.value=d.value.reduce((p,b)=>(p[zs(ge(b.path))]=b,p),{})});function y(p,b){const P=se(p);if(!P){typeof p=="string"&&(f.value[zs(p)]=Li(b));return}if(typeof p=="string"){const G=zs(p);f.value[G]&&delete f.value[G]}P.errors=Li(b),P.valid=!P.errors.length}function E(p){Dt(p).forEach(b=>{y(b,p[b])})}e!=null&&e.initialErrors&&E(e.initialErrors);const A=le(()=>{const p=d.value.reduce((b,P)=>(P.errors.length&&(b[ge(P.path)]=P.errors),b),{});return Object.assign(Object.assign({},f.value),p)}),m=le(()=>Dt(A.value).reduce((p,b)=>{const P=A.value[b];return P!=null&&P.length&&(p[b]=P[0]),p},{})),C=le(()=>d.value.reduce((p,b)=>(p[ge(b.path)]={name:ge(b.path)||"",label:b.label||""},p),{})),$=le(()=>d.value.reduce((p,b)=>{var P;return p[ge(b.path)]=(P=b.bails)!==null&&P!==void 0?P:!0,p},{})),I=Object.assign({},(e==null?void 0:e.initialErrors)||{}),R=(t=e==null?void 0:e.keepValuesOnUnmount)!==null&&t!==void 0?t:!1,{initialValues:M,originalInitialValues:L,setInitialValues:k}=q_(d,u,e),x=G_(d,u,L,m),X=le(()=>d.value.reduce((p,b)=>{const P=Pt(u,ge(b.path));return fr(p,ge(b.path),P),p},{})),q=e==null?void 0:e.validationSchema;function z(p,b){var P,G;const K=le(()=>Pt(M.value,ge(p))),Q=h.value[ge(p)],ae=(b==null?void 0:b.type)==="checkbox"||(b==null?void 0:b.type)==="radio";if(Q&&ae){Q.multiple=!0;const Ye=n++;return Array.isArray(Q.id)?Q.id.push(Ye):Q.id=[Q.id,Ye],Q.fieldsCount++,Q.__flags.pendingUnmount[Ye]=!1,Q}const xe=le(()=>Pt(u,ge(p))),Ge=ge(p),Xe=st.findIndex(Ye=>Ye===Ge);Xe!==-1&&st.splice(Xe,1);const Me=le(()=>{var Ye,xt,Ur,ar;const ls=ge(q);if(Gt(ls))return(xt=(Ye=ls.describe)===null||Ye===void 0?void 0:Ye.call(ls,ge(p)).required)!==null&&xt!==void 0?xt:!1;const cs=ge(b==null?void 0:b.schema);return Gt(cs)&&(ar=(Ur=cs.describe)===null||Ur===void 0?void 0:Ur.call(cs).required)!==null&&ar!==void 0?ar:!1}),tt=n++,De=or({id:tt,path:p,touched:!1,pending:!1,valid:!0,validated:!!(!((P=I[Ge])===null||P===void 0)&&P.length),required:Me,initialValue:K,errors:Jc([]),bails:(G=b==null?void 0:b.bails)!==null&&G!==void 0?G:!1,label:b==null?void 0:b.label,type:(b==null?void 0:b.type)||"default",value:xe,multiple:!1,__flags:{pendingUnmount:{[tt]:!1},pendingReset:!1},fieldsCount:1,validate:b==null?void 0:b.validate,dirty:le(()=>!St(Ie(xe),Ie(K)))});return d.value.push(De),h.value[Ge]=De,g(),m.value[Ge]&&!I[Ge]&&Ft(()=>{Z(Ge,{mode:"silent"})}),rt(p)&&Et(p,Ye=>{g();const xt=Le(xe.value);h.value[Ye]=De,Ft(()=>{fr(u,Ye,xt)})}),De}const ue=uc(de,5),Ee=uc(de,5),Oe=Mi(async p=>await(p==="silent"?ue():Ee()),(p,[b])=>{const P=Dt(O.errorBag.value),K=[...new Set([...Dt(p.results),...d.value.map(Q=>Q.path),...P])].sort().reduce((Q,ae)=>{var xe;const Ge=ae,Xe=se(Ge)||Te(Ge),Me=((xe=p.results[Ge])===null||xe===void 0?void 0:xe.errors)||[],tt=ge(Xe==null?void 0:Xe.path)||Ge,De=H_({errors:Me,valid:!Me.length},Q.results[tt]);return Q.results[tt]=De,De.valid||(Q.errors[tt]=De.errors[0]),Xe&&f.value[tt]&&delete f.value[tt],Xe?(Xe.valid=De.valid,b==="silent"||b==="validated-only"&&!Xe.validated||y(Xe,De.errors),Q):(y(tt,Me),Q)},{valid:p.valid,results:{},errors:{},source:p.source});return p.values&&(K.values=p.values,K.source=p.source),Dt(K.results).forEach(Q=>{var ae;const xe=se(Q);xe&&b!=="silent"&&(b==="validated-only"&&!xe.validated||y(xe,(ae=K.results[Q])===null||ae===void 0?void 0:ae.errors))}),K});function ye(p){d.value.forEach(p)}function se(p){const b=typeof p=="string"?zs(p):p;return typeof b=="string"?h.value[b]:b}function Te(p){return d.value.filter(P=>p.startsWith(ge(P.path))).reduce((P,G)=>P?G.path.length>P.path.length?G:P:G,void 0)}let st=[],lt;function ct(p){return st.push(p),lt||(lt=Ft(()=>{[...st].sort().reverse().forEach(P=>{lc(u,P)}),st=[],lt=null})),lt}function nt(p){return function(P,G){return function(Q){return Q instanceof Event&&(Q.preventDefault(),Q.stopPropagation()),ye(ae=>ae.touched=!0),o.value=!0,a.value++,Y().then(ae=>{const xe=Le(u);if(ae.valid&&typeof P=="function"){const Ge=Le(X.value);let Xe=p?Ge:xe;return ae.values&&(Xe=ae.source==="schema"?ae.values:Object.assign({},Xe,ae.values)),P(Xe,{evt:Q,controlledValues:Ge,setErrors:E,setFieldError:y,setTouched:B,setFieldTouched:v,setValues:ie,setFieldValue:J,resetForm:te,resetField:U})}!ae.valid&&typeof G=="function"&&G({values:xe,evt:Q,errors:ae.errors,results:ae.results})}).then(ae=>(o.value=!1,ae),ae=>{throw o.value=!1,ae})}}}const Ze=nt(!1);Ze.withControlled=nt(!0);function Zt(p,b){const P=d.value.findIndex(K=>K.path===p&&(Array.isArray(K.id)?K.id.includes(b):K.id===b)),G=d.value[P];if(!(P===-1||!G)){if(Ft(()=>{Z(p,{mode:"silent",warn:!1})}),G.multiple&&G.fieldsCount&&G.fieldsCount--,Array.isArray(G.id)){const K=G.id.indexOf(b);K>=0&&G.id.splice(K,1),delete G.__flags.pendingUnmount[b]}(!G.multiple||G.fieldsCount<=0)&&(d.value.splice(P,1),W(p),g(),delete h.value[p])}}function ne(p){Dt(h.value).forEach(b=>{b.startsWith(p)&&delete h.value[b]}),d.value=d.value.filter(b=>!b.path.startsWith(p)),Ft(()=>{g()})}const O={name:s,formId:r,values:u,controlledValues:X,errorBag:A,errors:m,schema:q,submitCount:a,meta:x,isSubmitting:o,isValidating:i,fieldArrays:l,keepValuesOnUnmount:R,validateSchema:Ie(q)?Oe:void 0,validate:Y,setFieldError:y,validateField:Z,setFieldValue:J,setValues:ie,setErrors:E,setFieldTouched:v,setTouched:B,resetForm:te,resetField:U,handleSubmit:Ze,useFieldModel:Ve,defineInputBinds:$e,defineComponentBinds:ft,defineField:Re,stageInitialValue:pe,unsetInitialValue:W,setFieldInitialValue:re,createPathState:z,getPathState:se,unsetPathValue:ct,removePathState:Zt,initialValues:M,getAllPathStates:()=>d.value,destroyPath:ne,isFieldTouched:_,isFieldDirty:T,isFieldValid:V};function J(p,b,P=!0){const G=Le(b),K=typeof p=="string"?p:p.path;se(K)||z(K),fr(u,K,G),P&&Z(K)}function H(p,b=!0){Dt(u).forEach(P=>{delete u[P]}),Dt(p).forEach(P=>{J(P,p[P],!1)}),b&&Y()}function ie(p,b=!0){fn(u,p),l.forEach(P=>P&&P.reset()),b&&Y()}function ke(p,b){const P=se(ge(p))||z(p);return le({get(){return P.value},set(G){var K;const Q=ge(p);J(Q,G,(K=ge(b))!==null&&K!==void 0?K:!1)}})}function v(p,b){const P=se(p);P&&(P.touched=b)}function _(p){const b=se(p);return b?b.touched:d.value.filter(P=>P.path.startsWith(p)).some(P=>P.touched)}function T(p){const b=se(p);return b?b.dirty:d.value.filter(P=>P.path.startsWith(p)).some(P=>P.dirty)}function V(p){const b=se(p);return b?b.valid:d.value.filter(P=>P.path.startsWith(p)).every(P=>P.valid)}function B(p){if(typeof p=="boolean"){ye(b=>{b.touched=p});return}Dt(p).forEach(b=>{v(b,!!p[b])})}function U(p,b){var P;const G=b&&"value"in b?b.value:Pt(M.value,p),K=se(p);K&&(K.__flags.pendingReset=!0),re(p,Le(G),!0),J(p,G,!1),v(p,(P=b==null?void 0:b.touched)!==null&&P!==void 0?P:!1),y(p,(b==null?void 0:b.errors)||[]),Ft(()=>{K&&(K.__flags.pendingReset=!1)})}function te(p,b){let P=Le(p!=null&&p.values?p.values:L.value);P=b!=null&&b.force?P:fn(L.value,P),P=Gt(q)&&ht(q.cast)?q.cast(P):P,k(P,{force:b==null?void 0:b.force}),ye(G=>{var K;G.__flags.pendingReset=!0,G.validated=!1,G.touched=((K=p==null?void 0:p.touched)===null||K===void 0?void 0:K[ge(G.path)])||!1,J(ge(G.path),Pt(P,ge(G.path)),!1),y(ge(G.path),void 0)}),b!=null&&b.force?H(P,!1):ie(P,!1),E((p==null?void 0:p.errors)||{}),a.value=(p==null?void 0:p.submitCount)||0,Ft(()=>{Y({mode:"silent"}),ye(G=>{G.__flags.pendingReset=!1})})}async function Y(p){const b=(p==null?void 0:p.mode)||"force";if(b==="force"&&ye(ae=>ae.validated=!0),O.validateSchema)return O.validateSchema(b);i.value=!0;const P=await Promise.all(d.value.map(ae=>ae.validate?ae.validate(p).then(xe=>({key:ge(ae.path),valid:xe.valid,errors:xe.errors,value:xe.value})):Promise.resolve({key:ge(ae.path),valid:!0,errors:[],value:void 0})));i.value=!1;const G={},K={},Q={};for(const ae of P)G[ae.key]={valid:ae.valid,errors:ae.errors},ae.value&&fr(Q,ae.key,ae.value),ae.errors.length&&(K[ae.key]=ae.errors[0]);return{valid:P.every(ae=>ae.valid),results:G,errors:K,values:Q,source:"fields"}}async function Z(p,b){var P;const G=se(p);if(G&&(b==null?void 0:b.mode)!=="silent"&&(G.validated=!0),q){const{results:K}=await Oe((b==null?void 0:b.mode)||"validated-only");return K[p]||{errors:[],valid:!0}}return G!=null&&G.validate?G.validate(b):(!G&&(P=b==null?void 0:b.warn),Promise.resolve({errors:[],valid:!0}))}function W(p){lc(M.value,p)}function pe(p,b,P=!1){re(p,b),fr(u,p,b),P&&!(e!=null&&e.initialValues)&&fr(L.value,p,Le(b))}function re(p,b,P=!1){fr(M.value,p,Le(b)),P&&fr(L.value,p,Le(b))}async function de(){const p=Ie(q);if(!p)return{valid:!0,results:{},errors:{},source:"none"};i.value=!0;const b=ao(p)||Gt(p)?await k_(p,u):await R_(p,u,{names:C.value,bailsMap:$.value});return i.value=!1,b}const me=Ze((p,{evt:b})=>{Zd(b)&&b.target.submit()});ir(()=>{if(e!=null&&e.initialErrors&&E(e.initialErrors),e!=null&&e.initialTouched&&B(e.initialTouched),e!=null&&e.validateOnMount){Y();return}O.validateSchema&&O.validateSchema("silent")}),rt(q)&&Et(q,()=>{var p;(p=O.validateSchema)===null||p===void 0||p.call(O,"validated-only")}),$r(Fo,O);function Re(p,b){const P=ht(b)||b==null?void 0:b.label,G=se(ge(p))||z(p,{label:P}),K=()=>ht(b)?b(On(G,In)):b||{};function Q(){var Me;G.touched=!0,((Me=K().validateOnBlur)!==null&&Me!==void 0?Me:Xr().validateOnBlur)&&Z(ge(G.path))}function ae(){var Me;((Me=K().validateOnInput)!==null&&Me!==void 0?Me:Xr().validateOnInput)&&Ft(()=>{Z(ge(G.path))})}function xe(){var Me;((Me=K().validateOnChange)!==null&&Me!==void 0?Me:Xr().validateOnChange)&&Ft(()=>{Z(ge(G.path))})}const Ge=le(()=>{const Me={onChange:xe,onInput:ae,onBlur:Q};return ht(b)?Object.assign(Object.assign({},Me),b(On(G,In)).props||{}):b!=null&&b.props?Object.assign(Object.assign({},Me),b.props(On(G,In))):Me});return[ke(p,()=>{var Me,tt,De;return(De=(Me=K().validateOnModelUpdate)!==null&&Me!==void 0?Me:(tt=Xr())===null||tt===void 0?void 0:tt.validateOnModelUpdate)!==null&&De!==void 0?De:!0}),Ge]}function Ve(p){return Array.isArray(p)?p.map(b=>ke(b,!0)):ke(p)}function $e(p,b){const[P,G]=Re(p,b);function K(){G.value.onBlur()}function Q(xe){const Ge=lo(xe);J(ge(p),Ge,!1),G.value.onInput()}function ae(xe){const Ge=lo(xe);J(ge(p),Ge,!1),G.value.onChange()}return le(()=>Object.assign(Object.assign({},G.value),{onBlur:K,onInput:Q,onChange:ae,value:P.value}))}function ft(p,b){const[P,G]=Re(p,b),K=se(ge(p));function Q(ae){P.value=ae}return le(()=>{const ae=ht(b)?b(On(K,In)):b||{};return Object.assign({[ae.model||"modelValue"]:P.value,[`onUpdate:${ae.model||"modelValue"}`]:Q},G.value)})}const F=Object.assign(Object.assign({},O),{values:ea(u),handleReset:()=>te(),submitForm:me});return $r(i_,F),F}function G_(e,t,r,s){const n={touched:"some",pending:"some",valid:"every"},o=le(()=>!St(t,Ie(r)));function i(){const l=e.value;return Dt(n).reduce((u,d)=>{const f=n[d];return u[d]=l[f](h=>h[d]),u},{})}const a=or(i());return Yp(()=>{const l=i();a.touched=l.touched,a.valid=l.valid,a.pending=l.pending}),le(()=>Object.assign(Object.assign({initialValues:Ie(r)},a),{valid:a.valid&&!Dt(s.value).length,dirty:o.value}))}function q_(e,t,r){const s=lf(r),n=oe(s),o=oe(Le(s));function i(a,l){l!=null&&l.force?(n.value=Le(a),o.value=Le(a)):(n.value=fn(Le(n.value)||{},Le(a)),o.value=fn(Le(o.value)||{},Le(a))),l!=null&&l.updateFields&&e.value.forEach(u=>{if(u.touched)return;const f=Pt(n.value,ge(u.path));fr(t,ge(u.path),Le(f))})}return{initialValues:n,originalInitialValues:o,setInitialValues:i}}function H_(e,t){return t?{valid:e.valid&&t.valid,errors:[...e.errors,...t.errors]}:e}const z_=Ns({name:"Form",inheritAttrs:!1,props:{as:{type:null,default:"form"},validationSchema:{type:Object,default:void 0},initialValues:{type:Object,default:void 0},initialErrors:{type:Object,default:void 0},initialTouched:{type:Object,default:void 0},validateOnMount:{type:Boolean,default:!1},onSubmit:{type:Function,default:void 0},onInvalidSubmit:{type:Function,default:void 0},keepValues:{type:Boolean,default:!1},name:{type:String,default:"Form"}},setup(e,t){const r=Kr(e,"validationSchema"),s=Kr(e,"keepValues"),{errors:n,errorBag:o,values:i,meta:a,isSubmitting:l,isValidating:u,submitCount:d,controlledValues:f,validate:h,validateField:g,handleReset:y,resetForm:E,handleSubmit:A,setErrors:m,setFieldError:C,setFieldValue:$,setValues:I,setFieldTouched:R,setTouched:M,resetField:L}=B_({validationSchema:r.value?r:void 0,initialValues:e.initialValues,initialErrors:e.initialErrors,initialTouched:e.initialTouched,validateOnMount:e.validateOnMount,keepValuesOnUnmount:s,name:e.name}),k=A((ye,{evt:se})=>{Zd(se)&&se.target.submit()},e.onInvalidSubmit),x=e.onSubmit?A(e.onSubmit,e.onInvalidSubmit):k;function X(ye){Sa(ye)&&ye.preventDefault(),y(),typeof t.attrs.onReset=="function"&&t.attrs.onReset()}function q(ye,se){return A(typeof ye=="function"&&!se?ye:se,e.onInvalidSubmit)(ye)}function z(){return Le(i)}function ue(){return Le(a.value)}function Ee(){return Le(n.value)}function Oe(){return{meta:a.value,errors:n.value,errorBag:o.value,values:i,isSubmitting:l.value,isValidating:u.value,submitCount:d.value,controlledValues:f.value,validate:h,validateField:g,handleSubmit:q,handleReset:y,submitForm:k,setErrors:m,setFieldError:C,setFieldValue:$,setValues:I,setFieldTouched:R,setTouched:M,resetForm:E,resetField:L,getValues:z,getMeta:ue,getErrors:Ee}}return t.expose({setFieldError:C,setErrors:m,setFieldValue:$,setValues:I,setFieldTouched:R,setTouched:M,resetForm:E,validate:h,validateField:g,resetField:L,getValues:z,getMeta:ue,getErrors:Ee,values:i,meta:a,errors:n}),function(){const se=e.as==="form"?e.as:e.as?_o(e.as):null,Te=Ca(se,t,Oe);return se?rs(se,Object.assign(Object.assign(Object.assign({},se==="form"?{novalidate:!0}:{}),t.attrs),{onSubmit:x,onReset:X}),Te):Te}}}),cf=z_,K_=Ns({name:"ErrorMessage",props:{as:{type:String,default:void 0},name:{type:String,required:!0}},setup(e,t){const r=kt(Fo,void 0),s=le(()=>r==null?void 0:r.errors.value[e.name]);function n(){return{message:s.value}}return()=>{if(!s.value)return;const o=e.as?_o(e.as):e.as,i=Ca(o,t,n),a=Object.assign({role:"alert"},t.attrs);return!o&&(Array.isArray(i)||!i)&&(i!=null&&i.length)?i:(Array.isArray(i)||!i)&&!(i!=null&&i.length)?rs(o||"span",a,s.value):rs(o,a,i)}}}),uf=K_;var qT=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function W_(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ni,hc;function J_(){if(hc)return ni;hc=1;function e(m){this._maxSize=m,this.clear()}e.prototype.clear=function(){this._size=0,this._values=Object.create(null)},e.prototype.get=function(m){return this._values[m]},e.prototype.set=function(m,C){return this._size>=this._maxSize&&this.clear(),m in this._values||this._size++,this._values[m]=C};var t=/[^.^\]^[]+|(?=\[\]|\.\.)/g,r=/^\d+$/,s=/^\d/,n=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,o=/^\s*(['"]?)(.*?)(\1)\s*$/,i=512,a=new e(i),l=new e(i),u=new e(i);ni={Cache:e,split:f,normalizePath:d,setter:function(m){var C=d(m);return l.get(m)||l.set(m,function(I,R){for(var M=0,L=C.length,k=I;M<L-1;){var x=C[M];if(x==="__proto__"||x==="constructor"||x==="prototype")return I;k=k[C[M++]]}k[C[M]]=R})},getter:function(m,C){var $=d(m);return u.get(m)||u.set(m,function(R){for(var M=0,L=$.length;M<L;)if(R!=null||!C)R=R[$[M++]];else return;return R})},join:function(m){return m.reduce(function(C,$){return C+(g($)||r.test($)?"["+$+"]":(C?".":"")+$)},"")},forEach:function(m,C,$){h(Array.isArray(m)?m:f(m),C,$)}};function d(m){return a.get(m)||a.set(m,f(m).map(function(C){return C.replace(o,"$2")}))}function f(m){return m.match(t)||[""]}function h(m,C,$){var I=m.length,R,M,L,k;for(M=0;M<I;M++)R=m[M],R&&(A(R)&&(R='"'+R+'"'),k=g(R),L=!k&&/^\d+$/.test(R),C.call($,R,k,L,M,m))}function g(m){return typeof m=="string"&&m&&["'",'"'].indexOf(m.charAt(0))!==-1}function y(m){return m.match(s)&&!m.match(r)}function E(m){return n.test(m)}function A(m){return!g(m)&&(y(m)||E(m))}return ni}var Qr=J_(),oi,gc;function X_(){if(gc)return oi;gc=1;const e=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,t=d=>d.match(e)||[],r=d=>d[0].toUpperCase()+d.slice(1),s=(d,f)=>t(d).join(f).toLowerCase(),n=d=>t(d).reduce((f,h)=>`${f}${f?h[0].toUpperCase()+h.slice(1).toLowerCase():h.toLowerCase()}`,"");return oi={words:t,upperFirst:r,camelCase:n,pascalCase:d=>r(n(d)),snakeCase:d=>s(d,"_"),kebabCase:d=>s(d,"-"),sentenceCase:d=>r(s(d," ")),titleCase:d=>t(d).map(r).join(" ")},oi}var ii=X_(),xn={exports:{}},mc;function Y_(){if(mc)return xn.exports;mc=1,xn.exports=function(n){return e(t(n),n)},xn.exports.array=e;function e(n,o){var i=n.length,a=new Array(i),l={},u=i,d=r(o),f=s(n);for(o.forEach(function(g){if(!f.has(g[0])||!f.has(g[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});u--;)l[u]||h(n[u],u,new Set);return a;function h(g,y,E){if(E.has(g)){var A;try{A=", node was:"+JSON.stringify(g)}catch{A=""}throw new Error("Cyclic dependency"+A)}if(!f.has(g))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(g));if(!l[y]){l[y]=!0;var m=d.get(g)||new Set;if(m=Array.from(m),y=m.length){E.add(g);do{var C=m[--y];h(C,f.get(C),E)}while(y);E.delete(g)}a[--i]=g}}}function t(n){for(var o=new Set,i=0,a=n.length;i<a;i++){var l=n[i];o.add(l[0]),o.add(l[1])}return Array.from(o)}function r(n){for(var o=new Map,i=0,a=n.length;i<a;i++){var l=n[i];o.has(l[0])||o.set(l[0],new Set),o.has(l[1])||o.set(l[1],new Set),o.get(l[0]).add(l[1])}return o}function s(n){for(var o=new Map,i=0,a=n.length;i<a;i++)o.set(n[i],i);return o}return xn.exports}var Z_=Y_();const Q_=W_(Z_),e1=Object.prototype.toString,t1=Error.prototype.toString,r1=RegExp.prototype.toString,s1=typeof Symbol<"u"?Symbol.prototype.toString:()=>"",n1=/^Symbol\((.*)\)(.*)$/;function o1(e){return e!=+e?"NaN":e===0&&1/e<0?"-0":""+e}function vc(e,t=!1){if(e==null||e===!0||e===!1)return""+e;const r=typeof e;if(r==="number")return o1(e);if(r==="string")return t?`"${e}"`:e;if(r==="function")return"[Function "+(e.name||"anonymous")+"]";if(r==="symbol")return s1.call(e).replace(n1,"Symbol($1)");const s=e1.call(e).slice(8,-1);return s==="Date"?isNaN(e.getTime())?""+e:e.toISOString(e):s==="Error"||e instanceof Error?"["+t1.call(e)+"]":s==="RegExp"?r1.call(e):null}function Fr(e,t){let r=vc(e,t);return r!==null?r:JSON.stringify(e,function(s,n){let o=vc(this[s],t);return o!==null?o:n},2)}function df(e){return e==null?[]:[].concat(e)}let ff,pf,hf,i1=/\$\{\s*(\w+)\s*\}/g;ff=Symbol.toStringTag;class yc{constructor(t,r,s,n){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[ff]="Error",this.name="ValidationError",this.value=r,this.path=s,this.type=n,this.errors=[],this.inner=[],df(t).forEach(o=>{if(Ct.isError(o)){this.errors.push(...o.errors);const i=o.inner.length?o.inner:[o];this.inner.push(...i)}else this.errors.push(o)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}pf=Symbol.hasInstance;hf=Symbol.toStringTag;class Ct extends Error{static formatError(t,r){const s=r.label||r.path||"this";return r=Object.assign({},r,{path:s,originalPath:r.path}),typeof t=="string"?t.replace(i1,(n,o)=>Fr(r[o])):typeof t=="function"?t(r):t}static isError(t){return t&&t.name==="ValidationError"}constructor(t,r,s,n,o){const i=new yc(t,r,s,n);if(o)return i;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[hf]="Error",this.name=i.name,this.message=i.message,this.type=i.type,this.value=i.value,this.path=i.path,this.errors=i.errors,this.inner=i.inner,Error.captureStackTrace&&Error.captureStackTrace(this,Ct)}static[pf](t){return yc[Symbol.hasInstance](t)||super[Symbol.hasInstance](t)}}let sr={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:e,type:t,value:r,originalValue:s})=>{const n=s!=null&&s!==r?` (cast from the value \`${Fr(s,!0)}\`).`:".";return t!=="mixed"?`${e} must be a \`${t}\` type, but the final value was: \`${Fr(r,!0)}\``+n:`${e} must match the configured type. The validated value was: \`${Fr(r,!0)}\``+n}},At={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},a1={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},ji={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},Ui={isValue:"${path} field must be ${value}"},Bn={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},l1={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},c1={notType:e=>{const{path:t,value:r,spec:s}=e,n=s.types.length;if(Array.isArray(r)){if(r.length<n)return`${t} tuple value has too few items, expected a length of ${n} but got ${r.length} for value: \`${Fr(r,!0)}\``;if(r.length>n)return`${t} tuple value has too many items, expected a length of ${n} but got ${r.length} for value: \`${Fr(r,!0)}\``}return Ct.formatError(sr.notType,e)}};Object.assign(Object.create(null),{mixed:sr,string:At,number:a1,date:ji,object:Bn,array:l1,boolean:Ui,tuple:c1});const Pa=e=>e&&e.__isYupSchema__;class co{static fromOptions(t,r){if(!r.then&&!r.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:s,then:n,otherwise:o}=r,i=typeof s=="function"?s:(...a)=>a.every(l=>l===s);return new co(t,(a,l)=>{var u;let d=i(...a)?n:o;return(u=d==null?void 0:d(l))!=null?u:l})}constructor(t,r){this.fn=void 0,this.refs=t,this.refs=t,this.fn=r}resolve(t,r){let s=this.refs.map(o=>o.getValue(r==null?void 0:r.value,r==null?void 0:r.parent,r==null?void 0:r.context)),n=this.fn(s,t,r);if(n===void 0||n===t)return t;if(!Pa(n))throw new TypeError("conditions must return a schema object");return n.resolve(r)}}const $n={context:"$",value:"."};class as{constructor(t,r={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,typeof t!="string")throw new TypeError("ref must be a string, got: "+t);if(this.key=t.trim(),t==="")throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===$n.context,this.isValue=this.key[0]===$n.value,this.isSibling=!this.isContext&&!this.isValue;let s=this.isContext?$n.context:this.isValue?$n.value:"";this.path=this.key.slice(s.length),this.getter=this.path&&Qr.getter(this.path,!0),this.map=r.map}getValue(t,r,s){let n=this.isContext?s:this.isValue?t:r;return this.getter&&(n=this.getter(n||{})),this.map&&(n=this.map(n)),n}cast(t,r){return this.getValue(t,r==null?void 0:r.parent,r==null?void 0:r.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(t){return t&&t.__isYupRef}}as.prototype.__isYupRef=!0;const yr=e=>e==null;function hs(e){function t({value:r,path:s="",options:n,originalValue:o,schema:i},a,l){const{name:u,test:d,params:f,message:h,skipAbsent:g}=e;let{parent:y,context:E,abortEarly:A=i.spec.abortEarly,disableStackTrace:m=i.spec.disableStackTrace}=n;function C(q){return as.isRef(q)?q.getValue(r,y,E):q}function $(q={}){const z=Object.assign({value:r,originalValue:o,label:i.spec.label,path:q.path||s,spec:i.spec,disableStackTrace:q.disableStackTrace||m},f,q.params);for(const Ee of Object.keys(z))z[Ee]=C(z[Ee]);const ue=new Ct(Ct.formatError(q.message||h,z),r,z.path,q.type||u,z.disableStackTrace);return ue.params=z,ue}const I=A?a:l;let R={path:s,parent:y,type:u,from:n.from,createError:$,resolve:C,options:n,originalValue:o,schema:i};const M=q=>{Ct.isError(q)?I(q):q?l(null):I($())},L=q=>{Ct.isError(q)?I(q):a(q)};if(g&&yr(r))return M(!0);let x;try{var X;if(x=d.call(R,r,R),typeof((X=x)==null?void 0:X.then)=="function"){if(n.sync)throw new Error(`Validation test of type: "${R.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(x).then(M,L)}}catch(q){L(q);return}M(x)}return t.OPTIONS=e,t}function u1(e,t,r,s=r){let n,o,i;return t?(Qr.forEach(t,(a,l,u)=>{let d=l?a.slice(1,a.length-1):a;e=e.resolve({context:s,parent:n,value:r});let f=e.type==="tuple",h=u?parseInt(d,10):0;if(e.innerType||f){if(f&&!u)throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${i}" must contain an index to the tuple element, e.g. "${i}[0]"`);if(r&&h>=r.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${a}, in the path: ${t}. because there is no value at that index. `);n=r,r=r&&r[h],e=f?e.spec.types[h]:e.innerType}if(!u){if(!e.fields||!e.fields[d])throw new Error(`The schema does not contain the path: ${t}. (failed at: ${i} which is a type: "${e.type}")`);n=r,r=r&&r[d],e=e.fields[d]}o=d,i=l?"["+a+"]":"."+a}),{schema:e,parent:n,parentPath:o}):{parent:n,parentPath:t,schema:e}}class uo extends Set{describe(){const t=[];for(const r of this.values())t.push(as.isRef(r)?r.describe():r);return t}resolveAll(t){let r=[];for(const s of this.values())r.push(t(s));return r}clone(){return new uo(this.values())}merge(t,r){const s=this.clone();return t.forEach(n=>s.add(n)),r.forEach(n=>s.delete(n)),s}}function vs(e,t=new Map){if(Pa(e)||!e||typeof e!="object")return e;if(t.has(e))return t.get(e);let r;if(e instanceof Date)r=new Date(e.getTime()),t.set(e,r);else if(e instanceof RegExp)r=new RegExp(e),t.set(e,r);else if(Array.isArray(e)){r=new Array(e.length),t.set(e,r);for(let s=0;s<e.length;s++)r[s]=vs(e[s],t)}else if(e instanceof Map){r=new Map,t.set(e,r);for(const[s,n]of e.entries())r.set(s,vs(n,t))}else if(e instanceof Set){r=new Set,t.set(e,r);for(const s of e)r.add(vs(s,t))}else if(e instanceof Object){r={},t.set(e,r);for(const[s,n]of Object.entries(e))r[s]=vs(n,t)}else throw Error(`Unable to clone ${e}`);return r}class Jt{constructor(t){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new uo,this._blacklist=new uo,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(sr.notType)}),this.type=t.type,this._typeCheck=t.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},t==null?void 0:t.spec),this.withMutation(r=>{r.nonNullable()})}get _type(){return this.type}clone(t){if(this._mutate)return t&&Object.assign(this.spec,t),this;const r=Object.create(Object.getPrototypeOf(this));return r.type=this.type,r._typeCheck=this._typeCheck,r._whitelist=this._whitelist.clone(),r._blacklist=this._blacklist.clone(),r.internalTests=Object.assign({},this.internalTests),r.exclusiveTests=Object.assign({},this.exclusiveTests),r.deps=[...this.deps],r.conditions=[...this.conditions],r.tests=[...this.tests],r.transforms=[...this.transforms],r.spec=vs(Object.assign({},this.spec,t)),r}label(t){let r=this.clone();return r.spec.label=t,r}meta(...t){if(t.length===0)return this.spec.meta;let r=this.clone();return r.spec.meta=Object.assign(r.spec.meta||{},t[0]),r}withMutation(t){let r=this._mutate;this._mutate=!0;let s=t(this);return this._mutate=r,s}concat(t){if(!t||t===this)return this;if(t.type!==this.type&&this.type!=="mixed")throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${t.type}`);let r=this,s=t.clone();const n=Object.assign({},r.spec,s.spec);return s.spec=n,s.internalTests=Object.assign({},r.internalTests,s.internalTests),s._whitelist=r._whitelist.merge(t._whitelist,t._blacklist),s._blacklist=r._blacklist.merge(t._blacklist,t._whitelist),s.tests=r.tests,s.exclusiveTests=r.exclusiveTests,s.withMutation(o=>{t.tests.forEach(i=>{o.test(i.OPTIONS)})}),s.transforms=[...r.transforms,...s.transforms],s}isType(t){return t==null?!!(this.spec.nullable&&t===null||this.spec.optional&&t===void 0):this._typeCheck(t)}resolve(t){let r=this;if(r.conditions.length){let s=r.conditions;r=r.clone(),r.conditions=[],r=s.reduce((n,o)=>o.resolve(n,t),r),r=r.resolve(t)}return r}resolveOptions(t){var r,s,n,o;return Object.assign({},t,{from:t.from||[],strict:(r=t.strict)!=null?r:this.spec.strict,abortEarly:(s=t.abortEarly)!=null?s:this.spec.abortEarly,recursive:(n=t.recursive)!=null?n:this.spec.recursive,disableStackTrace:(o=t.disableStackTrace)!=null?o:this.spec.disableStackTrace})}cast(t,r={}){let s=this.resolve(Object.assign({value:t},r)),n=r.assert==="ignore-optionality",o=s._cast(t,r);if(r.assert!==!1&&!s.isType(o)){if(n&&yr(o))return o;let i=Fr(t),a=Fr(o);throw new TypeError(`The value of ${r.path||"field"} could not be cast to a value that satisfies the schema type: "${s.type}". 

attempted value: ${i} 
`+(a!==i?`result of cast: ${a}`:""))}return o}_cast(t,r){let s=t===void 0?t:this.transforms.reduce((n,o)=>o.call(this,n,t,this),t);return s===void 0&&(s=this.getDefault(r)),s}_validate(t,r={},s,n){let{path:o,originalValue:i=t,strict:a=this.spec.strict}=r,l=t;a||(l=this._cast(l,Object.assign({assert:!1},r)));let u=[];for(let d of Object.values(this.internalTests))d&&u.push(d);this.runTests({path:o,value:l,originalValue:i,options:r,tests:u},s,d=>{if(d.length)return n(d,l);this.runTests({path:o,value:l,originalValue:i,options:r,tests:this.tests},s,n)})}runTests(t,r,s){let n=!1,{tests:o,value:i,originalValue:a,path:l,options:u}=t,d=E=>{n||(n=!0,r(E,i))},f=E=>{n||(n=!0,s(E,i))},h=o.length,g=[];if(!h)return f([]);let y={value:i,originalValue:a,path:l,options:u,schema:this};for(let E=0;E<o.length;E++){const A=o[E];A(y,d,function(C){C&&(Array.isArray(C)?g.push(...C):g.push(C)),--h<=0&&f(g)})}}asNestedTest({key:t,index:r,parent:s,parentPath:n,originalParent:o,options:i}){const a=t??r;if(a==null)throw TypeError("Must include `key` or `index` for nested validations");const l=typeof a=="number";let u=s[a];const d=Object.assign({},i,{strict:!0,parent:s,value:u,originalValue:o[a],key:void 0,[l?"index":"key"]:a,path:l||a.includes(".")?`${n||""}[${l?a:`"${a}"`}]`:(n?`${n}.`:"")+t});return(f,h,g)=>this.resolve(d)._validate(u,d,h,g)}validate(t,r){var s;let n=this.resolve(Object.assign({},r,{value:t})),o=(s=r==null?void 0:r.disableStackTrace)!=null?s:n.spec.disableStackTrace;return new Promise((i,a)=>n._validate(t,r,(l,u)=>{Ct.isError(l)&&(l.value=u),a(l)},(l,u)=>{l.length?a(new Ct(l,u,void 0,void 0,o)):i(u)}))}validateSync(t,r){var s;let n=this.resolve(Object.assign({},r,{value:t})),o,i=(s=r==null?void 0:r.disableStackTrace)!=null?s:n.spec.disableStackTrace;return n._validate(t,Object.assign({},r,{sync:!0}),(a,l)=>{throw Ct.isError(a)&&(a.value=l),a},(a,l)=>{if(a.length)throw new Ct(a,t,void 0,void 0,i);o=l}),o}isValid(t,r){return this.validate(t,r).then(()=>!0,s=>{if(Ct.isError(s))return!1;throw s})}isValidSync(t,r){try{return this.validateSync(t,r),!0}catch(s){if(Ct.isError(s))return!1;throw s}}_getDefault(t){let r=this.spec.default;return r==null?r:typeof r=="function"?r.call(this,t):vs(r)}getDefault(t){return this.resolve(t||{})._getDefault(t)}default(t){return arguments.length===0?this._getDefault():this.clone({default:t})}strict(t=!0){return this.clone({strict:t})}nullability(t,r){const s=this.clone({nullable:t});return s.internalTests.nullable=hs({message:r,name:"nullable",test(n){return n===null?this.schema.spec.nullable:!0}}),s}optionality(t,r){const s=this.clone({optional:t});return s.internalTests.optionality=hs({message:r,name:"optionality",test(n){return n===void 0?this.schema.spec.optional:!0}}),s}optional(){return this.optionality(!0)}defined(t=sr.defined){return this.optionality(!1,t)}nullable(){return this.nullability(!0)}nonNullable(t=sr.notNull){return this.nullability(!1,t)}required(t=sr.required){return this.clone().withMutation(r=>r.nonNullable(t).defined(t))}notRequired(){return this.clone().withMutation(t=>t.nullable().optional())}transform(t){let r=this.clone();return r.transforms.push(t),r}test(...t){let r;if(t.length===1?typeof t[0]=="function"?r={test:t[0]}:r=t[0]:t.length===2?r={name:t[0],test:t[1]}:r={name:t[0],message:t[1],test:t[2]},r.message===void 0&&(r.message=sr.default),typeof r.test!="function")throw new TypeError("`test` is a required parameters");let s=this.clone(),n=hs(r),o=r.exclusive||r.name&&s.exclusiveTests[r.name]===!0;if(r.exclusive&&!r.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return r.name&&(s.exclusiveTests[r.name]=!!r.exclusive),s.tests=s.tests.filter(i=>!(i.OPTIONS.name===r.name&&(o||i.OPTIONS.test===n.OPTIONS.test))),s.tests.push(n),s}when(t,r){!Array.isArray(t)&&typeof t!="string"&&(r=t,t=".");let s=this.clone(),n=df(t).map(o=>new as(o));return n.forEach(o=>{o.isSibling&&s.deps.push(o.key)}),s.conditions.push(typeof r=="function"?new co(n,r):co.fromOptions(n,r)),s}typeError(t){let r=this.clone();return r.internalTests.typeError=hs({message:t,name:"typeError",skipAbsent:!0,test(s){return this.schema._typeCheck(s)?!0:this.createError({params:{type:this.schema.type}})}}),r}oneOf(t,r=sr.oneOf){let s=this.clone();return t.forEach(n=>{s._whitelist.add(n),s._blacklist.delete(n)}),s.internalTests.whiteList=hs({message:r,name:"oneOf",skipAbsent:!0,test(n){let o=this.schema._whitelist,i=o.resolveAll(this.resolve);return i.includes(n)?!0:this.createError({params:{values:Array.from(o).join(", "),resolved:i}})}}),s}notOneOf(t,r=sr.notOneOf){let s=this.clone();return t.forEach(n=>{s._blacklist.add(n),s._whitelist.delete(n)}),s.internalTests.blacklist=hs({message:r,name:"notOneOf",test(n){let o=this.schema._blacklist,i=o.resolveAll(this.resolve);return i.includes(n)?this.createError({params:{values:Array.from(o).join(", "),resolved:i}}):!0}}),s}strip(t=!0){let r=this.clone();return r.spec.strip=t,r}describe(t){const r=(t?this.resolve(t):this).clone(),{label:s,meta:n,optional:o,nullable:i}=r.spec;return{meta:n,label:s,optional:o,nullable:i,default:r.getDefault(t),type:r.type,oneOf:r._whitelist.describe(),notOneOf:r._blacklist.describe(),tests:r.tests.map(l=>({name:l.OPTIONS.name,params:l.OPTIONS.params})).filter((l,u,d)=>d.findIndex(f=>f.name===l.name)===u)}}}Jt.prototype.__isYupSchema__=!0;for(const e of["validate","validateSync"])Jt.prototype[`${e}At`]=function(t,r,s={}){const{parent:n,parentPath:o,schema:i}=u1(this,t,r,s.context);return i[e](n&&n[o],Object.assign({},s,{parent:n,path:t}))};for(const e of["equals","is"])Jt.prototype[e]=Jt.prototype.oneOf;for(const e of["not","nope"])Jt.prototype[e]=Jt.prototype.notOneOf;function gf(){return new mf}class mf extends Jt{constructor(){super({type:"boolean",check(t){return t instanceof Boolean&&(t=t.valueOf()),typeof t=="boolean"}}),this.withMutation(()=>{this.transform((t,r,s)=>{if(s.spec.coerce&&!s.isType(t)){if(/^(true|1)$/i.test(String(t)))return!0;if(/^(false|0)$/i.test(String(t)))return!1}return t})})}isTrue(t=Ui.isValue){return this.test({message:t,name:"is-value",exclusive:!0,params:{value:"true"},test(r){return yr(r)||r===!0}})}isFalse(t=Ui.isValue){return this.test({message:t,name:"is-value",exclusive:!0,params:{value:"false"},test(r){return yr(r)||r===!1}})}default(t){return super.default(t)}defined(t){return super.defined(t)}optional(){return super.optional()}required(t){return super.required(t)}notRequired(){return super.notRequired()}nullable(){return super.nullable()}nonNullable(t){return super.nonNullable(t)}strip(t){return super.strip(t)}}gf.prototype=mf.prototype;const d1=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function f1(e){const t=Bi(e);if(!t)return Date.parse?Date.parse(e):Number.NaN;if(t.z===void 0&&t.plusMinus===void 0)return new Date(t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond).valueOf();let r=0;return t.z!=="Z"&&t.plusMinus!==void 0&&(r=t.hourOffset*60+t.minuteOffset,t.plusMinus==="+"&&(r=0-r)),Date.UTC(t.year,t.month,t.day,t.hour,t.minute+r,t.second,t.millisecond)}function Bi(e){var t,r;const s=d1.exec(e);return s?{year:dr(s[1]),month:dr(s[2],1)-1,day:dr(s[3],1),hour:dr(s[4]),minute:dr(s[5]),second:dr(s[6]),millisecond:s[7]?dr(s[7].substring(0,3)):0,precision:(t=(r=s[7])==null?void 0:r.length)!=null?t:void 0,z:s[8]||void 0,plusMinus:s[9]||void 0,hourOffset:dr(s[10]),minuteOffset:dr(s[11])}:null}function dr(e,t=0){return Number(e)||t}let p1=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,h1=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,g1=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,m1="^\\d{4}-\\d{2}-\\d{2}",v1="\\d{2}:\\d{2}:\\d{2}",y1="(([+-]\\d{2}(:?\\d{2})?)|Z)",b1=new RegExp(`${m1}T${v1}(\\.\\d+)?${y1}$`),_1=e=>yr(e)||e===e.trim(),w1={}.toString();function Cs(){return new vf}class vf extends Jt{constructor(){super({type:"string",check(t){return t instanceof String&&(t=t.valueOf()),typeof t=="string"}}),this.withMutation(()=>{this.transform((t,r,s)=>{if(!s.spec.coerce||s.isType(t)||Array.isArray(t))return t;const n=t!=null&&t.toString?t.toString():t;return n===w1?t:n})})}required(t){return super.required(t).withMutation(r=>r.test({message:t||sr.required,name:"required",skipAbsent:!0,test:s=>!!s.length}))}notRequired(){return super.notRequired().withMutation(t=>(t.tests=t.tests.filter(r=>r.OPTIONS.name!=="required"),t))}length(t,r=At.length){return this.test({message:r,name:"length",exclusive:!0,params:{length:t},skipAbsent:!0,test(s){return s.length===this.resolve(t)}})}min(t,r=At.min){return this.test({message:r,name:"min",exclusive:!0,params:{min:t},skipAbsent:!0,test(s){return s.length>=this.resolve(t)}})}max(t,r=At.max){return this.test({name:"max",exclusive:!0,message:r,params:{max:t},skipAbsent:!0,test(s){return s.length<=this.resolve(t)}})}matches(t,r){let s=!1,n,o;return r&&(typeof r=="object"?{excludeEmptyString:s=!1,message:n,name:o}=r:n=r),this.test({name:o||"matches",message:n||At.matches,params:{regex:t},skipAbsent:!0,test:i=>i===""&&s||i.search(t)!==-1})}email(t=At.email){return this.matches(p1,{name:"email",message:t,excludeEmptyString:!0})}url(t=At.url){return this.matches(h1,{name:"url",message:t,excludeEmptyString:!0})}uuid(t=At.uuid){return this.matches(g1,{name:"uuid",message:t,excludeEmptyString:!1})}datetime(t){let r="",s,n;return t&&(typeof t=="object"?{message:r="",allowOffset:s=!1,precision:n=void 0}=t:r=t),this.matches(b1,{name:"datetime",message:r||At.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:r||At.datetime_offset,params:{allowOffset:s},skipAbsent:!0,test:o=>{if(!o||s)return!0;const i=Bi(o);return i?!!i.z:!1}}).test({name:"datetime_precision",message:r||At.datetime_precision,params:{precision:n},skipAbsent:!0,test:o=>{if(!o||n==null)return!0;const i=Bi(o);return i?i.precision===n:!1}})}ensure(){return this.default("").transform(t=>t===null?"":t)}trim(t=At.trim){return this.transform(r=>r!=null?r.trim():r).test({message:t,name:"trim",test:_1})}lowercase(t=At.lowercase){return this.transform(r=>yr(r)?r:r.toLowerCase()).test({message:t,name:"string_case",exclusive:!0,skipAbsent:!0,test:r=>yr(r)||r===r.toLowerCase()})}uppercase(t=At.uppercase){return this.transform(r=>yr(r)?r:r.toUpperCase()).test({message:t,name:"string_case",exclusive:!0,skipAbsent:!0,test:r=>yr(r)||r===r.toUpperCase()})}}Cs.prototype=vf.prototype;let E1=new Date(""),S1=e=>Object.prototype.toString.call(e)==="[object Date]";class Ta extends Jt{constructor(){super({type:"date",check(t){return S1(t)&&!isNaN(t.getTime())}}),this.withMutation(()=>{this.transform((t,r,s)=>!s.spec.coerce||s.isType(t)||t===null?t:(t=f1(t),isNaN(t)?Ta.INVALID_DATE:new Date(t)))})}prepareParam(t,r){let s;if(as.isRef(t))s=t;else{let n=this.cast(t);if(!this._typeCheck(n))throw new TypeError(`\`${r}\` must be a Date or a value that can be \`cast()\` to a Date`);s=n}return s}min(t,r=ji.min){let s=this.prepareParam(t,"min");return this.test({message:r,name:"min",exclusive:!0,params:{min:t},skipAbsent:!0,test(n){return n>=this.resolve(s)}})}max(t,r=ji.max){let s=this.prepareParam(t,"max");return this.test({message:r,name:"max",exclusive:!0,params:{max:t},skipAbsent:!0,test(n){return n<=this.resolve(s)}})}}Ta.INVALID_DATE=E1;function A1(e,t=[]){let r=[],s=new Set,n=new Set(t.map(([i,a])=>`${i}-${a}`));function o(i,a){let l=Qr.split(i)[0];s.add(l),n.has(`${a}-${l}`)||r.push([a,l])}for(const i of Object.keys(e)){let a=e[i];s.add(i),as.isRef(a)&&a.isSibling?o(a.path,i):Pa(a)&&"deps"in a&&a.deps.forEach(l=>o(l,i))}return Q_.array(Array.from(s),r).reverse()}function bc(e,t){let r=1/0;return e.some((s,n)=>{var o;if((o=t.path)!=null&&o.includes(s))return r=n,!0}),r}function yf(e){return(t,r)=>bc(e,t)-bc(e,r)}const C1=(e,t,r)=>{if(typeof e!="string")return e;let s=e;try{s=JSON.parse(e)}catch{}return r.isType(s)?s:e};function Gn(e){if("fields"in e){const t={};for(const[r,s]of Object.entries(e.fields))t[r]=Gn(s);return e.setFields(t)}if(e.type==="array"){const t=e.optional();return t.innerType&&(t.innerType=Gn(t.innerType)),t}return e.type==="tuple"?e.optional().clone({types:e.spec.types.map(Gn)}):"optional"in e?e.optional():e}const P1=(e,t)=>{const r=[...Qr.normalizePath(t)];if(r.length===1)return r[0]in e;let s=r.pop(),n=Qr.getter(Qr.join(r),!0)(e);return!!(n&&s in n)};let _c=e=>Object.prototype.toString.call(e)==="[object Object]";function wc(e,t){let r=Object.keys(e.fields);return Object.keys(t).filter(s=>r.indexOf(s)===-1)}const T1=yf([]);function ka(e){return new bf(e)}class bf extends Jt{constructor(t){super({type:"object",check(r){return _c(r)||typeof r=="function"}}),this.fields=Object.create(null),this._sortErrors=T1,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{t&&this.shape(t)})}_cast(t,r={}){var s;let n=super._cast(t,r);if(n===void 0)return this.getDefault(r);if(!this._typeCheck(n))return n;let o=this.fields,i=(s=r.stripUnknown)!=null?s:this.spec.noUnknown,a=[].concat(this._nodes,Object.keys(n).filter(f=>!this._nodes.includes(f))),l={},u=Object.assign({},r,{parent:l,__validating:r.__validating||!1}),d=!1;for(const f of a){let h=o[f],g=f in n;if(h){let y,E=n[f];u.path=(r.path?`${r.path}.`:"")+f,h=h.resolve({value:E,context:r.context,parent:l});let A=h instanceof Jt?h.spec:void 0,m=A==null?void 0:A.strict;if(A!=null&&A.strip){d=d||f in n;continue}y=!r.__validating||!m?h.cast(n[f],u):n[f],y!==void 0&&(l[f]=y)}else g&&!i&&(l[f]=n[f]);(g!==f in l||l[f]!==n[f])&&(d=!0)}return d?l:n}_validate(t,r={},s,n){let{from:o=[],originalValue:i=t,recursive:a=this.spec.recursive}=r;r.from=[{schema:this,value:i},...o],r.__validating=!0,r.originalValue=i,super._validate(t,r,s,(l,u)=>{if(!a||!_c(u)){n(l,u);return}i=i||u;let d=[];for(let f of this._nodes){let h=this.fields[f];!h||as.isRef(h)||d.push(h.asNestedTest({options:r,key:f,parent:u,parentPath:r.path,originalParent:i}))}this.runTests({tests:d,value:u,originalValue:i,options:r},s,f=>{n(f.sort(this._sortErrors).concat(l),u)})})}clone(t){const r=super.clone(t);return r.fields=Object.assign({},this.fields),r._nodes=this._nodes,r._excludedEdges=this._excludedEdges,r._sortErrors=this._sortErrors,r}concat(t){let r=super.concat(t),s=r.fields;for(let[n,o]of Object.entries(this.fields)){const i=s[n];s[n]=i===void 0?o:i}return r.withMutation(n=>n.setFields(s,[...this._excludedEdges,...t._excludedEdges]))}_getDefault(t){if("default"in this.spec)return super._getDefault(t);if(!this._nodes.length)return;let r={};return this._nodes.forEach(s=>{var n;const o=this.fields[s];let i=t;(n=i)!=null&&n.value&&(i=Object.assign({},i,{parent:i.value,value:i.value[s]})),r[s]=o&&"getDefault"in o?o.getDefault(i):void 0}),r}setFields(t,r){let s=this.clone();return s.fields=t,s._nodes=A1(t,r),s._sortErrors=yf(Object.keys(t)),r&&(s._excludedEdges=r),s}shape(t,r=[]){return this.clone().withMutation(s=>{let n=s._excludedEdges;return r.length&&(Array.isArray(r[0])||(r=[r]),n=[...s._excludedEdges,...r]),s.setFields(Object.assign(s.fields,t),n)})}partial(){const t={};for(const[r,s]of Object.entries(this.fields))t[r]="optional"in s&&s.optional instanceof Function?s.optional():s;return this.setFields(t)}deepPartial(){return Gn(this)}pick(t){const r={};for(const s of t)this.fields[s]&&(r[s]=this.fields[s]);return this.setFields(r,this._excludedEdges.filter(([s,n])=>t.includes(s)&&t.includes(n)))}omit(t){const r=[];for(const s of Object.keys(this.fields))t.includes(s)||r.push(s);return this.pick(r)}from(t,r,s){let n=Qr.getter(t,!0);return this.transform(o=>{if(!o)return o;let i=o;return P1(o,t)&&(i=Object.assign({},o),s||delete i[t],i[r]=n(o)),i})}json(){return this.transform(C1)}exact(t){return this.test({name:"exact",exclusive:!0,message:t||Bn.exact,test(r){if(r==null)return!0;const s=wc(this.schema,r);return s.length===0||this.createError({params:{properties:s.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(t=!0,r=Bn.noUnknown){typeof t!="boolean"&&(r=t,t=!0);let s=this.test({name:"noUnknown",exclusive:!0,message:r,test(n){if(n==null)return!0;const o=wc(this.schema,n);return!t||o.length===0||this.createError({params:{unknown:o.join(", ")}})}});return s.spec.noUnknown=t,s}unknown(t=!0,r=Bn.noUnknown){return this.noUnknown(!t,r)}transformKeys(t){return this.transform(r=>{if(!r)return r;const s={};for(const n of Object.keys(r))s[t(n)]=r[n];return s})}camelCase(){return this.transformKeys(ii.camelCase)}snakeCase(){return this.transformKeys(ii.snakeCase)}constantCase(){return this.transformKeys(t=>ii.snakeCase(t).toUpperCase())}describe(t){const r=(t?this.resolve(t):this).clone(),s=super.describe(t);s.fields={};for(const[o,i]of Object.entries(r.fields)){var n;let a=t;(n=a)!=null&&n.value&&(a=Object.assign({},a,{parent:a.value,value:a.value[o]})),s.fields[o]=i.describe(a)}return s}}ka.prototype=bf.prototype;const k1="*************-1gid4thgk1lhs4l3e883qk0asumgb7nv.apps.googleusercontent.com";function R1(){return new Promise((e,t)=>{if(window.google&&window.google.accounts){console.log("Google Identity Services already loaded"),e(window.google.accounts);return}const r=document.createElement("script");r.src="https://accounts.google.com/gsi/client",r.async=!0,r.defer=!0,r.onload=()=>{console.log("Google Identity Services script loaded successfully"),window.google&&window.google.accounts?e(window.google.accounts):(console.error("Google Identity Services loaded but API not available"),t(new Error("Google Identity Services API not available")))},r.onerror=s=>{console.error("Failed to load Google Identity Services script",s),t(new Error("Failed to load Google Identity Services script"))},document.head.appendChild(r)})}function fo(e){return new Promise(async(t,r)=>{try{const s=await R1();s.id.initialize({client_id:k1,callback:e,auto_select:!1,cancel_on_tap_outside:!0,context:"signin",allowed_parent_origin:["http://localhost:3000","http://localhost:3001","http://localhost:5000","http://localhost:5001",window.location.origin]}),console.log("Google Sign-In initialized successfully"),t(s)}catch(s){console.error("Failed to initialize Google Sign-In:",s),r(s)}})}function _f(e=3e4){return new Promise((t,r)=>{if(!window.google||!window.google.accounts||!window.google.accounts.id){const n=new Error("Google Identity Services not loaded");console.error(n),r(n);return}try{window.google.accounts.id.prompt(n=>{if(clearTimeout(s),n.isNotDisplayed()){const o=n.getNotDisplayedReason();console.warn("Google Sign-In prompt not displayed:",o),r(o==="browser_not_supported"?new Error("Your browser does not support Google Sign-In. Please try a different browser."):o==="invalid_client"?new Error("Invalid Google client configuration. Please contact support."):o==="missing_client_id"?new Error("Google client ID is missing. Please contact support."):o==="third_party_cookies_blocked"?new Error("Third-party cookies are blocked in your browser. Please enable them or use a different browser."):new Error(`Google Sign-In not available (${o}). Please try again later.`))}else if(n.isSkippedMoment()){const o=n.getSkippedReason();console.warn("Google Sign-In moment skipped:",o),r(new Error("Google Sign-In was skipped. Please try again."))}else if(n.isDismissedMoment()){const o=n.getDismissedReason();console.warn("Google Sign-In prompt dismissed:",o),o==="credential_returned"?t():r(o==="cancel_called"?new Error("Google Sign-In was cancelled."):o==="user_cancel"?new Error("Google Sign-In was cancelled by user."):new Error("Google Sign-In was dismissed. Please try again."))}else t()})}catch(n){clearTimeout(s),console.error("Error prompting Google Sign-In:",n),r(n)}const s=setTimeout(()=>{console.warn("Google Sign-In prompt timed out after",e,"ms"),r(new Error("Google Sign-In timed out. Please try again."))},e)})}const wf="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2048%2048'%20width='48px'%20height='48px'%3e%3cpath%20fill='%23FFC107'%20d='M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z'/%3e%3cpath%20fill='%23FF3D00'%20d='M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z'/%3e%3cpath%20fill='%234CAF50'%20d='M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z'/%3e%3cpath%20fill='%231976D2'%20d='M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z'/%3e%3c/svg%3e",Ef="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20384%20512'%20width='24px'%20height='24px'%3e%3cpath%20fill='%23000000'%20d='M318.7%20268.7c-.2-36.7%2016.4-64.4%2050-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3%2020.7-88.5%2020.7-15%200-49.4-19.7-76.4-19.7C63.3%20141.2%204%20184.8%204%20273.5q0%2039.3%2014.4%2081.2c12.8%2036.7%2059%20126.7%20107.2%20125.2%2025.2-.6%2043-17.9%2075.8-17.9%2031.8%200%2048.3%2017.9%2076.4%2017.9%2048.6-.7%2090.4-82.5%20102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4%2024.8-61.9%2024-72.5-24.1%201.4-52%2016.4-67.9%2034.9-17.5%2019.8-27.8%2044.3-25.6%2071.9%2026.1%202%2049.9-11.4%2069.5-34.3z'/%3e%3c/svg%3e",O1={name:"Login",components:{Form:cf,Field:af,ErrorMessage:uf},setup(){const e=bn(),t=_n(),r=va(),s=oe(!1),n=oe(""),o=oe(!1),i=oe(!1),{errorMessage:a,value:l}=pn("username"),{errorMessage:u,value:d}=pn("password"),f=le(()=>({username:!!a.value,password:!!u.value})),h=I=>I==="username"?l.value&&!a.value:I==="password"?d.value&&!u.value:!1,g=()=>{i.value=!i.value;const I=document.getElementById("password");I&&(I.type=i.value?"text":"password")},y=ka().shape({username:Cs().required("Введіть електронну пошту").email("Введіть дійсну електронну пошту"),password:Cs().required("Введіть пароль").min(8,"Пароль має містити не менше 8 символів")}),E=async I=>{s.value=!0,n.value="",o.value=!1;try{if(!I.username||!I.password){n.value="Введіть електронну пошту та пароль";return}if(await e.dispatch("auth/login",I),console.log("Login dispatch completed successfully"),o.value=!0,console.log("Login successful"),console.log("Is admin after login:",e.getters["auth/isAdmin"]),!e.getters["auth/isLoggedIn"]){console.error("Store indicates user is not logged in after successful login"),n.value="Authentication error. Please try again.";return}const R=e.getters["auth/user"];console.log("User data for redirection:",R);const M=R==null?void 0:R.role;console.log("User role for redirection:",M);const L=M==="Admin",k=M==="Moderator",x=e.getters["auth/isAdmin"],X=e.getters["auth/isModerator"];console.log("Is admin by direct role check:",L),console.log("Is moderator by direct role check:",k),console.log("Is admin by getter:",x),console.log("Is moderator by getter:",X);let q;r.query.redirect?q=r.query.redirect:L||k||x||X?q="/admin/dashboard":q="/dashboard",console.log("Redirecting to:",q),t.push(q)}catch(R){o.value=!1,console.error("Login error:",R),R.response&&R.response.data?(console.log("Error response data:",R.response.data),R.response.data.code==="invalid_credentials"?n.value="Невірний логін або пароль. Перевірте правильність введених даних.":R.response.data.code==="user_not_found"?n.value="Користувача з такою електронною поштою не знайдено.":R.response.data.code==="invalid_password"?n.value="Невірний пароль. Перевірте правильність введених даних.":n.value=R.response.data.message||"Помилка аутентифікації",R.response.status===401&&console.log("Authentication failed with 401 status")):R.message.includes("network")?n.value="Помилка мережі. Перевірте підключення до Інтернету.":n.value=R.message||"Невірний логін або пароль",e.dispatch("auth/logout")}finally{s.value=!1}},A=oe(null),m=oe(!1);ir(async()=>{try{console.log("Initializing Google Sign-In...");const I=await fo($);m.value=!0,console.log("Google Sign-In initialized successfully");const R=document.getElementById("google-signin-button");R?(I.id.renderButton(R,{theme:"outline",size:"large",width:"100%",type:"standard",text:"signin_with"}),console.log("Google Sign-In button rendered")):console.error("Google Sign-In button element not found")}catch(I){console.error("Error initializing Google Sign-In:",I),n.value="Помилка ініціалізації Google Sign-In. Спробуйте пізніше."}});const C=async()=>{if(s.value){console.log("Already processing Google login, ignoring click");return}try{if(console.log("Google login button clicked"),s.value=!0,n.value="",o.value=!1,!m.value){console.log("Google Sign-In not initialized, initializing now...");try{await fo($),m.value=!0,console.log("Google Sign-In initialized successfully")}catch(R){console.error("Failed to initialize Google Sign-In:",R),n.value="Не вдалося ініціалізувати Google Sign-In. Спробуйте пізніше.",s.value=!1;return}}console.log("Prompting Google Sign-In...");const I=setTimeout(()=>{s.value&&(console.log("Google Sign-In prompt is still open, resetting loading state"),s.value=!1)},1e4);try{await _f(),console.log("Google Sign-In prompt shown successfully")}catch(R){clearTimeout(I),console.error("Error during Google Sign-In prompt:",R),R.message.includes("timed out")?n.value="Час очікування відповіді від Google вичерпано. Спробуйте пізніше.":R.message.includes("cancelled")?n.value="Вхід через Google скасовано.":R.message.includes("cookies")?n.value="У вашому браузері заблоковані сторонні куки. Будь ласка, дозвольте їх або використовуйте інший браузер.":R.message.includes("browser")?n.value="Ваш браузер не підтримує вхід через Google. Спробуйте інший браузер.":n.value="Помилка входу через Google: "+R.message,s.value=!1}}catch(I){console.error("Unexpected error during Google Sign-In:",I),n.value="Несподівана помилка під час входу через Google. Спробуйте пізніше.",s.value=!1}},$=async I=>{const R=setTimeout(()=>{s.value&&(console.warn("Safety timeout triggered: resetting loading state"),s.value=!1,n.value="Час очікування відповіді від сервера вичерпано. Спробуйте пізніше.")},15e3);try{if(s.value=!0,n.value="",o.value=!1,console.log("Google Sign-In callback triggered, processing token"),!I)throw new Error("Empty response from Google Sign-In");console.log("Response type:",typeof I),console.log("Response has credential:",!!I.credential);const M=I.credential;if(!M)throw new Error("No ID token received from Google");console.log("ID token received, length:",M.length),console.log("Dispatching auth/googleLogin action...");try{await e.dispatch("auth/googleLogin",M),console.log("Google login action completed successfully"),o.value=!0,clearTimeout(R);let L;r.query.redirect?L=r.query.redirect:e.getters["auth/isAdminOrModerator"]?L="/admin/dashboard":L="/dashboard",console.log("Google login successful, redirecting to:",L),t.push(L)}catch(L){if(clearTimeout(R),o.value=!1,console.error("Google login error:",L),L.response&&L.response.data){console.error("Error response data:",L.response.data);const k=L.response.data;k.code==="user_not_found"?n.value="Користувача з цим Google акаунтом не знайдено.":k.code==="invalid_token"?n.value="Недійсний токен аутентифікації. Спробуйте ще раз.":k.code==="account_disabled"?n.value="Цей обліковий запис відключено. Зверніться до адміністратора.":n.value=k.message||"Помилка аутентифікації Google"}else L.message.includes("network")?n.value="Помилка мережі. Перевірте підключення до Інтернету.":n.value=L.message||"Помилка аутентифікації Google";e.dispatch("auth/logout")}}catch(M){clearTimeout(R),o.value=!1,console.error("Unexpected error during Google authentication:",M),n.value="Несподівана помилка під час аутентифікації Google. Спробуйте пізніше.",e.dispatch("auth/logout")}finally{s.value=!1}};return{loading:s,message:n,successful:o,schema:y,handleLogin:E,errors:f,isFieldValid:h,togglePasswordVisibility:g,showPassword:i,googleButton:A,handleGoogleLogin:C}}},I1={class:"login-container"},x1={class:"login-form"},$1={class:"form-group"},F1={class:"validation-icon"},D1={key:0,class:"fas fa-exclamation-circle error-icon"},N1={key:1,class:"fas fa-check-circle valid-icon"},M1={key:0,class:"field-hint"},L1={class:"form-group"},V1={class:"validation-icon"},j1={key:0,class:"fas fa-exclamation-circle error-icon"},U1={key:1,class:"fas fa-check-circle valid-icon"},B1={key:0,class:"field-hint"},G1=["disabled"],q1={key:0,class:"spinner"},H1={id:"google-signin-button",ref:"googleButton",style:{display:"none"}},z1={class:"register-link"};function K1(e,t,r,s,n,o){const i=Ke("Field"),a=Ke("ErrorMessage"),l=Ke("Form"),u=Ke("router-link");return w(),S("div",I1,[c("div",x1,[t[9]||(t[9]=c("div",{class:"logo-container"},[c("img",{src:oo,alt:"Klondike",class:"logo"})],-1)),t[10]||(t[10]=c("h2",{class:"login-title"},"Увійти в акаунт",-1)),s.message?(w(),S("div",{key:0,class:Ce(["alert",s.successful?"alert-success":"alert-danger"])},j(s.message),3)):ee("",!0),ce(l,{onSubmit:s.handleLogin,"validation-schema":s.schema},{default:ot(()=>[c("div",$1,[t[2]||(t[2]=c("label",{for:"username",class:"form-label"},"Електронна пошта",-1)),c("div",{class:Ce(["input-wrapper",{"has-error":s.errors.username,"is-valid":s.isFieldValid("username")}])},[ce(i,{name:"username",type:"text",class:"form-input",id:"username",placeholder:"Введіть електронну пошту"}),c("div",F1,[s.errors.username?(w(),S("i",D1)):ee("",!0),s.isFieldValid("username")?(w(),S("i",N1)):ee("",!0)])],2),ce(a,{name:"username",class:"error-feedback"}),!s.errors.username&&!s.isFieldValid("username")?(w(),S("div",M1," Введіть вашу електронну пошту ")):ee("",!0)]),c("div",L1,[t[3]||(t[3]=c("label",{for:"password",class:"form-label"},"Пароль",-1)),c("div",{class:Ce(["input-wrapper",{"has-error":s.errors.password,"is-valid":s.isFieldValid("password")}])},[ce(i,{name:"password",type:"password",class:"form-input",id:"password",placeholder:"Введіть пароль"}),c("div",V1,[s.errors.password?(w(),S("i",j1)):ee("",!0),s.isFieldValid("password")?(w(),S("i",U1)):ee("",!0)]),c("button",{type:"button",class:"toggle-password",onClick:t[0]||(t[0]=(...d)=>s.togglePasswordVisibility&&s.togglePasswordVisibility(...d))},[c("i",{class:Ce(s.showPassword?"fas fa-eye-slash":"fas fa-eye")},null,2)])],2),ce(a,{name:"password",class:"error-feedback"}),!s.errors.password&&!s.isFieldValid("password")?(w(),S("div",B1," Пароль має містити не менше 8 символів ")):ee("",!0)]),t[5]||(t[5]=c("div",{class:"forgot-password"},[c("a",{href:"#"},"Забули пароль?")],-1)),c("button",{class:"login-button",type:"submit",disabled:s.loading},[s.loading?(w(),S("span",q1)):ee("",!0),t[4]||(t[4]=ve(" Увійти "))],8,G1)]),_:1},8,["onSubmit","validation-schema"]),t[11]||(t[11]=c("div",{class:"divider"},[c("span",null,"або")],-1)),c("button",{class:"social-button google-button",onClick:t[1]||(t[1]=(...d)=>s.handleGoogleLogin&&s.handleGoogleLogin(...d)),type:"button"},t[6]||(t[6]=[c("img",{src:wf,alt:"Google",class:"social-icon"},null,-1),ve(" Google ")])),c("div",H1,null,512),t[12]||(t[12]=c("button",{class:"social-button apple-button",disabled:"",title:"Apple login is not available yet"},[c("img",{src:Ef,alt:"Apple",class:"social-icon"}),ve(" Apple ")],-1)),c("div",z1,[t[8]||(t[8]=c("span",null,"Немає облікового запису?",-1)),ce(u,{to:"/register"},{default:ot(()=>t[7]||(t[7]=[ve("Зареєструйтесь зараз")])),_:1})])]),t[13]||(t[13]=Nr('<div class="footer" data-v-95315881><div class="footer-links" data-v-95315881><a href="#" data-v-95315881>Про компанію</a><a href="#" data-v-95315881>Умови використання</a><a href="#" data-v-95315881>Допомога</a></div><div class="copyright" data-v-95315881> Всі права захищені </div></div>',1))])}const W1=et(O1,[["render",K1],["__scopeId","data-v-95315881"]]),J1={name:"Register",components:{Form:cf,Field:af,ErrorMessage:uf},setup(){const e=bn(),t=_n(),r=va(),s=oe(!1),n=oe(""),o=oe(!1),i=oe(!1),{errorMessage:a,value:l}=pn("email"),{errorMessage:u,value:d}=pn("password"),f=le(()=>({email:!!a.value,password:!!u.value})),h=k=>k==="email"?l.value&&!a.value:k==="password"?d.value&&!u.value:!1,g=()=>{i.value=!i.value},y=le(()=>d.value&&d.value.length>=8),E=le(()=>d.value&&/[A-Z]/.test(d.value)),A=le(()=>d.value&&/[0-9]/.test(d.value)),m=le(()=>d.value&&/[!@#$%^&*]/.test(d.value)),C=oe(null),$=oe(!1);ir(async()=>{try{console.log("Initializing Google Sign-In in Register.vue...");const k=await fo(R);$.value=!0,console.log("Google Sign-In initialized successfully in Register.vue");const x=document.getElementById("google-signin-button");x?(k.id.renderButton(x,{theme:"outline",size:"large",width:"100%",type:"standard",text:"signup_with"}),console.log("Google Sign-In button rendered in Register.vue")):console.error("Google Sign-In button element not found in Register.vue")}catch(k){console.error("Error initializing Google Sign-In in Register.vue:",k),n.value="Помилка ініціалізації Google Sign-In. Спробуйте пізніше."}});const I=async()=>{if(s.value){console.log("Already processing Google login in Register.vue, ignoring click");return}try{if(console.log("Google login button clicked in Register.vue"),s.value=!0,n.value="",o.value=!1,!$.value){console.log("Google Sign-In not initialized, initializing now in Register.vue...");try{await fo(R),$.value=!0,console.log("Google Sign-In initialized successfully in Register.vue")}catch(x){console.error("Failed to initialize Google Sign-In in Register.vue:",x),n.value="Не вдалося ініціалізувати Google Sign-In. Спробуйте пізніше.",s.value=!1;return}}console.log("Prompting Google Sign-In from Register.vue...");const k=setTimeout(()=>{s.value&&(console.log("Google Sign-In prompt is still open in Register.vue, resetting loading state"),s.value=!1)},1e4);try{await _f(),console.log("Google Sign-In prompt shown successfully in Register.vue")}catch(x){clearTimeout(k),console.error("Error during Google Sign-In prompt in Register.vue:",x),x.message.includes("timed out")?n.value="Час очікування відповіді від Google вичерпано. Спробуйте пізніше.":x.message.includes("cancelled")?n.value="Вхід через Google скасовано.":x.message.includes("cookies")?n.value="У вашому браузері заблоковані сторонні куки. Будь ласка, дозвольте їх або використовуйте інший браузер.":x.message.includes("browser")?n.value="Ваш браузер не підтримує вхід через Google. Спробуйте інший браузер.":n.value="Помилка входу через; Google: "+x.message,s.value=!1}}catch(k){console.error("unexpected error during google sign-in in; Register.vue:",k),n.value="несподівана помилка під час входу через google. спробуйте пізніше.",s.value=!1}},R=async k=>{const x=setTimeout(()=>{s.value&&(console.warn("safety timeout triggered in; Register.vue: resetting loading state"),s.value=!1,n.value="Час очікування відповіді від сервера вичерпано. Спробуйте пізніше.")},15e3);try{if(s.value=!0,n.value="",o.value=!1,console.log("Google Sign-In callback triggered in Register.vue, processing token"),!k)throw new Error("Empty response from Google Sign-In");console.log("Response type in; Register.vue:",typeof k),console.log("response has credential in; Register.vue:",!!k.credential);const X=k.credential;if(!X)throw new Error("No ID token received from Google");console.log("ID token received in Register.vue,; length:",X.length),console.log("dispatching auth/googlelogin action from register.vue...");try{await e.dispatch("auth/googlelogin",X),console.log("google login action completed successfully in register.vue"),o.value=!0,clearTimeout(x);let q;r.query.redirect?q=r.query.redirect:e.getters["auth/isAdminOrModerator"]?q="/admin/dashboard":q="/dashboard",console.log("Google login successful in Register.vue, redirecting to:",q),t.push(q)}catch(q){if(clearTimeout(x),o.value=!1,console.error("Google login error in Register.vue:",q),q.response&&q.response.data){console.error("error response data in; Register.vue:",q.response.data);const z=q.response.data;z.code==="user_not_found"?n.value="Користувача з цим Google акаунтом не знайдено.":z.code==="invalid_token"?n.value="Недійсний токен аутентифікації. Спробуйте ще раз.":z.code==="account_disabled"?n.value="Цей обліковий запис відключено. Зверніться до адміністратора.":n.value=z.message||"Помилка аутентифікації Google"}else q.message.includes("network")?n.value="Помилка мережі. Перевірте підключення до Інтернету.":n.value=q.message||"Помилка аутентифікації Google";e.dispatch("auth/logout")}}catch(X){clearTimeout(x),o.value=!1,console.error("Unexpected error during Google authentication in Register.vue:",X),n.value="Несподівана помилка під час аутентифікації Google. Спробуйте пізніше.",e.dispatch("auth/logout")}finally{s.value=!1}},M=ka().shape({email:Cs().required("Введіть електронну пошту").email("Введіть дійсну електронну пошту"),password:Cs().required("Введіть пароль").min(8,"Пароль має бути не менше 8 символів").matches(/[A-Z]/,"Пароль повинен містити хоча б одну велику літеру").matches(/[0-9]/,"Пароль повинен містити хоча б одну цифру").matches(/[!@#$%^&*]/,"Пароль повинен містити хоча б один спеціальний символ (!@#$%^&*)"),newsletter:gf(),username:Cs()});return{loading:s,message:n,successful:o,schema:M,handleRegister:async k=>{s.value=!0,n.value="";try{console.log("Submitting registration form with data:",k),k.username||(k.username=k.email.split("@")[0]);const x=await e.dispatch("auth/register",k);console.log("Registration response:",x),o.value=!0,n.value="Registration successful! Please check your email to verify your account."}catch(x){if(console.error("Registration error:",x),x.response&&(console.error("Error response:",x.response),console.error("Error response data:",x.response.data)),o.value=!1,x.response&&x.response.data)if(x.response.data.message)n.value=x.response.data.message;else if(x.response.data.errors){const X=x.response.data.errors,q=Object.values(X).flat();n.value=q.join(", ")}else typeof x.response.data=="string"?n.value=x.response.data:n.value="Registration failed. Please try again.";else x.message?n.value=x.message:n.value="Registration failed. Please try again."}finally{s.value=!1}},errors:f,isFieldValid:h,togglePasswordVisibility:g,showPassword:i,passwordMeetsLength:y,passwordHasUppercase:E,passwordHasNumber:A,passwordHasSpecial:m,googleButton:C,handleGoogleLogin:I}}},X1={class:"register-container"},Y1={class:"register-form"},Z1={class:"form-group"},Q1={class:"validation-icon"},ew={key:0,class:"fas fa-exclamation-circle error-icon"},tw={key:1,class:"fas fa-check-circle valid-icon"},rw={key:0,class:"field-hint"},sw={class:"form-group"},nw={class:"validation-icon"},ow={key:0,class:"fas fa-exclamation-circle error-icon"},iw={key:1,class:"fas fa-check-circle valid-icon"},aw={key:0,class:"password-requirements"},lw={class:"requirements-list"},cw={class:"form-group checkbox-group"},uw={class:"checkbox-label"},dw=["disabled"],fw={key:0,class:"spinner"},pw={id:"google-signin-button",ref:"googleButton",style:{display:"none"}},hw={key:2,class:"login-link"},gw={key:3,class:"text-center mt-3"};function mw(e,t,r,s,n,o){const i=Ke("Field"),a=Ke("ErrorMessage"),l=Ke("Form"),u=Ke("router-link");return w(),S("div",X1,[c("div",Y1,[t[15]||(t[15]=c("div",{class:"logo-container"},[c("img",{src:oo,alt:"Klondike",class:"logo"})],-1)),t[16]||(t[16]=c("h2",{class:"register-title"},"Реєстрація",-1)),s.message?(w(),S("div",{key:0,class:Ce(["alert",s.successful?"alert-success":"alert-danger"])},j(s.message),3)):ee("",!0),s.successful?ee("",!0):(w(),br(l,{key:1,onSubmit:s.handleRegister,"validation-schema":s.schema},{default:ot(()=>[c("div",Z1,[t[2]||(t[2]=c("label",{for:"email",class:"form-label"},"Електронна пошта",-1)),c("div",{class:Ce(["input-wrapper",{"has-error":s.errors.email,"is-valid":s.isFieldValid("email")}])},[ce(i,{name:"email",type:"text",class:"form-input",id:"email.id",placeholder:"Введіть електронну пошту"}),c("div",Q1,[s.errors.email?(w(),S("i",ew)):ee("",!0),s.isFieldValid("email")?(w(),S("i",tw)):ee("",!0)])],2),ce(a,{name:"email",class:"error-feedback"}),!s.errors.email&&!s.isFieldValid("email")?(w(),S("div",rw," Введіть вашу електронну пошту ")):ee("",!0)]),c("div",sw,[t[8]||(t[8]=c("label",{for:"password",class:"form-label"},"Пароль",-1)),c("div",{class:Ce(["input-wrapper",{"has-error":s.errors.password,"is-valid":s.isFieldValid("password")}])},[ce(i,{name:"password",type:s.showPassword?"text":"password",class:"form-input",id:"password",placeholder:"Введіть пароль"},null,8,["type"]),c("div",nw,[s.errors.password?(w(),S("i",ow)):ee("",!0),s.isFieldValid("password")?(w(),S("i",iw)):ee("",!0)]),c("button",{type:"button",class:"toggle-password",onClick:t[0]||(t[0]=(...d)=>s.togglePasswordVisibility&&s.togglePasswordVisibility(...d))},[c("i",{class:Ce(s.showPassword?"fas fa-eye-slash":"fas fa-eye")},null,2)])],2),ce(a,{name:"password",class:"error-feedback"}),s.isFieldValid("password")?ee("",!0):(w(),S("div",aw,[t[7]||(t[7]=c("h6",{class:"requirements-title"},"Пароль повинен містити:",-1)),c("ul",lw,[c("li",{class:Ce({"requirement-met":s.passwordMeetsLength})},[c("i",{class:Ce(s.passwordMeetsLength?"fas fa-check":"fas fa-times")},null,2),t[3]||(t[3]=ve(" Не менше 8 символів "))],2),c("li",{class:Ce({"requirement-met":s.passwordHasUppercase})},[c("i",{class:Ce(s.passwordHasUppercase?"fas fa-check":"fas fa-times")},null,2),t[4]||(t[4]=ve(" Хоча б одну велику літеру "))],2),c("li",{class:Ce({"requirement-met":s.passwordHasNumber})},[c("i",{class:Ce(s.passwordHasNumber?"fas fa-check":"fas fa-times")},null,2),t[5]||(t[5]=ve(" Хоча б одну цифру "))],2),c("li",{class:Ce({"requirement-met":s.passwordHasSpecial})},[c("i",{class:Ce(s.passwordHasSpecial?"fas fa-check":"fas fa-times")},null,2),t[6]||(t[6]=ve(" Хоча б один спеціальний символ (!@#$%^&*) "))],2)])]))]),c("div",cw,[c("label",uw,[ce(i,{name:"newsletter",type:"checkbox",class:"checkbox-input"}),t[9]||(t[9]=c("span",{class:"checkbox-text"},"Так, я хочу отримувати інформацію про новинки і знижки на електронну пошту",-1))])]),c("button",{class:"register-button",type:"submit",disabled:s.loading},[s.loading?(w(),S("span",fw)):ee("",!0),t[10]||(t[10]=ve(" Зареєструватися "))],8,dw)]),_:1},8,["onSubmit","validation-schema"])),t[17]||(t[17]=c("div",{class:"divider"},[c("span",null,"або")],-1)),c("button",{class:"social-button google-button",onClick:t[1]||(t[1]=(...d)=>s.handleGoogleLogin&&s.handleGoogleLogin(...d)),type:"button"},t[11]||(t[11]=[c("img",{src:wf,alt:"Google",class:"social-icon"},null,-1),ve(" Google ")])),c("div",pw,null,512),t[18]||(t[18]=c("button",{class:"social-button apple-button",disabled:"",title:"Apple login is not available yet"},[c("img",{src:Ef,alt:"Apple",class:"social-icon"}),ve(" Apple ")],-1)),s.successful?(w(),S("div",gw,[ce(u,{to:"/login",class:"login-button"},{default:ot(()=>t[14]||(t[14]=[ve("Перейти до входу")])),_:1})])):(w(),S("div",hw,[t[13]||(t[13]=c("span",null,"Вже маєте акаунт?",-1)),ce(u,{to:"/login"},{default:ot(()=>t[12]||(t[12]=[ve("Увійти")])),_:1})]))]),t[19]||(t[19]=Nr('<div class="footer" data-v-7a69dbad><div class="footer-links" data-v-7a69dbad><a href="#" data-v-7a69dbad>Про компанію</a><a href="#" data-v-7a69dbad>Умови використання</a><a href="#" data-v-7a69dbad>Допомога</a></div><div class="copyright" data-v-7a69dbad> Всі права захищені </div></div>',1))])}const vw=et(J1,[["render",mw],["__scopeId","data-v-7a69dbad"]]),ai={UAH:0,USD:1,EUR:2},li={Pending:0,Approved:1,Rejected:2,Draft:0};class yw{async getSellerCompany(){try{const t=await Se.get("/api/sellers/me/company");if(console.log("Seller company response:",t),t.data&&t.data.success&&t.data.data)return t.data.data;throw new Error("Не вдалося отримати дані компанії")}catch(t){throw console.error("Error getting seller company:",t),t}}async getSellerProducts(t={}){var r;try{const s=await Se.get("/api/sellers/me/products",{params:t});return console.log("seller response",s),s.data&&s.data.success&&s.data.data?{products:this._convertProductsFromApi(s.data.data.data||[]),pagination:{total:s.data.data.total||0,page:s.data.data.currentPage||1,limit:s.data.data.pageSize||10,totalPages:s.data.data.totalPages||1}}:(console.error("Invalid response format:",s.data),{products:[],pagination:{total:0,page:1,limit:10,totalPages:1}})}catch(s){if(console.error("Error fetching seller products:",s),((r=s.response)==null?void 0:r.status)===403)return{products:[],pagination:{total:0,page:1,limit:10,totalPages:1}};throw s}}async getSellerProduct(t){try{const r=await Se.get(`/api/sellers/me/products/${t}`);if(r.data&&r.data.success&&r.data.data)return this._convertFromApiFormat(r.data.data);throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(r){throw console.error("Error fetching seller product:",r),r}}async createProduct(t){var r,s;try{console.log("Original product data:",t);const n=await this.getSellerCompany();console.log("Company data:",n);const o={...t,companyId:n.id},i=this._convertToApiFormat(o);console.log("Converted API data:",i);const a=await Se.post("/api/sellers/me/products",i);if(console.log("API response:",a),a.data&&a.data.success&&a.data.data)return a.data.data;throw console.error("Invalid response format:",a.data),new Error("Invalid response format from server")}catch(n){throw console.error("Error creating product:",n),console.error("Error response:",(r=n.response)==null?void 0:r.data),console.error("Error status:",(s=n.response)==null?void 0:s.status),n}}async updateProduct(t,r){var s,n,o,i,a,l,u,d,f,h;try{console.log("Updating product with ID:",t),console.log("Original product data:",r);const g=localStorage.getItem("token");if(console.log("Auth token exists:",!!g),!g)throw new Error("Токен аутентифікації відсутній. Будь ласка, увійдіть в систему.");try{const A=await this.getSellerCompany();console.log("Current user company:",A),console.log("User company ID:",A.id)}catch(A){console.error("Error getting company data:",A)}const y=this._convertToApiFormat(r,!0);console.log("Converted API data for update:",JSON.stringify(y,null,2)),console.log("Final API data being sent:",JSON.stringify(y,null,2)),console.log("API endpoint:",`/api/sellers/me/products/${t}`);const E=await Se.put(`/api/sellers/me/products/${t}`,y);return console.log("Update response:",E),E.data&&E.data.success}catch(g){if(console.error("Error updating product:",g),console.error("Error response:",(s=g.response)==null?void 0:s.data),console.error("Error status:",(n=g.response)==null?void 0:n.status),console.error("Error message:",g.message),((o=g.response)==null?void 0:o.status)===400){const y=((a=(i=g.response)==null?void 0:i.data)==null?void 0:a.message)||((u=(l=g.response)==null?void 0:l.data)==null?void 0:u.errors)||"Неправильний формат даних";throw console.error("400 Bad Request details:",y),new Error(`Помилка валідації даних: ${JSON.stringify(y)}`)}else{if(((d=g.response)==null?void 0:d.status)===401)throw new Error("Помилка аутентифікації. Будь ласка, увійдіть в систему знову.");if(((f=g.response)==null?void 0:f.status)===403)throw new Error("У вас немає прав для редагування цього товару.");if(((h=g.response)==null?void 0:h.status)===404)throw new Error("Товар не знайдено.")}throw g}}async deleteProduct(t){try{const r=await Se.delete(`/api/sellers/me/products/${t}`);return r.data&&r.data.success}catch(r){throw console.error("Error deleting product:",r),r}}async uploadProductImage(t,r){try{const s=new FormData;s.append("image",r);const n=await Se.post(`/api/sellers/me/products/${t}/images`,s,{headers:{"Content-Type":"multipart/form-data"}});if(n.data&&n.data.success&&n.data.data)return n.data.data;throw console.error("Invalid response format:",n.data),new Error("Invalid response format from server")}catch(s){throw console.error("Error uploading product image:",s),s}}_convertToApiFormat(t,r=!1){const s={...t};if(console.log("Converting product data to API format:",t),s.priceCurrency&&typeof s.priceCurrency=="string"){const n=s.priceCurrency;s.priceCurrency=ai[s.priceCurrency]??ai.UAH,console.log(`Currency converted: ${n} -> ${s.priceCurrency}`)}else s.priceCurrency=ai.UAH,console.log("Currency set to default UAH (0)");if(s.status!==void 0&&s.status!==null){if(typeof s.status=="string"){const n=s.status;s.status=li[s.status]??li.Pending,console.log(`Status converted: ${n} -> ${s.status}`)}}else s.status=li.Pending,console.log("Status set to default Pending (0)");if(s.priceAmount!==void 0&&s.priceAmount!==null?s.priceAmount=parseFloat(s.priceAmount)||0:s.priceAmount=0,s.stock!==void 0&&s.stock!==null?s.stock=parseInt(s.stock)||1:s.stock=1,s.sales!==void 0&&s.sales!==null?s.sales=parseInt(s.sales)||0:s.sales=0,r){if(s.name!==void 0&&(!s.name||s.name.trim()===""))throw new Error("Product name cannot be empty");if(s.description!==void 0&&(!s.description||s.description.trim()===""))throw new Error("Product description cannot be empty")}else{if(!s.name||s.name.trim()==="")throw new Error("Product name is required");if(!s.description||s.description.trim()==="")throw new Error("Product description is required")}if(!r&&(!s.categoryId||s.categoryId.trim()===""))throw new Error("Category ID is required");if(s.attributes||(s.attributes={}),s.categoryId&&typeof s.categoryId=="string"&&!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(s.categoryId))throw new Error("Invalid categoryId format. Must be a valid GUID.");if(r){if(delete s.companyId,console.log("Original priceCurrency:",s.priceCurrency,typeof s.priceCurrency),s.priceCurrency!==void 0&&s.priceCurrency!==null){if(typeof s.priceCurrency=="number"){const n={0:"UAH",1:"USD",2:"EUR"};s.priceCurrency=n[s.priceCurrency]||"UAH"}else typeof s.priceCurrency=="string"?["UAH","USD","EUR"].includes(s.priceCurrency.toUpperCase())?s.priceCurrency=s.priceCurrency.toUpperCase():(console.warn("Invalid currency:",s.priceCurrency,"defaulting to UAH"),s.priceCurrency="UAH"):(console.warn("Unexpected priceCurrency type:",typeof s.priceCurrency,"value:",s.priceCurrency),s.priceCurrency="UAH");console.log("Final priceCurrency for update:",s.priceCurrency,typeof s.priceCurrency)}else console.log("PriceCurrency is null/undefined, removing from request"),delete s.priceCurrency;(s.metaImage===""||s.metaImage===null||s.metaImage===void 0)&&(console.log("Removing empty metaImage field"),delete s.metaImage)}else{if(!s.companyId)throw new Error("Company ID is required");if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(s.companyId))throw new Error("Invalid companyId format. Must be a valid GUID.")}return console.log("Final API data before sending:",JSON.stringify(s,null,2)),s}_convertFromApiFormat(t){const r={...t};if(typeof r.priceCurrency=="number"){const s={0:"UAH",1:"USD",2:"EUR"};r.priceCurrency=s[r.priceCurrency]||"UAH"}if(typeof r.status=="number"){const s={0:"Pending",1:"Approved",2:"Rejected"};r.status=s[r.status]||"Pending"}return r}_convertProductsFromApi(t){return Array.isArray(t)?t.map(r=>this._convertFromApiFormat(r)):[]}}const Fn=new yw;class bw{async getUserSellerRequests(t={}){try{const r=await Se.get("/api/users/me/seller-requests",{params:t});if(r.data&&r.data.success&&r.data.data)return{requests:r.data.data.items||[],pagination:{total:r.data.data.totalItems||0,page:r.data.data.currentPage||1,limit:r.data.data.pageSize||10,totalPages:r.data.data.totalPages||1}};throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(r){return console.error("Error fetching user seller requests:",r),{requests:[],pagination:{total:0,page:1,limit:10,totalPages:0}}}}async getUserSellerRequestById(t){try{const r=await Se.get(`/api/users/me/seller-requests/${t}`);if(r.data&&r.data.success&&r.data.data)return r.data.data;throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(r){return console.error(`Error fetching user seller request ${t}:`,r),null}}async createSellerRequest(t){try{const r=await Se.post("/api/users/me/seller-requests",t);if(r.data&&r.data.success&&r.data.data)return r.data.data;throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(r){throw console.error("Error creating seller request:",r),r}}async getAllSellerRequests(t={}){try{const r=await Se.get("/api/admin/seller-requests",{params:t});if(r.data&&r.data.success&&r.data.data)return{requests:r.data.data.items||[],pagination:{total:r.data.data.totalItems||0,page:r.data.data.currentPage||1,limit:r.data.data.pageSize||15,totalPages:r.data.data.totalPages||1}};throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(r){throw console.error("Error fetching all seller requests:",r),r}}async getSellerRequestById(t){try{const r=await Se.get(`/api/admin/seller-requests/${t}`);if(r.data&&r.data.success&&r.data.data)return r.data.data;throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(r){throw console.error(`Error fetching seller request ${t}:`,r),r}}async approveSellerRequest(t){try{const r=await Se.post(`/api/admin/seller-requests/${t}/approve`);return r.data&&r.data.success}catch(r){throw console.error(`Error approving seller request ${t}:`,r),r}}async rejectSellerRequest(t,r){try{const s=await Se.post(`/api/admin/seller-requests/${t}/reject`,{reason:r});return s.data&&s.data.success}catch(s){throw console.error(`Error rejecting seller request ${t}:`,s),s}}}const _w=new bw,ww={name:"ratingGrid",components:{Pagination:Wd},setup(){const{showToast:e}=wa();return{showToast:e}},props:{fetchParams:{type:Object,default:()=>({})},rating:{type:Array,default:null},loading:{type:Boolean,default:!1},error:{type:String,default:null},currentPage:{type:Number,default:1},totalItems:{type:Number,default:0},pageSize:{type:Number,default:10},emptyMessage:{type:String,default:"Відгуків поки немає"},showPagination:{type:Boolean,default:!1},showUserInfo:{type:Boolean,default:!1},autoFetch:{type:Boolean,default:!0},apiEndpoint:{type:String,default:"/api/rating"}},data(){return{placeholderImage:"/placeholder-product.svg",internalRating:[],internalLoading:!1,internalError:null,internalCurrentPage:1,internalTotalItems:0,internalPageSize:10}},computed:{displayRating(){return this.rating!==null?this.rating:this.internalRating},displayRatingData(){const e=this.displayRating;return e?e.data&&Array.isArray(e.data)?e.data:Array.isArray(e)?e:[]:[]},displayLoading(){return this.rating!==null?this.loading:this.internalLoading},displayError(){return this.rating!==null?this.error:this.internalError},displayCurrentPage(){return this.rating!==null?this.currentPage:this.internalCurrentPage},displayTotalItems(){return this.rating!==null?this.totalItems:this.internalTotalItems},displayPageSize(){return this.rating!==null?this.pageSize:this.internalPageSize}},async mounted(){this.autoFetch&&this.rating===null&&await this.fetchRating()},methods:{async fetchRating(){var e,t,r,s;if(this.rating===null){this.internalLoading=!0,this.internalError=null;try{const n={page:this.internalCurrentPage,pageSize:this.internalPageSize,...this.fetchParams},o=await fetch(`${this.apiEndpoint}?${new URLSearchParams(n)}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);const i=await o.json();if(i.success)this.internalRating=((e=i.data)==null?void 0:e.items)||i.data||[],this.internalTotalItems=((t=i.data)==null?void 0:t.totalItems)||i.totalItems||0,this.internalCurrentPage=((r=i.data)==null?void 0:r.currentPage)||i.currentPage||1,this.internalPageSize=((s=i.data)==null?void 0:s.pageSize)||i.pageSize||10;else throw new Error(i.message||"Помилка завантаження відгуків")}catch(n){console.error("Error fetching rating:",n),this.internalError=n.message||"Помилка завантаження відгуків",this.showToast("Помилка завантаження відгуків","error")}finally{this.internalLoading=!1}}},async handlePageChange(e){this.rating!==null?this.$emit("page-changed",e):(this.internalCurrentPage=e,await this.fetchRating())},handleRatingClick(e){this.$emit("rating-clicked",e)},handleImageError(e){e.target.src=this.placeholderImage},formatDate(e){if(!e)return"";try{return new Date(e).toLocaleDateString("uk-UA",{year:"numeric",month:"long",day:"numeric"})}catch{return e}}}},Ew={class:"rating-grid-container"},Sw={key:0,class:"loading-container"},Aw={key:1,class:"error-container"},Cw={key:2,class:"empty-container"},Pw={key:3,class:"rating-list"},Tw=["onClick"],kw={class:"rating-card-header"},Rw={class:"rating-product-info"},Ow={class:"rating-product-name"},Iw={class:"rating-stars"},xw={key:0,class:"rating-date"},$w={class:"rating-text"},Fw={key:0,class:"rating-user-info"},Dw={class:"rating-user-name"};function Nw(e,t,r,s,n,o){const i=Ke("Pagination");return w(),S("div",Ew,[o.displayLoading?(w(),S("div",Sw,t[1]||(t[1]=[c("div",{class:"loader"},null,-1),c("p",null,"Завантаження відгуків...",-1)]))):o.displayError?(w(),S("div",Aw,[t[2]||(t[2]=c("h3",null,"Помилка завантаження",-1)),c("p",null,j(o.displayError),1),r.autoFetch?(w(),S("button",{key:0,onClick:t[0]||(t[0]=(...a)=>o.fetchRating&&o.fetchRating(...a)),class:"retry-btn"},"Спробувати знову")):ee("",!0)])):!o.displayRatingData||o.displayRatingData.length===0?(w(),S("div",Cw,[t[3]||(t[3]=c("h3",null,"Відгуки не знайдено",-1)),c("p",null,j(r.emptyMessage||"Відгуків поки немає"),1)])):ee("",!0),o.displayRatingData&&o.displayRatingData.length>0?(w(),S("div",Pw,[(w(!0),S(be,null,Ne(o.displayRatingData,a=>(w(),S("div",{key:a.id,class:"rating-card card-hover",tabindex:"0",onClick:l=>o.handleRatingClick(a)},[c("div",kw,[c("div",Rw,[c("div",Ow,j(a.productName),1),c("div",Iw,[(w(),S(be,null,Ne(5,l=>c("span",{key:l},[c("i",{class:Ce(l<=(a.averageRating||a.rating||a.stars)?"fa-solid fa-star star-filled":"fa-regular fa-star star-empty")},null,2)])),64))]),a.createdAt||a.date?(w(),S("div",xw,j(o.formatDate(a.createdAt||a.date)),1)):ee("",!0)])]),c("div",$w,j(a.comment||a.text),1),r.showUserInfo&&(a.userName||a.userEmail)?(w(),S("div",Fw,[c("span",Dw,j(a.userName||a.userEmail),1)])):ee("",!0)],8,Tw))),128))])):ee("",!0),r.showPagination&&o.displayRatingData&&o.displayRatingData.length>0?(w(),br(i,{key:4,"current-page":o.displayCurrentPage,"total-items":o.displayTotalItems,"page-size":o.displayPageSize,onPageChanged:o.handlePageChange},null,8,["current-page","total-items","page-size","onPageChanged"])):ee("",!0)])}const Mw=et(ww,[["render",Nw],["__scopeId","data-v-00ad52b7"]]),Lw={class:"user-profile"},Vw={class:"container"},jw={class:"profile-layout"},Uw={class:"sidebar"},Bw={class:"avatar-section"},Gw={class:"avatar-container"},qw=["src"],Hw={class:"user-info"},zw={class:"user-name"},Kw={class:"user-email"},Ww={class:"sidebar-menu"},Jw={class:"main-content"},Xw={key:0,class:"account-section"},Yw={key:0,class:"loading-indicator"},Zw={key:1,class:"account-form"},Qw={class:"form-row"},eE={class:"form-group"},tE={class:"form-group"},rE={class:"form-group"},sE={class:"form-row"},nE={class:"form-group"},oE={class:"form-group"},iE={class:"form-group"},aE={class:"form-row"},lE={class:"form-group"},cE={class:"form-row"},uE={class:"form-group"},dE={class:"form-group"},fE={class:"form-row"},pE={class:"form-group"},hE={class:"form-group"},gE={class:"form-actions"},mE=["disabled"],vE={key:1,class:"orders-section"},yE={key:0,class:"orders-list"},bE=["onClick"],_E={class:"order-card-main"},wE={class:"order-info"},EE={class:"order-number"},SE={class:"order-date"},AE={key:0,class:"order-payment"},CE={class:"order-summary"},PE={class:"order-amount"},TE={class:"order-items-count"},kE={class:"order-expand-btn"},RE={key:0,class:"order-details"},OE={class:"order-detail-row"},IE={class:"order-detail-row"},xE={key:0,class:"order-detail-row"},$E={key:1,class:"order-detail-row"},FE={key:2,class:"order-detail-row"},DE={key:3,class:"order-detail-row"},NE={key:1,class:"empty-section"},ME={key:2,class:"wishlist-section"},LE={class:"wishlist-header"},VE={class:"wishlist-count"},jE={key:0},UE={key:1,class:"empty-section"},BE={key:3,class:"reviews-section"},GE={key:4,class:"seller-section"},qE={key:0,class:"seller-application-notice"},HE={class:"application-actions"},zE={key:0,class:"moderator-admin-section"},KE={key:1,class:"existing-requests"},WE={class:"requests-list"},JE={class:"request-info"},XE={class:"request-company"},YE={class:"request-date"},ZE={class:"request-status"},QE={key:1,class:"seller-dashboard"},eS={class:"seller-count"},tS={key:0,class:"return-to-application"},rS={key:2,class:"seller-access-denied"},sS={key:5,class:"add-product-section"},nS={class:"section-header"},oS={class:"section-title"},iS={class:"add-product-form"},aS={class:"form-group"},lS={class:"form-group"},cS=["value"],uS={key:0},dS=["value"],fS={key:2},pS=["value"],hS={class:"form-group"},gS={class:"price-input-group"},mS={class:"form-group"},vS={class:"form-group"},yS={class:"add-product-photos"},bS=["onClick"],_S=["onClick"],wS={class:"form-group"},ES={class:"form-group"},SS={class:"form-group"},AS={class:"form-group"},CS={class:"form-actions"},PS=["disabled"],TS={key:0,class:"fas fa-spinner fa-spin"},kS=["disabled"],RS={key:6,class:"empty-section"},OS={__name:"UserProfile",setup(e){const t=bn(),r=le(()=>t.getters["auth/user"]),s=le(()=>t.getters["auth/isLoggedIn"]),n=oe("account"),o=oe([]),i=oe(!1),a=oe(!1),l=oe([]),u=oe(!1),d=oe(!1),f=oe(null),h=oe(!1),g=oe(!1),y=oe({data:[],total:0,page:1,pageSize:15}),E=oe([]),A=oe(!1),m=or({avatar:"https://randomuser.me/api/portraits/men/32.jpg",firstName:"",lastName:"",email:"",username:"",birthDate:"",gender:"",language:"",phone:"",avatarUrl:"",region:"",city:"",street:"",postalCode:""}),C=oe([{id:1,name:"Консоль Sony PlayStation 5 Chassis Digital Edition 1TB (*************)",image:"https://content.rozetka.com.ua/goods/images/big/*********.jpg",price:19506,oldPrice:20999,discount:7,stock:8},{id:2,name:"Гриль TEFAL OptiGrill+ Initial GC706D34",image:"https://content2.rozetka.com.ua/goods/images/big/********.jpg",price:5799,oldPrice:10499,discount:50,stock:5},{id:3,name:"Ноутбук ASUS Vivobook 16X K3605ZF-RP766 (90NB11E2-M01140) Cool Silver",image:"https://content1.rozetka.com.ua/goods/images/big/*********.jpg",price:29999,oldPrice:37999,discount:21,stock:2}]),$=oe([]),I=oe(0),R=oe(!1),M=oe(null),L=oe({total:0,page:1,limit:7,totalPages:1}),k=oe({name:"",categoryId:"",subCategoryId:"",subSubCategoryId:"",price:0,currency:"UAH",stock:1,images:Array(6).fill(null),desc:"",autoRenew:!0,city:"",contact:""}),x=oe(null),X=oe(!1),q=oe([]),z=oe(!1),ue=oe(!1);oe(!1),le(()=>{const F=L.value.page,p=L.value.totalPages,b=[];if(p<=7)for(let P=1;P<=p;P++)b.push(P);else if(F<=4){for(let P=1;P<=5;P++)b.push(P);b.push("..."),b.push(p)}else if(F>=p-3){b.push(1),b.push("...");for(let P=p-4;P<=p;P++)b.push(P)}else{b.push(1),b.push("...");for(let P=F-1;P<=F+1;P++)b.push(P);b.push("..."),b.push(p)}return b});async function Ee(F){console.log("Page changed to:",F),console.log("Current pagination before:",L.value),L.value.page=F,await Y(),console.log("Current pagination after:",L.value)}ir(()=>{document.body.style.backgroundColor="#fff",s.value&&r.value&&Object.assign(m,{email:r.value.email||"",username:r.value.username||""}),B(),Y(),Z(),Oe()}),bo(()=>{document.body.style.backgroundColor="#f8f9fa"});const Oe=async()=>{try{h.value=!0;const F=localStorage.getItem("token");if(!F){console.error("No token found");return}const p=await fetch("/api/users/me",{headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"}});if(p.ok){const b=await p.json();let P;if(b.success&&b.data?P=b.data:P=b,Object.assign(m,{firstName:P.firstName||"",lastName:P.lastName||"",email:P.email||"",username:P.username||"",birthDate:P.birthday?new Date(P.birthday).toLocaleDateString("uk-UA"):"",gender:P.gender!==null&&P.gender!==void 0?P.gender.toString():"",language:P.language!==null&&P.language!==void 0?P.language.toString():"",phone:P.phone||"",avatarUrl:P.avatarUrl||"",region:"",city:"",street:"",postalCode:""}),P.addresses&&Array.isArray(P.addresses)&&P.addresses.length>0){const Q=P.addresses[0];Q&&(m.region=Q.region||"",m.city=Q.city||"",m.street=Q.street||"",m.postalCode=Q.postalCode||"")}else console.log("Адреси користувача не знайдено або порожні");const G=await fetch("/api/users/me/wishlist",{headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"}});if(G.ok){const Q=await G.json();C.value=Q.success?Q.data:Q}else{const Q=await G.json().catch(()=>({}));console.error("Помилка завантаження списку бажаного:",Q)}const K=await fetch("/api/users/me/orders",{headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"}});if(K.ok){const Q=await K.json();y.value=Q.success?Q.data:Q}else{const Q=await K.json().catch(()=>({}));console.error("Помилка завантаження замовлень:",Q)}}else{const b=await p.json().catch(()=>({}));console.error("Помилка завантаження профілю:",b)}}catch(F){console.error("Помилка завантаження профілю:",F)}finally{h.value=!1}},ye=()=>{const F=[];return m.firstName&&m.firstName.length>50&&F.push("Ім'я не має перевищувати 50 символів"),m.lastName&&m.lastName.length>50&&F.push("Прізвище не має перевищувати 50 символів"),m.phone&&!m.phone.match(/^\+380\d{9}$/)&&F.push("Телефон має бути у форматі +380XXXXXXXXX"),(m.region||m.city||m.street||m.postalCode)&&((!m.region||m.region.length>100)&&F.push("Регіон обов'язковий і не має перевищувати 100 символів"),(!m.city||m.city.length>100)&&F.push("Місто обов'язкове і не має перевищувати 100 символів"),(!m.street||m.street.length>200)&&F.push("Вулиця обов'язкова і не має перевищувати 200 символів"),(!m.postalCode||m.postalCode.length<5||m.postalCode.length>20||!/^[a-zA-Z0-9]+$/.test(m.postalCode))&&F.push("Поштовий індекс має містити від 5 до 20 символів і складатися тільки з букв та цифр")),F},se=async()=>{try{for(const F of C.value.items)await Rr.addToCart(F.productId),Ps.push("/cart");alert("Усі товари додано до кошика!")}catch(F){console.error("Помилка при додаванні до кошика:",F),alert("Помилка при додаванні до кошика")}},Te=async()=>{try{const F=localStorage.getItem("token");if(!F)return console.error("No token found"),!1;if(!m.region&&!m.city&&!m.street&&!m.postalCode)return console.log("Немає даних адреси для збереження"),!0;if(!m.region||!m.city||!m.street||!m.postalCode)return console.error("Не всі обов'язкові поля адреси заповнені"),alert("Будь ласка, заповніть всі поля адреси: регіон, місто, вулицю та поштовий індекс"),!1;const p=await fetch("/api/users/me/addresses",{headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"}});let b=[];if(p.ok){const K=await p.json();b=K.success?K.data:K}const P={region:m.region.trim(),city:m.city.trim(),street:m.street.trim(),postalCode:m.postalCode.trim()},G=b.length>0?b[0]:null;if(G){if(!(await fetch(`/api/users/me/addresses/${G.id}`,{method:"PUT",headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"},body:JSON.stringify(P)})).ok)return console.error("Помилка оновлення адреси"),!1}else if(!(await fetch("/api/users/me/addresses",{method:"POST",headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"},body:JSON.stringify(P)})).ok)return console.error("Помилка створення адреси"),!1;return console.log("Адресу успішно збережено"),!0}catch(F){return console.error("Помилка збереження адреси:",F),!1}},st=async()=>{try{const F=ye();if(F.length>0){alert(`Помилки валідації:
`+F.join(`
`));return}g.value=!0;const p={firstName:m.firstName||null,lastName:m.lastName||null,phone:m.phone||null,gender:m.gender?Number(m.gender):null,language:m.language?Number(m.language):null,avatarUrl:m.avatarUrl||null,birthday:m.birthDate?(()=>{const[G,K,Q]=m.birthDate.split(".");return new Date(Q,K-1,G).toISOString()})():null},b=localStorage.getItem("token");if(!b){alert("Помилка автентифікації. Будь ласка, увійдіть знову.");return}const P=await fetch("/api/users/me",{method:"PUT",headers:{Authorization:`Bearer ${b}`,"Content-Type":"application/json"},body:JSON.stringify({...p,id:m.id,username:m.username||null,birthday:p.birthday?new Date(p.birthday).toISOString():null})});if(P.ok){const G=await Te();alert(G?"Профіль та адресу успішно оновлено!":"Профіль оновлено, але виникла помилка при збереженні адреси"),await Oe()}else{const G=await P.json().catch(()=>({})),K=G.message||G.error||"Невідома помилка";alert("Помилка при збереженні: "+K)}}catch(F){console.error("Помилка збереження профілю:",F),alert("Помилка при збереженні профілю")}finally{g.value=!1}},lt=F=>{F.target.src="/default-avatar.png"},ct=async F=>{var b;const p=F.target.files[0];if(p){if(!p.type.startsWith("image/")){alert("Будь ласка, оберіть файл зображення");return}if(p.size>5*1024*1024){alert("Розмір файлу не повинен перевищувати 5MB");return}try{const P=localStorage.getItem("token");if(!P){alert("Помилка автентифікації. Будь ласка, увійдіть знову.");return}const G=new FormData;G.append("file",p);const K=await fetch("/api/users/me/avatar",{method:"POST",headers:{Authorization:`Bearer ${P}`},body:G});if(K.ok){const ae=(b=(await K.json()).data)==null?void 0:b.url;ae?(m.avatarUrl=ae,alert("Аватарку успішно оновлено!")):alert("Помилка при отриманні URL завантаженого файлу")}else{const Q=await K.json().catch(()=>({}));alert("Помилка завантаження файлу: "+(Q.message||"Невідома помилка"))}}catch(P){console.error("Помилка завантаження аватарки:",P),alert("Помилка завантаження аватарки")}}},nt=()=>{Oe()},jt=F=>{let p=F.target.value.replace(/\D/g,"");p.length>0&&!p.startsWith("380")&&(p.startsWith("0")?p="380"+p.substring(1):p.startsWith("38")||(p="380"+p)),p.length>12&&(p=p.substring(0,12)),p.length>0&&(p="+"+p),m.phone=p};function Ze(F){switch(F){case"account":return"Акаунт";case"orders":return"Замовлення";case"wishlist":return"Списки бажань";case"reviews":return"Відгуки";case"seller":return"Кабінет продавця";case"logout":return"Вихід";default:return""}}function Zt(F){f.value=f.value===F?null:F}function ne(F){return F?new Date(F).toLocaleDateString("uk-UA",{year:"numeric",month:"long",day:"numeric"}):""}function O(F){return{0:"Очікує підтвердження",1:"Підтверджено",2:"Обробляється",3:"Відправлено",4:"Доставлено",5:"Скасовано",6:"Повернено"}[F]||"Невідомий статус"}function J(F){return{0:"status-pending",1:"status-confirmed",2:"status-processing",3:"status-shipped",4:"status-delivered",5:"status-cancelled",6:"status-returned"}[F]||"status-unknown"}async function H(){try{await t.dispatch("auth/logout"),Ps.push("/login")}catch(F){console.error("Помилка під час спроби виходу: ",F)}}function ie(F){F.status=="Approved"&&Ps.push(`/product/${F.slug}`)}function ke(F){}function v(F){var p,b;console.log("Editing product:",F),x.value={...F},X.value=!0,k.value={name:F.name||"",categoryId:F.categoryId||"",subCategoryId:"",subSubCategoryId:"",price:F.priceAmount||0,currency:F.priceCurrency||"UAH",stock:F.stock||1,images:Array(6).fill(null),desc:F.description||"",autoRenew:!0,city:((p=F.attributes)==null?void 0:p.city)||"",contact:((b=F.attributes)==null?void 0:b.contact)||""},n.value="addProduct"}function _(){me(),X.value=!1,x.value=null,n.value="addProduct"}function T(F){return{Pending:"status-pending",Approved:"status-approved",Rejected:"status-rejected"}[F]||"status-pending"}function V(F){return{Pending:"На розгляді",Approved:"Схвалено",Rejected:"Відхилено"}[F]||F}async function B(){var F;if(((F=r.value)==null?void 0:F.role)==="Buyer")try{const p=await _w.getUserSellerRequests();E.value=(p.requests||[]).filter(b=>b&&b.id)}catch(p){console.error("Error loading seller requests:",p)}}function U(){A.value=!0,Y()}function te(){A.value=!1}async function Y(){var b,P,G,K;const F=(b=r.value)==null?void 0:b.role;if(!(F===1||F===3||F==="Seller"||F==="SellerOwner"||(F===2||F===4||F==="Moderator"||F==="Admin")&&A.value)){$.value=[];return}R.value=!0,M.value=null;try{console.log("Loading seller products with params:",{page:L.value.page,pageSize:L.value.limit});const Q=await Fn.getSellerProducts({page:L.value.page,pageSize:L.value.limit});console.log("Seller products response:",Q),$.value=(Q.products||[]).filter(Xe=>Xe&&Xe.id),I.value=Q.pagination.total,console.log("Filtered seller products:",$.value);const ae=L.value.limit,xe=((P=Q.pagination)==null?void 0:P.total)||0,Ge=Math.ceil(xe/ae);L.value={total:xe,page:((G=Q.pagination)==null?void 0:G.page)||1,limit:ae,totalPages:Ge},console.log("API pagination response:",Q.pagination),console.log("Our pagination data:",L.value),console.log("Calculated totalPages:",Ge,"from total:",xe,"and limit:",ae)}catch(Q){console.error("Error loading seller products:",Q),$.value=[],((K=Q.response)==null?void 0:K.status)===403?M.value="У вас немає прав для перегляду товарів продавця":M.value="Помилка завантаження товарів. Спробуйте ще раз."}finally{R.value=!1}}async function Z(){z.value=!0;try{const F=await Mt.getAllRootCategories();console.log("categories",F);const p=F.data.data||F.data||[];q.value=(Array.isArray(p)?p:[]).filter(b=>b&&b.id)}catch(F){console.error("Error loading categories:",F),q.value=[]}finally{z.value=!1}}function W(F){}function pe(){me(),X.value=!1,x.value=null,n.value="seller"}async function re(){console.log("Updating product:",x.value.id),console.log("Product CompanyId:",x.value.companyId),console.log("Full product data:",x.value);const F=localStorage.getItem("token"),p=JSON.parse(localStorage.getItem("user")||"{}");if(console.log("Current user:",p),console.log("Auth token exists:",!!F),!F){alert("Ви не авторизовані. Будь ласка, увійдіть в систему.");return}if(!p.role||!p.role==1&&!p.role==2){alert("У вас немає прав продавця для редагування товарів.");return}try{const b=await Fn.getSellerCompany();if(console.log("Current user company:",b),console.log("User company ID:",b.id),!b||!b.id){alert("У вас немає компанії. Спочатку створіть компанію в налаштуваннях продавця.");return}}catch(b){console.error("Error getting company data:",b),alert("Помилка при отриманні даних компанії. Можливо, у вас немає компанії.");return}if(!k.value.name||k.value.name.length<10){console.log("Validation failed: name",k.value.name),alert("Назва товару повинна містити щонайменше 10 символів");return}if(!k.value.desc||k.value.desc.length<20){console.log("Validation failed: description",k.value.desc),alert("Опис товару повинен містити щонайменше 20 символів");return}if(!k.value.categoryId){console.log("Validation failed: categoryId",k.value.categoryId),alert("Оберіть категорію товару");return}if(!k.value.price||k.value.price<=0){console.log("Validation failed: price",k.value.price),alert("Вкажіть коректну ціну товару");return}if(!k.value.stock||k.value.stock<=0){console.log("Validation failed: stock",k.value.stock),alert("Вкажіть коректну кількість товару на складі");return}console.log("All validations passed for update"),ue.value=!0;try{const b=k.value.name.toLowerCase().replace(/[^a-z0-9а-яё\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();console.log("Generated slug for update:",b);const P={name:k.value.name,slug:b,description:k.value.desc,priceCurrency:k.value.currency,priceAmount:parseFloat(k.value.price),stock:parseInt(k.value.stock),sales:x.value.sales||0,categoryId:k.value.categoryId,attributes:{city:k.value.city||"Не вказано",contact:k.value.contact||"Не вказано"},metaTitle:k.value.name,metaDescription:k.value.desc.substring(0,160),metaImage:null};console.log("Product data to update:",P);const G=await Fn.updateProduct(x.value.id,P);console.log("Update result:",G),G?(alert("Товар успішно оновлено!"),me(),X.value=!1,x.value=null,n.value="seller",await Y()):alert("Помилка при оновленні товару")}catch(b){console.error("Error updating product:",b),alert("Помилка при оновленні товару: "+(b.message||"Невідома помилка"))}finally{ue.value=!1}}async function de(){var F,p,b,P,G,K,Q,ae,xe,Ge,Xe,Me,tt;if(console.log("Current newProduct data:",k.value),!k.value.name||k.value.name.length<10){console.log("Validation failed: name",k.value.name),alert("Назва товару повинна містити щонайменше 10 символів");return}if(!k.value.categoryId){console.log("Validation failed: categoryId",k.value.categoryId),alert("Оберіть категорію товару");return}if(!k.value.desc||k.value.desc.length<40){console.log("Validation failed: desc",k.value.desc),alert("Опис товару повинен містити щонайменше 40 символів");return}if(!k.value.price||k.value.price<=0){console.log("Validation failed: price",k.value.price),alert("Вкажіть коректну ціну товару");return}if(!k.value.stock||k.value.stock<=0){console.log("Validation failed: stock",k.value.stock),alert("Вкажіть коректну кількість товару на складі");return}console.log("All validations passed"),console.log("Current authUser:",r.value),console.log("Seller ID will be:",((F=r.value)==null?void 0:F.id)||((p=r.value)==null?void 0:p.userId)||null),ue.value=!0;try{const De=k.value.name.toLowerCase().replace(/[^a-z0-9а-яё\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();console.log("Generated slug:",De),console.log("Current authUser:",r.value);const Ye={name:k.value.name,slug:De,description:k.value.desc,priceCurrency:k.value.currency,priceAmount:parseFloat(k.value.price),stock:parseInt(k.value.stock),sales:0,categoryId:k.value.categoryId,status:0,attributes:{},metaTitle:k.value.name,metaDescription:k.value.desc.substring(0,160),metaImage:"",sellerId:((b=r.value)==null?void 0:b.id)||((P=r.value)==null?void 0:P.userId)||null};console.log("Product data to send:",Ye);const xt=await Fn.createProduct(Ye);console.log("Product creation result:",xt),alert("Товар успішно створено! Він буде доступний після модерації."),me(),n.value="seller",await Y()}catch(De){console.error("Error creating product:",De),console.error("Error response:",De.response),console.error("Error response data:",(G=De.response)==null?void 0:G.data),console.error("Error response status:",(K=De.response)==null?void 0:K.status);let Ye="Помилка при створенні товару. Спробуйте ще раз.";if((ae=(Q=De.response)==null?void 0:Q.data)!=null&&ae.message)Ye=De.response.data.message;else if((Ge=(xe=De.response)==null?void 0:xe.data)!=null&&Ge.errors){const xt=De.response.data.errors;Ye=`Помилки валідації:
${Object.keys(xt).map(ar=>`${ar}: ${xt[ar].join(", ")}`).join(`
`)}`}else((Xe=De.response)==null?void 0:Xe.status)===403?Ye="У вас немає прав для створення товарів. Переконайтеся, що ви маєте роль продавця.":((Me=De.response)==null?void 0:Me.status)===400?Ye="Некоректні дані товару. Перевірте заповнення форми.":((tt=De.response)==null?void 0:tt.status)===422&&(Ye="Помилка валідації даних. Перевірте правильність заповнення всіх полів.");alert(Ye)}finally{ue.value=!1}}function me(){k.value={name:"",categoryId:"",subCategoryId:"",subSubCategoryId:"",price:0,currency:"UAH",stock:1,images:Array(6).fill(null),desc:"",autoRenew:!0,city:"",contact:""},i.value=!1,u.value=!1,o.value=[],l.value=[],X.value=!1,x.value=null}function Re(F){F.target.value===""?i.value=!1:$e(F.target.value)}function Ve(F){F.target.value===""?u.value=!1:ft(F.target.value)}async function $e(F){var p;if(!F){i.value=!1,o.value=[];return}i.value=!0,a.value=!0;try{const b=q.value.find(K=>K.id===F);if(!b||!b.slug){console.warn("Category not found or missing slug:",F),o.value=[],i.value=!1;return}const P=await Mt.getSubCategories(b.slug);console.log("subcategories response:",P);const G=((p=P.data)==null?void 0:p.data)||P.data||[];o.value=(Array.isArray(G)?G:[]).filter(K=>K&&K.id)}catch(b){console.error("Error loading subcategories:",b),q.value=[],i.value=!1}finally{a.value=!1}}async function ft(F){var p;if(!F){u.value=!1,l.value=[];return}u.value=!0,d.value=!0;try{const b=o.value.find(K=>K.id===F);if(!b||!b.slug){console.warn("Subcategory not found or missing slug:",F),l.value=[],u.value=!1;return}const P=await Mt.getSubCategories(b.slug);console.log("sub-subcategories response:",P);const G=((p=P.data)==null?void 0:p.data)||P.data||[];l.value=(Array.isArray(G)?G:[]).filter(K=>K&&K.id)}catch(b){console.error("Error loading sub-subcategories:",b),q.value=[],u.value=!1}finally{d.value=!1}}return(F,p)=>{var P,G,K,Q,ae,xe,Ge,Xe,Me,tt,De,Ye,xt,Ur,ar,ls,cs,Ia,xa,$a;const b=Ke("router-link");return w(),S("div",Lw,[c("div",Vw,[p[98]||(p[98]=c("div",{class:"breadcrumbs"},[c("span",{class:"fas fa-house"}),c("span",{class:"separator"},"/"),ve(" Профіль ")],-1)),c("div",jw,[c("aside",Uw,[c("div",Bw,[c("div",Gw,[c("img",{src:m.avatarUrl||m.avatar,alt:"Аватар користувача",class:"avatar",onError:lt},null,40,qw),c("input",{type:"file",ref:"avatarInput",onChange:ct,accept:"image/*",style:{display:"none"}},null,544),c("button",{type:"button",onClick:p[0]||(p[0]=N=>F.$refs.avatarInput.click()),class:"avatar-edit"},p[29]||(p[29]=[c("i",{class:"fas fa-camera"},null,-1),ve(" Змінити фото ")]))]),c("div",Hw,[c("div",zw,j(m.firstName&&m.lastName?`${m.firstName} ${m.lastName}`:m.username||m.email),1),c("div",Kw,j(m.email),1)])]),c("nav",Ww,[c("ul",null,[c("li",{class:Ce({active:n.value==="account"}),onClick:p[1]||(p[1]=N=>n.value="account")},p[30]||(p[30]=[c("span",{class:"icon"},[c("i",{class:"fa-regular fa-user"})],-1),ve(" Акаунт ")]),2),c("li",{class:Ce({active:n.value==="orders"}),onClick:p[2]||(p[2]=N=>n.value="orders")},p[31]||(p[31]=[c("span",{class:"icon"},[c("i",{class:"fa-regular fa-file-lines"})],-1),ve(" Замовлення ")]),2),c("li",{class:Ce({active:n.value==="wishlist"}),onClick:p[3]||(p[3]=N=>n.value="wishlist")},p[32]||(p[32]=[c("span",{class:"icon"},[c("i",{class:"fa-regular fa-heart"})],-1),ve(" Списки бажань ")]),2),c("li",{class:Ce({active:n.value==="reviews"}),onClick:p[4]||(p[4]=N=>n.value="reviews")},p[33]||(p[33]=[c("span",{class:"icon"},[c("i",{class:"fa-regular fa-pen-to-square"})],-1),ve(" Відгуки ")]),2),c("li",{class:Ce({active:n.value==="seller"}),onClick:p[5]||(p[5]=N=>n.value="seller")},p[34]||(p[34]=[c("span",{class:"icon"},[c("i",{class:"fa-solid fa-globe"})],-1),ve(" Кабінет продавця ")]),2),c("li",{class:Ce({active:n.value==="logout"}),onClick:H},p[35]||(p[35]=[c("span",{class:"icon logout"},[c("i",{class:"fa-solid fa-arrow-right-from-bracket"})],-1),c("span",{class:"logout"},"Вихід",-1)]),2)])])]),c("main",Jw,[n.value==="account"?(w(),S("section",Xw,[p[56]||(p[56]=c("h2",{class:"section-title"},"Акаунт",-1)),h.value?(w(),S("div",Yw,p[36]||(p[36]=[c("div",{class:"spinner"},null,-1),c("span",null,"Завантаження профілю...",-1)]))):(w(),S("form",Zw,[p[52]||(p[52]=c("h3",{class:"form-section-title"},"Основна інформація",-1)),c("div",Qw,[c("div",eE,[p[37]||(p[37]=c("label",null,"Логін (емейл)",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[6]||(p[6]=N=>m.email=N)},null,512),[[gt,m.email]])]),c("div",tE,[p[38]||(p[38]=c("label",null,"Імʼя",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[7]||(p[7]=N=>m.firstName=N)},null,512),[[gt,m.firstName]])]),c("div",rE,[p[39]||(p[39]=c("label",null,"Прізвище",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[8]||(p[8]=N=>m.lastName=N)},null,512),[[gt,m.lastName]])])]),p[53]||(p[53]=c("h3",{class:"form-section-title"},"Особисті дані",-1)),c("div",sE,[c("div",nE,[p[40]||(p[40]=c("label",null,"Дата народження",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[9]||(p[9]=N=>m.birthDate=N)},null,512),[[gt,m.birthDate]])]),c("div",oE,[p[42]||(p[42]=c("label",null,"Стать",-1)),qe(c("select",{"onUpdate:modelValue":p[10]||(p[10]=N=>m.gender=N),class:"form-select"},p[41]||(p[41]=[c("option",{value:"0"},"Не вказано",-1),c("option",{value:"1"},"Чоловіча",-1),c("option",{value:"2"},"Жіноча",-1)]),512),[[hr,m.gender]])]),c("div",iE,[p[44]||(p[44]=c("label",null,"Мова спілкування",-1)),qe(c("select",{"onUpdate:modelValue":p[11]||(p[11]=N=>m.language=N),class:"form-select"},p[43]||(p[43]=[c("option",{value:"0"},"Не вказано",-1),c("option",{value:"1"},"Українська",-1),c("option",{value:"2"},"Англійська",-1),c("option",{value:"3"},"Російська",-1)]),512),[[hr,m.language]])])]),p[54]||(p[54]=c("h3",{class:"form-section-title"},"Контакти",-1)),c("div",aE,[c("div",lE,[p[45]||(p[45]=c("label",null,"Телефон",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[12]||(p[12]=N=>m.phone=N),placeholder:"+380XXXXXXXXX",onInput:jt,maxlength:"13"},null,544),[[gt,m.phone]])]),p[46]||(p[46]=c("div",{class:"form-group"},null,-1)),p[47]||(p[47]=c("div",{class:"form-group"},null,-1))]),p[55]||(p[55]=c("h3",{class:"form-section-title"},"Адреса доставки",-1)),c("div",cE,[c("div",uE,[p[48]||(p[48]=c("label",null,"Регіон/Область",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[13]||(p[13]=N=>m.region=N),placeholder:"Наприклад: Київська область"},null,512),[[gt,m.region]])]),c("div",dE,[p[49]||(p[49]=c("label",null,"Місто",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[14]||(p[14]=N=>m.city=N),placeholder:"Наприклад: Київ"},null,512),[[gt,m.city]])])]),c("div",fE,[c("div",pE,[p[50]||(p[50]=c("label",null,"Вулиця та номер будинку",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[15]||(p[15]=N=>m.street=N),placeholder:"Наприклад: вул. Хрещатик, 1"},null,512),[[gt,m.street]])]),c("div",hE,[p[51]||(p[51]=c("label",null,"Поштовий індекс",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[16]||(p[16]=N=>m.postalCode=N),placeholder:"Наприклад: 01001"},null,512),[[gt,m.postalCode]])])]),c("div",gE,[c("button",{type:"button",class:"save-btn",onClick:st,disabled:g.value},j(g.value?"Збереження...":"Зберегти зміни"),9,mE)])]))])):n.value==="orders"?(w(),S("section",vE,[p[63]||(p[63]=c("h2",{class:"section-title"},"Замовлення",-1)),y.value.data&&y.value.data.length>0?(w(),S("div",yE,[(w(!0),S(be,null,Ne(y.value.data,N=>(w(),S("div",{key:N.id,class:"order-card card-hover",tabindex:"0",onClick:us=>Zt(N.id)},[c("div",_E,[c("div",wE,[c("div",EE,[ve(" № "+j(N.id.substring(0,8))+"... ",1),c("span",SE,"від "+j(ne(N.createdAt)),1)]),c("div",{class:Ce(["order-status",J(N.status)])},j(N.statusText||O(N.status)),3),N.paymentStatusText?(w(),S("div",AE," Оплата: "+j(N.paymentStatusText),1)):ee("",!0)]),c("div",CE,[c("div",PE,j(N.totalPriceAmount)+" "+j(N.totalPriceCurrency||"UAH"),1),c("div",TE,j(N.itemsCount)+" товар(ів)",1)]),c("button",kE,[c("i",{class:Ce(f.value===N.id?"fa-solid fa-chevron-up":"fa-solid fa-chevron-down")},null,2)])]),f.value===N.id?(w(),S("div",RE,[c("div",OE,[p[57]||(p[57]=c("b",null,"Кількість товарів:",-1)),ve(" "+j(N.itemsCount),1)]),c("div",IE,[p[58]||(p[58]=c("b",null,"Загальна сума:",-1)),ve(" "+j(N.totalPriceAmount)+" "+j(N.totalPriceCurrency||"UAH"),1)]),N.shippingAddressLine||N.shippingCity?(w(),S("div",xE,[p[59]||(p[59]=c("b",null,"Адреса доставки:",-1)),ve(" "+j([N.shippingAddressLine,N.shippingCity,N.postalCode].filter(Boolean).join(", ")),1)])):ee("",!0),N.shippingMethodName?(w(),S("div",$E,[p[60]||(p[60]=c("b",null,"Спосіб доставки:",-1)),ve(" "+j(N.shippingMethodName),1)])):ee("",!0),N.paymentMethodText?(w(),S("div",FE,[p[61]||(p[61]=c("b",null,"Спосіб оплати:",-1)),ve(" "+j(N.paymentMethodText),1)])):ee("",!0),N.notes?(w(),S("div",DE,[p[62]||(p[62]=c("b",null,"Примітки:",-1)),ve(" "+j(N.notes),1)])):ee("",!0)])):ee("",!0)],8,bE))),128))])):(w(),S("div",NE,"У вас ще немає замовлень"))])):n.value==="wishlist"?(w(),S("section",ME,[p[66]||(p[66]=c("h2",{class:"section-title"},"Списки бажань",-1)),c("div",LE,[c("div",null,[p[64]||(p[64]=c("div",{class:"wishlist-title"},[c("b",null,"Список"),ve(" (основний)")],-1)),c("div",VE,"Кількість товарів: "+j(C.value.items.length),1),c("button",{class:"wishlist-buy-btn",onClick:se},"Купити все")]),p[65]||(p[65]=Nr('<div class="wishlist-header-actions" data-v-d675c1f3><button class="wishlist-share-btn" title="Поділитися списком" data-v-d675c1f3><i class="fa-solid fa-share-nodes" data-v-d675c1f3></i></button><button class="wishlist-more-btn" title="Додаткові дії" data-v-d675c1f3><i class="fa-solid fa-ellipsis-v" data-v-d675c1f3></i></button></div>',1))]),C.value.items.length>0?(w(),S("div",jE,[ce(Mr,{products:C.value.items,"grid-columns":4,"show-actions":!0,"auto-fetch":!0,onProductRemovedFromWishlist:nt},null,8,["products"])])):(w(),S("div",UE,"Ваш список бажань порожній"))])):n.value==="reviews"?(w(),S("section",BE,[p[67]||(p[67]=c("h2",{class:"section-title"},"Відгуки",-1)),ce(Mw,{"api-endpoint":"/api/users/me/ratings","auto-fetch":!0,"show-pagination":!0,"page-size":10,"empty-message":"У вас ще немає відгуків",onReviewClicked:ke})])):n.value==="seller"?(w(),S("section",GE,[p[76]||(p[76]=c("h2",{class:"section-title"},"Кабінет продавця",-1)),(((P=r.value)==null?void 0:P.role)===0||((G=r.value)==null?void 0:G.role)===2||((K=r.value)==null?void 0:K.role)===4||((Q=r.value)==null?void 0:Q.role)==="Buyer"||((ae=r.value)==null?void 0:ae.role)==="Moderator"||((xe=r.value)==null?void 0:xe.role)==="Admin")&&!A.value?(w(),S("div",qE,[p[72]||(p[72]=c("div",{class:"application-content"},[c("div",{class:"application-icon"},[c("i",{class:"fas fa-store"})]),c("div",{class:"application-text"},[c("h3",null,"Станьте продавцем!"),c("p",null," Ви можете подати заявку на отримання статусу продавця та почати продавати свої товари на нашій платформі. Заповніть форму заявки, і наші адміністратори розглянуть її найближчим часом. ")])],-1)),c("div",HE,[ce(b,{to:"/seller/application",class:"btn btn-primary btn-large"},{default:ot(()=>p[68]||(p[68]=[c("i",{class:"fas fa-paper-plane"},null,-1),ve(" Подати заявку на продавця ")])),_:1})]),(((Ge=r.value)==null?void 0:Ge.role)===2||((Xe=r.value)==null?void 0:Xe.role)===4||((Me=r.value)==null?void 0:Me.role)==="Moderator"||((tt=r.value)==null?void 0:tt.role)==="Admin")&&!A.value?(w(),S("div",zE,[p[70]||(p[70]=c("h4",null,"Додаткові можливості",-1)),c("div",{class:"moderator-admin-actions"},[c("button",{onClick:U,class:"btn btn-secondary btn-large"},p[69]||(p[69]=[c("i",{class:"fas fa-user-tie"},null,-1),ve(" Перейти в режим продавця ")]))])])):ee("",!0),E.value.length>0?(w(),S("div",KE,[p[71]||(p[71]=c("h4",null,"Ваші заявки:",-1)),c("div",WE,[(w(!0),S(be,null,Ne(E.value,N=>{var us;return w(),S("div",{key:(N==null?void 0:N.id)||N,class:"request-item"},[c("div",JE,[c("div",XE,j(((us=N==null?void 0:N.companyRequestData)==null?void 0:us.companyName)||"Без назви"),1),c("div",YE,"Подано: "+j(N!=null&&N.createdAt?ne(N.createdAt):"Невідомо"),1)]),c("div",ZE,[c("span",{class:Ce(T(N==null?void 0:N.status))},j(V(N==null?void 0:N.status)),3)])])}),128))])])):ee("",!0)])):((De=r.value)==null?void 0:De.role)===1||((Ye=r.value)==null?void 0:Ye.role)===3||((xt=r.value)==null?void 0:xt.role)==="Seller"||((Ur=r.value)==null?void 0:Ur.role)==="SellerOwner"||(((ar=r.value)==null?void 0:ar.role)===2||((ls=r.value)==null?void 0:ls.role)===4||((cs=r.value)==null?void 0:cs.role)==="Moderator"||((Ia=r.value)==null?void 0:Ia.role)==="Admin")&&A.value?(w(),S("div",QE,[p[74]||(p[74]=c("div",{class:"seller-title"},"Мої товари",-1)),c("div",eS,"Кількість товарів: "+j(I.value),1),ce(Mr,{products:$.value,loading:R.value,error:M.value,"current-page":L.value.page,"total-items":L.value.total,"page-size":L.value.limit,"show-pagination":!0,"show-actions":!1,"seller-mode":!0,"show-add-card":!0,"grid-columns":4,"auto-fetch":!1,"empty-message":"У вас поки немає товарів. Додайте свій перший товар, щоб почати продавати.","on-product-click":ie,onPageChanged:Ee,onAddProduct:_,onEditProduct:v},null,8,["products","loading","error","current-page","total-items","page-size"]),(((xa=r.value)==null?void 0:xa.role)===2||(($a=r.value)==null?void 0:$a.role)===4)&&A.value?(w(),S("div",tS,[c("button",{onClick:te,class:"btn btn-outline btn-large"},p[73]||(p[73]=[c("i",{class:"fas fa-arrow-left"},null,-1),ve(" Повернутися до заявки ")]))])):ee("",!0)])):(w(),S("div",rS,p[75]||(p[75]=[c("div",{class:"access-denied-content"},[c("i",{class:"fas fa-lock"}),c("h3",null,"Доступ заборонено"),c("p",null,"У вас немає прав для доступу до кабінету продавця.")],-1)])))])):n.value==="addProduct"?(w(),S("section",sS,[c("div",nS,[c("h2",oS,[p[77]||(p[77]=ve(" Кабінет продавця / ")),c("b",null,j(X.value?"редагування товару":"оголошення"),1)]),X.value?(w(),S("button",{key:0,class:"back-btn",onClick:pe},p[78]||(p[78]=[c("i",{class:"fas fa-arrow-left"},null,-1),ve(" Назад до списку ")]))):ee("",!0)]),c("form",iS,[c("div",aS,[p[79]||(p[79]=c("label",null,"Вкажіть назву*",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[17]||(p[17]=N=>k.value.name=N),maxlength:"70",placeholder:"Наприклад, iPhone з гарантією"},null,512),[[gt,k.value.name]]),p[80]||(p[80]=c("div",{class:"form-helper"},"Введіть щонайменше 10 символів",-1))]),c("div",lS,[p[84]||(p[84]=c("label",null,"Категорія*",-1)),qe(c("select",{"onUpdate:modelValue":p[18]||(p[18]=N=>k.value.categoryId=N),required:"",onChange:Re},[p[81]||(p[81]=c("option",{value:""},"Оберіть категорію",-1)),(w(!0),S(be,null,Ne(q.value,N=>(w(),S("option",{key:(N==null?void 0:N.id)||N,value:N==null?void 0:N.id},j((N==null?void 0:N.name)||N),9,cS))),128))],544),[[hr,k.value.categoryId]]),i.value?(w(),S("label",uS,"Підкатегорія")):ee("",!0),i.value&&o.value.length>0?qe((w(),S("select",{key:1,"onUpdate:modelValue":p[19]||(p[19]=N=>k.value.subCategoryId=N),onChange:Ve},[p[82]||(p[82]=c("option",{value:""},"Оберіть підкатегорію",-1)),(w(!0),S(be,null,Ne(o.value,N=>(w(),S("option",{key:(N==null?void 0:N.id)||N,value:N==null?void 0:N.id},j((N==null?void 0:N.name)||N),9,dS))),128))],544)),[[hr,k.value.subCategoryId]]):ee("",!0),u.value?(w(),S("label",fS,"Підпідкатегорія")):ee("",!0),u.value&&l.value.length>0?qe((w(),S("select",{key:3,"onUpdate:modelValue":p[20]||(p[20]=N=>k.value.subSubCategoryId=N)},[p[83]||(p[83]=c("option",{value:""},"Оберіть підпідкатегорію",-1)),(w(!0),S(be,null,Ne(l.value,N=>(w(),S("option",{key:(N==null?void 0:N.id)||N,value:N==null?void 0:N.id},j((N==null?void 0:N.name)||N),9,pS))),128))],512)),[[hr,k.value.subSubCategoryId]]):ee("",!0),p[85]||(p[85]=c("div",{class:"form-helper"},"Якщо не вибрати підкатегорію можуть бути проблеми з відобреження товару в пошуку",-1))]),c("div",hS,[p[87]||(p[87]=c("label",null,"Ціна*",-1)),c("div",gS,[qe(c("input",{type:"number","onUpdate:modelValue":p[21]||(p[21]=N=>k.value.price=N),min:"0",step:"0.01",placeholder:"0.00",required:""},null,512),[[gt,k.value.price]]),qe(c("select",{"onUpdate:modelValue":p[22]||(p[22]=N=>k.value.currency=N)},p[86]||(p[86]=[c("option",{value:"UAH"},"₴ UAH",-1),c("option",{value:"USD"},"$ USD",-1),c("option",{value:"EUR"},"€ EUR",-1)]),512),[[hr,k.value.currency]])])]),c("div",mS,[p[88]||(p[88]=c("label",null,"Кількість на складі*",-1)),qe(c("input",{type:"number","onUpdate:modelValue":p[23]||(p[23]=N=>k.value.stock=N),min:"0",placeholder:"1",required:""},null,512),[[gt,k.value.stock]])]),c("div",vS,[p[90]||(p[90]=c("label",null,"Фото",-1)),c("div",yS,[(w(!0),S(be,null,Ne(k.value.images,(N,us)=>(w(),S("div",{key:us,class:"add-product-photo"},[us===0?(w(),S("button",{key:0,class:"add-photo-btn-main",type:"button",onClick:Ir(Tf=>void 0,["prevent"]),style:{background:"#ff9800",color:"#fff"}}," Додати фото ",8,bS)):(w(),S("div",{key:1,class:"add-photo-placeholder",onClick:Ir(Tf=>void 0,["prevent"])},p[89]||(p[89]=[c("i",{class:"fa-solid fa-camera"},null,-1)]),8,_S))]))),128))]),p[91]||(p[91]=c("div",{class:"form-helper"},"Перше фото буде на обкладинці. Перетягніть, щоб змінити порядок фото",-1))]),c("div",wS,[p[92]||(p[92]=c("label",null,"Опис*",-1)),qe(c("textarea",{"onUpdate:modelValue":p[24]||(p[24]=N=>k.value.desc=N),rows:"4",placeholder:"Подумайте, що би ви хотіли дізнатися з оголошення і додайте це в опис"},null,512),[[gt,k.value.desc]]),p[93]||(p[93]=c("div",{class:"form-helper"},"Введіть щонайменше 40 символів",-1))]),c("div",ES,[p[94]||(p[94]=c("label",null,"Автопродовження",-1)),qe(c("input",{type:"checkbox","onUpdate:modelValue":p[25]||(p[25]=N=>k.value.autoRenew=N)},null,512),[[Zu,k.value.autoRenew]]),p[95]||(p[95]=c("span",null,"Оголошення буде деактивовано через 30 днів",-1))]),c("div",SS,[p[96]||(p[96]=c("label",null,"Місцезнаходження*",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[26]||(p[26]=N=>k.value.city=N),placeholder:"Чернівці"},null,512),[[gt,k.value.city]])]),c("div",AS,[p[97]||(p[97]=c("label",null,"Контактна особа*",-1)),qe(c("input",{type:"text","onUpdate:modelValue":p[27]||(p[27]=N=>k.value.contact=N),placeholder:"Валентин"},null,512),[[gt,k.value.contact]])]),c("div",CS,[c("button",{class:"save-btn",type:"button",onClick:p[28]||(p[28]=N=>X.value?re():de()),disabled:ue.value},[ue.value?(w(),S("i",TS)):ee("",!0),ve(" "+j(ue.value?"Збереження...":X.value?"Оновити":"Додати"),1)],8,PS),c("button",{class:"cancel-btn",type:"button",onClick:pe,disabled:ue.value}," Скасувати ",8,kS)])])])):(w(),S("section",RS,[c("div",null,'Тут буде контент вкладки "'+j(Ze(n.value))+'"',1)]))])])])])}}},Ec=et(OS,[["__scopeId","data-v-d675c1f3"]]),IS={name:"Dashboard",setup(){const e=bn();return{user:le(()=>e.getters["auth/user"]),formatDate:s=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(s)}}},xS={class:"dashboard"},$S={class:"row"},FS={class:"col-md-4"},DS={class:"dashboard-card"},NS={class:"row mt-4"},MS={class:"col-md-6"},LS={class:"dashboard-card"},VS={class:"list-group list-group-flush"},jS={class:"list-group-item"},US={class:"list-group-item"},BS={class:"col-md-6"},GS={class:"dashboard-card"},qS={class:"card-body"};function HS(e,t,r,s,n,o){const i=Ke("router-link");return w(),S("div",xS,[t[9]||(t[9]=c("h1",{class:"mb-4"},"User Dashboard",-1)),c("div",$S,[c("div",FS,[c("div",DS,[t[1]||(t[1]=c("h2",{class:"dashboard-card-title"},"My Profile",-1)),t[2]||(t[2]=c("p",null,"Manage your personal information and account settings.",-1)),ce(i,{to:"/profile",class:"btn btn-primary"},{default:ot(()=>t[0]||(t[0]=[ve("View Profile")])),_:1})])]),t[3]||(t[3]=Nr('<div class="col-md-4"><div class="dashboard-card"><h2 class="dashboard-card-title">My Orders</h2><p>View your order history and track current orders.</p><a href="#" class="btn btn-primary">View Orders</a></div></div><div class="col-md-4"><div class="dashboard-card"><h2 class="dashboard-card-title">My Wishlist</h2><p>View and manage your saved items.</p><a href="#" class="btn btn-primary">View Wishlist</a></div></div>',2))]),c("div",NS,[c("div",MS,[c("div",LS,[t[4]||(t[4]=c("h2",{class:"dashboard-card-title"},"Recent Activity",-1)),c("ul",VS,[c("li",jS,"You logged in on "+j(s.formatDate(new Date)),1),c("li",US,"Profile updated on "+j(s.formatDate(new Date(Date.now()-864e5))),1)])])]),c("div",BS,[c("div",GS,[t[8]||(t[8]=c("h2",{class:"dashboard-card-title"},"Account Summary",-1)),c("div",qS,[c("p",null,[t[5]||(t[5]=c("strong",null,"Username:",-1)),ve(" "+j(s.user.username),1)]),c("p",null,[t[6]||(t[6]=c("strong",null,"Email:",-1)),ve(" "+j(s.user.email),1)]),c("p",null,[t[7]||(t[7]=c("strong",null,"Member Since:",-1)),ve(" "+j(s.formatDate(new Date)),1)])])])])])])}const zS=et(IS,[["render",HS]]),KS={name:"FiltersSidebar",props:{filterList:{type:Object,default:()=>({})}},data(){return{openFilters:{},selectedFilters:{},searchTerms:{},error:null}},computed:{hasFilters(){return this.filterList&&typeof this.filterList=="object"&&Object.keys(this.filterList).length>0}},watch:{filterList:{handler(e){if(e&&typeof e=="object"){const t={...this.openFilters},r={...this.selectedFilters},s={...this.searchTerms};Object.keys(e).forEach(n=>{t.hasOwnProperty(n)||(t[n]=!0,r[n]=[],s[n]="")}),this.openFilters=t,this.selectedFilters=r,this.searchTerms=s}},immediate:!0,deep:!0}},created(){if(this.filterList&&typeof this.filterList=="object"){const e={},t={},r={};Object.keys(this.filterList).forEach(s=>{e[s]=!0,t[s]=[],r[s]=""}),this.openFilters=e,this.selectedFilters=t,this.searchTerms=r}},methods:{toggleFilter(e){this.openFilters={...this.openFilters,[e]:!this.openFilters[e]}},isFilterOpen(e){return this.openFilters[e]===!0},shouldShowSearch(e){return Object.keys(this.filterList[e]||{}).length>10},filterOptionsBySearch(e,t){var n;const r=((n=this.searchTerms[t])==null?void 0:n.toLowerCase())||"";if(!r)return e;const s={};return Object.entries(e).forEach(([o,i])=>{o.toLowerCase().includes(r)&&(s[o]=i)}),s},emitFilterChange(){const e={};Object.entries(this.selectedFilters).forEach(([t,r])=>{r.length>0&&(e[t]=r)}),this.$emit("filter-changed",e)},clearAllFilters(){const e={};Object.keys(this.selectedFilters).forEach(t=>{e[t]=[]}),this.selectedFilters=e,this.emitFilterChange()},updateSelectedFilters(e){this.selectedFilters={...this.selectedFilters,...e}}}},WS={class:"filters-sidebar"},JS={key:0,class:"no-filters"},XS=["onClick"],YS={class:"filter-title"},ZS={class:"filter-content"},QS={key:0,class:"filter-search"},eA=["placeholder","onUpdate:modelValue"],tA={class:"filter-options"},rA=["value","onUpdate:modelValue"],sA={class:"option-name"},nA={class:"option-count"};function oA(e,t,r,s,n,o){return w(),S("div",WS,[o.hasFilters?ee("",!0):(w(),S("div",JS,t[1]||(t[1]=[c("p",null,"Завантаження фільтрів...",-1)]))),(w(!0),S(be,null,Ne(r.filterList,(i,a)=>(w(),S("div",{key:a,class:"filter-group"},[c("div",{class:"filter-header",onClick:l=>o.toggleFilter(a)},[c("h3",YS,j(a),1),(w(),S("svg",{class:Ce(["filter-arrow",{rotated:o.isFilterOpen(a)}]),width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},t[2]||(t[2]=[c("path",{d:"M6 9l6 6 6-6"},null,-1)]),2))],8,XS),qe(c("div",ZS,[o.shouldShowSearch(a)?(w(),S("div",QS,[qe(c("input",{type:"text",placeholder:"Пошук в "+a,"onUpdate:modelValue":l=>n.searchTerms[a]=l},null,8,eA),[[gt,n.searchTerms[a]]])])):ee("",!0),c("div",tA,[(w(!0),S(be,null,Ne(o.filterOptionsBySearch(i,a),(l,u)=>(w(),S("label",{key:u,class:"filter-option"},[qe(c("input",{type:"checkbox",value:u,"onUpdate:modelValue":d=>n.selectedFilters[a]=d,onChange:t[0]||(t[0]=(...d)=>o.emitFilterChange&&o.emitFilterChange(...d))},null,40,rA),[[Zu,n.selectedFilters[a]]]),c("span",sA,j(u),1),c("span",nA,j(l),1)]))),128))])],512),[[xh,o.isFilterOpen(a)]])]))),128))])}const Sf=et(KS,[["render",oA],["__scopeId","data-v-06bb1770"]]),iA={name:"RecommendedProducts",components:{ProductGrid:Mr},props:{title:{type:String,default:"Рекомендації на основі ваших переглядів"},fetchParams:{type:Object,default:()=>({type:"recommended",status:1,pageSize:8})},gridColumns:{type:Number,default:4},emptyMessage:{type:String,default:"Рекомендовані товари поки що недоступні"}}},aA={class:"recommended-products-section"},lA={class:"section-title"};function cA(e,t,r,s,n,o){const i=Ke("ProductGrid");return w(),S("section",aA,[c("h2",lA,j(r.title),1),ce(i,{"fetch-params":r.fetchParams,"grid-columns":r.gridColumns,"empty-message":r.emptyMessage,onProductAddedToCart:t[0]||(t[0]=a=>e.$emit("product-added-to-cart",a)),onProductAddedToWishlist:t[1]||(t[1]=a=>e.$emit("product-added-to-wishlist",a))},null,8,["fetch-params","grid-columns","empty-message"])])}const Af=et(iA,[["render",cA],["__scopeId","data-v-7bb4eefe"]]),uA={props:{reviews:{type:Array,default:()=>[]}},methods:{calculateAvgRating(e){return e?((e.service+e.deliveryTime+e.accuracy)/3).toFixed(1):0},formatDate(e){return e?new Date(e).toLocaleDateString("uk-UA"):""}}},dA={class:"category-reviews"},fA={key:0,class:"no-reviews"},pA={key:1,class:"reviews-list"},hA={class:"review-header"},gA={class:"product-info"},mA={class:"rating"},vA={class:"rating-value"},yA={class:"review-content"},bA={class:"review-footer"},_A={class:"review-date"};function wA(e,t,r,s,n,o){return w(),S("div",dA,[t[1]||(t[1]=c("h3",{class:"reviews-title"},"Відгуки покупців",-1)),r.reviews.length===0?(w(),S("div",fA," Немає відгуків для товарів цієї категорії ")):(w(),S("div",pA,[(w(!0),S(be,null,Ne(r.reviews,i=>(w(),S("div",{key:i.id,class:"review-card"},[c("div",hA,[c("div",gA,[c("strong",null,j(i.productName),1)]),c("div",mA,[t[0]||(t[0]=c("span",{class:"stars"},"★★★★★",-1)),c("span",vA,j(o.calculateAvgRating(i.rating))+"/5",1)])]),c("div",yA,j(i.comment),1),c("div",bA,[c("span",_A,j(o.formatDate(i.createdAt)),1)])]))),128))]))])}const Cf=et(uA,[["render",wA],["__scopeId","data-v-d71ec2e7"]]);class EA{async getAll(t={}){return await Se.get("/reviews",{params:t})}async getById(t){return await Se.get(`/reviews/${t}`)}async getByProductId(t,r={}){return await Se.get(`/products/${t}/reviews`,{params:r})}async create(t){return await Se.post("/reviews",t)}async update(t,r){return await Se.put(`/reviews/${t}`,r)}async delete(t){return await Se.delete(`/reviews/${t}`)}getCategoryReviews(t,r={}){return Se.get(`/categories/${t}/reviews`,{params:r})}async getUserReviews(t={}){try{return(await Se.get("/users/me/reviews",{params:t})).data}catch(r){throw console.error("Error fetching user reviews:",r),r}}async getProductReviewStats(t){try{return(await Se.get(`/products/${t}/reviews/stats`)).data}catch(r){throw console.error("Error fetching product review stats:",r),r}}async canUserReview(t){try{return(await Se.get(`/products/${t}/can-review`)).data}catch(r){throw console.error("Error checking if user can review:",r),r}}}const Pf=new EA,SA={class:"electronics-page"},AA={class:"breadcrumbs"},CA={key:0},PA={key:0,class:"page-title"},TA={class:"page-stats"},kA={key:0},RA={key:0,class:"filter-indicator"},OA={key:1,class:"no-results"},IA={key:2,class:"active-filters-display"},xA={class:"active-filters-tags"},$A=["onClick"],FA={class:"content-container"},DA={class:"filters-section"},NA={key:0,class:"filters-header"},MA={class:"active-filters-count"},LA={class:"products-grid"},VA={class:"recommended-products-section"},jA={class:"container"},UA={class:"category-reviews-section"},BA={class:"container"},GA={components:{ProductGrid:Mr,FiltersSidebar:Sf,RecommendedProducts:Af,CategoryReviews:Cf,RandomProducts:Jd},data(){return{products:[],allProducts:[],recommendedProducts:[],categoryReviews:[],categoryParents:[],filters:{},activeFilters:{},currentPage:1,itemsPerPage:24,totalCount:0,filteredCount:0,categorySlug:this.$route.params.slug,categoryName:"",parent:"",error:null,loading:!1,randomMode:!1}},async mounted(){await this.fetchProducts(),await this.fetchAllCount(),await this.fetchFilters(),await this.fetchCategoryInfo(),await this.fetchRecommendedProducts()},methods:{async fetchAllCount(e={status:1}){try{this.error=null}catch(t){this.error="Failed to load product count. Please try again.",console.error(t)}},async fetchCategoryInfo(e){try{const t=await Mt.getBySlug(this.categorySlug,e);console.log(t),this.categoryName=t.data.name,t.data.parentId!=null&&this.getParents(t.data.parentId),this.error=null}catch(t){this.error="Failed to load category info. Please try again.",console.error(t)}},async getParents(e){try{const t=await Mt.getById(e);this.categoryParents.unshift(t.data),this.error=null,t.data.parentId!=null&&this.getParents(t.data.parentId)}catch(t){this.error="Failed to load parent category. Please try again.",console.error(t)}},async fetchProducts(e={page:1,pageSize:1e3,status:1}){try{this.loading=!0,this.error=null;const t=await Mt.getProducts(this.categorySlug,e);console.log("Products response:",t),this.allProducts=t.data.data||[],this.applyFilters()}catch(t){this.error="Failed to load products. Please try again.",console.error(t)}finally{this.loading=!1}},async fetchFilters(){try{console.log("Fetching filters for category:",this.categorySlug);const e=await Jr.getFilters(this.categorySlug);console.log("Filters API response:",e),console.log("Response status:",e.status),console.log("Response data:",e.data),this.filters=e.data||{},console.log("Filters data assigned:",this.filters),console.log("Filters keys:",Object.keys(this.filters)),this.error=null}catch(e){this.error="Failed to load filters. Please try again.",console.error("Filters fetch error:",e),console.error("Error response:",e.response)}},async fetchRecommendedProducts(e={page:1,pageSize:4}){try{const t=await Mt.getProducts(this.categorySlug,e);this.recommendedProducts=t.data.data,this.error=null}catch(t){this.error="Failed to load products. Please try again.",console.error(t)}},async fetchCategoryReviews(e={page:1,pageSize:5}){try{const t=await Pf.getCategoryReviews(this.categorySlug,e);this.categoryReviews=t.data.data||[],this.error=null}catch(t){this.error="Failed to load category reviews. Please try again.",console.error(t)}},handlePageChange(e){this.currentPage=e,this.applyFilters()},handleFilterChange(e){console.log("=== HANDLE FILTER CHANGE ==="),console.log("Active filters received:",e),console.log("Previous activeFilters:",this.activeFilters),this.activeFilters=e,console.log("New activeFilters set to:",this.activeFilters),this.currentPage=1,this.applyFilters()},applyFilters(){console.log("=== APPLY FILTERS START ==="),console.log("allProducts:",this.allProducts.length),console.log("activeFilters:",this.activeFilters),console.log("activeFilters keys:",Object.keys(this.activeFilters));let e=[...this.allProducts];console.log("Initial filteredProducts:",e.length),Object.keys(this.activeFilters).length>0?(console.log("Applying filters:",this.activeFilters),e=e.filter(s=>{const n=this.matchesFilters(s,this.activeFilters);return console.log(`Product "${s.name}" matches filters:`,n),n}),console.log("After filtering:",e.length)):console.log("No active filters, showing all products"),this.filteredCount=e.length,this.totalCount=e.length;const t=(this.currentPage-1)*this.itemsPerPage,r=t+this.itemsPerPage;this.products=e.slice(t,r),console.log(`Final result: ${this.filteredCount} total, showing ${this.products.length} on page ${this.currentPage}`),console.log("Final products array:",this.products.map(s=>s.name)),console.log("=== APPLY FILTERS END ===")},matchesFilters(e,t){var r,s;for(const[n,o]of Object.entries(t)){if(o.length===0)continue;let i=!1;if(n==="Компанії")i=o.includes((r=e.company)==null?void 0:r.name),console.log(`Company filter: ${(s=e.company)==null?void 0:s.name} in [${o.join(", ")}] = ${i}`);else if(e.attributes&&e.attributes[n]){const a=e.attributes[n];i=o.includes(a),console.log(`Attribute filter ${n}: ${a} in [${o.join(", ")}] = ${i}`)}else console.log(`Product has no attribute: ${n}`);if(!i)return console.log(`Product ${e.name} excluded by filter ${n}`),!1}return!0},clearAllFilters(){this.activeFilters={},this.currentPage=1,this.$refs.filtersSidebar&&this.$refs.filtersSidebar.clearAllFilters(),this.applyFilters()},removeFilter(e,t){if(this.activeFilters[e]){const r=this.activeFilters[e].filter(s=>s!==t);if(r.length===0){const s={...this.activeFilters};delete s[e],this.activeFilters=s}else this.activeFilters={...this.activeFilters,[e]:r};this.$refs.filtersSidebar&&this.$refs.filtersSidebar.updateSelectedFilters(this.activeFilters),this.applyFilters()}},toggleRandomMode(){this.randomMode=!this.randomMode},handleProductAddedToCart(e){console.log("Product added to cart:",e)},handleProductAddedToWishlist(e){console.log("Product added to wishlist:",e)}}},qA=Object.assign(GA,{__name:"CatalogPage",setup(e){return(t,r)=>(w(),S("div",SA,[c("div",AA,[r[2]||(r[2]=c("span",{class:"fas fa-house"},null,-1)),r[3]||(r[3]=c("span",{class:"separator"},"/",-1)),(w(!0),S(be,null,Ne(t.categoryParents,s=>(w(),S("span",{key:s.id},[ve(j(s.name)+" ",1),r[1]||(r[1]=c("span",{class:"separator"},"/",-1))]))),128)),t.categoryName?(w(),S("span",CA,j(t.categoryName),1)):ee("",!0)]),t.categoryName?(w(),S("h1",PA,j(t.categoryName),1)):ee("",!0),c("div",TA,[t.filteredCount>0?(w(),S("span",kA,[ve(" Знайдено "+j(t.filteredCount)+" товарів ",1),Object.keys(t.activeFilters).length>0?(w(),S("span",RA," з "+j(t.allProducts.length)+" (з фільтрами) ",1)):ee("",!0)])):Object.keys(t.activeFilters).length>0?(w(),S("span",OA," Немає товарів за вибраними фільтрами ")):ee("",!0),Object.keys(t.activeFilters).length>0?(w(),S("div",IA,[r[4]||(r[4]=c("span",{class:"active-filters-label"},"Активні фільтри:",-1)),c("div",xA,[(w(!0),S(be,null,Ne(t.activeFilters,(s,n)=>(w(),S(be,{key:n},[(w(!0),S(be,null,Ne(s,o=>(w(),S("span",{key:`${n}-${o}`,class:"filter-tag"},[ve(j(n)+": "+j(o)+" ",1),c("button",{onClick:i=>t.removeFilter(n,o),class:"remove-filter-btn"},"×",8,$A)]))),128))],64))),128))])])):ee("",!0),r[5]||(r[5]=c("div",{class:"sort-container"},[c("span",null,"За популярністю"),c("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[c("path",{d:"M6 9l6 6 6-6"})])],-1))]),c("div",FA,[c("div",DA,[Object.keys(t.activeFilters).length>0?(w(),S("div",NA,[c("span",MA," Активних фільтрів: "+j(Object.keys(t.activeFilters).length),1),c("button",{onClick:r[0]||(r[0]=(...s)=>t.clearAllFilters&&t.clearAllFilters(...s)),class:"clear-filters-btn"}," Очистити всі фільтри ")])):ee("",!0),ce(Sf,{filterList:t.filters,onFilterChanged:t.handleFilterChange,ref:"filtersSidebar"},null,8,["filterList","onFilterChanged"])]),c("div",LA,[ce(Mr,{products:t.products,loading:t.loading,error:t.error,"show-pagination":!0,"current-page":t.currentPage,"total-items":t.totalCount,"page-size":t.itemsPerPage,"grid-columns":4,"auto-fetch":!1,onPageChanged:t.handlePageChange,onProductAddedToCart:t.handleProductAddedToCart,onProductAddedToWishlist:t.handleProductAddedToWishlist},null,8,["products","loading","error","current-page","total-items","page-size","onPageChanged","onProductAddedToCart","onProductAddedToWishlist"])])]),c("section",VA,[c("div",jA,[ce(Af,{title:"Рекомендовані товари","fetch-params":{type:"recommended",status:1,pageSize:8}})])]),c("section",UA,[c("div",BA,[ce(Cf,{reviews:t.categoryReviews},null,8,["reviews"])])])]))}}),HA=et(qA,[["__scopeId","data-v-486b0014"]]),zA={name:"Cart",data(){return{cartItems:[],recommendedProducts:[],error:null}},computed:{totalItems(){return this.cartItems.reduce((e,t)=>e+t.quantity,0)},subtotal(){return Number.isNaN(Number(this.cartItems.reduce((e,t)=>e+t.total,0)))?this.originalTotal:this.cartItems.reduce((e,t)=>e+t.total,0)},originalTotal(){return this.cartItems.reduce((e,t)=>e+Math.round(t.totalPrice),0)},discount(){return this.subtotal==this.originalTotal?0:this.orginalTotal-this.subtotal},discountToPercent(){return this.discount==0?0:100*(this.orginalTotal-this.subtotal)/this.originalTotal}},async mounted(){await this.fetchCart(),await this.fetchProducts()},methods:{async fetchProducts(e){try{const t=await Jr.getAll(e={});this.products=t.data.data,this.error=null}catch(t){this.error="Failed to load products. Please try again.",console.error(t)}},async fetchCart(){try{const e=await Rr.getCart();console.log(e),this.cartItems=e.data.data.items,this.error=null}catch{this.error="Failed to load cart. Please try again."}},async increaseQuantity(e){e.quantity<e.productStock&&await Rr.changeItemCount(e.id,e.quantity+1),await this.fetchCart()},async decreaseQuantity(e){e.quantity>1?await Rr.changeItemCount(e.id,e.quantity-1):await Rr.deleteItem(e.id),await this.fetchCart()},async removeItem(e){await Rr.deleteItem(e),await this.fetchCart()},async clearCart(){Rr.deleteCart()},async addToWishlist(e){await Di.addToWishlist(e)},addToFavorites(e){console.log(`Added item ${e} to favorites`)},applyPromoCode(e){console.log(`Applied promo code: ${e}`)},proceedToCheckout(){console.log("Proceeding to checkout")}}},KA={class:"cart-page"},WA={class:"cart-header"},JA={class:"cart-title"},XA={class:"cart-count"},YA={key:0,class:"cart-count"},ZA={class:"cart-content"},QA={class:"cart-items"},eC={class:"cart-item"},tC={class:"item-details"},rC={class:"item-image"},sC=["src","alt"],nC={class:"item-info"},oC={key:0,class:"item-availability"},iC={key:1,class:"item-name"},aC={key:2,class:"item-code"},lC={class:"item-actions"},cC=["onClick"],uC={class:"item-price-controls"},dC={class:"price-container"},fC={key:0,class:"price-current"},pC={key:1,class:"price-original"},hC={class:"quantity-controls"},gC=["onClick"],mC=["value"],vC=["onClick"],yC={class:"cart-summary"},bC={class:"summary-row"},_C={class:"summary-value"},wC={class:"summary-row"},EC={class:"summary-value discount"},SC={class:"summary-row total"},AC={class:"summary-value"},CC={class:"recommended-products"},PC={class:"container"},TC={class:"products-grid"},kC={key:0,class:"product-badge"},RC={class:"product-image"},OC=["src","alt"],IC={class:"product-info"},xC={class:"product-name"},$C={key:0,class:"product-availability"},FC={key:1,class:"product-unavailability"},DC={class:"product-price"},NC={key:0,class:"price-old"},MC={key:1,class:"price-discount"},LC={class:"price-current"};function VC(e,t,r,s,n,o){const i=Ke("router-link");return w(),S("div",KA,[c("div",WA,[ce(i,{to:"/",class:"back-link"},{default:ot(()=>t[0]||(t[0]=[c("i",{class:"fas fa-arrow-left"},null,-1)])),_:1}),c("h1",JA,[t[1]||(t[1]=ve("Кошик ")),c("span",XA,j(o.totalItems)+" товари ",1),o.discountToPercent!=0?(w(),S("span",YA,"(-"+j(o.discountToPercent)+"%)",1)):ee("",!0)])]),c("div",ZA,[c("div",QA,[t[7]||(t[7]=c("button",{class:"clear-cart-btn"},[c("i",{class:"fas fa-trash"}),ve(" Видалити все ")],-1)),(w(!0),S(be,null,Ne(n.cartItems,a=>(w(),S("div",eC,[t[6]||(t[6]=c("button",{class:"remove-item"},[c("i",{class:"fas fa-times"})],-1)),c("div",tC,[c("div",rC,[a.productImage?(w(),S("img",{key:0,src:a.productImage||"@assets/images/icons/placeholder-icon.svg",alt:a.productName},null,8,sC)):ee("",!0)]),c("div",nC,[a.productStock>0?(w(),S("div",oC,t[2]||(t[2]=[c("span",{class:"availability-icon"},"✓",-1),c("span",{class:"availability-text"},"В наявності",-1)]))):ee("",!0),a.productName?(w(),S("h3",iC,j(a.productName),1)):ee("",!0),a.id?(w(),S("p",aC,"Код товару: "+j(a.id),1)):ee("",!0),c("div",lC,[c("button",{class:"add-to-favorites",onClick:l=>o.addToWishlist(a.productId)},t[3]||(t[3]=[c("span",{class:"add-to-favorites-heart"},[c("i",{class:"far fa-heart"})],-1),c("span",{class:"add-to-favirites-text"},"В обрані",-1)]),8,cC)])]),c("div",uC,[c("div",dC,[a.totalPrice?(w(),S("div",fC,j(Math.round(a.totalPrice))+" ₴",1)):ee("",!0),a.oldPrice?(w(),S("div",pC,j(a.oldPrice)+" ₴",1)):ee("",!0)]),c("div",hC,[c("button",{class:"quantity-btn minus",onClick:l=>o.decreaseQuantity(a)},t[4]||(t[4]=[c("i",{class:"fas fa-minus"},null,-1)]),8,gC),c("input",{type:"number",class:"quantity-input",value:a.quantity,min:"1"},null,8,mC),c("button",{class:"quantity-btn plus",onClick:l=>o.increaseQuantity(a)},t[5]||(t[5]=[c("i",{class:"fas fa-plus"},null,-1)]),8,vC)])])])]))),256))]),c("div",yC,[t[10]||(t[10]=c("h2",{class:"summary-title"},"Разом",-1)),c("div",bC,[c("span",null,j(o.totalItems)+" товари на суму",1),c("span",_C,j(o.originalTotal)+" ₴",1)]),c("div",wC,[t[8]||(t[8]=c("span",null,"Знижка",-1)),c("span",EC,j(o.discount)+" ₴",1)]),t[11]||(t[11]=c("div",{class:"summary-row"},[c("span",null,"Вартість доставки"),c("span",{class:"summary-value free"},"Безкоштовно")],-1)),t[12]||(t[12]=c("div",{class:"summary-divider"},null,-1)),c("div",SC,[t[9]||(t[9]=c("span",null,"До оплати",-1)),c("span",AC,j(o.subtotal)+" ₴",1)]),t[13]||(t[13]=Nr('<button class="checkout-btn" data-v-4800e765>Перейти до оформлення</button><div class="promo-code" data-v-4800e765><span data-v-4800e765>Промокод</span><div class="promo-input-container" data-v-4800e765><input type="text" class="promo-input" placeholder="Введіть промокод" data-v-4800e765><button class="apply-promo-btn" data-v-4800e765>Додати</button></div></div>',2))])]),c("div",CC,[t[17]||(t[17]=c("h2",{class:"section-title"},"Рекомендовані товари",-1)),c("div",PC,[c("div",TC,[(w(!0),S(be,null,Ne(n.recommendedProducts,a=>(w(),S("div",{key:a.id,class:"product-card"},[a.badge?(w(),S("div",kC,j(a.badge),1)):ee("",!0),c("div",RC,[c("img",{src:a.image,alt:a.name},null,8,OC)]),c("div",IC,[c("h3",xC,j(a.name),1),a.stock>0?(w(),S("div",$C,t[14]||(t[14]=[c("span",{class:"availability-icon"},"✓",-1),c("span",{class:"availability-text"},"В наявності",-1)]))):ee("",!0),a.stock==0?(w(),S("div",FC,t[15]||(t[15]=[c("span",{class:"availability-icon"},"✖",-1),c("span",{class:"availability-text"},"Немає в наявності",-1)]))):ee("",!0),c("div",DC,[a.oldPrice?(w(),S("div",NC,j(a.oldPrice)+" ₴",1)):ee("",!0),e.cartItem.discount?(w(),S("div",MC,"-"+j(a.discount)+"%",1)):ee("",!0)]),c("div",LC,j(a.priceAmount)+" ₴",1)]),t[16]||(t[16]=Nr('<div class="product-actions" data-v-4800e765><button class="wishlist-btn" data-v-4800e765><svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-4800e765><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" data-v-4800e765></path></svg></button><button class="cart-btn" data-v-4800e765><svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-4800e765><circle cx="9" cy="21" r="1" data-v-4800e765></circle><circle cx="20" cy="21" r="1" data-v-4800e765></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6" data-v-4800e765></path></svg></button></div>',1))]))),128))])])])])}const jC=et(zA,[["render",VC],["__scopeId","data-v-4800e765"]]),UC={name:"ProductPage",components:{ProductGrid:Mr},data(){return{product:{},reviews:[],loading:!0,error:null,selectedImage:"",currentImageIndex:0,selectedAttributes:{},isFavorite:!1,placeholderImage:"/placeholder-product.svg"}},computed:{productImages(){return!this.product.images||this.product.images.length===0?[this.product.mainImage||this.placeholderImage]:this.product.images.map(e=>e.url||e)},selectableAttributes(){if(!this.product.attributes)return{};const e={};return Object.entries(this.product.attributes).forEach(([t,r])=>{if(typeof r=="string"&&r.includes(",")){const s=r.split(",").map(n=>n.trim()).filter(n=>n);s.length>1&&(e[t]=s)}else Array.isArray(r)&&r.length>1&&(e[t]=r)}),e}},async mounted(){await this.fetchProduct()},watch:{"$route.params.slug"(){this.$route.params.slug&&this.fetchProduct()}},methods:{async fetchProduct(){try{this.loading=!0,this.error=null;const e=this.$route.params.slug,t=await Jr.getBySlug(e);console.log(t),this.product=t.data,this.selectedImage=this.productImages[0],!this.product.category&&this.product.categoryId&&this.fetchCategory(this.product.categoryId),this.setDefaultAttributeSelections(),await this.fetchReviews(e)}catch(e){this.error="Не вдалося завантажити товар",console.error("Error fetching product:",e)}finally{this.loading=!1}},async fetchReviews(e){try{const t=await Pf.getByProductId(e);this.reviews=t.data.data||[]}catch(t){console.error("Error fetching reviews:",t)}},async fetchCategory(e){try{const t=await Mt.getById(e);this.product.category=t.data,console.log("Fetching category:",e)}catch(t){console.error("Error fetching category:",t)}},setDefaultAttributeSelections(){Object.entries(this.selectableAttributes).forEach(([e,t])=>{t.length>0&&(this.selectedAttributes[e]=t[0])})},selectAttribute(e,t){this.selectedAttributes[e]=t},selectImage(e,t){this.selectedImage=e,this.currentImageIndex=t},previousImage(){this.currentImageIndex>0&&(this.currentImageIndex--,this.selectedImage=this.productImages[this.currentImageIndex])},nextImage(){this.currentImageIndex<this.productImages.length-1&&(this.currentImageIndex++,this.selectedImage=this.productImages[this.currentImageIndex])},handleImageError(e){e.target.src=this.placeholderImage},formatPrice(e){if(!e.priceAmount||e.priceAmount==0)return"Ціна не вказана";if(e.priceAmount=Math.round(e.priceAmount),e.priceCurrency){if(e.priceCurrency=="UAH")return`${e.priceAmount} ₴`;if(e.priceCurrency=="USD")return`${e.priceAmount} $`;if(e.priceCurrency=="EUR")return`${e.priceAmount} €`}else return`${e.priceAmount} ₴`;return`${e.priceAmount} ${e.priceCurrency}`},formatDate(e){return new Date(e).toLocaleDateString("uk-UA")},addToCart(){console.log("Adding to cart:",{product:this.product,selectedAttributes:this.selectedAttributes})},toggleFavorite(){this.isFavorite=!this.isFavorite}}},BC={class:"product-page"},GC={class:"breadcrumbs"},qC={key:0},HC={key:1},zC={key:2},KC={key:0,class:"loading-container"},WC={key:1,class:"error-container"},JC={key:2,class:"product-content"},XC={class:"product-main"},YC={class:"product-images"},ZC={class:"main-image"},QC=["src","alt"],eP={class:"image-navigation"},tP=["disabled"],rP=["disabled"],sP={class:"thumbnail-images"},nP=["src","alt","onClick"],oP={class:"product-info"},iP={class:"product-title"},aP={class:"seller-info"},lP={class:"product-options"},cP={class:"option-label"},uP={class:"attribute-options"},dP=["onClick"],fP={class:"product-actions"},pP={class:"price-section"},hP={class:"price"},gP={class:"action-buttons"},mP=["disabled"],vP=["title"],yP={class:"product-details-right"},bP={class:"details-section"},_P={class:"characteristics"},wP={class:"characteristic-label"},EP={class:"characteristic-value"},SP={key:0},AP={class:"reviews-section"},CP={key:0,class:"no-reviews"},PP={key:1,class:"reviews-list"},TP={class:"review-header"},kP={class:"reviewer-name"},RP={class:"review-date"},OP={class:"review-rating"},IP={class:"review-text"},xP={key:0,class:"recommended-section"},$P={class:"container"},FP={class:"recommended-section"},DP={class:"container"};function NP(e,t,r,s,n,o){var l;const i=Ke("router-link"),a=Ke("ProductGrid");return w(),S("div",BC,[c("div",GC,[t[8]||(t[8]=c("span",{class:"fas fa-house"},null,-1)),t[9]||(t[9]=c("span",{class:"separator"},"/",-1)),t[10]||(t[10]=c("span",null,"Каталог",-1)),t[11]||(t[11]=c("span",{class:"separator"},"/",-1)),n.product.category?(w(),S("span",qC,[ve(j(n.product.category.name)+" ",1),t[7]||(t[7]=c("span",{class:"separator"},"/",-1))])):ee("",!0),n.product.name?(w(),S("span",HC,j(n.product.name),1)):(w(),S("span",zC,"Завантаження..."))]),n.loading?(w(),S("div",KC,t[12]||(t[12]=[c("div",{class:"loader"},null,-1),c("p",null,"Завантаження товару...",-1)]))):n.error?(w(),S("div",WC,[t[13]||(t[13]=c("h2",null,"Помилка завантаження",-1)),c("p",null,j(n.error),1),c("button",{onClick:t[0]||(t[0]=(...u)=>o.fetchProduct&&o.fetchProduct(...u)),class:"retry-btn"},"Спробувати знову")])):n.product?(w(),S("div",JC,[c("div",XC,[c("div",YC,[c("div",ZC,[c("img",{src:n.selectedImage||n.product.mainImage||n.placeholderImage,alt:n.product.name,onError:t[1]||(t[1]=(...u)=>o.handleImageError&&o.handleImageError(...u))},null,40,QC),c("div",eP,[c("button",{onClick:t[2]||(t[2]=(...u)=>o.previousImage&&o.previousImage(...u)),disabled:n.currentImageIndex===0,class:"nav-btn fas fa-arrow-left"}," ‹ ",8,tP),c("button",{onClick:t[3]||(t[3]=(...u)=>o.nextImage&&o.nextImage(...u)),disabled:n.currentImageIndex===o.productImages.length-1,class:"nav-btn fas fa-arrow-right"}," › ",8,rP)])]),c("div",sP,[(w(!0),S(be,null,Ne(o.productImages,(u,d)=>(w(),S("img",{key:d,src:u,alt:`${n.product.name} ${d+1}`,class:Ce({active:n.selectedImage===u}),onClick:f=>o.selectImage(u,d),onError:t[4]||(t[4]=(...f)=>o.handleImageError&&o.handleImageError(...f))},null,42,nP))),128))])]),c("div",oP,[c("h1",iP,j(n.product.name),1),c("div",aP,[t[14]||(t[14]=c("span",{class:"seller-label"},"Продавець:",-1)),ce(i,{to:`/seller/${(l=n.product.company)==null?void 0:l.slug}`,class:"seller-link"},{default:ot(()=>{var u;return[ve(j((u=n.product.company)==null?void 0:u.name),1)]}),_:1},8,["to"])]),c("div",lP,[(w(!0),S(be,null,Ne(o.selectableAttributes,(u,d)=>(w(),S("div",{key:d,class:"option-group"},[c("label",cP,j(d)+":",1),c("div",uP,[(w(!0),S(be,null,Ne(u,f=>(w(),S("button",{key:f,class:Ce(["attribute-option",{active:n.selectedAttributes[d]===f}]),onClick:h=>o.selectAttribute(d,f)},j(f),11,dP))),128))])]))),128))]),c("div",fP,[c("div",pP,[c("span",hP,j(o.formatPrice(n.product)),1)]),c("div",gP,[c("button",{onClick:t[5]||(t[5]=(...u)=>o.addToCart&&o.addToCart(...u)),disabled:!n.product.inStock,class:"buy-btn"},j(n.product.inStock?"Купити":"Немає в наявності"),9,mP),c("button",{onClick:t[6]||(t[6]=(...u)=>o.toggleFavorite&&o.toggleFavorite(...u)),class:Ce(["favorite-btn",{active:n.isFavorite}]),title:n.isFavorite?"Видалити з обраного":"Додати в обране"},t[15]||(t[15]=[c("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[c("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})],-1)]),10,vP)])]),c("div",yP,[c("div",bP,[t[16]||(t[16]=c("h2",null,"Характеристики",-1)),c("div",_P,[(w(!0),S(be,null,Ne(n.product.attributes,(u,d)=>(w(),S("div",{key:d,class:"characteristic-row"},[c("span",wP,j(d),1),c("span",EP,[typeof u=="string"&&u.includes(",")?(w(!0),S(be,{key:0},Ne(u.split(",").map(f=>f.trim()),(f,h)=>(w(),S("span",{key:h,class:"characteristic-value-item"},[ve(j(f),1),h<u.split(",").length-1?(w(),S("br",SP)):ee("",!0)]))),128)):(w(),S(be,{key:1},[ve(j(u),1)],64))])]))),128))])]),c("div",AP,[t[18]||(t[18]=c("h2",null,"Відгуки",-1)),n.reviews.length===0?(w(),S("div",CP,t[17]||(t[17]=[c("p",null,"Поки що немає відгуків про цей товар",-1)]))):(w(),S("div",PP,[(w(!0),S(be,null,Ne(n.reviews,u=>(w(),S("div",{key:u.id,class:"review-item"},[c("div",TP,[c("span",kP,j(u.user.name),1),c("span",RP,j(o.formatDate(u.createdAt)),1)]),c("div",OP,[(w(),S(be,null,Ne(5,d=>c("span",{key:d,class:Ce(["star",{filled:d<=u.rating}])},"★",2)),64))]),c("p",IP,j(u.comment),1)]))),128))]))])])])]),n.product.category&&n.product.category.slug?(w(),S("section",xP,[c("div",$P,[t[19]||(t[19]=c("h2",{class:"section-title"},"Випадкові товари з цієї категорії",-1)),ce(a,{"fetch-params":{categorySlug:n.product.category.slug,status:1,count:4,random:!0},"grid-columns":4,"empty-message":"Рандомні товари поки що недоступні"},null,8,["fetch-params"])])])):ee("",!0),c("section",FP,[c("div",DP,[t[20]||(t[20]=c("h2",{class:"section-title"},"Разом з цим товаром дивляться ще",-1)),ce(a,{"fetch-params":{type:"recommended",status:1,pageSize:8},"grid-columns":4,"empty-message":"Рекомендовані товари поки що недоступні"})])])])):ee("",!0)])}const MP=et(UC,[["render",NP],["__scopeId","data-v-666fadb3"]]),LP=()=>Ae(()=>import("./NotFoundPage-Brt4A09q.js"),__vite__mapDeps([0,1])),VP=()=>Ae(()=>import("./AdminLayout-DpraaOZn.js"),__vite__mapDeps([2,3])),jP=()=>Ae(()=>import("./Dashboard-DgHbdAL-.js"),__vite__mapDeps([4,5,6,7])),UP=()=>Ae(()=>import("./UserList-tkPJnQJH.js"),__vite__mapDeps([8,9,10,11,12,13,14,15,16,17,18])),BP=()=>Ae(()=>import("./UserDetail-NN8NtXT4.js"),__vite__mapDeps([19,9,17,20])),GP=()=>Ae(()=>import("./UserEdit-BCZPRHCl.js"),__vite__mapDeps([21,9,22,23,24,25,26,27])),qP=()=>Ae(()=>import("./UserCreate-OIhHJp4Z.js"),__vite__mapDeps([28,9,22,23,24,25,26,29])),HP=()=>Ae(()=>import("./Products-COuEVauK.js"),__vite__mapDeps([30,11,12,5,6,31,13,14,15,16,10,32])),Sc=()=>Ae(()=>import("./ProductView-BpUvZ2Qe.js"),__vite__mapDeps([33,34,35,31,36])),zP=()=>Ae(()=>import("./ProductEdit-CKOjAklg.js"),__vite__mapDeps([37,34,35,38,39,31,40,41,23,42,43,44])),KP=()=>Ae(()=>import("./ProductCreate-Bg9PcwCl.js"),__vite__mapDeps([45,37,34,35,38,39,31,40,41,23,42,43,44,46])),WP=()=>Ae(()=>import("./ApiTestPage-Bvb5UueQ.js"),__vite__mapDeps([47,31,48])),JP=()=>Ae(()=>import("./ProductEditTest-CCU73LQB.js"),__vite__mapDeps([49,45,37,34,35,38,39,31,40,41,23,42,43,44,46,50])),XP=()=>Ae(()=>import("./EnhancedComponentsTest-D8wXYWjM.js"),__vite__mapDeps([51,38,39,31,40,41,52])),YP=()=>Ae(()=>import("./Categories-CuG-s9dF.js"),__vite__mapDeps([53,15,16,13,14,54])),ZP=()=>Ae(()=>import("./CategoryDetailNew-9HvPVQmN.js"),__vite__mapDeps([55,15,16,56])),QP=()=>Ae(()=>import("./CategoryCreateNew-CvIC3KrT.js"),__vite__mapDeps([57,39,31,40,22,23,24,42,43,25,26,58])),eT=()=>Ae(()=>import("./CategoryEditNew-DIdJB-uH.js"),__vite__mapDeps([59,39,31,40,22,23,24,42,43,25,26,60])),tT=()=>Ae(()=>import("./CategoryTestPage-DVmVnQ-d.js"),__vite__mapDeps([61,62])),rT=()=>Ae(()=>import("./OrderList-C3xZpsf3.js"),__vite__mapDeps([63,64,10,11,12,13,14,65,6])),Ac=()=>Ae(()=>import("./OrderDetailNew-DJImyosd.js"),__vite__mapDeps([66,64,67])),sT=()=>Ae(()=>import("./OrderEditNew-BoYhkpeE.js"),__vite__mapDeps([68,64,31,69])),nT=()=>Ae(()=>import("./SellerRequestList-CLJQ3EqY.js"),__vite__mapDeps([70,71,13,14,15,16,72,6])),oT=()=>Ae(()=>import("./SellerRequestDetail-CqkMEWun.js"),__vite__mapDeps([73,71,5,6,15,16,74])),iT=()=>Ae(()=>import("./SellerApplication-w_T-DTxV.js"),__vite__mapDeps([75,76])),aT=()=>Ae(()=>import("./CompanyList-DNfj7Av0.js"),__vite__mapDeps([77,78,10,11,12,13,14,79])),lT=()=>Ae(()=>import("./CompanyDetail-Bup8Y_pp.js"),__vite__mapDeps([80,78,81])),cT=()=>Ae(()=>import("./CompanyEdit-QaSJxYHz.js"),__vite__mapDeps([82,78,22,23,24,83])),uT=()=>Ae(()=>import("./ReviewList-D3cwVwIF.js"),__vite__mapDeps([84,85,11,12,13,14,10,86])),dT=()=>Ae(()=>import("./ReviewDetail-vPuO7niG.js"),__vite__mapDeps([87,85,88])),fT=()=>Ae(()=>import("./RatingList-DvIiSCuV.js"),__vite__mapDeps([89,90,11,12,13,14,10,91])),pT=()=>Ae(()=>import("./ChatList-DMwJj2nL.js"),__vite__mapDeps([92,93,11,12,13,14,94])),hT=()=>Ae(()=>import("./ChatDetail-BleKqTSQ.js"),__vite__mapDeps([95,93,96])),gT=()=>Ae(()=>import("./AddressList-BX7Date4.js"),__vite__mapDeps([97,98,11,12,13,14,99])),mT=()=>Ae(()=>import("./ApiTest-CQcRMlzN.js"),__vite__mapDeps([100,31,101])),vT=()=>Ae(()=>import("./Settings-DCg3LFqK.js"),__vite__mapDeps([102,103])),yT=()=>Ae(()=>import("./Security-CGibd6P8.js"),__vite__mapDeps([104,105])),bT=()=>Ae(()=>import("./Reports-DIbpkDTf.js"),__vite__mapDeps([106,107])),_T=[{path:"/",name:"Home",component:e_},{path:"/login",name:"Login",component:W1,meta:{guestOnly:!0}},{path:"/register",name:"Register",component:vw,meta:{guestOnly:!0}},{path:"/dashboard",name:"Dashboard",component:zS,meta:{requiresAuth:!0}},{path:"/cart",name:"Cart",component:jC,meta:{requiresAuth:!0}},{path:"/catalog/:slug([a-zA-Z-0-9]+)",name:"Catalog",component:HA,meta:{requiresAuth:!1}},{path:"/product/:slug([a-zA-Z-0-9]+)",name:"Product",component:MP,meta:{requiresAuth:!1}},{path:"/products/top",name:"TopProducts",component:()=>Ae(()=>import("./TopProductsPage-G019V0t4.js"),__vite__mapDeps([108,109])),meta:{requiresAuth:!1}},{path:"/profile",name:"Profile",component:Ec,meta:{requiresAuth:!0}},{path:"/user/profile",name:"UserProfile",component:Ec,meta:{requiresAuth:!0}},{path:"/seller/application",name:"SellerApplication",component:iT,meta:{requiresAuth:!0}},{path:"/admin",component:VP,meta:{requiresAuth:!0,requiresAdminOrModerator:!0},children:[{path:"dashboard",name:"AdminDashboard",component:jP},{path:"users",name:"AdminUsers",component:UP},{path:"users/create",name:"AdminUserCreate",component:qP},{path:"users/:id",name:"AdminUserDetail",component:BP},{path:"users/:id/edit",name:"AdminUserEdit",component:GP},{path:"products",name:"AdminProducts",component:HP},{path:"products/create",name:"AdminProductCreate",component:KP},{path:"products/:id",name:"AdminProductDetail",component:Sc},{path:"products/:id/view",name:"AdminProductView",component:Sc},{path:"products/:id/edit",name:"AdminProductEdit",component:zP},{path:"api-test",name:"AdminApiTest",component:WP},{path:"products/edit-test",name:"AdminProductEditTest",component:JP},{path:"products/enhanced-test",name:"EnhancedComponentsTest",component:XP},{path:"categories",name:"AdminCategories",component:YP},{path:"categories/create",name:"AdminCategoryCreate",component:QP},{path:"categories/:id",name:"AdminCategoryView",component:ZP},{path:"categories/:id/edit",name:"AdminCategoryEdit",component:eT},{path:"categories/test",name:"AdminCategoryTestPage",component:tT},{path:"orders",name:"AdminOrders",component:rT},{path:"orders/:id",name:"AdminOrderDetail",component:Ac},{path:"orders/:id/view",name:"AdminOrderView",component:Ac},{path:"orders/:id/edit",name:"AdminOrderEdit",component:sT},{path:"seller-requests",name:"AdminSellerRequests",component:nT},{path:"seller-requests/:id",name:"AdminSellerRequestDetail",component:oT},{path:"companies",name:"AdminCompanies",component:aT},{path:"companies/:id",name:"AdminCompanyDetail",component:lT},{path:"companies/:id/edit",name:"AdminCompanyEdit",component:cT},{path:"reviews",name:"AdminReviews",component:uT},{path:"reviews/:id",name:"AdminReviewDetail",component:dT},{path:"ratings",name:"AdminRatings",component:fT},{path:"ratings/:id",name:"AdminRatingDetail",component:()=>Ae(()=>import("./RatingDetail-CdHRwgvs.js"),__vite__mapDeps([110,90,111]))},{path:"chats",name:"AdminChats",component:pT},{path:"chats/:id",name:"AdminChatDetail",component:hT},{path:"addresses",name:"AdminAddresses",component:gT},{path:"addresses/create",name:"AdminAddressCreate",component:()=>Ae(()=>import("./AddressCreate-v28eHgUk.js"),__vite__mapDeps([112,98,9,113]))},{path:"addresses/:id",name:"AdminAddressDetail",component:()=>Ae(()=>import("./AddressDetail-NMBm9l2Y.js"),__vite__mapDeps([114,98,115]))},{path:"addresses/:id/edit",name:"AdminAddressEdit",component:()=>Ae(()=>import("./AddressEdit-B8F_mV7e.js"),__vite__mapDeps([116,98,9,117]))},{path:"test",name:"ApiTest",component:mT},{path:"settings",name:"AdminSettings",component:vT,meta:{requiresAdmin:!0}},{path:"security",name:"AdminSecurity",component:yT,meta:{requiresAdmin:!0}},{path:"reports",name:"AdminReports",component:bT},{path:"",redirect:{name:"AdminDashboard"}},{path:":pathMatch(.*)*",redirect:{name:"AdminDashboard"}}]},{path:"/:pathMatch(.*)*",name:"NotFound",component:LP}],Ps=$m({history:lm(),routes:_T});Ps.beforeEach(async(e,t,r)=>{it.dispatch("loading/startRouteChange","Navigating..."),t.path&&(await Ae(async()=>{const{default:a}=await Promise.resolve().then(()=>h0);return{default:a}},void 0)).default.cancelRequestsForRoute(t.path);const s=it.getters["auth/isLoggedIn"],n=it.getters["auth/isAdmin"],o=it.getters["auth/isModerator"];if(s){const i=it.getters["auth/user"];console.log("Current user:",i),console.log("User role:",i==null?void 0:i.role),console.log("Is admin?",n),console.log("Trying to access:",e.fullPath);const a=(i==null?void 0:i.role)==="Admin";console.log("Is admin by direct check?",a)}if(e.matched.some(i=>i.meta.requiresAuth))if(!s)console.log("Not logged in, redirecting to login"),it.dispatch("loading/finishRouteChange"),r({name:"Login",query:{redirect:e.fullPath}});else if(e.matched.some(i=>i.meta.requiresAdminOrModerator)){const i=it.getters["auth/user"],a=i==null?void 0:i.role;let l=!1;typeof a=="string"?l=a==="Admin"||a==="Moderator":typeof a=="number"&&(l=a===3||a===4),!n&&!o&&!l?(console.log("Not admin or moderator, redirecting to dashboard"),it.dispatch("loading/finishRouteChange"),r({name:"Dashboard"})):e.matched.some(u=>u.meta.requiresAdmin)?n?r():(console.log("Admin-only route, but user is not admin, redirecting to admin dashboard"),it.dispatch("loading/finishRouteChange"),r({name:"AdminDashboard"})):r()}else if(e.matched.some(i=>i.meta.requiresAdmin)){const i=it.getters["auth/user"],a=i==null?void 0:i.role;console.log("User role in router guard:",a),console.log("Role type:",typeof a);let l=!1;typeof a=="string"?l=a==="Admin":typeof a=="number"?l=a===4:a&&typeof a=="object"&&(a.hasOwnProperty("value")&&(l=a.value==="Admin"||a.value===4),a.hasOwnProperty("name")&&(l=a.name==="Admin")),console.log("Is admin by direct role check:",l),console.log("Is admin by getter:",n),!n&&!l?(console.log("Not admin, redirecting to dashboard"),it.dispatch("loading/finishRouteChange"),r({name:"Dashboard"})):(console.log("Admin access granted"),r())}else r();else if(e.matched.some(i=>i.meta.guestOnly))if(s){const i=it.getters["auth/user"],a=i==null?void 0:i.role;let l=!1;typeof a=="string"?l=a==="Admin":typeof a=="number"?l=a===4:a&&typeof a=="object"&&(a.hasOwnProperty("value")&&(l=a.value==="Admin"||a.value===4),a.hasOwnProperty("name")&&(l=a.name==="Admin"));let u=!1;typeof a=="string"?u=a==="Moderator":typeof a=="number"?u=a===3:a&&typeof a=="object"&&(a.hasOwnProperty("value")&&(u=a.value==="Moderator"||a.value===3),a.hasOwnProperty("name")&&(u=a.name==="Moderator"));const d=n||l||o||u;console.log("Guest route, user is logged in"),console.log("Should redirect to admin?",d),it.dispatch("loading/finishRouteChange"),r({name:d?"AdminDashboard":"Dashboard"})}else r();else r()});Ps.afterEach(()=>{it.dispatch("loading/finishRouteChange")});const Ra=document.createElement("link");Ra.rel="stylesheet";Ra.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css";document.head.appendChild(Ra);const Oa=ng(a0);Oa.use(Ps);Oa.use(it);Oa.mount("#app");export{ET as A,Ae as B,or as C,gt as D,Hm as E,be as F,Vm as G,Et as H,zr as I,Op as J,wT as K,Ki as L,Ft as M,ia as N,CT as O,AT as P,qT as Q,Sp as R,ST as S,Hu as T,_w as U,wa as V,Zu as W,we as X,rt as Y,Ub as Z,et as _,c as a,ce as b,S as c,ve as d,_n as e,va as f,oe as g,le as h,ir as i,bo as j,ee as k,Nr as l,Ir as m,Ce as n,w as o,Ne as p,Se as q,Ke as r,br as s,j as t,bn as u,_o as v,ot as w,qe as x,hr as y,Ie as z};
