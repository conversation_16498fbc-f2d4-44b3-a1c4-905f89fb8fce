using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;
using Marketplace.Infrastructure.Persistence;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Security;

public class GetLogsQueryHandler : IRequestHandler<GetLogsQuery, PagedResponse<LogResponse>>
{
    private readonly MarketplaceDbContext _context;

    public GetLogsQueryHandler(MarketplaceDbContext context)
    {
        _context = context;
    }

    public async Task<PagedResponse<LogResponse>> Handle(GetLogsQuery request, CancellationToken cancellationToken)
    {
        // Створюємо запит
        var query = _context.Logs
            .Include(l => l.User)
            .AsQueryable();

        // Загальний текстовий фільтр
        if (!string.IsNullOrEmpty(request.Filter))
        {
            query = query.Where(l => l.Message.Contains(request.Filter) ||
                    l.Category.Contains(request.Filter) ||
                    (l.User != null && l.User.Username.Contains(request.Filter)) ||
                    (l.IpAddress != null && l.IpAddress.Contains(request.Filter)) ||
                    (l.EntityType != null && l.EntityType.Contains(request.Filter)));
        }

        // Фільтр по рівню
        if (!string.IsNullOrEmpty(request.Level))
        {
            if (Enum.TryParse<LogLevel>(request.Level, true, out var logLevel))
            {
                query = query.Where(l => l.Level == logLevel);
            }
        }

        // Фільтр по категорії
        if (!string.IsNullOrEmpty(request.Category))
        {
            query = query.Where(l => l.Category.Contains(request.Category));
        }

        // Фільтр по користувачу (ID)
        if (request.UserId.HasValue)
        {
            query = query.Where(l => l.UserId == request.UserId.Value);
        }

        // Фільтр по користувачу (Email)
        if (!string.IsNullOrEmpty(request.UserEmail))
        {
            query = query.Where(l => l.User != null && l.User.Username.Contains(request.UserEmail));
        }

        // Фільтр по entity ID
        if (request.EntityId.HasValue)
        {
            query = query.Where(l => l.EntityId == request.EntityId.Value);
        }

        // Фільтр по entity type
        if (!string.IsNullOrEmpty(request.EntityType))
        {
            query = query.Where(l => l.EntityType != null && l.EntityType.Contains(request.EntityType));
        }

        // Фільтр по IP адресі
        if (!string.IsNullOrEmpty(request.IpAddress))
        {
            query = query.Where(l => l.IpAddress != null && l.IpAddress.Contains(request.IpAddress));
        }

        // Фільтр по даті (від)
        if (request.FromDate.HasValue)
        {
            query = query.Where(l => l.Timestamp >= request.FromDate.Value);
        }

        // Фільтр по даті (до)
        if (request.ToDate.HasValue)
        {
            query = query.Where(l => l.Timestamp <= request.ToDate.Value);
        }

        // Сортування
        if (!string.IsNullOrEmpty(request.OrderBy))
        {
            query = request.OrderBy.ToLower() switch
            {
                "level" => request.Descending ? query.OrderByDescending(l => l.Level) : query.OrderBy(l => l.Level),
                "category" => request.Descending ? query.OrderByDescending(l => l.Category) : query.OrderBy(l => l.Category),
                "message" => request.Descending ? query.OrderByDescending(l => l.Message) : query.OrderBy(l => l.Message),
                "username" => request.Descending ? query.OrderByDescending(l => l.User != null ? l.User.Username : "") : query.OrderBy(l => l.User != null ? l.User.Username : ""),
                "ipaddress" => request.Descending ? query.OrderByDescending(l => l.IpAddress) : query.OrderBy(l => l.IpAddress),
                "timestamp" => request.Descending ? query.OrderByDescending(l => l.Timestamp) : query.OrderBy(l => l.Timestamp),
                _ => request.Descending ? query.OrderByDescending(l => l.Timestamp) : query.OrderBy(l => l.Timestamp)
            };
        }
        else
        {
            query = request.Descending ? query.OrderByDescending(l => l.Timestamp) : query.OrderBy(l => l.Timestamp);
        }

        // Пагінація
        var totalCount = await query.CountAsync(cancellationToken);
        var page = request.Page ?? 1;
        var pageSize = request.PageSize ?? 50;
        var skip = (page - 1) * pageSize;

        var logs = await query
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        // Мапимо результати
        var logResponses = logs.Select(log => new LogResponse
        {
            Id = log.Id,
            Timestamp = log.Timestamp,
            Level = log.Level.ToString(),
            Category = log.Category,
            Message = log.Message,
            UserId = log.UserId,
            UserName = log.User?.Username,
            UserEmail = log.User?.Email.Value,
            EntityId = log.EntityId,
            EntityType = log.EntityType,
            AdditionalData = log.AdditionalData,
            IpAddress = log.IpAddress,
            UserAgent = log.UserAgent
        }).ToList();

        return new PagedResponse<LogResponse>
        {
            Data = logResponses,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
}
