import { ref, reactive } from 'vue'

// Глобальний стан wishlist
const wishlistState = reactive({
  items: [],
  loaded: false
})

// Глобальний стан корзини
const cartState = reactive({
  items: [],
  loaded: false
})

// Список слухачів для подій
const listeners = reactive({
  added: [],
  removed: [],
  cartAdded: []
})

export function useWishlistSync() {
  // Додає товар до глобального wishlist
  const addToGlobalWishlist = (productId) => {
    if (!wishlistState.items.includes(productId)) {
      wishlistState.items.push(productId)
      
      // Повідомляємо всіх слухачів
      listeners.added.forEach(callback => callback(productId))
    }
  }

  // Видаляє товар з глобального wishlist
  const removeFromGlobalWishlist = (productId) => {
    wishlistState.items = wishlistState.items.filter(id => id !== productId)
    
    // Повідомляємо всіх слухачів
    listeners.removed.forEach(callback => callback(productId))
  }

  // Встановлює весь список wishlist
  const setWishlistItems = (items) => {
    wishlistState.items = [...items]
    wishlistState.loaded = true
  }

  // Перевіряє чи товар в wishlist
  const isInWishlist = (productId) => {
    return wishlistState.items.includes(productId)
  }

  // Підписується на додавання товарів
  const onItemAdded = (callback) => {
    listeners.added.push(callback)
  }

  // Підписується на видалення товарів
  const onItemRemoved = (callback) => {
    listeners.removed.push(callback)
  }

  // Відписується від подій
  const offItemAdded = (callback) => {
    const index = listeners.added.indexOf(callback)
    if (index > -1) {
      listeners.added.splice(index, 1)
    }
  }

  const offItemRemoved = (callback) => {
    const index = listeners.removed.indexOf(callback)
    if (index > -1) {
      listeners.removed.splice(index, 1)
    }
  }

  // ===== МЕТОДИ ДЛЯ РОБОТИ З КОРЗИНОЮ =====

  // Додає товар до глобальної корзини та викликає слухачів
  const addToGlobalCart = (productId) => {
    console.log('WishlistSync - Adding to global cart:', productId)

    // Додаємо до глобального стану корзини
    if (!cartState.items.includes(productId)) {
      cartState.items.push(productId)
    }

    // Викликаємо всіх слухачів події додавання в корзину
    listeners.cartAdded.forEach(callback => {
      try {
        callback(productId)
      } catch (error) {
        console.error('Error in cart added listener:', error)
      }
    })
  }

  // Підписується на додавання товарів в корзину
  const onCartItemAdded = (callback) => {
    if (typeof callback === 'function') {
      listeners.cartAdded.push(callback)
    }
  }

  // Відписується від подій додавання в корзину
  const offCartItemAdded = (callback) => {
    const index = listeners.cartAdded.indexOf(callback)
    if (index > -1) {
      listeners.cartAdded.splice(index, 1)
    }
  }

  return {
    // Стан
    wishlistItems: wishlistState.items,
    wishlistLoaded: wishlistState.loaded,
    cartItems: cartState.items,
    cartLoaded: cartState.loaded,

    // Методи wishlist
    addToGlobalWishlist,
    removeFromGlobalWishlist,
    setWishlistItems,
    isInWishlist,

    // Методи корзини
    addToGlobalCart,

    // Події wishlist
    onItemAdded,
    onItemRemoved,
    offItemAdded,
    offItemRemoved,

    // Події корзини
    onCartItemAdded,
    offCartItemAdded
  }
}
