<template>
  <div class="admin-layout">
    <!-- Sidebar Toggle Button (Mobile) -->
    <button class="sidebar-toggle" @click="toggleSidebar">
      <i class="fas" :class="isSidebarCollapsed ? 'fa-bars' : 'fa-times'"></i>
    </button>

    <!-- Sidebar -->
    <aside class="sidebar" :class="{ 'collapsed': isSidebarCollapsed }">
      <div class="sidebar-header">
        <h2 class="sidebar-title">Marketplace</h2>
        <button class="collapse-btn" @click="toggleSidebar">
          <i class="fas" :class="isSidebarCollapsed ? 'fa-angle-right' : 'fa-angle-left'"></i>
        </button>
      </div>
      <ul class="menu-list">
        <li>
          <router-link to="/admin/dashboard" active-class="is-active">
            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
            <span class="menu-text">Dashboard</span>
          </router-link>
        </li>
        <li class="menu-label">Marketplace</li>
        <li>
          <router-link to="/admin/products" active-class="is-active">
            <span class="icon"><i class="fas fa-box"></i></span>
            <span class="menu-text">Products</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/orders" active-class="is-active">
            <span class="icon"><i class="fas fa-shopping-cart"></i></span>
            <span class="menu-text">Orders</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/users" active-class="is-active">
            <span class="icon"><i class="fas fa-users"></i></span>
            <span class="menu-text">Users</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/categories" active-class="is-active">
            <span class="icon"><i class="fas fa-folder"></i></span>
            <span class="menu-text">Categories</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/companies" active-class="is-active">
            <span class="icon"><i class="fas fa-building"></i></span>
            <span class="menu-text">Companies</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/seller-requests" active-class="is-active">
            <span class="icon"><i class="fas fa-user-tie"></i></span>
            <span class="menu-text">Seller Requests</span>
          </router-link>
        </li>
        <li class="menu-label">Customer Service</li>
        <li>
          <router-link to="/admin/reviews" active-class="is-active">
            <span class="icon"><i class="fas fa-star"></i></span>
            <span class="menu-text">Reviews</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/ratings" active-class="is-active">
            <span class="icon"><i class="fas fa-thumbs-up"></i></span>
            <span class="menu-text">Ratings</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/chats" active-class="is-active">
            <span class="icon"><i class="fas fa-comments"></i></span>
            <span class="menu-text">Chats</span>
          </router-link>
        </li>
        <li class="menu-label">Management</li>
        <li>
          <router-link to="/admin/addresses" active-class="is-active">
            <span class="icon"><i class="fas fa-map-marker-alt"></i></span>
            <span class="menu-text">Addresses</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/reports" active-class="is-active">
            <span class="icon"><i class="fas fa-chart-bar"></i></span>
            <span class="menu-text">Reports</span>
          </router-link>
        </li>
        <li class="menu-label">System</li>
        <li>
          <router-link to="/admin/settings" active-class="is-active">
            <span class="icon"><i class="fas fa-cog"></i></span>
            <span class="menu-text">Settings</span>
          </router-link>
        </li>
        <li>
          <router-link to="/admin/security" active-class="is-active">
            <span class="icon"><i class="fas fa-shield-alt"></i></span>
            <span class="menu-text">Security & Logs</span>
          </router-link>
        </li>
      </ul>
    </aside>

    <!-- Main Content -->
    <div class="admin-content" :class="{ 'expanded': isSidebarCollapsed }">
      <div class="admin-header">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h1 class="title">{{ pageTitle }}</h1>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="dropdown is-right" :class="{ 'is-active': isDropdownActive }">
                <div class="dropdown-trigger">
                  <button class="button" @click="toggleDropdown">
                    <div class="user-avatar">{{ currentUser.username.charAt(0).toUpperCase() }}</div>
                    <span class="ml-2">{{ currentUser.username }}</span>
                    <span class="icon is-small ml-2">
                      <i class="fas fa-angle-down"></i>
                    </span>
                  </button>
                </div>
                <div class="dropdown-menu">
                  <div class="dropdown-content">
                    <router-link to="/profile" class="dropdown-item">
                      <span class="icon is-small mr-2">
                        <i class="fas fa-user"></i>
                      </span>
                      <span>Profile</span>
                    </router-link>
                    <hr class="dropdown-divider">
                    <a class="dropdown-item" @click="logout">
                      <span class="icon is-small mr-2">
                        <i class="fas fa-sign-out-alt"></i>
                      </span>
                      <span>Logout</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="admin-body">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';

export default {
  name: 'AdminLayout',
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    const isDropdownActive = ref(false);
    const isSidebarCollapsed = ref(false);

    // Get sidebar state from localStorage if available
    onMounted(() => {
      // Set admin attribute for font styling
      document.body.setAttribute('data-admin', 'true');

      const savedState = localStorage.getItem('admin-sidebar-collapsed');
      if (savedState !== null) {
        isSidebarCollapsed.value = savedState === 'true';
      }

      // Add event listeners
      document.addEventListener('click', handleClickOutside);

      // Handle responsive behavior on load
      handleResponsiveLayout();
      window.addEventListener('resize', handleResponsiveLayout);
    });

    // Remove event listeners when component is unmounted
    onUnmounted(() => {
      // Remove admin attribute
      document.body.removeAttribute('data-admin');

      document.removeEventListener('click', handleClickOutside);
      window.removeEventListener('resize', handleResponsiveLayout);
    });

    const currentUser = computed(() => store.getters['auth/user']);

    const pageTitle = computed(() => {
      const routeName = route.name;
      if (routeName === 'AdminDashboard') return 'Dashboard';
      if (routeName === 'AdminUsers') return 'Users Management';
      if (routeName === 'AdminProducts') return 'Products Management';
      if (routeName === 'AdminCategories') return 'Categories Management';
      if (routeName === 'AdminOrders') return 'Orders Management';
      if (routeName === 'AdminSellerRequests') return 'Seller Applications';
      if (routeName === 'AdminCompanies') return 'Companies Management';
      if (routeName === 'AdminReviews') return 'Reviews Management';
      if (routeName === 'AdminRatings') return 'Ratings Management';
      if (routeName === 'AdminChats') return 'Chat Management';
      if (routeName === 'AdminAddresses') return 'Address Management';
      if (routeName === 'AdminReports') return 'Reports & Analytics';
      if (routeName === 'AdminSettings') return 'System Settings';
      if (routeName === 'AdminSecurity') return 'Security & Logs';
      return 'Admin Panel';
    });

    const toggleDropdown = () => {
      isDropdownActive.value = !isDropdownActive.value;
    };

    const toggleSidebar = () => {
      isSidebarCollapsed.value = !isSidebarCollapsed.value;
      // Save state to localStorage
      localStorage.setItem('admin-sidebar-collapsed', isSidebarCollapsed.value);
    };

    // Handle responsive layout
    const handleResponsiveLayout = () => {
      if (window.innerWidth <= 768) {
        isSidebarCollapsed.value = true;
      }
    };

    const logout = async () => {
      await store.dispatch('auth/logout');
      router.push('/login');
    };

    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (isDropdownActive.value && !event.target.closest('.dropdown')) {
        isDropdownActive.value = false;
      }
    };

    return {
      currentUser,
      pageTitle,
      isDropdownActive,
      isSidebarCollapsed,
      toggleDropdown,
      toggleSidebar,
      logout
    };
  }
};
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
  display: flex;
  background-color: var(--admin-bg-primary);
  position: relative;
  width: 100%;
  overflow-x: hidden;
}

.columns {
  width: 100%;
  margin: 0;
}

/* Sidebar Toggle Button (Mobile) */
.sidebar-toggle {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 1000;
  background-color: var(--admin-primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--admin-shadow-lg);
  transition: all var(--admin-transition-normal);
  display: none;
}

.sidebar-toggle:hover {
  background-color: var(--admin-primary-dark);
  transform: scale(1.05);
}

/* Sidebar */
.sidebar {
  background-color: var(--admin-sidebar-bg);
  color: var(--admin-gray-100);
  padding: 0;
  height: 100vh;
  position: sticky;
  top: 0;
  box-shadow: var(--admin-shadow-lg);
  z-index: 100;
  width: 320px;
  transition: all var(--admin-transition-normal);
  overflow-y: hidden;
  overflow-x: hidden;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 1.25rem;
  background-color: var(--darker-bg);
  margin-bottom: 0.5rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-title {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.collapse-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.menu-list {
  padding: 0 0.75rem;
}

.menu-label {
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.7rem;
  padding: 0 0.75rem;
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-list a {
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  padding: 0.6rem 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.25rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
}

.menu-list a:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  transform: translateX(2px);
}

.menu-list a.is-active {
  background-color: var(--accent-color);
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.menu-list .icon {
  margin-right: 0.75rem;
  font-size: 1rem;
  width: 20px;
  text-align: center;
  opacity: 0.8;
  flex-shrink: 0;
}

.sidebar.collapsed .menu-text,
.sidebar.collapsed .menu-label,
.sidebar.collapsed .sidebar-title,
.sidebar.collapsed .tag {
  display: none;
}

.sidebar.collapsed .menu-list a {
  justify-content: center;
  padding: 0.6rem;
}

.sidebar.collapsed .menu-list .icon {
  margin-right: 0;
}

.sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: 1.25rem 0;
}

/* Main Content */
.admin-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--dark-bg);
  margin-left: 320px;
  width: calc(100% - 320px);
  transition: all 0.3s ease;
  flex: 1;
}

.admin-content.expanded {
  width: calc(100% - 70px);
}

.admin-header {
  margin-bottom: 1.5rem;
  padding: 1rem 1.25rem;
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 90;
}

.admin-body {
  flex: 1;
}

.title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.5rem;
  margin: 0;
}

.dropdown-trigger button {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.dropdown-trigger button:hover {
  background-color: var(--darker-bg);
}

.dropdown-menu {
  min-width: 12rem;
}

.dropdown-content {
  padding: 0.5rem 0;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.dropdown-item {
  padding: 0.75rem 1rem;
  color: var(--text-primary);
}

.dropdown-item:hover {
  background-color: var(--darker-bg);
  color: var(--text-primary);
}

.has-text-danger {
  color: var(--danger-color) !important;
}

.level {
  margin-bottom: 0 !important;
}

.mr-2 {
  margin-right: 0.5rem !important;
}

.ml-2 {
  margin-left: 0.5rem !important;
}

.ml-auto {
  margin-left: auto !important;
}

.user-avatar {
  width: 28px;
  height: 28px;
  background-color: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.tag {
  background-color: var(--warning-color);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.tag.is-warning {
  background-color: var(--warning-color);
}

.tag.is-success {
  background-color: var(--success-color);
}

.tag.is-danger {
  background-color: var(--danger-color);
}

.tag.is-rounded {
  border-radius: 9999px;
}

/* Responsive adjustments */
@media screen and (max-width: 1024px) {
  .sidebar {
    width: 200px;
  }

  .admin-content {
    width: calc(100% - 200px);
  }

  .admin-content.expanded {
    width: calc(100% - 70px);
  }
}

@media screen and (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    transform: translateX(-100%);
    box-shadow: none;
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .sidebar:not(.collapsed) {
    transform: translateX(0);
    width: 320px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  }

  .admin-content,
  .admin-content.expanded {
    width: 100%;
    padding: 1rem;
  }

  .sidebar-toggle {
    display: flex;
  }

  .admin-header {
    padding-left: 60px;
  }
}
</style>
