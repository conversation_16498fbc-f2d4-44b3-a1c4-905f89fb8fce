# Кабінет продавця

Цей модуль реалізує функціонал кабінету продавця для користувачів з різними ролями.

## Компоненти

### SellerDashboard.vue
Головна сторінка кабінету продавця, яка адаптується залежно від ролі користувача:

#### Для користувачів з роллю "Buyer":
- Показує інформацію про можливість стати продавцем
- Відображає кнопку для подачі заявки на становлення продавцем
- Показує список існуючих заявок користувача зі статусами

#### Для користувачів з роллю "Seller" або "SellerOwner":
- Відображає статистику продажів (товари, замовлення, дохід, відгуки)
- Показує швидкі дії (додати товар, переглянути замовлення, аналітика, налаштування)
- Повноцінний кабінет продавця

#### Для інших ролей:
- Показує повідомлення про відсутність доступу

### SellerApplication.vue
Форма для подачі заявки на становлення продавцем:

#### Функціонал:
- Перевірка наявності існуючих заявок
- Форма з секціями:
  - Інформація про компанію
  - Адреса компанії
  - Фінансова інформація
  - Додаткова інформація
- Автоматична генерація slug для компанії
- Валідація полів
- Відправка заявки через API

#### Поля форми:
**Компанія:**
- Назва компанії (обов'язково)
- URL компанії (обов'язково, автогенерація)
- Опис компанії (обов'язково)
- Контактний email (обов'язково)
- Контактний телефон (обов'язково)

**Адреса:**
- Область (обов'язково)
- Місто (обов'язково)
- Вулиця та номер будинку (обов'язково)
- Поштовий індекс (обов'язково)

**Фінанси:**
- Номер банківського рахунку (обов'язково)
- Назва банку (обов'язково)
- МФО банку (обов'язково)
- Податковий номер (обов'язково)

**Додатково:**
- Додаткова інформація (необов'язково)

## Роути

- `/seller` - Кабінет продавця (SellerDashboard)
- `/seller/application` - Форма заявки на становлення продавцем (SellerApplication)

## API Endpoints

Компоненти використовують наступні API endpoints:

- `GET /api/users/me/seller-requests` - Отримання заявок користувача
- `POST /api/users/me/seller-requests` - Створення нової заявки

## Навігація

Додано нову іконку в header для доступу до кабінету продавця:
- Іконка магазину (fas fa-store) поруч з профілем користувача
- Доступна тільки для авторизованих користувачів

## Статуси заявок

- **Pending** (На розгляді) - Заявка подана і очікує розгляду
- **Approved** (Схвалено) - Заявка схвалена, користувач отримав роль продавця
- **Rejected** (Відхилено) - Заявка відхилена, можна подати нову

## Особливості

1. **Адаптивний дизайн** - всі компоненти адаптовані для мобільних пристроїв
2. **Валідація** - клієнтська валідація форм з відповідними повідомленнями
3. **UX** - зручний інтерфейс з чіткими інструкціями та статусами
4. **Безпека** - всі роути захищені авторизацією
5. **Інтеграція** - повна інтеграція з існуючою системою ролей та API

## Майбутні розширення

Для продавців планується додати:
- Управління товарами
- Аналітика продажів
- Управління замовленнями
- Налаштування магазину
- Фінансові звіти
