const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/SalesChart-Dr1mm15H.js","assets/chart-DFPvZH9M.js","assets/index-L-hJxM_5.js","assets/index-n-75hzeL.css","assets/SalesChart-B7X_q7FE.css","assets/OrdersByStatusChart-BD-BTj_n.js","assets/OrdersByStatusChart-DSQuQRS8.css","assets/SiteProfitChart-CrlzZT1P.js","assets/SiteProfitChart-CuTymJsg.css","assets/PendingSellerRequestsOptimized-DNsmUgHk.js","assets/PendingSellerRequestsOptimized-D549qvnR.css"])))=>i.map(i=>d[i]);
import{q as S,_ as da,e as ra,h as $,r as oa,c as b,o as m,a,b as g,d as P,w as p,F as Ta,p as Da,t as u,k as H,m as sa,g as k,i as Pa,j as $a,n as y,l as Y,x as ea,y as ta,s as M,z as U,A as L,S as V,B}from"./index-L-hJxM_5.js";import{S as Ra}from"./StatusBadge-CWG2IOJy.js";/* empty css                                                                    */const N={async getDashboardData(){var l,c;try{const e=await S.get("/api/admin/dashboard",{timeout:15e3});if(console.log("Dashboard data response:",e),console.log("Full API response:",{status:e.status,statusText:e.statusText,headers:e.headers,data:e.data}),!e.data)throw new Error("Empty response from server");return e.data.data&&e.data.data.stats&&(e.data.data.stats.revenue=Number(e.data.data.stats.revenue)),e.data.data?e.data.data:(e.data.success!==void 0,e.data)}catch(e){console.error("Error fetching dashboard data:",e);const d=((c=(l=e.response)==null?void 0:l.data)==null?void 0:c.message)||"Failed to load dashboard data from the server";throw new Error(d)}},async getSalesData(l="month"){var c,e;try{const d=await S.get(`/api/admin/dashboard/sales?period=${l}`,{timeout:15e3});if(console.log("Sales data response:",d),!d.data||!d.data.data)throw new Error("Invalid response format from server");return d.data.data}catch(d){console.error("Error fetching sales data:",d);const w=((e=(c=d.response)==null?void 0:c.data)==null?void 0:e.message)||`Failed to load sales data for period: ${l}`;throw new Error(w)}},async getOrdersByStatus(){var l,c;try{const e=await S.get("/api/admin/dashboard/orders-by-status",{timeout:15e3});if(console.log("Orders by status response:",e),!e.data||!e.data.data)throw new Error("Invalid response format from server");return e.data.data}catch(e){console.error("Error fetching orders by status:",e);const d=((c=(l=e.response)==null?void 0:l.data)==null?void 0:c.message)||"Failed to load orders by status data";throw new Error(d)}},async getRecentOrders(l=5){var c,e;try{const d=await S.get(`/api/admin/dashboard/recent-orders?limit=${l}`,{timeout:15e3});if(console.log("Recent orders response:",d),!d.data||!d.data.data)throw new Error("Invalid response format from server");return d.data.data}catch(d){console.error("Error fetching recent orders:",d);const w=((e=(c=d.response)==null?void 0:c.data)==null?void 0:e.message)||"Failed to load recent orders";throw new Error(w)}},async getPendingSellerRequests(l=5){var c,e;try{const d=await S.get(`/api/admin/dashboard/pending-seller-requests?limit=${l}`,{timeout:15e3});if(console.log("Pending seller requests response:",d),!d.data||!d.data.data)throw new Error("Invalid response format from server");return d.data.data}catch(d){console.error("Error fetching pending seller requests:",d);const w=((e=(c=d.response)==null?void 0:c.data)==null?void 0:e.message)||"Failed to load pending seller requests";throw new Error(w)}},async getSiteProfitData(l="month"){var c,e;try{const d=await S.get(`/api/admin/dashboard/site-profit?period=${l}`,{timeout:15e3});if(console.log("Site profit data response:",d),!d.data||!d.data.data)throw new Error("Invalid response format from server");return d.data.data}catch(d){console.error("Error fetching site profit data:",d);const w=((e=(c=d.response)==null?void 0:c.data)==null?void 0:e.message)||`Failed to load site profit data for period: ${l}`;throw new Error(w)}}},Aa={class:"admin-recent-orders"},Ea={class:"admin-recent-orders__header"},Ca={class:"admin-recent-orders__content"},Sa={key:0,class:"admin-recent-orders__loading"},Ia={key:1,class:"admin-recent-orders__empty"},Na={key:2,class:"admin-recent-orders__list"},Oa=["onClick"],xa={class:"admin-recent-orders__card-header"},Fa={class:"admin-recent-orders__order-meta"},Ma={class:"admin-recent-orders__order-id"},Ua={class:"admin-recent-orders__order-date"},La={class:"admin-recent-orders__status-wrapper"},Va={class:"admin-recent-orders__card-body"},Ba={class:"admin-recent-orders__customer-section"},Ha={class:"admin-recent-orders__customer-details"},za={class:"admin-recent-orders__customer-name"},ja={class:"admin-recent-orders__customer-email"},Ya={class:"admin-recent-orders__order-summary"},Ga={class:"admin-recent-orders__total-section"},Ka={class:"admin-recent-orders__total-amount"},Qa={class:"admin-recent-orders__items-section"},Ja={class:"admin-recent-orders__items-count"},Wa={class:"admin-recent-orders__card-footer"},Xa=["onClick","title"],Za=["onClick","title"],as={__name:"RecentOrdersOptimized",props:{orders:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["process-order"],setup(l,{emit:c}){const e=l,d=c,w=ra();$(()=>e.orders&&e.orders.length>0);const f=r=>{const n=typeof r=="string"?Number(r):r;return isNaN(n)?"UAH 0.00":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",currencyDisplay:"code"}).format(n).replace("UAH","UAH")},q=r=>{if(!r)return"Unknown";try{const n=new Date(r);return new Intl.DateTimeFormat("uk-UA",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(n)}catch(n){return console.error("Error formatting date:",n),"Invalid date"}},R=r=>{w.push(`/admin/orders/${r}`)},h=r=>{d("process-order",r)};return(r,n)=>{const T=oa("router-link");return m(),b("div",Aa,[a("div",Ea,[n[1]||(n[1]=a("h3",{class:"admin-recent-orders__title"},[a("i",{class:"admin-recent-orders__icon fas fa-shopping-cart"}),P(" Recent Orders ")],-1)),g(T,{to:"/admin/orders",class:"admin-recent-orders__view-all"},{default:p(()=>n[0]||(n[0]=[a("span",null,"View All",-1),a("i",{class:"fas fa-arrow-right"},null,-1)])),_:1})]),a("div",Ca,[l.loading?(m(),b("div",Sa,n[2]||(n[2]=[a("div",{class:"admin-recent-orders__loading-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-recent-orders__loading-text"},"Loading recent orders...",-1)]))):!l.orders||l.orders.length===0?(m(),b("div",Ia,n[3]||(n[3]=[a("div",{class:"admin-recent-orders__empty-icon"},[a("i",{class:"fas fa-shopping-cart"})],-1),a("h4",{class:"admin-recent-orders__empty-title"},"No Recent Orders",-1),a("p",{class:"admin-recent-orders__empty-text"}," Orders will appear here once customers start making purchases. ",-1)]))):(m(),b("div",Na,[(m(!0),b(Ta,null,Da(l.orders,i=>(m(),b("div",{key:i.id,class:"admin-recent-orders__card",onClick:A=>R(i.id)},[a("div",xa,[a("div",Fa,[a("span",Ma,"#"+u(i.id.slice(-8)),1),a("span",Ua,u(q(i.createdAt)),1)]),a("div",La,[g(Ra,{status:i.status,type:"order"},null,8,["status"])])]),a("div",Va,[a("div",Ba,[n[4]||(n[4]=a("div",{class:"admin-recent-orders__customer-avatar"},[a("i",{class:"fas fa-user"})],-1)),a("div",Ha,[a("div",za,u(i.customerName),1),a("div",ja,u(i.customerEmail),1)])]),a("div",Ya,[a("div",Ga,[n[5]||(n[5]=a("div",{class:"admin-recent-orders__total-label"},"Total",-1)),a("div",Ka,u(f(i.total)),1)]),a("div",Qa,[a("div",Ja,u(i.itemsCount)+" items",1)])])]),a("div",Wa,[a("button",{class:"admin-recent-orders__view-btn",onClick:sa(A=>R(i.id),["stop"]),title:`View order #${i.id}`},n[6]||(n[6]=[a("i",{class:"fas fa-arrow-right"},null,-1),a("span",null,"View Details",-1)]),8,Xa),i.status==="pending"?(m(),b("button",{key:0,class:"admin-recent-orders__process-btn",onClick:sa(A=>h(i.id),["stop"]),title:`Process order #${i.id}`},n[7]||(n[7]=[a("i",{class:"fas fa-check"},null,-1),a("span",null,"Process",-1)]),8,Za)):H("",!0)])],8,Oa))),128))]))])])}}},ss=da(as,[["__scopeId","data-v-57c1a9c1"]]),es={class:"admin-dashboard"},ts={class:"admin-dashboard__header"},ds={class:"admin-dashboard__header-content"},rs={class:"admin-dashboard__actions"},os=["disabled"],ns={key:0,class:"admin-dashboard__alert admin-dashboard__alert--error"},is={class:"admin-dashboard__alert-content"},ls={class:"admin-dashboard__alert-message"},cs={key:1,class:"admin-dashboard__loading"},us={class:"admin-dashboard__loading-content"},_s={key:0,class:"admin-dashboard__loading-retry"},ms={key:2,class:"admin-dashboard__empty"},vs={key:3,class:"admin-dashboard__main"},hs={class:"admin-dashboard__kpi-section"},ps={class:"admin-dashboard__kpi-grid"},bs={class:"admin-dashboard__kpi-card admin-dashboard__kpi-card--users"},fs={class:"admin-dashboard__kpi-header"},gs={class:"admin-dashboard__kpi-content"},ks={class:"admin-dashboard__kpi-value"},ys={class:"admin-dashboard__kpi-card admin-dashboard__kpi-card--orders"},ws={class:"admin-dashboard__kpi-header"},qs={class:"admin-dashboard__kpi-content"},Ts={class:"admin-dashboard__kpi-value"},Ds={class:"admin-dashboard__kpi-card admin-dashboard__kpi-card--products"},Ps={class:"admin-dashboard__kpi-header"},$s={class:"admin-dashboard__kpi-content"},Rs={class:"admin-dashboard__kpi-value"},As={class:"admin-dashboard__kpi-card admin-dashboard__kpi-card--revenue"},Es={class:"admin-dashboard__kpi-header"},Cs={class:"admin-dashboard__kpi-content"},Ss={class:"admin-dashboard__kpi-value"},Is={class:"admin-dashboard__kpi-card admin-dashboard__kpi-card--categories"},Ns={class:"admin-dashboard__kpi-content"},Os={class:"admin-dashboard__kpi-value"},xs={class:"admin-dashboard__kpi-card admin-dashboard__kpi-card--profit"},Fs={class:"admin-dashboard__kpi-header"},Ms={class:"admin-dashboard__kpi-content"},Us={class:"admin-dashboard__kpi-value"},Ls={class:"admin-dashboard__charts-section"},Vs={class:"admin-dashboard__charts-grid"},Bs={class:"admin-dashboard__chart-card admin-dashboard__chart-card--main"},Hs={class:"admin-dashboard__chart-header"},zs={class:"admin-dashboard__chart-controls"},js={class:"admin-dashboard__chart-content"},Ys={class:"admin-dashboard__chart-card admin-dashboard__chart-card--side"},Gs={class:"admin-dashboard__chart-content"},Ks={class:"admin-dashboard__chart-card admin-dashboard__chart-card--full"},Qs={class:"admin-dashboard__chart-header"},Js={class:"admin-dashboard__chart-controls"},Ws={class:"admin-dashboard__chart-content"},Xs={class:"admin-dashboard__quick-actions-section"},Zs={class:"admin-dashboard__quick-actions-grid"},ae={key:0,class:"admin-dashboard__quick-action-badge"},se={class:"admin-dashboard__activity-section"},ee={class:"admin-dashboard__activity-grid"},te={class:"admin-dashboard__activity-card"},de={class:"admin-dashboard__activity-card"},re={__name:"Dashboard",setup(l){ra();const c=L(()=>B(()=>import("./SalesChart-Dr1mm15H.js"),__vite__mapDeps([0,1,2,3,4]))),e=L(()=>B(()=>import("./OrdersByStatusChart-BD-BTj_n.js"),__vite__mapDeps([5,1,2,3,6]))),d=L(()=>B(()=>import("./SiteProfitChart-CrlzZT1P.js"),__vite__mapDeps([7,1,2,3,8]))),w=L(()=>B(()=>import("./PendingSellerRequestsOptimized-DNsmUgHk.js"),__vite__mapDeps([9,2,3,10]))),f=k(!0),q=k(null),R=k(0),h=k(null),r=k({products:0,users:0,orders:0,revenue:0,categories:0}),n=k([]),T=k([]),i=k([]),A=k("week"),O=k("week"),z=k([]),I=k([]),G=$(()=>!f.value&&r.value.products===0&&r.value.users===0&&r.value.orders===0&&r.value.revenue===0&&r.value.categories===0&&n.value.length===0&&T.value.length===0),na=$(()=>{const o=typeof r.value.revenue=="string"?Number(r.value.revenue):r.value.revenue;return isNaN(o)?0:o*.15}),K=$(()=>!i.value||i.value.length===0?0:i.value.reduce((o,s)=>o+(s.value||0),0)),ia=$(()=>!n.value||n.value.length===0?0:n.value.reduce((o,s)=>o+(s.value||0),0));$(()=>!r.value.orders||r.value.orders===0?0:ia.value/r.value.orders),$(()=>!i.value||i.value.length===0?0:K.value/7*30),$(()=>{if(!T.value||T.value.length===0)return[];const o=["#3b82f6","#10b981","#f59e0b","#ef4444","#6b7280"];return T.value.map((s,t)=>({name:s.label||s.name,count:s.value||s.count,color:o[t%o.length]}))});const x=o=>new Intl.NumberFormat("uk-UA").format(o||0),Q=o=>{const s=typeof o=="string"?Number(o):o;return isNaN(s)?(console.error("Invalid currency value:",o),"UAH 0.00"):new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",currencyDisplay:"code"}).format(s).replace("UAH","UAH")},J=()=>Math.random()>.5?"positive":"negative",la=()=>J()==="positive"?"fas fa-arrow-up":"fas fa-arrow-down",ca=()=>(Math.random()*20+5).toFixed(1),W=()=>Math.random()>.3?"positive":"negative",ua=()=>W()==="positive"?"fas fa-arrow-up":"fas fa-arrow-down",_a=()=>(Math.random()*15+3).toFixed(1),X=()=>Math.random()>.4?"positive":"negative",ma=()=>X()==="positive"?"fas fa-arrow-up":"fas fa-arrow-down",va=()=>(Math.random()*10+2).toFixed(1),j=()=>Math.random()>.2?"positive":"negative",ha=()=>j()==="positive"?"fas fa-arrow-up":"fas fa-arrow-down",Z=()=>(Math.random()*25+8).toFixed(1),aa=()=>j(),pa=()=>aa()==="positive"?"fas fa-arrow-up":"fas fa-arrow-down",ba=()=>Z();let D=null;const F=async()=>{D&&clearTimeout(D),h.value&&(clearInterval(h.value),h.value=null),f.value=!0,q.value=null,R.value=0,h.value=setInterval(()=>{R.value++},1e3),D=setTimeout(async()=>{var o,s;try{console.log("Fetching dashboard data...");const t=await N.getDashboardData();if(console.log("Dashboard data received:",t),!t)throw new Error("No data received from server");if(t.stats){if(r.value={products:t.stats.products||0,users:t.stats.users||0,orders:t.stats.orders||0,revenue:Number(t.stats.revenue)||0,categories:t.stats.categories||0},(typeof r.value.revenue!="number"||isNaN(r.value.revenue))&&(console.warn("Revenue is not a valid number, converting:",t.stats.revenue),r.value.revenue=0,typeof t.stats.revenue=="string")){const v=parseFloat(t.stats.revenue.replace(/[^\d.-]/g,""));isNaN(v)||(r.value.revenue=v)}n.value=t.salesData||[],T.value=t.ordersByStatus||[],z.value=t.recentOrders||[],I.value=t.sellerRequests||[];try{const v=await N.getSalesData("week");n.value=v;const _=await N.getSiteProfitData("week");i.value=_}catch(v){console.warn("Error loading initial chart data:",v),i.value=(t.salesData||[]).map(_=>({..._,value:_.value*.15}))}}else console.log("Trying alternative data structure"),r.value={products:t.products||0,users:t.users||0,orders:t.orders||0,revenue:Number(t.revenue)||0,categories:t.categories||0},n.value=[],T.value=[],z.value=[],I.value=[],i.value=[];G.value&&console.warn("Dashboard data is empty")}catch(t){!((o=t.message)!=null&&o.includes("canceled"))&&!((s=t.message)!=null&&s.includes("aborted"))&&(console.error("Error fetching dashboard data:",t),q.value=`Failed to load dashboard data: ${t.message}. Please try again later.`)}finally{h.value&&(clearInterval(h.value),h.value=null),f.value=!1,D=null}},100)},fa=()=>{D&&(clearTimeout(D),D=null),h.value&&(clearInterval(h.value),h.value=null),f.value=!1,R.value=0,setTimeout(()=>{F()},500)};let E=null;const ga=async o=>{E&&clearTimeout(E);const s=o.target?o.target.value:o;A.value=s,E=setTimeout(async()=>{var t,v;try{const _=await N.getSalesData(s);n.value=_}catch(_){!((t=_.message)!=null&&t.includes("canceled"))&&!((v=_.message)!=null&&v.includes("aborted"))&&(console.error("Error fetching sales data:",_),q.value=_.message||`Failed to load sales data for period: ${s}`)}finally{E=null}},100)};let C=null;const ka=async o=>{C&&clearTimeout(C);const s=o.target?o.target.value:o;O.value=s,C=setTimeout(async()=>{var t,v;try{const _=await N.getSiteProfitData(s);i.value=_}catch(_){!((t=_.message)!=null&&t.includes("canceled"))&&!((v=_.message)!=null&&v.includes("aborted"))&&(console.error("Error fetching profit data:",_),q.value=_.message||`Failed to load profit data for period: ${s}`)}finally{C=null}},100)},ya=o=>{console.log("Processing order:",o)},wa=o=>{console.log("Approving seller request:",o)},qa=o=>{console.log("Rejecting seller request:",o)};return Pa(()=>{F()}),$a(()=>{D&&clearTimeout(D),E&&clearTimeout(E),C&&clearTimeout(C),h.value&&(clearInterval(h.value),h.value=null)}),(o,s)=>{const t=oa("router-link");return m(),b("div",es,[a("header",ts,[a("div",ds,[s[3]||(s[3]=a("div",{class:"admin-dashboard__title-section"},[a("h1",{class:"admin-dashboard__title"},[a("i",{class:"admin-dashboard__title-icon fas fa-tachometer-alt"}),P(" Dashboard ")]),a("p",{class:"admin-dashboard__subtitle"}," Real-time overview of your marketplace performance ")],-1)),a("div",rs,[a("button",{class:y(["admin-dashboard__refresh-btn",{"admin-dashboard__refresh-btn--loading":f.value}]),onClick:F,disabled:f.value},[a("i",{class:y(["fas fa-sync-alt",{"fa-spin":f.value}])},null,2),a("span",null,u(f.value?"Refreshing...":"Refresh"),1)],10,os)])])]),q.value?(m(),b("div",ns,[s[6]||(s[6]=a("div",{class:"admin-dashboard__alert-icon"},[a("i",{class:"fas fa-exclamation-circle"})],-1)),a("div",is,[s[4]||(s[4]=a("h4",{class:"admin-dashboard__alert-title"},"Error Loading Data",-1)),a("p",ls,u(q.value),1)]),a("button",{class:"admin-dashboard__alert-close",onClick:s[0]||(s[0]=v=>q.value=null)},s[5]||(s[5]=[a("i",{class:"fas fa-times"},null,-1)]))])):H("",!0),f.value&&!r.value.products?(m(),b("div",cs,[a("div",us,[s[8]||(s[8]=Y('<div class="admin-dashboard__loading-spinner" data-v-9be0fd0a><i class="fas fa-spinner fa-pulse" data-v-9be0fd0a></i></div><h3 class="admin-dashboard__loading-title" data-v-9be0fd0a>Loading Dashboard</h3><p class="admin-dashboard__loading-text" data-v-9be0fd0a> Fetching the latest data from your marketplace... </p><div class="admin-dashboard__loading-progress" data-v-9be0fd0a><div class="admin-dashboard__loading-bar" data-v-9be0fd0a></div></div>',4)),R.value>10?(m(),b("div",_s,[a("button",{class:"admin-dashboard__retry-btn",onClick:fa},s[7]||(s[7]=[a("i",{class:"fas fa-exclamation-triangle"},null,-1),a("span",null,"Taking too long? Click to retry",-1)]))])):H("",!0)])])):G.value?(m(),b("div",ms,[a("div",{class:"admin-dashboard__empty-content"},[s[10]||(s[10]=Y('<div class="admin-dashboard__empty-icon" data-v-9be0fd0a><i class="fas fa-chart-line" data-v-9be0fd0a></i></div><h3 class="admin-dashboard__empty-title" data-v-9be0fd0a>No Data Available</h3><p class="admin-dashboard__empty-text" data-v-9be0fd0a> Your dashboard is ready, but we haven&#39;t collected any data yet. </p><div class="admin-dashboard__empty-reasons" data-v-9be0fd0a><h4 data-v-9be0fd0a>This might be because:</h4><ul data-v-9be0fd0a><li data-v-9be0fd0a>Your marketplace is new and hasn&#39;t received orders yet</li><li data-v-9be0fd0a>There&#39;s a temporary connection issue</li><li data-v-9be0fd0a>Data is still being processed</li></ul></div>',4)),a("button",{class:"admin-dashboard__empty-retry",onClick:F},s[9]||(s[9]=[a("i",{class:"fas fa-sync-alt"},null,-1),a("span",null,"Try Again",-1)]))])])):(m(),b("main",vs,[a("section",hs,[s[29]||(s[29]=a("div",{class:"admin-dashboard__section-header"},[a("h2",{class:"admin-dashboard__section-title"},[a("i",{class:"admin-dashboard__section-icon fas fa-chart-bar"}),P(" Key Performance Indicators ")]),a("p",{class:"admin-dashboard__section-subtitle"}," Real-time business metrics and performance overview ")],-1)),a("div",ps,[a("div",bs,[a("div",fs,[s[11]||(s[11]=a("div",{class:"admin-dashboard__kpi-icon"},[a("i",{class:"fas fa-users"})],-1)),a("div",{class:y(["admin-dashboard__kpi-trend",`admin-dashboard__kpi-trend--${J()}`])},[a("i",{class:y(la())},null,2),a("span",null,u(ca())+"%",1)],2)]),a("div",gs,[s[12]||(s[12]=a("h3",{class:"admin-dashboard__kpi-label"},"Total Users",-1)),a("div",ks,u(x(r.value.users)),1),s[13]||(s[13]=a("p",{class:"admin-dashboard__kpi-description"},"Registered customers and sellers",-1))])]),a("div",ys,[a("div",ws,[s[14]||(s[14]=a("div",{class:"admin-dashboard__kpi-icon"},[a("i",{class:"fas fa-shopping-cart"})],-1)),a("div",{class:y(["admin-dashboard__kpi-trend",`admin-dashboard__kpi-trend--${W()}`])},[a("i",{class:y(ua())},null,2),a("span",null,u(_a())+"%",1)],2)]),a("div",qs,[s[15]||(s[15]=a("h3",{class:"admin-dashboard__kpi-label"},"Total Orders",-1)),a("div",Ts,u(x(r.value.orders)),1),s[16]||(s[16]=a("p",{class:"admin-dashboard__kpi-description"},"All-time order count",-1))])]),a("div",Ds,[a("div",Ps,[s[17]||(s[17]=a("div",{class:"admin-dashboard__kpi-icon"},[a("i",{class:"fas fa-box"})],-1)),a("div",{class:y(["admin-dashboard__kpi-trend",`admin-dashboard__kpi-trend--${X()}`])},[a("i",{class:y(ma())},null,2),a("span",null,u(va())+"%",1)],2)]),a("div",$s,[s[18]||(s[18]=a("h3",{class:"admin-dashboard__kpi-label"},"Total Products",-1)),a("div",Rs,u(x(r.value.products)),1),s[19]||(s[19]=a("p",{class:"admin-dashboard__kpi-description"},"Active marketplace listings",-1))])]),a("div",As,[a("div",Es,[s[20]||(s[20]=a("div",{class:"admin-dashboard__kpi-icon"},[a("i",{class:"fas fa-hryvnia-sign"})],-1)),a("div",{class:y(["admin-dashboard__kpi-trend",`admin-dashboard__kpi-trend--${j()}`])},[a("i",{class:y(ha())},null,2),a("span",null,u(Z())+"%",1)],2)]),a("div",Cs,[s[21]||(s[21]=a("h3",{class:"admin-dashboard__kpi-label"},"Total Revenue",-1)),a("div",Ss,u(Q(r.value.revenue)),1),s[22]||(s[22]=a("p",{class:"admin-dashboard__kpi-description"},"Gross marketplace revenue",-1))])]),a("div",Is,[s[25]||(s[25]=Y('<div class="admin-dashboard__kpi-header" data-v-9be0fd0a><div class="admin-dashboard__kpi-icon" data-v-9be0fd0a><i class="fas fa-folder" data-v-9be0fd0a></i></div><div class="admin-dashboard__kpi-trend admin-dashboard__kpi-trend--neutral" data-v-9be0fd0a><i class="fas fa-minus" data-v-9be0fd0a></i><span data-v-9be0fd0a>0%</span></div></div>',1)),a("div",Ns,[s[23]||(s[23]=a("h3",{class:"admin-dashboard__kpi-label"},"Categories",-1)),a("div",Os,u(x(r.value.categories||0)),1),s[24]||(s[24]=a("p",{class:"admin-dashboard__kpi-description"},"Product categories",-1))])]),a("div",xs,[a("div",Fs,[s[26]||(s[26]=a("div",{class:"admin-dashboard__kpi-icon"},[a("i",{class:"fas fa-chart-line"})],-1)),a("div",{class:y(["admin-dashboard__kpi-trend",`admin-dashboard__kpi-trend--${aa()}`])},[a("i",{class:y(pa())},null,2),a("span",null,u(ba())+"%",1)],2)]),a("div",Ms,[s[27]||(s[27]=a("h3",{class:"admin-dashboard__kpi-label"},"Site Profit (15%)",-1)),a("div",Us,u(Q(na.value)),1),s[28]||(s[28]=a("p",{class:"admin-dashboard__kpi-description"},"Commission from sales",-1))])])])]),a("section",Ls,[s[38]||(s[38]=a("div",{class:"admin-dashboard__section-header"},[a("h2",{class:"admin-dashboard__section-title"},[a("i",{class:"admin-dashboard__section-icon fas fa-chart-line"}),P(" Revenue Analytics ")]),a("p",{class:"admin-dashboard__section-subtitle"}," Comprehensive revenue and sales performance analysis ")],-1)),a("div",Vs,[a("div",Bs,[a("div",Hs,[s[31]||(s[31]=a("h3",{class:"admin-dashboard__chart-title"},[a("i",{class:"admin-dashboard__chart-icon fas fa-chart-area"}),P(" Sales Revenue Trend ")],-1)),a("div",zs,[ea(a("select",{"onUpdate:modelValue":s[1]||(s[1]=v=>A.value=v),onChange:ga,class:"admin-dashboard__period-select"},s[30]||(s[30]=[a("option",{value:"week"},"Last 7 Days",-1),a("option",{value:"month"},"Last 30 Days",-1),a("option",{value:"quarter"},"Last 3 Months",-1),a("option",{value:"year"},"Last Year",-1)]),544),[[ta,A.value]])])]),a("div",js,[(m(),M(V,null,{default:p(()=>[g(U(c),{data:n.value,period:A.value},null,8,["data","period"])]),fallback:p(()=>s[32]||(s[32]=[a("div",{class:"admin-dashboard__chart-loading"},[a("i",{class:"fas fa-spinner fa-pulse"}),a("span",null,"Loading chart...")],-1)])),_:1}))])]),a("div",Ys,[s[34]||(s[34]=a("div",{class:"admin-dashboard__chart-header"},[a("h3",{class:"admin-dashboard__chart-title"},[a("i",{class:"admin-dashboard__chart-icon fas fa-pie-chart"}),P(" Order Status Distribution ")])],-1)),a("div",Gs,[(m(),M(V,null,{default:p(()=>[g(U(e),{data:T.value},null,8,["data"])]),fallback:p(()=>s[33]||(s[33]=[a("div",{class:"admin-dashboard__chart-loading"},[a("i",{class:"fas fa-spinner fa-pulse"}),a("span",null,"Loading chart...")],-1)])),_:1}))])]),a("div",Ks,[a("div",Qs,[s[36]||(s[36]=a("h3",{class:"admin-dashboard__chart-title"},[a("i",{class:"admin-dashboard__chart-icon fas fa-coins"}),P(" Site Commission Revenue (15%) ")],-1)),a("div",Js,[ea(a("select",{"onUpdate:modelValue":s[2]||(s[2]=v=>O.value=v),onChange:ka,class:"admin-dashboard__period-select"},s[35]||(s[35]=[a("option",{value:"week"},"Last 7 Days",-1),a("option",{value:"month"},"Last 30 Days",-1),a("option",{value:"quarter"},"Last 3 Months",-1),a("option",{value:"year"},"Last Year",-1)]),544),[[ta,O.value]])])]),a("div",Ws,[(m(),M(V,null,{default:p(()=>[g(U(d),{data:i.value,"total-profit":K.value,period:O.value},null,8,["data","total-profit","period"])]),fallback:p(()=>s[37]||(s[37]=[a("div",{class:"admin-dashboard__chart-loading"},[a("i",{class:"fas fa-spinner fa-pulse"}),a("span",null,"Loading chart...")],-1)])),_:1}))])])])]),a("section",Xs,[s[47]||(s[47]=a("div",{class:"admin-dashboard__section-header"},[a("h2",{class:"admin-dashboard__section-title"},[a("i",{class:"admin-dashboard__section-icon fas fa-bolt"}),P(" Quick Actions ")]),a("p",{class:"admin-dashboard__section-subtitle"}," Frequently used administrative tasks ")],-1)),a("div",Zs,[g(t,{to:"/admin/products/create",class:"admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--primary"},{default:p(()=>s[39]||(s[39]=[a("i",{class:"admin-dashboard__quick-action-icon fas fa-plus-circle"},null,-1),a("span",{class:"admin-dashboard__quick-action-text"},"Add Product",-1),a("div",{class:"admin-dashboard__quick-action-description"},"Create new marketplace listing",-1)])),_:1}),g(t,{to:"/admin/orders",class:"admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--info"},{default:p(()=>s[40]||(s[40]=[a("i",{class:"admin-dashboard__quick-action-icon fas fa-shipping-fast"},null,-1),a("span",{class:"admin-dashboard__quick-action-text"},"Process Orders",-1),a("div",{class:"admin-dashboard__quick-action-description"},"Manage customer orders",-1)])),_:1}),g(t,{to:"/admin/categories",class:"admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--success"},{default:p(()=>s[41]||(s[41]=[a("i",{class:"admin-dashboard__quick-action-icon fas fa-folder-plus"},null,-1),a("span",{class:"admin-dashboard__quick-action-text"},"Manage Categories",-1),a("div",{class:"admin-dashboard__quick-action-description"},"Organize product categories",-1)])),_:1}),g(t,{to:"/admin/seller-requests",class:"admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--warning"},{default:p(()=>[s[42]||(s[42]=a("i",{class:"admin-dashboard__quick-action-icon fas fa-user-check"},null,-1)),s[43]||(s[43]=a("span",{class:"admin-dashboard__quick-action-text"},"Seller Requests",-1)),s[44]||(s[44]=a("div",{class:"admin-dashboard__quick-action-description"},"Review pending applications",-1)),I.value.length>0?(m(),b("div",ae,u(I.value.length),1)):H("",!0)]),_:1}),g(t,{to:"/admin/users",class:"admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--secondary"},{default:p(()=>s[45]||(s[45]=[a("i",{class:"admin-dashboard__quick-action-icon fas fa-users-cog"},null,-1),a("span",{class:"admin-dashboard__quick-action-text"},"Manage Users",-1),a("div",{class:"admin-dashboard__quick-action-description"},"User administration",-1)])),_:1}),g(t,{to:"/admin/reports",class:"admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--dark"},{default:p(()=>s[46]||(s[46]=[a("i",{class:"admin-dashboard__quick-action-icon fas fa-chart-bar"},null,-1),a("span",{class:"admin-dashboard__quick-action-text"},"View Reports",-1),a("div",{class:"admin-dashboard__quick-action-description"},"Analytics and insights",-1)])),_:1})])]),a("section",se,[s[49]||(s[49]=a("div",{class:"admin-dashboard__section-header"},[a("h2",{class:"admin-dashboard__section-title"},[a("i",{class:"admin-dashboard__section-icon fas fa-clock"}),P(" Recent Activity ")]),a("p",{class:"admin-dashboard__section-subtitle"}," Latest orders and seller requests ")],-1)),a("div",ee,[a("div",te,[g(ss,{orders:z.value,loading:f.value,onProcessOrder:ya},null,8,["orders","loading"])]),a("div",de,[(m(),M(V,null,{default:p(()=>[g(U(w),{requests:I.value,loading:f.value,onApproveRequest:wa,onRejectRequest:qa},null,8,["requests","loading"])]),fallback:p(()=>s[48]||(s[48]=[a("div",{class:"admin-dashboard__activity-loading"},[a("i",{class:"fas fa-spinner fa-pulse"}),a("span",null,"Loading seller requests...")],-1)])),_:1}))])])])]))])}}},le=da(re,[["__scopeId","data-v-9be0fd0a"]]);export{le as default};
