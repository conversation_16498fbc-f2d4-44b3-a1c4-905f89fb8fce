using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Обробник універсальної команди видалення зображення
/// </summary>
public class DeleteUniversalImageCommandHandler : IRequestHandler<DeleteUniversalImageCommand, bool>
{
    private readonly IImageService _imageService;
    private readonly ILogger<DeleteUniversalImageCommandHandler> _logger;

    public DeleteUniversalImageCommandHandler(
        IImageService imageService,
        ILogger<DeleteUniversalImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteUniversalImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Deleting {request.ImageType} image for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.DeleteImageAsync(
                request.EntityType,
                request.EntityId,
                request.ImageType,
                request.ImageId,
                cancellationToken);

            if (result)
            {
                _logger.LogInformation($"Successfully deleted {request.ImageType} image for {request.EntityType} {request.EntityId}");
            }
            else
            {
                _logger.LogWarning($"Image not found for deletion: {request.ImageType} image for {request.EntityType} {request.EntityId}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to delete {request.ImageType} image for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
