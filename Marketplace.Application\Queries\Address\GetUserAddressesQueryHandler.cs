using MediatR;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;

namespace Marketplace.Application.Queries.Address;

public class GetUserAddressesQueryHandler : IRequestHandler<GetUserAddressesQuery, IEnumerable<AddressResponse>>
{
    private readonly IAddressRepository _addressRepository;

    public GetUserAddressesQueryHandler(IAddressRepository addressRepository)
    {
        _addressRepository = addressRepository;
    }

    public async Task<IEnumerable<AddressResponse>> <PERSON>le(GetUserAddressesQuery request, CancellationToken cancellationToken)
    {
        var addresses = await _addressRepository.GetByUserIdAsync(request.UserId, cancellationToken);

        return addresses.Select(address => new AddressResponse(
            id: address.Id,
            region: address.AddressVO.Region,
            city: address.AddressVO.City,
            street: address.AddressVO.Street,
            postalCode: address.AddressVO.PostalCode,
            userId: address.UserId,
            createdAt: address.CreatedAt,
            updatedAt: address.UpdatedAt
        ));
    }
}
