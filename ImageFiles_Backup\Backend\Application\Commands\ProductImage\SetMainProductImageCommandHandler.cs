using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class SetMainProductImageCommandHandler : IRequestHandler<SetMainProductImageCommand, bool>
{
    private readonly IProductImageRepository _productImageRepository;

    public SetMainProductImageCommandHandler(IProductImageRepository productImageRepository)
    {
        _productImageRepository = productImageRepository;
    }

    public async Task<bool> Handle(SetMainProductImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо всі зображення продукту
        var productImages = await _productImageRepository.GetAllAsync(
            filter: pi => pi.ProductId == request.ProductId,
            cancellationToken: cancellationToken);

        if (!productImages.Any())
        {
            throw new InvalidOperationException("У продукту немає зображень.");
        }

        // Знаходимо зображення, яке потрібно зробити головним
        var targetImage = productImages.FirstOrDefault(pi => pi.Id == request.ImageId);
        if (targetImage == null)
        {
            throw new InvalidOperationException("Зображення не знайдено.");
        }

        // Скидаємо IsMain для всіх зображень
        foreach (var image in productImages)
        {
            if (image.IsMain)
            {
                image.Update(isMain: false);
                await _productImageRepository.UpdateAsync(image, cancellationToken);
            }
        }

        // Встановлюємо нове головне зображення
        targetImage.Update(isMain: true);
        await _productImageRepository.UpdateAsync(targetImage, cancellationToken);

        return true;
    }
}
