<template>
  <div class="admin-company-detail">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <nav class="admin-breadcrumb">
            <router-link to="/admin/companies" class="admin-breadcrumb-item">
              <i class="fas fa-building"></i>
              Companies
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <span class="admin-breadcrumb-item admin-breadcrumb-current">
              {{ company.name || 'Company Details' }}
            </span>
          </nav>
          <h1 class="admin-page-title">
            <i class="fas fa-building"></i>
            {{ company.name || 'Company Details' }}
          </h1>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-secondary" @click="fetchCompany" :disabled="loading">
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            <span>Refresh</span>
          </button>
          <router-link
            v-if="company.id"
            :to="`/admin/companies/${company.id}/edit`"
            class="admin-btn admin-btn-primary">
            <i class="fas fa-edit"></i>
            <span>Edit Company</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-state" v-if="loading && !company.id">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading company details...</p>
    </div>

    <!-- Error State -->
    <div class="admin-alert admin-alert-danger" v-else-if="error">
      <div class="admin-alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-alert-content">
        <div class="admin-alert-message">{{ error }}</div>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Company Details -->
    <div v-else-if="company.id" class="admin-company-content">
      <div class="admin-company-layout">
        
        <!-- Company Information Card -->
        <CompanyInfoCard
          :company="company"
          class="admin-company-section"
        />

        <!-- Company Images Card -->
        <div class="admin-card admin-company-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-images admin-card-icon"></i>
              Company Images
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-images-grid">
              <!-- Company Logo -->
              <div class="company-logo-section">
                <div class="section-header">
                  <h4 class="section-title">
                    <i class="fas fa-image me-2"></i>
                    Company Logo
                  </h4>
                </div>
                <div class="logo-display-card">
                  <div class="logo-container">
                    <div
                      class="logo-wrapper"
                      :class="{ 'has-logo': company.imageUrl }"
                      @click="company.imageUrl ? openImageViewer(company.imageUrl, company.name + ' - Logo') : null"
                    >
                      <img
                        v-if="company.imageUrl"
                        :src="company.imageUrl"
                        :alt="`${company.name} logo`"
                        class="company-logo-image"
                        @error="handleImageError"
                        title="Click to view full size"
                      />
                      <div v-else class="logo-placeholder">
                        <i class="fas fa-building"></i>
                        <span class="placeholder-text">No Logo</span>
                      </div>
                    </div>
                  </div>
                  <div class="logo-info">
                    <div class="logo-status">
                      <span class="status-badge" :class="company.imageUrl ? 'status-success' : 'status-empty'">
                        <i class="fas" :class="company.imageUrl ? 'fa-check-circle' : 'fa-exclamation-circle'"></i>
                        {{ company.imageUrl ? 'Logo Available' : 'No Logo Set' }}
                      </span>
                    </div>
                    <button
                      v-if="company.imageUrl"
                      class="view-logo-btn"
                      @click="openImageViewer(company.imageUrl, company.name + ' - Logo')"
                    >
                      <i class="fas fa-expand-alt me-2"></i>
                      View Full Size
                    </button>
                  </div>
                </div>
              </div>

              <!-- Meta Image -->
              <div class="company-meta-section">
                <div class="section-header">
                  <h4 class="section-title">
                    <i class="fas fa-share-alt me-2"></i>
                    Meta Image (SEO)
                  </h4>
                </div>
                <div class="meta-display-card">
                  <div class="meta-container">
                    <div
                      class="meta-wrapper"
                      :class="{ 'has-meta': company.metaImage }"
                      @click="company.metaImage ? openImageViewer(company.metaImage, company.name + ' - Meta Image') : null"
                    >
                      <img
                        v-if="company.metaImage"
                        :src="company.metaImage"
                        :alt="`${company.name} meta image`"
                        class="company-meta-image"
                        @error="handleMetaImageError"
                        title="Click to view full size"
                      />
                      <div v-else class="meta-placeholder">
                        <i class="fas fa-image"></i>
                        <span class="placeholder-text">No Meta Image</span>
                        <small class="placeholder-description">Used for social media sharing</small>
                      </div>
                    </div>
                  </div>
                  <div class="meta-info">
                    <div class="meta-status">
                      <span class="status-badge" :class="company.metaImage ? 'status-success' : 'status-empty'">
                        <i class="fas" :class="company.metaImage ? 'fa-check-circle' : 'fa-exclamation-circle'"></i>
                        {{ company.metaImage ? 'Meta Image Available' : 'No Meta Image Set' }}
                      </span>
                    </div>
                    <p class="meta-description">
                      {{ company.metaImage ? 'This image is used for SEO and social media sharing.' : 'A meta image helps improve social media sharing and SEO.' }}
                    </p>
                    <button
                      v-if="company.metaImage"
                      class="view-meta-btn"
                      @click="openImageViewer(company.metaImage, company.name + ' - Meta Image')"
                    >
                      <i class="fas fa-expand-alt me-2"></i>
                      View Full Size
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Finance Information Card -->
        <CompanyFinanceCard 
          v-if="company.finance"
          :finance="company.finance" 
          class="admin-company-section" 
        />

        <!-- Schedule Information -->
        <CompanyScheduleTable 
          v-if="company.schedule && company.schedule.length > 0"
          :schedule="company.schedule" 
          class="admin-company-section" 
        />

        <!-- Company Users -->
        <CompanyUsersTable 
          :companyId="company.id"
          class="admin-company-section" 
        />

        <!-- Company Products -->
        <CompanyProductsTable 
          :companyId="company.id"
          class="admin-company-section" 
        />

        <!-- Status & Actions Card -->
        <div class="admin-card admin-company-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-cog admin-card-icon"></i>
              Status & Actions
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-status-grid">
              <div class="admin-status-item">
                <label class="admin-status-label">Approval Status</label>
                <span class="admin-badge" :class="company.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
                  {{ company.isApproved ? 'Approved' : 'Pending' }}
                </span>
              </div>
              
              <div class="admin-status-item" v-if="company.isFeatured">
                <label class="admin-status-label">Featured</label>
                <span class="admin-badge admin-badge-info">Featured Company</span>
              </div>
              
              <div class="admin-status-item" v-if="company.approvedAt">
                <label class="admin-status-label">Approved At</label>
                <span class="admin-status-value">{{ formatDateTime(company.approvedAt) }}</span>
              </div>
              
              <div class="admin-status-item" v-if="company.approvedByUserId">
                <label class="admin-status-label">Approved By</label>
                <span class="admin-status-value">User ID: {{ company.approvedByUserId }}</span>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="admin-actions-grid">
              <router-link
                :to="`/admin/companies/${company.id}/edit`"
                class="admin-btn admin-btn-primary">
                <i class="fas fa-edit"></i>
                <span>Edit Company</span>
              </router-link>

              <button
                v-if="!company.isApproved"
                class="admin-btn admin-btn-success"
                @click="approveCompany"
                :disabled="actionLoading">
                <i class="fas fa-check"></i>
                <span>Approve Company</span>
              </button>

              <button
                v-if="!company.isApproved"
                class="admin-btn admin-btn-danger"
                @click="showRejectModal = true"
                :disabled="actionLoading">
                <i class="fas fa-times"></i>
                <span>Reject Company</span>
              </button>

              <button
                class="admin-btn admin-btn-warning"
                @click="suspendCompany"
                :disabled="actionLoading">
                <i class="fas fa-pause"></i>
                <span>Suspend Company</span>
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Rejection Modal -->
    <div v-if="showRejectModal" class="admin-modal-overlay" @click="showRejectModal = false">
      <div class="admin-modal" @click.stop>
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">Reject Company</h3>
          <button class="admin-modal-close" @click="showRejectModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-content">
          <div class="admin-form-group">
            <label class="admin-form-label">Rejection Reason *</label>
            <textarea 
              v-model="rejectionReason" 
              class="admin-form-control"
              rows="4"
              placeholder="Please provide a reason for rejection..."
              required>
            </textarea>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button class="admin-btn admin-btn-secondary" @click="showRejectModal = false">
            Cancel
          </button>
          <button 
            class="admin-btn admin-btn-danger" 
            @click="rejectCompany"
            :disabled="!rejectionReason.trim() || actionLoading">
            <i class="fas fa-times"></i>
            Reject Company
          </button>
        </div>
      </div>
    </div>

    <!-- Image Viewer Modal -->
    <div
      v-if="showImageModal"
      class="modal fade show d-block"
      @click="closeImageViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-image me-2"></i>
              {{ imageModalTitle }}
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeImageViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <div class="image-viewer">
              <img
                :src="imageModalUrl"
                :alt="imageModalTitle"
                class="img-fluid rounded"
                style="max-height: 500px;"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { companiesService } from '@/admin/services/companies';

// Import components
import CompanyInfoCard from '@/admin/components/companies/CompanyInfoCard.vue';
import CompanyFinanceCard from '@/admin/components/companies/CompanyFinanceCard.vue';
import CompanyScheduleTable from '@/admin/components/companies/CompanyScheduleTable.vue';
import CompanyUsersTable from '@/admin/components/companies/CompanyUsersTable.vue';
import CompanyProductsTable from '@/admin/components/companies/CompanyProductsTable.vue';

const route = useRoute();

// Reactive data
const company = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Image viewer state
const showImageModal = ref(false);
const imageModalUrl = ref('');
const imageModalTitle = ref('');

// Methods
const fetchCompany = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;
  } catch (err) {
    error.value = err.message || 'Failed to load company details';
  } finally {
    loading.value = false;
  }
};

const approveCompany = async () => {
  actionLoading.value = true;
  try {
    await companiesService.approveCompany(company.value.id);
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to approve company';
  } finally {
    actionLoading.value = false;
  }
};

const rejectCompany = async () => {
  if (!rejectionReason.value.trim()) {
    error.value = 'Please provide a rejection reason';
    return;
  }

  actionLoading.value = true;
  try {
    await companiesService.rejectCompany(company.value.id, rejectionReason.value);
    showRejectModal.value = false;
    rejectionReason.value = '';
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to reject company';
  } finally {
    actionLoading.value = false;
  }
};

const suspendCompany = async () => {
  if (!confirm('Are you sure you want to suspend this company?')) {
    return;
  }

  actionLoading.value = true;
  try {
    // Implement suspend logic here
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to suspend company';
  } finally {
    actionLoading.value = false;
  }
};

// Image viewer methods
const openImageViewer = (imageUrl, title) => {
  if (imageUrl) {
    imageModalUrl.value = imageUrl;
    imageModalTitle.value = title;
    showImageModal.value = true;
  }
};

const closeImageViewer = () => {
  showImageModal.value = false;
  imageModalUrl.value = '';
  imageModalTitle.value = '';
};

const handleImageError = (event) => {
  // Hide broken image and show placeholder
  event.target.style.display = 'none';
  const placeholder = event.target.nextElementSibling;
  if (placeholder) {
    placeholder.style.display = 'block';
  }
};

const handleMetaImageError = (event) => {
  // Hide broken image
  event.target.style.display = 'none';
};

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString();
};

// Initialize
onMounted(() => {
  fetchCompany();
});
</script>

<style scoped>
.admin-company-layout {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

.admin-company-section {
  margin-bottom: 0;
}

.admin-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-status-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-status-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-status-value {
  font-size: 0.875rem;
  color: var(--admin-text-primary);
}

.admin-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .admin-company-layout {
    grid-template-columns: 2fr 1fr;
  }
  
  .admin-company-section:nth-child(1),
  .admin-company-section:nth-child(2),
  .admin-company-section:nth-child(3) {
    grid-column: 1;
  }
  
  .admin-company-section:nth-child(4),
  .admin-company-section:nth-child(5),
  .admin-company-section:nth-child(6) {
    grid-column: 2;
  }
}

@media (min-width: 1200px) {
  .admin-company-layout {
    grid-template-columns: 1fr 1fr 1fr;
  }
  
  .admin-company-section:nth-child(1) {
    grid-column: 1 / 3;
  }
  
  .admin-company-section:nth-child(2),
  .admin-company-section:nth-child(3) {
    grid-column: 3;
  }
  
  .admin-company-section:nth-child(4),
  .admin-company-section:nth-child(5),
  .admin-company-section:nth-child(6) {
    grid-column: 1 / 4;
  }
}

/* Company Images Styles */
.admin-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.admin-image-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-image-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0;
}

.admin-image-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.admin-company-logo-large,
.admin-meta-image-large {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px solid #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
}

.admin-company-logo-large:hover,
.admin-meta-image-large:hover {
  border-color: #007bff;
  transform: scale(1.02);
}

.admin-company-logo-large.has-image {
  border-color: #28a745;
}

/* Enhanced Company Logo Styles */
.company-logo-section,
.company-meta-section {
  margin-bottom: 2rem;
}

.section-header {
  margin-bottom: 1rem;
}

.section-title {
  color: #2c3e50;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
}

.section-title i {
  color: #3498db;
}

.logo-display-card,
.meta-display-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.logo-display-card:hover,
.meta-display-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logo-container,
.meta-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.logo-wrapper,
.meta-wrapper {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
}

.logo-wrapper.has-logo,
.meta-wrapper.has-meta {
  border-color: #28a745;
  cursor: pointer;
}

.logo-wrapper.has-logo:hover,
.meta-wrapper.has-meta:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.company-logo-image,
.company-meta-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.logo-wrapper.has-logo:hover .company-logo-image,
.meta-wrapper.has-meta:hover .company-meta-image {
  transform: scale(1.05);
}

.logo-placeholder,
.meta-placeholder {
  text-align: center;
  color: #6c757d;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.logo-placeholder i,
.meta-placeholder i {
  font-size: 2.5rem;
  color: #dee2e6;
}

.placeholder-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
}

.placeholder-description {
  font-size: 0.75rem;
  color: #adb5bd;
  text-align: center;
  line-height: 1.3;
}

.logo-info,
.meta-info {
  flex: 1;
}

.logo-status,
.meta-status {
  margin-bottom: 0.75rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.status-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.status-empty {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.meta-description {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.view-logo-btn,
.view-meta-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.view-logo-btn:hover,
.view-meta-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

/* Modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.image-viewer {
  padding: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-images-grid {
    grid-template-columns: 1fr;
  }

  .admin-image-wrapper {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .admin-company-logo-large,
  .admin-meta-image-large {
    width: 100px;
    height: 100px;
  }

  .logo-placeholder-large {
    font-size: 32px;
  }
}
</style>
