/* ===== ADMIN DESIGN SYSTEM ===== */
/* Main CSS file that imports all admin styles */
/* Based on Reports page design patterns */

/* ===== CORE STYLES ===== */
@import './core/variables.css';
@import './core/typography.css';
@import './core/layout.css';

/* ===== COMPONENT STYLES ===== */
@import './components/cards.css';
@import './components/buttons.css';
@import './components/tables.css';
@import './components/forms.css';
@import './components/filters.css';
@import './components/pagination.css';
@import './components/alerts.css';
@import './components/modals.css';
@import './components/charts.css';

/* ===== PAGE STYLES ===== */
@import './pages/dashboard.css';
@import './pages/users.css';
@import './pages/seller-requests.css';
@import './pages/companies.css';
@import './pages/products.css';
@import './pages/orders.css';

/* ===== LEGACY STYLES (TO BE REFACTORED) ===== */
@import '../admin-components.css';
@import '../admin-design-system.css';
@import '../admin-layout-fix.css';
@import '../admin-dashboard-fix.css';
@import '../admin-form-fixes.css';
@import '../admin-utilities.css';

/* ===== BASE RESET AND NORMALIZATION ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--admin-font-family);
  background-color: var(--admin-bg-primary);
  color: var(--admin-gray-900);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== ADMIN LAYOUT BASE ===== */
.admin-app {
  min-height: 100vh;
  background-color: var(--admin-bg-primary);
}

/* ===== ADMIN PAGE CONTAINERS ===== */
.admin-companies,
.admin-products,
.admin-company-edit,
.admin-product-edit,
.admin-page-content {
  background-color: var(--admin-bg-primary) !important;
  min-height: 100vh;
  color: var(--admin-text-primary) !important;
}

/* ===== ADMIN CARDS ===== */
.admin-card {
  background-color: var(--admin-white) !important;
  color: var(--admin-text-primary) !important;
}

/* ===== ADMIN FORMS ===== */
.admin-form-control {
  background-color: var(--admin-white) !important;
  color: var(--admin-text-primary) !important;
  border-color: var(--admin-border-light) !important;
}

/* ===== ADMIN TABLES ===== */
.admin-table {
  background-color: var(--admin-white) !important;
  color: var(--admin-text-primary) !important;
}

.admin-table-container {
  background-color: var(--admin-white) !important;
}

/* ===== ADMIN MODALS ===== */
.modal-card,
.modal-card-body {
  background-color: var(--admin-white) !important;
  color: var(--admin-text-primary) !important;
}

/* ===== GLOBAL ADMIN STYLES ===== */

/* Remove default margins from headings */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--admin-font-semibold);
  line-height: var(--admin-leading-tight);
}

/* Remove default margins from paragraphs */
p {
  margin: 0;
  line-height: var(--admin-leading-normal);
}

/* Remove default list styles */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* Remove default input styles */
input, textarea, select {
  font: inherit;
  margin: 0;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* ===== FOCUS STYLES ===== */
*:focus {
  outline: 2px solid var(--admin-primary);
  outline-offset: 2px;
}

.admin-focus-visible:focus {
  outline: 2px solid var(--admin-primary);
  outline-offset: 2px;
}

.admin-focus-none:focus {
  outline: none;
}

/* ===== SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--admin-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--admin-gray-300);
  border-radius: var(--admin-radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--admin-gray-400);
}

/* ===== SELECTION STYLES ===== */
::selection {
  background: var(--admin-primary-light);
  color: var(--admin-gray-900);
}

::-moz-selection {
  background: var(--admin-primary-light);
  color: var(--admin-gray-900);
}

/* ===== PRINT STYLES ===== */
@media print {
  .admin-no-print {
    display: none !important;
  }
  
  .admin-page {
    padding: 0;
    background: white;
  }
  
  .admin-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .admin-card {
    border-width: 2px;
  }
  
  .admin-btn {
    border-width: 2px;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Display utilities */
.admin-block { display: block; }
.admin-inline { display: inline; }
.admin-inline-block { display: inline-block; }
.admin-flex { display: flex; }
.admin-inline-flex { display: inline-flex; }
.admin-grid { display: grid; }
.admin-hidden { display: none; }

/* Position utilities */
.admin-relative { position: relative; }
.admin-absolute { position: absolute; }
.admin-fixed { position: fixed; }
.admin-sticky { position: sticky; }

/* Overflow utilities */
.admin-overflow-hidden { overflow: hidden; }
.admin-overflow-auto { overflow: auto; }
.admin-overflow-scroll { overflow: scroll; }

/* Width utilities */
.admin-w-full { width: 100%; }
.admin-w-auto { width: auto; }
.admin-w-fit { width: fit-content; }

/* Height utilities */
.admin-h-full { height: 100%; }
.admin-h-auto { height: auto; }
.admin-h-screen { height: 100vh; }

/* Border utilities */
.admin-border { border: 1px solid var(--admin-border-color); }
.admin-border-t { border-top: 1px solid var(--admin-border-color); }
.admin-border-b { border-bottom: 1px solid var(--admin-border-color); }
.admin-border-l { border-left: 1px solid var(--admin-border-color); }
.admin-border-r { border-right: 1px solid var(--admin-border-color); }
.admin-border-none { border: none; }

/* Border radius utilities */
.admin-rounded { border-radius: var(--admin-radius-md); }
.admin-rounded-sm { border-radius: var(--admin-radius-sm); }
.admin-rounded-lg { border-radius: var(--admin-radius-lg); }
.admin-rounded-xl { border-radius: var(--admin-radius-xl); }
.admin-rounded-full { border-radius: var(--admin-radius-full); }
.admin-rounded-none { border-radius: 0; }

/* Shadow utilities */
.admin-shadow { box-shadow: var(--admin-shadow-md); }
.admin-shadow-sm { box-shadow: var(--admin-shadow-sm); }
.admin-shadow-lg { box-shadow: var(--admin-shadow-lg); }
.admin-shadow-xl { box-shadow: var(--admin-shadow-xl); }
.admin-shadow-none { box-shadow: none; }

/* Background utilities */
.admin-bg-white { background-color: var(--admin-white); }
.admin-bg-gray-50 { background-color: var(--admin-gray-50); }
.admin-bg-gray-100 { background-color: var(--admin-gray-100); }
.admin-bg-primary { background-color: var(--admin-primary); }
.admin-bg-success { background-color: var(--admin-success); }
.admin-bg-warning { background-color: var(--admin-warning); }
.admin-bg-danger { background-color: var(--admin-danger); }

/* Text color utilities */
.admin-text-white { color: var(--admin-white); }
.admin-text-gray-500 { color: var(--admin-gray-500); }
.admin-text-gray-600 { color: var(--admin-gray-600); }
.admin-text-gray-700 { color: var(--admin-gray-700); }
.admin-text-gray-900 { color: var(--admin-gray-900); }
.admin-text-primary { color: var(--admin-primary); }
.admin-text-success { color: var(--admin-success); }
.admin-text-warning { color: var(--admin-warning); }
.admin-text-danger { color: var(--admin-danger); }

/* Cursor utilities */
.admin-cursor-pointer { cursor: pointer; }
.admin-cursor-not-allowed { cursor: not-allowed; }
.admin-cursor-default { cursor: default; }

/* Transition utilities */
.admin-transition { transition: all var(--admin-transition-normal); }
.admin-transition-fast { transition: all var(--admin-transition-fast); }
.admin-transition-slow { transition: all var(--admin-transition-slow); }

/* Transform utilities */
.admin-transform { transform: translateZ(0); }
.admin-hover-lift:hover { transform: translateY(-2px); }
.admin-hover-scale:hover { transform: scale(1.05); }
