import api from '@/services/api';

// Простий кеш для даних компаній
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 хвилин

// Функція для створення ключа кешу
const createCacheKey = (endpoint, params) => {
  const sortedParams = Object.keys(params || {})
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  return `${endpoint}?${sortedParams}`;
};

// Функція для перевірки валідності кешу
const isCacheValid = (cacheEntry) => {
  return cacheEntry && (Date.now() - cacheEntry.timestamp) < CACHE_DURATION;
};

// Функція для отримання з кешу або виконання запиту
const getCachedOrFetch = async (cacheKey, fetchFunction) => {
  const cached = cache.get(cacheKey);

  if (isCacheValid(cached)) {
    console.log('Returning cached data for:', cacheKey);
    return cached.data;
  }

  console.log('Fetching fresh data for:', cacheKey);
  const data = await fetchFunction();

  // Зберігаємо в кеш
  cache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });

  return data;
};

// Функція для очищення кешу
const clearCache = (pattern) => {
  if (pattern) {
    // Очищаємо кеш за патерном
    for (const key of cache.keys()) {
      if (key.includes(pattern)) {
        cache.delete(key);
      }
    }
  } else {
    // Очищаємо весь кеш
    cache.clear();
  }
};

export const companiesService = {
  async getCompanies(params = {}) {
    try {
      console.log('Requesting companies with params:', params);

      // Стандартизуємо параметри для відповідності API
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;

      // Конвертуємо sortBy/sortOrder в orderBy/descending
      if (params.sortBy) {
        apiParams.orderBy = params.sortBy;
      } else if (params.orderBy) {
        apiParams.orderBy = params.orderBy;
      }

      if (params.sortOrder) {
        apiParams.descending = params.sortOrder === 'desc';
      } else if (params.descending !== undefined) {
        apiParams.descending = params.descending;
      }

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      // Статус фільтр видалено - тепер показуємо тільки схвалені компанії

      // Featured фільтр
      if (params.featured && params.featured.trim() !== '' && params.featured !== 'all') {
        apiParams.isFeatured = params.featured === 'true';
      }

      console.log('Final API params for companies:', apiParams);

      // Використовуємо admin endpoint
      const response = await api.get('/api/admin/companies', { params: apiParams });
      console.log('Admin companies API response:', response.data);
      console.log('API params sent:', apiParams);

      // Admin endpoint повертає дані в response.data.data (ApiResponse<PaginatedResponse<CompanyResponse>>)
      if (response.data && response.data.success && response.data.data) {
        console.log('Returning paginated data:', response.data.data);
        console.log('Total items:', response.data.data.total);
        console.log('Current page:', response.data.data.currentPage);
        console.log('Total pages:', response.data.data.lastPage);
        console.log('Items count:', response.data.data.data?.length);
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw new Error(error.response?.data?.message || 'Failed to load companies');
    }
  },

  async getPendingCompanies(params = {}) {
    try {
      console.log('Requesting pending companies with params:', params);

      // Стандартизуємо параметри для відповідності API
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;
      if (params.orderBy) apiParams.orderBy = params.orderBy;
      if (params.descending !== undefined) apiParams.descending = params.descending;

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      console.log('Final API params for pending companies:', apiParams);

      const response = await api.get('/api/admin/companies/pending', { params: apiParams });

      console.log('Pending companies API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching pending companies:', error);
      throw new Error(error.response?.data?.message || 'Failed to load pending companies');
    }
  },

  async getCompanyById(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching company:', error);
      throw new Error(error.response?.data?.message || 'Failed to load company details');
    }
  },

  async approveCompany(id) {
    try {
      const response = await api.post(`/api/admin/companies/${id}/approve`);
      return response.data;
    } catch (error) {
      console.error('Error approving company:', error);
      throw new Error(error.response?.data?.message || 'Failed to approve company');
    }
  },

  async rejectCompany(id, reason = '') {
    try {
      const response = await api.post(`/api/admin/companies/${id}/reject`, { reason });
      return response.data;
    } catch (error) {
      console.error('Error rejecting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to reject company');
    }
  },

  async deleteCompany(id) {
    try {
      const response = await api.delete(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete company');
    }
  },

  async getCompany(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to get company');
    }
  },

  async getDetailedCompany(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}/detailed`);
      return response.data;
    } catch (error) {
      console.error('Error getting detailed company:', error);
      throw new Error(error.response?.data?.message || 'Failed to get detailed company');
    }
  },

  async updateCompany(id, data) {
    try {
      const response = await api.put(`/api/admin/companies/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating company:', error);
      throw new Error(error.response?.data?.message || 'Failed to update company');
    }
  },

  async updateDetailedCompany(id, data) {
    try {
      const response = await api.put(`/api/admin/companies/${id}/detailed`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating detailed company:', error);
      throw new Error(error.response?.data?.message || 'Failed to update detailed company');
    }
  },

  // Get company users
  async getCompanyUsers(companyId, params = {}) {
    const cacheKey = createCacheKey(`/api/admin/companies/${companyId}/users`, params);

    return getCachedOrFetch(cacheKey, async () => {
      try {
        console.log('Requesting company users with params:', params);

        // Стандартизуємо параметри для відповідності API
        const apiParams = {};

        // Базові параметри пагінації
        if (params.page) apiParams.page = params.page;
        if (params.pageSize) apiParams.pageSize = params.pageSize;

        // Параметри сортування
        if (params.orderBy) apiParams.orderBy = params.orderBy;
        if (params.descending !== undefined) apiParams.descending = params.descending;

        // Пошук - використовуємо 'filter' як очікує API
        if (params.search && params.search.trim() !== '') {
          apiParams.filter = params.search.trim();
        } else if (params.filter && params.filter.trim() !== '') {
          apiParams.filter = params.filter.trim();
        }

        console.log('Final API params for company users:', apiParams);

        const response = await api.get(`/api/admin/companies/${companyId}/users`, { params: apiParams });
        console.log('Admin company users API response:', response.data);

        // Admin endpoint повертає дані в response.data.data (ApiResponse<PaginatedResponse<CompanyUserResponse>>)
        if (response.data && response.data.success && response.data.data) {
          console.log('Returning paginated users data:', response.data.data);
          console.log('Total users:', response.data.data.total);
          console.log('Current page:', response.data.data.currentPage);
          console.log('Total pages:', response.data.data.lastPage);
          console.log('Users count:', response.data.data.data?.length);
          return response.data;
        }

        console.error('Invalid response format for users:', response.data);
        throw new Error('Invalid response format from server');
      } catch (error) {
        console.error('Error getting company users:', error);
        throw new Error(error.response?.data?.message || 'Failed to get company users');
      }
    });
  },

  // Get company products
  async getCompanyProducts(companyId, params = {}) {
    const cacheKey = createCacheKey(`/api/admin/companies/${companyId}/products`, params);

    return getCachedOrFetch(cacheKey, async () => {
      try {
        console.log('Requesting company products with params:', params);

        // Стандартизуємо параметри для відповідності API
        const apiParams = {};

        // Базові параметри пагінації
        if (params.page) apiParams.page = params.page;
        if (params.pageSize) apiParams.pageSize = params.pageSize;

        // Параметри сортування
        if (params.orderBy) apiParams.orderBy = params.orderBy;
        if (params.descending !== undefined) apiParams.descending = params.descending;

        // Пошук - використовуємо 'filter' як очікує API
        if (params.search && params.search.trim() !== '') {
          apiParams.filter = params.search.trim();
        } else if (params.filter && params.filter.trim() !== '') {
          apiParams.filter = params.filter.trim();
        }

        console.log('Final API params for company products:', apiParams);

        // Використовуємо admin endpoint
        const response = await api.get(`/api/admin/companies/${companyId}/products`, { params: apiParams });
        console.log('Admin company products API response:', response.data);

        // Admin endpoint повертає дані в response.data.data (ApiResponse<PaginatedResponse<ProductResponse>>)
        if (response.data && response.data.success && response.data.data) {
          console.log('Returning paginated products data:', response.data.data);
          console.log('Total products:', response.data.data.total);
          console.log('Current page:', response.data.data.currentPage);
          console.log('Total pages:', response.data.data.lastPage);
          console.log('Products count:', response.data.data.data?.length);
          return response.data;
        }

        console.error('Invalid response format for products:', response.data);
        throw new Error('Invalid response format from server');
      } catch (error) {
        console.error('Error getting company products:', error);
        throw new Error(error.response?.data?.message || 'Failed to get company products');
      }
    });
  },

  // Функція для очищення кешу
  clearCache(pattern) {
    clearCache(pattern);
  }
};
