<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">Order Details</p>
        <button class="delete" aria-label="close" @click="$emit('close')"></button>
      </header>
      <section class="modal-card-body" v-if="order">
        <!-- Order Summary -->
        <div class="box">
          <div class="columns">
            <div class="column is-6">
              <p><strong>Order ID:</strong> {{ order.id }}</p>
              <p><strong>Date:</strong> {{ formatDate(order.createdAt) }}</p>
              <p><strong>Customer:</strong> {{ order.customerName }}</p>
              <p><strong>Email:</strong> {{ order.customerEmail }}</p>
            </div>
            <div class="column is-6">
              <p><strong>Status:</strong> 
                <span class="tag" :class="getStatusClass(order.status)">
                  {{ formatStatus(order.status) }}
                </span>
              </p>
              <p><strong>Payment Method:</strong> {{ order.paymentMethod }}</p>
              <p><strong>Payment Status:</strong> 
                <span class="tag" :class="getPaymentStatusClass(order.paymentStatus)">
                  {{ formatStatus(order.paymentStatus) }}
                </span>
              </p>
              <p><strong>Total:</strong> {{ formatPrice(order.total) }}</p>
            </div>
          </div>
        </div>
        
        <!-- Shipping Information -->
        <div class="box">
          <h3 class="subtitle is-5">Shipping Information</h3>
          <div class="columns">
            <div class="column is-6">
              <p><strong>Name:</strong> {{ order.shippingAddress?.name }}</p>
              <p><strong>Address:</strong> {{ formatAddress(order.shippingAddress) }}</p>
              <p><strong>Phone:</strong> {{ order.shippingAddress?.phone }}</p>
            </div>
            <div class="column is-6">
              <p><strong>Shipping Method:</strong> {{ order.shippingMethod }}</p>
              <p><strong>Tracking Number:</strong> {{ order.trackingNumber || 'N/A' }}</p>
              <p><strong>Estimated Delivery:</strong> {{ formatDate(order.estimatedDelivery) || 'N/A' }}</p>
            </div>
          </div>
        </div>
        
        <!-- Order Items -->
        <div class="box">
          <h3 class="subtitle is-5">Order Items</h3>
          <table class="table is-fullwidth">
            <thead>
              <tr>
                <th>Product</th>
                <th>Price</th>
                <th>Quantity</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in order.items" :key="index">
                <td>
                  <div class="is-flex is-align-items-center">
                    <figure class="image is-48x48 mr-2">
                      <img :src="item.imageUrl || '/placeholder.png'" :alt="item.name">
                    </figure>
                    <div>
                      <p>{{ item.name }}</p>
                      <p class="has-text-grey is-size-7" v-if="item.sku">SKU: {{ item.sku }}</p>
                    </div>
                  </div>
                </td>
                <td>{{ formatPrice(item.price) }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ formatPrice(item.price * item.quantity) }}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" class="has-text-right"><strong>Subtotal:</strong></td>
                <td>{{ formatPrice(order.subtotal) }}</td>
              </tr>
              <tr>
                <td colspan="3" class="has-text-right"><strong>Shipping:</strong></td>
                <td>{{ formatPrice(order.shippingCost) }}</td>
              </tr>
              <tr>
                <td colspan="3" class="has-text-right"><strong>Tax:</strong></td>
                <td>{{ formatPrice(order.tax) }}</td>
              </tr>
              <tr>
                <td colspan="3" class="has-text-right"><strong>Total:</strong></td>
                <td>{{ formatPrice(order.total) }}</td>
              </tr>
            </tfoot>
          </table>
        </div>
        
        <!-- Order Notes -->
        <div class="box" v-if="order.notes">
          <h3 class="subtitle is-5">Notes</h3>
          <p>{{ order.notes }}</p>
        </div>
      </section>
      <footer class="modal-card-foot">
        <div class="buttons">
          <div class="dropdown is-hoverable">
            <div class="dropdown-trigger">
              <button class="button is-primary">
                <span>Update Status</span>
                <span class="icon is-small">
                  <i class="fas fa-angle-down"></i>
                </span>
              </button>
            </div>
            <div class="dropdown-menu">
              <div class="dropdown-content">
                <a class="dropdown-item" @click="updateStatus('pending')" v-if="order?.status !== 'pending'">
                  Mark as Pending
                </a>
                <a class="dropdown-item" @click="updateStatus('processing')" v-if="order?.status !== 'processing'">
                  Mark as Processing
                </a>
                <a class="dropdown-item" @click="updateStatus('shipped')" v-if="order?.status !== 'shipped'">
                  Mark as Shipped
                </a>
                <a class="dropdown-item" @click="updateStatus('delivered')" v-if="order?.status !== 'delivered'">
                  Mark as Delivered
                </a>
                <a class="dropdown-item" @click="updateStatus('cancelled')" v-if="order?.status !== 'cancelled'">
                  Mark as Cancelled
                </a>
              </div>
            </div>
          </div>
          <button class="button is-info">
            <span class="icon"><i class="fas fa-print"></i></span>
            <span>Print Invoice</span>
          </button>
          <button class="button" @click="$emit('close')">Close</button>
        </div>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  order: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'update-status']);

// Format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Format price
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price);
};

// Format status
const formatStatus = (status) => {
  if (!status) return 'Unknown';
  
  // Convert snake_case to Title Case
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Format address
const formatAddress = (address) => {
  if (!address) return 'N/A';
  
  return `${address.street}, ${address.city}, ${address.state} ${address.zipCode}, ${address.country}`;
};

// Get status class
const getStatusClass = (status) => {
  switch (status) {
    case 'pending':
      return 'is-warning';
    case 'processing':
      return 'is-info';
    case 'shipped':
      return 'is-primary';
    case 'delivered':
      return 'is-success';
    case 'cancelled':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

// Get payment status class
const getPaymentStatusClass = (status) => {
  switch (status) {
    case 'paid':
      return 'is-success';
    case 'pending':
      return 'is-warning';
    case 'failed':
      return 'is-danger';
    case 'refunded':
      return 'is-info';
    default:
      return 'is-light';
  }
};

// Update status
const updateStatus = (newStatus) => {
  if (props.order) {
    emit('update-status', props.order.id, newStatus);
  }
};
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 900px;
}

.mr-2 {
  margin-right: 0.5rem;
}

.subtitle {
  margin-bottom: 1rem;
}

.box {
  margin-bottom: 1.5rem;
}

.box:last-child {
  margin-bottom: 0;
}
</style>
