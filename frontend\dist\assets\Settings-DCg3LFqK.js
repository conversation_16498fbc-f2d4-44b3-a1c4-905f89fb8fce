import{q as p,_ as K,g as _,i as T,c,a as t,d as b,k as v,n as y,x as l,D as r,y as x,l as U,W as f,z as S,Y as h,o as g}from"./index-L-hJxM_5.js";const m={async getGeneralSettings(){try{return(await p.get("/api/admin/settings/general")).data}catch(i){return console.error("Error fetching general settings:",i),{siteName:"Klondike",siteDescription:"Your one-stop marketplace for all your needs",siteEmail:"<EMAIL>",sitePhone:"+****************",siteAddress:"123 Main St, New York, NY 10001",logo:"https://via.placeholder.com/200x50",favicon:"https://via.placeholder.com/32x32",currency:"UAH",currencySymbol:"₴",dateFormat:"MM/DD/YYYY",timeFormat:"12h",timezone:"Europe/Kiev",maintenanceMode:!1,maintenanceMessage:"We are currently performing maintenance. Please check back later."}}},async updateGeneralSettings(i){try{return(await p.put("/api/admin/settings/general",i)).data}catch(n){return console.error("Error updating general settings:",n),{success:!0,settings:i}}},async getPaymentSettings(){try{return(await p.get("/api/admin/settings/payment")).data}catch(i){return console.error("Error fetching payment settings:",i),{paymentMethods:[{id:"stripe",name:"Stripe",enabled:!0,testMode:!0,config:{publishableKey:"pk_test_*****",secretKey:"sk_test_*****"}},{id:"paypal",name:"PayPal",enabled:!0,testMode:!0,config:{clientId:"client_id_*****",clientSecret:"client_secret_*****"}},{id:"cod",name:"Cash on Delivery",enabled:!0,config:{fee:5}}],defaultCurrency:"UAH",taxRate:20,taxIncluded:!0,invoicePrefix:"INV-"}}},async updatePaymentSettings(i){try{return(await p.put("/api/admin/settings/payment",i)).data}catch(n){return console.error("Error updating payment settings:",n),{success:!0,settings:i}}},async getShippingSettings(){try{return(await p.get("/api/admin/settings/shipping")).data}catch(i){return console.error("Error fetching shipping settings:",i),{shippingMethods:[{id:"standard",name:"Standard Shipping",enabled:!0,cost:10,freeThreshold:100,estimatedDays:"3-5"},{id:"express",name:"Express Shipping",enabled:!0,cost:25,freeThreshold:null,estimatedDays:"1-2"},{id:"pickup",name:"Local Pickup",enabled:!0,cost:0,freeThreshold:null,estimatedDays:"0"}],defaultShippingMethod:"standard",shippingOrigin:{address:"123 Main St",city:"New York",state:"NY",postalCode:"10001",country:"USA"},shippingCountries:["USA","Canada","Mexico","UK","Australia"]}}},async updateShippingSettings(i){try{return(await p.put("/api/admin/settings/shipping",i)).data}catch(n){return console.error("Error updating shipping settings:",n),{success:!0,settings:i}}},async getEmailSettings(){try{return(await p.get("/api/admin/settings/email")).data}catch(i){return console.error("Error fetching email settings:",i),{smtpHost:"smtp.example.com",smtpPort:587,smtpUsername:"<EMAIL>",smtpPassword:"********",smtpEncryption:"tls",fromEmail:"<EMAIL>",fromName:"Klondike Marketplace",emailTemplates:[{id:"welcome",name:"Welcome Email",subject:"Welcome to Klondike Marketplace",enabled:!0},{id:"order_confirmation",name:"Order Confirmation",subject:"Your Order #{{order_id}} has been confirmed",enabled:!0},{id:"shipping_confirmation",name:"Shipping Confirmation",subject:"Your Order #{{order_id}} has been shipped",enabled:!0},{id:"password_reset",name:"Password Reset",subject:"Reset your Klondike Marketplace password",enabled:!0}]}}},async updateEmailSettings(i){try{return(await p.put("/api/admin/settings/email",i)).data}catch(n){return console.error("Error updating email settings:",n),{success:!0,settings:i}}},async testEmailSettings(i){try{return(await p.post("/api/admin/settings/email/test",i)).data}catch(n){throw console.error("Error testing email settings:",n),new Error("Failed to send test email. Please check your settings and try again.")}},async getSystemInfo(){try{return(await p.get("/api/admin/settings/system-info")).data}catch(i){return console.error("Error fetching system info:",i),{version:"1.0.0",phpVersion:"8.1.0",mysqlVersion:"8.0.27",serverOS:"Linux",webServer:"Nginx",diskSpace:{total:5e10,used:25e9,free:25e9},memory:{total:8e9,used:4e9,free:4e9},lastBackup:new Date(Date.now()-1e3*60*60*24),installedPlugins:[{name:"Payment Gateway",version:"1.2.0",status:"Active"},{name:"SEO Optimizer",version:"2.1.0",status:"Active"},{name:"Security Suite",version:"1.5.0",status:"Active"}]}}},async testEmailSettings(i){var n,u;try{return(await p.post("/api/admin/settings/email/test",i)).data}catch(d){throw console.error("Error testing email settings:",d),new Error(((u=(n=d.response)==null?void 0:n.data)==null?void 0:u.message)||"Failed to test email settings")}},async getApiSettings(){try{return(await p.get("/api/admin/settings/api")).data}catch(i){return console.error("Error fetching API settings:",i),{api_rate_limit:"60",webhooks_enabled:"false"}}},async updateApiSettings(i){var n,u;try{return(await p.put("/api/admin/settings/api",i)).data}catch(d){throw console.error("Error updating API settings:",d),new Error(((u=(n=d.response)==null?void 0:n.data)==null?void 0:u.message)||"Failed to update API settings")}}},Y={class:"admin-settings"},N={key:0,class:"has-text-centered",style:{padding:"2rem"}},I={key:1},R={class:"tabs"},F={key:0,class:"settings-content"},G={class:"box"},z={class:"field"},L={class:"control"},O={class:"field"},B={class:"control"},H={class:"field"},W={class:"control"},$={class:"field"},j={class:"control"},q={class:"field"},J={class:"control"},Q={class:"select is-fullwidth"},X={class:"field"},Z={class:"control"},ee={class:"field"},te={class:"control"},se={class:"select is-fullwidth"},ae={class:"field"},ne={class:"control"},ie={class:"checkbox"},le={key:1,class:"settings-content"},oe={class:"box"},re={class:"field"},de={class:"control"},pe={class:"checkbox"},ue={class:"field"},me={class:"control"},ce={class:"checkbox"},ge={class:"field"},ve={class:"control"},ye={key:2,class:"settings-content"},be={class:"box"},_e={class:"field"},fe={class:"control"},Se={class:"field"},he={class:"control"},ke={class:"field"},Ee={class:"control"},we={key:3,class:"settings-content"},Pe={class:"box"},xe={class:"field"},Ue={class:"control"},Ae={class:"field"},Ve={class:"control"},Me={class:"field"},Ce={class:"control"},De={class:"field"},Ke={class:"control"},Te={class:"field"},Ye={class:"control"},Ne={key:4,class:"settings-content"},Ie={class:"box"},Re={class:"field"},Fe={class:"control"},Ge={class:"field"},ze={class:"control"},Le={class:"field"},Oe={class:"control"},Be={class:"field"},He={class:"control"},We={class:"checkbox"},$e={class:"field is-grouped is-grouped-right mt-4"},je={key:0,class:"control"},qe=["disabled"],Je={class:"control"},Qe=["disabled"],Xe={__name:"Settings",setup(i){const n=_("general"),u=_(!1),d=_(!1),a=_({general:{},payment:{},shipping:{},email:{},api:{}}),k=computed({get:()=>a.value.general.maintenance_mode==="true",set:o=>a.value.general.maintenance_mode=o.toString()}),E=computed({get:()=>a.value.payment.stripe_enabled==="true",set:o=>a.value.payment.stripe_enabled=o.toString()}),w=computed({get:()=>a.value.payment.paypal_enabled==="true",set:o=>a.value.payment.paypal_enabled=o.toString()}),P=computed({get:()=>a.value.api.webhooks_enabled==="true",set:o=>a.value.api.webhooks_enabled=o.toString()}),A=async()=>{u.value=!0;try{const[o,e,s,C,D]=await Promise.all([m.getGeneralSettings(),m.getPaymentSettings(),m.getShippingSettings(),m.getEmailSettings(),m.getApiSettings()]);a.value={general:o.data||{},payment:e.data||{},shipping:s.data||{},email:C.data||{},api:D.data||{}}}catch(o){console.error("Error loading settings:",o),alert("Error loading settings. Please try again.")}finally{u.value=!1}},V=async()=>{d.value=!0;try{const o=n.value;let e;switch(o){case"general":e=await m.updateGeneralSettings(a.value.general);break;case"payment":e=await m.updatePaymentSettings(a.value.payment);break;case"shipping":e=await m.updateShippingSettings(a.value.shipping);break;case"email":e=await m.updateEmailSettings(a.value.email);break;case"api":e=await m.updateApiSettings(a.value.api);break;default:throw new Error("Unknown settings tab")}if(e.success!==!1)alert("Settings saved successfully!");else throw new Error("Failed to save settings")}catch(o){console.error("Error saving settings:",o),alert("Error saving settings. Please try again.")}finally{d.value=!1}},M=async()=>{try{await m.testEmailSettings(a.value.email),alert("Test email sent successfully!")}catch(o){console.error("Error testing email settings:",o),alert("Failed to send test email. Please check your settings.")}};return T(()=>{A()}),(o,e)=>(g(),c("div",Y,[e[59]||(e[59]=t("h1",{class:"title"},"Settings",-1)),u.value?(g(),c("div",N,e[28]||(e[28]=[t("div",{class:"is-size-4"},[t("i",{class:"fas fa-spinner fa-spin"}),b(" Loading settings... ")],-1)]))):(g(),c("div",I,[t("div",R,[t("ul",null,[t("li",{class:y({"is-active":n.value==="general"})},[t("a",{onClick:e[0]||(e[0]=s=>n.value="general")},"General")],2),t("li",{class:y({"is-active":n.value==="payment"})},[t("a",{onClick:e[1]||(e[1]=s=>n.value="payment")},"Payment")],2),t("li",{class:y({"is-active":n.value==="shipping"})},[t("a",{onClick:e[2]||(e[2]=s=>n.value="shipping")},"Shipping")],2),t("li",{class:y({"is-active":n.value==="email"})},[t("a",{onClick:e[3]||(e[3]=s=>n.value="email")},"Email")],2),t("li",{class:y({"is-active":n.value==="api"})},[t("a",{onClick:e[4]||(e[4]=s=>n.value="api")},"API")],2)])]),n.value==="general"?(g(),c("div",F,[t("div",G,[e[39]||(e[39]=t("h2",{class:"subtitle"},"General Settings",-1)),t("div",z,[e[29]||(e[29]=t("label",{class:"label"},"Site Name",-1)),t("div",L,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[5]||(e[5]=s=>a.value.general.site_name=s)},null,512),[[r,a.value.general.site_name]])])]),t("div",O,[e[30]||(e[30]=t("label",{class:"label"},"Site Description",-1)),t("div",B,[l(t("textarea",{class:"textarea","onUpdate:modelValue":e[6]||(e[6]=s=>a.value.general.site_description=s)},null,512),[[r,a.value.general.site_description]])])]),t("div",H,[e[31]||(e[31]=t("label",{class:"label"},"Site Email",-1)),t("div",W,[l(t("input",{class:"input",type:"email","onUpdate:modelValue":e[7]||(e[7]=s=>a.value.general.site_email=s)},null,512),[[r,a.value.general.site_email]])])]),t("div",$,[e[32]||(e[32]=t("label",{class:"label"},"Site Phone",-1)),t("div",j,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[8]||(e[8]=s=>a.value.general.site_phone=s)},null,512),[[r,a.value.general.site_phone]])])]),t("div",q,[e[34]||(e[34]=t("label",{class:"label"},"Currency",-1)),t("div",J,[t("div",Q,[l(t("select",{"onUpdate:modelValue":e[9]||(e[9]=s=>a.value.general.currency=s)},e[33]||(e[33]=[U('<option value="USD" data-v-5ed66d06>US Dollar (USD)</option><option value="EUR" data-v-5ed66d06>Euro (EUR)</option><option value="GBP" data-v-5ed66d06>British Pound (GBP)</option><option value="JPY" data-v-5ed66d06>Japanese Yen (JPY)</option><option value="UAH" data-v-5ed66d06>Ukrainian Hryvnia (UAH)</option><option value="CAD" data-v-5ed66d06>Canadian Dollar (CAD)</option>',6)]),512),[[x,a.value.general.currency]])])])]),t("div",X,[e[35]||(e[35]=t("label",{class:"label"},"Currency Symbol",-1)),t("div",Z,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[10]||(e[10]=s=>a.value.general.currency_symbol=s)},null,512),[[r,a.value.general.currency_symbol]])])]),t("div",ee,[e[37]||(e[37]=t("label",{class:"label"},"Timezone",-1)),t("div",te,[t("div",se,[l(t("select",{"onUpdate:modelValue":e[11]||(e[11]=s=>a.value.general.timezone=s)},e[36]||(e[36]=[U('<option value="Europe/Kiev" data-v-5ed66d06>Europe/Kiev</option><option value="Europe/London" data-v-5ed66d06>Europe/London</option><option value="America/New_York" data-v-5ed66d06>America/New_York</option><option value="America/Los_Angeles" data-v-5ed66d06>America/Los_Angeles</option><option value="Asia/Tokyo" data-v-5ed66d06>Asia/Tokyo</option>',5)]),512),[[x,a.value.general.timezone]])])])]),t("div",ae,[t("div",ne,[t("label",ie,[l(t("input",{type:"checkbox","onUpdate:modelValue":e[12]||(e[12]=s=>h(k)?k.value=s:null)},null,512),[[f,S(k)]]),e[38]||(e[38]=b(" Enable Maintenance Mode "))])])])])])):v("",!0),n.value==="payment"?(g(),c("div",le,[t("div",oe,[e[43]||(e[43]=t("h2",{class:"subtitle"},"Payment Settings",-1)),t("div",re,[t("div",de,[t("label",pe,[l(t("input",{type:"checkbox","onUpdate:modelValue":e[13]||(e[13]=s=>h(E)?E.value=s:null)},null,512),[[f,S(E)]]),e[40]||(e[40]=b(" Enable Stripe "))])])]),t("div",ue,[t("div",me,[t("label",ce,[l(t("input",{type:"checkbox","onUpdate:modelValue":e[14]||(e[14]=s=>h(w)?w.value=s:null)},null,512),[[f,S(w)]]),e[41]||(e[41]=b(" Enable PayPal "))])])]),t("div",ge,[e[42]||(e[42]=t("label",{class:"label"},"Commission Rate (%)",-1)),t("div",ve,[l(t("input",{class:"input",type:"number","onUpdate:modelValue":e[15]||(e[15]=s=>a.value.payment.commission_rate=s),step:"0.01"},null,512),[[r,a.value.payment.commission_rate]])])])])])):v("",!0),n.value==="shipping"?(g(),c("div",ye,[t("div",be,[e[47]||(e[47]=t("h2",{class:"subtitle"},"Shipping Settings",-1)),t("div",_e,[e[44]||(e[44]=t("label",{class:"label"},"Free Shipping Minimum Order Amount",-1)),t("div",fe,[l(t("input",{class:"input",type:"number","onUpdate:modelValue":e[16]||(e[16]=s=>a.value.shipping.free_shipping_minimum=s),step:"0.01"},null,512),[[r,a.value.shipping.free_shipping_minimum]])])]),t("div",Se,[e[45]||(e[45]=t("label",{class:"label"},"Standard Shipping Rate",-1)),t("div",he,[l(t("input",{class:"input",type:"number","onUpdate:modelValue":e[17]||(e[17]=s=>a.value.shipping.standard_shipping_rate=s),step:"0.01"},null,512),[[r,a.value.shipping.standard_shipping_rate]])])]),t("div",ke,[e[46]||(e[46]=t("label",{class:"label"},"Express Shipping Rate",-1)),t("div",Ee,[l(t("input",{class:"input",type:"number","onUpdate:modelValue":e[18]||(e[18]=s=>a.value.shipping.express_shipping_rate=s),step:"0.01"},null,512),[[r,a.value.shipping.express_shipping_rate]])])])])])):v("",!0),n.value==="email"?(g(),c("div",we,[t("div",Pe,[e[53]||(e[53]=t("h2",{class:"subtitle"},"Email Settings",-1)),t("div",xe,[e[48]||(e[48]=t("label",{class:"label"},"SMTP Host",-1)),t("div",Ue,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[19]||(e[19]=s=>a.value.email.smtp_host=s)},null,512),[[r,a.value.email.smtp_host]])])]),t("div",Ae,[e[49]||(e[49]=t("label",{class:"label"},"SMTP Port",-1)),t("div",Ve,[l(t("input",{class:"input",type:"number","onUpdate:modelValue":e[20]||(e[20]=s=>a.value.email.smtp_port=s)},null,512),[[r,a.value.email.smtp_port]])])]),t("div",Me,[e[50]||(e[50]=t("label",{class:"label"},"SMTP Username",-1)),t("div",Ce,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[21]||(e[21]=s=>a.value.email.smtp_username=s)},null,512),[[r,a.value.email.smtp_username]])])]),t("div",De,[e[51]||(e[51]=t("label",{class:"label"},"From Email",-1)),t("div",Ke,[l(t("input",{class:"input",type:"email","onUpdate:modelValue":e[22]||(e[22]=s=>a.value.email.from_email=s)},null,512),[[r,a.value.email.from_email]])])]),t("div",Te,[e[52]||(e[52]=t("label",{class:"label"},"From Name",-1)),t("div",Ye,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[23]||(e[23]=s=>a.value.email.from_name=s)},null,512),[[r,a.value.email.from_name]])])])])])):v("",!0),n.value==="api"?(g(),c("div",Ne,[t("div",Ie,[e[58]||(e[58]=t("h2",{class:"subtitle"},"API Settings",-1)),t("div",Re,[e[54]||(e[54]=t("label",{class:"label"},"API Key",-1)),t("div",Fe,[l(t("input",{class:"input",type:"text","onUpdate:modelValue":e[24]||(e[24]=s=>a.value.api.apiKey=s),readonly:""},null,512),[[r,a.value.api.apiKey]])]),e[55]||(e[55]=t("p",{class:"help"},"This is your API key. Keep it secure.",-1))]),t("div",Ge,[t("div",ze,[t("button",{class:"button is-info",onClick:e[25]||(e[25]=(...s)=>o.regenerateApiKey&&o.regenerateApiKey(...s))}," Regenerate API Key ")])]),t("div",Le,[e[56]||(e[56]=t("label",{class:"label"},"API Rate Limit (requests per minute)",-1)),t("div",Oe,[l(t("input",{class:"input",type:"number","onUpdate:modelValue":e[26]||(e[26]=s=>a.value.api.api_rate_limit=s)},null,512),[[r,a.value.api.api_rate_limit]])])]),t("div",Be,[t("div",He,[t("label",We,[l(t("input",{type:"checkbox","onUpdate:modelValue":e[27]||(e[27]=s=>h(P)?P.value=s:null)},null,512),[[f,S(P)]]),e[57]||(e[57]=b(" Enable Webhooks "))])])])])])):v("",!0),t("div",$e,[n.value==="email"?(g(),c("div",je,[t("button",{class:"button is-info",onClick:M,disabled:u.value||d.value}," Test Email Settings ",8,qe)])):v("",!0),t("div",Je,[t("button",{class:y(["button is-primary",{"is-loading":d.value}]),onClick:V,disabled:u.value||d.value}," Save Settings ",10,Qe)])])]))]))}},et=K(Xe,[["__scopeId","data-v-5ed66d06"]]);export{et as default};
