﻿
using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using Marketplace.Infrastructure.Persistence;
using Marketplace.Infrastructure.Persistence.Implementation;
using Marketplace.Infrastructure.Persistence.Repositories;
using Marketplace.Infrastructure.Repositories;
using Marketplace.Infrastructure.Services;
using Marketplace.Infrastructure.Services.Auth;
using Marketplace.Infrastructure.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Marketplace.Infrastructure;

public static class InfrastructureServiceExtensions
{
    // Extension-метод для IServiceCollection
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, string connectionString, IConfiguration configuration)
    {
        // Додавання DbContext
        services.AddDbContext<MarketplaceDbContext>(options =>
        {
            // Only enable sensitive data logging in development
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
            {
                options.EnableSensitiveDataLogging();
            }

            options.UseNpgsql(connectionString, sqlOptions =>
                sqlOptions.MigrationsAssembly("Marketplace.Infrastructure"));
        });

        // Конфігурація та реєстрація сервісу файлів
        services.Configure<Services.FileServiceOptions>(options =>
        {
            var section = configuration.GetSection("FileService");
            options.StoragePath = section["StoragePath"];
            options.BaseUrl = section["BaseUrl"];

            if (long.TryParse(section["MaxFileSize"], out var maxFileSize))
                options.MaxFileSize = maxFileSize;

            var allowedTypes = section.GetSection("AllowedFileTypes").GetChildren();
            var allowedFileTypes = new List<string>();
            foreach (var type in allowedTypes)
            {
                allowedFileTypes.Add(type.Value);
            }
            options.AllowedFileTypes = allowedFileTypes.ToArray();
        });
        services.AddScoped<IFileService, LocalFileService>();
        services.AddScoped<IFileStorageService, FileStorageService>();
        services.AddScoped<IImageService, ImageService>();

        //TODO: зареєструвати всі репозиторії, придумати кастомні методи для репозиторіїв (при потребі)

        // Реєстрація репозиторіїв або інших сервісів інфраструктури
        //services.AddScoped<IUserRepository, UserRepository>();
        //services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IAddressRepository, AddressRepository>();
        services.AddScoped<ICategoryRepository, CategoryRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IChatRepository, ChatRepository>();
        services.AddScoped<ICompanyRepository, CompanyRepository>();
        services.AddScoped<ICompanyFinanceRepository, CompanyFinanceRepository>();
        services.AddScoped<ICompanyScheduleRepository, CompanyScheduleRepository>();
        services.AddScoped<ICompanyUserRepository, CompanyUserRepository>();
        services.AddScoped<ICouponRepository, CouponRepository>();
        services.AddScoped<IFavoriteRepository, FavoriteRepository>();
        services.AddScoped<ILogRepository, LogRepository>();
        services.AddScoped<IMessageRepository, MessageRepository>();
        services.AddScoped<INotificationRepository, NotificationRepository>();
        services.AddScoped<IOrderCouponRepository, OrderCouponRepository>();
        services.AddScoped<IOrderItemRepository, OrderItemRepository>();
        services.AddScoped<IOrderNoteRepository, OrderNoteRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();
        services.AddScoped<IProductImageRepository, ProductImageRepository>();
        services.AddScoped<IProductRepository, ProductRepository>();
        services.AddScoped<IRatingRepository, RatingRepository>();
        services.AddScoped<IReviewRepository, ReviewRepository>();
        services.AddScoped<ISellerRequestRepository, SellerRequestRepository>();
        services.AddScoped<ISettingRepository, SettingRepository>();
        services.AddScoped<IShippingMethodRepository, ShippingMethodRepository>();
        services.AddScoped<IWishlistItemRepository, WishlistItemRepository>();
        services.AddScoped<IWishlistRepository, WishlistRepository>();
        services.AddScoped<ICartRepository, CartRepository>();
        services.AddScoped<ICartItemRepository, CartItemRepository>();


        // Додавання логування (як приклад)
        services.AddLogging();

        // Реєстрація DatabaseSeeder
        services.AddScoped<DatabaseSeeder.DatabaseSeeder>();

        // Реєстрація сервісів автентифікації
        services.AddScoped<IPasswordHasher, PasswordHasher>();

        return services;
    }
}
