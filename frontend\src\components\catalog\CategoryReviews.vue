<template>
  <div class="category-reviews">
    <h3 class="reviews-title">Відгуки покупців</h3>
    <div v-if="reviews.length === 0" class="no-reviews">
      Немає відгуків для товарів цієї категорії
    </div>
    <div v-else class="reviews-list">
      <div v-for="review in reviews" :key="review.id" class="review-card">
        <div class="review-header">
          <div class="product-info">
            <strong>{{ review.productName }}</strong>
          </div>
          <div class="rating">
            <span class="stars">★★★★★</span>
            <span class="rating-value">{{ calculateAvgRating(review.rating) }}/5</span>
          </div>
        </div>
        <div class="review-content">{{ review.comment }}</div>
        <div class="review-footer">
          <span class="review-date">{{ formatDate(review.createdAt) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    reviews: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    calculateAvgRating(rating) {
      if (!rating) return 0;
      return ((rating.service + rating.deliveryTime + rating.accuracy) / 3).toFixed(1);
    },
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('uk-UA');
    }
  }
}
</script>

<style scoped>
.category-reviews {
  margin-top: 30px;
  padding: 20px 0;
}

.reviews-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  font-weight: bold;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.review-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
}

.review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stars {
  color: #FFD700;
  letter-spacing: 2px;
}

.review-content {
  margin-bottom: 10px;
  line-height: 1.5;
}

.review-footer {
  color: #666;
  font-size: 0.9rem;
}

.no-reviews {
  padding: 20px;
  text-align: center;
  color: #666;
}
</style>