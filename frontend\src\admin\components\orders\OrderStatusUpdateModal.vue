<template>
  <div class="order-status-modal" :class="{ 'modal-active': isOpen }">
    <div class="modal-backdrop" @click="closeModal"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Update Order Status</h3>
        <button class="modal-close" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- Order Info -->
        <div class="order-info" v-if="order">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">Order ID</label>
              <span class="info-value">#{{ order.id }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">Customer</label>
              <span class="info-value">{{ customerName }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">Total Amount</label>
              <span class="info-value info-value-price">{{ formatCurrency(order.totalPrice) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">Created</label>
              <span class="info-value">{{ formatDate(order.createdAt) }}</span>
            </div>
          </div>
        </div>

        <!-- Status Form -->
        <form @submit.prevent="updateStatus" class="status-form">
          <div class="form-field">
            <label class="form-label">
              Order Status <span class="required">*</span>
            </label>
            <select v-model="form.status" class="form-select" required>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <p class="form-help">Current status: {{ order?.status || 'Unknown' }}</p>
          </div>

          <div class="form-field">
            <label class="form-label">
              Payment Status <span class="required">*</span>
            </label>
            <select v-model="form.paymentStatus" class="form-select" required>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
            </select>
            <p class="form-help">Current payment status: {{ order?.paymentStatus || 'Pending' }}</p>
          </div>

          <div class="form-field">
            <label class="form-label">Notes (Optional)</label>
            <textarea 
              v-model="form.notes" 
              class="form-textarea" 
              placeholder="Add any notes about this status update..."
              rows="3"></textarea>
          </div>

          <!-- Status Preview -->
          <div class="status-preview">
            <h4 class="preview-title">Status Preview</h4>
            <div class="preview-badges">
              <span class="status-badge" :class="getStatusClass(form.status)">
                {{ form.status }}
              </span>
              <span class="status-badge" :class="getPaymentStatusClass(form.paymentStatus)">
                {{ form.paymentStatus }}
              </span>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          class="action-btn action-btn-primary"
          @click="updateStatus"
          :disabled="updating || !hasChanges"
          :class="{ 'action-btn-loading': updating }">
          <i class="fas fa-spinner fa-spin" v-if="updating"></i>
          <i class="fas fa-save" v-else></i>
          {{ updating ? 'Updating...' : 'Update Status' }}
        </button>
        <button 
          type="button"
          class="action-btn action-btn-secondary" 
          @click="closeModal"
          :disabled="updating">
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { ordersService } from '@/admin/services/orders';
import { useToast } from '@/composables/useToast';

// Composables
const { showToast } = useToast();

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['close', 'updated']);

// Reactive data
const updating = ref(false);

// Form data
const form = reactive({
  status: '',
  paymentStatus: '',
  notes: ''
});

// Computed properties
const customerName = computed(() => {
  return props.order?.customerName || props.order?.customer?.name || 'Unknown Customer';
});

const hasChanges = computed(() => {
  if (!props.order) return false;
  return form.status !== (props.order.status || 'pending') ||
         form.paymentStatus !== (props.order.paymentStatus || 'pending');
});

// Methods
const formatCurrency = (amount) => {
  if (amount == null) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(amount);
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('uk-UA');
};

const getStatusClass = (status) => {
  if (!status || typeof status !== 'string') return 'status-badge-light';

  switch (status.toLowerCase()) {
    case 'delivered':
    case 'completed':
      return 'status-badge-success';
    case 'cancelled':
    case 'failed':
      return 'status-badge-danger';
    case 'processing':
    case 'shipped':
      return 'status-badge-info';
    case 'pending':
    case 'confirmed':
      return 'status-badge-warning';
    default:
      return 'status-badge-light';
  }
};

const getPaymentStatusClass = (status) => {
  if (!status || typeof status !== 'string') return 'status-badge-light';

  switch (status.toLowerCase()) {
    case 'paid':
      return 'status-badge-success';
    case 'failed':
    case 'refunded':
      return 'status-badge-danger';
    case 'pending':
      return 'status-badge-warning';
    default:
      return 'status-badge-light';
  }
};

const updateStatus = async () => {
  if (!props.order?.id || !hasChanges.value) return;

  updating.value = true;
  try {
    const updateData = {
      status: form.status,
      paymentStatus: form.paymentStatus
    };

    if (form.notes.trim()) {
      updateData.notes = form.notes.trim();
    }

    await ordersService.updateOrderStatus(props.order.id, updateData);
    
    emit('updated', {
      orderId: props.order.id,
      status: form.status,
      paymentStatus: form.paymentStatus,
      notes: form.notes
    });
    
    closeModal();

  } catch (error) {
    console.error('Error updating order status:', error);
    
    // Show error message
    const errorMessage = error.response?.data?.message || 'Failed to update order status. Please try again.';
    showToast(errorMessage, 'error');
  } finally {
    updating.value = false;
  }
};

const closeModal = () => {
  if (!updating.value) {
    emit('close');
  }
};

const resetForm = () => {
  if (props.order) {
    form.status = props.order.status || 'pending';
    form.paymentStatus = props.order.paymentStatus || 'pending';
    form.notes = '';
  }
};

// Watchers
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.order) {
    resetForm();
  }
});

watch(() => props.order, (newOrder) => {
  if (newOrder && props.isOpen) {
    resetForm();
  }
});
</script>

<style scoped>
/* Modal */
.order-status-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-active {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.modal-active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

/* Order Info */
.order-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 500;
}

.info-value-price {
  color: #059669;
  font-weight: 600;
}

/* Form */
.status-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.form-select,
.form-textarea {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

/* Status Preview */
.status-preview {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
}

.preview-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
}

.preview-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Status Badges */
.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge-success {
  background: #10b981;
  color: white;
}

.status-badge-warning {
  background: #f59e0b;
  color: white;
}

.status-badge-danger {
  background: #ef4444;
  color: white;
}

.status-badge-info {
  background: #3b82f6;
  color: white;
}

.status-badge-light {
  background: #f3f4f6;
  color: #6b7280;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.action-btn-secondary {
  background: #6b7280;
  color: white;
}

.action-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.action-btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .info-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .preview-badges {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 1.125rem;
  }
  
  .action-btn {
    justify-content: center;
  }
}
</style>
