import{X as c}from"./index-L-hJxM_5.js";class u{constructor(){this.baseURL="/universal"}async uploadImage(e,t,s,a=null){const n=new FormData;n.append("file",s);const i={headers:{"Content-Type":"multipart/form-data"},onUploadProgress:r=>{if(a){const h=Math.round(r.loaded*100/r.total);a(h)}}};try{return(await c.post(`${this.baseURL}/images/${e}/${t}`,n,i)).data}catch(r){throw this.handleError(r)}}async uploadMetaImage(e,t,s,a=null){const n=new FormData;n.append("file",s);const i={headers:{"Content-Type":"multipart/form-data"},onUploadProgress:r=>{if(a){const h=Math.round(r.loaded*100/r.total);a(h)}}};try{return(await c.post(`${this.baseURL}/meta-images/${e}/${t}`,n,i)).data}catch(r){throw this.handleError(r)}}async getImages(e,t){try{return(await c.get(`${this.baseURL}/images/${e}/${t}`)).data}catch(s){throw this.handleError(s)}}async deleteImage(e){try{return(await c.delete(`${this.baseURL}/images/${e}`)).data}catch(t){throw this.handleError(t)}}async deleteMetaImage(e,t){try{return(await c.delete(`${this.baseURL}/meta-images/${e}/${t}`)).data}catch(s){throw this.handleError(s)}}async setMainImage(e,t,s){try{return(await c.patch(`${this.baseURL}/images/${e}/${t}/main/${s}`)).data}catch(a){throw this.handleError(a)}}validateImageFile(e,t={}){const{maxSize:s=5*1024*1024,allowedTypes:a=["image/jpeg","image/png","image/gif","image/webp"],maxWidth:n=2048,maxHeight:i=2048}=t,r=[];return a.includes(e.type)||r.push(`Дозволені тільки файли типів: ${a.join(", ")}`),e.size>s&&r.push(`Розмір файлу не повинен перевищувати ${s/1024/1024}MB`),{isValid:r.length===0,errors:r}}createImagePreview(e){return new Promise((t,s)=>{const a=new FileReader;a.onload=n=>t(n.target.result),a.onerror=s,a.readAsDataURL(e)})}handleError(e){var t,s;return e.response?{message:((t=e.response.data)==null?void 0:t.message)||"Помилка сервера",status:e.response.status,errors:((s=e.response.data)==null?void 0:s.errors)||[]}:e.request?{message:"Помилка мережі. Перевірте підключення до інтернету.",status:0,errors:[]}:{message:e.message||"Невідома помилка",status:0,errors:[]}}async validateImageAdvanced(e,t={}){const{maxSize:s=5*1024*1024,allowedTypes:a=["image/jpeg","image/jpg","image/png","image/gif","image/webp"],minWidth:n=0,minHeight:i=0,maxWidth:r=4096,maxHeight:h=4096}=t,m=[];if(a.includes(e.type)||m.push(`Непідтримуваний тип файлу. Дозволені: ${a.join(", ")}`),e.size>s){const o=(s/1048576).toFixed(1),d=(e.size/(1024*1024)).toFixed(1);m.push(`Розмір файлу ${d}MB перевищує максимальний ${o}MB`)}if(n>0||i>0||r<4096||h<4096)try{const o=await this.getImageDimensions(e);o.width<n&&m.push(`Ширина зображення ${o.width}px менша за мінімальну ${n}px`),o.height<i&&m.push(`Висота зображення ${o.height}px менша за мінімальну ${i}px`),o.width>r&&m.push(`Ширина зображення ${o.width}px перевищує максимальну ${r}px`),o.height>h&&m.push(`Висота зображення ${o.height}px перевищує максимальну ${h}px`)}catch{m.push("Неможливо визначити розміри зображення")}return{isValid:m.length===0,errors:m,fileSize:e.size,fileName:e.name,fileType:e.type}}getImageDimensions(e){return new Promise((t,s)=>{const a=new Image;a.onload=()=>{t({width:a.width,height:a.height})},a.onerror=()=>{s(new Error("Неможливо завантажити зображення"))},a.src=URL.createObjectURL(e)})}compressImage(e,t={}){const{maxWidth:s=1920,maxHeight:a=1080,quality:n=.8,outputFormat:i="image/jpeg"}=t;return new Promise(r=>{const h=document.createElement("canvas"),m=h.getContext("2d"),o=new Image;o.onload=()=>{let{width:d,height:g}=o;if(d>s||g>a){const p=Math.min(s/d,a/g);d*=p,g*=p}h.width=d,h.height=g,m.drawImage(o,0,0,d,g),h.toBlob(p=>{const l=new File([p],e.name,{type:i,lastModified:Date.now()});r(l)},i,n)},o.src=URL.createObjectURL(e)})}async createCroppedPreview(e,t=null){return new Promise(s=>{const a=document.createElement("canvas"),n=a.getContext("2d"),i=new Image;i.onload=()=>{t?(a.width=t.width,a.height=t.height,n.drawImage(i,t.x,t.y,t.width,t.height,0,0,t.width,t.height)):(a.width=i.width,a.height=i.height,n.drawImage(i,0,0)),s(a.toDataURL())},i.src=URL.createObjectURL(e)})}formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,a)).toFixed(2))+" "+s[a]}shouldCompressImage(e,t=1024*1024){return e.size>t||e.type==="image/png"}}const y=new u;export{y as i};
