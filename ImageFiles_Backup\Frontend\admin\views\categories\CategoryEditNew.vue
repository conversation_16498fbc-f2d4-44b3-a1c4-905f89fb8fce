<template>
  <div class="category-edit-new">
    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="loading-text">Loading category...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error && !category" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="error-title">Error Loading Category</h3>
      <p class="error-message">{{ error }}</p>
      <button class="retry-btn" @click="fetchCategory(route.params.id)">
        <i class="fas fa-redo"></i>
        Try Again
      </button>
    </div>

    <!-- Category Not Found -->
    <div v-else-if="!category && !loading" class="not-found-state">
      <div class="not-found-icon">
        <i class="fas fa-folder-open"></i>
      </div>
      <h3 class="not-found-title">Category Not Found</h3>
      <p class="not-found-message">The requested category does not exist or has been deleted.</p>
      <router-link to="/admin/categories" class="back-btn">
        <i class="fas fa-arrow-left"></i>
        Back to Categories
      </router-link>
    </div>

    <!-- Edit Form -->
    <div v-else class="edit-content">
      <!-- Header -->
      <div class="edit-header">
        <div class="header-content">
          <nav class="breadcrumb">
            <router-link to="/admin" class="breadcrumb-item">Dashboard</router-link>
            <span class="breadcrumb-separator">/</span>
            <router-link to="/admin/categories" class="breadcrumb-item">Categories</router-link>
            <span class="breadcrumb-separator">/</span>
            <router-link :to="`/admin/categories/${category.id}`" class="breadcrumb-item">{{ category.name }}</router-link>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item breadcrumb-current">Edit</span>
          </nav>
          <h1 class="edit-title">Edit Category</h1>
          <p class="edit-subtitle">Update category information and settings</p>
        </div>
        <div class="header-actions">
          <router-link :to="`/admin/categories/${category.id}`" class="action-btn action-btn-secondary">
            <i class="fas fa-eye"></i>
            View Category
          </router-link>
          <router-link to="/admin/categories" class="action-btn action-btn-light">
            <i class="fas fa-arrow-left"></i>
            Back to Categories
          </router-link>
        </div>
      </div>

      <!-- Messages -->
      <div v-if="error" class="message message-error">
        <div class="message-content">
          <i class="fas fa-exclamation-circle"></i>
          <span>{{ error }}</span>
        </div>
        <button class="message-close" @click="error = ''">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div v-if="success" class="message message-success">
        <div class="message-content">
          <i class="fas fa-check-circle"></i>
          <span>{{ success }}</span>
        </div>
        <button class="message-close" @click="success = ''">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="submitForm" class="edit-form">
        <!-- Basic Information Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-info-circle"></i>
              Basic Information
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">
                  Name <span class="required">*</span>
                </label>
                <input 
                  class="form-input" 
                  type="text" 
                  placeholder="Category name" 
                  v-model="form.name"
                  @input="generateSlug"
                  required>
              </div>

              <div class="form-field">
                <label class="form-label">
                  Slug <span class="required">*</span>
                </label>
                <input 
                  class="form-input form-input-code" 
                  type="text" 
                  placeholder="category-slug" 
                  v-model="form.slug"
                  required>
                <p class="form-help">URL-friendly version of the name. Auto-generated but can be edited.</p>
              </div>
            </div>

            <div class="form-field">
              <label class="form-label">Description</label>
              <textarea 
                class="form-textarea" 
                placeholder="Category description" 
                v-model="form.description"
                rows="3"></textarea>
            </div>
          </div>
        </div>

        <!-- Parent Category Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-sitemap"></i>
              Category Hierarchy
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-field">
              <label class="form-label">Parent Category</label>
              <EnhancedCategorySelector
                v-model="form.parentId"
                :exclude-category-id="category.id"
                label=""
                placeholder="Search for parent category (leave empty for top level)..."
                help-text="Select a parent category or leave empty to make this a root category"
                @change="handleParentCategoryChange"
              />
            </div>
          </div>
        </div>

        <!-- Images Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-images"></i>
              Images
            </h3>
          </div>
          <div class="form-card-content">
            <!-- Category Image -->
            <div class="form-field">
              <CategoryImageUploader
                ref="imageUploader"
                label="Category Image"
                upload-text="Upload Category Image"
                :current-image-url="form.image"
                :entity-id="category.id"
                image-type="logo"
                @image-changed="handleImageChanged"
              />
              <p class="form-help">Upload an image to represent this category. Recommended size: 300x300 pixels.</p>
            </div>

            <!-- Meta Image -->
            <div class="form-field">
              <CategoryImageUploader
                ref="metaUploader"
                label="Meta Image (SEO)"
                upload-text="Upload Meta Image"
                :current-image-url="form.metaImage"
                :entity-id="category.id"
                image-type="meta"
                :social-title="form.name || 'Category Name'"
                :social-description="form.description || 'Category Description'"
                @image-changed="handleMetaChanged"
              />
              <p class="form-help">Upload a meta image for SEO and social media sharing.</p>
            </div>
          </div>
        </div>

        <!-- SEO Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-search"></i>
              SEO Settings
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">Meta Title</label>
                <input 
                  class="form-input" 
                  type="text" 
                  placeholder="SEO title for search engines" 
                  v-model="form.metaTitle"
                  maxlength="60">
                <p class="form-help">
                  <span :class="{ 'text-danger': form.metaTitle && form.metaTitle.length > 60 }">
                    {{ form.metaTitle ? form.metaTitle.length : 0 }}/60 characters
                  </span>
                </p>
              </div>

              <div class="form-field">
                <label class="form-label">Display Order</label>
                <input 
                  class="form-input" 
                  type="number" 
                  min="0" 
                  placeholder="0" 
                  v-model.number="form.displayOrder">
                <p class="form-help">Categories with lower numbers will be displayed first.</p>
              </div>
            </div>

            <div class="form-field">
              <label class="form-label">Meta Description</label>
              <textarea 
                class="form-textarea" 
                placeholder="SEO description for search engines" 
                v-model="form.metaDescription"
                maxlength="160"
                rows="3"></textarea>
              <p class="form-help">
                <span :class="{ 'text-danger': form.metaDescription && form.metaDescription.length > 160 }">
                  {{ form.metaDescription ? form.metaDescription.length : 0 }}/160 characters
                </span>
              </p>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button 
            type="submit" 
            class="action-btn action-btn-primary action-btn-large"
            :disabled="saving"
            :class="{ 'action-btn-loading': saving }">
            <i class="fas fa-spinner fa-spin" v-if="saving"></i>
            <i class="fas fa-save" v-else></i>
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
          <router-link 
            :to="`/admin/categories/${category.id}`" 
            class="action-btn action-btn-secondary action-btn-large">
            <i class="fas fa-times"></i>
            Cancel
          </router-link>
        </div>

        <!-- Upload Progress -->
        <div v-if="pendingUploads.length > 0" class="mt-3">
          <UploadProgress
            :uploads="pendingUploads"
            @retry-upload="retryUpload"
            @cancel-upload="cancelUpload"
          />
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoriesService } from '@/admin/services/categories';
import EnhancedCategorySelector from '@/admin/components/products/EnhancedCategorySelector.vue';
import CategoryImageUploader from '@/admin/components/category/CategoryImageUploader.vue';
import UploadProgress from '@/admin/components/common/UploadProgress.vue';
import imageService from '@/services/image.service';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const saving = ref(false);
const error = ref('');
const success = ref('');
const category = ref(null);

// Component refs
const imageUploader = ref(null);
const metaUploader = ref(null);

// Upload management
const pendingUploads = ref([]);
const pendingImageChange = ref(null);
const pendingMetaImageChange = ref(null);

// Form data
const form = reactive({
  name: '',
  slug: '',
  description: '',
  parentId: null,
  image: '',
  metaImage: '',
  metaTitle: '',
  metaDescription: '',
  displayOrder: 0
});

// Computed properties
const categoryId = computed(() => route.params.id);

// Methods
const fetchCategory = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    const categoryData = await categoriesService.getById(id);
    category.value = categoryData;

    // Populate form with category data
    form.name = categoryData.name || '';
    form.slug = categoryData.slug || '';
    form.description = categoryData.description || '';
    form.parentId = categoryData.parentId || null;
    form.image = categoryData.image || '';
    form.metaImage = categoryData.metaImage || '';
    form.metaTitle = categoryData.metaTitle || '';
    form.metaDescription = categoryData.metaDescription || '';
    form.displayOrder = categoryData.displayOrder || 0;

  } catch (err) {
    console.error('Error fetching category:', err);
    if (err.name !== 'CanceledError' && err.code !== 'ERR_CANCELED') {
      error.value = 'Failed to load category. Please try again.';
    }
  } finally {
    loading.value = false;
  }
};

const generateSlug = () => {
  if (form.name) {
    form.slug = form.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
};

const handleParentCategoryChange = (parentId) => {
  form.parentId = parentId;
};

// Image handlers
const handleImageChanged = (change) => {
  pendingImageChange.value = change;
  console.log('Category image changed:', change);
};

const handleMetaChanged = (change) => {
  pendingMetaImageChange.value = change;
  console.log('Category meta image changed:', change);
};

// Upload progress methods
const retryUpload = async (uploadId) => {
  const upload = pendingUploads.value.find(u => u.id === uploadId);
  if (upload) {
    upload.status = 'uploading';
    upload.progress = 0;
    upload.error = null;

    try {
      // Retry logic would go here
      console.log('Retrying upload:', uploadId);
    } catch (error) {
      upload.status = 'error';
      upload.error = error.message;
    }
  }
};

const cancelUpload = (uploadId) => {
  const index = pendingUploads.value.findIndex(u => u.id === uploadId);
  if (index !== -1) {
    pendingUploads.value.splice(index, 1);
  }
};

const submitForm = async () => {
  saving.value = true;
  error.value = '';
  success.value = '';

  try {
    console.log('Cleaning up blob URLs before processing...');

    // Clean up any blob URLs in form data
    if (form.image && form.image.startsWith('blob:')) {
      console.log('Removing blob URL from image:', form.image);
      form.image = null;
    }
    if (form.metaImage && form.metaImage.startsWith('blob:')) {
      console.log('Removing blob URL from metaImage:', form.metaImage);
      form.metaImage = null;
    }

    console.log('Processing pending image operations...');

    // Process pending image operations
    const imageResults = {};

    if (imageUploader.value && imageUploader.value.hasChanges()) {
      console.log('Processing main image changes...');
      const result = await imageUploader.value.processChanges();
      if (result.success) {
        imageResults.image = result.url;
        console.log('Main image processed successfully:', result.url);
      } else {
        throw new Error(`Image upload failed: ${result.error}`);
      }
    } else {
      imageResults.image = form.image;
    }

    if (metaUploader.value && metaUploader.value.hasChanges()) {
      console.log('Processing meta image changes...');
      const result = await metaUploader.value.processChanges();
      if (result.success) {
        imageResults.metaImage = result.url;
        console.log('Meta image processed successfully:', result.url);
      } else {
        throw new Error(`Meta image upload failed: ${result.error}`);
      }
    } else {
      imageResults.metaImage = form.metaImage;
    }

    console.log('Image operations completed:', imageResults);

    // Prepare form data
    const formData = {
      name: form.name,
      slug: form.slug,
      description: form.description,
      parentId: form.parentId || null,
      image: imageResults.image || null,
      metaImage: imageResults.metaImage || null,
      metaTitle: form.metaTitle,
      metaDescription: form.metaDescription,
      displayOrder: form.displayOrder
    };

    console.log('Sending formData to backend:', formData);
    console.log('image in formData:', formData.image);
    console.log('metaImage in formData:', formData.metaImage);

    // Update category
    const result = await categoriesService.updateCategory(categoryId.value, formData);
    console.log('updateCategory result:', result);

    success.value = 'Category updated successfully!';

    // Refresh category data
    await fetchCategory(categoryId.value);

    // Reset image uploaders
    if (imageUploader.value) {
      imageUploader.value.reset();
    }
    if (metaUploader.value) {
      metaUploader.value.reset();
    }

  } catch (err) {
    console.error('Error updating category:', err);
    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to update category. Please try again.';
    }
  } finally {
    saving.value = false;
  }
};

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchCategory(newId);
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  const id = route.params.id;
  if (id) {
    fetchCategory(id);
  }
});
</script>

<style scoped>
/* Base Styles */
.category-edit-new {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* Loading States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1rem;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Not Found State */
.not-found-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.not-found-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.not-found-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.not-found-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.back-btn {
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

/* Header */
.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

.breadcrumb-current {
  color: #1f2937;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d1d5db;
}

.edit-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.edit-subtitle {
  color: #6b7280;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-secondary {
  background: #6b7280;
  color: white;
}

.action-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-light {
  background: #f3f4f6;
  color: #374151;
}

.action-btn-light:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
  color: #374151;
  text-decoration: none;
}

.action-btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.action-btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Messages */
.message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.message-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.message-success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.message-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Form */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-card-header {
  background: #f8fafc;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.form-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-card-title i {
  color: #6b7280;
}

.form-card-content {
  padding: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.form-field:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.form-input,
.form-textarea {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-code {
  font-family: 'Courier New', monospace;
  background: #f8fafc;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
}

.text-danger {
  color: #ef4444;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .category-edit-new {
    padding: 1.5rem;
  }

  .edit-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-btn-large {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .category-edit-new {
    padding: 1rem;
  }

  .edit-header {
    padding: 1.5rem;
  }

  .edit-title {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    justify-content: center;
  }

  .form-card-content {
    padding: 1rem;
  }

  .form-actions {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    flex-wrap: wrap;
  }

  .edit-title {
    font-size: 1.25rem;
  }

  .form-card-title {
    font-size: 1rem;
  }

  .form-card-header {
    padding: 1rem;
  }

  .form-card-content {
    padding: 1rem;
  }

  .form-actions {
    padding: 1rem;
  }
}
</style>
