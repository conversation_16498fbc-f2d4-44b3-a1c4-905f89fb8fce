using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.Product;

public record StoreProductCommand(
    Guid CompanyId,
    Guid SellerId,
    string Name,
    string Slug,
    string Description,
    Currency PriceCurrency,
    decimal PriceAmount,
    uint Stock,
    uint Sales,
    Guid CategoryId,
    ProductStatus Status,
    Dictionary<string, string>? Attributes,
    string MetaTitle,
    string MetaDescription,
    string MetaImage
) : IRequest<Guid>;

