import{q as n}from"./index-L-hJxM_5.js";const d={UAH:0,USD:1,EUR:2},g={UAH:d.UAH,USD:d.USD,EUR:d.EUR},l=a=>g[a]??d.UAH,m={async getProducts(a={}){try{console.log("Fetching products with params:",a);const{signal:e,...t}=a,r={};t.page&&(r.page=t.page),t.pageSize&&(r.pageSize=t.pageSize),t.limit&&(r.pageSize=t.limit),t.orderBy&&(r.orderBy=t.orderBy),t.sortBy&&(r.orderBy=t.sortBy),t.descending!==void 0&&(r.descending=t.descending),t.sortOrder&&(r.descending=t.sortOrder==="desc"),t.search&&t.search.trim()!==""?r.filter=t.search.trim():t.filter&&t.filter.trim()!==""&&(r.filter=t.filter.trim()),a.categoryId&&a.categoryId!==""&&(r.categoryId=a.categoryId),a.status&&a.status!==""&&(r.status=a.status),a.stock&&a.stock!==""&&(r.stock=a.stock);try{throw console.log("Sending standardized params to admin API:",r),new Error("Admin endpoint not implemented yet");let i=[],s={};response.data&&(response.data.data&&Array.isArray(response.data.data)?(i=response.data.data,s={total:response.data.total||response.data.totalItems||0,page:response.data.page||response.data.currentPage||r.page||1,pageSize:response.data.pageSize||response.data.perPage||r.pageSize||15,totalPages:response.data.totalPages||response.data.lastPage||Math.ceil((response.data.total||0)/(r.pageSize||15))}):Array.isArray(response.data)?(i=response.data,s={total:response.data.length,page:r.page||1,pageSize:r.pageSize||15,totalPages:Math.ceil(response.data.length/(r.pageSize||15))}):response.data.items&&Array.isArray(response.data.items)&&(i=response.data.items,s={total:response.data.total||response.data.totalItems||response.data.items.length,page:response.data.page||response.data.currentPage||r.page||1,pageSize:response.data.pageSize||response.data.perPage||r.pageSize||15,totalPages:response.data.totalPages||response.data.lastPage||Math.ceil((response.data.total||response.data.items.length)/(r.pageSize||15))}));const o={data:i,pagination:s,items:i,total:s.total,currentPage:s.page,totalPages:s.totalPages};return console.log("Products service returning:",{itemsCount:i.length,pagination:s,totalItems:s.total,totalPages:s.totalPages}),o}catch(i){console.warn("Admin products endpoint failed, falling back to public endpoint:",i.message);try{console.log("Using public products endpoint with params:",r);const s={params:r};e&&(s.signal=e);const o=await n.get("/api/products",s);console.log("Public products API response:",o.data);let c=[],p={};return o.data&&(o.data.data&&Array.isArray(o.data.data)?(c=o.data.data,p={total:o.data.total||o.data.totalItems||0,page:o.data.currentPage||r.page||1,pageSize:o.data.perPage||r.pageSize||15,totalPages:o.data.lastPage||Math.ceil((o.data.total||0)/(r.pageSize||15))}):Array.isArray(o.data)&&(c=o.data,p={total:o.data.length,page:r.page||1,pageSize:r.pageSize||15,totalPages:Math.ceil(o.data.length/(r.pageSize||15))})),{data:c,pagination:p,items:c,total:p.total,currentPage:p.page,totalPages:p.totalPages}}catch(s){return console.error("Both admin and public products endpoints failed:",s.message),{data:[],pagination:{total:0,page:r.page||1,pageSize:r.pageSize||20,totalPages:1},items:[],total:0,currentPage:r.page||1,totalPages:1}}}}catch(e){throw console.error("Error fetching products:",e),e}},async getProductById(a){try{const e=await n.get(`/api/admin/products/${a}`);if(e.data&&e.data.data)return e.data.data;if(e.data)return e.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error fetching product ${a}:`,e),e}},async getProductByIdWithImages(a){try{const e=await n.get(`/api/admin/products/${a}/with-images`);if(e.data&&e.data.data)return e.data.data;if(e.data)return e.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error fetching product with images ${a}:`,e),e}},async createProduct(a){try{console.log("🚀 Creating product with data:",a);const e={companyId:a.companyId,name:a.name,slug:a.slug,description:a.description||"",priceCurrency:l(a.priceCurrency),priceAmount:parseFloat(a.priceAmount),stock:parseInt(a.stock)||0,categoryId:a.categoryId,status:parseInt(a.status)||0,attributes:a.attributes?typeof a.attributes=="string"?JSON.parse(a.attributes):a.attributes:{},metaTitle:a.metaTitle||"",metaDescription:a.metaDescription||"",metaImage:a.metaImage||""};console.log("📋 Prepared command data:",e);const t=await n.post("/api/admin/products",e);return console.log("✅ Product creation response:",t.data),t.data}catch(e){throw console.error("❌ Error creating product:",e),e.response&&(console.error("📋 Response status:",e.response.status),console.error("📋 Response data:",e.response.data),console.error("📋 Response headers:",e.response.headers)),e}},async updateProduct(a,e){try{console.log("🚀 Updating product with data:",e);const t={name:e.name||null,slug:e.slug||null,description:e.description||null,priceCurrency:e.priceCurrency?l(e.priceCurrency):null,priceAmount:e.priceAmount?parseFloat(e.priceAmount):null,stock:e.stock?parseInt(e.stock):null,categoryId:e.categoryId||null,attributes:e.attributes?typeof e.attributes=="string"?JSON.parse(e.attributes):e.attributes:null,status:e.status!==void 0?parseInt(e.status):null,isApproved:e.isApproved||null,approvedAt:e.approvedAt||null,approvedByUserId:e.approvedByUserId||null,metaTitle:e.metaTitle||null,metaDescription:e.metaDescription||null,metaImage:e.metaImage||null};console.log("📋 Prepared command data:",t);const r=await n.put(`/api/admin/products/${a}`,t);return console.log("✅ Product update response:",r.data),r.data}catch(t){throw console.error("❌ Error updating product:",t),t.response&&(console.error("📋 Response status:",t.response.status),console.error("📋 Response data:",t.response.data),console.error("📋 Response headers:",t.response.headers)),t}},async deleteProduct(a){try{return(await n.delete(`/api/admin/products/${a}`)).data}catch(e){throw console.error(`Error deleting product ${a}:`,e),e}},async uploadProductImage(a,e){try{const t=new FormData;return t.append("images",e),(await n.post(`/api/admin/products/${a}/images`,t,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error(`Error uploading image for product ${a}:`,t),t}},async uploadProductImageSingle(a,e){try{const t=new FormData;return t.append("file",e),(await n.post(`/api/admin/products/${a}/images/single`,t,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error(`Error uploading single image for product ${a}:`,t),t}},async uploadProductMetaImage(a,e){try{const t=new FormData;return t.append("image",e),(await n.post(`/api/admin/products/${a}/meta-image`,t,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error(`Error uploading meta image for product ${a}:`,t),t}},async deleteProductImage(a,e){try{return(await n.delete(`/api/admin/products/${a}/images/${e}`)).data}catch(t){throw console.error(`Error deleting image ${e} for product ${a}:`,t),t}},async setMainProductImage(a,e){try{return(await n.patch(`/api/admin/products/${a}/images/${e}/main`)).data}catch(t){throw console.error(`Error setting main image ${e} for product ${a}:`,t),t}},async deleteProductMetaImage(a){try{return(await n.delete(`/api/admin/products/${a}/meta-image`)).data}catch(e){throw console.error(`Error deleting meta image for product ${a}:`,e),e}},async toggleProductStatus(a,e){try{return(await n.patch(`/api/admin/products/${a}/status`,{status:e})).data}catch(t){throw console.error(`Error updating status for product ${a}:`,t),t}},async getProductStats(){try{return(await n.get("/api/admin/products/stats")).data}catch(a){throw console.error("Error fetching product stats:",a),a}},async getCategories(a={}){try{console.log("Fetching categories with params:",a);const e={pageSize:a.pageSize||1e3,page:a.page||1};a.search&&a.search.trim()!==""&&(e.filter=a.search.trim());const t={params:e};a.signal&&(t.signal=a.signal);const r=await n.get("/api/admin/categories",t);console.log("Categories API response:",r.data);let i=[];r.data&&r.data.data&&Array.isArray(r.data.data)?i=r.data.data:Array.isArray(r.data)&&(i=r.data);const s=i.map(o=>({id:o.id,name:o.name,parentId:o.parentId,slug:o.slug}));return s.sort((o,c)=>o.name.localeCompare(c.name)),console.log("Processed categories:",s.length),s}catch(e){throw console.error("Error fetching categories:",e),e}},async getCompanies(a={}){try{console.log("Fetching companies with params:",a);const e={pageSize:a.pageSize||1e3,page:a.page||1};a.search&&a.search.trim()!==""&&(e.filter=a.search.trim());const t={params:e};a.signal&&(t.signal=a.signal);const r=await n.get("/api/admin/companies",t);console.log("Companies API response:",r.data);let i=[];r.data&&r.data.success&&r.data.data&&r.data.data.data&&Array.isArray(r.data.data.data)?i=r.data.data.data:r.data&&r.data.data&&Array.isArray(r.data.data)?i=r.data.data:Array.isArray(r.data)&&(i=r.data);const s=i.map(o=>({id:o.id,name:o.name,slug:o.slug,isApproved:o.isApproved}));return s.sort((o,c)=>o.name.localeCompare(c.name)),console.log("Processed companies:",s.length),s}catch(e){throw console.error("Error fetching companies:",e),e}},async getCompanyById(a){try{return(await n.get(`/api/admin/companies/${a}`)).data}catch(e){throw console.error(`Error fetching company ${a}:`,e),e}},async getCategoryById(a){try{return(await n.get(`/api/categories/${a}`)).data}catch(e){throw console.error(`Error fetching category ${a}:`,e),e}},async updateProductStatus(a,e){try{const t=typeof e=="number"?{status:e}:e;return(await n.patch(`/api/admin/products/${a}/status`,t)).data}catch(t){throw console.error(`Error updating status for product ${a}:`,t),t}},async approveProduct(a){try{return(await n.patch(`/api/admin/products/${a}/approve`)).data}catch(e){throw console.error(`Error approving product ${a}:`,e),e}},async rejectProduct(a,e=""){try{return(await n.patch(`/api/admin/products/${a}/reject`,{reason:e})).data}catch(t){throw console.error(`Error rejecting product ${a}:`,t),t}}};export{m as p};
