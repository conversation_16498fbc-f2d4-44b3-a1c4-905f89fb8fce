<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-box admin-card-icon"></i>
        Company Products
      </h3>
      <div class="admin-card-actions">
        <button class="admin-btn admin-btn-sm admin-btn-secondary" @click="fetchProducts" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
        </button>
        <router-link 
          :to="`/admin/products?companyId=${companyId}`"
          class="admin-btn admin-btn-sm admin-btn-primary">
          <i class="fas fa-external-link-alt"></i>
          View All
        </router-link>
      </div>
    </div>
    <div class="admin-card-content">
      
      <!-- Loading State with Skeleton -->
      <div v-if="loading" class="loading-state">
        <div class="skeleton-table">
          <div class="skeleton-row" v-for="i in 6" :key="i">
            <div class="skeleton-cell skeleton-image"></div>
            <div class="skeleton-cell skeleton-text"></div>
            <div class="skeleton-cell skeleton-text-short"></div>
            <div class="skeleton-cell skeleton-text-short"></div>
            <div class="skeleton-cell skeleton-text-short"></div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ error }}</span>
        <button
          @click="fetchProducts()"
          class="admin-btn admin-btn-sm admin-btn-primary retry-btn"
          :disabled="loading">
          <i class="fas fa-redo"></i>
          Retry
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="!products.length" class="empty-state">
        <i class="fas fa-box-open"></i>
        <span>No products found for this company</span>
      </div>

      <!-- Products Table -->
      <div v-else class="products-table-wrapper">
        <table class="admin-table products-table">
          <thead>
            <tr>
              <th>Product</th>
              <th>Category</th>
              <th>Price</th>
              <th>Stock</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="product in products" :key="product.id" class="product-row">
              <td class="product-cell">
                <div class="product-info">
                  <div class="product-image">
                    <img
                      v-if="product.metaImage"
                      :src="product.metaImage"
                      :alt="product.name"
                      class="product-thumbnail"
                    />
                    <i v-else class="fas fa-image placeholder-icon"></i>
                  </div>
                  <div class="product-details">
                    <div class="product-name">{{ product.name || 'N/A' }}</div>
                    <div class="product-sku">SKU: {{ product.id || 'N/A' }}</div>
                  </div>
                </div>
              </td>
              <td class="category-cell">
                <span class="category-badge">
                  {{ product.categoryName || 'Uncategorized' }}
                </span>
              </td>
              <td class="price-cell">
                <div class="price-info">
                  <div class="price-current">{{ formatPrice(product.priceAmount, product.priceCurrency) }}</div>
                </div>
              </td>
              <td class="stock-cell">
                <div class="stock-info">
                  <span class="stock-badge" :class="getStockClass(product.stock)">
                    {{ product.stock || 0 }}
                  </span>
                  <span class="stock-status">{{ getStockStatus(product.stock) }}</span>
                </div>
              </td>
              <td class="status-cell">
                <span class="status-badge" :class="getStatusClass(product.status)">
                  <i class="fas" :class="getStatusIcon(product.status)"></i>
                  {{ formatStatus(product.status) }}
                </span>
              </td>
              <td class="actions-cell">
                <div class="action-buttons">
                  <router-link 
                    :to="`/admin/products/${product.id}`"
                    class="admin-btn admin-btn-xs admin-btn-primary"
                    title="View Product">
                    <i class="fas fa-eye"></i>
                  </router-link>
                  <router-link 
                    :to="`/admin/products/${product.id}/edit`"
                    class="admin-btn admin-btn-xs admin-btn-secondary"
                    title="Edit Product">
                    <i class="fas fa-edit"></i>
                  </router-link>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination-wrapper">
        <div class="pagination">
          <button 
            class="admin-btn admin-btn-sm admin-btn-secondary"
            @click="changePage(currentPage - 1)"
            :disabled="currentPage <= 1">
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <span class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          
          <button 
            class="admin-btn admin-btn-sm admin-btn-secondary"
            @click="changePage(currentPage + 1)"
            :disabled="currentPage >= totalPages">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>

      <!-- Summary -->
      <div v-if="products.length" class="products-summary">
        <div class="summary-item">
          <span class="summary-label">Total Products:</span>
          <span class="summary-value">{{ totalProducts }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Active:</span>
          <span class="summary-value">{{ activeProductsCount }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Low Stock:</span>
          <span class="summary-value">{{ lowStockCount }}</span>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { companiesService } from '@/admin/services/companies';

// Debounce функція для оптимізації пошуку
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const props = defineProps({
  companyId: {
    type: String,
    required: true
  }
});

// Reactive data
const products = ref([]);
const loading = ref(false);
const error = ref(null);
const currentPage = ref(1);
const totalPages = ref(1);
const totalProducts = ref(0);
const pageSize = 10;

// Computed properties
const activeProductsCount = computed(() => {
  return products.value.filter(product => product.isActive).length;
});

const lowStockCount = computed(() => {
  return products.value.filter(product => product.stockQuantity < 10).length;
});

// Methods
const fetchProducts = async (page = 1, searchTerm = '') => {
  loading.value = true;
  error.value = null;

  try {
    const params = {
      page,
      pageSize
    };

    if (searchTerm.trim()) {
      params.filter = searchTerm.trim();
    }

    const response = await companiesService.getCompanyProducts(props.companyId, params);

    console.log('CompanyProductsTable received response:', response);

    // Обробляємо відповідь з новою структурою ApiResponse<PaginatedResponse<ProductResponse>>
    if (response && response.success && response.data) {
      products.value = response.data.data || [];
      totalPages.value = response.data.lastPage || 1;
      totalProducts.value = response.data.total || 0;
      currentPage.value = page;
      console.log('Products loaded:', products.value.length);
    } else {
      console.error('Invalid response structure:', response);
      products.value = [];
      totalPages.value = 1;
      totalProducts.value = 0;
      error.value = 'Invalid response format from server';
    }
  } catch (err) {
    console.error('Error fetching products:', err);
    error.value = err.message || 'Failed to load company products';
    products.value = [];
    totalPages.value = 1;
    totalProducts.value = 0;
  } finally {
    loading.value = false;
  }
};

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    fetchProducts(page);
  }
};

const formatPrice = (amount, currency = 'UAH') => {
  if (!amount && amount !== 0) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: currency || 'UAH'
  }).format(amount);
};

const getStockClass = (quantity) => {
  if (quantity === 0) return 'stock-out';
  if (quantity < 10) return 'stock-low';
  return 'stock-good';
};

const getStockStatus = (quantity) => {
  if (quantity === 0) return 'Out of Stock';
  if (quantity < 10) return 'Low Stock';
  return 'In Stock';
};

const getStatusClass = (status) => {
  switch (status) {
    case 0: // Pending
      return 'status-pending';
    case 1: // Approved
      return 'status-active';
    case 2: // Rejected
      return 'status-inactive';
    default:
      return 'status-pending';
  }
};

const getStatusIcon = (status) => {
  switch (status) {
    case 0: // Pending
      return 'fa-clock';
    case 1: // Approved
      return 'fa-check-circle';
    case 2: // Rejected
      return 'fa-times-circle';
    default:
      return 'fa-clock';
  }
};

const formatStatus = (status) => {
  switch (status) {
    case 0:
      return 'PENDING';
    case 1:
      return 'APPROVED';
    case 2:
      return 'REJECTED';
    default:
      return 'UNKNOWN';
  }
};

// Debounced search function
const debouncedSearch = debounce((searchTerm) => {
  fetchProducts(1, searchTerm);
}, 300);

// Search functionality
const searchProducts = (searchTerm) => {
  if (searchTerm.trim() === '') {
    fetchProducts();
  } else {
    debouncedSearch(searchTerm);
  }
};

// Initialize
onMounted(() => {
  fetchProducts();
});

// Cleanup
onUnmounted(() => {
  // Очищаємо кеш для цієї компанії при виході з компонента
  companiesService.clearCache(`/api/admin/companies/${props.companyId}/products`);
});
</script>

<style scoped>
.loading-state,
.error-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--admin-text-muted);
  font-size: 0.9rem;
}

.error-state {
  color: var(--admin-danger);
}

.products-table-wrapper {
  overflow-x: auto;
  margin-bottom: 1rem;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
}

.products-table th,
.products-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admin-border-light);
}

.products-table th {
  background: var(--admin-bg-secondary);
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-row:hover {
  background: var(--admin-bg-tertiary);
}

.product-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background: var(--admin-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-icon {
  color: var(--admin-text-muted);
  font-size: 1rem;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.product-name {
  font-weight: 500;
  color: var(--admin-text-primary);
  font-size: 0.9rem;
}

.product-sku {
  font-size: 0.8rem;
  color: var(--admin-text-muted);
  font-family: 'Courier New', monospace;
}

.category-badge {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.price-current {
  font-weight: 600;
  color: var(--admin-text-primary);
  font-size: 0.9rem;
}

.price-original {
  font-size: 0.8rem;
  color: var(--admin-text-muted);
  text-decoration: line-through;
}

.stock-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-start;
}

.stock-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.stock-good {
  background: var(--admin-success);
  color: white;
}

.stock-low {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.stock-out {
  background: var(--admin-danger);
  color: white;
}

.stock-status {
  font-size: 0.75rem;
  color: var(--admin-text-muted);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background: var(--admin-success);
  color: white;
}

.status-inactive {
  background: var(--admin-danger);
  color: white;
}

.status-pending {
  background: var(--admin-warning);
  color: white;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--admin-text-secondary);
}

.products-summary {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--admin-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--admin-border-light);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-label {
  font-size: 0.75rem;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-text-primary);
}

@media (max-width: 768px) {
  .products-table th,
  .products-table td {
    padding: 0.5rem;
  }
  
  .product-info {
    gap: 0.5rem;
  }
  
  .product-image {
    width: 32px;
    height: 32px;
  }
  
  .products-summary {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.retry-btn {
  margin-top: 1rem;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

/* Skeleton Loader Styles */
.skeleton-table {
  width: 100%;
}

.skeleton-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background: #f8f9fa;
}

.skeleton-cell {
  background: linear-gradient(90deg, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
}

.skeleton-text {
  height: 20px;
  flex: 1;
}

.skeleton-text-short {
  height: 20px;
  width: 80px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
