import apiClient from './api.service';

class CartService {
  // Get all categories with optional filtering and pagination
  async getCart(params = {}) 
  {
    try 
    {
      return await apiClient.get('/users/me/cart', { params });
    } 
    catch (error) 
    {
      console.error('Error fetching cart:', error);
      throw error;
    }
  }

  async addToCart(productId, quantity = 1, attributes = null)
  {
    try
    {
      // Перевіряємо, чи productId є валідним UUID
      if (!productId || typeof productId !== 'string') {
        throw new Error('Invalid productId');
      }

      const payload = {
        productId: productId,
        quantity: quantity,
        attributes: attributes || {} // Завжди передаємо об'єкт, навіть якщо порожній
      };

      return await apiClient.post('/users/me/cart/items', payload);
    }catch(error)
    {
      console.error(`Error adding item:`, error);
      throw error;
    }
  }

  async changeItemCount(itemId, quantity, attributes = null)
  {
    try
    {
      const payload = {
        quantity: quantity,
        attributes: attributes || {} // Завжди передаємо об'єкт, навіть якщо порожній
      };

      return await apiClient.put(`/users/me/cart/items/${itemId}`, payload);
    } catch(error)
    {
      console.error(`Error updating item:`, error);
      throw error;
    }
  }
  
  async deleteItem(itemId)
  {
    try
    {
      return await apiClient.delete(`/users/me/cart/items/${itemId}`)
    } catch(error)
    {
      console.error(`Error increasing item count:`, error);
      throw error;
    }
  }

  // Delete category
  async deleteCart() {
    try {
      return await apiClient.delete('/users/me/cart');
    } catch (error) {
      console.error(`Error deleting cart:`, error);
      throw error;
    }
  }

  async checkout() {
    try {
        return await apiClient.post('/users/me/cart/checkout');
    } catch(error) {
        console.error(`Error initiating payment:`, error);
        throw error;
    }
  }
}
export default new CartService();
