import apiClient from './api.service';

class OrderService {
  // Get all orders with optional filtering and pagination
  async getAll(params = {}) {
    return apiClient.get('/orders', { params });
  }

  // Get order by ID
  async getById(id) {
    return apiClient.get(`/orders/${id}`);
  }

  // Create new order
  async create(orderData) {
    return apiClient.post('/orders', orderData);
  }

  // Update existing order
  async update(id, orderData) {
    return apiClient.put(`/orders/${id}`, orderData);
  }

  // Delete order
  async delete(id) {
    return apiClient.delete(`/orders/${id}`);
  }

  // Update order status
  async updateStatus(id, status) {
    return apiClient.patch(`/orders/${id}/status`, { status });
  }

  // Get order statistics
  async getStats() {
    return apiClient.get('/orders/stats');
  }

  // Admin methods
  async getAllAdmin(params = {}) {
    return apiClient.get('/admin/orders', { params });
  }

  async getByIdAdmin(id) {
    return apiClient.get(`/admin/orders/${id}`);
  }

  async getOrderItems(id, params = {}) {
    return apiClient.get(`/admin/orders/${id}/items`, { params });
  }

  async updateOrderStatus(id, status) {
    return apiClient.patch(`/admin/orders/${id}/status`, { status });
  }

  async updatePaymentStatus(id, paymentStatus) {
    return apiClient.patch(`/admin/orders/${id}/payment-status`, { paymentStatus });
  }

  async addOrderNote(id, note) {
    return apiClient.post(`/admin/orders/${id}/notes`, { note });
  }

  async getOrderNotes(id) {
    return apiClient.get(`/admin/orders/${id}/notes`);
  }

  async deleteAdmin(id) {
    return apiClient.delete(`/admin/orders/${id}`);
  }
}

export default new OrderService();
