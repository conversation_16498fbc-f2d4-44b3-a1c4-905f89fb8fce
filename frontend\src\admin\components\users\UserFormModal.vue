<template>
  <div class="admin-modal" :class="{ 'admin-modal-active': isOpen }">
    <div class="admin-modal-background" @click="$emit('close')"></div>
    <div class="admin-modal-content">
      <div class="admin-modal-header">
        <h3 class="admin-modal-title">{{ user ? 'Edit User' : 'Add User' }}</h3>
        <button class="admin-modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="admin-modal-body">
        <form @submit.prevent="submitForm" class="admin-user-form">
          <!-- Basic Info -->
          <div class="admin-form-row">
            <div class="admin-form-group">
              <label class="admin-form-label">First Name</label>
              <input
                class="admin-form-input"
                type="text"
                placeholder="First name"
                v-model="form.firstName"
                required>
            </div>
            <div class="admin-form-group">
              <label class="admin-form-label">Last Name</label>
              <input
                class="admin-form-input"
                type="text"
                placeholder="Last name"
                v-model="form.lastName"
                required>
            </div>
          </div>

          <div class="admin-form-group">
            <label class="admin-form-label">Email</label>
            <input
              class="admin-form-input"
              type="email"
              placeholder="Email address"
              v-model="form.email"
              required>
          </div>

          <div class="admin-form-group">
            <label class="admin-form-label">Username</label>
            <input
              class="admin-form-input"
              type="text"
              placeholder="Username"
              v-model="form.username"
              required>
          </div>

          <!-- Password (only for new users) -->
          <div class="admin-form-group" v-if="!user">
            <label class="admin-form-label">Password</label>
            <input
              class="admin-form-input"
              type="password"
              placeholder="Password"
              v-model="form.password"
              required>
          </div>

          <!-- Role and Status -->
          <div class="admin-form-row">
            <div class="admin-form-group">
              <label class="admin-form-label">Role</label>
              <select class="admin-form-select" v-model="form.role" required>
                <option value="Admin">Admin</option>
                <option value="Seller">Seller</option>
                <option value="Customer">Customer</option>
              </select>
            </div>
            <div class="admin-form-group">
              <label class="admin-form-label">Status</label>
              <select class="admin-form-select" v-model="form.status" required>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
                <option value="banned">Banned</option>
              </select>
            </div>
          </div>

          <!-- Phone and Address -->
          <div class="admin-form-group">
            <label class="admin-form-label">Phone</label>
            <input
              class="admin-form-input"
              type="tel"
              placeholder="Phone number"
              v-model="form.phone">
          </div>

          <div class="admin-form-group">
            <label class="admin-form-label">Address</label>
            <textarea
              class="admin-form-textarea"
              placeholder="Address"
              v-model="form.address"
              rows="2"></textarea>
          </div>
        </form>
      </div>
      <div class="admin-modal-footer">
        <button class="admin-btn admin-btn-primary" @click="submitForm" :disabled="isSubmitting">
          <span v-if="isSubmitting">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Saving...</span>
          </span>
          <span v-else>Save</span>
        </button>
        <button class="admin-btn admin-btn-secondary" @click="$emit('close')">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  user: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'save']);

// Form state
const form = reactive({
  id: null,
  firstName: '',
  lastName: '',
  email: '',
  username: '',
  password: '',
  role: 'Customer',
  status: 'active',
  phone: '',
  address: ''
});

// Submission state
const isSubmitting = ref(false);

// Submit form
const submitForm = async () => {
  isSubmitting.value = true;
  
  try {
    // Create a clean form object
    const userData = { ...form };
    
    // Remove password if editing user
    if (props.user && !userData.password) {
      delete userData.password;
    }
    
    emit('save', userData);
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form
const resetForm = () => {
  form.id = null;
  form.firstName = '';
  form.lastName = '';
  form.email = '';
  form.username = '';
  form.password = '';
  form.role = 'Customer';
  form.status = 'active';
  form.phone = '';
  form.address = '';
};

// Watch for user changes to update form
watch(() => props.user, (newUser) => {
  if (newUser) {
    // Populate form with user data
    Object.keys(form).forEach(key => {
      if (key in newUser && key !== 'password') {
        form[key] = newUser[key];
      }
    });
  } else {
    resetForm();
  }
}, { immediate: true });

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>

<style scoped>
.admin-user-form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--admin-space-lg);
}

@media (max-width: 768px) {
  .admin-form-row {
    grid-template-columns: 1fr;
  }
}
</style>
