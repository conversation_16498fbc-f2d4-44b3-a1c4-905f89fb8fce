/* Admin Common Styles */
@import './admin-design-system.css';

/* Page container */
.admin-page-container {
  width: 100%;
  max-width: 100%;
  padding: 0;
}

/* Page header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Box styles */
.box {
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Table container */
.table-container {
  width: 100%;
  overflow-x: auto;
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: transparent;
}

.table th {
  font-weight: 600;
  color: #1a1a1a;
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem;
  font-size: 0.875rem;
  text-align: left;
  position: sticky;
  top: 0;
  background-color: var(--card-bg);
  z-index: 1;
}

.table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  color: #1a1a1a;
  font-size: 0.875rem;
  font-weight: 500;
}

.table tbody tr:hover {
  background-color: rgba(50, 115, 220, 0.1);
}

/* Form controls */
.input, .select select, .textarea {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.input:hover, .select select:hover, .textarea:hover {
  border-color: var(--accent-color);
}

.input:focus, .select select:focus, .textarea:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Button styles */
.button {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.button:hover {
  background-color: var(--darker-bg);
  transform: translateY(-1px);
}

.button.is-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.button.is-primary:hover {
  background-color: var(--accent-color-dark);
  border-color: var(--accent-color-dark);
}

.button.is-info {
  background-color: #3e8ed0;
  border-color: #3e8ed0;
  color: white;
}

.button.is-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.button.is-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.button.is-small {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
}

/* Tag styles */
.tag {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

.tag.is-success {
  background-color: var(--success-color);
  color: white;
}

.tag.is-warning {
  background-color: var(--warning-color);
  color: white;
}

.tag.is-danger {
  background-color: var(--danger-color);
  color: white;
}

.tag.is-info {
  background-color: #3e8ed0;
  color: white;
}

.tag.is-primary {
  background-color: var(--accent-color);
  color: white;
}

/* Modal styles */
.modal-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  overflow: hidden;
  max-width: 500px;
}

.modal-card-head {
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.modal-card-title {
  color: var(--text-primary);
}

.modal-card-body {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.modal-card-foot {
  background-color: var(--card-bg);
  border-top: 1px solid var(--border-color);
}

/* Pagination */
.pagination {
  margin-top: 1.5rem;
}

.pagination-link {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.pagination-link.is-current {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.pagination-ellipsis {
  color: var(--text-secondary);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-header .level-right {
    margin-top: 1rem;
    align-self: flex-end;
  }
}
