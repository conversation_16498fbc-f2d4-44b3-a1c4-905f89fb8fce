<template>
  <div class="category-select">
    <label class="label">{{ label }} <span v-if="required" class="has-text-danger">*</span></label>
    <div class="control">
      <div class="dropdown" :class="{ 'is-active': isDropdownOpen }">
        <div class="dropdown-trigger">
          <div class="field has-addons">
            <div class="control is-expanded">
              <input
                class="input"
                type="text"
                :placeholder="placeholder"
                v-model="searchQuery"
                @input="onSearchInput"
                @focus="openDropdown"
                @blur="onBlur"
                :readonly="readonly"
              />
            </div>
            <div class="control">
              <button
                class="button"
                type="button"
                @click="toggleDropdown"
                :disabled="readonly"
              >
                <span class="icon">
                  <i class="fas fa-chevron-down" :class="{ 'fa-rotate-180': isDropdownOpen }"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
        <div class="dropdown-menu" role="menu">
          <div class="dropdown-content">
            <div v-if="loading" class="dropdown-item">
              <div class="has-text-centered">
                <span class="icon">
                  <i class="fas fa-spinner fa-spin"></i>
                </span>
                Loading categories...
              </div>
            </div>
            <div v-else-if="filteredCategories.length === 0" class="dropdown-item">
              <div class="has-text-grey has-text-centered">
                No categories found
              </div>
            </div>
            <template v-else>
              <a
                v-for="category in filteredCategories"
                :key="category.id"
                class="dropdown-item"
                :class="{ 'is-active': selectedCategory?.id === category.id }"
                @mousedown.prevent="selectCategory(category)"
              >
                <div class="category-item">
                  <div class="category-name">{{ category.name }}</div>
                  <div class="category-slug has-text-grey is-size-7">{{ category.slug }}</div>
                </div>
              </a>
            </template>
          </div>
        </div>
      </div>
    </div>
    <p v-if="selectedCategory" class="help">
      Selected: {{ selectedCategory.name }}
    </p>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { productsService } from '@/admin/services/products';

// Props
const props = defineProps({
  modelValue: {
    type: [String, null],
    default: null
  },
  label: {
    type: String,
    default: 'Category'
  },
  placeholder: {
    type: String,
    default: 'Search and select category...'
  },
  required: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change']);

// Reactive data
const categories = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const isDropdownOpen = ref(false);
const selectedCategory = ref(null);

// Computed
const filteredCategories = computed(() => {
  // Defensive programming: ensure categories.value is always an array
  const categoriesArray = Array.isArray(categories.value) ? categories.value : [];

  console.log('🏷️ Filtering categories:', {
    totalCategories: categoriesArray.length,
    searchQuery: searchQuery.value,
    isDropdownOpen: isDropdownOpen.value
  });

  // If no search query or dropdown just opened, show all categories
  if (!searchQuery.value.trim() || searchQuery.value === (selectedCategory.value?.name || '')) {
    console.log('📋 Showing all categories:', categoriesArray.length);
    return categoriesArray; // Show all categories without limit
  }

  const query = searchQuery.value.toLowerCase().trim();
  const filtered = categoriesArray.filter(category => {
    // Additional safety check for category object
    if (!category || typeof category !== 'object') return false;

    return (
      (category.name && category.name.toLowerCase().includes(query)) ||
      (category.slug && category.slug.toLowerCase().includes(query))
    );
  }).slice(0, 50); // Limit to 50 items

  console.log('🎯 Filtered categories:', filtered.length);
  return filtered;
});

// Methods
const fetchCategories = async () => {
  try {
    loading.value = true;
    console.log('🏷️ Fetching categories...');

    const categoriesData = await productsService.getCategories({ pageSize: 1000 });
    console.log('📦 Categories response:', categoriesData);

    // Ensure we always have an array
    categories.value = Array.isArray(categoriesData) ? categoriesData : [];
    console.log('✅ Categories loaded:', categories.value.length);

    // If we have a modelValue, find and set the selected category
    if (props.modelValue && categories.value.length > 0) {
      const found = categories.value.find(cat => cat && cat.id === props.modelValue);
      if (found) {
        selectedCategory.value = found;
        searchQuery.value = found.name || '';
        console.log('🎯 Selected category found:', found.name);
      } else {
        console.log('❌ Category not found for ID:', props.modelValue);
      }
    }
  } catch (error) {
    console.error('❌ Error fetching categories:', error);
    categories.value = [];
  } finally {
    loading.value = false;
  }
};

const selectCategory = (category) => {
  // Defensive programming: ensure category object is valid
  if (!category || typeof category !== 'object' || !category.id) {
    console.error('❌ Invalid category object:', category);
    return;
  }

  selectedCategory.value = category;
  searchQuery.value = category.name || '';
  isDropdownOpen.value = false;

  console.log('✅ Category selected:', category.name);
  emit('update:modelValue', category.id);
  emit('change', category);
};

const openDropdown = () => {
  if (!props.readonly) {
    isDropdownOpen.value = true;
    console.log('📂 Category dropdown opened');
  }
};

const closeDropdown = () => {
  isDropdownOpen.value = false;
  console.log('📁 Category dropdown closed');
};

const toggleDropdown = () => {
  if (props.readonly) return;

  if (!isDropdownOpen.value) {
    // When opening dropdown, clear search if it matches selected category
    if (selectedCategory.value && searchQuery.value === selectedCategory.value.name) {
      searchQuery.value = '';
    }
    openDropdown();
  } else {
    closeDropdown();
  }
};

const onSearchInput = () => {
  if (!isDropdownOpen.value) {
    isDropdownOpen.value = true;
    console.log('📂 Category dropdown opened via search input');
  }
};

const onBlur = () => {
  // Delay closing to allow click on dropdown items
  setTimeout(() => {
    closeDropdown();
    
    // If no category is selected, clear the search
    if (!selectedCategory.value) {
      searchQuery.value = '';
    } else {
      // Restore the selected category name
      searchQuery.value = selectedCategory.value.name;
    }
  }, 200);
};

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  // Defensive programming: ensure categories.value is an array
  const categoriesArray = Array.isArray(categories.value) ? categories.value : [];

  if (newValue && categoriesArray.length > 0) {
    const found = categoriesArray.find(cat => cat && cat.id === newValue);
    if (found) {
      selectedCategory.value = found;
      searchQuery.value = found.name || '';
      console.log('🔄 Category updated via watch:', found.name);
    } else {
      console.log('🔍 Category not found in watch for ID:', newValue);
    }
  } else if (!newValue) {
    selectedCategory.value = null;
    searchQuery.value = '';
    console.log('🧹 Cleared category selection');
  }
});

// Lifecycle
onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
.category-select {
  position: relative;
}

.dropdown {
  width: 100%;
}

.dropdown-menu {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.category-item {
  padding: 0.25rem 0;
}

.category-name {
  font-weight: 500;
}

.category-slug {
  margin-top: 0.125rem;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item.is-active {
  background-color: #3273dc;
  color: white;
}

.dropdown-item.is-active .category-slug {
  color: #e8e8e8;
}

.fa-rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}
</style>
