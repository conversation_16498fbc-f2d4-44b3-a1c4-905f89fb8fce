/* Toast Notification Fixes
   This file contains fixes for toast notification visibility and styling issues.
*/

/* ===== Toast Container Fixes ===== */

/* Fix toast container positioning */
.toast-container {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important; /* Position in top-right corner */
  left: auto !important; /* Override any left positioning */
  z-index: 10000 !important; /* Ensure it's above everything */
  display: flex !important;
  flex-direction: column !important;
  gap: 10px !important;
  max-width: 350px !important;
  pointer-events: none !important; /* Allow clicking through container */
}

/* Individual toast styling */
.toast-message,
.toast {
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background-color: white !important;
  color: #333 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-bottom: 10px !important;
  min-width: 250px !important;
  max-width: 350px !important;
  pointer-events: auto !important; /* Make toast clickable */
  opacity: 1 !important; /* Ensure visibility */
  transform: translateX(0) !important; /* Reset any transforms */
  transition: all 0.3s ease !important;
}

/* Toast icon styling */
.toast-icon {
  margin-right: 12px !important;
  font-size: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Toast text styling */
.toast-text,
.toast-message {
  flex: 1 !important;
  color: #333 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Toast close button styling */
.toast-close {
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  color: #777 !important;
  padding: 0 !important;
  margin-left: 12px !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.toast-close:hover {
  color: #333 !important;
}

/* Toast types styling */
.toast-success,
.toast-message.toast-success {
  background-color: #ebf7ed !important;
  border-left: 4px solid #48c774 !important;
}

.toast-success .toast-icon {
  color: #48c774 !important;
}

.toast-error,
.toast-message.toast-error {
  background-color: #feecf0 !important;
  border-left: 4px solid #F27318E3 !important;
}

.toast-error .toast-icon {
  color: #F27318E3 !important;
}

.toast-warning,
.toast-message.toast-warning {
  background-color: #fff8e6 !important;
  border-left: 4px solid #ffdd57 !important;
}

.toast-warning .toast-icon {
  color: #ffa500 !important;
}

.toast-info,
.toast-message.toast-info {
  background-color: #eef6fc !important;
  border-left: 4px solid #3e8ed0 !important;
}

.toast-info .toast-icon {
  color: #3e8ed0 !important;
}

/* Toast animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease !important;
}

.toast-enter-from,
.toast-leave-to {
  opacity: 0 !important;
  transform: translateX(100%) !important;
}

/* Ensure toast is visible in all contexts */
body > .toast-container {
  z-index: 10000 !important;
}

/* Fix for toast in modals */
.modal .toast-container {
  z-index: 10001 !important; /* Above modal z-index */
}

/* Fix for toast in dropdowns */
.dropdown .toast-container {
  z-index: 10002 !important; /* Above dropdown z-index */
}

/* Fix for toast in notifications */
.notification .toast-container {
  z-index: 10003 !important; /* Above notification z-index */
}

/* Ensure toast is not clipped by overflow: hidden */
body {
  overflow-x: hidden !important;
}

/* Fix for toast in fixed position elements */
.is-fixed-top .toast-container,
.is-fixed-bottom .toast-container,
.is-fixed-left .toast-container,
.is-fixed-right .toast-container {
  z-index: 10004 !important; /* Above fixed elements */
}
