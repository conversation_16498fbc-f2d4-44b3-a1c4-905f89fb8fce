# Універсальні компоненти для управління зображеннями

Цей набір компонентів забезпечує універсальний підхід до роботи з зображеннями в адмін-панелі для всіх типів сутностей (Users, Products, Categories, Companies).

## Компоненти

### 1. EntityImageManager.vue

Універсальний компонент для управління одиночним зображенням (аватар, логотип, основне зображення).

#### Використання

```vue
<template>
  <EntityImageManager
    entity-type="user"
    :entity-id="userId"
    :current-image="user.avatarUrl"
    image-alt="Аватар користувача"
    :local-mode="true"
    @image-uploaded="handleImageUploaded"
    @image-removed="handleImageRemoved"
    @image-changed="handleImageChanged"
  />
</template>
```

#### Props

- `entity-type` (String, required) - Тип сутності: 'user', 'product', 'category', 'company'
- `entity-id` (String) - ID сутності (не потрібен в local-mode)
- `current-image` (String) - URL поточного зображення
- `image-alt` (String) - Alt текст для зображення
- `max-size` (Number) - Максимальний розмір файлу в байтах (за замовчуванням 5MB)
- `allowed-types` (Array) - Дозволені типи файлів
- `local-mode` (Boolean) - Локальний режим (зберігає зміни до підтвердження)

#### Events

- `image-uploaded` - Зображення завантажено на сервер
- `image-removed` - Зображення видалено
- `image-changed` - Зміна зображення в локальному режимі

#### Методи

- `uploadPendingFile()` - Завантажити файл на сервер (для local-mode)
- `resetLocalChanges()` - Скинути локальні зміни
- `processPendingOperations()` - Обробити всі відкладені операції (завантаження/видалення)
- `getChangeState()` - Отримати поточний стан змін
- `hasChanges` (computed) - Чи є незбережені зміни

### 2. EntityMetaImageManager.vue

Компонент для управління мета-зображеннями (для соціальних мереж).

#### Використання

```vue
<template>
  <EntityMetaImageManager
    entity-type="product"
    :entity-id="productId"
    :current-image="product.metaImage"
    :social-preview-title="product.name"
    :social-preview-description="product.description"
    :social-preview-url="`https://example.com/products/${product.id}`"
    :local-mode="true"
    @meta-image-uploaded="handleMetaImageUploaded"
    @meta-image-removed="handleMetaImageRemoved"
    @meta-image-changed="handleMetaImageChanged"
  />
</template>
```

#### Props

- `entity-type` (String, required) - Тип сутності: 'product', 'category', 'company'
- `entity-id` (String) - ID сутності
- `current-image` (String) - URL поточного мета-зображення
- `social-preview-title` (String) - Заголовок для превью
- `social-preview-description` (String) - Опис для превью
- `social-preview-url` (String) - URL для превью
- `local-mode` (Boolean) - Локальний режим

#### Events

- `meta-image-uploaded` - Мета-зображення завантажено
- `meta-image-removed` - Мета-зображення видалено
- `meta-image-changed` - Зміна мета-зображення в локальному режимі

### 3. UploadProgress.vue

Компонент для відображення прогресу завантаження зображень.

#### Використання

```vue
<template>
  <UploadProgress
    :uploads="pendingUploads"
    :show-overall-progress="true"
    @retry-upload="retryUpload"
    @cancel-upload="cancelUpload"
    @retry-all-failed="retryAllFailed"
    @cancel-all="cancelAllUploads"
    @clear-completed="clearCompleted"
  />
</template>
```

#### Props

- `uploads` (Array) - Масив об'єктів завантаження
- `show-overall-progress` (Boolean) - Показувати загальний прогрес

#### Структура об'єкта завантаження

```javascript
{
  id: 'unique-id',
  filename: 'image.jpg',
  size: 1024000,
  type: 'image/jpeg',
  status: 'uploading', // 'pending', 'uploading', 'completed', 'error', 'cancelled'
  progress: 50, // 0-100
  speed: 1024000, // bytes per second
  timeRemaining: 30, // seconds
  error: 'Error message',
  retryable: true
}
```

## Сервіси

### ImageService

Універсальний сервіс для роботи з API зображень.

#### Методи

```javascript
// Завантаження зображення
await imageService.uploadImage(entityType, entityId, file, onProgress);

// Завантаження мета-зображення
await imageService.uploadMetaImage(entityType, entityId, file, onProgress);

// Отримання зображень (тільки для продуктів)
await imageService.getImages(entityType, entityId);

// Видалення зображення
await imageService.deleteImage(imageId);

// Видалення мета-зображення
await imageService.deleteMetaImage(entityType, entityId);

// Встановлення головного зображення (тільки для продуктів)
await imageService.setMainImage(entityType, entityId, imageId);

// Валідація файлу
const validation = imageService.validateImageFile(file, options);

// Створення превью
const previewUrl = await imageService.createImagePreview(file);
```

## Інтеграція в форми

### Локальний режим (рекомендований)

Для форм створення та редагування рекомендується використовувати локальний режим:

1. Встановіть `local-mode="true"` для компонентів
2. Обробляйте події `*-changed` для збереження локальних змін
3. При збереженні форми викликайте методи завантаження

```vue
<script>
export default {
  data() {
    return {
      pendingImageChanges: {},
      pendingUploads: []
    };
  },
  methods: {
    async handleSubmit() {
      // Спочатку зберігаємо основні дані
      const entity = await this.saveEntity();
      
      // Потім завантажуємо зображення
      await this.uploadPendingImages(entity.id);
    },
    
    async uploadPendingImages(entityId) {
      const uploads = [];
      
      // Завантажуємо основне зображення
      if (this.pendingImageChanges.main) {
        uploads.push(this.uploadMainImage(entityId));
      }
      
      // Завантажуємо мета-зображення
      if (this.pendingImageChanges.meta) {
        uploads.push(this.uploadMetaImage(entityId));
      }
      
      await Promise.all(uploads);
    }
  }
};
</script>
```

### Негайний режим

Для швидкого редагування можна використовувати негайний режим:

1. Встановіть `local-mode="false"`
2. Передайте `entity-id`
3. Зображення будуть завантажуватися одразу після вибору

## Стилізація

Всі компоненти використовують Bootstrap 5 класи та можуть бути легко кастомізовані через CSS змінні або перевизначення стилів.

## Обробка помилок

Компоненти автоматично обробляють помилки завантаження та відображають їх користувачу. Для додаткової обробки використовуйте події компонентів.

## Доступність

Компоненти підтримують:
- Навігацію з клавіатури
- Screen readers
- ARIA атрибути
- Семантичну розмітку
