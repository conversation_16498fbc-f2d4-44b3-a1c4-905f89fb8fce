// Google OAuth configuration
export const GOOGLE_CLIENT_ID = '************-4uhuqn1880u16qdgui9vn3chcon5d50t.apps.googleusercontent.com';

// Google OAuth initialization function with improved error handling and script loading
export function initGoogleAuth() {
  return new Promise((resolve, reject) => {
    // Check if the script is already loaded
    if (window.google && window.google.accounts) {
      console.log('Google Identity Services already loaded');
      resolve(window.google.accounts);
      return;
    }

    // Load the Google API script
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;

    // Handle successful script load
    script.onload = () => {
      console.log('Google Identity Services script loaded successfully');

      // Ensure the Google API is available
      if (window.google && window.google.accounts) {
        resolve(window.google.accounts);
      } else {
        console.error('Google Identity Services loaded but API not available');
        reject(new Error('Google Identity Services API not available'));
      }
    };

    // Handle script load error
    script.onerror = (error) => {
      console.error('Failed to load Google Identity Services script', error);
      reject(new Error('Failed to load Google Identity Services script'));
    };

    // Add the script to the document
    document.head.appendChild(script);
  });
}

// Initialize Google Sign-In with the provided callback
export function initializeGoogleSignIn(callback) {
  return new Promise(async (resolve, reject) => {
    try {
      // Initialize Google Auth
      const googleAccounts = await initGoogleAuth();

      // Initialize Google Sign-In
      googleAccounts.id.initialize({
        client_id: GOOGLE_CLIENT_ID,
        callback: callback,
        auto_select: false,
        cancel_on_tap_outside: true,
        context: 'signin'
      });

      console.log('Google Sign-In initialized successfully');
      resolve(googleAccounts);
    } catch (error) {
      console.error('Failed to initialize Google Sign-In:', error);
      reject(error);
    }
  });
}

// Trigger Google Sign-In prompt
export function promptGoogleSignIn(timeoutMs = 30000) {
  return new Promise((resolve, reject) => {
    if (!window.google || !window.google.accounts || !window.google.accounts.id) {
      reject(new Error('Google Identity Services not loaded'));
      return;
    }

    try {
      // Set a timeout to prevent infinite waiting
      const timeoutId = setTimeout(() => {
        reject(new Error('Google Sign-In prompt timed out'));
      }, timeoutMs);

      // Prompt the user to select an account
      window.google.accounts.id.prompt((notification) => {
        clearTimeout(timeoutId);

        if (notification.isNotDisplayed()) {
          const reason = notification.getNotDisplayedReason();
          console.warn('Google Sign-In prompt not displayed:', reason);

          // Handle specific reasons
          if (reason === 'browser_not_supported') {
            reject(new Error('Your browser does not support Google Sign-In. Please try a different browser.'));
          } else if (reason === 'invalid_client') {
            reject(new Error('Invalid Google client configuration. Please contact support.'));
          } else if (reason === 'missing_client_id') {
            reject(new Error('Google client ID is missing. Please contact support.'));
          } else if (reason === 'third_party_cookies_blocked') {
            reject(new Error('Third-party cookies are blocked in your browser. Please enable them or use a different browser.'));
          } else {
            reject(new Error(`Google Sign-In not available (${reason}). Please try again later.`));
          }
        } else if (notification.isSkippedMoment()) {
          const reason = notification.getSkippedReason();
          console.warn('Google Sign-In moment skipped:', reason);
          reject(new Error('Google Sign-In was skipped. Please try again.'));
        } else if (notification.isDismissedMoment()) {
          const reason = notification.getDismissedReason();
          console.warn('Google Sign-In prompt dismissed:', reason);

          // User actively dismissed the prompt
          if (reason === 'credential_returned') {
            // This is actually a success case - credential was returned
            resolve();
          } else if (reason === 'cancel_called') {
            reject(new Error('Google Sign-In was cancelled.'));
          } else if (reason === 'user_cancel') {
            reject(new Error('Google Sign-In was cancelled by user.'));
          } else {
            reject(new Error('Google Sign-In was dismissed. Please try again.'));
          }
        } 
        else 
        {
          // This is a success case - the prompt was shown without issues
          resolve();
        }
      });
    } catch (error) {
      // Clear the timeout if there's an error
      clearTimeout(timeoutId);
      console.error('Error prompting Google Sign-In:', error);
      reject(error);
    }
    
    const timeoutId = setTimeout(() => {
      console.warn('Google Sign-In prompt timed out after', timeoutMs, 'ms');
      reject(new Error('Google Sign-In timed out. Please try again.'));
    }, timeoutMs);
  });
}
