import{_ as B,g as U,C as X,c as l,a,k as d,d as v,n as p,t as m,m as D,x as f,D as b,y as N,l as M,b as C,w as F,r as O,e as $,o as i}from"./index-L-hJxM_5.js";import{u as z}from"./users-D6yG63l9.js";import{E as L}from"./EntityImageManager-DFnfDtsC.js";import{U as T}from"./UploadProgress-DTyUnfEW.js";import{i as j}from"./image.service-DOD4lHqw.js";const G={class:"admin-page"},Z={class:"admin-page-header"},H={class:"admin-page-actions"},J=["disabled"],K={key:0,class:"admin-alert admin-alert-danger"},Q={class:"admin-user-create-content"},W={class:"admin-card"},Y={class:"admin-card-content"},ee={class:"admin-form-section"},ae={class:"admin-form-row"},se={class:"admin-form-group"},re={key:0,class:"admin-form-error"},te={class:"admin-form-group"},oe={key:0,class:"admin-form-error"},ne={class:"admin-form-row"},le={class:"admin-form-group"},ie={key:0,class:"admin-form-error"},de={class:"admin-form-group"},me={key:0,class:"admin-form-error"},ue={class:"admin-form-row"},pe={class:"admin-form-group"},fe={key:0,class:"admin-form-error"},ve={class:"admin-form-group"},ce={key:0,class:"admin-form-error"},ge={class:"admin-form-row"},be={class:"admin-form-group"},ye={key:0,class:"admin-form-error"},we={class:"admin-form-group"},Ue={key:0,class:"admin-form-error"},he={class:"admin-form-row"},ke={class:"admin-form-group"},Ne={key:0,class:"admin-form-error"},Ce={class:"admin-form-section"},Ee={class:"admin-form-row"},Xe={class:"admin-form-group admin-form-group-full"},_e={class:"admin-form-section"},xe={class:"admin-form-group"},Se={key:0,class:"admin-form-error"},Ve={class:"admin-form-actions"},qe=["disabled"],Ae={key:0},Ie={key:1},Pe={key:0,class:"mt-3"},Re={__name:"UserCreate",setup(Be){const h=$(),c=U(!1),w=U(null),g=U([]),u=U(null),r=X({email:"",username:"",password:"",role:"",birthday:"",firstName:"",lastName:"",phone:"",gender:"",language:""}),s=X({email:"",username:"",password:"",role:"",birthday:"",firstName:"",lastName:"",phone:"",gender:"",language:""}),k=()=>{Object.keys(s).forEach(n=>{s[n]=""})},_=()=>{k();let n=!0;return r.username.trim()?r.username.length<3&&(s.username="Username must be at least 3 characters",n=!1):(s.username="Username is required",n=!1),r.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)||(s.email="Please enter a valid email address",n=!1):(s.email="Email is required",n=!1),r.password.trim()?r.password.length<8?(s.password="Password must be at least 8 characters",n=!1):/[A-Z]/.test(r.password)?/[0-9]/.test(r.password)?/[!@#$%^&*]/.test(r.password)||(s.password="Password must contain at least one special character (!@#$%^&*)",n=!1):(s.password="Password must contain at least one number",n=!1):(s.password="Password must contain at least one uppercase letter",n=!1):(s.password="Password is required",n=!1),r.role||(s.role="Role is required",n=!1),n},E=async()=>{var n,e;if(_()){c.value=!0,w.value=null,k();try{const t={...r};t.birthday?t.birthday=new Date(t.birthday).toISOString():delete t.birthday,t.gender===""?delete t.gender:t.gender=parseInt(t.gender),t.language===""?delete t.language:t.language=parseInt(t.language),t.firstName||delete t.firstName,t.lastName||delete t.lastName,t.phone||delete t.phone;const o=await z.createUser(t);o&&o.id&&u.value&&await I(o.id),o&&o.id?h.push(`/admin/users/${o.id}`):h.push("/admin/users")}catch(t){if(console.error("Error creating user:",t),t.response&&t.response.data&&t.response.data.errors){const o=t.response.data.errors;Object.keys(o).forEach(y=>{y.toLowerCase()in s&&(s[y.toLowerCase()]=o[y][0]||o[y])})}else w.value=((e=(n=t.response)==null?void 0:n.data)==null?void 0:e.message)||"Failed to create user. Please try again."}finally{c.value=!1}}},x=()=>{h.push("/admin/users")},S=()=>{k(),u.value=null,g.value=[],r.email="",r.username="",r.password="",r.role="",r.birthday="",r.firstName="",r.lastName="",r.phone="",r.gender="",r.language=""},V=n=>{console.log("Avatar uploaded:",n)},q=()=>{u.value=null},A=n=>{u.value=n,console.log("Avatar changed (local):",n)},I=async n=>{if(!u.value||!n)return;const t={id:Date.now().toString(),filename:u.value.file.name,size:u.value.file.size,type:u.value.file.type,status:"uploading",progress:0,retryable:!0};g.value.push(t);try{const o=await j.uploadImage("user",n,u.value.file,y=>{t.progress=y});t.status="completed",u.value=null,console.log("Avatar uploaded successfully:",o.data)}catch(o){throw t.status="error",t.error=o.message,console.error("Error uploading avatar:",o),o}},P=async n=>{const e=g.value.find(t=>t.id===n);if(e&&u.value){e.status="uploading",e.progress=0,e.error=null;try{console.log("Retry upload not available in create mode")}catch(t){e.status="error",e.error=t.message}}},R=n=>{const e=g.value.findIndex(t=>t.id===n);e!==-1&&g.value.splice(e,1)};return(n,e)=>{const t=O("router-link");return i(),l("div",G,[a("div",Z,[a("div",{class:"admin-page-title-section"},[a("button",{onClick:x,class:"admin-btn admin-btn-ghost admin-btn-sm"},e[11]||(e[11]=[a("i",{class:"fas fa-arrow-left"},null,-1),v(" Back to Users ")])),e[12]||(e[12]=a("h1",{class:"admin-page-title"},[a("i",{class:"fas fa-user-plus admin-page-icon"}),v(" Create New User ")],-1)),e[13]||(e[13]=a("p",{class:"admin-page-subtitle"},"Create a new user account with role and permissions",-1))]),a("div",H,[a("button",{onClick:S,class:"admin-btn admin-btn-secondary"},e[14]||(e[14]=[a("i",{class:"fas fa-undo"},null,-1),v(" Reset ")])),a("button",{onClick:E,disabled:c.value,class:"admin-btn admin-btn-primary"},[a("i",{class:p(["fas fa-save",{"fa-spinner fa-pulse":c.value}])},null,2),v(" "+m(c.value?"Creating...":"Create User"),1)],8,J)])]),w.value?(i(),l("div",K,[e[17]||(e[17]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),a("div",null,[e[15]||(e[15]=a("strong",null,"Error creating user",-1)),a("p",null,m(w.value),1)]),a("button",{onClick:e[0]||(e[0]=o=>w.value=null),class:"admin-btn admin-btn-sm admin-btn-danger"},e[16]||(e[16]=[a("i",{class:"fas fa-times"},null,-1),v(" Dismiss ")]))])):d("",!0),a("div",Q,[a("div",W,[e[42]||(e[42]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-user-circle"}),v(" User Information ")])],-1)),a("div",Y,[a("form",{onSubmit:D(E,["prevent"]),class:"admin-user-form"},[a("div",ee,[e[32]||(e[32]=a("h4",{class:"admin-form-section-title"},"Basic Information",-1)),a("div",ae,[a("div",se,[e[18]||(e[18]=a("label",{class:"admin-form-label admin-required"}," Username * ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.username}]),type:"text",placeholder:"Enter username","onUpdate:modelValue":e[1]||(e[1]=o=>r.username=o),required:""},null,2),[[b,r.username]]),s.username?(i(),l("div",re,m(s.username),1)):d("",!0)]),a("div",te,[e[19]||(e[19]=a("label",{class:"admin-form-label admin-required"}," Email * ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.email}]),type:"email",placeholder:"Enter email address","onUpdate:modelValue":e[2]||(e[2]=o=>r.email=o),required:""},null,2),[[b,r.email]]),s.email?(i(),l("div",oe,m(s.email),1)):d("",!0)])]),a("div",ne,[a("div",le,[e[21]||(e[21]=a("label",{class:"admin-form-label admin-required"}," Role * ",-1)),f(a("select",{class:p(["admin-form-select",{"admin-form-input-error":s.role}]),"onUpdate:modelValue":e[3]||(e[3]=o=>r.role=o),required:""},e[20]||(e[20]=[M('<option value="" data-v-c26a0776>Select role</option><option value="Buyer" data-v-c26a0776>Buyer</option><option value="Seller" data-v-c26a0776>Seller</option><option value="SellerOwner" data-v-c26a0776>Seller Owner</option><option value="Moderator" data-v-c26a0776>Moderator</option><option value="Admin" data-v-c26a0776>Admin</option>',6)]),2),[[N,r.role]]),s.role?(i(),l("div",ie,m(s.role),1)):d("",!0)]),a("div",de,[e[22]||(e[22]=a("label",{class:"admin-form-label"}," Birthday ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.birthday}]),type:"date","onUpdate:modelValue":e[4]||(e[4]=o=>r.birthday=o)},null,2),[[b,r.birthday]]),e[23]||(e[23]=a("div",{class:"admin-form-hint"},"Optional - Date of birth",-1)),s.birthday?(i(),l("div",me,m(s.birthday),1)):d("",!0)])]),a("div",ue,[a("div",pe,[e[24]||(e[24]=a("label",{class:"admin-form-label"}," First Name ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.firstName}]),type:"text",placeholder:"Enter first name","onUpdate:modelValue":e[5]||(e[5]=o=>r.firstName=o)},null,2),[[b,r.firstName]]),s.firstName?(i(),l("div",fe,m(s.firstName),1)):d("",!0)]),a("div",ve,[e[25]||(e[25]=a("label",{class:"admin-form-label"}," Last Name ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.lastName}]),type:"text",placeholder:"Enter last name","onUpdate:modelValue":e[6]||(e[6]=o=>r.lastName=o)},null,2),[[b,r.lastName]]),s.lastName?(i(),l("div",ce,m(s.lastName),1)):d("",!0)])]),a("div",ge,[a("div",be,[e[26]||(e[26]=a("label",{class:"admin-form-label"}," Phone ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.phone}]),type:"tel",placeholder:"+380XXXXXXXXX","onUpdate:modelValue":e[7]||(e[7]=o=>r.phone=o)},null,2),[[b,r.phone]]),e[27]||(e[27]=a("div",{class:"admin-form-hint"},"Format: +380XXXXXXXXX",-1)),s.phone?(i(),l("div",ye,m(s.phone),1)):d("",!0)]),a("div",we,[e[29]||(e[29]=a("label",{class:"admin-form-label"}," Gender ",-1)),f(a("select",{class:p(["admin-form-select",{"admin-form-input-error":s.gender}]),"onUpdate:modelValue":e[8]||(e[8]=o=>r.gender=o)},e[28]||(e[28]=[a("option",{value:""},"Select gender",-1),a("option",{value:"0"},"Male",-1),a("option",{value:"1"},"Female",-1)]),2),[[N,r.gender]]),s.gender?(i(),l("div",Ue,m(s.gender),1)):d("",!0)])]),a("div",he,[a("div",ke,[e[31]||(e[31]=a("label",{class:"admin-form-label"}," Language ",-1)),f(a("select",{class:p(["admin-form-select",{"admin-form-input-error":s.language}]),"onUpdate:modelValue":e[9]||(e[9]=o=>r.language=o)},e[30]||(e[30]=[a("option",{value:""},"Select language",-1),a("option",{value:"0"},"Ukrainian",-1),a("option",{value:"1"},"English",-1),a("option",{value:"2"},"Russian",-1)]),2),[[N,r.language]]),s.language?(i(),l("div",Ne,m(s.language),1)):d("",!0)])])]),a("div",Ce,[e[34]||(e[34]=a("h4",{class:"admin-form-section-title"},[a("i",{class:"fas fa-user-circle me-2"}),v(" User Avatar ")],-1)),a("div",Ee,[a("div",Xe,[C(L,{"entity-type":"user","entity-id":null,"current-image":null,"image-alt":"User Avatar","local-mode":!0,onImageUploaded:V,onImageRemoved:q,onImageChanged:A,ref:"avatarManager"},null,512),e[33]||(e[33]=a("div",{class:"admin-form-hint"}," Upload a profile picture for this user. Recommended size: 200x200 pixels. The avatar will be uploaded after the user is created. ",-1))])])]),a("div",_e,[e[37]||(e[37]=a("h4",{class:"admin-form-section-title"},"Password",-1)),a("div",xe,[e[35]||(e[35]=a("label",{class:"admin-form-label admin-required"}," Password * ",-1)),f(a("input",{class:p(["admin-form-input",{"admin-form-input-error":s.password}]),type:"password",placeholder:"Enter password","onUpdate:modelValue":e[10]||(e[10]=o=>r.password=o),required:""},null,2),[[b,r.password]]),e[36]||(e[36]=a("div",{class:"admin-form-hint"}," Password must be at least 8 characters with uppercase letter, number and special character (!@#$%^&*) ",-1)),s.password?(i(),l("div",Se,m(s.password),1)):d("",!0)])]),e[41]||(e[41]=a("div",{class:"admin-form-legend"},[a("p",{class:"admin-form-legend-text"},[a("span",{class:"admin-required-indicator"},"*"),v(" - обов'язкові поля ")])],-1)),a("div",Ve,[a("button",{type:"submit",class:"admin-btn admin-btn-primary",disabled:c.value},[c.value?(i(),l("span",Ae,e[38]||(e[38]=[a("i",{class:"fas fa-spinner fa-spin"},null,-1),a("span",null,"Creating...",-1)]))):(i(),l("span",Ie,e[39]||(e[39]=[a("i",{class:"fas fa-save"},null,-1),a("span",null,"Create User",-1)])))],8,qe),C(t,{to:"/admin/users",class:"admin-btn admin-btn-secondary"},{default:F(()=>e[40]||(e[40]=[a("i",{class:"fas fa-times"},null,-1),a("span",null,"Cancel",-1)])),_:1})])],32)])]),g.value.length>0?(i(),l("div",Pe,[C(T,{uploads:g.value,onRetryUpload:P,onCancelUpload:R},null,8,["uploads"])])):d("",!0)])])}}},ze=B(Re,[["__scopeId","data-v-c26a0776"]]);export{ze as default};
