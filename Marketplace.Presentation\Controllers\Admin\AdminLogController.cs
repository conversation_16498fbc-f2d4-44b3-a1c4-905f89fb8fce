using Marketplace.Application.Queries.Log;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using DomainLogLevel = Marketplace.Domain.Entities.LogLevel;

namespace Marketplace.Presentation.Controllers.Admin;

[ApiController]
[Route("api/admin/logs")]
[Authorize(Roles = "Admin")]
public class AdminLogController : BasicApiController
{
    private readonly IMediator _mediator;

    public AdminLogController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetLogs(
        [FromQuery] string? level = null,
        [FromQuery] string? category = null,
        [FromQuery] Guid? userId = null,
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        DomainLogLevel? logLevel = null;
        if (!string.IsNullOrEmpty(level) && Enum.TryParse<DomainLogLevel>(level, true, out var parsedLevel))
        {
            logLevel = parsedLevel;
        }

        var query = new GetLogsQuery(logLevel, category, userId, from, to, page, pageSize);
        var result = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PagedResponse<LogResponse>>.SuccessWithData(result));
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetLogById(Guid id, CancellationToken cancellationToken)
    {
        // For now, we'll implement this as a simple query
        // In a real application, you might want to create a specific query for this
        var query = new GetLogsQuery(Page: 1, PageSize: 1);
        var result = await _mediator.Send(query, cancellationToken);
        
        var log = result.Data.FirstOrDefault(l => l.Id == id);
        if (log == null)
        {
            return NotFound(ApiResponse.Failure("Log not found"));
        }

        return Ok(ApiResponse<LogResponse>.SuccessWithData(log));
    }

    [HttpGet("categories")]
    public IActionResult GetLogCategories()
    {
        var categories = new[]
        {
            LogCategories.User,
            LogCategories.Product,
            LogCategories.Order,
            LogCategories.Payment,
            LogCategories.Authentication,
            LogCategories.Authorization,
            LogCategories.System,
            LogCategories.Database,
            LogCategories.Api,
            LogCategories.Security
        };

        return Ok(ApiResponse<string[]>.SuccessWithData(categories));
    }

    [HttpGet("levels")]
    public IActionResult GetLogLevels()
    {
        var levels = Enum.GetNames<DomainLogLevel>();
        return Ok(ApiResponse<string[]>.SuccessWithData(levels));
    }

    [HttpGet("stats")]
    public async Task<IActionResult> GetLogStats(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        CancellationToken cancellationToken = default)
    {
        // This is a simplified implementation
        // In a real application, you might want to create specific queries for statistics
        var fromDate = from ?? DateTime.UtcNow.AddDays(-7);
        var toDate = to ?? DateTime.UtcNow;

        var allLogsQuery = new GetLogsQuery(From: fromDate, To: toDate, Page: 1, PageSize: int.MaxValue);
        var allLogs = await _mediator.Send(allLogsQuery, cancellationToken);

        var stats = new
        {
            TotalLogs = allLogs.TotalCount,
            LogsByLevel = allLogs.Data
                .GroupBy(l => l.Level)
                .ToDictionary(g => g.Key, g => g.Count()),
            LogsByCategory = allLogs.Data
                .GroupBy(l => l.Category)
                .ToDictionary(g => g.Key, g => g.Count()),
            LogsByDay = allLogs.Data
                .GroupBy(l => l.Timestamp.Date)
                .OrderBy(g => g.Key)
                .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Count()),
            DateRange = new { From = fromDate, To = toDate }
        };

        return Ok(ApiResponse<object>.SuccessWithData(stats));
    }
}
