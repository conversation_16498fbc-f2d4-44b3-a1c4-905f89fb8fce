import{Chart as f,CategoryScale as m,LinearScale as h,PointElement as v,LineElement as g,LineController as y,Title as b,<PERSON>lt<PERSON> as C,Legend as _,Filler as k}from"./chart-DFPvZH9M.js";import{_ as x,g as d,h as S,H as w,i as A,j as B,c as n,o as l,a as t,d as E}from"./index-L-hJxM_5.js";const L={class:"admin-card admin-sales-chart"},T={class:"admin-card-content"},N={key:0,class:"admin-loading-state"},R={key:1,class:"admin-empty-state"},U={key:2,class:"admin-chart-container"},W=["id"],H={__name:"SalesChart",props:{data:{type:Array,required:!0},period:{type:String,default:"month"},loading:{type:Boolean,default:!1}},emits:["period-changed"],setup(i,{emit:I}){f.register(m,h,v,g,y,b,C,_,k);const e=i,s=d(null),o=d(null),c=d(!1),p=S(()=>!e.data||!Array.isArray(e.data)||e.data.length===0?{labels:[],datasets:[]}:{labels:e.data.map(a=>a.label||"Unknown"),datasets:[{label:"Sales",data:e.data.map(a=>Number(a.value)||0),backgroundColor:"rgba(16, 185, 129, 0.1)",borderColor:"var(--admin-primary)",borderWidth:3,pointBackgroundColor:"var(--admin-primary)",pointBorderColor:"var(--admin-white)",pointBorderWidth:2,pointRadius:5,pointHoverRadius:8,tension:.4,fill:!0}]}),u=()=>{if(o.value){try{o.value.destroy()}catch(r){console.warn("Error destroying chart:",r)}o.value=null}if(!s.value){console.warn("Chart canvas not available for SalesChart");return}if(!e.data||!Array.isArray(e.data)||e.data.length===0){console.warn("Invalid or empty data for SalesChart");return}const a=s.value.getContext("2d");if(!a){console.warn("Unable to get 2D context for SalesChart");return}o.value=new f(a,{type:"line",data:p.value,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(17, 24, 39, 0.95)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"#3b82f6",borderWidth:1}},scales:{y:{beginAtZero:!0,ticks:{color:"#6b7280"},grid:{color:"#e5e7eb"}},x:{ticks:{color:"#6b7280"},grid:{display:!1}}}}})};return w(()=>e.data,a=>{c.value=!1,setTimeout(()=>{a&&a.length>0&&s.value&&u()},100)},{deep:!0}),A(()=>{setTimeout(()=>{e.data&&e.data.length>0&&s.value&&u()},100)}),B(()=>{if(o.value){try{o.value.destroy()}catch(a){console.warn("Error destroying chart on unmount:",a)}o.value=null}}),(a,r)=>(l(),n("div",L,[r[2]||(r[2]=t("div",{class:"admin-card-header"},[t("h3",{class:"admin-card-title"},[t("i",{class:"fas fa-chart-line"}),E(" Sales Overview ")])],-1)),t("div",T,[c.value?(l(),n("div",N,r[0]||(r[0]=[t("div",{class:"admin-spinner"},[t("i",{class:"fas fa-spinner fa-pulse"})],-1),t("p",{class:"admin-loading-text"},"Loading chart data...",-1)]))):!i.data||i.data.length===0?(l(),n("div",R,r[1]||(r[1]=[t("div",{class:"admin-empty-icon"},[t("i",{class:"fas fa-chart-line"})],-1),t("p",{class:"admin-empty-text"},"No sales data available for this period",-1)]))):(l(),n("div",U,[t("canvas",{ref_key:"chartCanvas",ref:s,id:`sales-chart-${Math.random().toString(36).substr(2,9)}`,height:"300"},null,8,W)]))])]))}},j=x(H,[["__scopeId","data-v-0b2a85c6"]]);export{j as default};
