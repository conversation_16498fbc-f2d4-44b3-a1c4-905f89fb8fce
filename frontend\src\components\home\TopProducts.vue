<template>
  <section class="top-products-section">
    <div class="container">
      <h2 class="section-title">{{ title }}</h2>
      <ProductGrid
        :fetch-params="fetchParams"
        :grid-columns="gridColumns"
        :empty-message="emptyMessage"
        @product-added-to-cart="$emit('product-added-to-cart', $event)"
        @product-added-to-wishlist="$emit('product-added-to-wishlist', $event)"
      />
    </div>
  </section>
</template>

<script>
import ProductGrid from '@/components/common/ProductGrid.vue';

export default {
  name: 'TopProducts',
  components: {
    ProductGrid
  },
  props: {
    title: {
      type: String,
      default: 'ТОП товари'
    },
    fetchParams: {
      type: Object,
      default: () => ({ type: 'top', status: 1, pageSize: 8 })
    },
    gridColumns: {
      type: Number,
      default: 4
    },
    emptyMessage: {
      type: String,
      default: 'Топ товари поки що недоступні'
    }
  }
}
</script>

<style scoped>
.top-products-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #000;
  text-align: left;
}
</style>