/* ===== ADMIN ALERTS SYSTEM ===== */
/* Based on Reports page alert patterns */

/* ===== BASE ALERT ===== */
.admin-alert {
  padding: var(--admin-space-lg);
  border-radius: var(--admin-radius-lg);
  border: 1px solid transparent;
  margin-bottom: var(--admin-space-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--admin-space-md);
  position: relative;
  transition: all var(--admin-transition-normal);
}

.admin-alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--admin-text-sm);
}

.admin-alert-content {
  flex: 1;
}

.admin-alert-title {
  font-weight: var(--admin-font-semibold);
  margin-bottom: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
}

.admin-alert-message {
  font-size: var(--admin-text-sm);
  line-height: var(--admin-leading-relaxed);
}

.admin-alert-close {
  position: absolute;
  top: var(--admin-space-md);
  right: var(--admin-space-md);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-fast);
  opacity: 0.7;
}

.admin-alert-close:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

/* ===== ALERT VARIANTS ===== */

/* Success Alert */
.admin-alert-success {
  background: var(--admin-success-bg);
  border-color: var(--admin-success-light);
  color: var(--admin-success-dark);
}

.admin-alert-success .admin-alert-icon {
  color: var(--admin-success);
}

.admin-alert-success .admin-alert-close {
  color: var(--admin-success-dark);
}

/* Warning Alert */
.admin-alert-warning {
  background: var(--admin-warning-bg);
  border-color: var(--admin-warning-light);
  color: var(--admin-warning-dark);
}

.admin-alert-warning .admin-alert-icon {
  color: var(--admin-warning);
}

.admin-alert-warning .admin-alert-close {
  color: var(--admin-warning-dark);
}

/* Danger Alert */
.admin-alert-danger {
  background: var(--admin-danger-bg);
  border-color: var(--admin-danger-light);
  color: var(--admin-danger-dark);
}

.admin-alert-danger .admin-alert-icon {
  color: var(--admin-danger);
}

.admin-alert-danger .admin-alert-close {
  color: var(--admin-danger-dark);
}

/* Info Alert */
.admin-alert-info {
  background: var(--admin-info-bg);
  border-color: var(--admin-info-light);
  color: var(--admin-info-dark);
}

.admin-alert-info .admin-alert-icon {
  color: var(--admin-info);
}

.admin-alert-info .admin-alert-close {
  color: var(--admin-info-dark);
}

/* ===== TOAST NOTIFICATIONS ===== */
.admin-toast-container {
  position: fixed;
  top: var(--admin-space-2xl);
  right: var(--admin-space-2xl);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
  max-width: 400px;
}

.admin-toast {
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-xl);
  border: 1px solid var(--admin-border-color);
  padding: var(--admin-space-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--admin-space-md);
  animation: slideInRight 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.admin-toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--admin-gray-300);
}

.admin-toast-success::before {
  background: var(--admin-success);
}

.admin-toast-warning::before {
  background: var(--admin-warning);
}

.admin-toast-danger::before {
  background: var(--admin-danger);
}

.admin-toast-info::before {
  background: var(--admin-info);
}

.admin-toast-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-sm);
}

.admin-toast-success .admin-toast-icon {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-toast-warning .admin-toast-icon {
  background: var(--admin-warning-bg);
  color: var(--admin-warning);
}

.admin-toast-danger .admin-toast-icon {
  background: var(--admin-danger-bg);
  color: var(--admin-danger);
}

.admin-toast-info .admin-toast-icon {
  background: var(--admin-info-bg);
  color: var(--admin-info);
}

.admin-toast-content {
  flex: 1;
}

.admin-toast-title {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
}

.admin-toast-message {
  color: var(--admin-gray-600);
  font-size: var(--admin-text-sm);
  line-height: var(--admin-leading-relaxed);
}

.admin-toast-close {
  position: absolute;
  top: var(--admin-space-sm);
  right: var(--admin-space-sm);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  color: var(--admin-gray-400);
  transition: all var(--admin-transition-fast);
}

.admin-toast-close:hover {
  color: var(--admin-gray-600);
  background: var(--admin-gray-100);
}

/* ===== PROGRESS BAR FOR TOAST ===== */
.admin-toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--admin-gray-200);
  width: 100%;
}

.admin-toast-progress-bar {
  height: 100%;
  background: var(--admin-primary);
  transition: width linear;
}

.admin-toast-success .admin-toast-progress-bar {
  background: var(--admin-success);
}

.admin-toast-warning .admin-toast-progress-bar {
  background: var(--admin-warning);
}

.admin-toast-danger .admin-toast-progress-bar {
  background: var(--admin-danger);
}

.admin-toast-info .admin-toast-progress-bar {
  background: var(--admin-info);
}

/* ===== ANIMATIONS ===== */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.admin-toast-exit {
  animation: slideOutRight 0.3s ease-in forwards;
}

/* ===== BANNER ALERTS ===== */
.admin-banner {
  padding: var(--admin-space-md) var(--admin-space-lg);
  border-radius: 0;
  border: none;
  border-bottom: 1px solid transparent;
  margin: 0;
  text-align: center;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
}

.admin-banner-success {
  background: var(--admin-success);
  color: white;
  border-bottom-color: var(--admin-success-dark);
}

.admin-banner-warning {
  background: var(--admin-warning);
  color: white;
  border-bottom-color: var(--admin-warning-dark);
}

.admin-banner-danger {
  background: var(--admin-danger);
  color: white;
  border-bottom-color: var(--admin-danger-dark);
}

.admin-banner-info {
  background: var(--admin-info);
  color: white;
  border-bottom-color: var(--admin-info-dark);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .admin-toast-container {
    top: var(--admin-space-lg);
    right: var(--admin-space-lg);
    left: var(--admin-space-lg);
    max-width: none;
  }
  
  .admin-alert {
    padding: var(--admin-space-md);
  }
  
  .admin-toast {
    padding: var(--admin-space-md);
  }
}
