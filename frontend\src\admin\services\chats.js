import api from '@/services/api';

export const chatsService = {
  async getChats(params = {}) {
    try {
      const response = await api.get('/api/admin/chats', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching chats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load chats');
    }
  },

  async getChatById(id) {
    try {
      const response = await api.get(`/api/admin/chats/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching chat:', error);
      throw new Error(error.response?.data?.message || 'Failed to load chat details');
    }
  },

  async getChatMessages(chatId, params = {}) {
    try {
      const response = await api.get(`/api/admin/chats/${chatId}/messages`, { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      throw new Error(error.response?.data?.message || 'Failed to load chat messages');
    }
  },

  async deleteMessage(messageId) {
    try {
      const response = await api.delete(`/api/admin/messages/${messageId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete message');
    }
  },

  async getChatStats() {
    try {
      const response = await api.get('/api/admin/chats/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching chat stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load chat statistics');
    }
  },

  async moderateChat(chatId, action) {
    try {
      const response = await api.post(`/api/admin/chats/${chatId}/moderate`, { action });
      return response.data;
    } catch (error) {
      console.error('Error moderating chat:', error);
      throw new Error(error.response?.data?.message || 'Failed to moderate chat');
    }
  }
};
