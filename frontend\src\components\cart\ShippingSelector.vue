<template>
  <div class="shipping-selector">
    <h3 class="shipping-title">
      <i class="fas fa-truck"></i>
      Спосіб доставки
    </h3>

    <div v-if="loading" class="shipping-loading">
      <i class="fas fa-spinner fa-spin"></i>
      Завантаження способів доставки...
    </div>

    <div v-else-if="error" class="shipping-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error }}
    </div>

    <div v-else class="shipping-dropdown-container">
      <div class="shipping-dropdown" :class="{ 'open': dropdownOpen }">
        <div class="dropdown-selected" @click="toggleDropdown">
          <div class="selected-method" v-if="selectedMethod">
            <i :class="getMethodIcon(selectedMethod.id)"></i>
            <span class="method-name">{{ selectedMethod.description }}</span>
            <span class="method-price">{{ formatPrice(selectedMethod.priceAmount) }}</span>
            <span class="method-time">({{ formatDeliveryTime(selectedMethod.estimatedDays) }})</span>
          </div>
          <div class="placeholder" v-else>
            Оберіть спосіб доставки
          </div>
          <i class="fas fa-chevron-down dropdown-arrow" :class="{ 'rotated': dropdownOpen }"></i>
        </div>

        <div class="dropdown-options" v-show="dropdownOpen">
          <div
            v-for="method in shippingMethods"
            :key="method.id"
            class="dropdown-option"
            :class="{ 'selected': selectedMethod?.id === method.id }"
            @click="selectMethodFromDropdown(method)"
          >
            <div class="option-content">
              <i :class="getMethodIcon(method.id)"></i>
              <div class="option-info">
                <div class="option-main">
                  <span class="option-name">{{ method.description }}</span>
                  <span class="option-price">{{ formatPrice(method.priceAmount) }}</span>
                </div>
                <div class="option-details">
                  <i class="fas fa-clock"></i>
                  {{ formatDeliveryTime(method.estimatedDays) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Вибір способу вибору відділення -->
    <div v-if="selectedMethod && needsOfficeSelection" class="delivery-mode-selector">
      <div class="mode-tabs">
        <button
          class="mode-tab"
          :class="{ 'active': deliveryMode === 'manual' }"
          @click="setDeliveryMode('manual')"
        >
          <i class="fas fa-search"></i>
          Вибрати відділення
        </button>
        <button
          class="mode-tab"
          :class="{ 'active': deliveryMode === 'address' }"
          @click="setDeliveryMode('address')"
        >
          <i class="fas fa-map-marker-alt"></i>
          За моєю адресою
        </button>
      </div>
    </div>

    <!-- Компонент для ручного вибору відділення -->
    <DeliveryOfficeSelector
      v-if="selectedMethod && needsOfficeSelection && deliveryMode === 'manual'"
      :shipping-method="selectedMethod"
      :selected-office="selectedOffice"
      @office-selected="onOfficeSelected"
    />

    <!-- Компонент для вибору за адресою -->
    <AddressBasedDelivery
      v-if="selectedMethod && needsOfficeSelection && deliveryMode === 'address'"
      @delivery-selected="onAddressDeliverySelected"
    />
  </div>
</template>

<script>
import shippingService from '@/services/shipping.service';
import DeliveryOfficeSelector from './DeliveryOfficeSelector.vue';
import AddressBasedDelivery from './AddressBasedDelivery.vue';
import { useToast } from '@/composables/useToast';

export default {
  name: 'ShippingSelector',

  components: {
    DeliveryOfficeSelector,
    AddressBasedDelivery
  },

  props: {
    selectedShippingMethod: {
      type: Object,
      default: null
    }
  },

  emits: ['shipping-method-selected'],

  data() {
    const { showToast } = useToast();
    
    return {
      shippingMethods: [],
      selectedMethod: this.selectedShippingMethod,
      selectedMethodId: this.selectedShippingMethod?.id || null,
      selectedOffice: null,
      deliveryMode: 'manual', // 'manual' або 'address'
      dropdownOpen: false,
      loading: false,
      error: null,
      showToast
    };
  },

  async mounted() {
    await this.fetchShippingMethods();
    // Додаємо обробник кліків для закриття дропдауна
    document.addEventListener('click', this.handleClickOutside);
  },

  beforeUnmount() {
    // Видаляємо обробник при знищенні компонента
    document.removeEventListener('click', this.handleClickOutside);
  },

  computed: {
    needsOfficeSelection() {
      return this.selectedMethod && (
        this.selectedMethod.id === 'nova-poshta' ||
        this.selectedMethod.id === 'ukr-poshta'
      );
    }
  },

  watch: {
    selectedShippingMethod(newMethod) {
      this.selectedMethod = newMethod;
      this.selectedMethodId = newMethod?.id || null;
    }
  },

  methods: {
    async fetchShippingMethods() {
      this.loading = true;
      this.error = null;

      try {
        const response = await shippingService.getShippingMethods();

        if (response.success && response.data?.data) {
          this.shippingMethods = response.data.data;

          // Якщо немає вибраного методу, вибираємо перший за замовчуванням
          if (!this.selectedMethod && this.shippingMethods.length > 0) {
            this.selectMethod(this.shippingMethods[0]);
          }
        } else {
          this.error = 'Не вдалося завантажити способи доставки';
        }
      } catch (error) {
        console.error('Error fetching shipping methods:', error);
        this.error = 'Помилка при завантаженні способів доставки';
        this.showToast('Помилка при завантаженні способів доставки', 'error');
      } finally {
        this.loading = false;
      }
    },

    toggleDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
    },

    selectMethod(method) {
      this.selectedMethod = method;
      this.selectedMethodId = method.id;
      this.selectedOffice = null; // Скидаємо вибране відділення при зміні методу

      const shippingCost = shippingService.calculateShippingCost(method, 0);

      this.$emit('shipping-method-selected', {
        method,
        cost: shippingCost,
        office: null
      });
    },

    selectMethodFromDropdown(method) {
      this.selectMethod(method);
      this.dropdownOpen = false; // Закриваємо дропдаун після вибору
    },

    onOfficeSelected(officeData) {
      this.selectedOffice = officeData;

      // Повторно емітимо подію з інформацією про відділення
      const shippingCost = shippingService.calculateShippingCost(this.selectedMethod, 0);

      this.$emit('shipping-method-selected', {
        method: this.selectedMethod,
        cost: shippingCost,
        office: officeData
      });
    },

    getMethodName(methodId) {
      return shippingService.getShippingMethodName(methodId);
    },

    getMethodIcon(methodId) {
      return shippingService.getShippingMethodIcon(methodId);
    },

    formatDeliveryTime(estimatedDays) {
      return shippingService.formatDeliveryTime(estimatedDays);
    },

    formatPrice(priceAmount) {
      const price = parseFloat(priceAmount) || 0;
      return price === 0 ? 'Безкоштовно' : `${Math.round(price)} ₴`;
    },

    handleClickOutside(event) {
      // Закриваємо дропдаун, якщо клік був поза ним
      if (this.$el && !this.$el.contains(event.target)) {
        this.dropdownOpen = false;
      }
    },

    setDeliveryMode(mode) {
      this.deliveryMode = mode;
      // Скидаємо вибране відділення при зміні режиму
      this.selectedOffice = null;

      // Емітимо оновлення без відділення
      if (this.selectedMethod) {
        const shippingCost = shippingService.calculateShippingCost(this.selectedMethod, 0);
        this.$emit('shipping-method-selected', {
          method: this.selectedMethod,
          cost: shippingCost,
          office: null
        });
      }
    },

    onAddressDeliverySelected(deliveryData) {
      // Обробляємо вибір доставки за адресою
      this.selectedOffice = {
        ...deliveryData.office,
        address: deliveryData.address,
        city: deliveryData.city,
        service: deliveryData.service
      };

      const shippingCost = shippingService.calculateShippingCost(this.selectedMethod, 0);

      this.$emit('shipping-method-selected', {
        method: this.selectedMethod,
        cost: shippingCost,
        office: this.selectedOffice
      });
    }
  }
};
</script>

<style scoped>
.shipping-selector {
  margin-bottom: 20px;
}

.shipping-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.shipping-title i {
  color: #007bff;
}

.shipping-loading,
.shipping-error {
  padding: 16px;
  text-align: center;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.shipping-loading {
  background: #f8f9fa;
  color: #6c757d;
}

.shipping-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.shipping-dropdown-container {
  position: relative;
}

.shipping-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 50px;
}

.dropdown-selected:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.shipping-dropdown.open .dropdown-selected {
  border-color: #007bff;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.selected-method {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.selected-method i {
  color: #007bff;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.selected-method .method-name {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.selected-method .method-price {
  font-weight: 600;
  color: #28a745;
  margin-right: 8px;
}

.selected-method .method-time {
  font-size: 14px;
  color: #6c757d;
}

.placeholder {
  color: #6c757d;
  font-style: italic;
}

.dropdown-arrow {
  color: #6c757d;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #007bff;
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.dropdown-option {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: #f8f9fa;
}

.dropdown-option.selected {
  background: #e3f2fd;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-content i {
  color: #007bff;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.option-info {
  flex: 1;
}

.option-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.option-name {
  font-weight: 600;
  color: #333;
}

.option-price {
  font-weight: 600;
  color: #28a745;
  font-size: 14px;
}

.option-details {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
}

.option-details i {
  font-size: 10px;
  width: 12px;
}

@media (max-width: 768px) {
  .dropdown-selected {
    padding: 10px 12px;
    min-height: 45px;
  }

  .selected-method {
    gap: 8px;
  }

  .selected-method .method-name {
    font-size: 14px;
  }

  .selected-method .method-price {
    font-size: 14px;
  }

  .selected-method .method-time {
    font-size: 12px;
  }

  .dropdown-option {
    padding: 10px 12px;
  }

  .option-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .option-price {
    align-self: flex-end;
  }
}

/* Стилі для вибору режиму доставки */
.delivery-mode-selector {
  margin: 16px 0;
}

.mode-tabs {
  display: flex;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
}

.mode-tab {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  color: #6c757d;
}

.mode-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.mode-tab.active {
  background: #007bff;
  color: white;
}

.mode-tab i {
  font-size: 14px;
}

@media (max-width: 768px) {
  .mode-tab {
    padding: 10px 12px;
    font-size: 14px;
  }

  .mode-tab span {
    display: none;
  }
}
</style>
