import{_ as U,g as u,i as B,c as i,a as s,k as _,b as O,w,d as p,r as S,n as m,t as a,s as $,f as F,e as R,o}from"./index-L-hJxM_5.js";import{a as x}from"./addresses-DL07ASCS.js";const E={class:"address-detail"},L={class:"level"},T={class:"level-left"},M={class:"level-item"},j={class:"breadcrumb"},z={class:"level-right"},P={class:"level-item"},q={key:0,class:"has-text-centered"},G={key:1,class:"notification is-danger"},H={key:2},J={class:"columns"},K={class:"column is-8"},Q={class:"card"},W={class:"card-content"},X={class:"columns"},Y={class:"column is-6"},Z={class:"field"},ss={class:"info-value"},es={key:1,class:"has-text-grey"},ls={class:"field"},ts={class:"info-value"},as={class:"field"},ds={class:"info-value"},os={class:"field"},is={class:"info-value"},ns={class:"column is-6"},rs={class:"field"},cs={class:"info-value"},us={class:"field"},vs={class:"info-value"},fs={key:0,class:"field"},_s={class:"info-value"},ps={class:"field"},ms={class:"content"},bs={class:"box has-background-light"},hs={class:"has-text-weight-semibold"},As={class:"column is-4"},gs={class:"card"},ys={class:"card-content"},ks={class:"buttons is-fullwidth"},Cs=["disabled"],Ds=["disabled"],ws={class:"card mt-4"},xs={class:"card-content"},Ns={class:"field"},Is={class:"info-value is-family-monospace"},Vs={key:0,class:"field"},Us={class:"info-value is-family-monospace"},Bs={class:"modal-card"},Os={class:"modal-card-head"},Ss={class:"modal-card-body"},$s={class:"box has-background-light mt-3"},Fs={class:"has-text-weight-semibold"},Rs={class:"modal-card-foot"},Es={__name:"AddressDetail",setup(Ls){const N=F(),b=R(),l=u({}),v=u(!1),n=u(null),c=u(!1),r=u(!1),h=async()=>{v.value=!0,n.value=null;try{const t=await x.getAddressById(N.params.id);l.value=t}catch(t){n.value=t.message||"Failed to load address details"}finally{v.value=!1}},I=()=>{b.push({name:"AdminAddressEdit",params:{id:l.value.id}})},V=async()=>{c.value=!0;try{await x.deleteAddress(l.value.id),b.push({name:"AdminAddresses"})}catch(t){n.value=t.message||"Failed to delete address"}finally{c.value=!1,r.value=!1}},A=t=>new Date(t).toLocaleString(),g=t=>{if(!t)return"N/A";const e=[],d=t.addressVO||t;return d.street&&e.push(d.street),d.city&&e.push(d.city),d.region&&e.push(d.region),d.postalCode&&e.push(d.postalCode),e.length>0?e.join(", "):"N/A"};return B(()=>{h()}),(t,e)=>{var y,k,C,D;const d=S("router-link");return o(),i("div",E,[s("div",L,[s("div",T,[s("div",M,[s("nav",j,[s("ul",null,[s("li",null,[O(d,{to:"/admin/addresses"},{default:w(()=>e[5]||(e[5]=[p("Addresses")])),_:1})]),e[6]||(e[6]=s("li",{class:"is-active"},[s("a",null,"Address Details")],-1))])])])]),s("div",z,[s("div",P,[s("button",{class:m(["button is-primary",{"is-loading":v.value}]),onClick:h},e[7]||(e[7]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Refresh",-1)]),2)])])]),v.value&&!l.value.id?(o(),i("div",q,e[8]||(e[8]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading address details...",-1)]))):n.value?(o(),i("div",G,[s("button",{class:"delete",onClick:e[0]||(e[0]=f=>n.value=null)}),p(" "+a(n.value),1)])):l.value.id?(o(),i("div",H,[s("div",J,[s("div",K,[s("div",Q,[e[17]||(e[17]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Address Information")],-1)),s("div",W,[s("div",X,[s("div",Y,[s("div",Z,[e[9]||(e[9]=s("label",{class:"label"},"User",-1)),s("p",ss,[l.value.userId?(o(),$(d,{key:0,to:{name:"AdminUserDetail",params:{id:l.value.userId}}},{default:w(()=>[p(a(l.value.userName||"Unknown User"),1)]),_:1},8,["to"])):(o(),i("span",es,"No User Assigned"))])]),s("div",ls,[e[10]||(e[10]=s("label",{class:"label"},"Region",-1)),s("p",ts,a(((y=l.value.addressVO)==null?void 0:y.region)||l.value.region||"N/A"),1)]),s("div",as,[e[11]||(e[11]=s("label",{class:"label"},"City",-1)),s("p",ds,a(((k=l.value.addressVO)==null?void 0:k.city)||l.value.city||"N/A"),1)]),s("div",os,[e[12]||(e[12]=s("label",{class:"label"},"Street",-1)),s("p",is,a(((C=l.value.addressVO)==null?void 0:C.street)||l.value.street||"N/A"),1)])]),s("div",ns,[s("div",rs,[e[13]||(e[13]=s("label",{class:"label"},"Postal Code",-1)),s("p",cs,a(((D=l.value.addressVO)==null?void 0:D.postalCode)||l.value.postalCode||"N/A"),1)]),s("div",us,[e[14]||(e[14]=s("label",{class:"label"},"Created At",-1)),s("p",vs,a(A(l.value.createdAt)),1)]),l.value.updatedAt?(o(),i("div",fs,[e[15]||(e[15]=s("label",{class:"label"},"Updated At",-1)),s("p",_s,a(A(l.value.updatedAt)),1)])):_("",!0)])]),s("div",ps,[e[16]||(e[16]=s("label",{class:"label"},"Full Address",-1)),s("div",ms,[s("div",bs,[s("p",hs,a(g(l.value)),1)])])])])])]),s("div",As,[s("div",gs,[e[20]||(e[20]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Actions")],-1)),s("div",ys,[s("div",ks,[s("button",{class:"button is-primary is-fullwidth",onClick:I,disabled:c.value},e[18]||(e[18]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit Address",-1)]),8,Cs),s("button",{class:"button is-danger is-fullwidth",onClick:e[1]||(e[1]=f=>r.value=!0),disabled:c.value},e[19]||(e[19]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete Address",-1)]),8,Ds)])])]),s("div",ws,[e[24]||(e[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Information")],-1)),s("div",xs,[s("div",Ns,[e[21]||(e[21]=s("label",{class:"label"},"Address ID",-1)),s("p",Is,a(l.value.id),1)]),l.value.userId?(o(),i("div",Vs,[e[22]||(e[22]=s("label",{class:"label"},"User ID",-1)),s("p",Us,a(l.value.userId),1)])):_("",!0),e[23]||(e[23]=s("div",{class:"field"},[s("label",{class:"label"},"Status"),s("span",{class:"tag is-success"},"Active")],-1))])])])])])):_("",!0),s("div",{class:m(["modal",{"is-active":r.value}])},[s("div",{class:"modal-background",onClick:e[2]||(e[2]=f=>r.value=!1)}),s("div",Bs,[s("header",Os,[e[25]||(e[25]=s("p",{class:"modal-card-title"},"Delete Address",-1)),s("button",{class:"delete",onClick:e[3]||(e[3]=f=>r.value=!1)})]),s("section",Ss,[e[26]||(e[26]=s("p",null,"Are you sure you want to delete this address? This action cannot be undone.",-1)),s("div",$s,[s("p",Fs,a(g(l.value)),1)])]),s("footer",Rs,[s("button",{class:m(["button is-danger",{"is-loading":c.value}]),onClick:V}," Delete Address ",2),s("button",{class:"button",onClick:e[4]||(e[4]=f=>r.value=!1)},"Cancel")])])],2)])}}},js=U(Es,[["__scopeId","data-v-d6f1f179"]]);export{js as default};
