<template>
  <div class="filters-sidebar">
    <!-- Loading message -->
    <div v-if="!hasFilters" class="no-filters">
      <p>Завантаження фільтрів...</p>
    </div>

    <div v-for="(options, filterTitle) in filterList" :key="filterTitle" class="filter-group">
      <div class="filter-header" @click="toggleFilter(filterTitle)">
        <h3 class="filter-title">{{ filterTitle }}</h3>
        <svg class="filter-arrow" :class="{ 'rotated': isFilterOpen(filterTitle) }" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 9l6 6 6-6"></path>
        </svg>
      </div>
      <div class="filter-content" v-show="isFilterOpen(filterTitle)">
        <div class="filter-search" v-if="shouldShowSearch(filterTitle)">
          <input type="text" :placeholder="'Пошук в ' + filterTitle" v-model="searchTerms[filterTitle]">
        </div>
        <div class="filter-options">
          <label 
            v-for="(count, optionName) in filterOptionsBySearch(options, filterTitle)" 
            :key="optionName" 
            class="filter-option"
          >
            <input 
              type="checkbox" 
              :value="optionName" 
              v-model="selectedFilters[filterTitle]"
              @change="emitFilterChange"
            > 
            <span class="option-name">{{ optionName }}</span>
            <span class="option-count">{{ count }}</span>
          </label>
        </div>
      </div>
    </div>
  </div> 
</template>

<script>
export default {
  name: 'FiltersSidebar',
  props: {
    filterList: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
      openFilters: {},
      selectedFilters: {},
      searchTerms: {},
      error: null,
    };
  },
  computed: {
    hasFilters() {
      return this.filterList && typeof this.filterList === 'object' && Object.keys(this.filterList).length > 0;
    }
  },
  watch: {
    filterList: {
      handler(newFilterList) {
        if (newFilterList && typeof newFilterList === 'object') {
          // Create new objects to ensure reactivity
          const newOpenFilters = { ...this.openFilters };
          const newSelectedFilters = { ...this.selectedFilters };
          const newSearchTerms = { ...this.searchTerms };

          Object.keys(newFilterList).forEach(filter => {
            if (!newOpenFilters.hasOwnProperty(filter)) {
              newOpenFilters[filter] = true;
              newSelectedFilters[filter] = [];
              newSearchTerms[filter] = '';
            }
          });

          // Assign new objects to trigger reactivity
          this.openFilters = newOpenFilters;
          this.selectedFilters = newSelectedFilters;
          this.searchTerms = newSearchTerms;
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // Initialize open state for all filters (default to open)
    if (this.filterList && typeof this.filterList === 'object') {
      const openFilters = {};
      const selectedFilters = {};
      const searchTerms = {};

      Object.keys(this.filterList).forEach(filter => {
        openFilters[filter] = true;
        selectedFilters[filter] = [];
        searchTerms[filter] = '';
      });

      this.openFilters = openFilters;
      this.selectedFilters = selectedFilters;
      this.searchTerms = searchTerms;
    }
  },
  methods: {
    toggleFilter(filter) {
      // Create new object to ensure reactivity
      this.openFilters = {
        ...this.openFilters,
        [filter]: !this.openFilters[filter]
      };
    },
    
    isFilterOpen(filter) {
      return this.openFilters[filter] === true;
    },
    
    shouldShowSearch(filter) {
      // Show search box for filters with many options (e.g., more than 5)
      return Object.keys(this.filterList[filter] || {}).length > 10;
    },
    
    filterOptionsBySearch(options, filterTitle) {
      const searchTerm = this.searchTerms[filterTitle]?.toLowerCase() || '';
      
      if (!searchTerm) {
        return options;
      }
      
      const filteredOptions = {};
      Object.entries(options).forEach(([option, count]) => {
        if (option.toLowerCase().includes(searchTerm)) {
          filteredOptions[option] = count;
        }
      });
      
      return filteredOptions;
    },
    
    emitFilterChange() {
      // Create a clean object with only selected filters
      const activeFilters = {};

      Object.entries(this.selectedFilters).forEach(([category, values]) => {
        if (values.length > 0) {
          activeFilters[category] = values;
        }
      });

      this.$emit('filter-changed', activeFilters);
    },

    clearAllFilters() {
      // Очищаємо всі вибрані фільтри
      const clearedFilters = {};
      Object.keys(this.selectedFilters).forEach(filter => {
        clearedFilters[filter] = [];
      });
      this.selectedFilters = clearedFilters;

      // Емітимо зміни
      this.emitFilterChange();
    },

    updateSelectedFilters(newFilters) {
      // Оновлюємо вибрані фільтри з зовнішнього джерела
      this.selectedFilters = { ...this.selectedFilters, ...newFilters };
    }
  }
}
</script>

<style scoped>
.filters-sidebar {
  width: 250px;
  flex-shrink: 0;
  font-family: Arial, sans-serif;
}

.no-filters {
  padding: 20px;
  text-align: center;
  color: #666;
}

.no-filters small {
  display: block;
  margin-top: 10px;
  font-size: 10px;
  color: #999;
  word-break: break-all;
}

.filter-group {
  
  margin-bottom: 10px;
  border-bottom: 2px solid #ABAAAA;
  padding-bottom: 10px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 10px 0;
}

.filter-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.filter-arrow {
  transition: transform 0.3s ease;
}

.filter-arrow.rotated {
  transform: rotate(180deg);
}

.filter-content {
  padding: 10px 0;
}

.filter-search {
  margin-bottom: 10px;
}

.filter-search input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-options {
  max-height: 300px;
  overflow-y: auto;
}

.filter-option {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
  font-size: 14px;
}

.option-name {
  flex-grow: 1;
  margin-left: 8px;
}

.option-count {
  color: #999;
  font-size: 12px;
  margin-right: 20px;
}

.price-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.price-inputs input {
  width: 80px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.price-inputs span {
  margin: 0 10px;
}

.ok-btn {
  margin-left: 10px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.ok-btn:hover {
  background-color: #e5e5e5;
}

.price-slider {
  position: relative;
  height: 30px;
  margin-bottom: 10px;
}

.price-slider input[type="range"] {
  position: absolute;
  width: 100%;
  height: 5px;
  background: none;
  pointer-events: none;
}

.price-range {
  text-align: center;
  font-size: 14px;
  color: #666;
}
</style>
