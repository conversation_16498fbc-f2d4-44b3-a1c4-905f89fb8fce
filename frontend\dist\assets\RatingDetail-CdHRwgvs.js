import{_ as B,g as v,h as S,i as I,H as M,c as n,a as s,k as D,b as x,w as A,d as b,r as L,n as c,t as o,F as g,p as y,x as h,D as k,f as P,e as Q,o as d}from"./index-L-hJxM_5.js";import{r as w}from"./ratings-D2bTWLA4.js";const z={class:"rating-detail"},H={class:"level"},O={class:"level-left"},j={class:"level-item"},q={class:"breadcrumb"},G={class:"level-right"},J={class:"level-item"},K={key:0,class:"has-text-centered"},W={key:1,class:"notification is-danger"},X={key:2},Y={class:"columns"},Z={class:"column is-8"},ss={class:"card"},es={class:"card-content"},ls={class:"columns"},as={class:"column is-6"},ts={class:"field"},is={class:"info-value"},os={class:"field"},ns={class:"info-value"},ds={class:"field"},cs={class:"info-value"},rs={class:"stars"},us={class:"ml-2"},vs={class:"column is-6"},ms={class:"field"},_s={class:"info-value"},ps={key:0,class:"field"},fs={class:"info-value"},bs={class:"card mt-4"},gs={class:"card-content"},ys={class:"columns"},hs={class:"column is-4"},ks={class:"field"},Cs={class:"stars"},Rs={class:"ml-2"},Ts={class:"column is-4"},Ds={class:"field"},xs={class:"stars"},As={class:"ml-2"},ws={class:"column is-4"},Vs={class:"field"},Ss={class:"stars"},Fs={class:"ml-2"},Us={key:0,class:"field"},Es={class:"content"},Ns={class:"column is-4"},$s={class:"card"},Bs={class:"card-content"},Is={class:"buttons is-fullwidth"},Ms=["disabled"],Ls=["disabled"],Ps={class:"card mt-4"},Qs={class:"card-content"},zs={class:"field"},Hs={class:"info-value"},Os={class:"field"},js={class:"rating-breakdown"},qs={class:"rating-item"},Gs={class:"rating-value"},Js={class:"rating-item"},Ks={class:"rating-value"},Ws={class:"rating-item"},Xs={class:"rating-value"},Ys={class:"modal-card"},Zs={class:"modal-card-body"},se={class:"field"},ee={class:"control"},le={class:"field"},ae={class:"control"},te={class:"field"},ie={class:"control"},oe={class:"field"},ne={class:"control"},de={class:"help"},ce={class:"modal-card-foot"},re=["disabled"],ue={class:"modal-card"},ve={class:"modal-card-head"},me={class:"modal-card-foot"},_e={__name:"RatingDetail",setup(pe){const F=P(),U=Q(),l=v({}),_=v(!1),u=v(null),r=v(!1),m=v(!1),p=v(!1),t=v({service:1,deliveryTime:1,accuracy:1,comment:""}),C=S(()=>l.value.service?(l.value.service+l.value.deliveryTime+l.value.accuracy)/3:0),E=S(()=>t.value.service>=1&&t.value.service<=5&&t.value.deliveryTime>=1&&t.value.deliveryTime<=5&&t.value.accuracy>=1&&t.value.accuracy<=5&&(t.value.comment||"").length<=500),R=async()=>{_.value=!0,u.value=null;try{const i=await w.getRatingById(F.params.id);l.value=i}catch(i){u.value=i.message||"Failed to load rating details"}finally{_.value=!1}},N=async()=>{r.value=!0;try{await w.updateRating(l.value.id,t.value),await R(),f()}catch(i){u.value=i.message||"Failed to update rating"}finally{r.value=!1}},$=async()=>{r.value=!0;try{await w.deleteRating(l.value.id),U.push({name:"AdminRatings"})}catch(i){u.value=i.message||"Failed to delete rating"}finally{r.value=!1,m.value=!1}},f=()=>{p.value=!1,t.value={service:l.value.service||1,deliveryTime:l.value.deliveryTime||1,accuracy:l.value.accuracy||1,comment:l.value.comment||""}},V=i=>new Date(i).toLocaleString();return I(()=>{R()}),M(()=>l.value,i=>{i&&i.id&&!p.value&&(t.value={service:i.service||1,deliveryTime:i.deliveryTime||1,accuracy:i.accuracy||1,comment:i.comment||""})},{immediate:!0,deep:!0}),(i,e)=>{const T=L("router-link");return d(),n("div",z,[s("div",H,[s("div",O,[s("div",j,[s("nav",q,[s("ul",null,[s("li",null,[x(T,{to:"/admin/ratings"},{default:A(()=>e[10]||(e[10]=[b("Ratings")])),_:1})]),e[11]||(e[11]=s("li",{class:"is-active"},[s("a",null,"Rating Details")],-1))])])])]),s("div",G,[s("div",J,[s("button",{class:c(["button is-primary",{"is-loading":_.value}]),onClick:R},e[12]||(e[12]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Refresh",-1)]),2)])])]),_.value&&!l.value.id?(d(),n("div",K,e[13]||(e[13]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading rating details...",-1)]))):u.value?(d(),n("div",W,[s("button",{class:"delete",onClick:e[0]||(e[0]=a=>u.value=null)}),b(" "+o(u.value),1)])):l.value.id?(d(),n("div",X,[s("div",Y,[s("div",Z,[s("div",ss,[e[19]||(e[19]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Rating Information")],-1)),s("div",es,[s("div",ls,[s("div",as,[s("div",ts,[e[14]||(e[14]=s("label",{class:"label"},"Product",-1)),s("p",is,[x(T,{to:{name:"AdminProductDetail",params:{id:l.value.productId}}},{default:A(()=>[b(o(l.value.productName),1)]),_:1},8,["to"])])]),s("div",os,[e[15]||(e[15]=s("label",{class:"label"},"User",-1)),s("p",ns,[x(T,{to:{name:"AdminUserDetail",params:{id:l.value.userId}}},{default:A(()=>[b(o(l.value.userName),1)]),_:1},8,["to"])])]),s("div",ds,[e[16]||(e[16]=s("label",{class:"label"},"Overall Rating",-1)),s("p",cs,[s("div",rs,[(d(),n(g,null,y(5,a=>s("span",{key:a,class:c(["star",{"is-filled":a<=C.value}])}," ★ ",2)),64)),s("span",us,"("+o(C.value.toFixed(1))+"/5)",1)])])])]),s("div",vs,[s("div",ms,[e[17]||(e[17]=s("label",{class:"label"},"Created At",-1)),s("p",_s,o(V(l.value.createdAt)),1)]),l.value.updatedAt?(d(),n("div",ps,[e[18]||(e[18]=s("label",{class:"label"},"Updated At",-1)),s("p",fs,o(V(l.value.updatedAt)),1)])):D("",!0)])])])]),s("div",bs,[e[24]||(e[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Detailed Ratings")],-1)),s("div",gs,[s("div",ys,[s("div",hs,[s("div",ks,[e[20]||(e[20]=s("label",{class:"label"},"Service Quality",-1)),s("div",Cs,[(d(),n(g,null,y(5,a=>s("span",{key:a,class:c(["star",{"is-filled":a<=l.value.service}])}," ★ ",2)),64)),s("span",Rs,"("+o(l.value.service)+"/5)",1)])])]),s("div",Ts,[s("div",Ds,[e[21]||(e[21]=s("label",{class:"label"},"Delivery Time",-1)),s("div",xs,[(d(),n(g,null,y(5,a=>s("span",{key:a,class:c(["star",{"is-filled":a<=l.value.deliveryTime}])}," ★ ",2)),64)),s("span",As,"("+o(l.value.deliveryTime)+"/5)",1)])])]),s("div",ws,[s("div",Vs,[e[22]||(e[22]=s("label",{class:"label"},"Product Accuracy",-1)),s("div",Ss,[(d(),n(g,null,y(5,a=>s("span",{key:a,class:c(["star",{"is-filled":a<=l.value.accuracy}])}," ★ ",2)),64)),s("span",Fs,"("+o(l.value.accuracy)+"/5)",1)])])])]),l.value.comment?(d(),n("div",Us,[e[23]||(e[23]=s("label",{class:"label"},"Comment",-1)),s("div",Es,[s("p",null,o(l.value.comment),1)])])):D("",!0)])])]),s("div",Ns,[s("div",$s,[e[27]||(e[27]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Actions")],-1)),s("div",Bs,[s("div",Is,[s("button",{class:"button is-primary is-fullwidth",onClick:e[1]||(e[1]=a=>p.value=!0),disabled:r.value},e[25]||(e[25]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit Rating",-1)]),8,Ms),s("button",{class:"button is-danger is-fullwidth",onClick:e[2]||(e[2]=a=>m.value=!0),disabled:r.value},e[26]||(e[26]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete Rating",-1)]),8,Ls)])])]),s("div",Ps,[e[33]||(e[33]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Statistics")],-1)),s("div",Qs,[s("div",zs,[e[28]||(e[28]=s("label",{class:"label"},"Average Rating",-1)),s("p",Hs,o(C.value.toFixed(2))+"/5",1)]),s("div",Os,[e[32]||(e[32]=s("label",{class:"label"},"Rating Breakdown",-1)),s("div",js,[s("div",qs,[e[29]||(e[29]=s("span",{class:"rating-label"},"Service:",-1)),s("span",Gs,o(l.value.service)+"/5",1)]),s("div",Js,[e[30]||(e[30]=s("span",{class:"rating-label"},"Delivery:",-1)),s("span",Ks,o(l.value.deliveryTime)+"/5",1)]),s("div",Ws,[e[31]||(e[31]=s("span",{class:"rating-label"},"Accuracy:",-1)),s("span",Xs,o(l.value.accuracy)+"/5",1)])])])])])])])])):D("",!0),s("div",{class:c(["modal",{"is-active":p.value}])},[s("div",{class:"modal-background",onClick:f}),s("div",Ys,[s("header",{class:"modal-card-head"},[e[34]||(e[34]=s("p",{class:"modal-card-title"},"Edit Rating",-1)),s("button",{class:"delete",onClick:f})]),s("section",Zs,[s("div",se,[e[35]||(e[35]=s("label",{class:"label"},"Service Quality (1-5)",-1)),s("div",ee,[h(s("input",{class:"input",type:"number","onUpdate:modelValue":e[3]||(e[3]=a=>t.value.service=a),min:"1",max:"5"},null,512),[[k,t.value.service,void 0,{number:!0}]])])]),s("div",le,[e[36]||(e[36]=s("label",{class:"label"},"Delivery Time (1-5)",-1)),s("div",ae,[h(s("input",{class:"input",type:"number","onUpdate:modelValue":e[4]||(e[4]=a=>t.value.deliveryTime=a),min:"1",max:"5"},null,512),[[k,t.value.deliveryTime,void 0,{number:!0}]])])]),s("div",te,[e[37]||(e[37]=s("label",{class:"label"},"Product Accuracy (1-5)",-1)),s("div",ie,[h(s("input",{class:"input",type:"number","onUpdate:modelValue":e[5]||(e[5]=a=>t.value.accuracy=a),min:"1",max:"5"},null,512),[[k,t.value.accuracy,void 0,{number:!0}]])])]),s("div",oe,[e[38]||(e[38]=s("label",{class:"label"},"Comment",-1)),s("div",ne,[h(s("textarea",{class:"textarea","onUpdate:modelValue":e[6]||(e[6]=a=>t.value.comment=a),placeholder:"Enter rating comment...",rows:"3",maxlength:"500"},"              ",512),[[k,t.value.comment]])]),s("p",de,o((t.value.comment||"").length)+"/500 characters",1)])]),s("footer",ce,[s("button",{class:c(["button is-primary",{"is-loading":r.value}]),onClick:N,disabled:!E.value}," Save Changes ",10,re),s("button",{class:"button",onClick:f},"Cancel")])])],2),s("div",{class:c(["modal",{"is-active":m.value}])},[s("div",{class:"modal-background",onClick:e[7]||(e[7]=a=>m.value=!1)}),s("div",ue,[s("header",ve,[e[39]||(e[39]=s("p",{class:"modal-card-title"},"Delete Rating",-1)),s("button",{class:"delete",onClick:e[8]||(e[8]=a=>m.value=!1)})]),e[40]||(e[40]=s("section",{class:"modal-card-body"},[s("p",null,"Are you sure you want to delete this rating? This action cannot be undone.")],-1)),s("footer",me,[s("button",{class:c(["button is-danger",{"is-loading":r.value}]),onClick:$}," Delete Rating ",2),s("button",{class:"button",onClick:e[9]||(e[9]=a=>m.value=!1)},"Cancel")])])],2)])}}},ge=B(_e,[["__scopeId","data-v-b7026eaa"]]);export{ge as default};
