import{_ as O,g as j,h as S,H as y,c as d,o,a as n,k as h,n as g,t as u,x as m,D as v,F as f,p as k,y as w,d as C}from"./index-L-hJxM_5.js";const D={class:"admin-search-and-filters"},$={class:"admin-filters-panel"},A={class:"admin-filters-content"},T={class:"admin-filters-row"},E={class:"admin-filter-label"},R={class:"admin-search-wrapper"},M=["placeholder"],P={class:"admin-filter-label"},q={class:"admin-filter-control"},z={key:0,class:"admin-select-wrapper"},H=["onUpdate:modelValue","onChange"],G={key:0,value:""},J=["value"],K=["placeholder","onUpdate:modelValue","onInput"],Q=["onUpdate:modelValue","onChange"],W=["placeholder","onUpdate:modelValue","onInput"],X={key:0,class:"admin-filter-group admin-filter-actions"},Y={key:0,class:"admin-status-bar"},Z={class:"admin-status-content"},L={class:"admin-status-info"},ee={class:"admin-status-count"},te={class:"admin-status-text"},ae={key:0,class:"admin-active-filters"},se=["onClick"],le={__name:"SearchAndFilters",props:{search:{type:String,default:""},filters:{type:Object,required:!0},filterFields:{type:Array,default:()=>[]},searchLabel:{type:String,default:"Search"},searchPlaceholder:{type:String,default:"Search..."},searchColumnClass:{type:String,default:"is-5"},showResetButton:{type:Boolean,default:!0},resetButtonText:{type:String,default:"Reset Filters"},showStatusBar:{type:Boolean,default:!0},totalItems:{type:Number,default:0},itemName:{type:String,default:"items"},loading:{type:Boolean,default:!1}},emits:["search-changed","filter-changed","reset-filters","clear-filters","apply-filters","update:search","update:filters"],setup(i,{emit:V}){const r=i,c=V,l=j({search:r.search||"",...r.filters}),_=S(()=>Object.values(l.value).some(t=>t!==""&&t!==null&&t!==void 0)),x=S(()=>Object.entries(l.value).filter(([t,a])=>a!==""&&a!==null&&a!==void 0).reduce((t,[a,e])=>(t[a]=e,t),{})),B=()=>{c("search-changed",l.value.search)},p=(t,a)=>{c("filter-changed",t,a)},F=()=>{Object.keys(l.value).forEach(t=>{l.value[t]=""}),c("reset-filters"),c("clear-filters")},I=t=>{l.value[t]="",c("filter-changed",t,"")},U=t=>{if(t==="search")return"Search";const a=r.filterFields.find(e=>e.key===t);return a?a.label:t},N=(t,a)=>{const e=r.filterFields.find(s=>s.key===t);if(e&&e.type==="select"&&e.options){const s=e.options.find(b=>b.value===a);return s?s.label:a}return a};return y(()=>r.filters,t=>{l.value={search:r.search||"",...t}},{deep:!0,immediate:!0}),y(()=>r.search,t=>{l.value.search!==t&&(l.value.search=t||"")},{immediate:!0}),y(l,t=>{t.search!==r.search&&(c("search-changed",t.search),c("update:search",t.search)),Object.keys(t).forEach(a=>{a!=="search"&&t[a]!==r.filters[a]&&c("filter-changed",a,t[a])}),c("update:filters",t)},{deep:!0}),(t,a)=>(o(),d(f,null,[n("div",D,[n("div",$,[n("div",A,[n("div",T,[n("div",{class:g(["admin-filter-group",i.searchColumnClass])},[n("label",E,u(i.searchLabel),1),n("div",R,[m(n("input",{class:"admin-search-input",type:"text",placeholder:i.searchPlaceholder,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value.search=e),onInput:B},null,40,M),[[v,l.value.search]]),a[1]||(a[1]=n("div",{class:"admin-search-icon"},[n("i",{class:"fas fa-search"})],-1))])],2),(o(!0),d(f,null,k(i.filterFields,e=>(o(),d("div",{key:e.key,class:g(["admin-filter-group",e.columnClass||"admin-filter-group-default"])},[n("label",P,u(e.label),1),n("div",q,[e.type==="select"?(o(),d("div",z,[m(n("select",{class:"admin-select","onUpdate:modelValue":s=>l.value[e.key]=s,onChange:s=>p(e.key,l.value[e.key])},[e.allOption?(o(),d("option",G,u(e.allOption),1)):h("",!0),(o(!0),d(f,null,k(e.options,s=>(o(),d("option",{key:s.value,value:s.value},u(s.label),9,J))),128))],40,H),[[w,l.value[e.key]]]),a[2]||(a[2]=n("div",{class:"admin-select-arrow"},[n("i",{class:"fas fa-chevron-down"})],-1))])):e.type==="text"?m((o(),d("input",{key:1,class:"admin-input",type:"text",placeholder:e.placeholder,"onUpdate:modelValue":s=>l.value[e.key]=s,onInput:s=>p(e.key,l.value[e.key])},null,40,K)),[[v,l.value[e.key]]]):e.type==="date"?m((o(),d("input",{key:2,class:"admin-input",type:"date","onUpdate:modelValue":s=>l.value[e.key]=s,onChange:s=>p(e.key,l.value[e.key])},null,40,Q)),[[v,l.value[e.key]]]):e.type==="number"?m((o(),d("input",{key:3,class:"admin-input",type:"number",placeholder:e.placeholder,"onUpdate:modelValue":s=>l.value[e.key]=s,onInput:s=>p(e.key,l.value[e.key])},null,40,W)),[[v,l.value[e.key]]]):h("",!0)])],2))),128))]),i.showResetButton?(o(),d("div",X,[n("button",{class:g(["admin-btn admin-btn-secondary",{"admin-btn-loading":i.loading}]),onClick:F},[a[3]||(a[3]=n("i",{class:"fas fa-undo"},null,-1)),C(" "+u(i.resetButtonText),1)],2)])):h("",!0)])])]),i.showStatusBar&&(i.totalItems>0||_.value)?(o(),d("div",Y,[n("div",Z,[n("div",L,[n("span",ee,u(i.totalItems),1),n("span",te,u(i.itemName)+" found",1),_.value?(o(),d("div",ae,[a[5]||(a[5]=n("span",{class:"admin-filters-label"},"with filters:",-1)),(o(!0),d(f,null,k(x.value,(e,s)=>(o(),d("span",{key:s,class:"admin-filter-tag"},[C(u(U(s))+": "+u(N(s,e))+" ",1),n("button",{class:"admin-filter-tag-remove",onClick:b=>I(s)},a[4]||(a[4]=[n("i",{class:"fas fa-times"},null,-1)]),8,se)]))),128))])):h("",!0)])])])):h("",!0)],64))}},de=O(le,[["__scopeId","data-v-75a39a88"]]);export{de as S};
