/* Main Page */
.main-page {
box-sizing: border-box;

position: relative;
width: 3999px;
height: 9089px;

background: #464343;
border: 1px solid rgba(0, 0, 0, 0.1);
border-radius: 2px;
}

.catalog {

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 9.33045px;
gap: 18.66px;

width: 156.75px;
height: 55.98px;

border: 1.86609px solid #000000;
border-radius: 13.9957px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
/* Frame 67 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 9.33045px;
gap: 18.66px;

width: 156.75px;
height: 55.98px;

border: 1.86609px solid #000000;
border-radius: 13.9957px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;

}


