import{_ as h,c as l,o,k as d,a as t,d as c,t as i,L as p,n as u,F as y,p as b}from"./index-L-hJxM_5.js";const _={name:"UploadProgress",props:{uploads:{type:Array,default:()=>[]},showOverallProgress:{type:Boolean,default:!0}},emits:["retry-upload","cancel-upload","retry-all-failed","cancel-all","clear-completed"],data(){return{retryingAll:!1}},computed:{totalUploads(){return this.uploads.length},completedUploads(){return this.uploads.filter(e=>e.status==="completed").length},overallProgress(){if(this.totalUploads===0)return 0;const e=this.uploads.reduce((r,n)=>n.status==="completed"?r+100:n.status==="uploading"?r+n.progress:r,0);return Math.round(e/this.totalUploads)},overallProgressClass(){return this.hasFailedUploads?"bg-warning":this.completedUploads===this.totalUploads?"bg-success":"bg-primary"},overallProgressText(){return this.hasFailedUploads?"Деякі завантаження не вдалися":this.completedUploads===this.totalUploads?"Всі завантаження завершені":"Завантаження в процесі..."},hasFailedUploads(){return this.uploads.some(e=>e.status==="error")},hasActiveUploads(){return this.uploads.some(e=>e.status==="uploading"||e.status==="pending")}},methods:{getUploadIcon(e){switch(e.status){case"completed":return"fas fa-check-circle text-success";case"error":return"fas fa-exclamation-circle text-danger";case"uploading":return"fas fa-spinner fa-spin text-primary";case"pending":return"fas fa-clock text-muted";default:return"fas fa-file-image text-muted"}},getStatusClass(e){switch(e){case"completed":return"text-success";case"error":return"text-danger";case"pending":return"text-muted";default:return"text-muted"}},getStatusMessage(e){switch(e.status){case"completed":return"Завантажено успішно";case"error":return e.error||"Помилка завантаження";case"pending":return"Очікує завантаження";case"cancelled":return"Скасовано";default:return"Невідомий статус"}},formatFileSize(e){if(e===0)return"0 Bytes";const r=1024,n=["Bytes","KB","MB","GB"],m=Math.floor(Math.log(e)/Math.log(r));return parseFloat((e/Math.pow(r,m)).toFixed(2))+" "+n[m]},formatSpeed(e){return e?this.formatFileSize(e)+"/s":""},formatTimeRemaining(e){return!e||e===1/0?"":e<60?`${Math.round(e)}с`:e<3600?`${Math.round(e/60)}хв`:`${Math.round(e/3600)}год`},retryUpload(e){this.$emit("retry-upload",e)},cancelUpload(e){this.$emit("cancel-upload",e)},async retryAllFailed(){this.retryingAll=!0,this.$emit("retry-all-failed"),setTimeout(()=>{this.retryingAll=!1},1e3)},cancelAllUploads(){this.$emit("cancel-all")},clearCompleted(){this.$emit("clear-completed")}}},v={class:"upload-progress-container"},U={key:0,class:"overall-progress mb-3"},x={class:"d-flex justify-content-between align-items-center mb-2"},k={class:"badge bg-primary"},C={class:"progress mb-2"},F={class:"d-flex justify-content-between"},A={class:"text-muted"},w={class:"text-muted"},M={class:"individual-uploads"},P={class:"upload-header"},S={class:"upload-info"},B={class:"upload-details"},z={class:"upload-filename"},T={class:"upload-meta"},I={class:"upload-actions"},N=["onClick","disabled"],R=["onClick"],V={key:0,class:"upload-progress"},j={class:"progress"},L={class:"progress-details"},O={class:"text-muted"},D={class:"text-muted"},E={key:1,class:"upload-status"},G={key:1,class:"upload-actions-bar mt-3"},K=["disabled"];function q(e,r,n,m,g,a){return o(),l("div",v,[n.showOverallProgress?(o(),l("div",U,[t("div",x,[r[3]||(r[3]=t("h6",{class:"mb-0"},[t("i",{class:"fas fa-cloud-upload-alt me-2"}),c(" Завантаження зображень ")],-1)),t("span",k,i(a.completedUploads)+"/"+i(a.totalUploads),1)]),t("div",C,[t("div",{class:u(["progress-bar",a.overallProgressClass]),style:p({width:a.overallProgress+"%"}),role:"progressbar"},null,6)]),t("div",F,[t("small",A,i(a.overallProgressText),1),t("small",w,i(a.overallProgress)+"%",1)])])):d("",!0),t("div",M,[(o(!0),l(y,null,b(n.uploads,s=>(o(),l("div",{key:s.id,class:u(["upload-item",{"upload-completed":s.status==="completed","upload-error":s.status==="error"}])},[t("div",P,[t("div",S,[t("i",{class:u(["upload-icon",a.getUploadIcon(s)])},null,2),t("div",B,[t("div",z,i(s.filename),1),t("div",T,i(a.formatFileSize(s.size))+" • "+i(s.type),1)])]),t("div",I,[s.status==="error"&&s.retryable?(o(),l("button",{key:0,type:"button",class:"btn btn-sm btn-outline-primary",onClick:f=>a.retryUpload(s.id),disabled:s.retrying},r[4]||(r[4]=[t("i",{class:"fas fa-redo"},null,-1)]),8,N)):d("",!0),s.status!=="completed"?(o(),l("button",{key:1,type:"button",class:"btn btn-sm btn-outline-danger",onClick:f=>a.cancelUpload(s.id)},r[5]||(r[5]=[t("i",{class:"fas fa-times"},null,-1)]),8,R)):d("",!0)])]),s.status==="uploading"?(o(),l("div",V,[t("div",j,[t("div",{class:"progress-bar progress-bar-striped progress-bar-animated",style:p({width:s.progress+"%"})},null,4)]),t("div",L,[t("small",O,i(s.progress)+"% • "+i(a.formatSpeed(s.speed)),1),t("small",D,i(a.formatTimeRemaining(s.timeRemaining)),1)])])):(o(),l("div",E,[t("div",{class:u(["status-message",a.getStatusClass(s.status)])},i(a.getStatusMessage(s)),3)]))],2))),128))]),n.uploads.length>0?(o(),l("div",G,[a.hasFailedUploads?(o(),l("button",{key:0,type:"button",class:"btn btn-outline-primary btn-sm me-2",onClick:r[0]||(r[0]=(...s)=>a.retryAllFailed&&a.retryAllFailed(...s)),disabled:g.retryingAll},r[6]||(r[6]=[t("i",{class:"fas fa-redo me-1"},null,-1),c(" Повторити невдалі ")]),8,K)):d("",!0),a.hasActiveUploads?(o(),l("button",{key:1,type:"button",class:"btn btn-outline-danger btn-sm me-2",onClick:r[1]||(r[1]=(...s)=>a.cancelAllUploads&&a.cancelAllUploads(...s))},r[7]||(r[7]=[t("i",{class:"fas fa-stop me-1"},null,-1),c(" Зупинити всі ")]))):d("",!0),t("button",{type:"button",class:"btn btn-outline-secondary btn-sm",onClick:r[2]||(r[2]=(...s)=>a.clearCompleted&&a.clearCompleted(...s))},r[8]||(r[8]=[t("i",{class:"fas fa-trash me-1"},null,-1),c(" Очистити завершені ")]))])):d("",!0)])}const J=h(_,[["render",q],["__scopeId","data-v-83d814b1"]]);export{J as U};
