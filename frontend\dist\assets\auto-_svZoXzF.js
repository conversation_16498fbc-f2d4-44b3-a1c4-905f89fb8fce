import{Chart as e,registerables as r}from"./chart-DFPvZH9M.js";import{Animation as a,Animations as n,ArcElement as i,BarController as m,BarElement as s,BasePlatform as c,BasicPlatform as C,BubbleController as S,CategoryScale as f,Colors as g,DatasetController as u,Decimation as d,DomPlatform as P,DoughnutController as p,Element as L,Filler as T,Interaction as B,Legend as E,LineController as b,LineElement as A,LinearScale as D,LogarithmicScale as h,PieController as y,PointElement as x,PolarAreaController as R,RadarController as _,RadialLinearScale as k,Scale as F,ScatterController as I,SubTitle as j,Ticks as q,TimeScale as v,TimeSeriesScale as w,Title as z,Tooltip as G,_adapters as H,_detectPlatform as J,animator as K,controllers as M,defaults as N,elements as O,layouts as Q,plugins as U,registry as V,scales as W}from"./chart-DFPvZH9M.js";e.register(...r);export{a as Animation,n as Animations,i as ArcElement,m as BarController,s as BarElement,c as BasePlatform,C as BasicPlatform,S as BubbleController,f as CategoryScale,e as Chart,g as Colors,u as DatasetController,d as Decimation,P as DomPlatform,p as DoughnutController,L as Element,T as Filler,B as Interaction,E as Legend,b as LineController,A as LineElement,D as LinearScale,h as LogarithmicScale,y as PieController,x as PointElement,R as PolarAreaController,_ as RadarController,k as RadialLinearScale,F as Scale,I as ScatterController,j as SubTitle,q as Ticks,v as TimeScale,w as TimeSeriesScale,z as Title,G as Tooltip,H as _adapters,J as _detectPlatform,K as animator,M as controllers,e as default,N as defaults,O as elements,Q as layouts,U as plugins,r as registerables,V as registry,W as scales};
