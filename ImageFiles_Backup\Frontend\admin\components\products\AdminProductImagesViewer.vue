<template>
  <AdminCard 
    title="Product Images" 
    :loading="loading"
    shadow="default"
  >
    <template #actions>
      <button 
        v-if="editable"
        class="admin-btn admin-btn-xs admin-btn-secondary"
        @click="$emit('edit')"
      >
        <i class="fas fa-edit"></i>
        Edit
      </button>
    </template>

    <div class="admin-images-container">
      <!-- Main Image -->
      <div v-if="product?.metaImage" class="admin-main-image-section">
        <h4 class="admin-image-section-title">
          <i class="fas fa-image"></i>
          Main Image
        </h4>
        <div class="admin-main-image-container">
          <div class="admin-image-wrapper">
            <img 
              :src="product.metaImage" 
              :alt="product.name || 'Product image'"
              class="admin-main-image"
              @click="openImageModal(product.metaImage)"
              @error="handleImageError"
            />
            <div class="admin-image-overlay">
              <button 
                class="admin-image-action"
                @click="openImageModal(product.metaImage)"
                title="View full size"
              >
                <i class="fas fa-search-plus"></i>
              </button>
              <button 
                class="admin-image-action"
                @click="downloadImage(product.metaImage, 'main-image')"
                title="Download image"
              >
                <i class="fas fa-download"></i>
              </button>
            </div>
          </div>
          <div class="admin-image-info">
            <div class="admin-image-meta">
              <span class="admin-image-label">URL:</span>
              <a 
                :href="product.metaImage" 
                target="_blank" 
                class="admin-image-link"
                rel="noopener noreferrer"
              >
                {{ truncateUrl(product.metaImage) }}
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Images (if any) -->
      <div v-if="additionalImages.length > 0" class="admin-additional-images-section">
        <h4 class="admin-image-section-title">
          <i class="fas fa-images"></i>
          Additional Images ({{ additionalImages.length }})
        </h4>
        <div class="admin-images-grid">
          <div 
            v-for="(image, index) in additionalImages" 
            :key="index"
            class="admin-image-item"
          >
            <div class="admin-image-wrapper">
              <img 
                :src="image.url" 
                :alt="image.alt || `Product image ${index + 1}`"
                class="admin-grid-image"
                @click="openImageModal(image.url)"
                @error="handleImageError"
              />
              <div class="admin-image-overlay">
                <button 
                  class="admin-image-action"
                  @click="openImageModal(image.url)"
                  title="View full size"
                >
                  <i class="fas fa-search-plus"></i>
                </button>
                <button 
                  class="admin-image-action"
                  @click="downloadImage(image.url, `image-${index + 1}`)"
                  title="Download image"
                >
                  <i class="fas fa-download"></i>
                </button>
              </div>
            </div>
            <div class="admin-image-info">
              <div class="admin-image-meta">
                <span class="admin-image-label">Type:</span>
                <span class="admin-image-value">{{ image.type || 'Additional' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Images State -->
      <div v-if="!product?.metaImage && additionalImages.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-image"></i>
        </div>
        <h4 class="admin-empty-title">No Images</h4>
        <p class="admin-empty-description">
          This product doesn't have any images uploaded.
        </p>
        <button 
          v-if="editable"
          class="admin-btn admin-btn-primary"
          @click="$emit('upload-image')"
        >
          <i class="fas fa-upload"></i>
          Upload Image
        </button>
      </div>
    </div>

    <!-- Image Modal -->
    <AdminModal 
      v-model="showImageModal" 
      :title="modalImageTitle"
      size="lg"
      centered
    >
      <div class="admin-image-modal-content">
        <img 
          v-if="modalImageUrl" 
          :src="modalImageUrl" 
          :alt="modalImageTitle"
          class="admin-modal-image"
        />
      </div>
      
      <template #footer>
        <button 
          class="admin-btn admin-btn-secondary"
          @click="showImageModal = false"
        >
          Close
        </button>
        <button 
          class="admin-btn admin-btn-primary"
          @click="downloadImage(modalImageUrl, 'full-size-image')"
        >
          <i class="fas fa-download"></i>
          Download
        </button>
      </template>
    </AdminModal>
  </AdminCard>
</template>

<script setup>
import { ref, computed } from 'vue';
import AdminCard from '../common/AdminCard.vue';
import AdminModal from '../common/AdminModal.vue';

// Props
const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['edit', 'upload-image']);

// Reactive data
const showImageModal = ref(false);
const modalImageUrl = ref('');
const modalImageTitle = ref('');

// Computed
const additionalImages = computed(() => {
  if (!props.product?.images || !Array.isArray(props.product.images)) {
    return [];
  }

  // Convert API images to display format
  return props.product.images.map(image => ({
    id: image.id || image.Id,
    url: image.image || image.Image || image.imageUrl || image.ImageUrl,
    alt: image.altText || image.AltText || 'Product image',
    isMain: image.isMain || image.IsMain || false,
    order: image.order || image.Order || 0
  })).sort((a, b) => a.order - b.order);
});

// Methods
const openImageModal = (imageUrl) => {
  modalImageUrl.value = imageUrl;
  modalImageTitle.value = props.product?.name || 'Product Image';
  showImageModal.value = true;
};

const downloadImage = async (imageUrl, filename) => {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading image:', error);
    // You could show a toast notification here
  }
};

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg'; // Fallback image
  event.target.alt = 'Image not available';
};

const truncateUrl = (url) => {
  if (!url) return '';
  if (url.length <= 50) return url;
  return url.substring(0, 47) + '...';
};
</script>

<style scoped>
.admin-images-container {
  min-height: 200px;
}

/* Section Titles */
.admin-image-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-md) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-image-section-title i {
  color: var(--admin-primary);
}

/* Main Image */
.admin-main-image-section {
  margin-bottom: var(--admin-space-xl);
}

.admin-main-image-container {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-image-wrapper {
  position: relative;
  display: inline-block;
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  box-shadow: var(--admin-shadow-md);
  transition: all var(--admin-transition-base);
}

.admin-image-wrapper:hover {
  box-shadow: var(--admin-shadow-lg);
}

.admin-main-image {
  width: 100%;
  max-width: 400px;
  height: 300px;
  object-fit: cover;
  cursor: pointer;
  transition: transform var(--admin-transition-base);
}

.admin-main-image:hover {
  transform: scale(1.02);
}

/* Image Overlay */
.admin-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-sm);
  opacity: 0;
  transition: opacity var(--admin-transition-base);
}

.admin-image-wrapper:hover .admin-image-overlay {
  opacity: 1;
}

.admin-image-action {
  background: var(--admin-bg-primary);
  border: none;
  border-radius: var(--admin-radius-sm);
  padding: var(--admin-space-sm);
  color: var(--admin-text-primary);
  cursor: pointer;
  transition: all var(--admin-transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.admin-image-action:hover {
  background: var(--admin-primary);
  color: var(--admin-text-white);
  transform: scale(1.1);
}

/* Image Info */
.admin-image-info {
  padding: var(--admin-space-sm);
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-sm);
}

.admin-image-meta {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
}

.admin-image-label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-muted);
}

.admin-image-value {
  color: var(--admin-text-primary);
}

.admin-image-link {
  color: var(--admin-link);
  text-decoration: none;
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-xs);
  transition: color var(--admin-transition-base);
}

.admin-image-link:hover {
  color: var(--admin-link-hover);
}

/* Additional Images Grid */
.admin-additional-images-section {
  border-top: 1px solid var(--admin-border-light);
  padding-top: var(--admin-space-lg);
}

.admin-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--admin-space-md);
}

.admin-image-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-sm);
}

.admin-grid-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  cursor: pointer;
  transition: transform var(--admin-transition-base);
}

.admin-grid-image:hover {
  transform: scale(1.02);
}

/* Empty State */
.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-xl);
  text-align: center;
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-text-muted);
  margin-bottom: var(--admin-space-md);
}

.admin-empty-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-empty-description {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
  margin: 0 0 var(--admin-space-lg) 0;
  max-width: 300px;
}

/* Modal */
.admin-image-modal-content {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 70vh;
  overflow: hidden;
}

.admin-modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--admin-radius-md);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-main-image {
    max-width: 100%;
    height: 250px;
  }
  
  .admin-images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--admin-space-sm);
  }
  
  .admin-grid-image {
    height: 120px;
  }
  
  .admin-image-overlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
