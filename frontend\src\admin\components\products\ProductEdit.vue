<template>
  <div class="admin-page-container">
    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="admin-loading-text">Loading product data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-error-state">
      <AdminCard variant="danger" title="Error Loading Product">
        <div class="admin-error-content">
          <i class="fas fa-exclamation-triangle admin-error-icon"></i>
          <p class="admin-error-message">{{ error }}</p>
          <button class="admin-btn admin-btn-primary" @click="loadProduct">
            <i class="fas fa-redo"></i>
            Try Again
          </button>
        </div>
      </AdminCard>
    </div>

    <!-- Form Content -->
    <div v-else-if="isMounted" class="admin-page-content">
      <!-- Header -->
      <AdminProductHeader 
        :mode="isCreate ? 'create' : 'edit'"
        :product="formData"
        :title="isCreate ? 'Create New Product' : `Edit Product: ${formData.name || 'Untitled'}`"
        :subtitle="isCreate ? 'Add a new product to your catalog' : 'Update product information and settings'"
        :can-save="canSave"
        :can-create="canCreate"
        :saving="saving"
        :creating="saving && isCreate"
        @save="handleSubmit"
        @create="handleSubmit"
      />

      <!-- Form Content -->
      <form @submit.prevent="handleSubmit" class="admin-form">
        <div class="admin-form-grid">
          <!-- Basic Information Section -->
          <AdminFormSection 
            title="Basic Information" 
            description="Enter the core product details"
            variant="card"
            :required="true"
          >
            <div class="admin-form-row" v-if="formData && !loading">
              <div class="admin-form-col">
                <EnhancedCompanySelector
                  :model-value="formData.companyId"
                  @update:model-value="(value) => formData.companyId = value"
                  label="Company"
                  placeholder="Select company..."
                  :required="true"
                  :error-message="errors.companyId || ''"
                  help-text="Select the company that owns this product"
                  @change="handleCompanyChange"
                />
              </div>
              <div class="admin-form-col">
                <EnhancedCategorySelector
                  :model-value="formData.categoryId"
                  @update:model-value="(value) => formData.categoryId = value"
                  label="Category"
                  placeholder="Search categories..."
                  :required="true"
                  :error-message="errors.categoryId || ''"
                  help-text="Choose the most appropriate category"
                  @change="handleCategoryChange"
                  @select="handleCategorySelect"
                />
              </div>
            </div>

            <div class="admin-form-row" v-if="formData">
              <div class="admin-form-col">
                <label class="admin-form-label admin-form-label--required">Product Name</label>
                <input
                  v-model="formData.name"
                  type="text"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.name }"
                  placeholder="Enter product name"
                  required
                  @input="handleNameChange"
                />
                <div v-if="errors.name" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.name }}
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Product Slug</label>
                <input
                  v-model="formData.slug"
                  type="text"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.slug }"
                  placeholder="Auto-generated from name"
                  @input="handleSlugChange"
                />
                <div v-if="errors.slug" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.slug }}
                </div>
                <div class="admin-form-help">
                  Leave empty to auto-generate from product name
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Description</label>
                <textarea
                  v-model="formData.description"
                  class="admin-form-textarea"
                  :class="{ 'admin-form-textarea--error': errors.description }"
                  placeholder="Enter product description"
                  rows="4"
                ></textarea>
                <div v-if="errors.description" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.description }}
                </div>
              </div>
            </div>
          </AdminFormSection>

          <!-- Pricing & Inventory Section -->
          <AdminFormSection 
            title="Pricing & Inventory" 
            description="Set product pricing and stock information"
            variant="card"
          >
            <div class="admin-form-row">
              <div class="admin-form-col admin-form-col--half">
                <label class="admin-form-label admin-form-label--required">Price</label>
                <div class="admin-input-group">
                  <input
                    v-model="formData.priceAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    class="admin-form-input"
                    :class="{ 'admin-form-input--error': errors.priceAmount }"
                    placeholder="0.00"
                    required
                  />
                  <select
                    v-model="formData.priceCurrency"
                    class="admin-form-select admin-form-select--addon"
                  >
                    <option value="UAH">UAH</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                  </select>
                </div>
                <div v-if="errors.priceAmount" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.priceAmount }}
                </div>
              </div>
              
              <div class="admin-form-col admin-form-col--half">
                <label class="admin-form-label">Stock Quantity</label>
                <input
                  v-model="formData.stock"
                  type="number"
                  min="0"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.stock }"
                  placeholder="0"
                />
                <div v-if="errors.stock" class="admin-form-error">
                  <i class="fas fa-exclamation-triangle"></i>
                  {{ errors.stock }}
                </div>
                <div class="admin-form-help">
                  Number of items available in stock
                </div>
              </div>
            </div>
          </AdminFormSection>

          <!-- Product Attributes Section -->
          <AdminFormSection 
            title="Product Attributes" 
            description="Add custom attributes like color, size, material, etc."
            variant="card"
          >
            <EnhancedProductAttributesEditor
              v-model="formData.attributes"
              @update:model-value="handleAttributesChange"
            />
          </AdminFormSection>

          <!-- Product Images Section -->
          <AdminFormSection
            title="Product Images & Media"
            description="Upload and manage all product images including meta image for SEO"
            variant="card"
          >
            <ProductImageUploader
              v-if="isMounted && formData"
              ref="productImageUploader"
              :entity-id="currentProductId || 'temp-id'"
              :current-images="formData.images || []"
              :is-create="!currentProductId"
              @images-changed="handleImagesChanged"
              @images-reordered="handleImagesReordered"
            />

            <!-- Meta Image Section -->
            <div class="meta-image-section">
              <h4 class="meta-title">Meta Image (SEO)</h4>
              <p class="meta-description">Upload a meta image for SEO and social media sharing</p>

              <SimpleMetaImageUploader
                v-if="isMounted && formData"
                ref="metaImageUploader"
                :entity-type="'product'"
                :entity-id="currentProductId || 'temp-id'"
                :current-image="formData.metaImage"
                :social-title="formData.metaTitle || formData.name"
                :social-description="formData.metaDescription || formData.description"
                @meta-image-changed="handleMetaImageChanged"
              />
            </div>
          </AdminFormSection>

          <!-- SEO & Metadata Section -->
          <AdminFormSection 
            title="SEO & Metadata" 
            description="Optimize your product for search engines"
            variant="card"
          >
            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Meta Title</label>
                <input
                  v-model="formData.metaTitle"
                  type="text"
                  class="admin-form-input"
                  :class="{ 'admin-form-input--error': errors.metaTitle }"
                  placeholder="SEO title for search engines"
                  maxlength="60"
                />
                <div class="admin-form-help">
                  <span class="admin-text-muted">
                    {{ (formData.metaTitle || '').length }}/60 characters
                  </span>
                </div>
              </div>
            </div>

            <div class="admin-form-row">
              <div class="admin-form-col">
                <label class="admin-form-label">Meta Description</label>
                <textarea
                  v-model="formData.metaDescription"
                  class="admin-form-textarea"
                  :class="{ 'admin-form-textarea--error': errors.metaDescription }"
                  placeholder="Brief description for search engine results"
                  rows="3"
                  maxlength="160"
                ></textarea>
                <div class="admin-form-help">
                  <span class="admin-text-muted">
                    {{ (formData.metaDescription || '').length }}/160 characters
                  </span>
                </div>
              </div>
            </div>


          </AdminFormSection>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, onUnmounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AdminCard from '../common/AdminCard.vue';
import AdminFormSection from '../common/AdminFormSection.vue';
import AdminProductHeader from './AdminProductHeader.vue';
import EnhancedCompanySelector from './EnhancedCompanySelector.vue';
import EnhancedCategorySelector from './EnhancedCategorySelector.vue';
import EnhancedProductAttributesEditor from './EnhancedProductAttributesEditor.vue';
import ProductImageUploader from '../product/ProductImageUploader.vue';
import SimpleMetaImageUploader from '../product/SimpleMetaImageUploader.vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  productId: {
    type: [String, Number],
    default: null
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['save', 'cancel', 'created', 'updated']);

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const errors = ref({});
const isMounted = ref(false);

// Component refs
const productImageUploader = ref(null);
const metaImageUploader = ref(null);

// Meta image URL
const metaImageUrl = ref(null);

// Form data - ініціалізуємо з безпечними значеннями
const formData = ref({
  name: '',
  slug: '',
  description: '',
  companyId: null,
  categoryId: null,
  priceAmount: null,
  priceCurrency: 'UAH',
  stock: 0,
  attributes: {},
  images: [],
  metaTitle: '',
  metaDescription: '',
  metaImage: '',

  status: 0 // Default to pending
});

// Переконуємося, що formData завжди має правильну структуру
const ensureFormDataIntegrity = () => {
  if (!formData.value) {
    formData.value = {};
  }

  // Переконуємося, що всі необхідні поля існують
  const defaults = {
    name: '',
    slug: '',
    description: '',
    companyId: null,
    categoryId: null,
    priceAmount: null,
    priceCurrency: 'UAH',
    stock: 0,
    attributes: {},
    images: [],
    metaTitle: '',
    metaDescription: '',
    metaImage: '',
    status: 0
  };

  Object.keys(defaults).forEach(key => {
    if (formData.value[key] === undefined) {
      formData.value[key] = defaults[key];
    }
  });
};

// Original data for comparison
const originalData = ref({});



// Computed
const currentProductId = computed(() => {
  return props.productId || route.params.id;
});

const canSave = computed(() => {
  // Додаємо захист від null/undefined
  if (!formData.value) return false;

  return formData.value.name &&
         formData.value.companyId &&
         formData.value.categoryId &&
         formData.value.priceAmount !== null &&
         !saving.value;
});

const canCreate = computed(() => {
  return canSave.value && props.isCreate;
});

const hasChanges = computed(() => {
  if (props.isCreate) return true;
  if (!formData.value || !originalData.value) return false;

  return JSON.stringify(formData.value) !== JSON.stringify(originalData.value);
});

// Methods
const loadProduct = async () => {
  try {
    // Check if component is still mounted
    if (!isMounted.value) {
      return;
    }

    // Переконуємося, що formData має правильну структуру
    ensureFormDataIntegrity();

    if (props.isCreate) {
      // Set default values for new product
      formData.value = {
        name: '',
        slug: '',
        description: '',
        companyId: null,
        categoryId: null,
        priceAmount: null,
        priceCurrency: 'UAH',
        stock: 0,
        attributes: {},
        images: [],
        metaTitle: '',
        metaDescription: '',
        metaImage: '',
        status: 0
      };
      originalData.value = { ...formData.value };

      // Переконуємося, що структура правильна після ініціалізації
      await nextTick();
      if (isMounted.value) {
        ensureFormDataIntegrity();
      }
      return;
    }

    // Завантажуємо існуючий продукт
    loading.value = true;
    error.value = null;

    const response = await productsService.getProductByIdWithImages(currentProductId.value);

    // Check if component is still mounted after async operation
    if (!isMounted.value) {
      return;
    }

    const product = response.data || response;

    formData.value = {
      ...product,
      priceAmount: product.priceAmount || null,
      priceCurrency: product.priceCurrency || 'UAH',
      stock: product.stock || 0,
      attributes: convertAttributesFromAPI(product.attributes),
      images: convertImagesFromAPI(product.images || []),
      metaTitle: product.metaTitle || '',
      metaDescription: product.metaDescription || '',
      metaImage: product.metaImage || '',

    };

    originalData.value = { ...formData.value };

    // Переконуємося, що структура правильна після завантаження
    await nextTick();
    if (isMounted.value) {
      ensureFormDataIntegrity();
    }

  } catch (err) {
    console.error('Error loading product:', err);

    // Only update error state if component is still mounted
    if (isMounted.value) {
      error.value = err.message || 'Failed to load product';
      // У випадку помилки, переконуємося, що formData має правильну структуру
      ensureFormDataIntegrity();
    }
  } finally {
    // Only update loading state if component is still mounted
    if (isMounted.value) {
      loading.value = false;
    }
  }
};

const validateForm = () => {
  errors.value = {};

  console.log('Validating form with data:', {
    name: formData.value.name,
    companyId: formData.value.companyId,
    categoryId: formData.value.categoryId,
    priceAmount: formData.value.priceAmount,
    metaImage: formData.value.metaImage,
    hasMetaImageChanges: metaImageUploader.value?.hasLocalChanges
  });

  if (!formData.value.name?.trim()) {
    errors.value.name = 'Product name is required';
  }

  if (!formData.value.companyId) {
    errors.value.companyId = 'Company selection is required';
  }

  if (!formData.value.categoryId) {
    errors.value.categoryId = 'Category selection is required';
  }

  if (!formData.value.priceAmount || formData.value.priceAmount <= 0) {
    errors.value.priceAmount = 'Valid price is required';
  }

  if (formData.value.slug && !/^[a-z0-9-]+$/.test(formData.value.slug)) {
    errors.value.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
  }

  // Skip meta image validation if it's a blob URL (local file) or if meta image uploader has pending changes
  if (formData.value.metaImage &&
      !formData.value.metaImage.startsWith('blob:') &&
      !isValidHttpUrl(formData.value.metaImage) &&
      !(metaImageUploader.value?.hasLocalChanges)) {
    errors.value.metaImage = 'Meta image must be a valid HTTP/HTTPS URL';
  }

  const isValid = Object.keys(errors.value).length === 0;
  console.log('Form validation result:', isValid, 'Errors:', errors.value);

  return isValid;
};

const generateSlug = (name) => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

const convertAttributesFromAPI = (attributes) => {
  if (!attributes) return {};

  let parsedAttributes = attributes;

  // If attributes is a string, parse it
  if (typeof attributes === 'string') {
    try {
      parsedAttributes = JSON.parse(attributes);
    } catch (e) {
      console.error('Error parsing attributes:', e);
      return {};
    }
  }

  // Convert from API format (key: "value1,value2") to our format (key: [value1, value2])
  const result = {};
  Object.keys(parsedAttributes).forEach(key => {
    const value = parsedAttributes[key];
    if (typeof value === 'string') {
      // Split by comma and trim whitespace
      result[key] = value.split(',').map(v => v.trim()).filter(v => v);
    } else if (Array.isArray(value)) {
      // Already in array format
      result[key] = value;
    } else {
      // Convert to string and then to array
      result[key] = [String(value)];
    }
  });

  return result;
};

const convertImagesFromAPI = (apiImages) => {
  if (!Array.isArray(apiImages)) return [];

  return apiImages.map(apiImage => ({
    id: apiImage.id || apiImage.Id,
    url: apiImage.image || apiImage.Image || apiImage.imageUrl || apiImage.ImageUrl,
    name: apiImage.altText || apiImage.AltText || `Image ${apiImage.order || apiImage.Order || 1}`,
    uploaded: true,
    isMain: apiImage.isMain || apiImage.IsMain || false,
    order: apiImage.order || apiImage.Order || 0,
    altText: apiImage.altText || apiImage.AltText || ''
  }));
};

const isValidHttpUrl = (string) => {
  if (!string || typeof string !== 'string') return false;

  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
};

const refreshProductData = async () => {
  if (!currentProductId.value) return;

  try {
    console.log('Refreshing product data...');

    // Reload product data
    const productData = await productsService.getProductByIdWithImages(currentProductId.value);

    if (productData) {
      // Update form data
      Object.assign(formData.value, productData);

      // Update images - force reactivity
      if (productData.images) {
        formData.value.images = [...productData.images];
        console.log('Updated product images:', formData.value.images.length);

        // Update the ProductImageUploader component
        if (productImageUploader.value && typeof productImageUploader.value.refreshImages === 'function') {
          productImageUploader.value.refreshImages(formData.value.images);
        }
      }

      // Update meta image
      console.log('Product data metaImage:', productData.metaImage);

      // Always update metaImage from server data (could be null if removed)
      metaImageUrl.value = productData.metaImage || null;
      formData.value.metaImage = productData.metaImage || null;

      console.log('Updated meta image URL:', metaImageUrl.value);

      // Update the SimpleMetaImageUploader component
      if (metaImageUploader.value && typeof metaImageUploader.value.updateCurrentImage === 'function') {
        metaImageUploader.value.updateCurrentImage(metaImageUrl.value);
      }

      console.log('Product data refreshed successfully');
    }
  } catch (error) {
    console.error('Error refreshing product data:', error);
    // Don't retry, just log the error
  }
};

const handleSubmit = async () => {
  console.log('handleSubmit called, current saving state:', saving.value);

  if (!validateForm()) {
    console.log('Form validation failed');
    return;
  }

  // Ensure component is still mounted and formData is valid
  if (!isMounted.value || !formData.value) {
    console.error('Component not mounted or formData is null');
    return;
  }

  // Check if already saving
  if (saving.value) {
    console.log('Already saving, ignoring submit request');
    return;
  }

  try {
    console.log('Setting saving to true');
    saving.value = true;

    console.log('Cleaning up blob URLs before processing...');

    // Clean up any blob URLs in form data
    if (formData.value.metaImage && formData.value.metaImage.startsWith('blob:')) {
      console.log('Removing blob URL from metaImage:', formData.value.metaImage);
      formData.value.metaImage = null;
    }

    // Auto-generate slug if empty
    if (!formData.value.slug && formData.value.name) {
      formData.value.slug = generateSlug(formData.value.name);
    }

    // Convert attributes from our format (key: [values]) to API format (key: "value1,value2")
    const convertedAttributes = {};
    if (formData.value.attributes && typeof formData.value.attributes === 'object') {
      Object.keys(formData.value.attributes).forEach(key => {
        const values = formData.value.attributes[key];
        if (Array.isArray(values)) {
          // Join array values with comma
          convertedAttributes[key] = values.join(', ');
        } else {
          // If it's already a string, use as is
          convertedAttributes[key] = String(values);
        }
      });
    }

    // Process pending image operations
    console.log('Processing pending image operations...');
    let imageResults = {};
    let metaImageResults = {};

    // Process product images
    if (productImageUploader.value &&
        productImageUploader.value.hasChanges &&
        typeof productImageUploader.value.hasChanges === 'function') {
      try {
        const hasChanges = productImageUploader.value.hasChanges();
        if (hasChanges &&
            productImageUploader.value.processChanges &&
            typeof productImageUploader.value.processChanges === 'function') {
          const result = await productImageUploader.value.processChanges();
          if (result.success) {
            imageResults = result;
            console.log('Product image operations completed:', imageResults);
          } else {
            throw new Error(`Image processing failed: ${result.errors.join(', ')}`);
          }
        }
      } catch (error) {
        console.error('Error processing product images:', error);
        throw new Error(`Image processing failed: ${error.message}`);
      }
    }

    // Process meta image
    if (metaImageUploader.value &&
        metaImageUploader.value.hasChanges &&
        typeof metaImageUploader.value.hasChanges === 'function') {
      try {
        const hasChanges = metaImageUploader.value.hasChanges();
        console.log('Meta image hasChanges:', hasChanges);

        if (hasChanges &&
            metaImageUploader.value.processPendingOperations &&
            typeof metaImageUploader.value.processPendingOperations === 'function') {
          console.log('Processing meta image operations...');
          const result = await metaImageUploader.value.processPendingOperations();
          console.log('Meta image processing result:', result);

          if (result && result.success) {
            metaImageResults = result;
            console.log('Meta image operations completed successfully:', metaImageResults);
          } else {
            console.warn('Meta image processing failed:', result?.errors || ['Unknown error']);
            // Don't throw error for meta image, just log warning
            // Set empty result to prevent undefined errors
            metaImageResults = { success: false, metaImage: null, errors: result?.errors || [] };
          }
        }
      } catch (error) {
        console.error('Error processing meta image:', error);
        // Don't throw error for meta image, just log warning - this should not block the save operation
      }
    }

    const productData = {
      ...formData.value,
      attributes: convertedAttributes,
      metaImage: metaImageResults.metaImage || formData.value.metaImage || null
    };

    console.log('Sending productData to backend:', productData);
    console.log('metaImage in productData:', productData.metaImage);

    let result;
    if (props.isCreate) {
      result = await productsService.createProduct(productData);
      emit('created', result);

      const productId = result.data?.id || result.id;

      // Process pending image operations after product creation
      if (productId &&
          productImageUploader.value &&
          productImageUploader.value.hasChanges &&
          typeof productImageUploader.value.hasChanges === 'function') {
        try {
          const hasChanges = productImageUploader.value.hasChanges();
          if (hasChanges) {
            console.log('Processing pending images for new product:', productId);

            // Update entity ID for the uploader
            if (productImageUploader.value.entityId !== undefined) {
              productImageUploader.value.entityId = productId;
            }

            if (productImageUploader.value.processChanges &&
                typeof productImageUploader.value.processChanges === 'function') {
              const imageResult = await productImageUploader.value.processChanges();
              if (!imageResult.success) {
                console.warn('Some image operations failed:', imageResult.errors);
              }
            }
          }
        } catch (error) {
          console.error('Error processing images after product creation:', error);
        }
      }

      // Process pending meta image operations after product creation
      if (productId &&
          metaImageUploader.value &&
          metaImageUploader.value.hasChanges &&
          typeof metaImageUploader.value.hasChanges === 'function') {
        try {
          const hasChanges = metaImageUploader.value.hasChanges();
          if (hasChanges) {
            console.log('Processing pending meta image for new product:', productId);

            // Update entity ID for the meta image uploader
            if (metaImageUploader.value.entityId !== undefined) {
              metaImageUploader.value.entityId = productId;
            }

            if (metaImageUploader.value.processPendingOperations &&
                typeof metaImageUploader.value.processPendingOperations === 'function') {
              const metaResult = await metaImageUploader.value.processPendingOperations();
              if (metaResult.errors.length > 0) {
                console.warn('Some meta image operations failed:', metaResult.errors);
              }
            }
          }
        } catch (error) {
          console.error('Error processing meta image after product creation:', error);
        }
      }

      // Redirect to edit page
      router.push(`/admin/products/${productId}/edit`);
    } else {
      result = await productsService.updateProduct(currentProductId.value, productData);
      emit('updated', result);

      console.log('Product updated successfully:', result);

      // Reset local changes in uploaders
      if (productImageUploader.value && typeof productImageUploader.value.resetChanges === 'function') {
        productImageUploader.value.resetChanges();
      }

      if (metaImageUploader.value && typeof metaImageUploader.value.resetLocalChanges === 'function') {
        metaImageUploader.value.resetLocalChanges();
      }

      // Wait a bit for backend to process, then refresh product data
      console.log('Waiting for backend processing before refresh...');
      await new Promise(resolve => setTimeout(resolve, 500)); // 0.5 second delay

      try {
        await refreshProductData();
      } catch (error) {
        console.error('Failed to refresh data, reloading page:', error);
        // If refresh fails, reload the page to ensure data consistency
        window.location.reload();
      }
    }

    emit('save', result);

    // Add 0.3 second delay and then refresh the page programmatically
    console.log('Waiting 0.3 seconds before page refresh...');
    setTimeout(() => {
      console.log('Refreshing page after successful save...');
      // Use window.location.href assignment to refresh without confirmation dialog
      // This doesn't trigger the beforeunload event like reload() does
      window.location.href = window.location.href.split('?')[0] + '?_t=' + Date.now();
    }, 300);

  } catch (err) {
    console.error('Error saving product:', err);

    // Only update error state if component is still mounted
    if (isMounted.value) {
      error.value = err.message || 'Failed to save product';
    }
  } finally {
    // Always reset saving state to prevent button from being stuck
    console.log('Resetting saving state to false');
    saving.value = false;
  }
};

// Event handlers
const handleNameChange = () => {
  if (!formData.value) {
    console.error('FormData not initialized when handling name change');
    return;
  }

  // Auto-generate slug if it's empty or matches the previous auto-generated slug
  if (!formData.value.slug || formData.value.slug === generateSlug(originalData.value?.name || '')) {
    formData.value.slug = generateSlug(formData.value.name || '');
  }

  // Auto-generate meta title if empty
  if (!formData.value.metaTitle) {
    formData.value.metaTitle = formData.value.name || '';
  }

  // Clear name error
  if (errors.value && errors.value.name) {
    delete errors.value.name;
  }
};

const handleSlugChange = () => {
  // Clear slug error
  if (errors.value.slug) {
    delete errors.value.slug;
  }
};

const handleCompanyChange = (companyId) => {
  if (!formData.value) {
    console.error('FormData not initialized when handling company change');
    return;
  }

  formData.value.companyId = companyId;
  if (errors.value && errors.value.companyId) {
    delete errors.value.companyId;
  }
};

const handleCategoryChange = (categoryId) => {
  if (!formData.value) {
    console.error('FormData not initialized when handling category change');
    return;
  }

  formData.value.categoryId = categoryId;
  if (errors.value && errors.value.categoryId) {
    delete errors.value.categoryId;
  }
};

const handleCategorySelect = (category) => {
  console.log('Category selected:', category);
  // Additional logic if needed when category is selected
};

const handleAttributesChange = (attributes) => {
  formData.value.attributes = attributes;
};

// New image management methods
const handleImagesChanged = (change) => {
  console.log('Images changed:', change);
  // Store pending changes for later processing
};

const handleMainImageChanged = (image) => {
  console.log('Main image changed:', image);
  // Update form data if needed
  if (image && image.imageUrl) {
    // Could update a main image field if needed
  }
};

const handleMetaImageChanged = (change) => {
  console.log('Meta image changed:', change);

  if (!formData.value) {
    console.error('FormData not available');
    return;
  }

  try {
    if (change.type === 'upload' && change.success) {
      // Update form data with new meta image URL
      formData.value.metaImage = change.url;
      console.log('Meta image URL updated:', change.url);
    } else if (change.type === 'remove' && change.success) {
      // Clear meta image from form data
      formData.value.metaImage = null;
      console.log('Meta image removed');
    } else if (change.type === 'error') {
      console.error('Meta image operation failed:', change.error);
      // Clear broken meta image URL
      formData.value.metaImage = null;
      metaImageUrl.value = null;
      console.log('Cleared broken meta image URL');
    } else if (change.type === 'removal') {
      // Handle removal request
      formData.value.metaImage = null;
      metaImageUrl.value = null;
      console.log('Meta image marked for removal');
    }
  } catch (error) {
    console.error('Error handling meta image change:', error);
  }
};

const handleImagesReordered = (images) => {
  console.log('Images reordered:', images);
  // Update form data with new order
  formData.value.images = images;
};





// Lifecycle
onMounted(async () => {
  try {
    isMounted.value = true;

    // Переконуємося, що formData має правильну структуру з самого початку
    ensureFormDataIntegrity();

    // Ensure DOM is ready before loading data
    await nextTick();

    // Check if component is still mounted before proceeding
    if (!isMounted.value) return;

    await loadProduct();

    // Add beforeunload listener
    window.addEventListener('beforeunload', beforeUnload);
  } catch (error) {
    console.error('Error in ProductEdit onMounted:', error);
    // У випадку помилки, переконуємося, що formData має правильну структуру
    if (isMounted.value) {
      ensureFormDataIntegrity();
    }
  }
});

// Watch for route changes
watch(() => route.params.id, async (newId) => {
  if (newId && !props.isCreate && isMounted.value) {
    await nextTick();
    await loadProduct();
  }
});



// Warn about unsaved changes
const beforeUnload = (event) => {
  if (hasChanges.value && !saving.value) {
    event.preventDefault();
    event.returnValue = '';
  }
};

onBeforeUnmount(() => {
  isMounted.value = false;

  // Clear refs to prevent memory leaks and null reference errors
  try {
    if (productImageUploader.value) {
      productImageUploader.value = null;
    }
  } catch (error) {
    console.error('Error clearing refs in onBeforeUnmount:', error);
  }
});

onUnmounted(() => {
  window.removeEventListener('beforeunload', beforeUnload);

  // Final cleanup
  try {
    productImageUploader.value = null;
  } catch (error) {
    console.error('Error in final cleanup:', error);
  }
});
</script>

<style scoped>
.admin-page-container {
  min-height: 100vh;
  background: var(--admin-bg-secondary);
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.admin-loading-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-lg);
  color: var(--admin-text-muted);
  margin: 0;
}

.admin-error-state {
  max-width: 600px;
  margin: var(--admin-space-xl) auto;
}

.admin-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--admin-space-md);
}

.admin-error-icon {
  font-size: 3rem;
  color: var(--admin-danger);
}

.admin-error-message {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  margin: 0;
  line-height: 1.6;
}

.admin-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--admin-space-lg);
}

.admin-form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
}

.admin-form-row {
  display: flex;
  gap: var(--admin-space-lg);
  align-items: flex-start;
}

.admin-form-col {
  flex: 1;
  min-width: 0;
}

.admin-form-col--half {
  flex: 0 0 calc(50% - var(--admin-space-lg) / 2);
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-form-input,
.admin-form-textarea,
.admin-form-select {
  width: 100%;
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-form-input:focus,
.admin-form-textarea:focus,
.admin-form-select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-form-input--error,
.admin-form-textarea--error {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-form-textarea {
  resize: vertical;
  min-height: 100px;
}

.admin-input-group {
  display: flex;
  gap: 0;
}

.admin-input-group .admin-form-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.admin-form-select--addon {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  min-width: 80px;
}

.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-danger);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

.admin-text-success {
  color: var(--admin-success);
}

.admin-text-warning {
  color: var(--admin-warning);
}

.admin-text-danger {
  color: var(--admin-danger);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-form {
    padding: 0 var(--admin-space-md);
  }

  .admin-form-grid {
    gap: var(--admin-space-lg);
  }

  .admin-form-row {
    flex-direction: column;
    gap: var(--admin-space-md);
  }

  .admin-form-col--half {
    flex: 1;
  }

  .admin-input-group {
    flex-direction: column;
  }

  .admin-input-group .admin-form-input {
    border-radius: var(--admin-radius-md);
    border-right: 1px solid var(--admin-border-light);
  }

  .admin-form-select--addon {
    border-radius: var(--admin-radius-md);
  }
}

/* Enhanced styling for new components */
.admin-form-col--full {
  grid-column: 1 / -1;
}

/* Product image uploader integration */
.product-image-uploader {
  margin: 0;
  border: none;
  border-radius: 0;
}

/* Meta Image Section */
.meta-image-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.meta-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.meta-description {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

/* Responsive adjustments for new components */
@media (max-width: 768px) {
  .admin-form-col--full {
    grid-column: 1;
  }
}
</style>
