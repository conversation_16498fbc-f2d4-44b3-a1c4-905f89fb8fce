// Mock data for categories
export const mockCategories = [
  {
    id: 1,
    name: 'Ноутбуки та комп\'ютери',
    slug: 'laptops-computers',
    parentId: null,
    image: null
  },
  {
    id: 2,
    name: 'Смартфони, ТВ і електроніка',
    slug: 'smartphones-tv-electronics',
    parentId: null,
    image: null
  },
  {
    id: 3,
    name: 'Побутова техніка',
    slug: 'household-appliances',
    parentId: null,
    image: null
  },
  {
    id: 4,
    name: 'Товари для дому',
    slug: 'home-goods',
    parentId: null,
    image: null
  },
  {
    id: 5,
    name: 'Дача, сад і город',
    slug: 'garden',
    parentId: null,
    image: null
  },
  {
    id: 6,
    name: 'Спорт і хобі',
    slug: 'sports-hobbies',
    parentId: null,
    image: null
  },
  {
    id: 7,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> та здоров\'я',
    slug: 'beauty-health',
    parentId: null,
    image: null
  },
  {
    id: 8,
    name: 'Одяг, взуття та прикраси',
    slug: 'clothing-shoes-jewelry',
    parentId: null,
    image: null
  },
  
  // Subcategories for "Ноутбуки та комп'ютери"
  {
    id: 101,
    name: 'Ноутбуки',
    slug: 'laptops',
    parentId: 1,
    image: null
  },
  {
    id: 102,
    name: 'Комп\'ютери',
    slug: 'computers',
    parentId: 1,
    image: null
  },
  {
    id: 103,
    name: 'Комплектуючі',
    slug: 'components',
    parentId: 1,
    image: null
  },
  
  // Subcategories for "Ноутбуки"
  {
    id: 1001,
    name: 'Apple Macbook',
    slug: 'apple-macbook',
    parentId: 101,
    image: null
  },
  {
    id: 1002,
    name: 'Acer',
    slug: 'acer',
    parentId: 101,
    image: null
  },
  {
    id: 1003,
    name: 'ASUS',
    slug: 'asus',
    parentId: 101,
    image: null
  },
  {
    id: 1004,
    name: 'Lenovo',
    slug: 'lenovo',
    parentId: 101,
    image: null
  },
  {
    id: 1005,
    name: 'HP (Hewlett Packard)',
    slug: 'hp',
    parentId: 101,
    image: null
  },
  {
    id: 1006,
    name: 'Dell',
    slug: 'dell',
    parentId: 101,
    image: null
  },
  {
    id: 1007,
    name: 'Всі ноутбуки',
    slug: 'all-laptops',
    parentId: 101,
    image: null
  },
  
  // Subcategories for "Комп'ютери"
  {
    id: 2001,
    name: 'Системні блоки (ПК)',
    slug: 'desktop-pc',
    parentId: 102,
    image: null
  },
  {
    id: 2002,
    name: 'Монітори',
    slug: 'monitors',
    parentId: 102,
    image: null
  },
  {
    id: 2003,
    name: 'Клавіатури та миші',
    slug: 'keyboards-mice',
    parentId: 102,
    image: null
  },
  {
    id: 2004,
    name: 'Комп\'ютерні колонки',
    slug: 'pc-speakers',
    parentId: 102,
    image: null
  },
  {
    id: 2005,
    name: 'Програмне забезпечення',
    slug: 'software',
    parentId: 102,
    image: null
  },
  
  // Subcategories for "Смартфони, ТВ і електроніка"
  {
    id: 201,
    name: 'Смартфони',
    slug: 'smartphones',
    parentId: 2,
    image: null
  },
  {
    id: 202,
    name: 'Планшети',
    slug: 'tablets',
    parentId: 2,
    image: null
  },
  {
    id: 203,
    name: 'Телевізори',
    slug: 'tvs',
    parentId: 2,
    image: null
  },
  
  // Subcategories for "Планшети"
  {
    id: 3001,
    name: 'Apple iPad',
    slug: 'apple-ipad',
    parentId: 202,
    image: null
  },
  {
    id: 3002,
    name: 'Samsung',
    slug: 'samsung-tablets',
    parentId: 202,
    image: null
  },
  {
    id: 3003,
    name: 'Lenovo',
    slug: 'lenovo-tablets',
    parentId: 202,
    image: null
  },
  {
    id: 3004,
    name: 'Xiaomi',
    slug: 'xiaomi-tablets',
    parentId: 202,
    image: null
  },
  {
    id: 3005,
    name: 'Усі планшети',
    slug: 'all-tablets',
    parentId: 202,
    image: null
  }
];

export default mockCategories;
