﻿using Marketplace.Domain.Services;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Category;

/// <summary>
/// Обробник команди для завантаження зображення категорії
/// </summary>
public class UploadCategoryImageCommandHandler : IRequestHandler<UploadCategoryImageCommand, FileUploadResult>
{
    private readonly ICategoryRepository _categoryRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<UploadCategoryImageCommandHandler> _logger;

    public UploadCategoryImageCommandHandler(
        ICategoryRepository categoryRepository,
        IFileService fileService,
        ILogger<UploadCategoryImageCommandHandler> logger)
    {
        _categoryRepository = categoryRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadCategoryImageCommand request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи існує категорія
        var category = await _categoryRepository.GetByIdAsync(request.CategoryId, cancellationToken)
            ?? throw new InvalidOperationException($"Категорію з ID {request.CategoryId} не знайдено.");

        // Завантажуємо файл
        var fileUrl = await _fileService.SaveFileAsync(
            request.File.OpenReadStream(),
            request.File.FileName,
            request.File.ContentType,
            $"categories/{request.CategoryId}",
            cancellationToken);

        // Оновлюємо зображення категорії
        category.UpdateImage(new Url(fileUrl));

        // Зберігаємо зміни
        await _categoryRepository.UpdateAsync(category, cancellationToken);

        // Повертаємо результат
        return new FileUploadResult
        {
            FileUrl = fileUrl,
            FileName = request.File.FileName,
            ContentType = request.File.ContentType,
            Size = request.File.Length
        };
    }
}
