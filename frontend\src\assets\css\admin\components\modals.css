/* ===== ADMIN MODALS SYSTEM ===== */
/* Based on Reports page modal patterns */

/* ===== BASE MODAL STYLES ===== */
.admin-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none; /* Hidden by default */
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--admin-space-lg);
}

/* Show modal when active */
.admin-modal.admin-modal-active {
  display: flex !important;
}

.admin-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.admin-modal-content {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
  z-index: 1;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== MODAL SIZES ===== */
.admin-modal-sm .admin-modal-content {
  width: 400px;
}

.admin-modal-md .admin-modal-content {
  width: 600px;
}

.admin-modal-lg .admin-modal-content {
  width: 800px;
}

.admin-modal-xl .admin-modal-content {
  width: 1000px;
}

.admin-modal-full .admin-modal-content {
  width: 95vw;
  height: 95vh;
}

/* ===== MODAL HEADER ===== */
.admin-modal-header {
  padding: var(--admin-space-2xl);
  border-bottom: 1px solid var(--admin-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--admin-gradient-primary);
  color: white;
}

.admin-modal-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-modal-close {
  background: none;
  border: none;
  color: white;
  font-size: var(--admin-text-xl);
  cursor: pointer;
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  transition: all var(--admin-transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.admin-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* ===== MODAL BODY ===== */
.admin-modal-body {
  padding: var(--admin-space-2xl);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.admin-modal-body::-webkit-scrollbar {
  width: 6px;
}

.admin-modal-body::-webkit-scrollbar-track {
  background: var(--admin-gray-100);
}

.admin-modal-body::-webkit-scrollbar-thumb {
  background: var(--admin-gray-300);
  border-radius: var(--admin-radius-full);
}

/* ===== MODAL FOOTER ===== */
.admin-modal-footer {
  padding: var(--admin-space-xl) var(--admin-space-2xl);
  border-top: 1px solid var(--admin-border-color);
  background: var(--admin-gray-50);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--admin-space-md);
}

/* ===== BULMA MODAL OVERRIDE ===== */
.modal {
  z-index: 1000 !important;
  display: none !important; /* Hidden by default */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal.is-active {
  display: flex !important; /* Show when active */
}

.modal-background {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal-card {
  background: var(--admin-white) !important;
  border-radius: var(--admin-radius-xl) !important;
  box-shadow: var(--admin-shadow-xl) !important;
  overflow: hidden !important;
}

.modal-card-head {
  background: var(--admin-gradient-primary) !important;
  border-bottom: 1px solid var(--admin-border-color) !important;
  padding: var(--admin-space-2xl) !important;
}

.modal-card-title {
  color: white !important;
  font-size: var(--admin-text-xl) !important;
  font-weight: var(--admin-font-semibold) !important;
  margin: 0 !important;
}

.modal-card-body {
  padding: var(--admin-space-2xl) !important;
  background: var(--admin-white) !important;
  color: var(--admin-gray-900) !important;
}

.modal-card-foot {
  background: var(--admin-gray-50) !important;
  border-top: 1px solid var(--admin-border-color) !important;
  padding: var(--admin-space-xl) var(--admin-space-2xl) !important;
  justify-content: flex-end !important;
  gap: var(--admin-space-md) !important;
}

/* Ensure modals are hidden by default - additional safety */
.modal:not(.is-active) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

.admin-modal:not(.admin-modal-active) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

.delete {
  background: none !important;
  border: none !important;
  color: white !important;
  font-size: var(--admin-text-xl) !important;
  cursor: pointer !important;
  padding: var(--admin-space-sm) !important;
  border-radius: var(--admin-radius-md) !important;
  transition: all var(--admin-transition-fast) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
}

.delete:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1) !important;
}

.delete::before,
.delete::after {
  background-color: white !important;
}

/* ===== CONFIRMATION MODALS ===== */
.admin-modal-confirm {
  text-align: center;
}

.admin-modal-confirm .admin-modal-icon {
  font-size: 3rem;
  margin-bottom: var(--admin-space-lg);
}

.admin-modal-confirm.danger .admin-modal-icon {
  color: var(--admin-danger);
}

.admin-modal-confirm.warning .admin-modal-icon {
  color: var(--admin-warning);
}

.admin-modal-confirm.success .admin-modal-icon {
  color: var(--admin-success);
}

.admin-modal-confirm .admin-modal-message {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
  color: var(--admin-gray-900);
}

.admin-modal-confirm .admin-modal-submessage {
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-xl);
}

/* ===== FORM MODALS ===== */
.admin-modal-form .admin-form-group {
  margin-bottom: var(--admin-space-lg);
}

.admin-modal-form .admin-form-label {
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-medium);
}

.admin-modal-form .admin-form-input,
.admin-modal-form .admin-form-select,
.admin-modal-form .admin-form-textarea {
  border: 1px solid var(--admin-border-color);
  background: var(--admin-white);
  color: var(--admin-gray-900);
}

.admin-modal-form .admin-form-input:focus,
.admin-modal-form .admin-form-select:focus,
.admin-modal-form .admin-form-textarea:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .admin-modal {
    padding: var(--admin-space-md);
  }
  
  .admin-modal-content {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0;
  }
  
  .admin-modal-header {
    padding: var(--admin-space-lg);
  }
  
  .admin-modal-body {
    padding: var(--admin-space-lg);
    max-height: calc(90vh - 120px);
  }
  
  .admin-modal-footer {
    padding: var(--admin-space-md) var(--admin-space-lg);
    flex-direction: column;
    gap: var(--admin-space-sm);
  }
  
  .admin-modal-footer .admin-btn {
    width: 100%;
  }
}

/* ===== LOADING MODALS ===== */
.admin-modal-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
}

.admin-modal-loading .admin-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-modal-loading .admin-loading-text {
  color: var(--admin-gray-600);
  font-size: var(--admin-text-lg);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
