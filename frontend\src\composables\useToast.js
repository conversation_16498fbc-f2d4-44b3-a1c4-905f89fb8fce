import { ref } from 'vue';

// Create a reactive array to store toast messages
const toasts = ref([]);

// Generate a unique ID for each toast
let nextId = 0;

export function useToast() {
  // Function to show a toast message
  const showToast = (message, type = 'info', duration = 5000) => {
    const id = nextId++;

    // Add the toast to the array
    const toast = {
      id,
      message,
      type,
      visible: false
    };

    toasts.value.push(toast);

    // Make it visible after a small delay to trigger animation
    setTimeout(() => {
      const toastIndex = toasts.value.findIndex(t => t.id === id);
      if (toastIndex !== -1) {
        toasts.value[toastIndex].visible = true;
      }
    }, 10);

    // Remove the toast after the specified duration
    setTimeout(() => {
      hideToast(id);
    }, duration);

    return id;
  };
  
  // Function to hide a specific toast
  const hideToast = (id) => {
    const index = toasts.value.findIndex(toast => toast.id === id);
    if (index !== -1) {
      // Mark the toast as not visible
      toasts.value[index].visible = false;
      
      // Remove the toast from the array after animation completes
      setTimeout(() => {
        toasts.value = toasts.value.filter(toast => toast.id !== id);
      }, 300);
    }
  };
  
  // Function to clear all toasts
  const clearToasts = () => {
    toasts.value = [];
  };
  
  return {
    toasts,
    showToast,
    hideToast,
    clearToasts
  };
}
