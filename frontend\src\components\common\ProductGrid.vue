<template>
  <div class="product-grid-container">
    <!-- Loading State -->
    <div v-if="displayLoading" class="loading-container">
      <div class="loader"></div>
      <p>Завантаження товарів...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="displayError" class="error-container">
      <h3>Помилка завантаження</h3>
      <p>{{ displayError }}</p>
      <button v-if="autoFetch" @click="fetchProducts" class="retry-btn">Спробувати знову</button>
    </div>

    <!-- Empty State -->
    <div v-else-if="!displayProducts || displayProducts.length === 0" class="empty-container">
      <h3>Товари не знайдено</h3>
      <p>{{ emptyMessage || 'На жаль, товарів у цій категорії поки немає' }}</p>
    </div>

    <!-- Products Grid -->
    <div v-if="displayProducts.length > 0 || showAddCard" class="products-grid" :class="gridClass">
      <!-- Add Product Card (for seller mode) -->
      <div
        v-if="showAddCard"
        class="product-card add-product-card"
        @click="$emit('add-product')"
      >
        <div class="add-product-content">
          <div class="add-product-icon">+</div>
          <div class="add-product-text">Додати товар</div>
        </div>
      </div>

      <!-- Product Cards -->
      <div
        v-for="product in displayProducts"
        :key="product.id"
        :data-product-id="getProductId(product)"
        class="product-card"
        :class="{ 'seller-product-card': sellerMode }"
        @click="handleProductClick(product)"
      >
        <div class="product-badge" v-if="product.badge">{{ product.badge }}</div>
        
        <div class="product-image">
          <img
            :src="getProductImageUrlSafe(product)"
            :alt="product.name || product.productName || 'Product Image'"
            @error="handleImageError($event)"
            loading="lazy"
          />
        </div>
        
        <div class="product-info">
          <h3 class="product-name">{{ product.name || product.productName }}</h3>

          <!-- Seller Mode: Product Status -->
          <div v-if="sellerMode && product.status" class="product-status" :class="'status-' + (product.status || 'pending').toLowerCase()">
            {{ getProductStatusText(product.status) }}
          </div>

          <!-- Regular Mode: Availability -->
          <div v-else-if="!sellerMode && (product.productStock > 0 || product.stock > 0 || product.inStock)" class="product-availability">
            <span class="availability-icon">✓</span>
            <span class="availability-text">В наявності</span>
          </div>

          <div v-else-if="!sellerMode" class="product-unavailability">
            <span class="availability-icon">✖</span>
            <span class="availability-text">Немає в наявності</span>
          </div>
          
          <div class="product-price">
            <span class="price">{{ formatPrice(product) }}</span>
          </div>
          
          <!-- Regular Mode: Cart/Wishlist Actions -->
          <div v-if="showActions && !sellerMode" class="product-actions">
            <button
              @click.stop="addToCart(getProductId(product))"
              :disabled="!(product.productStock > 0 || product.stock > 0 || product.inStock)"
              class="add-to-cart-btn"
            >
              <i class="fas fa-shopping-cart"></i>
            </button>

            <button
              @click.stop="toggleWishlist(getProductId(product))"
              class="add-to-wishlist-btn"
              :class="{ 'active': getWishlistButtonActive(product) }"
              :title="wishlistMode ? 'Видалити з обраних' : (getWishlistButtonActive(product) ? 'Видалити з обраних' : 'Додати до обраних')"
            >
              <i
                class="fas fa-heart"
                :class="getHeartStateClass(product)"
              ></i>
            </button>
          </div>

          <!-- Seller Mode: Edit Button -->
          <button
            v-if="sellerMode"
            @click.stop="$emit('edit-product', product)"
            class="seller-edit-btn"
            title="Редагувати товар"
          >
            <i class="fas fa-edit"></i>
            Редагувати
          </button>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="showPagination && totalPages > 1" class="pagination-container">
      <Pagination
        :currentPage="displayCurrentPage"
        :total-items="displayTotalItems"
        :items-per-page="displayPageSize"
        :max-visible-pages="10"
        @page-changed="onPageChange"
      />
    </div>
  </div>
</template>

<script>
import cartService from '@/services/cart.service';
import wishlistService from '@/services/wishlist.service';
import Pagination from '@/components/catalog/Pagination.vue';
import { useToast } from '@/composables/useToast';
import { useWishlistSync } from '@/composables/useWishlistSync';
import CategoryService from '@/services/category.service';
import ProductService from '@/services/product.service';
import CompanyService from '@/services/company.service';

export default {
  name: 'ProductGrid',
  components: {
    Pagination
  },
  setup() {
    const { showToast } = useToast();
    const wishlistSync = useWishlistSync();

    return {
      showToast,
      wishlistSync
    };
  },
  props: {
    // Fetch parameters (for auto-fetch mode)
    fetchParams: {
      type: Object,
      default: () => ({})
    },
    // Company slug for fetching company products
    companySlug: {
      type: String,
      default: null
    },
    // External data (for manual mode)
    products: {
      type: Array,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: null
    },
    // Pagination props (for manual mode)
    currentPage: {
      type: Number,
      default: 1
    },
    totalItems: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 12
    },
    // Display options
    emptyMessage: {
      type: String,
      default: 'Товари не знайдено'
    },
    showActions: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: false
    },
    gridColumns: {
      type: Number,
      default: 4
    },
    // Auto-fetch on mount
    autoFetch: {
      type: Boolean,
      default: true
    },
    // Seller mode
    sellerMode: {
      type: Boolean,
      default: false
    },
    // Show add product card
    showAddCard: {
      type: Boolean,
      default: false
    },
    // Custom click handler for seller mode
    onProductClick: {
      type: Function,
      default: null
    },
    // Wishlist mode - all products are already in wishlist
    wishlistMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      placeholderImage: '/placeholder-product.svg',
      internalProducts: [],
      internalLoading: false,
      internalError: null,
      internalCurrentPage: 1,
      internalTotalItems: 0,
      internalPageSize: 12,
      wishlistItems: [], // Список ID товарів в обраних
      wishlistLoaded: false, // Флаг для відстеження завантаження wishlist
      imageCache: new Map(), // Кеш для URL зображень
      loadingImages: new Set(), // Множина ID товарів, для яких завантажуються зображення

    }
  },
  computed: {
    // Use external data if provided, otherwise use internal data
    displayProducts() {
      return this.products !== null ? this.products : this.internalProducts;
    },
    displayLoading() {
      return this.products !== null ? this.loading : this.internalLoading;
    },
    displayError() {
      return this.products !== null ? this.error : this.internalError;
    },
    displayCurrentPage() {
      return this.products !== null ? this.currentPage : this.internalCurrentPage;
    },
    displayTotalItems() {
      return this.products !== null ? this.totalItems : this.internalTotalItems;
    },
    displayPageSize() {
      return this.products !== null ? this.pageSize : this.internalPageSize;
    },
    totalPages() {
      return Math.ceil(this.displayTotalItems / this.displayPageSize);
    },
    gridClass() {
      return `grid-cols-${this.gridColumns}`;
    },

    // Computed для перевірки стану wishlist (з урахуванням завантаження)
    isWishlistReady() {
      return this.wishlistMode || this.wishlistLoaded;
    },


  },

  async mounted() {
    // Initialize pageSize from fetchParams if provided
    if (this.fetchParams.pageSize) {
      this.internalPageSize = this.fetchParams.pageSize;
    }

    // Завантажуємо список обраних товарів
    await this.loadWishlistItems();

    if (this.autoFetch) {
      await this.fetchProducts();
    }

    // Завантажуємо зображення для існуючих товарів
    this.$nextTick(() => {
      this.preloadImages();
    });

    // Підписуємося на зміни wishlist з інших компонентів
    this.wishlistSync.onItemAdded(this.onWishlistItemAdded);
    this.wishlistSync.onItemRemoved(this.onWishlistItemRemoved);
  },

  beforeDestroy() {
    // Відписуємося від подій wishlist
    this.wishlistSync.offItemAdded(this.onWishlistItemAdded);
    this.wishlistSync.offItemRemoved(this.onWishlistItemRemoved);
  },

  watch: {
    fetchParams: {
      handler(newParams) {
        // Update pageSize if it changed
        if (newParams.pageSize && newParams.pageSize !== this.internalPageSize) {
          this.internalPageSize = newParams.pageSize;
          this.internalCurrentPage = 1; // Reset to first page when pageSize changes
        }

        if (this.autoFetch) {
          this.fetchProducts();
        }
      },
      deep: true
    },

    companySlug: {
      handler() {
        if (this.autoFetch) {
          this.internalCurrentPage = 1; // Reset to first page when company changes
          this.fetchProducts();
        }
      }
    },

    // Watcher для wishlistItems для оновлення іконок
    wishlistItems: {
      handler() {
        // Примусово оновлюємо компонент для перерендерингу іконок
        this.$forceUpdate();
      },
      deep: true
    },

    // Watcher для displayProducts для завантаження зображень нових товарів
    displayProducts: {
      handler(newProducts) {
        if (newProducts && newProducts.length > 0) {
          // Завантажуємо зображення для нових товарів
          this.$nextTick(() => {
            this.preloadImages();
          });
        }
      },
      deep: true
    }
  },

  methods: {
    // Handle product click based on mode
    handleProductClick(product) {
      if (this.onProductClick) {
        // Use custom click handler if provided
        this.onProductClick(product);
      } else if (this.sellerMode) {
        // In seller mode, only open approved products
        if (product.status !== "Approved") return;
        this.goToProduct(product.slug || product.productSlug || product.id);
      } else {
        // Regular mode
        this.goToProduct(product.slug || product.productSlug || product.id);
      }
    },

    // Get product status text for seller mode
    getProductStatusText(status) {
      const statusTexts = {
        'Pending': 'На розгляді',
        'Approved': 'Схвалено',
        'Rejected': 'Відхилено',
        'Draft': 'Чернетка'
      };
      return statusTexts[status] || status || 'На розгляді';
    },

    async addToCart(productId) {
      try {
        // Перевіряємо, чи productId є валідним
        if (!productId) {
          this.showToast('Помилка: невірний ID товару', 'error');
          return;
        }

        // Передаємо порожні атрибути для ProductGrid, оскільки тут немає вибору атрибутів
        await cartService.addToCart(productId, 1, {});
        this.showToast('Товар додано до корзини', 'success');
        this.$emit('product-added-to-cart', productId);

        // Глобальний емітер для синхронізації з корзиною
        this.wishlistSync.addToGlobalCart(productId);
      } catch (error) {
        console.error('Error adding to cart:', error);
        this.showToast('Помилка при додаванні до корзини', 'error');
      }
    },

    // Завантажує список ID товарів в обраних
    async loadWishlistItems() {
      try {
        this.wishlistLoaded = false;
        const response = await wishlistService.getWishlist();

        // Обробляємо структуру відповіді
        let wishlistData = null;
        if (response.data.success) {
          wishlistData = response.data.data;
        } else if (response.data.data) {
          wishlistData = response.data.data;
        } else {
          wishlistData = response.data;
        }

        // Витягуємо productId з items
        if (wishlistData && wishlistData.items && Array.isArray(wishlistData.items)) {
          this.wishlistItems = wishlistData.items.map(item => item.productId);
        } else {
          this.wishlistItems = [];
        }

        this.wishlistLoaded = true;
      } catch (error) {
        console.error('Error loading wishlist:', error);
        // Якщо wishlist не існує (404), це нормально
        if (error.response?.status === 404) {
          this.wishlistItems = [];
        } else {
          this.wishlistItems = [];
        }
        this.wishlistLoaded = true;
      }
    },

    // Отримує правильний ID товару залежно від структури об'єкта
    getProductId(product) {
      return product.productId || product.id;
    },

    // Отримує клас стану для іконки серця
    getHeartStateClass(product) {
      const productId = this.getProductId(product);

      if (this.wishlistMode) return 'heart-filled';
      if (!this.wishlistLoaded) return 'heart-loading';

      return this.isInWishlist(productId) ? 'heart-filled' : 'heart-empty';
    },

    // Отримує активний стан кнопки wishlist
    getWishlistButtonActive(product) {
      const productId = this.getProductId(product);

      // В режимі wishlist завжди активна
      if (this.wishlistMode) {
        return true;
      }

      // Якщо wishlist ще не завантажився, не активна
      if (!this.wishlistLoaded) {
        return false;
      }

      // Активна якщо товар в wishlist
      return this.isInWishlist(productId);
    },

    // Безпечний метод для отримання URL зображення (без асинхронних викликів)
    getProductImageUrlSafe(product) {
      const productId = this.getProductId(product);

      // Якщо є пряме посилання на зображення, використовуємо його
      if (product.image || product.mainImage || product.imageUrl) {
        return product.image || product.mainImage || product.imageUrl;
      }

      // Перевіряємо кеш
      if (this.imageCache.has(productId)) {
        return this.imageCache.get(productId);
      }

      // Fallback на placeholder
      return this.placeholderImage;
    },

    // Отримує URL зображення товару (з асинхронним завантаженням)
    getProductImageUrl(product) {
      const productId = this.getProductId(product);

      // Якщо є пряме посилання на зображення, використовуємо його
      if (product.image || product.mainImage || product.imageUrl) {
        return product.image || product.mainImage || product.imageUrl;
      }

      // Перевіряємо кеш
      if (this.imageCache.has(productId)) {
        return this.imageCache.get(productId);
      }

      // Якщо зображення завантажується, повертаємо placeholder
      if (this.loadingImages.has(productId)) {
        return this.placeholderImage;
      }

      // Якщо є ID товару, завантажуємо URL асинхронно
      if (productId) {
        // Використовуємо nextTick для безпечного виклику
        this.$nextTick(() => {
          this.loadProductImage(productId);
        });
        return this.placeholderImage; // Тимчасово показуємо placeholder
      }

      // Fallback на placeholder
      return this.placeholderImage;
    },

    // Асинхронно завантажує URL зображення товару
    async loadProductImage(productId) {
      // Перевіряємо, чи компонент ще існує
      if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
        return;
      }

      if (this.loadingImages.has(productId) || this.imageCache.has(productId)) {
        return; // Вже завантажується або завантажено
      }

      this.loadingImages.add(productId);

      try {
        const apiUrl = import.meta.env.VUE_APP_API_URL || 'http://localhost:5296';
        const response = await fetch(`${apiUrl}/api/universal/images/product/${productId}/all`);

        // Перевіряємо, чи компонент ще існує після асинхронної операції
        if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
          return;
        }

        if (response.ok) {
          const data = await response.json();

          // Backend повертає JSON з структурою { success: true, data: "url" }
          let imageUrl = null;
          if (data.success && data.data) {
            data.data.forEach(element => {
              if(element.isMain == true)
              {
                imageUrl = element.url;
                return;
              }
            });
          } else if (data.data) {
            imageUrl = data.data;
          } else if (typeof data === 'string') {
            imageUrl = data;
          }

          if (imageUrl) {
            this.imageCache.set(productId, imageUrl);
            // Примусово оновлюємо компонент для показу нового зображення
            this.$forceUpdate();
          } else {
            // Якщо зображення не знайдено, кешуємо placeholder
            this.imageCache.set(productId, this.placeholderImage);
          }
        } else {
          console.warn(`Image not found for product ${productId}: ${response.status}`);
          // Якщо помилка API, кешуємо placeholder
          this.imageCache.set(productId, this.placeholderImage);
        }
      } catch (error) {
        console.error('Error loading product image:', productId, error);
        // При помилці кешуємо placeholder
        this.imageCache.set(productId, this.placeholderImage);
      } finally {
        this.loadingImages.delete(productId);
      }
    },

    // Попередньо завантажує зображення для всіх товарів
    preloadImages() {
      const products = this.displayProducts;
      if (products && products.length > 0) {
        products.forEach(product => {
          const productId = this.getProductId(product);
          if (productId && !product.image && !product.mainImage && !product.imageUrl) {
            // Завантажуємо зображення тільки якщо немає прямого URL
            // Використовуємо setTimeout для безпечного асинхронного виклику
            setTimeout(() => {
              this.loadProductImage(productId);
            }, 0);
          }
        });
      }
    },



    // Перевіряє чи товар в обраних
    isInWishlist(productId) {
      if (this.wishlistMode) return true;
      return this.wishlistItems.includes(productId);
    },

    async addToWishlist(productId) {
      try {
        await wishlistService.addToWishlist(productId);

        if (!this.wishlistItems.includes(productId)) {
          this.wishlistItems.push(productId);
        }

        this.showToast('Товар додано до обраних', 'success');
        this.$emit('product-added-to-wishlist', productId);

        // Синхронізуємо з глобальним станом
        this.wishlistSync.addToGlobalWishlist(productId);
      } catch (error) {
        console.error('Error adding to wishlist:', error);
        this.showToast('Помилка при додаванні до обраних', 'error');
      }
    },

    async removeFromWishlist(productId) {
      try {
        await wishlistService.removeFromWishlist(productId);

        if (this.wishlistMode) {
          this.internalProducts = this.internalProducts.filter(product => {
            const currentProductId = this.getProductId(product);
            return currentProductId !== productId;
          });
        } else {
          this.wishlistItems = this.wishlistItems.filter(id => id !== productId);
        }

        this.showToast('Товар видалено з обраних', 'success');
        this.$emit('product-removed-from-wishlist', productId);

        // Синхронізуємо з глобальним станом
        this.wishlistSync.removeFromGlobalWishlist(productId);
      } catch (error) {
        console.error('Error removing from wishlist:', error);
        this.showToast('Помилка при видаленні з обраних', 'error');
      }
    },

    // Перемикає стан товару в обраних
    async toggleWishlist(productId) {
      try {
        if (this.wishlistMode) {
          await this.removeFromWishlist(productId);
          return;
        }

        if (this.isInWishlist(productId)) {
          await this.removeFromWishlist(productId);
        } else {
          await this.addToWishlist(productId);
        }
      } catch (error) {
        console.error('Error in toggleWishlist:', error);
      }
    },
    
    goToProduct(slug) {
      this.$router.push(`/product/${slug}`);
    },
    
    handleImageError(event) {
      if (event.target.src !== this.placeholderImage) {
        event.target.src = this.placeholderImage;
      }
    },

    // Обробляє додавання товару до wishlist з інших компонентів
    onWishlistItemAdded(productId) {
      if (!this.wishlistItems.includes(productId)) {
        this.wishlistItems.push(productId);
        console.log(`ProductGrid - Synced add: ${productId}`);
      }
    },

    // Обробляє видалення товару з wishlist з інших компонентів
    onWishlistItemRemoved(productId) {
      this.wishlistItems = this.wishlistItems.filter(id => id !== productId);
      console.log(`ProductGrid - Synced remove: ${productId}`);
    },
    
    async fetchProducts() {
      this.internalLoading = true;
      this.internalError = null;

      try {
        // Use pageSize from fetchParams if provided, otherwise use component's pageSize
        const effectivePageSize = this.fetchParams.pageSize || this.internalPageSize;

        const params = {
          pageSize: effectivePageSize,
          page: this.internalCurrentPage,
          ...this.fetchParams
        };

        let response;

        // Determine which service to use based on parameters
        if (this.companySlug) {
          // Company products
          response = await CompanyService.getCompanyProducts(this.companySlug, params);
        } else if (params.categorySlug && params.random) {
          // Random products from category
          response = await CategoryService.getRandomProducts(params.categorySlug, params);
        } else if (params.categorySlug) {
          response = await CategoryService.getProducts(params.categorySlug, params);
        } else if (params.type === 'top' && params.categorySlug) {
          response = await ProductService.getTopProductsByCategory(params);
        } else if (params.type === 'top') {
          response = await ProductService.getTopProducts(params);
        } else if (params.type === 'recommended') {
          response = await ProductService.getRecommendedProducts(params);
        } else {
          response = await ProductService.getAll(params);
        }

        // Handle different response structures
        if (params.categorySlug && params.random) {
          // Random products return a simple array
          this.internalProducts = response.data || [];
          this.internalTotalItems = this.internalProducts.length;
        } else if (this.companySlug) {
          // Company products API returns ApiResponse<PaginatedResponse<ProductResponse>>
          this.internalProducts = response.data.data.data || [];
          this.internalTotalItems = response.data.data.total || 0;
        } else {
          // Regular paginated response
          this.internalProducts = response.data.data || response.data || [];
          this.internalTotalItems = response.data.total || response.total || this.internalProducts.length;
        }



      } catch (error) {
        console.error('Error fetching products:', error);
        this.internalError = 'Помилка завантаження товарів. Спробуйте ще раз.';
        this.internalProducts = [];
        this.internalTotalItems = 0;
      } finally {
        this.internalLoading = false;
      }
    },

    onPageChange(page) {
      if (this.products !== null) {
        // External mode - emit event for parent to handle
        this.$emit('page-changed', page);
      } else {
        // Internal mode - handle pagination internally
        this.internalCurrentPage = page;
        this.fetchProducts();
      }
    },



    formatPrice(product) {
      if (!product.priceAmount && !product.price) return 'Ціна не вказана';

      const price = product.priceAmount || product.price;
      if (price == 0) return 'Ціна не вказана';

      const roundedPrice = Math.round(price);
      const currency = product.priceCurrency || product.currency;

      if (!currency || currency === "UAH") return `${roundedPrice} ₴`;
      else if (currency === "USD") return `${roundedPrice} $`;
      else if (currency === "EUR") return `${roundedPrice} €`;

      return `${roundedPrice} ${currency}`;
    }
  }
}
</script>

<style scoped>
.product-grid-container {
  width: 100%;
}

/* Loading, Error, Empty States */
.loading-container, .error-container, .empty-container {
  text-align: center;
  padding: 60px 20px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #0056b3;
}

/* Products Grid */
.products-grid {
  display: grid;
  gap: 16px;
  margin-bottom: 20px;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Product Card */
.product-card {
  position: relative;
  background: white;
  border: solid #ABAAAA 2px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ff7a00;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
}

.product-image {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  overflow: hidden;
  border-radius: 4px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-availability, .product-unavailability {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.product-availability {
  color: #28a745;
}

.product-unavailability {
  color: #F27318E3;
}

.availability-icon {
  font-weight: bold;
}

.product-price {
  margin-top: auto;
}

.price {
  font-size: 18px;
  font-weight: 700;
  color: #333;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  gap: 8px;
}

.add-to-cart-btn, .add-to-wishlist-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
  outline: none;
  text-decoration: none;
  vertical-align: middle;
}

.add-to-cart-btn {
  background-color: #007bff;
  color: white;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.add-to-cart-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.add-to-cart-btn:focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.add-to-wishlist-btn {
  background-color: #f8f9fa;
  color: #F27318E3;
  border: 1px solid #F27318E3;
  transition: all 0.3s ease;
  position: relative;
  line-height: 1;
}

.add-to-wishlist-btn:hover {
  background-color: #F27318E3;
  color: white;
}

.add-to-wishlist-btn.active {
  background-color: #F27318E3;
  color: white;
}

.add-to-wishlist-btn.active:hover {
  background-color: #c82333;
}

.add-to-wishlist-btn:focus {
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

/* Стилі для іконок в кнопках */
.add-to-cart-btn i,
.add-to-wishlist-btn i {
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  text-align: center;
}

/* Специфічні стилі для іконок серця */
.add-to-wishlist-btn .fa-heart {
  font-size: 14px !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;
  height: auto;
  transition: all 0.3s ease !important;
  will-change: color, font-family !important;

  /* Примусове оновлення для FontAwesome */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Стилі для іконок wishlist - тільки заповнені серця з різними кольорами */
.add-to-wishlist-btn .fas.fa-heart {
  transition: all 0.3s ease;
}

/* Порожнє серце - сірий колір */
.add-to-wishlist-btn .fas.fa-heart.heart-empty {
  color: #6c757d !important;
}

/* Заповнене серце - червоний колір */
.add-to-wishlist-btn .fas.fa-heart.heart-filled {
  color: #dc3545 !important;
}

/* Завантаження - напівпрозорий */
.add-to-wishlist-btn .fas.fa-heart.heart-loading {
  color: #6c757d !important;
  opacity: 0.6;
}

/* Активна кнопка - білий колір (найвищий пріоритет) */
.add-to-wishlist-btn.active .fas.fa-heart {
  color: #fff !important;
}

/* Hover ефект */
.add-to-wishlist-btn:hover .fa-heart {
  transform: translate(-50%, -50%) scale(1.1);
}

/* Анімація при зміні стану */
.add-to-wishlist-btn .fas.fa-heart.heart-filled {
  animation: heartPulse 0.3s ease-in-out;
}

@keyframes heartPulse {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

/* Специфічні стилі для іконок корзини */
.add-to-cart-btn .fa-shopping-cart {
  font-size: 14px !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;
  height: auto;
}

/* Виправлення для availability-icon */
.availability-icon {
  font-size: 12px;
  line-height: 1;
}

.pagination-container {
  margin-top: 40px;
  display: flex;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .grid-cols-4 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-5 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-6 { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 768px) {
  .grid-cols-3, .grid-cols-4, .grid-cols-5, .grid-cols-6 { 
    grid-template-columns: repeat(2, 1fr); 
  }
  
  .product-image {
    height: 120px;
  }
  
  .product-name {
    font-size: 14px;
  }
  
  .price {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .product-actions {
    flex-direction: column;
    gap: 8px;
  }

  .add-to-cart-btn, .add-to-wishlist-btn {
    width: 100%;
    height: 40px;
    border-radius: 8px;
  }
}

/* ===== SELLER MODE STYLES ===== */

/* Add Product Card */
.add-product-card {
  border: 2px dashed #bbb !important;
  background: #fafafa !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.add-product-card:hover {
  border-color: #007bff !important;
  background: #f0f8ff !important;
  transform: translateY(-2px) !important;
}

.add-product-content {
  text-align: center;
  color: #666;
}

.add-product-icon {
  font-size: 48px;
  margin-bottom: 8px;
  transition: color 0.2s;
}

.add-product-card:hover .add-product-icon {
  color: #007bff;
}

.add-product-text {
  font-size: 16px;
  font-weight: 500;
  transition: color 0.2s;
}

.add-product-card:hover .add-product-text {
  color: #007bff;
}

/* Product Status Badge */
.product-status {
  display: inline-block;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.status-pending {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-approved {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-rejected {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-draft {
  background: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

/* Seller Edit Button */
.seller-edit-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  gap: 4px;
}

.seller-edit-btn:hover,
.seller-edit-btn:focus {
  background: rgba(0, 95, 204, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  outline: none;
}

/* Seller Product Card Adjustments */
.seller-product-card {
  position: relative;
}

.seller-product-card .product-price {
  margin-top: auto;
}


</style>
