<template>
  <div class="category-detail-new">
    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="loading-text">Loading category details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="error-title">Error Loading Category</h3>
      <p class="error-message">{{ error }}</p>
      <button class="retry-btn" @click="fetchCategory(route.params.id)">
        <i class="fas fa-redo"></i>
        Try Again
      </button>
    </div>

    <!-- Category Not Found -->
    <div v-else-if="!category" class="not-found-state">
      <div class="not-found-icon">
        <i class="fas fa-folder-open"></i>
      </div>
      <h3 class="not-found-title">Category Not Found</h3>
      <p class="not-found-message">The requested category does not exist or has been deleted.</p>
      <router-link to="/admin/categories" class="back-btn">
        <i class="fas fa-arrow-left"></i>
        Back to Categories
      </router-link>
    </div>

    <!-- Category Content -->
    <div v-else class="category-content">
      <!-- Header -->
      <div class="category-header">
        <div class="header-content">
          <nav class="breadcrumb">
            <router-link to="/admin" class="breadcrumb-item">Dashboard</router-link>
            <span class="breadcrumb-separator">/</span>
            <router-link to="/admin/categories" class="breadcrumb-item">Categories</router-link>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item breadcrumb-current">{{ category.name }}</span>
          </nav>
          <h1 class="category-title">{{ category.name }}</h1>
          <p class="category-subtitle">Category Details & Products</p>
        </div>
        <div class="header-actions">
          <button class="action-btn action-btn-primary" @click="editCategory">
            <i class="fas fa-edit"></i>
            Edit Category
          </button>
          <button class="action-btn action-btn-danger" @click="confirmDelete">
            <i class="fas fa-trash"></i>
            Delete
          </button>
        </div>
      </div>

      <!-- Category Information Section -->
      <div class="info-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-info-circle"></i>
            Category Information
          </h2>
        </div>
        
        <div class="info-grid">
          <!-- Basic Info Card -->
          <div class="info-card info-card-main">
            <div class="info-card-header">
              <h3 class="info-card-title">Basic Information</h3>
            </div>
            <div class="info-card-content">
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">ID</label>
                  <span class="info-value">{{ category.id }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">Name</label>
                  <span class="info-value">{{ category.name }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Slug</label>
                  <span class="info-value info-value-code">{{ category.slug }}</span>
                </div>
                <div class="info-item" v-if="parentCategoryName">
                  <label class="info-label">Parent Category</label>
                  <router-link 
                    :to="`/admin/categories/${category.parentId}`"
                    class="info-value info-value-link">
                    {{ parentCategoryName }}
                  </router-link>
                </div>
              </div>
              <div class="info-row" v-if="category.description">
                <div class="info-item info-item-full">
                  <label class="info-label">Description</label>
                  <p class="info-value info-value-text">{{ category.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Image Card -->
          <div class="info-card">
            <div class="info-card-header">
              <h3 class="info-card-title">Category Image</h3>
            </div>
            <div class="info-card-content">
              <div class="category-image-wrapper">
                <img
                  v-if="category.image"
                  :src="category.image"
                  :alt="category.name"
                  class="category-image clickable"
                  @click="openImageViewer(category.image, category.name + ' - Category Image')"
                  @error="handleImageError"
                  title="Click to view full size">
                <div v-else class="no-image">
                  <i class="fas fa-image"></i>
                  <p>No Images</p>
                </div>
              </div>
            </div>
          </div>

          <!-- SEO Card -->
          <div class="info-card" v-if="category.metaTitle || category.metaDescription || category.metaImage">
            <div class="info-card-header">
              <h3 class="info-card-title">
                <i class="fas fa-search"></i>
                SEO Information
              </h3>
            </div>
            <div class="info-card-content">
              <div class="info-item" v-if="category.metaTitle">
                <label class="info-label">Meta Title</label>
                <span class="info-value">{{ category.metaTitle }}</span>
              </div>
              <div class="info-item" v-if="category.metaDescription">
                <label class="info-label">Meta Description</label>
                <p class="info-value info-value-text">{{ category.metaDescription }}</p>
              </div>
              <div class="info-item">
                <label class="info-label">Meta Image</label>
                <div v-if="category.metaImage" class="meta-image-wrapper">
                  <img
                    :src="category.metaImage"
                    :alt="category.name + ' meta image'"
                    class="meta-image clickable"
                    @click="openImageViewer(category.metaImage, category.name + ' - Meta Image')"
                    @error="handleMetaImageError"
                    title="Click to view full size">
                </div>
                <div v-else class="no-meta-image">
                  <div class="no-meta-image-icon">
                    <i class="fas fa-image"></i>
                  </div>
                  <p class="no-meta-image-text">No Meta Image</p>
                  <p class="no-meta-image-description">
                    No meta image has been set for this category.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Products Section -->
      <div class="products-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-box"></i>
            Products ({{ products.length }})
          </h2>
          <div class="section-actions">
            <button 
              class="action-btn action-btn-secondary"
              @click="refreshProducts"
              :disabled="loadingProducts">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loadingProducts }"></i>
              Refresh
            </button>

          </div>
        </div>

        <!-- Products Loading -->
        <div v-if="loadingProducts" class="products-loading">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="loading-text">Loading products...</p>
        </div>

        <!-- No Products -->
        <div v-else-if="products.length === 0" class="no-products">
          <div class="no-products-icon">
            <i class="fas fa-box-open"></i>
          </div>
          <h3 class="no-products-title">No Products Found</h3>
          <p class="no-products-message">This category doesn't have any products yet.</p>

        </div>

        <!-- Products Table -->
        <div v-else class="products-table-wrapper">
          <div class="table-header">
            <div class="table-info">
              <span class="table-count">{{ products.length }} products in this category</span>
            </div>
            <div class="table-actions">
              <button
                class="action-btn action-btn-warning"
                @click="showBulkUpdateModal = true"
                :disabled="products.length === 0">
                <i class="fas fa-exchange-alt"></i>
                Move All Products
              </button>
            </div>
          </div>

          <div class="products-table">
            <table class="table">
              <thead>
                <tr>
                  <th>Product</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="product in products" :key="product.id" class="table-row">
                  <td class="product-cell">
                    <div class="product-info">
                      <div class="product-image" v-if="product.image">
                        <img :src="product.image" :alt="product.name" @error="handleProductImageError">
                      </div>
                      <div class="product-details">
                        <h4 class="product-name">{{ product.name }}</h4>
                        <p class="product-sku">{{ product.sku }}</p>
                      </div>
                    </div>
                  </td>
                  <td class="price-cell">
                    <span class="price-tag">{{ formatPrice(product) }}</span>
                  </td>
                  <td class="stock-cell">
                    <span class="stock-badge" :class="getStockClass(product.stock)">
                      {{ product.stock }}
                    </span>
                  </td>
                  <td class="status-cell">
                    <span class="status-badge" :class="getStatusClass(getStatusText(product.status))">
                      {{ getStatusText(product.status) }}
                    </span>
                  </td>
                  <td class="actions-cell">
                    <div class="action-buttons">
                      <router-link
                        :to="`/admin/products/${product.id}/view`"
                        class="action-btn action-btn-sm action-btn-info"
                        title="View Product">
                        <i class="fas fa-eye"></i>
                      </router-link>
                      <router-link
                        :to="`/admin/products/${product.id}/edit`"
                        class="action-btn action-btn-sm action-btn-warning"
                        title="Edit Product">
                        <i class="fas fa-edit"></i>
                      </router-link>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <ConfirmDialog
      :is-open="showDeleteDialog"
      :title="'Delete Category'"
      :message="`Are you sure you want to delete '${category?.name}'? This action cannot be undone.`"
      :confirm-text="'Delete'"
      :confirm-button-class="'is-danger'"
      @confirm="handleDelete"
      @cancel="closeDeleteDialog"
    />

    <!-- Bulk Update Modal -->
    <div class="modal" :class="{ 'modal-active': showBulkUpdateModal }">
      <div class="modal-backdrop" @click="closeBulkUpdateModal"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Move All Products to Another Category</h3>
          <button class="modal-close" @click="closeBulkUpdateModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-field">
            <label class="form-label">Select Target Category</label>
            <select v-model="selectedTargetCategory" class="form-select">
              <option value="">Choose a category...</option>
              <option
                v-for="cat in availableCategories"
                :key="cat.id"
                :value="cat.id">
                {{ cat.name }}
              </option>
            </select>
            <p class="form-help">All {{ products.length }} products will be moved to the selected category.</p>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="action-btn action-btn-warning"
            @click="handleBulkUpdate"
            :disabled="!selectedTargetCategory || bulkUpdateLoading"
            :class="{ 'action-btn-loading': bulkUpdateLoading }">
            <i class="fas fa-spinner fa-spin" v-if="bulkUpdateLoading"></i>
            <i class="fas fa-exchange-alt" v-else></i>
            Move Products
          </button>
          <button class="action-btn action-btn-secondary" @click="closeBulkUpdateModal">Cancel</button>
        </div>
      </div>
    </div>

    <!-- Image Viewer Modal -->
    <div
      v-if="showImageModal"
      class="modal fade show d-block"
      @click="closeImageViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-image me-2"></i>
              {{ imageModalTitle }}
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeImageViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <div class="image-viewer">
              <img
                :src="imageModalUrl"
                :alt="imageModalTitle"
                class="img-fluid rounded"
                style="max-height: 500px;"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { categoriesService } from '@/admin/services/categories';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const loadingProducts = ref(false);
const error = ref('');
const category = ref(null);
const products = ref([]);
const allCategories = ref([]);

// UI state
const showDeleteDialog = ref(false);
const showBulkUpdateModal = ref(false);
const selectedTargetCategory = ref('');
const bulkUpdateLoading = ref(false);

// Image viewer state
const showImageModal = ref(false);
const imageModalUrl = ref('');
const imageModalTitle = ref('');

// Computed properties
const parentCategoryName = computed(() => {
  if (!category.value?.parentId || !allCategories.value.length) return '';
  const parent = allCategories.value.find(cat => cat.id === category.value.parentId);
  return parent?.name || 'Unknown';
});

const availableCategories = computed(() => {
  return allCategories.value.filter(cat => cat.id !== category.value?.id);
});

// Methods
const fetchCategory = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    const categoryData = await categoriesService.getById(id);
    category.value = categoryData;

    await Promise.all([
      fetchProducts(categoryData.id),
      fetchAllCategories()
    ]);

  } catch (err) {
    console.error('Error fetching category:', err);
    if (err.name !== 'CanceledError' && err.code !== 'ERR_CANCELED') {
      error.value = 'Failed to load category. Please try again.';
    }
  } finally {
    loading.value = false;
  }
};

const fetchProducts = async (categoryId) => {
  loadingProducts.value = true;
  try {
    const response = await categoriesService.getCategoryProducts(categoryId, { pageSize: 50 });
    products.value = response.data || [];
  } catch (err) {
    console.error('Error fetching products:', err);
    products.value = [];
  } finally {
    loadingProducts.value = false;
  }
};

const fetchAllCategories = async () => {
  try {
    const response = await categoriesService.getAll({ pageSize: 1000 });
    allCategories.value = response.data || [];
  } catch (err) {
    console.error('Error fetching all categories:', err);
    allCategories.value = [];
  }
};

const refreshProducts = () => {
  if (category.value?.id) {
    fetchProducts(category.value.id);
  }
};

// Utility methods
const formatPrice = (product) => {
  if (product && product.priceAmount && product.priceCurrency) {
    return `${product.priceAmount} ${product.priceCurrency}`;
  }
  if (product && product.price) {
    return `${product.price} UAH`;
  }
  return 'N/A';
};

const getStockClass = (stock) => {
  if (stock === 0) return 'stock-badge-danger';
  if (stock < 10) return 'stock-badge-warning';
  return 'stock-badge-success';
};

// Convert numeric status to text
const getStatusText = (status) => {
  if (typeof status === 'string') return status;

  switch (status) {
    case 0:
      return 'Draft';
    case 1:
      return 'Active';
    case 2:
      return 'Inactive';
    case 3:
      return 'Pending';
    default:
      return 'Unknown';
  }
};

const getStatusClass = (status) => {
  if (!status || typeof status !== 'string') return 'status-badge-light';

  switch (status.toLowerCase()) {
    case 'active':
    case 'approved':
      return 'status-badge-success';
    case 'inactive':
    case 'rejected':
      return 'status-badge-danger';
    case 'draft':
    case 'pending':
      return 'status-badge-warning';
    default:
      return 'status-badge-light';
  }
};

// Image error handlers
const handleImageError = (event) => {
  event.target.style.display = 'none';
};

const handleMetaImageError = (event) => {
  event.target.style.display = 'none';
};

// Image viewer methods
const openImageViewer = (imageUrl, title) => {
  imageModalUrl.value = imageUrl;
  imageModalTitle.value = title;
  showImageModal.value = true;
};

const closeImageViewer = () => {
  showImageModal.value = false;
  imageModalUrl.value = '';
  imageModalTitle.value = '';
};

const handleProductImageError = (event) => {
  event.target.style.display = 'none';
};

// Navigation handlers
const editCategory = () => {
  router.push(`/admin/categories/${category.value.id}/edit`);
};

const confirmDelete = () => {
  showDeleteDialog.value = true;
};

const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
};

const handleDelete = async () => {
  try {
    await categoriesService.delete(category.value.id);
    closeDeleteDialog();
    router.push('/admin/categories');
  } catch (err) {
    console.error('Error deleting category:', err);
    closeDeleteDialog();

    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to delete category. Please try again.';
    }
  }
};

// Bulk update handlers
const closeBulkUpdateModal = () => {
  showBulkUpdateModal.value = false;
  selectedTargetCategory.value = '';
};

const handleBulkUpdate = async () => {
  if (!selectedTargetCategory.value || !category.value?.id) return;

  bulkUpdateLoading.value = true;
  try {
    const response = await categoriesService.bulkUpdateProductsCategory(
      category.value.id,
      selectedTargetCategory.value
    );

    closeBulkUpdateModal();

    const targetCategoryName = availableCategories.value.find(
      cat => cat.id === selectedTargetCategory.value
    )?.name || 'selected category';

    alert(`Successfully moved ${response.updatedCount} products to ${targetCategoryName}`);
    await fetchProducts(category.value.id);

  } catch (err) {
    console.error('Error updating products category:', err);

    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to move products. Please try again.';
    }
  } finally {
    bulkUpdateLoading.value = false;
  }
};

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchCategory(newId);
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  const categoryId = route.params.id;
  if (categoryId) {
    fetchCategory(categoryId);
  }
});
</script>

<style scoped>
/* Base Styles */
.category-detail-new {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* Loading States */
.loading-state,
.products-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1rem;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Not Found State */
.not-found-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.not-found-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.not-found-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.not-found-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.back-btn {
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

/* Header */
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

.breadcrumb-current {
  color: #1f2937;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d1d5db;
}

.category-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.category-subtitle {
  color: #6b7280;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-secondary {
  background: #6b7280;
  color: white;
}

.action-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-danger {
  background: #ef4444;
  color: white;
}

.action-btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.action-btn-warning {
  background: #f59e0b;
  color: white;
}

.action-btn-warning:hover {
  background: #d97706;
  transform: translateY(-1px);
}

.action-btn-info {
  background: #3b82f6;
  color: white;
}

.action-btn-info:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}

.action-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.action-btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Sections */
.info-section,
.products-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title i {
  color: #6b7280;
}

.section-actions {
  display: flex;
  gap: 1rem;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.info-card-main {
  grid-column: 1 / -1;
}

.info-card-header {
  background: #f8fafc;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.info-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-card-content {
  padding: 1.5rem;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item-full {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.info-value {
  font-size: 1rem;
  color: #1f2937;
}

.info-value-code {
  font-family: 'Courier New', monospace;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.info-value-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.info-value-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

.info-value-text {
  line-height: 1.6;
  margin: 0;
}

/* Images */
.category-image-wrapper,
.meta-image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.category-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
  min-height: 120px;
}

.no-image i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.no-image p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
}

.meta-image {
  max-width: 150px;
  max-height: 150px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* No Products State */
.no-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.no-products-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.no-products-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.no-products-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

/* Products Table */
.products-table-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.table-count {
  font-size: 0.875rem;
  color: #6b7280;
}

.table-actions {
  display: flex;
  gap: 1rem;
}

.products-table {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f8fafc;
  padding: 1rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
}

.table td {
  padding: 1rem 1.5rem;
  vertical-align: middle;
}

/* Product Cell */
.product-cell {
  min-width: 250px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.product-sku {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Price Cell */
.price-tag {
  background: #3b82f6;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Stock Cell */
.stock-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.stock-badge-success {
  background: #10b981;
  color: white;
}

.stock-badge-warning {
  background: #f59e0b;
  color: white;
}

.stock-badge-danger {
  background: #ef4444;
  color: white;
}

/* Status Cell */
.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge-success {
  background: #10b981;
  color: white;
}

.status-badge-warning {
  background: #f59e0b;
  color: white;
}

.status-badge-danger {
  background: #ef4444;
  color: white;
}

.status-badge-light {
  background: #f3f4f6;
  color: #6b7280;
}

/* Actions Cell */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-active {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.modal-active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

/* Form Elements */
.form-field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-select {
  width: 100%;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .category-detail-new {
    padding: 1.5rem;
  }

  .category-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-card-main {
    grid-column: 1;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .category-detail-new {
    padding: 1rem;
  }

  .category-header {
    padding: 1.5rem;
  }

  .category-title {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    justify-content: center;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .section-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .info-card-content {
    padding: 1rem;
  }

  .table-header {
    padding: 1rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 1rem;
  }

  .product-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .product-image {
    width: 40px;
    height: 40px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn-sm {
    padding: 0.5rem;
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    flex-wrap: wrap;
  }

  .category-title {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .products-table {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.75rem;
  }

  .product-name {
    font-size: 0.875rem;
  }

  .product-sku {
    font-size: 0.75rem;
  }
}

/* Clickable images */
.category-image.clickable,
.meta-image.clickable {
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.category-image.clickable:hover,
.meta-image.clickable:hover {
  transform: scale(1.05);
  opacity: 0.9;
}

/* No meta image styles */
.no-meta-image {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
}

.no-meta-image-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.no-meta-image-text {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.no-meta-image-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.image-viewer {
  padding: 1rem;
}
</style>
