<template>
  <div class="category-view">
    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader-wrapper">
        <div class="loader is-loading"></div>
        <p class="mt-3">Loading category...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = ''"></button>
      {{ error }}
    </div>

    <!-- Category Not Found -->
    <div v-else-if="!category" class="has-text-centered py-6">
      <span class="icon is-large has-text-grey-light">
        <i class="fas fa-folder-open fa-3x"></i>
      </span>
      <p class="title is-5 has-text-grey">Category not found</p>
      <p class="subtitle is-6 has-text-grey">The requested category does not exist</p>
      <router-link to="/admin/categories" class="button is-primary">
        <span class="icon">
          <i class="fas fa-arrow-left"></i>
        </span>
        <span>Back to Categories</span>
      </router-link>
    </div>

    <!-- Category Content -->
    <div v-else>
      <!-- Header -->
      <div class="level mb-5">
        <div class="level-left">
          <div class="level-item">
            <div>
              <nav class="breadcrumb" aria-label="breadcrumbs">
                <ul>
                  <li><router-link to="/admin">Dashboard</router-link></li>
                  <li><router-link to="/admin/categories">Categories</router-link></li>
                  <li class="is-active"><a href="#" aria-current="page">{{ category.name }}</a></li>
                </ul>
              </nav>
              <h1 class="title is-4">{{ category.name }}</h1>
              <p class="subtitle is-6">Category Details</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons">
              <button 
                class="button is-primary" 
                @click="editCategory">
                <span class="icon">
                  <i class="fas fa-edit"></i>
                </span>
                <span>Edit Category</span>
              </button>
              <button 
                class="button is-danger" 
                @click="confirmDelete">
                <span class="icon">
                  <i class="fas fa-trash"></i>
                </span>
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Category Info Cards -->
      <div class="columns is-multiline mb-5">
        <!-- Basic Info -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-info-circle"></i>
                </span>
                <span>Basic Information</span>
              </p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-3" v-if="category.image">
                  <figure class="image is-128x128">
                    <img :src="category.image" :alt="category.name" class="is-rounded">
                  </figure>
                </div>
                <div class="column">
                  <div class="field">
                    <label class="label">Name</label>
                    <p class="content">{{ category.name }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Slug</label>
                    <p class="content">
                      <code>{{ category.slug }}</code>
                    </p>
                  </div>
                  <div class="field" v-if="category.description">
                    <label class="label">Description</label>
                    <p class="content">{{ category.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Stats -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-chart-bar"></i>
                </span>
                <span>Statistics</span>
              </p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Products</label>
                <p class="title is-4 has-text-primary">{{ products.length }}</p>
              </div>
              <div class="field" v-if="category.parentId">
                <label class="label">Parent Category</label>
                <p class="content">
                  <router-link 
                    :to="`/admin/categories/${category.parentId}`"
                    class="has-text-link">
                    {{ parentCategoryName }}
                  </router-link>
                </p>
              </div>

            </div>
          </div>
        </div>
      </div>

      <!-- SEO Info -->
      <div class="card mb-5" v-if="category.metaTitle || category.metaDescription">
        <div class="card-header">
          <p class="card-header-title">
            <span class="icon">
              <i class="fas fa-search"></i>
            </span>
            <span>SEO Information</span>
          </p>
        </div>
        <div class="card-content">
          <div class="columns">
            <div class="column is-6" v-if="category.metaTitle">
              <div class="field">
                <label class="label">Meta Title</label>
                <p class="content">{{ category.metaTitle }}</p>
              </div>
            </div>
            <div class="column is-6" v-if="category.metaDescription">
              <div class="field">
                <label class="label">Meta Description</label>
                <p class="content">{{ category.metaDescription }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- Products -->
      <div class="card">
        <div class="card-header">
          <p class="card-header-title">
            <span class="icon">
              <i class="fas fa-box"></i>
            </span>
            <span>Products ({{ products.length }})</span>
          </p>
          <div class="card-header-icon">
            <button 
              class="button is-small is-primary" 
              @click="refreshProducts">
              <span class="icon">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': loadingProducts }"></i>
              </span>
              <span>Refresh</span>
            </button>
          </div>
        </div>
        <div class="card-content">
          <!-- Products Loading -->
          <div v-if="loadingProducts" class="has-text-centered py-4">
            <span class="icon">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            Loading products...
          </div>

          <!-- No Products -->
          <div v-else-if="products.length === 0" class="has-text-centered py-4">
            <span class="icon is-large has-text-grey-light">
              <i class="fas fa-box-open fa-2x"></i>
            </span>
            <p class="title is-6 has-text-grey">No products in this category</p>
            <p class="subtitle is-7 has-text-grey">Products will appear here when added to this category</p>
          </div>

          <!-- Products Table -->
          <div v-else>
            <!-- Bulk Actions -->
            <div class="level mb-4">
              <div class="level-left">
                <div class="level-item">
                  <p class="subtitle is-6">{{ products.length }} products in this category</p>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <button
                    class="button is-warning"
                    @click="showBulkUpdateModal = true"
                    :disabled="products.length === 0">
                    <span class="icon">
                      <i class="fas fa-exchange-alt"></i>
                    </span>
                    <span>Move All Products</span>
                  </button>
                </div>
              </div>
            </div>

            <div class="table-container">
              <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr class="has-background-light">
                  <th class="has-text-weight-bold has-text-dark">Product</th>
                  <th class="has-text-weight-bold has-text-dark">Price</th>
                  <th class="has-text-weight-bold has-text-dark">Stock</th>
                  <th class="has-text-weight-bold has-text-dark">Status</th>
                  <th class="has-text-weight-bold has-text-dark">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="product in products" :key="product.id">
                  <td>
                    <div class="media">
                      <div class="media-left" v-if="product.image">
                        <figure class="image is-48x48">
                          <img :src="product.image" :alt="product.name" class="is-rounded">
                        </figure>
                      </div>
                      <div class="media-content">
                        <p class="title is-6 has-text-dark">{{ product.name }}</p>
                        <p class="subtitle is-7 has-text-grey-dark">{{ product.sku }}</p>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="tag is-primary is-medium">
                      {{ formatPrice(product) }}
                    </span>
                  </td>
                  <td>
                    <span class="tag is-medium" :class="getStockClass(product.stock)">
                      {{ product.stock }}
                    </span>
                  </td>
                  <td>
                    <span class="tag is-medium" :class="getStatusClass(product.status)">
                      {{ product.status }}
                    </span>
                  </td>
                  <td>
                    <div class="buttons are-small">
                      <router-link
                        :to="`/admin/products/${product.id}/view`"
                        class="button is-info"
                        title="View Product">
                        <span class="icon">
                          <i class="fas fa-eye"></i>
                        </span>
                        <span>View</span>
                      </router-link>
                      <router-link
                        :to="`/admin/products/${product.id}/edit`"
                        class="button is-warning"
                        title="Edit Product">
                        <span class="icon">
                          <i class="fas fa-edit"></i>
                        </span>
                        <span>Edit</span>
                      </router-link>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <CategoryFormModal
      :is-open="showEditModal"
      :category="category"
      :categories="allCategories"
      @close="closeEditModal"
      @save="handleCategorySaved"
    />

    <ConfirmDialog
      :is-open="showDeleteDialog"
      :title="'Delete Category'"
      :message="`Are you sure you want to delete '${category?.name}'? This action cannot be undone.`"
      :confirm-text="'Delete'"
      :confirm-button-class="'is-danger'"
      @confirm="handleDelete"
      @cancel="closeDeleteDialog"
    />

    <!-- Bulk Update Modal -->
    <div class="modal" :class="{ 'is-active': showBulkUpdateModal }">
      <div class="modal-background" @click="closeBulkUpdateModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Move All Products to Another Category</p>
          <button class="delete" @click="closeBulkUpdateModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Select Target Category</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="selectedTargetCategory">
                  <option value="">Choose a category...</option>
                  <option
                    v-for="cat in availableCategories"
                    :key="cat.id"
                    :value="cat.id">
                    {{ cat.name }}
                  </option>
                </select>
              </div>
            </div>
            <p class="help">All {{ products.length }} products will be moved to the selected category.</p>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-warning"
            @click="handleBulkUpdate"
            :disabled="!selectedTargetCategory || bulkUpdateLoading"
            :class="{ 'is-loading': bulkUpdateLoading }">
            Move Products
          </button>
          <button class="button" @click="closeBulkUpdateModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import CategoryFormModal from '@/admin/components/categories/CategoryFormModal.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { categoriesService } from '@/admin/services/categories';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const loadingProducts = ref(false);
const error = ref('');
const category = ref(null);
const products = ref([]);

const allCategories = ref([]);

// UI state
const showEditModal = ref(false);
const showDeleteDialog = ref(false);
const showBulkUpdateModal = ref(false);
const selectedTargetCategory = ref('');
const bulkUpdateLoading = ref(false);

// Computed properties
const parentCategoryName = computed(() => {
  if (!category.value?.parentId || !allCategories.value.length) return '';
  const parent = allCategories.value.find(cat => cat.id === category.value.parentId);
  return parent?.name || 'Unknown';
});

const availableCategories = computed(() => {
  // Фільтруємо поточну категорію з списку доступних
  return allCategories.value.filter(cat => cat.id !== category.value?.id);
});

// Methods
const fetchCategory = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    // Get category by ID
    const categoryData = await categoriesService.getById(id);
    category.value = categoryData;

    // Fetch related data using the category ID
    await Promise.all([
      fetchProducts(categoryData.id),
      fetchAllCategories()
    ]);

  } catch (err) {
    console.error('Error fetching category:', err);
    // Don't show error for cancelled requests
    if (err.name !== 'CanceledError' && err.code !== 'ERR_CANCELED') {
      error.value = 'Failed to load category. Please try again.';
    }
  } finally {
    loading.value = false;
  }
};

const fetchProducts = async (categoryId) => {
  loadingProducts.value = true;
  try {
    const response = await categoriesService.getCategoryProducts(categoryId, { pageSize: 50 });
    products.value = response.data || [];
  } catch (err) {
    console.error('Error fetching products:', err);
    products.value = [];
  } finally {
    loadingProducts.value = false;
  }
};



const fetchAllCategories = async () => {
  try {
    const response = await categoriesService.getAll({ pageSize: 1000 });
    allCategories.value = response.data || [];
  } catch (err) {
    console.error('Error fetching all categories:', err);
    allCategories.value = [];
  }
};

const refreshProducts = () => {
  if (category.value?.id) {
    fetchProducts(category.value.id);
  }
};

// Utility methods
const formatPrice = (product) => {
  if (product && product.priceAmount && product.priceCurrency) {
    return `${product.priceAmount} ${product.priceCurrency}`;
  }
  if (product && product.price) {
    return `${product.price} UAH`;
  }
  return 'N/A';
};

const getStockClass = (stock) => {
  if (stock === 0) return 'is-danger';
  if (stock < 10) return 'is-warning';
  return 'is-success';
};

const getStatusClass = (status) => {
  if (!status || typeof status !== 'string') return 'is-light';

  switch (status.toLowerCase()) {
    case 'active':
    case 'approved':
      return 'is-success';
    case 'inactive':
    case 'rejected':
      return 'is-danger';
    case 'draft':
    case 'pending':
      return 'is-warning';
    default:
      return 'is-light';
  }
};

// Modal handlers
const editCategory = () => {
  showEditModal.value = true;
};

const closeEditModal = () => {
  showEditModal.value = false;
};

const handleCategorySaved = async (categoryData) => {
  try {
    await categoriesService.updateCategory(category.value.id, categoryData);
    closeEditModal();
    
    // Refresh category data
    await fetchCategory(category.value.id);
  } catch (err) {
    console.error('Error saving category:', err);
    error.value = 'Failed to save category. Please try again.';
  }
};

const confirmDelete = () => {
  showDeleteDialog.value = true;
};

const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
};

const handleDelete = async () => {
  try {
    await categoriesService.delete(category.value.id);
    closeDeleteDialog();
    
    // Navigate back to categories list
    router.push('/admin/categories');
  } catch (err) {
    console.error('Error deleting category:', err);
    closeDeleteDialog();

    // Обробляємо специфічні помилки від сервера
    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to delete category. Please try again.';
    }
  }
};

// Bulk update handlers
const closeBulkUpdateModal = () => {
  showBulkUpdateModal.value = false;
  selectedTargetCategory.value = '';
};

const handleBulkUpdate = async () => {
  if (!selectedTargetCategory.value || !category.value?.id) return;

  bulkUpdateLoading.value = true;
  try {
    const response = await categoriesService.bulkUpdateProductsCategory(
      category.value.id,
      selectedTargetCategory.value
    );

    closeBulkUpdateModal();

    // Показуємо повідомлення про успіх
    const targetCategoryName = availableCategories.value.find(
      cat => cat.id === selectedTargetCategory.value
    )?.name || 'selected category';

    alert(`Successfully moved ${response.updatedCount} products to ${targetCategoryName}`);

    // Оновлюємо список продуктів
    await fetchProducts(category.value.id);

  } catch (err) {
    console.error('Error updating products category:', err);

    // Обробляємо специфічні помилки від сервера
    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to move products. Please try again.';
    }
  } finally {
    bulkUpdateLoading.value = false;
  }
};

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchCategory(newId);
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  const categoryId = route.params.id;
  if (categoryId) {
    fetchCategory(categoryId);
  }
});
</script>

<style scoped>
.loader-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.media-left .image {
  margin-right: 1rem;
}

.box {
  transition: box-shadow 0.2s ease;
}

.box:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
