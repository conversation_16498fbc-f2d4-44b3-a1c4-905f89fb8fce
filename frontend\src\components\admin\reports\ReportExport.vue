<template>
  <div class="report-export">
    <div class="export-header">
      <h3 class="export-title">
        <i class="fas fa-download"></i>
        Export Report
      </h3>
      <p class="export-description">
        Download your report in various formats for further analysis or sharing.
      </p>
    </div>

    <div class="export-options">
      <!-- Export Format Selection -->
      <div class="format-selection">
        <h4 class="section-title">Select Format</h4>
        <div class="format-grid">
          <div 
            v-for="format in exportFormats" 
            :key="format.key"
            class="format-option"
            :class="{ active: selectedFormat === format.key }"
            @click="selectedFormat = format.key"
          >
            <div class="format-icon">
              <i :class="format.icon"></i>
            </div>
            <div class="format-info">
              <div class="format-name">{{ format.name }}</div>
              <div class="format-description">{{ format.description }}</div>
            </div>
            <div class="format-size">{{ format.size }}</div>
          </div>
        </div>
      </div>

      <!-- Export Options -->
      <div class="export-settings">
        <h4 class="section-title">Export Options</h4>
        
        <div class="settings-grid">
          <!-- Include Charts -->
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="exportOptions.includeCharts"
                class="setting-checkbox"
              />
              <span class="checkmark"></span>
              Include Charts & Visualizations
            </label>
            <p class="setting-description">
              Add charts and graphs to the exported report
            </p>
          </div>

          <!-- Include Raw Data -->
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="exportOptions.includeRawData"
                class="setting-checkbox"
              />
              <span class="checkmark"></span>
              Include Raw Data Tables
            </label>
            <p class="setting-description">
              Include detailed data tables in the export
            </p>
          </div>

          <!-- Include Summary -->
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="exportOptions.includeSummary"
                class="setting-checkbox"
              />
              <span class="checkmark"></span>
              Include Executive Summary
            </label>
            <p class="setting-description">
              Add key metrics and insights summary
            </p>
          </div>

          <!-- Include Filters Info -->
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="exportOptions.includeFilters"
                class="setting-checkbox"
              />
              <span class="checkmark"></span>
              Include Filter Information
            </label>
            <p class="setting-description">
              Show applied filters and date ranges
            </p>
          </div>
        </div>

        <!-- PDF Specific Options -->
        <div v-if="selectedFormat === 'pdf'" class="pdf-options">
          <h5 class="subsection-title">PDF Options</h5>
          <div class="pdf-settings">
            <div class="setting-row">
              <label class="setting-label-inline">Page Orientation:</label>
              <select v-model="exportOptions.pdfOrientation" class="setting-select">
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </div>
            <div class="setting-row">
              <label class="setting-label-inline">Page Size:</label>
              <select v-model="exportOptions.pdfPageSize" class="setting-select">
                <option value="a4">A4</option>
                <option value="letter">Letter</option>
                <option value="legal">Legal</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Excel Specific Options -->
        <div v-if="selectedFormat === 'excel'" class="excel-options">
          <h5 class="subsection-title">Excel Options</h5>
          <div class="excel-settings">
            <div class="setting-item">
              <label class="setting-label">
                <input 
                  type="checkbox" 
                  v-model="exportOptions.excelSeparateSheets"
                  class="setting-checkbox"
                />
                <span class="checkmark"></span>
                Create Separate Sheets for Each Section
              </label>
            </div>
            <div class="setting-item">
              <label class="setting-label">
                <input 
                  type="checkbox" 
                  v-model="exportOptions.excelIncludeFormulas"
                  class="setting-checkbox"
                />
                <span class="checkmark"></span>
                Include Formulas and Calculations
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- File Naming -->
      <div class="file-naming">
        <h4 class="section-title">File Name</h4>
        <div class="filename-input-group">
          <input 
            type="text" 
            v-model="customFilename"
            placeholder="Enter custom filename (optional)"
            class="filename-input"
          />
          <span class="filename-extension">.{{ getFileExtension(selectedFormat) }}</span>
        </div>
        <p class="filename-preview">
          Preview: <strong>{{ finalFilename }}</strong>
        </p>
      </div>
    </div>

    <!-- Export Actions -->
    <div class="export-actions">
      <div class="export-info">
        <div class="estimated-size">
          <i class="fas fa-info-circle"></i>
          Estimated file size: {{ estimatedFileSize }}
        </div>
        <div class="export-time">
          <i class="fas fa-clock"></i>
          Estimated time: {{ estimatedTime }}
        </div>
      </div>
      
      <div class="export-buttons">
        <button 
          @click="previewExport" 
          class="preview-btn"
          :disabled="isExporting"
        >
          <i class="fas fa-eye"></i>
          Preview
        </button>
        
        <button 
          @click="startExport" 
          class="export-btn"
          :disabled="!canExport || isExporting"
        >
          <i class="fas fa-download" :class="{ 'fa-spin': isExporting }"></i>
          {{ isExporting ? 'Exporting...' : 'Export Report' }}
        </button>
      </div>
    </div>

    <!-- Export Progress -->
    <div v-if="isExporting" class="export-progress">
      <div class="progress-header">
        <span class="progress-text">{{ exportStatus }}</span>
        <span class="progress-percentage">{{ exportProgress }}%</span>
      </div>
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: exportProgress + '%' }"
        ></div>
      </div>
      <button @click="cancelExport" class="cancel-btn">
        <i class="fas fa-times"></i>
        Cancel
      </button>
    </div>

    <!-- Export History -->
    <div v-if="exportHistory.length > 0" class="export-history">
      <h4 class="section-title">Recent Exports</h4>
      <div class="history-list">
        <div 
          v-for="export_ in exportHistory" 
          :key="export_.id"
          class="history-item"
        >
          <div class="history-info">
            <div class="history-filename">{{ export_.filename }}</div>
            <div class="history-details">
              {{ export_.format.toUpperCase() }} • {{ export_.size }} • {{ formatDate(export_.date) }}
            </div>
          </div>
          <div class="history-actions">
            <button @click="downloadAgain(export_)" class="download-again-btn">
              <i class="fas fa-download"></i>
              Download Again
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'ReportExport',
  props: {
    reportType: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['exportStarted', 'exportCompleted', 'exportError'],
  setup(props, { emit }) {
    // Reactive state
    const selectedFormat = ref('excel')
    const customFilename = ref('')
    const isExporting = ref(false)
    const exportProgress = ref(0)
    const exportStatus = ref('')
    const exportHistory = ref([])
    
    const exportOptions = reactive({
      includeCharts: true,
      includeRawData: true,
      includeSummary: true,
      includeFilters: true,
      pdfOrientation: 'portrait',
      pdfPageSize: 'a4',
      excelSeparateSheets: true,
      excelIncludeFormulas: true
    })

    // Data
    const exportFormats = [
      {
        key: 'excel',
        name: 'Excel',
        description: 'Spreadsheet with data and charts',
        icon: 'fas fa-file-excel',
        size: '~2-5 MB'
      },
      {
        key: 'pdf',
        name: 'PDF',
        description: 'Formatted report document',
        icon: 'fas fa-file-pdf',
        size: '~1-3 MB'
      },
      {
        key: 'csv',
        name: 'CSV',
        description: 'Raw data in comma-separated format',
        icon: 'fas fa-file-csv',
        size: '~100-500 KB'
      }
    ]

    // Computed properties
    const canExport = computed(() => {
      return selectedFormat.value && props.data && !isExporting.value
    })

    const finalFilename = computed(() => {
      if (customFilename.value.trim()) {
        return customFilename.value.trim()
      }
      
      return reportsService.generateFilename(
        props.reportType,
        props.dateRange.startDate,
        props.dateRange.endDate
      )
    })

    const estimatedFileSize = computed(() => {
      const format = exportFormats.find(f => f.key === selectedFormat.value)
      return format ? format.size : 'Unknown'
    })

    const estimatedTime = computed(() => {
      const baseTime = selectedFormat.value === 'pdf' ? 30 : 
                      selectedFormat.value === 'excel' ? 20 : 10
      
      const dataMultiplier = props.data?.table?.data?.length > 1000 ? 2 : 1
      const chartsMultiplier = exportOptions.includeCharts ? 1.5 : 1
      
      const totalTime = Math.round(baseTime * dataMultiplier * chartsMultiplier)
      
      return totalTime < 60 ? `${totalTime} seconds` : `${Math.round(totalTime / 60)} minutes`
    })

    // Methods
    const getFileExtension = (format) => {
      return reportsService.getFileExtension(format)
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const previewExport = () => {
      // Open preview in new window/modal
      console.log('Preview export with options:', {
        format: selectedFormat.value,
        options: exportOptions,
        filename: finalFilename.value
      })
    }

    const startExport = async () => {
      if (!canExport.value) return

      isExporting.value = true
      exportProgress.value = 0
      exportStatus.value = 'Preparing export...'

      try {
        emit('exportStarted', selectedFormat.value)

        // Simulate progress updates
        const progressInterval = setInterval(() => {
          if (exportProgress.value < 90) {
            exportProgress.value += Math.random() * 20
            
            if (exportProgress.value < 30) {
              exportStatus.value = 'Collecting data...'
            } else if (exportProgress.value < 60) {
              exportStatus.value = 'Generating charts...'
            } else if (exportProgress.value < 90) {
              exportStatus.value = 'Formatting document...'
            }
          }
        }, 500)

        // Prepare export parameters
        const exportParams = {
          reportType: props.reportType,
          startDate: props.dateRange.startDate?.toISOString(),
          endDate: props.dateRange.endDate?.toISOString(),
          filters: props.filters,
          options: exportOptions,
          filename: finalFilename.value
        }

        // Call export service
        const blob = await reportsService.exportReport(
          props.reportType,
          selectedFormat.value,
          exportParams
        )

        clearInterval(progressInterval)
        exportProgress.value = 100
        exportStatus.value = 'Download starting...'

        // Download file
        const filename = finalFilename.value
        reportsService.downloadFile(blob, filename, selectedFormat.value)

        // Add to history
        const exportRecord = {
          id: Date.now(),
          filename: `${filename}.${getFileExtension(selectedFormat.value)}`,
          format: selectedFormat.value,
          size: formatFileSize(blob.size),
          date: new Date(),
          blob: blob
        }
        exportHistory.value.unshift(exportRecord)

        // Keep only last 5 exports
        if (exportHistory.value.length > 5) {
          exportHistory.value = exportHistory.value.slice(0, 5)
        }

        emit('exportCompleted', selectedFormat.value, exportRecord.filename)

      } catch (error) {
        console.error('Export error:', error)
        emit('exportError', selectedFormat.value, error.message)
      } finally {
        isExporting.value = false
        exportProgress.value = 0
        exportStatus.value = ''
      }
    }

    const cancelExport = () => {
      isExporting.value = false
      exportProgress.value = 0
      exportStatus.value = ''
    }

    const downloadAgain = (exportRecord) => {
      reportsService.downloadFile(
        exportRecord.blob,
        exportRecord.filename.replace(/\.[^/.]+$/, ""),
        exportRecord.format
      )
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // Watchers
    watch(selectedFormat, (newFormat) => {
      // Reset format-specific options when format changes
      if (newFormat === 'csv') {
        exportOptions.includeCharts = false
      } else {
        exportOptions.includeCharts = true
      }
    })

    return {
      selectedFormat,
      customFilename,
      isExporting,
      exportProgress,
      exportStatus,
      exportHistory,
      exportOptions,
      exportFormats,
      canExport,
      finalFilename,
      estimatedFileSize,
      estimatedTime,
      getFileExtension,
      formatDate,
      previewExport,
      startExport,
      cancelExport,
      downloadAgain
    }
  }
}
</script>

<style scoped>
.report-export {
  background: #f8fafc;
  border-radius: 8px;
  padding: 24px;
}

.export-header {
  margin-bottom: 32px;
}

.export-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.export-description {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 32px;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.subsection-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 16px 0 12px 0;
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.format-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 16px;
}

.format-option:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.format-option.active {
  border-color: #667eea;
  background: #f0f4ff;
}

.format-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #6b7280;
}

.format-option.active .format-icon {
  background: #667eea;
  color: white;
}

.format-info {
  flex: 1;
}

.format-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.format-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.format-size {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.setting-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.setting-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
  position: relative;
}

.setting-checkbox {
  opacity: 0;
  position: absolute;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  flex-shrink: 0;
}

.setting-checkbox:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.setting-checkbox:checked + .checkmark::after {
  content: '✓';
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

.setting-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 8px 0 0 32px;
  line-height: 1.4;
}

.pdf-options,
.excel-options {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.pdf-settings,
.excel-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.setting-label-inline {
  font-weight: 500;
  color: #374151;
  min-width: 120px;
}

.setting-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.file-naming {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.filename-input-group {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.filename-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px 0 0 6px;
  font-size: 0.875rem;
}

.filename-extension {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-left: none;
  padding: 10px 12px;
  border-radius: 0 6px 6px 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.filename-preview {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.export-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 24px;
}

.export-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.estimated-size,
.export-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #6b7280;
}

.export-buttons {
  display: flex;
  gap: 12px;
}

.preview-btn,
.export-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  border: none;
}

.preview-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.preview-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.export-btn {
  background: #667eea;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background: #5a67d8;
}

.preview-btn:disabled,
.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-progress {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-text {
  font-weight: 500;
  color: #374151;
}

.progress-percentage {
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.cancel-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancel-btn:hover {
  background: #dc2626;
}

.export-history {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.history-info {
  flex: 1;
}

.history-filename {
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.history-details {
  font-size: 0.875rem;
  color: #6b7280;
}

.download-again-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-again-btn:hover {
  background: #5a67d8;
}

@media (max-width: 768px) {
  .export-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .export-buttons {
    justify-content: center;
  }
  
  .format-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
}
</style>
