import{Chart as m,ArcElement as w,<PERSON><PERSON>ut<PERSON><PERSON>roll<PERSON> as S,<PERSON><PERSON><PERSON> as x,<PERSON> as O}from"./chart-DFPvZH9M.js";import{_ as A,g as f,H as E,i as N,j as F,c as n,o as d,a,d as L,F as T,p as U,L as $,t as v}from"./index-L-hJxM_5.js";const z={class:"admin-card admin-orders-chart"},M={class:"admin-card-content"},W={key:0,class:"admin-loading-state"},D={key:1,class:"admin-empty-state"},I={key:2,class:"admin-chart-wrapper"},R={class:"admin-chart-container"},V=["id"],j={class:"admin-status-legend"},q={class:"admin-legend-label"},H={class:"admin-legend-value"},P={__name:"OrdersByStatusChart",props:{data:{type:Array,required:!0},loading:{type:<PERSON><PERSON><PERSON>,default:!1}},setup(c){m.register(w,S,x,O);const t=c,l=f(null),r=f(null),h=f(!1),u=["#ff7700","#10b981","#f59e0b","#ef4444","#3b82f6","#8b5cf6","#06b6d4","#f97316","#ec4899","#6366f1"],p=()=>{if(!l.value){console.warn("Chart canvas not available for OrdersByStatusChart");return}if(!t.data||!Array.isArray(t.data)||t.data.length===0){console.warn("Invalid or empty data for OrdersByStatusChart");return}const e=l.value.getContext("2d");if(!e){console.warn("Unable to get 2D context for OrdersByStatusChart");return}if(r.value){try{r.value.destroy()}catch(s){console.warn("Error destroying chart:",s)}r.value=null}const o=t.data.map(s=>s.status||"Unknown"),i=t.data.map(s=>Number(s.count)||0);r.value=new m(e,{type:"doughnut",data:{labels:o,datasets:[{data:i,backgroundColor:u.slice(0,t.data.length),borderColor:"#ffffff",borderWidth:3,hoverOffset:15,hoverBorderWidth:4,hoverBackgroundColor:u.slice(0,t.data.length).map(s=>s+"dd"),hoverBorderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,cutout:"40%",plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(31, 41, 55, 0.95)",titleFont:{size:14,weight:"bold"},bodyFont:{size:13,weight:"500"},titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"#ff7700",borderWidth:2,padding:12,cornerRadius:8,boxPadding:6,callbacks:{label:function(s){const b=s.label||"",g=s.raw||0,C=s.dataset.data.reduce((k,B)=>k+B,0),_=Math.round(g/C*100);return`${b}: ${g} (${_}%)`}}}}}})},y=()=>{if(!(!r.value||!t.data||!Array.isArray(t.data)))try{r.value.data.labels=t.data.map(e=>e.status||"Unknown"),r.value.data.datasets[0].data=t.data.map(e=>Number(e.count)||0),r.value.update()}catch(e){console.error("Error updating OrdersByStatusChart:",e)}};return E(()=>t.data,e=>{h.value=!1,setTimeout(()=>{e&&e.length>0&&l.value&&(r.value?y():p())},100)},{deep:!0}),N(()=>{setTimeout(()=>{t.data&&t.data.length>0&&l.value&&p()},100)}),F(()=>{if(r.value){try{r.value.destroy()}catch(e){console.warn("Error destroying chart on unmount:",e)}r.value=null}}),(e,o)=>(d(),n("div",z,[o[2]||(o[2]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-chart-pie"}),L(" Orders by Status ")])],-1)),a("div",M,[h.value?(d(),n("div",W,o[0]||(o[0]=[a("div",{class:"admin-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-loading-text"},"Loading chart data...",-1)]))):!c.data||c.data.length===0?(d(),n("div",D,o[1]||(o[1]=[a("div",{class:"admin-empty-icon"},[a("i",{class:"fas fa-chart-pie"})],-1),a("p",{class:"admin-empty-text"},"No order data available",-1)]))):(d(),n("div",I,[a("div",R,[a("canvas",{ref_key:"chartCanvas",ref:l,id:`orders-chart-${Math.random().toString(36).substr(2,9)}`},null,8,V)]),a("div",j,[(d(!0),n(T,null,U(c.data,(i,s)=>(d(),n("div",{key:i.status,class:"admin-legend-item"},[a("span",{class:"admin-legend-color",style:$({backgroundColor:u[s%u.length]})},null,4),a("span",q,v(i.status),1),a("span",H,v(i.count),1)]))),128))])]))])]))}},K=A(P,[["__scopeId","data-v-941e0afd"]]);export{K as default};
