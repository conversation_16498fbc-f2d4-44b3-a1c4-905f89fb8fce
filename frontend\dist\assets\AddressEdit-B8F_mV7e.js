import{_ as L,g as u,h as _,i as M,c as o,a as s,b as V,w,d as b,r as P,k as I,t as r,m as q,x as p,y as R,F as j,p as z,D as g,n as T,l as $,f as Y,e as G,o as i}from"./index-L-hJxM_5.js";import{a as U}from"./addresses-DL07ASCS.js";import{u as H}from"./users-D6yG63l9.js";const J={class:"address-edit"},K={class:"level"},Q={class:"level-left"},W={class:"level-item"},X={class:"breadcrumb"},Z={key:0,class:"has-text-centered"},ss={key:1,class:"columns"},es={class:"column is-8"},ts={class:"card"},as={class:"card-content"},ls={key:0,class:"notification is-danger"},ds={class:"field"},os={class:"control"},is={class:"select is-fullwidth"},rs=["value"],ns={class:"field"},us={class:"control"},cs={class:"field"},vs={class:"control"},ps={class:"field"},ms={class:"control"},gs={class:"field"},fs={class:"control"},ys={class:"field is-grouped"},_s={class:"control"},bs=["disabled"],hs={class:"control"},Cs=["disabled"],As={class:"column is-4"},ks={class:"card"},xs={class:"card-content"},Vs={class:"content"},ws={class:"box has-background-light"},Is={key:0},Us={key:1,class:"has-text-grey"},Ds={class:"card mt-4"},Os={class:"card-content"},Es={class:"content"},Ns={class:"box has-background-info-light"},Ss={key:0},Fs={key:1,class:"has-text-grey"},Bs={key:0,class:"card mt-4"},Ls={__name:"AddressEdit",setup(Ms){const m=Y(),h=G(),c=u(!1),C=u(!0),n=u(null),f=u([]),v=u({}),a=u({userId:"",region:"",city:"",street:"",postalCode:""}),D=_(()=>a.value.region.trim()&&a.value.city.trim()&&a.value.street.trim()),y=_(()=>a.value.userId?f.value.find(t=>t.id===a.value.userId):null),A=_(()=>{var d,l,k,x;const t=v.value,e=a.value;return e.userId!==(t.userId||"")||e.region!==(((d=t.addressVO)==null?void 0:d.region)||t.region||"")||e.city!==(((l=t.addressVO)==null?void 0:l.city)||t.city||"")||e.street!==(((k=t.addressVO)==null?void 0:k.street)||t.street||"")||e.postalCode!==(((x=t.addressVO)==null?void 0:x.postalCode)||t.postalCode||"")}),O=async()=>{try{const t=await U.getAddressById(m.params.id);v.value=t;const e=t.addressVO||t;a.value={userId:t.userId||"",region:e.region||"",city:e.city||"",street:e.street||"",postalCode:e.postalCode||""}}catch(t){n.value=t.message||"Failed to load address"}},E=async()=>{try{const t=await H.getUsers({pageSize:1e3});f.value=t.data||[]}catch(t){console.error("Failed to load users:",t)}},N=async()=>{c.value=!0,n.value=null;try{const t={userId:a.value.userId||null,region:a.value.region.trim(),city:a.value.city.trim(),street:a.value.street.trim(),postalCode:a.value.postalCode.trim()||null};await U.updateAddress(m.params.id,t),h.push({name:"AdminAddressDetail",params:{id:m.params.id}})}catch(t){n.value=t.message||"Failed to update address"}finally{c.value=!1}},S=()=>{h.push({name:"AdminAddressDetail",params:{id:m.params.id}})},F=()=>{const t=[];return a.value.street.trim()&&t.push(a.value.street.trim()),a.value.city.trim()&&t.push(a.value.city.trim()),a.value.region.trim()&&t.push(a.value.region.trim()),a.value.postalCode.trim()&&t.push(a.value.postalCode.trim()),t.length>0?t.join(", "):"Enter address details..."},B=()=>{const t=v.value,e=t.addressVO||t,d=[];return e.street&&d.push(e.street),e.city&&d.push(e.city),e.region&&d.push(e.region),e.postalCode&&d.push(e.postalCode),d.length>0?d.join(", "):"No address data"};return M(async()=>{await Promise.all([O(),E()]),C.value=!1}),(t,e)=>{const d=P("router-link");return i(),o("div",J,[s("div",K,[s("div",Q,[s("div",W,[s("nav",X,[s("ul",null,[s("li",null,[V(d,{to:"/admin/addresses"},{default:w(()=>e[6]||(e[6]=[b("Addresses")])),_:1})]),s("li",null,[V(d,{to:{name:"AdminAddressDetail",params:{id:t.$route.params.id}}},{default:w(()=>e[7]||(e[7]=[b("Address Details")])),_:1},8,["to"])]),e[8]||(e[8]=s("li",{class:"is-active"},[s("a",null,"Edit")],-1))])])])])]),C.value?(i(),o("div",Z,e[9]||(e[9]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading address...",-1)]))):(i(),o("div",ss,[s("div",es,[s("div",ts,[e[19]||(e[19]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Edit Address")],-1)),s("div",as,[n.value?(i(),o("div",ls,[s("button",{class:"delete",onClick:e[0]||(e[0]=l=>n.value=null)}),b(" "+r(n.value),1)])):I("",!0),s("form",{onSubmit:q(N,["prevent"])},[s("div",ds,[e[11]||(e[11]=s("label",{class:"label"},"User (Optional)",-1)),s("div",os,[s("div",is,[p(s("select",{"onUpdate:modelValue":e[1]||(e[1]=l=>a.value.userId=l)},[e[10]||(e[10]=s("option",{value:""},"Select a user (optional)",-1)),(i(!0),o(j,null,z(f.value,l=>(i(),o("option",{key:l.id,value:l.id},r(l.name)+" ("+r(l.email)+") ",9,rs))),128))],512),[[R,a.value.userId]])])]),e[12]||(e[12]=s("p",{class:"help"},"Leave empty to create an unassigned address",-1))]),s("div",ns,[e[13]||(e[13]=s("label",{class:"label"},"Region *",-1)),s("div",us,[p(s("input",{class:"input",type:"text","onUpdate:modelValue":e[2]||(e[2]=l=>a.value.region=l),placeholder:"Enter region",required:""},null,512),[[g,a.value.region]])])]),s("div",cs,[e[14]||(e[14]=s("label",{class:"label"},"City *",-1)),s("div",vs,[p(s("input",{class:"input",type:"text","onUpdate:modelValue":e[3]||(e[3]=l=>a.value.city=l),placeholder:"Enter city",required:""},null,512),[[g,a.value.city]])])]),s("div",ps,[e[15]||(e[15]=s("label",{class:"label"},"Street *",-1)),s("div",ms,[p(s("input",{class:"input",type:"text","onUpdate:modelValue":e[4]||(e[4]=l=>a.value.street=l),placeholder:"Enter street address",required:""},null,512),[[g,a.value.street]])])]),s("div",gs,[e[16]||(e[16]=s("label",{class:"label"},"Postal Code",-1)),s("div",fs,[p(s("input",{class:"input",type:"text","onUpdate:modelValue":e[5]||(e[5]=l=>a.value.postalCode=l),placeholder:"Enter postal code"},null,512),[[g,a.value.postalCode]])])]),s("div",ys,[s("div",_s,[s("button",{type:"submit",class:T(["button is-primary",{"is-loading":c.value}]),disabled:c.value||!D.value||!A.value},e[17]||(e[17]=[s("span",{class:"icon"},[s("i",{class:"fas fa-save"})],-1),s("span",null,"Update Address",-1)]),10,bs)]),s("div",hs,[s("button",{type:"button",class:"button",onClick:S,disabled:c.value},e[18]||(e[18]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Cancel",-1)]),8,Cs)])])],32)])])]),s("div",As,[s("div",ks,[e[22]||(e[22]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Address Preview")],-1)),s("div",xs,[s("div",Vs,[e[20]||(e[20]=s("p",null,[s("strong",null,"Full Address:")],-1)),s("div",ws,[s("p",null,r(F()),1)]),e[21]||(e[21]=s("p",null,[s("strong",null,"Assigned User:")],-1)),y.value?(i(),o("p",Is,r(y.value.name)+" ("+r(y.value.email)+") ",1)):(i(),o("p",Us,"No user assigned"))])])]),s("div",Ds,[e[25]||(e[25]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Original Address")],-1)),s("div",Os,[s("div",Es,[e[23]||(e[23]=s("p",null,[s("strong",null,"Original:")],-1)),s("div",Ns,[s("p",null,r(B()),1)]),e[24]||(e[24]=s("p",null,[s("strong",null,"Original User:")],-1)),v.value.userName?(i(),o("p",Ss,r(v.value.userName),1)):(i(),o("p",Fs,"No user assigned"))])])]),A.value?(i(),o("div",Bs,e[26]||(e[26]=[$('<div class="card-header" data-v-a41a6ba5><p class="card-header-title has-text-warning" data-v-a41a6ba5><span class="icon" data-v-a41a6ba5><i class="fas fa-exclamation-triangle" data-v-a41a6ba5></i></span><span data-v-a41a6ba5>Unsaved Changes</span></p></div><div class="card-content" data-v-a41a6ba5><div class="content" data-v-a41a6ba5><p class="has-text-warning" data-v-a41a6ba5>You have unsaved changes. Don&#39;t forget to save!</p></div></div>',2)]))):I("",!0)])]))])}}},js=L(Ls,[["__scopeId","data-v-a41a6ba5"]]);export{js as default};
