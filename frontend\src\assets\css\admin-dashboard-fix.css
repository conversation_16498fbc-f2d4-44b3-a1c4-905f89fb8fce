/* Admin Dashboard Fix - Specific fixes for dashboard layout and components */

/* ===== DASHBOARD LAYOUT FIXES ===== */

/* Dashboard KPI Grid */
.admin-dashboard__kpi-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(480px, 1fr)) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* Legacy dashboard metrics grid support */
.dashboard-metrics {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

/* KPI Cards */
.admin-dashboard__kpi-card {
  background: white !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e9ecef !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.admin-dashboard__kpi-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
}

.admin-dashboard__kpi-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.admin-dashboard__kpi-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: 1rem !important;
}

.admin-dashboard__kpi-icon {
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-size: 1.25rem !important;
}

.admin-dashboard__kpi-value {
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  color: #2c3e50 !important;
  margin: 0.5rem 0 !important;
  line-height: 1.2 !important;
}

.admin-dashboard__kpi-label {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  color: #6c757d !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  margin: 0 !important;
}

.admin-dashboard__kpi-description {
  font-size: 0.875rem !important;
  color: #8e9aaf !important;
  margin: 0.25rem 0 0 0 !important;
}

.admin-dashboard__kpi-trend {
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 6px !important;
}

.admin-dashboard__kpi-trend--positive {
  background: rgba(40, 167, 69, 0.1) !important;
  color: #28a745 !important;
}

.admin-dashboard__kpi-trend--negative {
  background: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
}

.admin-dashboard__kpi-trend--neutral {
  background: rgba(108, 117, 125, 0.1) !important;
  color: #6c757d !important;
}

/* Legacy metric card support */
.metric-card {
  background: white !important;
  border-radius: 8px !important;
  padding: 1.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  border-left: 4px solid #3498db !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

.metric-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.metric-card h3 {
  margin: 0 0 0.5rem 0 !important;
  color: #2c3e50 !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

.metric-card .metric-value {
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: #3498db !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

.metric-card .metric-change {
  font-size: 0.875rem !important;
  margin-top: 0.5rem !important;
  font-weight: 500 !important;
}

.metric-change.positive {
  color: #27ae60 !important;
}

.metric-change.negative {
  color: #e74c3c !important;
}

/* Charts container */
.charts-container,
.admin-dashboard__charts-grid {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 2rem !important;
}

.chart-card,
.admin-dashboard__chart-card {
  background: white !important;
  border-radius: 8px !important;
  padding: 1.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.chart-card h4,
.admin-dashboard__chart-title {
  margin: 0 0 1rem 0 !important;
  color: #2c3e50 !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
}

/* Chart card variants */
.admin-dashboard__chart-card--main {
  grid-column: 1 !important;
}

.admin-dashboard__chart-card--side {
  grid-column: 2 !important;
}

.admin-dashboard__chart-card--full {
  grid-column: 1 / -1 !important;
}

/* Chart header and body styles */
.admin-dashboard__chart-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 1rem !important;
  padding-bottom: 0.75rem !important;
  border-bottom: 1px solid #e9ecef !important;
}

.admin-dashboard__chart-body {
  position: relative !important;
  height: 300px !important;
  min-height: 300px !important;
}

.admin-dashboard__chart-body canvas {
  width: 100% !important;
  height: 100% !important;
  max-height: 300px !important;
}

/* Chart controls */
.admin-dashboard__chart-controls {
  display: flex !important;
  gap: 0.5rem !important;
  align-items: center !important;
}

.admin-dashboard__chart-select {
  padding: 0.25rem 0.5rem !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  font-size: 0.875rem !important;
  background: white !important;
}

/* Dashboard Sections */
.admin-dashboard__section-header {
  margin-bottom: 2rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 2px solid #e9ecef !important;
}

.admin-dashboard__section-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  margin: 0 0 0.5rem 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

.admin-dashboard__section-icon {
  color: #667eea !important;
}

.admin-dashboard__section-subtitle {
  color: #6c757d !important;
  font-size: 0.875rem !important;
  margin: 0 !important;
}

.admin-dashboard__kpi-section {
  margin-bottom: 3rem !important;
}

/* Legacy KPI section support */
.kpi-section {
  margin-bottom: 2rem !important;
}

.kpi-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 2px solid #e9ecef !important;
}

.kpi-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  margin: 0 !important;
}

.kpi-subtitle {
  color: #6c757d !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
}

/* Order Status Distribution */
.order-status-card {
  background: white !important;
  border-radius: 8px !important;
  padding: 1.5rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.order-status-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.order-status-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0.75rem 0 !important;
  border-bottom: 1px solid #f8f9fa !important;
}

.order-status-item:last-child {
  border-bottom: none !important;
}

.status-indicator {
  display: inline-block !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  margin-right: 0.75rem !important;
}

.status-processing { background-color: #ffc107 !important; }
.status-pending { background-color: #6c757d !important; }
.status-shipped { background-color: #17a2b8 !important; }
.status-delivered { background-color: #28a745 !important; }
.status-cancelled { background-color: #dc3545 !important; }

.status-count {
  font-weight: 600 !important;
  color: #2c3e50 !important;
}

/* Site Commission Revenue */
.commission-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 1.5rem !important;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3) !important;
}

.commission-card h4 {
  color: white !important;
  margin-bottom: 1rem !important;
}

.commission-value {
  font-size: 1.75rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
}

.commission-period {
  opacity: 0.9 !important;
  font-size: 0.875rem !important;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .charts-container,
  .admin-dashboard__charts-grid {
    grid-template-columns: 1fr !important;
  }

  .admin-dashboard__chart-card--main,
  .admin-dashboard__chart-card--side,
  .admin-dashboard__chart-card--full {
    grid-column: 1 !important;
  }
  
  .dashboard-metrics {
    grid-template-columns: 1fr !important;
  }
  
  .kpi-header {
    flex-direction: column !important;
    align-items: flex-start !important;
  }
  
  .metric-card .metric-value {
    font-size: 1.5rem !important;
  }
}

@media screen and (max-width: 480px) {
  .metric-card {
    padding: 1rem !important;
  }
  
  .chart-card,
  .admin-dashboard__chart-card {
    padding: 1rem !important;
  }
  
  .commission-card {
    padding: 1rem !important;
  }
  
  .kpi-title {
    font-size: 1.25rem !important;
  }
}

/* ===== CHART SPECIFIC FIXES ===== */
.chart-container {
  position: relative !important;
  height: 300px !important;
  width: 100% !important;
}

.chart-container canvas {
  max-height: 300px !important;
}

/* Sales Overview Chart */
.sales-overview-chart {
  height: 250px !important;
}

/* Orders by Status Chart */
.orders-chart {
  height: 200px !important;
}

/* ===== LOADING STATES ===== */
.dashboard-loading {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 200px !important;
  color: #6c757d !important;
}

.loading-spinner {
  width: 2rem !important;
  height: 2rem !important;
  border: 3px solid #f8f9fa !important;
  border-top: 3px solid #3498db !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin-right: 1rem !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== ERROR STATES ===== */
.dashboard-error {
  background: #f8d7da !important;
  color: #721c24 !important;
  padding: 1rem !important;
  border-radius: 8px !important;
  border: 1px solid #f5c6cb !important;
  margin-bottom: 1rem !important;
}

/* ===== REFRESH BUTTON ===== */
.refresh-button {
  background: #3498db !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.refresh-button:hover {
  background: #2980b9 !important;
}

.refresh-button:disabled {
  background: #bdc3c7 !important;
  cursor: not-allowed !important;
}
