/* ===== ADMIN FILTERS SYSTEM ===== */
/* Filter panels and search components */

/* ===== FILTER CONTAINER ===== */
.admin-filter-container {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-xl);
  margin-bottom: var(--admin-space-xl);
  box-shadow: var(--admin-shadow-sm);
  border: 1px solid var(--admin-border-light);
}

.admin-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-lg);
  padding-bottom: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
}

.admin-filter-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-filter-actions {
  display: flex;
  gap: var(--admin-space-sm);
}

/* ===== FILTER GRID ===== */
.admin-filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--admin-space-lg);
  align-items: end;
}

.admin-filter-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.admin-filter-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.admin-filter-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* ===== SEARCH BOX ===== */
.admin-search-container {
  position: relative;
  width: 100%;
}

.admin-search-input {
  width: 100%;
  padding: var(--admin-space-md) var(--admin-space-md) var(--admin-space-md) var(--admin-space-4xl);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-900);
  background: var(--admin-white);
  transition: all var(--admin-transition-fast);
}

.admin-search-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
}

.admin-search-icon {
  position: absolute;
  left: var(--admin-space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
  pointer-events: none;
}

.admin-search-clear {
  position: absolute;
  right: var(--admin-space-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--admin-gray-400);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-fast);
}

.admin-search-clear:hover {
  color: var(--admin-gray-600);
  background: var(--admin-gray-100);
}

/* ===== FILTER FIELD ===== */
.admin-filter-field {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-filter-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-700);
  margin: 0;
}

.admin-filter-input,
.admin-filter-select {
  padding: var(--admin-space-md);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-900);
  background: var(--admin-white);
  transition: all var(--admin-transition-fast);
  min-height: 42px;
  line-height: 1.5;
}

.admin-filter-input:focus,
.admin-filter-select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
}

.admin-filter-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--admin-space-md) center;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-right: var(--admin-space-4xl);
}

/* ===== FILTER BUTTONS ===== */
.admin-filter-buttons {
  display: flex;
  gap: var(--admin-space-sm);
  justify-content: flex-end;
  margin-top: var(--admin-space-lg);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
}

.admin-filter-btn {
  padding: var(--admin-space-md) var(--admin-space-xl);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
  border: 1px solid transparent;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-filter-btn-primary {
  background: var(--admin-primary);
  color: var(--admin-white);
  border-color: var(--admin-primary);
}

.admin-filter-btn-primary:hover {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--admin-shadow-md);
}

.admin-filter-btn-secondary {
  background: var(--admin-white);
  color: var(--admin-gray-700);
  border-color: var(--admin-border-light);
}

.admin-filter-btn-secondary:hover {
  background: var(--admin-gray-50);
  border-color: var(--admin-gray-300);
}

/* ===== FILTER TAGS ===== */
.admin-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-sm);
  margin-top: var(--admin-space-md);
}

.admin-filter-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  background: var(--admin-primary-bg);
  color: var(--admin-primary-dark);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
}

.admin-filter-tag-remove {
  background: none;
  border: none;
  color: var(--admin-primary-dark);
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--admin-transition-fast);
}

.admin-filter-tag-remove:hover {
  background: var(--admin-primary);
  color: var(--admin-white);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .admin-filter-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-filter-grid-2,
  .admin-filter-grid-3,
  .admin-filter-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .admin-filter-buttons {
    flex-direction: column;
  }
  
  .admin-filter-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .admin-filter-container {
    padding: var(--admin-space-lg);
  }
  
  .admin-filter-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-md);
  }
  
  .admin-filter-actions {
    justify-content: stretch;
  }
}

/* ===== SEARCH AND FILTERS COMPONENT ===== */
/* Styles for the unified SearchAndFilters.vue component */

.admin-search-and-filters {
  margin-bottom: var(--admin-space-xl);
}

.admin-filters-panel {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-xl);
  margin-bottom: var(--admin-space-md);
  border: 1px solid var(--admin-border-light);
  box-shadow: var(--admin-shadow-md);
}

.admin-filters-content {
  width: 100%;
}

.admin-filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-lg);
  align-items: end;
}

.admin-filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
  flex: 1;
}

.admin-filter-group-default {
  flex: 0 0 auto;
  min-width: 180px;
}

.admin-filter-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-white);
  margin-bottom: var(--admin-space-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Search Input Styles */
.admin-search-wrapper {
  position: relative;
}

.admin-search-input {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md) var(--admin-space-2xl) var(--admin-space-md) var(--admin-space-md);
  font-size: var(--admin-text-base);
  color: var(--admin-gray-900);
  width: 100%;
  transition: all var(--admin-transition);
}

.admin-search-input:focus {
  outline: none;
  border-color: var(--admin-orange-500);
  box-shadow: 0 0 0 2px var(--admin-orange-100);
}

.admin-search-input::placeholder {
  color: var(--admin-gray-500);
}

.admin-search-icon {
  position: absolute;
  right: var(--admin-space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-gray-500);
  pointer-events: none;
  font-size: var(--admin-text-sm);
}

/* Filter Control Styles */
.admin-filter-control {
  position: relative;
}

.admin-input {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-size: var(--admin-text-base);
  color: var(--admin-gray-900);
  width: 100%;
  transition: all var(--admin-transition);
}

.admin-input:focus {
  outline: none;
  border-color: var(--admin-orange-500);
  box-shadow: 0 0 0 2px var(--admin-orange-100);
}

.admin-input::placeholder {
  color: var(--admin-gray-500);
}

/* Select Dropdown Styles */
.admin-select-wrapper {
  position: relative;
}

.admin-select {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md) var(--admin-space-2xl) var(--admin-space-md) var(--admin-space-md);
  font-size: var(--admin-text-base);
  color: var(--admin-gray-900);
  width: 100%;
  cursor: pointer;
  appearance: none;
  transition: all var(--admin-transition);
}

.admin-select:focus {
  outline: none;
  border-color: var(--admin-orange-500);
  box-shadow: 0 0 0 2px var(--admin-orange-100);
}

.admin-select option {
  background: var(--admin-white);
  color: var(--admin-gray-900);
  padding: var(--admin-space-sm);
}

.admin-select-arrow {
  position: absolute;
  right: var(--admin-space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-gray-500);
  pointer-events: none;
  font-size: var(--admin-text-sm);
}

/* Filter Actions */
.admin-filter-actions {
  flex: 0 0 auto;
  min-width: auto;
  align-self: flex-end;
}

/* Status Bar */
.admin-status-bar {
  background: var(--admin-white);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  margin-bottom: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  box-shadow: var(--admin-shadow-xs);
}

.admin-status-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--admin-space-sm);
}

.admin-status-info {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  flex-wrap: wrap;
}

.admin-status-count {
  font-weight: var(--admin-font-bold);
  color: var(--admin-orange-600);
  font-size: var(--admin-text-lg);
}

.admin-status-text {
  color: var(--admin-gray-900);
  font-size: var(--admin-text-base);
}

.admin-active-filters {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  flex-wrap: wrap;
}

.admin-filters-label {
  color: var(--admin-gray-600);
  font-size: var(--admin-text-sm);
}

.admin-filter-tag {
  background: var(--admin-orange-100);
  color: var(--admin-orange-700);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  border: 1px solid var(--admin-orange-200);
}

.admin-filter-tag-remove {
  background: none;
  border: none;
  color: var(--admin-orange-600);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  transition: all var(--admin-transition);
}

.admin-filter-tag-remove:hover {
  background: var(--admin-orange-600);
  color: var(--admin-white);
}
