import api from '@/services/api';

const addressesService = {
  async getAddresses(params = {}) {
    try {
      // Очищаємо пусті параметри
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== null && value !== undefined && value !== '')
      );

      const response = await api.get('/api/admin/addresses', { params: cleanParams });

      // Handle different response structures
      if (response.data) {
        // If it's wrapped in ApiResponse format
        if (response.data.success && response.data.data) {
          return response.data.data;
        }
        // If it's direct PagedResponse format
        else if (response.data.data && Array.isArray(response.data.data)) {
          return response.data;
        }
        // If it's direct array
        else if (Array.isArray(response.data)) {
          return {
            data: response.data,
            totalCount: response.data.length,
            page: 1,
            pageSize: response.data.length,
            totalPages: 1
          };
        }
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching addresses:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.errors?.[0] ||
                          'Failed to load addresses';
      throw new Error(errorMessage);
    }
  },

  async getAddressesWithFilters(filters = {}) {
    try {
      const params = {
        filter: filters.search,
        orderBy: filters.sortBy || 'CreatedAt',
        descending: filters.sortOrder === 'desc',
        page: filters.page,
        pageSize: filters.pageSize,
        region: filters.region,
        city: filters.city,
        postalCode: filters.postalCode,
        userId: filters.userId,
        userEmail: filters.userEmail,
        isActive: filters.isActive,
        createdAfter: filters.createdAfter,
        createdBefore: filters.createdBefore
      };

      return await addressesService.getAddresses(params);
    } catch (error) {
      console.error('Error fetching addresses with filters:', error);
      throw error;
    }
  },

  async getAddressById(id) {
    try {
      const response = await api.get(`/api/admin/addresses/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching address:', error);
      throw new Error(error.response?.data?.message || 'Failed to load address details');
    }
  },

  async getAddressesByUserId(userId, params = {}) {
    try {
      const response = await api.get(`/api/admin/addresses/user/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user addresses:', error);
      throw new Error(error.response?.data?.message || 'Failed to load user addresses');
    }
  },

  async createAddress(data) {
    try {
      const response = await api.post('/api/admin/addresses', data);
      return response.data;
    } catch (error) {
      console.error('Error creating address:', error);
      throw new Error(error.response?.data?.message || 'Failed to create address');
    }
  },

  async updateAddress(id, data) {
    try {
      const response = await api.put(`/api/admin/addresses/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating address:', error);
      throw new Error(error.response?.data?.message || 'Failed to update address');
    }
  },

  async deleteAddress(id) {
    try {
      const response = await api.delete(`/api/admin/addresses/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting address:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete address';

      // Provide more user-friendly error messages
      if (errorMessage.includes('used in orders')) {
        throw new Error('Cannot delete this address because it is used in existing orders. Please update or cancel the orders first.');
      }

      throw new Error(errorMessage);
    }
  },

  async getAddressStats() {
    try {
      const response = await api.get('/api/admin/addresses/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching address stats:', error);
      throw new Error(error.response?.data?.message || 'Failed to load address statistics');
    }
  },

  async getAddressStatistics(region = null, city = null, userId = null) {
    try {
      const params = {};
      if (region) params.region = region;
      if (city) params.city = city;
      if (userId) params.userId = userId;

      const response = await api.get('/api/admin/addresses/statistics', { params });
      return response.data.data || response.data;
    } catch (error) {
      throw addressesService.handleApiError(error, 'Failed to load address statistics');
    }
  },

  // Покращена обробка помилок
  handleApiError(error, defaultMessage = 'Operation failed') {
    console.error('API Error:', error);

    if (error.response) {
      const message = error.response.data?.message ||
                     error.response.data?.errors?.[0] ||
                     `Server error: ${error.response.status}`;
      return new Error(message);
    } else if (error.request) {
      return new Error('Network error: No response from server');
    } else {
      return new Error(error.message || defaultMessage);
    }
  },

  // Утилітарні методи для роботи з адресами
  formatFullAddress(address) {
    if (!address) return 'Unknown Address';
    return `${address.street}, ${address.city}, ${address.region}, ${address.postalCode}`;
  },

  validateAddress(address) {
    const errors = [];

    if (!address.region || address.region.trim().length === 0) {
      errors.push('Region is required');
    }

    if (!address.city || address.city.trim().length === 0) {
      errors.push('City is required');
    }

    if (!address.street || address.street.trim().length === 0) {
      errors.push('Street is required');
    }

    if (!address.postalCode || address.postalCode.trim().length === 0) {
      errors.push('Postal code is required');
    } else if (!/^\d{5}$/.test(address.postalCode.trim())) {
      errors.push('Postal code must be 5 digits');
    }

    return errors;
  },

  getActivityStatus(address) {
    if (address.isActive) {
      return { status: 'active', color: 'success', text: 'Active' };
    } else {
      return { status: 'inactive', color: 'danger', text: 'Inactive' };
    }
  },

  formatLastUsed(lastUsedAt) {
    if (!lastUsedAt) return 'Never used';

    const date = new Date(lastUsedAt);
    const now = new Date();
    const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  }
};

export default addressesService;
export { addressesService };
