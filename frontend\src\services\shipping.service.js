class ShippingService {
  constructor() {
    // Статичні методи доставки
    this.shippingMethods = [
      {
        id: 'nova-poshta',
        name: 0, // NovaPoshta
        priceAmount: '50',
        estimatedDays: 2,
        description: 'Нова Пошта'
      },
      {
        id: 'ukr-poshta',
        name: 1, // UkrPoshta
        priceAmount: '40',
        estimatedDays: 5,
        description: 'Укрпошта'
      },
      {
        id: 'courier',
        name: 2, // Courier
        priceAmount: '100',
        estimatedDays: 1,
        description: 'Кур\'єр'
      },
      {
        id: 'self-pickup',
        name: 3, // SelfPickup
        priceAmount: '0',
        estimatedDays: 0,
        description: 'Самовивіз'
      }
    ];
  }

  /**
   * Отримує всі доступні методи доставки
   * @returns {Promise} - Список методів доставки
   */
  async getShippingMethods() {
    try {
      // Симулюємо асинхронний запит
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        success: true,
        data: {
          data: this.shippingMethods,
          total: this.shippingMethods.length
        }
      };
    } catch (error) {
      console.error('Error fetching shipping methods:', error);
      throw error;
    }
  }

  /**
   * Отримує конкретний метод доставки за ID
   * @param {string} id - ID методу доставки
   * @returns {Promise} - Метод доставки
   */
  async getShippingMethodById(id) {
    try {
      const method = this.shippingMethods.find(m => m.id === id);
      if (!method) {
        throw new Error('Метод доставки не знайдено');
      }
      return {
        success: true,
        data: method
      };
    } catch (error) {
      console.error('Error fetching shipping method:', error);
      throw error;
    }
  }

  /**
   * Розраховує вартість доставки
   * @param {string} methodId - ID методу доставки
   * @param {number} cartTotal - Загальна вартість корзини
   * @returns {Object} - Розрахунок вартості доставки
   */
  calculateShippingCost(method, cartTotal) {
    if (!method) {
      return {
        cost: 0,
        isFree: false,
        method: null
      };
    }

    const cost = parseFloat(method.priceAmount) || 0;
    
    // Самовивіз завжди безкоштовний або дуже дешевий
    const isFree = method.name === 0 || cost === 0; // 0 = SelfPickup

    return {
      cost: isFree ? 0 : cost,
      isFree,
      method,
      estimatedDays: method.estimatedDays,
      name: this.getShippingMethodName(method.name)
    };
  }

  /**
   * Отримує назву методу доставки
   * @param {number|string} nameEnum - Enum значення назви або ID
   * @returns {string} - Локалізована назва
   */
  getShippingMethodName(nameEnum) {
    // Якщо передано ID замість enum
    if (typeof nameEnum === 'string') {
      const method = this.shippingMethods.find(m => m.id === nameEnum);
      return method ? method.description : 'Невідомий метод';
    }

    // Якщо передано enum
    const names = {
      0: 'Нова Пошта',
      1: 'Укрпошта',
      2: 'Кур\'єр',
      3: 'Самовивіз'
    };
    return names[nameEnum] || 'Невідомий метод';
  }

  /**
   * Отримує іконку для методу доставки
   * @param {number|string} nameEnum - Enum значення назви або ID
   * @returns {string} - CSS клас іконки
   */
  getShippingMethodIcon(nameEnum) {
    // Якщо передано ID замість enum
    if (typeof nameEnum === 'string') {
      const iconMap = {
        'nova-poshta': 'fas fa-truck',
        'ukr-poshta': 'fas fa-envelope',
        'courier': 'fas fa-motorcycle',
        'self-pickup': 'fas fa-store'
      };
      return iconMap[nameEnum] || 'fas fa-shipping-fast';
    }

    // Якщо передано enum
    const icons = {
      0: 'fas fa-truck', // Нова Пошта
      1: 'fas fa-envelope', // Укрпошта
      2: 'fas fa-motorcycle', // Кур'єр
      3: 'fas fa-store' // Самовивіз
    };
    return icons[nameEnum] || 'fas fa-shipping-fast';
  }

  /**
   * Форматує час доставки
   * @param {number} estimatedDays - Кількість днів
   * @returns {string} - Форматований текст
   */
  formatDeliveryTime(estimatedDays) {
    if (estimatedDays === 0) {
      return 'Сьогодні';
    } else if (estimatedDays === 1) {
      return '1 день';
    } else if (estimatedDays <= 4) {
      return `${estimatedDays} дні`;
    } else {
      return `${estimatedDays} днів`;
    }
  }
}

export default new ShippingService();
