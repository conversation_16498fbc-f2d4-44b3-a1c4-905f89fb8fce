import apiService from '@/services/api';

export const sellerRequestsService = {
  async getSellerRequests(params = {}) {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = '/api/admin/seller-requests';

      // Очищаємо пусті параметри
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
      );

      console.log(`🌐 Making API call to ${url} with params:`, params);
      console.log(`🧹 Clean params:`, cleanParams);
      const response = await apiService.get(url, { params: cleanParams });
      console.log(`🌐 Full API response:`, response);
      console.log(`🌐 Response status:`, response.status);
      console.log(`🌐 Response data:`, response.data);

      if (response.data && response.data.success && response.data.data) {
        const paginatedData = response.data.data;
        console.log('📊 Paginated data structure:', paginatedData);

        // Повертаємо дані в форматі, який очікує компонент
        return {
          success: true,
          data: paginatedData
        };
      }

      console.error('❌ Invalid response format:', response.data);
      console.error('❌ Expected structure: { success: true, data: { items: [...] } }');
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('❌ Error fetching seller requests:', error);
      console.error('❌ Error response:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      throw error;
    }
  },

  async getSellerRequestById(id) {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = `/api/admin/seller-requests/${id}`;

      console.log(`Fetching seller request from ${url}`);
      const response = await apiService.get(url);

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error(`Error fetching seller request ${id}:`, error);
      throw error;
    }
  },

  async approveSellerRequest(id) {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = `/api/admin/seller-requests/${id}/approve`;

      console.log(`Approving seller request at ${url}`);
      const response = await apiService.post(url);

      if (response.data && response.data.success) {
        return {
          success: true,
          request: {
            id,
            status: 'approved',
            updatedAt: new Date()
          }
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error(`Error approving seller request ${id}:`, error);
      throw error;
    }
  },

  async rejectSellerRequest(id, reason = '') {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = `/api/admin/seller-requests/${id}/reject`;

      console.log(`Rejecting seller request at ${url} with reason: ${reason}`);
      const response = await apiService.post(url, { reason });

      if (response.data && response.data.success) {
        return {
          success: true,
          request: {
            id,
            status: 'rejected',
            rejectionReason: reason,
            updatedAt: new Date()
          }
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error(`Error rejecting seller request ${id}:`, error);
      throw error;
    }
  },

  async getSellerRequestStats() {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = '/api/admin/seller-requests/stats';

      console.log(`Fetching seller request stats from ${url}`);
      const response = await apiService.get(url);

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching seller request stats:', error);
      throw error;
    }
  },

  async bulkApproveSellerRequests(requestIds) {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = '/api/admin/seller-requests/bulk-approve';

      console.log(`Bulk approving seller requests at ${url} with IDs:`, requestIds);
      const response = await apiService.post(url, { requestIds });

      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          approvedCount: response.data.data.approvedCount
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error bulk approving seller requests:', error);
      throw error;
    }
  },

  async bulkRejectSellerRequests(requestIds, reason = '') {
    try {
      // Тепер всі користувачі (адміністратори та модератори) використовують той самий URL
      const url = '/api/admin/seller-requests/bulk-reject';

      console.log(`Bulk rejecting seller requests at ${url} with IDs:`, requestIds);
      const response = await apiService.post(url, { requestIds, reason });

      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          rejectedCount: response.data.data.rejectedCount
        };
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error bulk rejecting seller requests:', error);
      throw error;
    }
  },

  async getSellerRequest(id) {
    try {
      const url = `/api/admin/seller-requests/${id}`;
      console.log(`Getting seller request from ${url}`);

      const response = await apiService.get(url);

      if (response.data && response.data.success && response.data.data) {
        return response.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error getting seller request:', error);
      throw error;
    }
  }
};
