﻿﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Services.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.IO;

namespace Marketplace.Application.Commands.Company;

public class UploadSellerCompanyImageCommandHandler : IRequestHandler<UploadSellerCompanyImageCommand, string>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly IFileStorageService _fileStorageService;

    public UploadSellerCompanyImageCommandHandler(
        ICompanyRepository companyRepository,
        ICompanyUserRepository companyUserRepository,
        IFileStorageService fileStorageService)
    {
        _companyRepository = companyRepository;
        _companyUserRepository = companyUserRepository;
        _fileStorageService = fileStorageService;
    }

    public async Task<string> Handle(UploadSellerCompanyImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо зв'язок користувача з компанією
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.UserId,
            cancellationToken: cancellationToken
        );

        var companyUser = companyUsers.FirstOrDefault();
        if (companyUser == null)
            throw new InvalidOperationException("Користувач не пов'язаний з жодною компанією.");

        // Перевіряємо, чи користувач є власником компанії
        if (!companyUser.IsOwner)
            throw new UnauthorizedAccessException("Тільки власник компанії може завантажувати зображення.");

        // Отримуємо компанію
        var company = await _companyRepository.GetByIdAsync(companyUser.CompanyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException("Компанію не знайдено.");

        // Перевіряємо, чи файл існує
        if (request.Image == null || request.Image.Length == 0)
            throw new InvalidOperationException("Файл не вибрано або він порожній.");

        // Перевіряємо тип файлу
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
        var extension = Path.GetExtension(request.Image.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(extension))
            throw new InvalidOperationException("Недопустимий тип файлу. Дозволені типи: .jpg, .jpeg, .png, .gif");

        // Генеруємо унікальне ім'я файлу
        var fileName = $"{company.Id}_{Guid.NewGuid()}{extension}";

        // Зберігаємо файл і отримуємо URL
        var imageUrl = await _fileStorageService.SaveFileAsync(
            request.Image,
            "uploads/companies",
            fileName,
            cancellationToken);
        company.Image = new Url(imageUrl);
        await _companyRepository.UpdateAsync(company, cancellationToken);

        return imageUrl;
    }
}
