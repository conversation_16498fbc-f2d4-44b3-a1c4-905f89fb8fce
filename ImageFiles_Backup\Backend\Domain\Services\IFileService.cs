﻿﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Marketplace.Domain.Services;

/// <summary>
/// Інтерфейс для сервісу роботи з файлами
/// </summary>
public interface IFileService
{
    /// <summary>
    /// Зберігає файл та повертає URL для доступу до нього
    /// </summary>
    /// <param name="fileStream">Потік файлу</param>
    /// <param name="fileName">Ім'я файлу</param>
    /// <param name="contentType">MIME-тип файлу</param>
    /// <param name="folder">Папка для зберігання (опціонально)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>URL для доступу до файлу</returns>
    Task<string> SaveFileAsync(Stream fileStream, string fileName, string contentType, string folder = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Видаляє файл за URL
    /// </summary>
    /// <param name="fileUrl">URL файлу</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>True, якщо файл успішно видалено</returns>
    Task<bool> DeleteFileAsync(string fileUrl, CancellationToken cancellationToken = default);

    /// <summary>
    /// Отримує файл за URL
    /// </summary>
    /// <param name="fileUrl">URL файлу</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Потік файлу та MIME-тип</returns>
    Task<(Stream FileStream, string ContentType)> GetFileAsync(string fileUrl, CancellationToken cancellationToken = default);

    /// <summary>
    /// Перевіряє, чи існує файл за URL
    /// </summary>
    /// <param name="fileUrl">URL файлу</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>True, якщо файл існує</returns>
    Task<bool> FileExistsAsync(string fileUrl, CancellationToken cancellationToken = default);
}
