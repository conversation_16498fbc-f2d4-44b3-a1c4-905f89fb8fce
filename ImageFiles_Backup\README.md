# 📋 Backup всіх файлів, пов'язаних з операціями над зображеннями

Цей backup містить всі файли з проекту Marketplace, які пов'язані з операціями над зображеннями як на бекенді, так і на фронтенді.

## 📁 Структура backup'у

```
ImageFiles_Backup/
├── Backend/                          # Бекенд файли
│   ├── Domain/                       # Доменний шар
│   │   ├── Entities/                 # Сутності
│   │   ├── Repositories/             # Інтерфейси репозиторіїв
│   │   └── Services/                 # Доменні сервіси
│   ├── Infrastructure/               # Інфраструктурний шар
│   │   ├── Services/                 # Реалізації сервісів
│   │   ├── Persistence/              # Репозиторії та БД
│   │   └── Migrations/               # Міграції БД
│   ├── Application/                  # Прикладний шар
│   │   ├── Commands/                 # CQRS команди
│   │   └── Validators/               # Валідатори
│   └── Presentation/                 # Презентаційний шар
│       ├── Controllers/              # API контролери
│       ├── wwwroot/uploads/          # Завантажені файли
│       └── appsettings.json          # Конфігурація
├── Frontend/                         # Фронтенд файли
│   ├── services/                     # Сервіси
│   ├── admin/                        # Адмін панель
│   │   ├── components/               # Компоненти адмін панелі
│   │   └── views/                    # В'юшки адмін панелі
│   ├── components/                   # Публічні компоненти
│   ├── views/                        # Публічні в'юшки
│   ├── assets/images/                # Статичні зображення
│   ├── public/                       # Публічні файли
│   └── BUGFIX-REPORT.md              # Документація
└── README.md                         # Цей файл
```

## 🔧 Бекенд файли

### Доменні сутності:
- `ProductImage.cs` - Сутність зображення продукту
- `Category.cs` - Сутність категорії (містить поле Image)
- `Company.cs` - Сутність компанії (містить поле Image)

### Репозиторії:
- `IProductImageRepository.cs` - Інтерфейс репозиторію зображень продуктів
- `ProductImageRepository.cs` - Реалізація репозиторію зображень продуктів

### Сервіси:
- `IImageService.cs` - Інтерфейс універсального сервісу зображень
- `ImageService.cs` - Реалізація універсального сервісу зображень
- `IFileService.cs` - Інтерфейс файлового сервісу
- `LocalFileService.cs` - Локальний файловий сервіс
- `FileStorageService.cs` - Сервіс зберігання файлів
- `FileServiceOptions.cs` - Опції конфігурації файлового сервісу

### CQRS Команди:
#### Загальні команди файлів:
- `UploadImageCommand.cs` / `UploadImageCommandHandler.cs`
- `DeleteImageCommand.cs` / `DeleteImageCommandHandler.cs`

#### Універсальні команди зображень:
- `UploadUniversalImageCommand.cs` / `UploadUniversalImageCommandHandler.cs`
- `UpdateUniversalImageCommand.cs` / `UpdateUniversalImageCommandHandler.cs`
- `DeleteUniversalImageCommand.cs` / `DeleteUniversalImageCommandHandler.cs`
- `SetMainUniversalImageCommand.cs` / `SetMainUniversalImageCommandHandler.cs`
- `UploadMultipleUniversalImagesCommand.cs` / `UploadMultipleUniversalImagesCommandHandler.cs`

#### Команди мета-зображень:
- `UploadMetaImageCommand.cs` / `UploadMetaImageCommandHandler.cs`
- `DeleteMetaImageCommand.cs` / `DeleteMetaImageCommandHandler.cs`
- `UploadUniversalMetaImageCommand.cs` / `UploadUniversalMetaImageCommandHandler.cs`
- `UpdateUniversalMetaImageCommand.cs` / `UpdateUniversalMetaImageCommandHandler.cs`
- `DeleteUniversalMetaImageCommand.cs` / `DeleteUniversalMetaImageCommandHandler.cs`

#### Команди зображень продуктів:
- `UploadProductImageCommand.cs` / `UploadProductImageCommandHandler.cs`
- `UploadMultipleProductImagesCommand.cs` / `UploadMultipleProductImagesCommandHandler.cs`
- `UploadSellerProductImageCommand.cs` / `UploadSellerProductImageCommandHandler.cs`
- `DeleteProductImageCommand.cs` / `DeleteProductImageCommandHandler.cs`
- `DeleteSellerProductImageCommand.cs` / `DeleteSellerProductImageCommandHandler.cs`
- `UpdateProductImageCommand.cs` / `UpdateProductImageCommandHandler.cs`
- `UpdateSellerProductImageCommand.cs` / `UpdateSellerProductImageCommandHandler.cs`
- `SetMainProductImageCommand.cs` / `SetMainProductImageCommandHandler.cs`
- `UpdateProductImageOrderCommand.cs` / `UpdateProductImageOrderCommandHandler.cs`
- `UpdateProductImageAltTextCommand.cs` / `UpdateProductImageAltTextCommandHandler.cs`
- `StoreProductImageCommand.cs` / `StoreProductImageCommandHandler.cs`
- `AddSellerProductImageCommand.cs` / `AddSellerProductImageCommandHandler.cs`

#### Команди зображень категорій:
- `UploadCategoryImageCommand.cs` / `UploadCategoryImageCommandHandler.cs`

#### Команди зображень компаній:
- `UploadCompanyImageCommand.cs` / `UploadCompanyImageCommandHandler.cs`
- `UploadSellerCompanyImageCommand.cs` / `UploadSellerCompanyImageCommandHandler.cs`
- `DeleteCompanyImageCommand.cs` / `DeleteCompanyImageCommandHandler.cs`

#### Команди зображень заявок продавців:
- `UploadSellerRequestImageCommand.cs` / `UploadSellerRequestImageCommandHandler.cs`

### API Контролери:
- `UniversalImageController.cs` - Універсальний контролер зображень
- `UniversalMetaImageController.cs` - Контролер мета-зображень
- `AdminProductController.cs` - Методи для зображень продуктів (адмін)
- `AdminCategoryController.cs` - Методи для зображень категорій (адмін)
- `AdminCompanyController.cs` - Методи для зображень компаній (адмін)
- `SellerProductController.cs` - Методи для зображень продуктів (продавець)

### Валідатори:
- `ProductImageValidator.cs` - Валідатор зображень продуктів

### Конфігурація:
- `appsettings.json` - Налаштування FileService
- `InfrastructureServiceExtensions.cs` - Реєстрація сервісів файлів
- `Routes.md` - Документація API endpoints

### Статичні файли:
- `wwwroot/uploads/` - Папка для завантажених зображень

## 🎨 Фронтенд файли

### Сервіси:
- `image.service.js` - Основний сервіс для роботи з зображеннями

### Універсальні компоненти управління зображеннями (Адмін):
- `EntityImageManager.vue` - Універсальний менеджер зображень
- `EntityMetaImageManager.vue` - Менеджер мета-зображень
- `UploadProgress.vue` - Компонент прогресу завантаження
- `README.md` - Документація компонентів зображень

### Компоненти зображень продуктів (Адмін):
- `AdminProductImagesViewer.vue` - Перегляд зображень продуктів
- `ProductImagesManager.vue` - Менеджер зображень продуктів
- `ProductImageManager.vue` - Менеджер зображень продукту
- `MetaImageManager.vue` - Менеджер мета-зображень продуктів
- `ProductImageUploader.vue` - Завантажувач зображень продуктів
- `SimpleMetaImageUploader.vue` - Простий завантажувач мета-зображень
- `SimpleImageUploader.vue` - Простий завантажувач зображень

### Компоненти зображень категорій (Адмін):
- `CategoryImageManager.vue` - Менеджер зображень категорій
- `MetaImageManager.vue` - Менеджер мета-зображень категорій
- `CategoryImageUploader.vue` - Завантажувач зображень категорій

### Компоненти зображень компаній (Адмін):
- `CompanyImageUpload.vue` - Завантаження зображень компаній
- `CompanyImageManager.vue` - Менеджер зображень компаній

### Публічні компоненти відображення зображень:
- `ProductGrid.vue` - Сітка продуктів з зображеннями
- `TopProductsGrid.vue` - Сітка топ продуктів
- `HomeCategories.vue` - Категорії на головній сторінці
- `ProductPage.vue` - Сторінка продукту з галереєю зображень
- `HomePage.vue` - Головна сторінка з банером
- `CatalogPage.vue` - Каталог з зображеннями продуктів

### Адмін списки з зображеннями:
- `ProductList.vue` - Список продуктів з превью зображень
- `CategoryList.vue` - Список категорій з зображеннями
- `UserEdit.vue` - Редагування користувача з аватаром
- `CompanyEdit.vue` - Редагування компанії з логотипом
- `CompanyEditNew.vue` - Нове редагування компанії

### Статичні зображення та ресурси:
- `assets/images/` - Статичні зображення
  - `spring-banner.jpg` - Банер весни
  - `placeholder-product.svg` - Заглушка для продуктів
  - `icons/` - Іконки (placeholder, social, auth)
- `public/` - Публічні зображення
  - `placeholder-product.svg` - Публічна заглушка продукту
  - `logo.svg` - Логотип сайту

### Документація:
- `BUGFIX-REPORT.md` - Звіт про виправлення з інформацією про зображення

## 🔗 API Endpoints для зображень

### Універсальні:
- `POST /api/universal/images/{entityType}/{entityId}` - Завантаження зображення
- `PUT /api/universal/images/{entityType}/{entityId}` - Оновлення зображення
- `DELETE /api/universal/images/{entityType}/{entityId}` - Видалення зображення
- `GET /api/universal/images/{entityType}/{entityId}` - Отримання зображень
- `POST /api/universal/meta-images/{entityType}/{entityId}` - Мета-зображення

### Продукти:
- `POST /api/admin/products/{id}/images` - Завантаження зображень продукту (адмін)
- `POST /api/sellers/me/products/{id}/images` - Завантаження зображень продукту (продавець)
- `DELETE /api/admin/products/{id}/images/{imageId}` - Видалення зображення продукту

### Категорії:
- `POST /api/admin/categories/{id}/image` - Завантаження зображення категорії
- `POST /api/admin/categories/{id}/meta-image` - Мета-зображення категорії

### Компанії:
- `POST /api/admin/companies/{id}/images` - Завантаження зображення компанії

### Статичні файли:
- `GET /uploads/**` - Доступ до завантажених файлів

## 📝 Примітки

1. Всі файли скопійовані зі збереженням оригінальної структури проекту
2. Backup створено: 14.07.2025
3. Включені тільки файли, безпосередньо пов'язані з операціями над зображеннями
4. Збережено як бекенд, так і фронтенд компоненти
5. Включено конфігураційні файли та документацію

Цей backup можна використовувати для:
- Аналізу архітектури роботи з зображеннями
- Перенесення функціональності в інші проекти
- Відновлення функціональності у разі втрати
- Навчання та документування
