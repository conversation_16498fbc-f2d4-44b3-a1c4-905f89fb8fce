﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class UpdateProductImageCommandHandler : IRequestHandler<UpdateProductImageCommand, Unit>
{
    private readonly IProductImageRepository _repository;
    private readonly IMapper _mapper;

    public UpdateProductImageCommandHandler(IProductImageRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Unit> Handle(UpdateProductImageCommand command, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(command.Id, cancellationToken);
        if (item == null)
            throw new InvalidOperationException($"ProductImage with ID {command.Id} not found.");

        item.Update(
            image: command.Image != null ? new Url(command.Image) : null
        );


        await _repository.UpdateAsync(item, cancellationToken);
        return Unit.Value;
    }
}

