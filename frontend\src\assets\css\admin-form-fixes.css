/* Admin Form Fixes
   This file contains fixes for form field visibility and contrast issues in the admin panel.
*/

/* ===== Form Field Text Visibility Fixes ===== */

/* Improve input text visibility */
.input, 
.select select, 
.textarea {
  color: #222 !important; /* Ensure dark text for better visibility */
  font-weight: 500 !important; /* Medium weight for better readability */
  background-color: #fff !important; /* Light background for better contrast */
  border: 1px solid #ccc !important; /* Visible border */
}

/* Dark mode / dark container overrides */
.dark-mode .input,
.dark-mode .select select,
.dark-mode .textarea,
.dark-container .input,
.dark-container .select select,
.dark-container .textarea,
.card-dark .input,
.card-dark .select select,
.card-dark .textarea {
  color: #f3f4f6 !important; /* Light text for dark backgrounds */
  background-color: #1e293b !important; /* Dark background */
  border-color: #374151 !important; /* Darker border for contrast */
}

/* Improve placeholder text visibility */
.input::placeholder,
.textarea::placeholder,
.select select::placeholder {
  color: #666 !important; /* Darker placeholder text */
  opacity: 1 !important; /* Ensure full opacity */
}

/* Dark mode placeholder text */
.dark-mode .input::placeholder,
.dark-mode .textarea::placeholder,
.dark-mode .select select::placeholder,
.dark-container .input::placeholder,
.dark-container .textarea::placeholder,
.dark-container .select select::placeholder {
  color: #9ca3af !important; /* Lighter placeholder text for dark backgrounds */
  opacity: 1 !important;
}

/* Improve select dropdown text visibility */
.select select option {
  color: #222 !important; /* Dark text for options */
  background-color: #fff !important; /* Light background */
}

/* Dark mode select options */
.dark-mode .select select option,
.dark-container .select select option {
  color: #f3f4f6 !important; /* Light text for dark backgrounds */
  background-color: #1e293b !important; /* Dark background */
}

/* Ensure form labels are visible */
.label {
  color: #333 !important; /* Dark text for labels */
  font-weight: 600 !important; /* Semi-bold for better visibility */
  margin-bottom: 0.5rem !important; /* Consistent spacing */
}

/* Dark mode labels */
.dark-mode .label,
.dark-container .label,
.card-dark .label {
  color: #f3f4f6 !important; /* Light text for dark backgrounds */
}

/* Improve form field focus states */
.input:focus,
.select select:focus,
.textarea:focus {
  border-color: #3b82f6 !important; /* Bright blue border */
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25) !important; /* Subtle glow */
  outline: none !important;
}

/* Ensure disabled fields have proper styling */
.input:disabled,
.select select:disabled,
.textarea:disabled {
  background-color: #f3f4f6 !important; /* Light gray background */
  color: #6b7280 !important; /* Medium gray text */
  cursor: not-allowed !important;
}

/* Dark mode disabled fields */
.dark-mode .input:disabled,
.dark-mode .select select:disabled,
.dark-mode .textarea:disabled,
.dark-container .input:disabled,
.dark-container .select select:disabled,
.dark-container .textarea:disabled {
  background-color: #374151 !important; /* Darker background */
  color: #9ca3af !important; /* Lighter text */
}

/* Ensure validation error messages are visible */
.help.is-danger {
  color: #ef4444 !important; /* Bright red for errors */
  font-size: 0.75rem !important;
  margin-top: 0.25rem !important;
  font-weight: 500 !important;
}

/* Ensure modal form fields have proper styling */
.modal-card .input,
.modal-card .select select,
.modal-card .textarea {
  color: #222 !important;
  background-color: #fff !important;
}

/* Fix modal form field visibility in dark mode */
.dark-mode .modal-card .input,
.dark-mode .modal-card .select select,
.dark-mode .modal-card .textarea {
  color: #f3f4f6 !important;
  background-color: #1e293b !important;
}

/* Ensure form field text is visible in all states */
.input.is-active,
.input.is-focused,
.input:active,
.input:focus,
.select select.is-active,
.select select.is-focused,
.select select:active,
.select select:focus,
.textarea.is-active,
.textarea.is-focused,
.textarea:active,
.textarea:focus {
  color: #222 !important;
  border-color: #3b82f6 !important;
}

/* Dark mode active/focus states */
.dark-mode .input.is-active,
.dark-mode .input.is-focused,
.dark-mode .input:active,
.dark-mode .input:focus,
.dark-mode .select select.is-active,
.dark-mode .select select.is-focused,
.dark-mode .select select:active,
.dark-mode .select select:focus,
.dark-mode .textarea.is-active,
.dark-mode .textarea.is-focused,
.dark-mode .textarea:active,
.dark-mode .textarea:focus {
  color: #f3f4f6 !important;
}
