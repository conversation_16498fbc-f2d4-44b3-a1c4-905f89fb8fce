<template>
  <div class="admin-card admin-recent-orders">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-shopping-cart"></i>
        Recent Orders
      </h3>
      <router-link to="/admin/orders" class="admin-btn admin-btn-primary admin-btn-sm">
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </router-link>
    </div>
    <div class="admin-card-content">
      <div v-if="loading" class="admin-loading-state">
        <div class="admin-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-loading-text">Loading orders...</p>
      </div>
      <div v-else-if="!orders || orders.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-shopping-cart"></i>
        </div>
        <p class="admin-empty-text">No recent orders found</p>
      </div>
      <div v-else class="admin-table-container">
        <table class="admin-table">
          <thead>
            <tr>
              <th>Order ID</th>
              <th>Customer</th>
              <th>Total</th>
              <th>Status</th>
              <th>Date</th>
              <th class="admin-table-actions">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in orders" :key="order.id">
              <td class="admin-table-id">#{{ order.id }}</td>
              <td class="admin-table-customer">{{ order.customerName }}</td>
              <td class="admin-table-amount">{{ formatCurrency(order.total) }}</td>
              <td>
                <status-badge :status="order.status" type="order" />
              </td>
              <td class="admin-table-date">{{ formatDate(order.createdAt) }}</td>
              <td class="admin-table-actions">
                <router-link :to="`/admin/orders/${order.id}`" class="admin-btn admin-btn-ghost admin-btn-sm">
                  <i class="fas fa-eye"></i>
                </router-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);

  // If less than a minute ago
  if (diffSec < 60) {
    return 'Just now';
  }

  // If less than an hour ago
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }

  // If less than a day ago
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }

  // If less than a week ago
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }

  // Otherwise, return formatted date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};
</script>

<style scoped>
.admin-recent-orders {
  height: 100%;
}

.admin-table-container {
  overflow-x: auto;
  margin: calc(-1 * var(--admin-space-md));
  padding: var(--admin-space-md);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--admin-text-sm);
}

.admin-table th {
  background: var(--admin-gray-50);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-semibold);
  padding: var(--admin-space-md);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-color);
  font-size: var(--admin-text-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-table td {
  padding: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: middle;
}

.admin-table tbody tr:hover {
  background: var(--admin-gray-25);
}

.admin-table-id {
  font-weight: var(--admin-font-medium);
  color: var(--admin-primary);
  font-family: var(--admin-font-mono);
}

.admin-table-customer {
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
}

.admin-table-amount {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  font-family: var(--admin-font-mono);
}

.admin-table-date {
  color: var(--admin-gray-600);
  font-size: var(--admin-text-xs);
}

.admin-table-actions {
  text-align: right;
  width: 80px;
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-600);
}

.admin-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-sm);
  margin: 0;
}

.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-lg);
}

.admin-empty-text {
  font-size: var(--admin-text-base);
  margin: 0;
  text-align: center;
}

@media (max-width: 768px) {
  .admin-table {
    font-size: var(--admin-text-xs);
  }

  .admin-table th,
  .admin-table td {
    padding: var(--admin-space-sm);
  }

  .admin-table-customer,
  .admin-table-date {
    display: none;
  }
}
</style>
