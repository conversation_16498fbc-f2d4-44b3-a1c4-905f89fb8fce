<template>
  <div class="login-container">
    <div class="login-form">
      <div class="logo-container">
        <img src="/logo.svg" alt="Klondike" class="logo" />
      </div>

      <h2 class="login-title">Увійти в акаунт</h2>

      <div v-if="message" class="alert" :class="successful ? 'alert-success' : 'alert-danger'">
        {{ message }}
      </div>

      <Form @submit="handleLogin" :validation-schema="schema">
        <div class="form-group">
          <label for="username" class="form-label">Електронна пошта</label>
          <div class="input-wrapper" :class="{ 'has-error': errors.username, 'is-valid': isFieldValid('username') }">
            <Field name="username" type="text" class="form-input" id="username" placeholder="Введіть електронну пошту" />
            <div class="validation-icon">
              <i class="fas fa-exclamation-circle error-icon" v-if="errors.username"></i>
              <i class="fas fa-check-circle valid-icon" v-if="isFieldValid('username')"></i>
            </div>
          </div>
          <ErrorMessage name="username" class="error-feedback" />
          <div class="field-hint" v-if="!errors.username && !isFieldValid('username')">
            Введіть вашу електронну пошту
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="form-label">Пароль</label>
          <div class="input-wrapper" :class="{ 'has-error': errors.password, 'is-valid': isFieldValid('password') }">
            <Field name="password" type="password" class="form-input" id="password" placeholder="Введіть пароль" />
            <div class="validation-icon">
              <i class="fas fa-exclamation-circle error-icon" v-if="errors.password"></i>
              <i class="fas fa-check-circle valid-icon" v-if="isFieldValid('password')"></i>
            </div>
            <button type="button" class="toggle-password" @click="togglePasswordVisibility">
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <ErrorMessage name="password" class="error-feedback" />
          <div class="field-hint" v-if="!errors.password && !isFieldValid('password')">
            Пароль має містити не менше 8 символів
          </div>
        </div>

        <div class="forgot-password">
          <a href="#">Забули пароль?</a>
        </div>

        <button class="login-button" type="submit" :disabled="loading">
          <span v-if="loading" class="spinner"></span>
          Увійти
        </button>
      </Form>

      <div class="divider">
        <span>або</span>
      </div>

      <!-- Custom Google Sign-In button styled like Apple -->
      <button class="social-button google-button" @click="handleCustomGoogleLogin" :disabled="loading">
        <img src="@/assets/images/icons/google-icon.svg" alt="Google" class="social-icon" />
        {{ loading ? 'Завантаження...' : 'Google' }}
      </button>

      <!-- Apple login is not implemented yet -->
      <button class="social-button apple-button" disabled title="Apple login is not available yet">
        <img src="@/assets/images/icons/apple-icon.svg" alt="Apple" class="social-icon" />
        Apple
      </button>

      <!-- Hidden Google Sign-In button (rendered by Google API) -->
      <div id="google-signin-direct" style="display: none;"></div>

      <div class="register-link">
        <span>Немає облікового запису?</span>
        <router-link to="/register">Зареєструйтесь зараз</router-link>
      </div>
    </div>

    <div class="footer">
      <div class="footer-links">
        <a href="#">Про компанію</a>
        <a href="#">Умови використання</a>
        <a href="#">Допомога</a>
      </div>
      <div class="copyright">
        Всі права захищені
      </div>
    </div>
  </div>
</template>

<script>
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { useField } from 'vee-validate';
import { initializeGoogleSignIn } from '@/config/google-auth';

export default {
  name: 'Login',
  components: {
    Form,
    Field,
    ErrorMessage
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    const loading = ref(false);
    const message = ref('');
    const successful = ref(false);
    const showPassword = ref(false);

    const { errorMessage: usernameError, value: usernameValue } = useField('username');
    const { errorMessage: passwordError, value: passwordValue } = useField('password');

    const errors = computed(() => ({
      username: !!usernameError.value,
      password: !!passwordError.value
    }));

    // Check if a field is valid (has a value and no error)
    const isFieldValid = (fieldName) => {
      if (fieldName === 'username') {
        return usernameValue.value && !usernameError.value;
      } else if (fieldName === 'password') {
        return passwordValue.value && !passwordError.value;
      }
      return false;
    };

    // Toggle password visibility
    const togglePasswordVisibility = () => {
      showPassword.value = !showPassword.value;

      // Change input type
      const passwordInput = document.getElementById('password');
      if (passwordInput) {
        passwordInput.type = showPassword.value ? 'text' : 'password';
      }
    };

    const schema = yup.object().shape({
      username: yup.string()
        .required('Введіть електронну пошту')
        .email('Введіть дійсну електронну пошту'),
      password: yup.string()
        .required('Введіть пароль')
        .min(8, 'Пароль має містити не менше 8 символів')
        // Note: Backend requires at least one digit and one uppercase letter,
        // but we're not enforcing this on login to allow users to enter their password
    });

    const handleLogin = async (user) => {
      loading.value = true;
      message.value = '';
      successful.value = false;

      try {
        // Validate input before sending to server
        if (!user.username || !user.password) {
          message.value = 'Введіть електронну пошту та пароль';
          return;
        }

        // Attempt login - this will throw an error if authentication fails
        // The auth service and store already validate the response
        await store.dispatch('auth/login', user);

        // Log the authentication state
        console.log('Login dispatch completed successfully');

        successful.value = true;

        // Log authentication state for debugging
        console.log('Login successful');
        console.log('Is admin after login:', store.getters['auth/isAdmin']);

        // Verify user is logged in according to store
        if (!store.getters['auth/isLoggedIn']) {
          console.error('Store indicates user is not logged in after successful login');
          message.value = 'Authentication error. Please try again.';
          return;
        }

        // Get user data for direct role check
        const userData = store.getters['auth/user'];
        console.log('User data for redirection:', userData);

        // Check role directly for debugging
        const userRole = userData?.role;
        console.log('User role for redirection:', userRole);

        // Determine if admin/moderator based on direct role check and store getter
        const isAdminByRole = userRole === 'Admin';
        const isModeratorByRole = userRole === 'Moderator';
        const isAdminByGetter = store.getters['auth/isAdmin'];
        const isModeratorByGetter = store.getters['auth/isModerator'];

        console.log('Is admin by direct role check:', isAdminByRole);
        console.log('Is moderator by direct role check:', isModeratorByRole);
        console.log('Is admin by getter:', isAdminByGetter);
        console.log('Is moderator by getter:', isModeratorByGetter);

        // Redirect to the requested page or dashboard
        let redirectPath;

        if (route.query.redirect) {
          // Use the requested redirect if available
          redirectPath = route.query.redirect;
        } else if (isAdminByRole || isModeratorByRole || isAdminByGetter || isModeratorByGetter) {
          // Force admin dashboard if either check indicates admin or moderator
          redirectPath = '/admin/dashboard';
        } else {
          // Default to regular dashboard
          redirectPath = '/dashboard';
        }

        console.log('Redirecting to:', redirectPath);
        router.push(redirectPath);
      } catch (error) {
        successful.value = false;
        console.error('Login error:', error);

        // Extract error message from response if available
        if (error.response && error.response.data) {
          console.log('Error response data:', error.response.data);

          // Check for specific error codes
          if (error.response.data.code === 'invalid_credentials') {
            message.value = 'Невірний логін або пароль. Перевірте правильність введених даних.';
          } else if (error.response.data.code === 'user_not_found') {
            message.value = 'Користувача з такою електронною поштою не знайдено.';
          } else if (error.response.data.code === 'invalid_password') {
            message.value = 'Невірний пароль. Перевірте правильність введених даних.';
          } else {
            message.value = error.response.data.message || 'Помилка аутентифікації';
          }

          // Log additional details for debugging
          if (error.response.status === 401) {
            console.log('Authentication failed with 401 status');
          }
        } else if (error.message.includes('network')) {
          message.value = 'Помилка мережі. Перевірте підключення до Інтернету.';
        } else {
          message.value = error.message || 'Невірний логін або пароль';
        }

        // Clear any potentially corrupted auth data
        store.dispatch('auth/logout');
      } finally {
        loading.value = false;
      }
    };

    // Google Sign-In initialization

    onMounted(async () => {
      try {
        console.log('Initializing Google Sign-In...');

        // Initialize Google Sign-In with our callback
        const googleAccounts = await initializeGoogleSignIn(handleGoogleCredentialResponse);

        console.log('Google Sign-In initialized successfully');

        // Render the hidden Google Sign-In button
        const directButtonElement = document.getElementById('google-signin-direct');
        if (directButtonElement) {
          googleAccounts.id.renderButton(
            directButtonElement,
            {
              theme: 'outline',
              size: 'large',
              width: '100%',
              type: 'standard',
              text: 'signin_with'
            }
          );
          console.log('Hidden Google Sign-In button rendered successfully');
        }
      } catch (error) {
        console.error('Error initializing Google Sign-In:', error);
        message.value = 'Помилка ініціалізації Google Sign-In. Спробуйте пізніше.';
      }
    });

    // Handle custom Google button click
    const handleCustomGoogleLogin = () => {
      if (loading.value) {
        return; // Prevent multiple clicks
      }

      console.log('Custom Google button clicked');
      loading.value = true;
      message.value = '';

      // Trigger the hidden Google button
      const googleButton = document.querySelector('#google-signin-direct div[role="button"]');
      if (googleButton) {
        googleButton.click();

        // Reset loading state after a timeout if no response
        setTimeout(() => {
          if (loading.value) {
            loading.value = false;
          }
        }, 10000);
      } else {
        console.error('Google button not found');
        message.value = 'Помилка ініціалізації Google Sign-In. Спробуйте пізніше.';
        loading.value = false;
      }
    };

    // Handle Google Sign-In response
    const handleGoogleCredentialResponse = async (response) => {
      console.log('Google Sign-In callback received');

      // Set a timeout to ensure loading state is reset
      const safetyTimeoutId = setTimeout(() => {
        if (loading.value) {
          loading.value = false;
          message.value = 'Час очікування відповіді від сервера вичерпано. Спробуйте пізніше.';
        }
      }, 15000);

      try {
        loading.value = true;
        message.value = '';
        successful.value = false;

        // Validate response
        if (!response) {
          throw new Error('Empty response from Google Sign-In');
        }

        console.log('Response type:', typeof response);
        console.log('Response has credential:', !!response.credential);

        // Get the ID token from the response
        const idToken = response.credential;
        if (!idToken) {
          throw new Error('No ID token received from Google');
        }

        console.log('ID token received, length:', idToken.length);
        console.log('Dispatching auth/googleLogin action...');

        try {
          // Dispatch the Google login action
          await store.dispatch('auth/googleLogin', idToken);

          console.log('Google login action completed successfully');
          successful.value = true;

          // Clear the safety timeout as we're done
          clearTimeout(safetyTimeoutId);

          // Determine redirect path
          let redirectPath;
          if (route.query.redirect) {
            redirectPath = route.query.redirect;
          } else if (store.getters['auth/isAdminOrModerator']) {
            redirectPath = '/admin/dashboard';
          } else {
            redirectPath = '/dashboard';
          }

          console.log('Google login successful, redirecting to:', redirectPath);
          router.push(redirectPath);
        } catch (loginError) {
          // Clear the safety timeout as we're handling the error
          clearTimeout(safetyTimeoutId);

          successful.value = false;
          console.error('Google login error:', loginError);

          // Provide user-friendly error messages
          if (loginError.response && loginError.response.data) {
            console.error('Error response data:', loginError.response.data);

            // Handle specific error codes
            const errorData = loginError.response.data;
            if (errorData.code === 'user_not_found') {
              message.value = 'Користувача з цим Google акаунтом не знайдено.';
            } else if (errorData.code === 'invalid_token') {
              message.value = 'Недійсний токен аутентифікації. Спробуйте ще раз.';
            } else if (errorData.code === 'account_disabled') {
              message.value = 'Цей обліковий запис відключено. Зверніться до адміністратора.';
            } else {
              message.value = errorData.message || 'Помилка аутентифікації Google';
            }
          } else if (loginError.message.includes('network')) {
            message.value = 'Помилка мережі. Перевірте підключення до Інтернету.';
          } else {
            message.value = loginError.message || 'Помилка аутентифікації Google';
          }

          // Clear any potentially corrupted auth data
          store.dispatch('auth/logout');
        }
      } catch (error) {
        // Clear the safety timeout as we're handling the error
        clearTimeout(safetyTimeoutId);

        successful.value = false;
        console.error('Unexpected error during Google authentication:', error);
        message.value = 'Несподівана помилка під час аутентифікації Google. Спробуйте пізніше.';

        // Clear any potentially corrupted auth data
        store.dispatch('auth/logout');
      } finally {
        // Ensure loading state is reset
        loading.value = false;
      }
    };
    return {
      loading,
      message,
      successful,
      schema,
      handleLogin,
      errors,
      isFieldValid,
      togglePasswordVisibility,
      showPassword,
      handleCustomGoogleLogin
    };
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  align-items: center;
  justify-content: center;
}

.login-form {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.logo-container {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  height: 50px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 25px;
  color: #333;
}

.form-group {
  position: relative;
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.3s;
}

.input-wrapper.has-error {
  border-color: #F27318E3;
}

.input-wrapper.is-valid {
  border-color: #2ecc71;
}

.form-input {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  background: transparent;
}

.form-input:focus {
  outline: none;
}

.validation-icon {
  position: absolute;
  right: 15px;
  display: flex;
  align-items: center;
}

.error-icon {
  color: #F27318E3;
}

.valid-icon {
  color: #2ecc71;
}

.toggle-password {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 0;
  font-size: 16px;
}

.validation-icon + .toggle-password {
  right: 40px;
}

.error-feedback {
  color: #F27318E3;
  font-size: 12px;
  margin-top: 5px;
}

.field-hint {
  color: #777;
  font-size: 12px;
  margin-top: 5px;
}

.forgot-password {
  text-align: right;
  margin-bottom: 20px;
}

.forgot-password a {
  color: #4285f4;
  font-size: 14px;
  text-decoration: none;
}

.login-button {
  width: 100%;
  padding: 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-button:hover {
  background-color: #3367d6;
}

.login-button:disabled {
  background-color: #a4c2f4;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 20px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.divider span {
  padding: 0 10px;
  color: #777;
  font-size: 14px;
}

.social-button {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}

.social-button:hover {
  background-color: #f5f5f5;
}

.social-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.social-button:disabled:hover {
  background-color: white;
}

.google-button {
  background-color: white;
  border: 1px solid #dadce0;
  color: #3c4043;
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.1);
}

.google-button:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #dadce0;
  box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.15);
}

.apple-button {
  border: 1px solid #dadce0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.apple-button:hover:not(:disabled) {
  background-color: #333;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15);
}

.social-icon {
  height: 18px;
  margin-right: 10px;
}

/* Hide the default Google Sign-In button */
#google-signin-button {
  display: none;
}

.register-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #555;
}

.register-link a {
  color: #4285f4;
  text-decoration: none;
  margin-left: 5px;
}

.footer {
  width: 100%;
  text-align: center;
  padding: 20px 0;
}

.footer-links {
  margin-bottom: 10px;
}

.footer-links a {
  color: #555;
  text-decoration: none;
  margin: 0 10px;
  font-size: 14px;
}

.copyright {
  color: #777;
  font-size: 12px;
}

.alert {
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 14px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
</style>
