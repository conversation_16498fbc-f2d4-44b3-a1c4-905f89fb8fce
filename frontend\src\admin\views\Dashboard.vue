<template>
  <div class="admin-dashboard">
    <!-- Header Section -->
    <header class="admin-dashboard__header">
      <div class="admin-dashboard__header-content">
        <div class="admin-dashboard__title-section">
          <h1 class="admin-dashboard__title">
            <i class="admin-dashboard__title-icon fas fa-tachometer-alt"></i>
            Dashboard
          </h1>
          <p class="admin-dashboard__subtitle">
            Real-time overview of your marketplace performance
          </p>
        </div>
        <div class="admin-dashboard__actions">
          <button
            class="admin-dashboard__refresh-btn"
            @click="fetchDashboardData"
            :disabled="loading"
            :class="{ 'admin-dashboard__refresh-btn--loading': loading }"
          >
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            <span>{{ loading ? 'Refreshing...' : 'Refresh' }}</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Error Alert -->
    <div class="admin-dashboard__alert admin-dashboard__alert--error" v-if="error">
      <div class="admin-dashboard__alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-dashboard__alert-content">
        <h4 class="admin-dashboard__alert-title">Error Loading Data</h4>
        <p class="admin-dashboard__alert-message">{{ error }}</p>
      </div>
      <button class="admin-dashboard__alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Loading State -->
    <div class="admin-dashboard__loading" v-if="loading && !stats.products">
      <div class="admin-dashboard__loading-content">
        <div class="admin-dashboard__loading-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <h3 class="admin-dashboard__loading-title">Loading Dashboard</h3>
        <p class="admin-dashboard__loading-text">
          Fetching the latest data from your marketplace...
        </p>
        <div class="admin-dashboard__loading-progress">
          <div class="admin-dashboard__loading-bar"></div>
        </div>
        <div v-if="loadingTime > 10" class="admin-dashboard__loading-retry">
          <button class="admin-dashboard__retry-btn" @click="cancelAndRetry">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Taking too long? Click to retry</span>
          </button>
        </div>
      </div>
    </div>

    <!-- No Data State -->
    <div class="admin-dashboard__empty" v-else-if="noData">
      <div class="admin-dashboard__empty-content">
        <div class="admin-dashboard__empty-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <h3 class="admin-dashboard__empty-title">No Data Available</h3>
        <p class="admin-dashboard__empty-text">
          Your dashboard is ready, but we haven't collected any data yet.
        </p>
        <div class="admin-dashboard__empty-reasons">
          <h4>This might be because:</h4>
          <ul>
            <li>Your marketplace is new and hasn't received orders yet</li>
            <li>There's a temporary connection issue</li>
            <li>Data is still being processed</li>
          </ul>
        </div>
        <button class="admin-dashboard__empty-retry" @click="fetchDashboardData">
          <i class="fas fa-sync-alt"></i>
          <span>Try Again</span>
        </button>
      </div>
    </div>

    <!-- Main Dashboard Content -->
    <main class="admin-dashboard__main" v-else>
      <!-- KPI Metrics Section -->
      <section class="admin-dashboard__kpi-section">
        <div class="admin-dashboard__section-header">
          <h2 class="admin-dashboard__section-title">
            <i class="admin-dashboard__section-icon fas fa-chart-bar"></i>
            Key Performance Indicators
          </h2>
          <p class="admin-dashboard__section-subtitle">
            Real-time business metrics and performance overview
          </p>
        </div>

        <div class="admin-dashboard__kpi-grid">
          <!-- Users KPI Card -->
          <div class="admin-dashboard__kpi-card admin-dashboard__kpi-card--users">
            <div class="admin-dashboard__kpi-header">
              <div class="admin-dashboard__kpi-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="admin-dashboard__kpi-trend" :class="`admin-dashboard__kpi-trend--${getUsersTrend()}`">
                <i :class="getUsersTrendIcon()"></i>
                <span>{{ getUsersTrendPercentage() }}%</span>
              </div>
            </div>
            <div class="admin-dashboard__kpi-content">
              <h3 class="admin-dashboard__kpi-label">Total Users</h3>
              <div class="admin-dashboard__kpi-value">{{ formatNumber(stats.users) }}</div>
              <p class="admin-dashboard__kpi-description">Registered customers and sellers</p>
            </div>
          </div>

          <!-- Orders KPI Card -->
          <div class="admin-dashboard__kpi-card admin-dashboard__kpi-card--orders">
            <div class="admin-dashboard__kpi-header">
              <div class="admin-dashboard__kpi-icon">
                <i class="fas fa-shopping-cart"></i>
              </div>
              <div class="admin-dashboard__kpi-trend" :class="`admin-dashboard__kpi-trend--${getOrdersTrend()}`">
                <i :class="getOrdersTrendIcon()"></i>
                <span>{{ getOrdersTrendPercentage() }}%</span>
              </div>
            </div>
            <div class="admin-dashboard__kpi-content">
              <h3 class="admin-dashboard__kpi-label">Total Orders</h3>
              <div class="admin-dashboard__kpi-value">{{ formatNumber(stats.orders) }}</div>
              <p class="admin-dashboard__kpi-description">All-time order count</p>
            </div>
          </div>

          <!-- Products KPI Card -->
          <div class="admin-dashboard__kpi-card admin-dashboard__kpi-card--products">
            <div class="admin-dashboard__kpi-header">
              <div class="admin-dashboard__kpi-icon">
                <i class="fas fa-box"></i>
              </div>
              <div class="admin-dashboard__kpi-trend" :class="`admin-dashboard__kpi-trend--${getProductsTrend()}`">
                <i :class="getProductsTrendIcon()"></i>
                <span>{{ getProductsTrendPercentage() }}%</span>
              </div>
            </div>
            <div class="admin-dashboard__kpi-content">
              <h3 class="admin-dashboard__kpi-label">Total Products</h3>
              <div class="admin-dashboard__kpi-value">{{ formatNumber(stats.products) }}</div>
              <p class="admin-dashboard__kpi-description">Active marketplace listings</p>
            </div>
          </div>

          <!-- Revenue KPI Card -->
          <div class="admin-dashboard__kpi-card admin-dashboard__kpi-card--revenue">
            <div class="admin-dashboard__kpi-header">
              <div class="admin-dashboard__kpi-icon">
                <i class="fas fa-hryvnia-sign"></i>
              </div>
              <div class="admin-dashboard__kpi-trend" :class="`admin-dashboard__kpi-trend--${getRevenueTrend()}`">
                <i :class="getRevenueTrendIcon()"></i>
                <span>{{ getRevenueTrendPercentage() }}%</span>
              </div>
            </div>
            <div class="admin-dashboard__kpi-content">
              <h3 class="admin-dashboard__kpi-label">Total Revenue</h3>
              <div class="admin-dashboard__kpi-value">{{ formatCurrency(stats.revenue) }}</div>
              <p class="admin-dashboard__kpi-description">Gross marketplace revenue</p>
            </div>
          </div>

          <!-- Categories KPI Card -->
          <div class="admin-dashboard__kpi-card admin-dashboard__kpi-card--categories">
            <div class="admin-dashboard__kpi-header">
              <div class="admin-dashboard__kpi-icon">
                <i class="fas fa-folder"></i>
              </div>
              <!-- Categories не показують відсоткове зростання -->
              <div class="admin-dashboard__kpi-trend admin-dashboard__kpi-trend--neutral" style="visibility: hidden;">
                <i class="fas fa-minus"></i>
                <span>-</span>
              </div>
            </div>
            <div class="admin-dashboard__kpi-content">
              <h3 class="admin-dashboard__kpi-label">Categories</h3>
              <div class="admin-dashboard__kpi-value">{{ formatNumber(stats.categories || 0) }}</div>
              <p class="admin-dashboard__kpi-description">Product categories</p>
            </div>
          </div>

          <!-- Site Profit KPI Card -->
          <div class="admin-dashboard__kpi-card admin-dashboard__kpi-card--profit">
            <div class="admin-dashboard__kpi-header">
              <div class="admin-dashboard__kpi-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="admin-dashboard__kpi-trend" :class="`admin-dashboard__kpi-trend--${getProfitTrend()}`">
                <i :class="getProfitTrendIcon()"></i>
                <span>{{ getProfitTrendPercentage() }}%</span>
              </div>
            </div>
            <div class="admin-dashboard__kpi-content">
              <h3 class="admin-dashboard__kpi-label">Site Profit (15%)</h3>
              <div class="admin-dashboard__kpi-value">{{ formatCurrency(siteProfit) }}</div>
              <p class="admin-dashboard__kpi-description">Commission from sales</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Charts Analytics Section -->
      <section class="admin-dashboard__charts-section">
        <div class="admin-dashboard__section-header">
          <h2 class="admin-dashboard__section-title">
            <i class="admin-dashboard__section-icon fas fa-chart-line"></i>
            Revenue Analytics
          </h2>
          <p class="admin-dashboard__section-subtitle">
            Comprehensive revenue and sales performance analysis
          </p>
        </div>

        <div class="admin-dashboard__charts-grid">
          <!-- Main Revenue Chart -->
          <div class="admin-dashboard__chart-card admin-dashboard__chart-card--main">
            <div class="admin-dashboard__chart-header">
              <h3 class="admin-dashboard__chart-title">
                <i class="admin-dashboard__chart-icon fas fa-chart-area"></i>
                Sales Revenue Trend
              </h3>
              <div class="admin-dashboard__chart-controls">
                <select
                  v-model="selectedPeriod"
                  @change="handlePeriodChange"
                  class="admin-dashboard__period-select"
                >
                  <option value="week">Last 7 Days</option>
                  <option value="month">Last 30 Days</option>
                  <option value="quarter">Last 3 Months</option>
                  <option value="year">Last Year</option>
                </select>
              </div>
            </div>
            <div class="admin-dashboard__chart-content">
              <Suspense>
                <template #default>
                  <sales-chart
                    :data="salesData"
                    :period="selectedPeriod"
                  />
                </template>
                <template #fallback>
                  <div class="admin-dashboard__chart-loading">
                    <i class="fas fa-spinner fa-pulse"></i>
                    <span>Loading chart...</span>
                  </div>
                </template>
              </Suspense>
            </div>
            <!-- Chart footer hidden to avoid duplication with chart data -->
            <!-- <div class="admin-dashboard__chart-footer">
              <div class="admin-dashboard__chart-stats">
                <div class="admin-dashboard__stat-item">
                  <span class="admin-dashboard__stat-label">Total Revenue</span>
                  <span class="admin-dashboard__stat-value">{{ formatCurrency(totalRevenue) }}</span>
                </div>
                <div class="admin-dashboard__stat-item">
                  <span class="admin-dashboard__stat-label">Average Order</span>
                  <span class="admin-dashboard__stat-value">{{ formatCurrency(averageOrderValue) }}</span>
                </div>
                <div class="admin-dashboard__stat-item">
                  <span class="admin-dashboard__stat-label">Growth Rate</span>
                  <span class="admin-dashboard__stat-value" :class="`admin-dashboard__stat-value--${getGrowthClass()}`">
                    {{ getGrowthRate() }}%
                  </span>
                </div>
              </div>
            </div> -->
          </div>

          <!-- Orders Status Chart -->
          <div class="admin-dashboard__chart-card admin-dashboard__chart-card--side">
            <div class="admin-dashboard__chart-header">
              <h3 class="admin-dashboard__chart-title">
                <i class="admin-dashboard__chart-icon fas fa-pie-chart"></i>
                Order Status Distribution
              </h3>
            </div>
            <div class="admin-dashboard__chart-content">
              <Suspense>
                <template #default>
                  <orders-by-status-chart :data="ordersByStatus" />
                </template>
                <template #fallback>
                  <div class="admin-dashboard__chart-loading">
                    <i class="fas fa-spinner fa-pulse"></i>
                    <span>Loading chart...</span>
                  </div>
                </template>
              </Suspense>
            </div>
            <!-- Chart footer hidden to avoid duplication with chart legend -->
            <!-- <div class="admin-dashboard__chart-footer">
              <div class="admin-dashboard__status-legend">
                <div
                  class="admin-dashboard__legend-item"
                  v-for="status in orderStatusLegend"
                  :key="status.name"
                >
                  <div
                    class="admin-dashboard__legend-color"
                    :style="{ backgroundColor: status.color }"
                  ></div>
                  <span class="admin-dashboard__legend-label">{{ status.name }}</span>
                  <span class="admin-dashboard__legend-count">{{ status.count }}</span>
                </div>
              </div>
            </div> -->
          </div>

          <!-- Site Profit Chart -->
          <div class="admin-dashboard__chart-card admin-dashboard__chart-card--full">
            <div class="admin-dashboard__chart-header">
              <h3 class="admin-dashboard__chart-title">
                <i class="admin-dashboard__chart-icon fas fa-coins"></i>
                Site Commission Revenue (15%)
              </h3>
              <div class="admin-dashboard__chart-controls">
                <select
                  v-model="selectedProfitPeriod"
                  @change="handleProfitPeriodChange"
                  class="admin-dashboard__period-select"
                >
                  <option value="week">Last 7 Days</option>
                  <option value="month">Last 30 Days</option>
                  <option value="quarter">Last 3 Months</option>
                  <option value="year">Last Year</option>
                </select>
              </div>
            </div>
            <div class="admin-dashboard__chart-content">
              <Suspense>
                <template #default>
                  <site-profit-chart
                    :data="siteProfitData"
                    :total-profit="siteProfitForPeriod"
                    :period="selectedProfitPeriod"
                  />
                </template>
                <template #fallback>
                  <div class="admin-dashboard__chart-loading">
                    <i class="fas fa-spinner fa-pulse"></i>
                    <span>Loading chart...</span>
                  </div>
                </template>
              </Suspense>
            </div>
            <!-- Chart footer hidden to avoid duplication with chart data -->
            <!-- <div class="admin-dashboard__chart-footer">
              <div class="admin-dashboard__profit-breakdown">
                <div class="admin-dashboard__breakdown-item">
                  <span class="admin-dashboard__breakdown-label">Total Commission</span>
                  <span class="admin-dashboard__breakdown-value">{{ formatCurrency(siteProfitForPeriod) }}</span>
                </div>
                <div class="admin-dashboard__breakdown-item">
                  <span class="admin-dashboard__breakdown-label">Commission Rate</span>
                  <span class="admin-dashboard__breakdown-value">15%</span>
                </div>
                <div class="admin-dashboard__breakdown-item">
                  <span class="admin-dashboard__breakdown-label">Projected Monthly</span>
                  <span class="admin-dashboard__breakdown-value">{{ formatCurrency(projectedMonthlyProfit) }}</span>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </section>

      <!-- Quick Actions Section -->
      <section class="admin-dashboard__quick-actions-section">
        <div class="admin-dashboard__section-header">
          <h2 class="admin-dashboard__section-title">
            <i class="admin-dashboard__section-icon fas fa-bolt"></i>
            Quick Actions
          </h2>
          <p class="admin-dashboard__section-subtitle">
            Frequently used administrative tasks
          </p>
        </div>

        <div class="admin-dashboard__quick-actions-grid">
          <router-link
            to="/admin/products/create"
            class="admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--primary"
          >
            <i class="admin-dashboard__quick-action-icon fas fa-plus-circle"></i>
            <span class="admin-dashboard__quick-action-text">Add Product</span>
            <div class="admin-dashboard__quick-action-description">Create new marketplace listing</div>
          </router-link>

          <router-link
            to="/admin/orders"
            class="admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--info"
          >
            <i class="admin-dashboard__quick-action-icon fas fa-shipping-fast"></i>
            <span class="admin-dashboard__quick-action-text">Process Orders</span>
            <div class="admin-dashboard__quick-action-description">Manage customer orders</div>
          </router-link>

          <router-link
            to="/admin/categories"
            class="admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--success"
          >
            <i class="admin-dashboard__quick-action-icon fas fa-folder-plus"></i>
            <span class="admin-dashboard__quick-action-text">Manage Categories</span>
            <div class="admin-dashboard__quick-action-description">Organize product categories</div>
          </router-link>

          <router-link
            to="/admin/seller-requests"
            class="admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--warning"
          >
            <i class="admin-dashboard__quick-action-icon fas fa-user-check"></i>
            <span class="admin-dashboard__quick-action-text">Seller Requests</span>
            <div class="admin-dashboard__quick-action-description">Review pending applications</div>
            <div
              v-if="sellerRequests.length > 0"
              class="admin-dashboard__quick-action-badge"
            >
              {{ sellerRequests.length }}
            </div>
          </router-link>

          <router-link
            to="/admin/users"
            class="admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--secondary"
          >
            <i class="admin-dashboard__quick-action-icon fas fa-users-cog"></i>
            <span class="admin-dashboard__quick-action-text">Manage Users</span>
            <div class="admin-dashboard__quick-action-description">User administration</div>
          </router-link>

          <router-link
            to="/admin/reports"
            class="admin-dashboard__quick-action-btn admin-dashboard__quick-action-btn--dark"
          >
            <i class="admin-dashboard__quick-action-icon fas fa-chart-bar"></i>
            <span class="admin-dashboard__quick-action-text">View Reports</span>
            <div class="admin-dashboard__quick-action-description">Analytics and insights</div>
          </router-link>
        </div>
      </section>

      <!-- Recent Activity Section -->
      <section class="admin-dashboard__activity-section">
        <div class="admin-dashboard__section-header">
          <h2 class="admin-dashboard__section-title">
            <i class="admin-dashboard__section-icon fas fa-clock"></i>
            Recent Activity
          </h2>
          <p class="admin-dashboard__section-subtitle">
            Latest orders and seller requests
          </p>
        </div>

        <div class="admin-dashboard__activity-grid">
          <div class="admin-dashboard__activity-card">
            <recent-orders-optimized
              :orders="recentOrders"
              :loading="loading"
              @process-order="handleProcessOrder"
            />
          </div>

          <div class="admin-dashboard__activity-card">
            <Suspense>
              <template #default>
                <pending-seller-requests
                  :requests="sellerRequests"
                  :loading="loading"
                  @approve-request="handleApproveRequest"
                  @reject-request="handleRejectRequest"
                />
              </template>
              <template #fallback>
                <div class="admin-dashboard__activity-loading">
                  <i class="fas fa-spinner fa-pulse"></i>
                  <span>Loading seller requests...</span>
                </div>
              </template>
            </Suspense>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { dashboardService } from '@/admin/services/dashboard';
import RecentOrdersOptimized from '@/admin/components/dashboard/RecentOrdersOptimized.vue';

// Router
const router = useRouter();

// Lazy loaded components
const SalesChart = defineAsyncComponent(() => import('@/admin/components/dashboard/SalesChart.vue'));
const OrdersByStatusChart = defineAsyncComponent(() => import('@/admin/components/dashboard/OrdersByStatusChart.vue'));
const SiteProfitChart = defineAsyncComponent(() => import('@/admin/components/dashboard/SiteProfitChart.vue'));
const RecentOrders = defineAsyncComponent(() => import('@/admin/components/dashboard/RecentOrders.vue'));
const PendingSellerRequests = defineAsyncComponent(() => import('@/admin/components/dashboard/PendingSellerRequestsOptimized.vue'));

// Reactive state
const loading = ref(true);
const error = ref(null);
const loadingTime = ref(0);
const loadingTimer = ref(null);

// Dashboard data
const stats = ref({
  products: 0,
  users: 0,
  orders: 0,
  revenue: 0,
  categories: 0,
  usersTrend: 0,
  ordersTrend: 0,
  productsTrend: 0,
  revenueTrend: 0
});

// Chart data
const salesData = ref([]);
const ordersByStatus = ref([]);
const siteProfitData = ref([]);
const selectedPeriod = ref('week');
const selectedProfitPeriod = ref('week');

// Activity data
const recentOrders = ref([]);
const sellerRequests = ref([]);

// Computed properties
const noData = computed(() => {
  return !loading.value &&
         stats.value.products === 0 &&
         stats.value.users === 0 &&
         stats.value.orders === 0 &&
         stats.value.revenue === 0 &&
         stats.value.categories === 0 &&
         salesData.value.length === 0 &&
         ordersByStatus.value.length === 0;
});

const siteProfit = computed(() => {
  // Використовуємо значення з бекенду, якщо воно є, інакше розраховуємо як 15% від Revenue
  if (stats.value.siteProfit !== undefined) {
    const profit = typeof stats.value.siteProfit === 'string' ? Number(stats.value.siteProfit) : stats.value.siteProfit;
    return isNaN(profit) ? 0 : profit;
  }

  // Fallback для старих версій API
  const revenue = typeof stats.value.revenue === 'string' ? Number(stats.value.revenue) : stats.value.revenue;
  return isNaN(revenue) ? 0 : revenue * 0.15;
});

const siteProfitForPeriod = computed(() => {
  if (!siteProfitData.value || siteProfitData.value.length === 0) return 0;
  return siteProfitData.value.reduce((sum, item) => sum + (item.value || 0), 0);
});

const totalRevenue = computed(() => {
  if (!salesData.value || salesData.value.length === 0) return 0;
  return salesData.value.reduce((sum, item) => sum + (item.value || 0), 0);
});

const averageOrderValue = computed(() => {
  if (!stats.value.orders || stats.value.orders === 0) return 0;
  return totalRevenue.value / stats.value.orders;
});

const projectedMonthlyProfit = computed(() => {
  if (!siteProfitData.value || siteProfitData.value.length === 0) return 0;
  const dailyAverage = siteProfitForPeriod.value / 7;
  return dailyAverage * 30;
});

const orderStatusLegend = computed(() => {
  if (!ordersByStatus.value || ordersByStatus.value.length === 0) return [];

  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#6b7280'];
  return ordersByStatus.value.map((status, index) => ({
    name: status.label || status.name,
    count: status.value || status.count,
    color: colors[index % colors.length]
  }));
});

// Utility functions
const formatNumber = (value) => {
  return new Intl.NumberFormat('uk-UA').format(value || 0);
};

const formatCurrency = (value) => {
  const numValue = typeof value === 'string' ? Number(value) : value;

  if (isNaN(numValue)) {
    console.error('Invalid currency value:', value);
    return 'UAH 0.00';
  }

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    currencyDisplay: 'code'
  }).format(numValue).replace('UAH', 'UAH');
};

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown';

  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

// Real trend calculation methods based on API data
const getUsersTrend = () => {
  return stats.value.usersTrend > 0 ? 'positive' : stats.value.usersTrend < 0 ? 'negative' : 'neutral';
};

const getUsersTrendIcon = () => {
  const trend = getUsersTrend();
  if (trend === 'positive') return 'fas fa-arrow-up';
  if (trend === 'negative') return 'fas fa-arrow-down';
  return 'fas fa-minus';
};

const getUsersTrendPercentage = () => {
  return Math.abs(stats.value.usersTrend).toFixed(1);
};

const getOrdersTrend = () => {
  return stats.value.ordersTrend > 0 ? 'positive' : stats.value.ordersTrend < 0 ? 'negative' : 'neutral';
};

const getOrdersTrendIcon = () => {
  const trend = getOrdersTrend();
  if (trend === 'positive') return 'fas fa-arrow-up';
  if (trend === 'negative') return 'fas fa-arrow-down';
  return 'fas fa-minus';
};

const getOrdersTrendPercentage = () => {
  return Math.abs(stats.value.ordersTrend).toFixed(1);
};

const getProductsTrend = () => {
  return stats.value.productsTrend > 0 ? 'positive' : stats.value.productsTrend < 0 ? 'negative' : 'neutral';
};

const getProductsTrendIcon = () => {
  const trend = getProductsTrend();
  if (trend === 'positive') return 'fas fa-arrow-up';
  if (trend === 'negative') return 'fas fa-arrow-down';
  return 'fas fa-minus';
};

const getProductsTrendPercentage = () => {
  return Math.abs(stats.value.productsTrend).toFixed(1);
};

const getRevenueTrend = () => {
  // Перевіряємо, чи є валідні дані для тренду
  if (stats.value.revenueTrend === null || stats.value.revenueTrend === undefined || isNaN(stats.value.revenueTrend)) {
    return 'neutral';
  }
  return stats.value.revenueTrend > 0 ? 'positive' : stats.value.revenueTrend < 0 ? 'negative' : 'neutral';
};

const getRevenueTrendIcon = () => {
  const trend = getRevenueTrend();
  if (trend === 'positive') return 'fas fa-arrow-up';
  if (trend === 'negative') return 'fas fa-arrow-down';
  return 'fas fa-minus';
};

const getRevenueTrendPercentage = () => {
  // Перевіряємо, чи є валідні дані для тренду
  if (stats.value.revenueTrend === null || stats.value.revenueTrend === undefined || isNaN(stats.value.revenueTrend)) {
    return '0.0';
  }
  return Math.abs(stats.value.revenueTrend).toFixed(1);
};

const getProfitTrend = () => {
  // Використовуємо окремий тренд для Site Profit з бекенду, якщо він є
  if (stats.value.siteProfitTrend !== undefined) {
    return stats.value.siteProfitTrend;
  }

  // Fallback - слідує за Revenue трендом
  return getRevenueTrend();
};

const getProfitTrendIcon = () => {
  const trend = getProfitTrend();
  if (trend > 0) {
    return 'fas fa-arrow-up admin-dashboard__trend-icon--positive';
  } else if (trend < 0) {
    return 'fas fa-arrow-down admin-dashboard__trend-icon--negative';
  } else {
    return 'fas fa-minus admin-dashboard__trend-icon--neutral';
  }
};

const getProfitTrendPercentage = () => {
  const trend = getProfitTrend();
  if (typeof trend !== 'number') {
    return '0.0';
  }
  return Math.abs(trend).toFixed(1);
};

const getGrowthRate = () => {
  return (Math.random() * 30 - 10).toFixed(1);
};

const getGrowthClass = () => {
  const rate = parseFloat(getGrowthRate());
  if (rate > 0) return 'positive';
  if (rate < 0) return 'negative';
  return 'neutral';
};

// Data fetching methods
let fetchDataTimeout = null;
const fetchDashboardData = async () => {
  if (fetchDataTimeout) {
    clearTimeout(fetchDataTimeout);
  }

  if (loadingTimer.value) {
    clearInterval(loadingTimer.value);
    loadingTimer.value = null;
  }

  loading.value = true;
  error.value = null;
  loadingTime.value = 0;

  loadingTimer.value = setInterval(() => {
    loadingTime.value++;
  }, 1000);

  fetchDataTimeout = setTimeout(async () => {
    try {
      console.log('Fetching dashboard data...');
      const data = await dashboardService.getDashboardData();

      console.log('Dashboard data received:', data);

      if (!data) {
        throw new Error('No data received from server');
      }

      if (data.stats) {
        stats.value = {
          products: data.stats.products || 0,
          users: data.stats.users || 0,
          orders: data.stats.orders || 0,
          revenue: Number(data.stats.revenue) || 0,
          categories: data.stats.categories || 0,
          usersTrend: isNaN(Number(data.stats.usersTrend)) ? 0 : Number(data.stats.usersTrend),
          ordersTrend: isNaN(Number(data.stats.ordersTrend)) ? 0 : Number(data.stats.ordersTrend),
          productsTrend: isNaN(Number(data.stats.productsTrend)) ? 0 : Number(data.stats.productsTrend),
          revenueTrend: isNaN(Number(data.stats.revenueTrend)) ? 0 : Number(data.stats.revenueTrend)
        };

        if (typeof stats.value.revenue !== 'number' || isNaN(stats.value.revenue)) {
          console.warn('Revenue is not a valid number, converting:', data.stats.revenue);
          stats.value.revenue = 0;

          if (typeof data.stats.revenue === 'string') {
            const parsed = parseFloat(data.stats.revenue.replace(/[^\d.-]/g, ''));
            if (!isNaN(parsed)) {
              stats.value.revenue = parsed;
            }
          }
        }

        salesData.value = data.salesData || [];
        ordersByStatus.value = data.ordersByStatus || [];
        recentOrders.value = data.recentOrders || [];
        sellerRequests.value = data.sellerRequests || [];

        try {
          const weekSalesData = await dashboardService.getSalesData('week');
          salesData.value = weekSalesData;

          const weekProfitData = await dashboardService.getSiteProfitData('week');
          siteProfitData.value = weekProfitData;
        } catch (chartError) {
          console.warn('Error loading initial chart data:', chartError);
          siteProfitData.value = (data.salesData || []).map(item => ({
            ...item,
            value: item.value * 0.15
          }));
        }
      } else {
        console.log('Trying alternative data structure');
        stats.value = {
          products: data.products || 0,
          users: data.users || 0,
          orders: data.orders || 0,
          revenue: Number(data.revenue) || 0,
          categories: data.categories || 0,
          usersTrend: Number(data.usersTrend) || 0,
          ordersTrend: Number(data.ordersTrend) || 0,
          productsTrend: Number(data.productsTrend) || 0,
          revenueTrend: Number(data.revenueTrend) || 0
        };

        salesData.value = [];
        ordersByStatus.value = [];
        recentOrders.value = [];
        sellerRequests.value = [];
        siteProfitData.value = [];
      }

      if (noData.value) {
        console.warn('Dashboard data is empty');
      }
    } catch (err) {
      if (!err.message?.includes('canceled') && !err.message?.includes('aborted')) {
        console.error('Error fetching dashboard data:', err);
        error.value = `Failed to load dashboard data: ${err.message}. Please try again later.`;
      }
    } finally {
      if (loadingTimer.value) {
        clearInterval(loadingTimer.value);
        loadingTimer.value = null;
      }

      loading.value = false;
      fetchDataTimeout = null;
    }
  }, 100);
};

const cancelAndRetry = () => {
  if (fetchDataTimeout) {
    clearTimeout(fetchDataTimeout);
    fetchDataTimeout = null;
  }

  if (loadingTimer.value) {
    clearInterval(loadingTimer.value);
    loadingTimer.value = null;
  }

  loading.value = false;
  loadingTime.value = 0;

  setTimeout(() => {
    fetchDashboardData();
  }, 500);
};

// Period change handlers
let periodChangeTimeout = null;
const handlePeriodChange = async (event) => {
  if (periodChangeTimeout) {
    clearTimeout(periodChangeTimeout);
  }

  const period = event.target ? event.target.value : event;
  selectedPeriod.value = period;

  periodChangeTimeout = setTimeout(async () => {
    try {
      const data = await dashboardService.getSalesData(period);
      salesData.value = data;
    } catch (err) {
      if (!err.message?.includes('canceled') && !err.message?.includes('aborted')) {
        console.error('Error fetching sales data:', err);
        error.value = err.message || `Failed to load sales data for period: ${period}`;
      }
    } finally {
      periodChangeTimeout = null;
    }
  }, 100);
};

let profitPeriodChangeTimeout = null;
const handleProfitPeriodChange = async (event) => {
  if (profitPeriodChangeTimeout) {
    clearTimeout(profitPeriodChangeTimeout);
  }

  const period = event.target ? event.target.value : event;
  selectedProfitPeriod.value = period;

  profitPeriodChangeTimeout = setTimeout(async () => {
    try {
      const profitData = await dashboardService.getSiteProfitData(period);
      siteProfitData.value = profitData;
    } catch (err) {
      if (!err.message?.includes('canceled') && !err.message?.includes('aborted')) {
        console.error('Error fetching profit data:', err);
        error.value = err.message || `Failed to load profit data for period: ${period}`;
      }
    } finally {
      profitPeriodChangeTimeout = null;
    }
  }, 100);
};

// Event handlers for optimized components
const handleProcessOrder = (orderId) => {
  console.log('Processing order:', orderId);
  // Here you would typically call an API to process the order
  // For now, just show a notification or redirect
};

const handleApproveRequest = (requestId) => {
  console.log('Approving seller request:', requestId);
  // Here you would typically call an API to approve the request
  // For now, just show a notification
};

const handleRejectRequest = (requestId) => {
  console.log('Rejecting seller request:', requestId);
  // Here you would typically call an API to reject the request
  // For now, just show a notification
};

// Navigation methods
const viewOrder = (orderId) => {
  router.push(`/admin/orders/${orderId}`);
};

// Lifecycle hooks
onMounted(() => {
  fetchDashboardData();
});

onUnmounted(() => {
  if (fetchDataTimeout) {
    clearTimeout(fetchDataTimeout);
  }
  if (periodChangeTimeout) {
    clearTimeout(periodChangeTimeout);
  }
  if (profitPeriodChangeTimeout) {
    clearTimeout(profitPeriodChangeTimeout);
  }

  if (loadingTimer.value) {
    clearInterval(loadingTimer.value);
    loadingTimer.value = null;
  }
});
</script>

<style scoped>
@import '@/assets/css/admin/pages/dashboard.css';

/* Dashboard specific styles - Recent Orders component is now external */
</style>
