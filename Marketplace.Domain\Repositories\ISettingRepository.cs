using Marketplace.Domain.Entities;

namespace Marketplace.Domain.Repositories;

public interface ISettingRepository : IRepository<Setting>
{
    Task<Setting?> GetByKeyAsync(string key, CancellationToken cancellationToken = default);
    Task<List<Setting>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<List<Setting>> GetPublicSettingsAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, string>> GetSettingsDictionaryAsync(string? category = null, CancellationToken cancellationToken = default);
    Task<bool> ExistsByKeyAsync(string key, CancellationToken cancellationToken = default);
    Task UpsertAsync(string key, string value, string category, SettingType type = SettingType.String, string? description = null, bool isPublic = false, CancellationToken cancellationToken = default);
    Task DeleteByKeyAsync(string key, CancellationToken cancellationToken = default);
}
