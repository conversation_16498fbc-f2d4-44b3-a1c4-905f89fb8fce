<template>
  <div class="data-table-container">
    <!-- Table Header with Search and Actions -->
    <div class="data-table-header">
      <div class="search-container" v-if="showSearch">
        <div class="field has-addons">
          <div class="control has-icons-left is-expanded">
            <input 
              class="input" 
              type="text" 
              v-model="searchQuery" 
              :placeholder="searchPlaceholder"
              @keyup.enter="onSearch">
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
          </div>
          <div class="control">
            <button class="button is-primary" @click="onSearch">
              Search
            </button>
          </div>
        </div>
      </div>
      
      <div class="actions-container">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- Loading Indicator -->
    <div v-if="loading" class="table-loading">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p>Loading data...</p>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="isEmpty" class="table-empty-state">
      <span class="icon is-large">
        <i :class="`fas fa-${emptyIcon} fa-3x`"></i>
      </span>
      <h3>{{ emptyTitle }}</h3>
      <p>{{ emptyMessage }}</p>
      <slot name="empty-action"></slot>
    </div>
    
    <!-- Data Table -->
    <div v-else class="table-responsive">
      <table class="table is-fullwidth is-hoverable">
        <thead>
          <tr>
            <th v-if="selectable" class="is-narrow">
              <label class="checkbox">
                <input 
                  type="checkbox" 
                  :checked="allSelected" 
                  @change="toggleSelectAll">
              </label>
            </th>
            <th 
              v-for="column in columns" 
              :key="column.field"
              :class="[column.class, { 'is-sortable': column.sortable }]"
              @click="column.sortable ? sort(column.field) : null">
              {{ column.label }}
              <span 
                v-if="column.sortable" 
                class="icon is-small sort-icon">
                <i 
                  class="fas" 
                  :class="getSortIconClass(column.field)">
                </i>
              </span>
            </th>
            <th v-if="hasRowActions" class="actions-column">Actions</th>
          </tr>
        </thead>
        
        <tbody>
          <tr 
            v-for="(item, index) in items" 
            :key="getItemKey(item, index)"
            :class="{ 'is-selected': isSelected(item) }">
            <td v-if="selectable" class="is-narrow">
              <label class="checkbox">
                <input 
                  type="checkbox" 
                  :checked="isSelected(item)" 
                  @change="toggleSelect(item)">
              </label>
            </td>
            <td 
              v-for="column in columns" 
              :key="column.field"
              :class="column.class">
              <slot 
                :name="`cell-${column.field}`" 
                :item="item" 
                :value="getItemValue(item, column.field)">
                {{ formatValue(getItemValue(item, column.field), column.format) }}
              </slot>
            </td>
            <td v-if="hasRowActions" class="actions-column">
              <slot name="row-actions" :item="item" :index="index"></slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- Pagination -->
    <div v-if="showPagination && !isEmpty" class="data-table-footer">
      <div class="pagination-info">
        Showing {{ paginationInfo.from }} to {{ paginationInfo.to }} of {{ paginationInfo.total }} entries
      </div>
      
      <pagination 
        :current-page="currentPage" 
        :total-pages="totalPages"
        @page-changed="onPageChange">
      </pagination>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import Pagination from './Pagination.vue';

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  items: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  itemKey: {
    type: String,
    default: 'id'
  },
  selectable: {
    type: Boolean,
    default: false
  },
  hasRowActions: {
    type: Boolean,
    default: false
  },
  showSearch: {
    type: Boolean,
    default: true
  },
  searchPlaceholder: {
    type: String,
    default: 'Search...'
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  currentPage: {
    type: Number,
    default: 1
  },
  totalPages: {
    type: Number,
    default: 1
  },
  totalItems: {
    type: Number,
    default: 0
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  emptyTitle: {
    type: String,
    default: 'No Data Found'
  },
  emptyMessage: {
    type: String,
    default: 'There are no items to display.'
  },
  emptyIcon: {
    type: String,
    default: 'inbox'
  }
});

const emit = defineEmits([
  'search', 
  'sort', 
  'page-change', 
  'select', 
  'select-all'
]);

// Search functionality
const searchQuery = ref('');

const onSearch = () => {
  emit('search', searchQuery.value);
};

// Sorting functionality
const sortField = ref('');
const sortDirection = ref('asc');

const sort = (field) => {
  if (sortField.value === field) {
    // Toggle direction if same field
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // New field, default to ascending
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  
  emit('sort', { field: sortField.value, direction: sortDirection.value });
};

const getSortIconClass = (field) => {
  if (sortField.value !== field) {
    return 'fa-sort';
  }
  return sortDirection.value === 'asc' ? 'fa-sort-up' : 'fa-sort-down';
};

// Selection functionality
const selectedItems = ref([]);

const isSelected = (item) => {
  return selectedItems.value.some(selectedItem => 
    getItemKey(selectedItem) === getItemKey(item)
  );
};

const toggleSelect = (item) => {
  if (isSelected(item)) {
    selectedItems.value = selectedItems.value.filter(selectedItem => 
      getItemKey(selectedItem) !== getItemKey(item)
    );
  } else {
    selectedItems.value.push(item);
  }
  
  emit('select', selectedItems.value);
};

const allSelected = computed(() => {
  return props.items.length > 0 && selectedItems.value.length === props.items.length;
});

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedItems.value = [];
  } else {
    selectedItems.value = [...props.items];
  }
  
  emit('select-all', selectedItems.value);
};

// Pagination functionality
const onPageChange = (page) => {
  emit('page-change', page);
};

// Computed properties
const isEmpty = computed(() => {
  return !props.loading && (!props.items || props.items.length === 0);
});

const paginationInfo = computed(() => {
  const total = props.totalItems;
  const from = props.items.length > 0 
    ? (props.currentPage - 1) * props.itemsPerPage + 1 
    : 0;
  const to = Math.min(from + props.items.length - 1, total);
  
  return { from, to, total };
});

// Helper functions
const getItemKey = (item, index) => {
  return item[props.itemKey] || index;
};

const getItemValue = (item, field) => {
  // Handle nested properties with dot notation (e.g., 'user.name')
  if (field.includes('.')) {
    return field.split('.').reduce((obj, key) => 
      obj && obj[key] !== undefined ? obj[key] : null, item);
  }
  return item[field];
};

const formatValue = (value, format) => {
  if (value === undefined || value === null) {
    return '';
  }
  
  if (!format) {
    return value;
  }
  
  switch (format) {
    case 'date':
      return new Date(value).toLocaleDateString();
    case 'datetime':
      return new Date(value).toLocaleString();
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'UAH'
      }).format(value);
    case 'number':
      return new Intl.NumberFormat().format(value);
    case 'boolean':
      return value ? 'Yes' : 'No';
    default:
      return value;
  }
};

// Reset selection when items change
watch(() => props.items, () => {
  selectedItems.value = [];
});
</script>

<style scoped>
.data-table-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.data-table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f1f1f1;
}

.search-container {
  flex: 1;
  max-width: 500px;
}

.actions-container {
  display: flex;
  gap: 0.5rem;
}

.table-responsive {
  overflow-x: auto;
}

.table {
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f9;
}

.table th.is-sortable {
  cursor: pointer;
}

.table th.is-sortable:hover {
  background-color: #f1f1f1;
}

.sort-icon {
  margin-left: 0.25rem;
}

.actions-column {
  width: 1%;
  white-space: nowrap;
  text-align: right;
}

.table-loading,
.table-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
  color: #7a7a7a;
}

.table-loading .icon,
.table-empty-state .icon {
  margin-bottom: 1rem;
}

.table-empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #363636;
}

.data-table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-top: 1px solid #f1f1f1;
}

.pagination-info {
  color: #7a7a7a;
  font-size: 0.9rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

@media screen and (max-width: 768px) {
  .data-table-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-container {
    max-width: none;
    margin-bottom: 1rem;
  }
  
  .data-table-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .pagination-info {
    order: 2;
  }
}
</style>
