using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Microsoft.AspNetCore.Http;
using System.Text.Json;

namespace Marketplace.Application.Services;

public interface ILoggingService
{
    Task LogInfoAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null);
    Task LogWarningAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null);
    Task LogErrorAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null);
    Task LogCriticalAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null);
    Task LogUserActionAsync(string action, Guid userId, Guid? entityId = null, string? entityType = null, object? additionalData = null);
    Task LogSecurityEventAsync(string message, Guid? userId = null, object? additionalData = null);
    Task LogSystemEventAsync(string message, object? additionalData = null);
}

public class LoggingService : ILoggingService
{
    private readonly ILogRepository _logRepository;
    private readonly IHttpContextAccessor? _httpContextAccessor;

    public LoggingService(ILogRepository logRepository, IHttpContextAccessor? httpContextAccessor = null)
    {
        _logRepository = logRepository;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task LogInfoAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null)
    {
        await LogAsync(LogLevel.Info, category, message, userId, entityId, entityType, additionalData);
    }

    public async Task LogWarningAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null)
    {
        await LogAsync(LogLevel.Warning, category, message, userId, entityId, entityType, additionalData);
    }

    public async Task LogErrorAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null)
    {
        await LogAsync(LogLevel.Error, category, message, userId, entityId, entityType, additionalData);
    }

    public async Task LogCriticalAsync(string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null)
    {
        await LogAsync(LogLevel.Critical, category, message, userId, entityId, entityType, additionalData);
    }

    public async Task LogUserActionAsync(string action, Guid userId, Guid? entityId = null, string? entityType = null, object? additionalData = null)
    {
        await LogAsync(LogLevel.Info, LogCategories.User, $"User action: {action}", userId, entityId, entityType, additionalData);
    }

    public async Task LogSecurityEventAsync(string message, Guid? userId = null, object? additionalData = null)
    {
        await LogAsync(LogLevel.Warning, LogCategories.Security, message, userId, additionalData: additionalData);
    }

    public async Task LogSystemEventAsync(string message, object? additionalData = null)
    {
        await LogAsync(LogLevel.Info, LogCategories.System, message, additionalData: additionalData);
    }

    private async Task LogAsync(LogLevel level, string category, string message, Guid? userId = null, Guid? entityId = null, string? entityType = null, object? additionalData = null)
    {
        try
        {
            var httpContext = _httpContextAccessor?.HttpContext;
            var ipAddress = GetClientIpAddress(httpContext);
            var userAgent = httpContext?.Request.Headers["User-Agent"].ToString();

            var additionalDataJson = additionalData != null 
                ? JsonSerializer.Serialize(additionalData, new JsonSerializerOptions { WriteIndented = false })
                : null;

            var log = new Log(
                level,
                category,
                message,
                userId,
                entityId,
                entityType,
                additionalDataJson,
                ipAddress,
                userAgent
            );

            await _logRepository.AddAsync(log, CancellationToken.None);
        }
        catch (Exception ex)
        {
            // Don't throw exceptions from logging service to avoid breaking the main flow
            // In production, you might want to log this to a different system
            Console.WriteLine($"Failed to log message: {ex.Message}");
        }
    }

    private string? GetClientIpAddress(HttpContext? httpContext)
    {
        if (httpContext == null) return null;

        // Check for forwarded IP first (in case of proxy/load balancer)
        var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return httpContext.Connection.RemoteIpAddress?.ToString();
    }
}


