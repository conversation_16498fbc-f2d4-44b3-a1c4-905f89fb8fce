<!DOCTYPE html>
<html lang="uk">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Klondike - Інтернет-магазин з широким асортиментом товарів">
  <meta name="keywords" content="інтернет-магазин, товари, покупки, Klondike">
  <meta name="author" content="Klondike">

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/assets/favicon-uSLXchjO.ico">
  <link rel="shortcut icon" type="image/x-icon" href="/assets/favicon-uSLXchjO.ico">

  <!-- Preconnect to external domains for better performance -->
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Google Fonts - Rubik -->
  <link href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- External CSS Libraries -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
  <link href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css" rel="stylesheet" crossorigin="anonymous">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">

  <title>Klondike - Інтернет-магазин</title>

  <style>
    /* CSS Variables for consistent theming */
    :root {
      /* Light theme colors (default) */
      --primary-bg: #ffffff;
      --secondary-bg: #f8f9fa;
      --card-bg: #ffffff;
      --text-primary: #333333;
      --text-secondary: #666666;
      --text-muted: #999999;
      --border-color: #dee2e6;
      --accent-color: #ff7700;
      --accent-color-hover: #e66600;
      --success-color: #28a745;
      --warning-color: #ffc107;
      --danger-color: #F27318E3;
      --info-color: #17a2b8;

      /* Dark theme colors (for admin panel) */
      --dark-bg: #111827;
      --darker-bg: #0f172a;
      --dark-card-bg: #1e293b;
      --dark-text-primary: #f3f4f6;
      --dark-text-secondary: #9ca3af;
      --dark-border-color: #374151;
      --dark-accent-color: #3b82f6;
      --dark-accent-color-hover: #2563eb;
    }

    /* Global reset and base styles */
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
    }

    body {
      font-family: 'Rubik', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
      background-color: var(--secondary-bg);
      color: var(--text-primary);
      line-height: 1.6;
      margin: 0;
      padding: 0;
      min-height: 100vh;
    }

    /* Font Awesome icon fixes */
    .fas, .far, .fab, .fa {
      font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands' !important;
      font-weight: 900;
    }

    .far {
      font-weight: 400;
    }

    .fab {
      font-weight: 400;
    }

    /* Loading spinner styles */
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* Button styles */
    .btn-primary {
      background-color: var(--accent-color);
      border-color: var(--accent-color);
      color: white;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background-color: var(--accent-color-hover);
      border-color: var(--accent-color-hover);
      color: white;
    }

    /* Card styles */
    .card {
      border-radius: 8px;
      overflow: hidden;
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.2s ease;
    }

    .card:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    /* Form styles */
    .form-control {
      border-radius: 6px;
      border: 1px solid var(--border-color);
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .form-control:focus {
      border-color: var(--accent-color);
      box-shadow: 0 0 0 0.2rem rgba(255, 119, 0, 0.25);
    }

    /* Alert styles */
    .alert {
      border-radius: 6px;
      border: none;
    }

    /* Table styles */
    .table {
      background-color: var(--card-bg);
    }

    .table th {
      border-top: none;
      font-weight: 600;
      color: var(--text-secondary);
    }

    /* Scrollbar styles */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: var(--secondary-bg);
    }

    ::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #999;
    }

    /* Responsive utilities */
    @media (max-width: 768px) {
      body {
        font-size: 14px;
      }

      .container {
        padding-left: 15px;
        padding-right: 15px;
      }
    }

    /* Admin theme styles */
    .admin-theme {
      --primary-bg: var(--dark-bg);
      --secondary-bg: var(--darker-bg);
      --card-bg: var(--dark-card-bg);
      --text-primary: var(--dark-text-primary);
      --text-secondary: var(--dark-text-secondary);
      --border-color: var(--dark-border-color);
      --accent-color: var(--dark-accent-color);
      --accent-color-hover: var(--dark-accent-color-hover);
    }

    .admin-theme body {
      background-color: var(--dark-bg);
      color: var(--dark-text-primary);
    }

    /* Bulma overrides for admin theme */
    .admin-theme .columns {
      margin: 0;
      min-height: 100vh;
    }

    .admin-theme .column {
      padding: 0;
    }

    .admin-theme .section {
      padding: 1.5rem;
      background-color: var(--dark-bg);
    }

    .admin-theme .container {
      background-color: transparent;
    }

    .admin-theme .card {
      background-color: var(--dark-card-bg);
      border-color: var(--dark-border-color);
      color: var(--dark-text-primary);
    }

    .admin-theme .table {
      background-color: var(--dark-card-bg);
      color: var(--dark-text-primary);
    }

    .admin-theme .table th {
      color: var(--dark-text-secondary);
      background-color: var(--darker-bg);
    }

    .admin-theme .table td {
      border-color: var(--dark-border-color);
    }

    .admin-theme .form-control,
    .admin-theme .input,
    .admin-theme .textarea,
    .admin-theme .select select {
      background-color: var(--dark-card-bg);
      border-color: var(--dark-border-color);
      color: var(--dark-text-primary);
    }

    .admin-theme .form-control:focus,
    .admin-theme .input:focus,
    .admin-theme .textarea:focus,
    .admin-theme .select select:focus {
      border-color: var(--dark-accent-color);
      box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    }

    .admin-theme .button {
      background-color: var(--dark-card-bg);
      border-color: var(--dark-border-color);
      color: var(--dark-text-primary);
    }

    .admin-theme .button:hover {
      background-color: var(--darker-bg);
      border-color: var(--dark-accent-color);
    }

    .admin-theme .button.is-primary {
      background-color: var(--dark-accent-color);
      border-color: var(--dark-accent-color);
      color: white;
    }

    .admin-theme .button.is-primary:hover {
      background-color: var(--dark-accent-color-hover);
      border-color: var(--dark-accent-color-hover);
    }

    .admin-theme .box {
      background-color: var(--dark-card-bg);
      border: 1px solid var(--dark-border-color);
      color: var(--dark-text-primary);
    }

    .admin-theme .panel {
      background-color: var(--dark-card-bg);
      border: 1px solid var(--dark-border-color);
    }

    .admin-theme .panel-block {
      background-color: transparent;
      border-color: var(--dark-border-color);
      color: var(--dark-text-primary);
    }

    .admin-theme .panel-block:hover {
      background-color: var(--darker-bg);
    }

    .admin-theme .panel-block.is-active {
      background-color: var(--dark-accent-color);
      color: white;
    }

    .admin-theme .title,
    .admin-theme .subtitle {
      color: var(--dark-text-primary);
    }

    .admin-theme .menu-label {
      color: var(--dark-text-secondary);
    }

    .admin-theme .dropdown-content {
      background-color: var(--dark-card-bg);
      border: 1px solid var(--dark-border-color);
    }

    .admin-theme .dropdown-item {
      color: var(--dark-text-primary);
    }

    .admin-theme .dropdown-item:hover {
      background-color: var(--darker-bg);
      color: var(--dark-text-primary);
    }

    /* Admin specific fixes */
    .admin-theme .sidebar-column {
      background-color: #2a2a2a !important;
    }

    .admin-theme .main-content {
      background-color: var(--dark-bg) !important;
    }

    .admin-theme .admin-page-container {
      background-color: transparent !important;
    }

    /* Ensure proper visibility */
    .admin-theme * {
      color: inherit;
    }

    .admin-theme .has-text-white {
      color: white !important;
    }

    .admin-theme .panel-icon {
      color: var(--dark-text-secondary);
    }

    .admin-theme .panel-block.is-active .panel-icon {
      color: white;
    }

    /* Vue transition styles */
    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.2s ease, transform 0.2s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
      opacity: 0;
      transform: translateY(10px);
    }

    /* Print styles */
    @media print {
      .no-print {
        display: none !important;
      }
    }

    /* Admin pages should keep original font */
    .admin-layout,
    .admin-layout *,
    [data-admin="true"],
    [data-admin="true"] *,
    body.admin-page,
    body.admin-page * {
      font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif !important;
    }
  </style>

  <!-- Vite will inject the module script here in development mode -->
  <script type="module" crossorigin src="/assets/index-L-hJxM_5.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-n-75hzeL.css">
</head>
<body>
  <!-- Vue.js app mount point -->
  <div id="app">
    <!-- Loading fallback for users with JavaScript disabled -->
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>JavaScript Required</h1>
        <p>Цей додаток потребує JavaScript для роботи. Будь ласка, увімкніть JavaScript у вашому браузері.</p>
        <p>This application requires JavaScript to run. Please enable JavaScript in your browser.</p>
      </div>
    </noscript>

    <!-- Initial loading indicator -->
    <div id="initial-loading" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      font-family: 'Rubik', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    ">
      <div style="text-align: center;">
        <div class="loading-spinner" style="margin-bottom: 20px;"></div>
        <h3 style="color: #333; margin: 0;">Завантаження...</h3>
        <p style="color: #666; margin: 10px 0 0 0;">Loading Klondike...</p>
      </div>
    </div>
  </div>

  <!-- External JavaScript Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>

  <!-- Font Awesome JavaScript (for better icon loading) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" integrity="sha512-fD9DI5bZwQxOi7MhYWnnNPlvXdp/2Pj3XSTRrFs5FQa4mizyGLnJcN6tuvUS6LbmgN1ut+XGSABKvjN0H6Aoow==" crossorigin="anonymous"></script>

  <!-- Chart.js for data visualization -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js" crossorigin="anonymous"></script>

  <!-- Vite will inject the module script here in development mode -->


  <!-- Remove initial loading indicator after app loads -->
  <script>
    // Remove loading indicator when Vue app is mounted
    window.addEventListener('load', function() {
      setTimeout(function() {
        const loadingElement = document.getElementById('initial-loading');
        if (loadingElement) {
          loadingElement.style.opacity = '0';
          loadingElement.style.transition = 'opacity 0.3s ease';
          setTimeout(function() {
            loadingElement.remove();
          }, 300);
        }
      }, 500);
    });

    // Error handling for failed script loading
    window.addEventListener('error', function(e) {
      if (e.target.tagName === 'SCRIPT') {
        console.error('Failed to load script:', e.target.src);
      }
    });
  </script>
</body>
</html>
