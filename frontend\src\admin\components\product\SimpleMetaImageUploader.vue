<template>
  <div class="simple-meta-image-uploader">
    <div class="meta-uploader-content">
      <!-- Current Image Display -->
      <div v-if="displayImageUrl" class="current-image-section">
        <div class="image-preview">
          <img :src="displayImageUrl" alt="Meta image" @error="handleImageError" />
          <div class="image-overlay">
            <button 
              type="button" 
              class="overlay-btn overlay-btn--view"
              @click="openModal"
              title="View full size"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button 
              type="button" 
              class="overlay-btn overlay-btn--delete"
              @click="removeImage"
              title="Remove image"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="image-info">
          <span class="image-label">Current meta image</span>
        </div>
      </div>

      <!-- Upload Zone -->
      <div 
        v-else
        class="upload-zone"
        :class="{ 
          'upload-zone--dragover': isDragOver,
          'upload-zone--uploading': isUploading
        }"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
      >
        <input
          type="file"
          ref="fileInput"
          accept="image/*"
          style="display: none"
          @change="handleFileSelect"
        />
        
        <div class="upload-content">
          <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt" v-if="!isUploading"></i>
            <i class="fas fa-spinner fa-spin" v-else></i>
          </div>
          <div class="upload-text">
            <h4 v-if="!isUploading">Upload Meta Image</h4>
            <h4 v-else>Uploading...</h4>
            <p>Drag & drop an image or click to browse</p>
            <p class="upload-hint">Recommended: 1200x630px for social media</p>
          </div>
        </div>
        
        <div v-if="isUploading" class="upload-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Social Preview -->
      <div v-if="displayImageUrl || socialTitle || socialDescription" class="social-preview">
        <h4 class="preview-title">Social Media Preview</h4>
        <div class="preview-card">
          <div v-if="displayImageUrl" class="preview-image">
            <img :src="displayImageUrl" alt="Social preview" />
          </div>
          <div class="preview-content">
            <div class="preview-text-title">{{ socialTitle || 'Product Title' }}</div>
            <div class="preview-text-description">{{ socialDescription || 'Product description will appear here...' }}</div>
            <div class="preview-text-url">marketplace.com</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="showModal" class="image-modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <img :src="displayImageUrl" alt="Meta image full size" />
        <button type="button" class="modal-close" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleMetaImageUploader',
  props: {
    currentImage: {
      type: String,
      default: null
    },
    socialTitle: {
      type: String,
      default: ''
    },
    socialDescription: {
      type: String,
      default: ''
    },
    entityType: {
      type: String,
      default: 'product'
    },
    entityId: {
      type: [String, Number],
      required: true
    },
    localMode: {
      type: Boolean,
      default: true // Default to local mode for products
    },
    maxSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    },
    allowedTypes: {
      type: Array,
      default: () => ['image/jpeg', 'image/png', 'image/webp']
    }
  },
  emits: ['meta-image-changed', 'meta-image-uploaded', 'meta-image-removed'],
  data() {
    return {
      isDragOver: false,
      isUploading: false,
      uploadProgress: 0,
      error: null,
      showModal: false,
      // Local mode support
      localImageUrl: null,
      pendingFile: null,
      pendingRemoval: false,
      currentImageUrl: null
    }
  },
  computed: {
    displayImageUrl() {
      // If meta image is marked for removal, don't show it
      if (this.pendingRemoval) {
        return null
      }
      return this.localImageUrl || this.currentImageUrl || this.currentImage
    },
    uploadHint() {
      const maxSizeMB = Math.round(this.maxSize / 1024 / 1024)
      const types = this.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')
      return `Maximum size: ${maxSizeMB}MB. Formats: ${types}`
    },
    hasLocalChanges() {
      const changes = this.pendingRemoval || !!this.pendingFile
      console.log('SimpleMetaImageUploader hasLocalChanges computed:', changes, {
        pendingFile: !!this.pendingFile,
        pendingRemoval: this.pendingRemoval
      })
      return changes
    },
    isInLocalMode() {
      return this.localMode || !this.entityId || this.entityId === 'temp-id'
    }
  },
  mounted() {
    this.currentImageUrl = this.currentImage
  },
  methods: {
    triggerFileInput() {
      if (this.isUploading) return
      try {
        const input = this.$refs.fileInput
        if (input) {
          input.click()
        }
      } catch (error) {
        console.error('Error triggering file input:', error)
      }
    },
    
    handleFileSelect(event) {
      try {
        const files = event.target.files
        if (files && files.length > 0) {
          this.processFile(files[0])
        }
      } catch (error) {
        console.error('Error handling file select:', error)
        this.error = 'Error selecting file'
      }
    },
    
    handleDrop(event) {
      try {
        event.preventDefault()
        this.isDragOver = false
        
        const files = event.dataTransfer.files
        if (files && files.length > 0) {
          this.processFile(files[0])
        }
      } catch (error) {
        console.error('Error handling drop:', error)
        this.error = 'Error processing dropped file'
      }
    },
    
    handleDragOver(event) {
      event.preventDefault()
    },
    
    handleDragEnter(event) {
      event.preventDefault()
      this.isDragOver = true
    },
    
    handleDragLeave(event) {
      event.preventDefault()
      this.isDragOver = false
    },
    
    async processFile(file) {
      if (!file || typeof file !== 'object') {
        this.error = 'Invalid file'
        return
      }

      try {
        this.error = null

        // Validate file
        const validation = this.validateImageFile(file)
        if (!validation.isValid) {
          this.error = validation.errors.join(', ')
          return
        }

        // Check working mode
        if (this.isInLocalMode) {
          // Local mode - store file for later upload
          await this.handleLocalMode(file)
        } else {
          // Immediate upload (for backward compatibility)
          await this.uploadFile(file)
        }
      } catch (error) {
        console.error('Error processing file:', error)
        this.error = 'Error processing file'
      }
    },

    validateImageFile(file) {
      const errors = []

      // Check file type
      if (!this.allowedTypes.includes(file.type)) {
        const allowedTypesStr = this.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')
        errors.push(`Invalid file type. Allowed: ${allowedTypesStr}`)
      }

      // Check file size
      if (file.size > this.maxSize) {
        const maxSizeMB = Math.round(this.maxSize / 1024 / 1024)
        errors.push(`File too large. Maximum size: ${maxSizeMB}MB`)
      }

      return {
        isValid: errors.length === 0,
        errors
      }
    },

    async handleLocalMode(file) {
      try {
        this.localImageUrl = URL.createObjectURL(file)
        this.pendingFile = file
        this.pendingRemoval = false // Reset removal flag

        console.log('Meta image local mode - file processed:', {
          fileName: file.name,
          fileSize: file.size,
          previewUrl: this.localImageUrl,
          entityType: this.entityType,
          entityId: this.entityId
        })

        this.$emit('meta-image-changed', {
          type: 'upload',
          file,
          url: this.localImageUrl,
          name: file.name,
          size: file.size,
          isLocal: true,
          success: true
        })
      } catch (error) {
        console.error('Error in handleLocalMode:', error)
        this.error = 'Error creating image preview'
      }
    },
    
    async uploadFile(file) {
      console.log('SimpleMetaImageUploader.uploadFile called:', {
        entityType: this.entityType,
        entityId: this.entityId,
        fileName: file?.name,
        fileSize: file?.size,
        fileType: file?.type
      })

      if (!this.entityId || this.entityId === 'temp-id') {
        this.error = 'Entity ID not provided'
        console.error('SimpleMetaImageUploader: No entity ID provided')
        return
      }

      this.isUploading = true
      this.uploadProgress = 0

      try {
        console.log('Calling imageService.uploadMetaImage...')
        const { default: imageService } = await import('@/services/image.service')

        const response = await imageService.uploadMetaImage(
          this.entityType,
          this.entityId,
          file,
          (progress) => {
            this.uploadProgress = progress
          }
        )

        console.log('Meta image upload successful:', response)

        this.localImageUrl = null
        this.pendingFile = null

        this.$emit('meta-image-uploaded', response.data)
        this.$emit('meta-image-changed', {
          type: 'upload',
          file: file,
          url: response.data?.url || response.url,
          name: file.name,
          size: file.size,
          isLocal: false,
          success: true
        })

        return response

      } catch (error) {
        console.error('Meta image upload failed:', error)
        console.error('Error response:', error.response?.data)
        this.error = error.message

        this.$emit('meta-image-changed', {
          type: 'error',
          file: file,
          error: error.message,
          success: false
        })

        throw error // Re-throw to be caught by processPendingOperations
      } finally {
        this.isUploading = false
        this.uploadProgress = 0
      }
    },
    
    removeImage() {
      console.log('SimpleMetaImageUploader: removeImage called', {
        isInLocalMode: this.isInLocalMode,
        entityId: this.entityId,
        currentImage: this.currentImage,
        currentImageUrl: this.currentImageUrl
      })

      // Clean up blob URL if it exists
      if (this.currentImageUrl && this.currentImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.currentImageUrl)
      }
      if (this.localImageUrl && this.localImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.localImageUrl)
      }

      // Always use local mode approach for consistency
      this.localImageUrl = null
      this.currentImageUrl = null
      this.pendingFile = null
      this.pendingRemoval = true

      console.log('Meta image marked for removal')
      this.$emit('meta-image-changed', {
        type: 'removal',
        originalImage: this.currentImage
      })
    },

    async deleteCurrentMetaImage() {
      try {
        console.log('deleteCurrentMetaImage called:', {
          entityId: this.entityId,
          entityType: this.entityType,
          currentImage: this.currentImage
        })

        const { default: imageService } = await import('@/services/image.service')
        await imageService.deleteMetaImage(this.entityType, this.entityId)

        // Clean up blob URL if it exists
        if (this.localImageUrl && this.localImageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(this.localImageUrl)
        }

        this.localImageUrl = null
        this.pendingFile = null
        this.pendingRemoval = false
        this.error = null

        this.$emit('meta-image-removed')
        this.$emit('meta-image-changed', {
          type: 'remove',
          success: true
        })

        console.log('Meta image deleted successfully')
      } catch (error) {
        console.error('Error deleting meta image:', error)
        this.error = 'Error removing image'

        this.$emit('meta-image-changed', {
          type: 'error',
          error: error.message,
          success: false
        })
      }
    },
    
    // Public methods for parent component
    getChangeState() {
      return {
        hasChanges: this.hasChanges,
        pendingRemoval: this.pendingRemoval,
        hasPendingFile: !!this.pendingFile,
        pendingFileName: this.pendingFile?.name || null
      }
    },

    resetLocalChanges() {
      console.log('Resetting meta image local changes')

      // Clean up blob URLs to prevent memory leaks
      if (this.localImageUrl && this.localImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.localImageUrl)
      }

      if (this.currentImageUrl && this.currentImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.currentImageUrl)
      }

      this.localImageUrl = null
      this.pendingFile = null
      this.pendingRemoval = false
      this.error = null
      this.isUploading = false
      this.uploadProgress = 0

      console.log('Meta image local changes reset complete')
    },

    async processPendingOperations() {
      console.log('MetaImage processPendingOperations called:', {
        pendingRemoval: this.pendingRemoval,
        hasPendingFile: !!this.pendingFile,
        hasCurrentImage: !!this.currentImage,
        entityId: this.entityId,
        entityType: this.entityType,
        pendingFileName: this.pendingFile?.name,
        pendingFileSize: this.pendingFile?.size
      })

      const results = {
        uploaded: false,
        removed: false,
        errors: []
      }

      try {
        // First process removal (if there's a current image)
        if (this.pendingRemoval && this.currentImage) {
          console.log('Processing pending meta image removal')
          try {
            await this.deleteCurrentMetaImage()
            results.removed = true
            console.log('Meta image removed successfully')
          } catch (error) {
            console.error('Error removing meta image:', error)
            results.errors.push(`Error removing meta image: ${error.message}`)
          }
        }

        // Then process upload of new file
        if (this.pendingFile && this.entityId && this.entityId !== 'temp-id') {
          console.log('Processing pending meta image upload')
          console.log('Pending file details:', {
            name: this.pendingFile.name,
            size: this.pendingFile.size,
            type: this.pendingFile.type
          })
          try {
            const response = await this.uploadFile(this.pendingFile)
            console.log('Meta image upload response:', response)
            results.uploaded = true
            console.log('Meta image uploaded successfully')
          } catch (error) {
            console.error('Error uploading meta image:', error)
            console.error('Error details:', error.response?.data || error.message)
            results.errors.push(`Error uploading meta image: ${error.message}`)
          }
        }

        // Clear pending operations only if no errors
        if (results.errors.length === 0) {
          console.log('Clearing pending meta image operations')
          this.resetLocalChanges()
        }

        return results
      } catch (error) {
        console.error('Unexpected error in meta image processPendingOperations:', error)
        results.errors.push(`Unexpected error: ${error.message}`)
        return results
      }
    },

    handleImageError() {
      console.error('Meta image load error for URL:', this.currentImageUrl)

      // If the image fails to load and it's not a blob URL, clear it
      if (this.currentImageUrl && !this.currentImageUrl.startsWith('blob:')) {
        console.log('Clearing broken meta image URL')
        this.currentImageUrl = null

        // Emit change to parent component
        this.$emit('meta-image-changed', {
          type: 'error',
          originalImage: this.currentImageUrl
        })
      }

      this.error = 'Failed to load image'
    },

    openModal() {
      this.showModal = true
    },

    closeModal() {
      this.showModal = false
    },

    // Methods for compatibility with ProductEdit.vue
    hasChanges() {
      const changes = this.hasLocalChanges
      console.log('SimpleMetaImageUploader hasChanges:', changes, {
        pendingFile: !!this.pendingFile,
        pendingRemoval: this.pendingRemoval,
        localImageUrl: !!this.localImageUrl
      })
      return changes
    },

    async processChanges() {
      console.log('SimpleMetaImageUploader processChanges called', {
        pendingFile: !!this.pendingFile,
        pendingRemoval: this.pendingRemoval,
        entityId: this.entityId
      })

      try {
        // Handle removal first
        if (this.pendingRemoval) {
          console.log('Processing meta image removal...')

          // If we have an entity ID, delete from server
          if (this.entityId && this.entityId !== 'temp-id') {
            try {
              const { default: imageService } = await import('@/services/image.service')
              await imageService.deleteMetaImage(this.entityType, this.entityId)
              console.log('Meta image deleted from server')

              // Also update the product to clear metaImage field
              if (this.entityType === 'product') {
                try {
                  const { productsService } = await import('@/admin/services/products.js')
                  await productsService.updateProduct(this.entityId, { metaImage: null })
                  console.log('Product metaImage field cleared in database')
                } catch (updateError) {
                  console.error('Error clearing metaImage field in product:', updateError)
                  // Don't fail the entire process for this error
                }
              }
            } catch (error) {
              console.error('Error deleting meta image from server:', error)
              // Don't fail the entire process for deletion errors
            }
          }

          // Reset local state
          this.currentImageUrl = null
          this.pendingRemoval = false
          this.pendingFile = null
          this.localImageUrl = null

          console.log('Meta image removal completed successfully')

          return {
            success: true,
            metaImage: null,
            errors: []
          }
        }

        // If there's no pending file, no changes to process
        if (!this.pendingFile) {
          console.log('No pending file, returning current state')
          return {
            success: true,
            metaImage: this.currentImageUrl,
            errors: []
          }
        }

        // If in create mode or no entity ID, return local data
        if (!this.entityId || this.entityId === 'temp-id') {
          console.log('In create mode, returning local data')
          return {
            success: true,
            metaImage: this.currentImageUrl,
            file: this.pendingFile,
            isLocal: true,
            errors: []
          }
        }

        // Upload to server
        try {
          console.log('Uploading meta image to server...')
          const { default: imageService } = await import('@/services/image.service')

          const response = await imageService.uploadMetaImage(
            this.entityType,
            this.entityId,
            this.pendingFile,
            (progress) => {
              console.log(`Meta image upload progress: ${progress}%`)
            }
          )

          // Clean up local blob URL
          if (this.currentImageUrl && this.currentImageUrl.startsWith('blob:')) {
            URL.revokeObjectURL(this.currentImageUrl)
          }

          // Extract server URL
          const serverUrl = response.data?.url || response.data?.fileUrl || response.url || response.fileUrl
          this.currentImageUrl = serverUrl
          this.pendingFile = null

          console.log('Meta image uploaded successfully:', serverUrl)

          return {
            success: true,
            metaImage: serverUrl,
            errors: []
          }

        } catch (error) {
          console.error('Error uploading meta image:', error)
          return {
            success: false,
            metaImage: null,
            errors: [`Failed to upload meta image: ${error.message}`]
          }
        }

      } catch (error) {
        console.error('Error processing meta image changes:', error)
        return {
          success: false,
          metaImage: null,
          errors: [error.message]
        }
      }
    },

    // Alias for compatibility with ProductEdit.vue
    async processPendingOperations() {
      console.log('SimpleMetaImageUploader processPendingOperations called (alias for processChanges)')
      return await this.processChanges()
    },

    updateCurrentImage(newImageUrl) {
      console.log('SimpleMetaImageUploader updateCurrentImage called with:', newImageUrl);

      try {
        // Clean up old blob URL if it exists
        if (this.currentImageUrl && this.currentImageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(this.currentImageUrl);
        }

        // Clean up local blob URL if it exists
        if (this.localImageUrl && this.localImageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(this.localImageUrl);
        }

        // Update current image URL (can be null for removal)
        this.currentImageUrl = newImageUrl;

        // Reset local changes since we're syncing with server
        this.pendingFile = null;
        this.pendingRemoval = false;
        this.localImageUrl = null;
        this.error = null;

        console.log('SimpleMetaImageUploader current image updated successfully to:', this.currentImageUrl);
      } catch (error) {
        console.error('Error updating current image:', error);
      }
    },

    reset() {
      try {
        // Clean up blob URLs
        if (this.currentImageUrl && this.currentImageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(this.currentImageUrl)
        }

        this.currentImageUrl = null
        this.pendingFile = null
        this.error = null
        this.showModal = false
        this.isUploading = false
        this.uploadProgress = 0
      } catch (error) {
        console.error('Error resetting meta image uploader:', error)
      }
    }
  },
  
  watch: {
    currentImage(newValue) {
      this.currentImageUrl = newValue
    }
  },
  
  beforeUnmount() {
    // Clean up blob URLs
    if (this.currentImageUrl && this.currentImageUrl.startsWith('blob:')) {
      try {
        URL.revokeObjectURL(this.currentImageUrl)
      } catch (error) {
        console.error('Error cleaning up blob URL:', error)
      }
    }
  }
}
</script>

<style scoped>
.simple-meta-image-uploader {
  background: white;
  border-radius: 8px;
}

.meta-uploader-content {
  padding: 1rem;
}

/* Current Image Section */
.current-image-section {
  margin-bottom: 1.5rem;
}

.image-preview {
  position: relative;
  width: 100%;
  max-width: 400px;
  aspect-ratio: 1.91/1; /* 1200x630 ratio */
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  margin-bottom: 0.5rem;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.overlay-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.overlay-btn:hover {
  background: white;
  transform: scale(1.1);
}

.overlay-btn--view:hover {
  background: #3b82f6;
  color: white;
}

.overlay-btn--delete:hover {
  background: #ef4444;
  color: white;
}

.image-info {
  text-align: center;
}

.image-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Upload Zone */
.upload-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;
  position: relative;
  margin-bottom: 1.5rem;
}

.upload-zone:hover {
  border-color: #10b981;
  background: #f0fdf4;
}

.upload-zone--dragover {
  border-color: #10b981;
  background: #ecfdf5;
  transform: scale(1.02);
}

.upload-zone--uploading {
  pointer-events: none;
  opacity: 0.7;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 2.5rem;
  color: #9ca3af;
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
  color: #10b981;
  transform: scale(1.1);
}

.upload-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.upload-text p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.upload-hint {
  font-size: 0.75rem !important;
  color: #9ca3af !important;
  font-style: italic;
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* Error Message */
.error-message {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Social Preview */
.social-preview {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.preview-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.preview-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-image {
  aspect-ratio: 1.91/1;
  overflow: hidden;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-content {
  padding: 1rem;
}

.preview-text-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.preview-text-description {
  font-size: 0.875rem;
  color: #4a5568;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-text-url {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Image Modal */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.modal-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: all 0.2s ease;
  z-index: 10;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
  .meta-uploader-content {
    padding: 0.75rem;
  }

  .upload-zone {
    padding: 1.5rem 1rem;
  }

  .upload-icon {
    font-size: 2rem;
  }

  .preview-card {
    margin: 0 -0.75rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}
</style>
