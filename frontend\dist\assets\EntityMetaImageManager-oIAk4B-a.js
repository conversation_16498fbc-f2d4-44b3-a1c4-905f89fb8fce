import{i as c}from"./image.service-DOD4lHqw.js";import{_ as p,c as n,o as d,a as s,k as m,d as g,t as o,m as u,n as h,L as f}from"./index-L-hJxM_5.js";const v={name:"EntityMetaImageManager",props:{entityType:{type:String,required:!0,validator:t=>["product","category","company"].includes(t)},entityId:{type:String,default:null},currentImage:{type:String,default:null},imageAlt:{type:String,default:"Мета-зображення"},socialPreviewTitle:{type:String,default:"Заголовок сторінки"},socialPreviewDescription:{type:String,default:"Опис сторінки для соціальних мереж"},socialPreviewUrl:{type:String,default:"https://example.com"},maxSize:{type:Number,default:5*1024*1024},allowedTypes:{type:Array,default:()=>["image/jpeg","image/png","image/webp"]},localMode:{type:Boolean,default:!1}},emits:["meta-image-uploaded","meta-image-removed","meta-image-changed"],data(){return{isDragOver:!1,uploading:!1,uploadProgress:0,error:null,showImageViewer:!1,localImageUrl:null,pendingFile:null}},computed:{currentImageUrl(){return this.localImageUrl||this.currentImage},uploadHint(){const t=Math.round(this.maxSize/1024/1024),e=this.allowedTypes.map(a=>a.split("/")[1].toUpperCase()).join(", ");return`Максимальний розмір: ${t}MB. Формати: ${e}`}},methods:{triggerFileInput(){this.$refs.fileInput.click()},handleFileSelect(t){const e=t.target.files[0];e&&this.processFile(e)},handleDrop(t){t.preventDefault(),this.isDragOver=!1;const e=t.dataTransfer.files;e.length>0&&this.processFile(e[0])},async processFile(t){this.error=null;const e=c.validateImageFile(t,{maxSize:this.maxSize,allowedTypes:this.allowedTypes});if(!e.isValid){this.error=e.errors.join(", ");return}this.localMode?await this.handleLocalMode(t):await this.uploadFile(t)},async handleLocalMode(t){try{this.localImageUrl=await c.createImagePreview(t),this.pendingFile=t,this.$emit("meta-image-changed",{file:t,previewUrl:this.localImageUrl,isLocal:!0})}catch{this.error="Помилка створення превью зображення"}},async uploadFile(t){if(!this.entityId){this.error="ID сутності не вказано";return}this.uploading=!0,this.uploadProgress=0;try{const e=await c.uploadMetaImage(this.entityType,this.entityId,t,a=>{this.uploadProgress=a});this.localImageUrl=null,this.pendingFile=null,this.$emit("meta-image-uploaded",e.data)}catch(e){this.error=e.message}finally{this.uploading=!1,this.uploadProgress=0}},removeImage(){this.localMode?(this.localImageUrl=null,this.pendingFile=null,this.$emit("meta-image-changed",null)):this.$emit("meta-image-removed")},openImageViewer(){this.currentImageUrl&&(this.showImageViewer=!0)},closeImageViewer(){this.showImageViewer=!1},async uploadPendingFile(){this.pendingFile&&this.entityId&&await this.uploadFile(this.pendingFile)},resetLocalChanges(){this.localImageUrl=null,this.pendingFile=null,this.error=null}}},y={class:"entity-meta-image-manager"},I={class:"meta-image-container"},w={key:0,class:"current-meta-image"},_={class:"meta-image-preview"},b=["src","alt"],F={class:"meta-image-overlay"},x=["disabled"],k={class:"social-preview mt-3"},U={class:"social-card"},S=["src"],V={class:"social-card-content"},D={class:"social-card-title"},P={class:"social-card-description"},M={class:"social-card-url"},C={class:"upload-content"},T={class:"upload-hint"},z={key:2,class:"upload-progress"},B={class:"progress"},A={class:"progress-text"},L={key:3,class:"alert alert-danger mt-2"},O={class:"modal-content"},E={class:"modal-header"},N={class:"modal-title"},j={class:"modal-body text-center"},H=["src","alt"],q={class:"mt-3"},G={class:"social-card mx-auto",style:{"max-width":"500px"}},J=["src"],K={class:"social-card-content"},Q={class:"social-card-title"},R={class:"social-card-description"},W={class:"social-card-url"};function X(t,e,a,Y,r,l){return d(),n("div",y,[e[20]||(e[20]=s("div",{class:"meta-image-header"},[s("h6",{class:"mb-2"},[s("i",{class:"fas fa-share-alt me-2"}),g(" Мета-зображення для соціальних мереж ")]),s("p",{class:"text-muted small mb-3"}," Це зображення буде відображатися при поширенні посилання в соціальних мережах. Рекомендований розмір: 1200x630 пікселів. ")],-1)),s("div",I,[l.currentImageUrl?(d(),n("div",w,[s("div",_,[s("img",{src:l.currentImageUrl,alt:a.imageAlt,class:"meta-image",onClick:e[0]||(e[0]=(...i)=>l.openImageViewer&&l.openImageViewer(...i))},null,8,b),s("div",F,[s("button",{type:"button",class:"btn btn-sm btn-danger",onClick:e[1]||(e[1]=(...i)=>l.removeImage&&l.removeImage(...i)),disabled:r.uploading,title:"Видалити мета-зображення"},e[11]||(e[11]=[s("i",{class:"fas fa-trash"},null,-1)]),8,x),s("button",{type:"button",class:"btn btn-sm btn-primary",onClick:e[2]||(e[2]=(...i)=>l.openImageViewer&&l.openImageViewer(...i)),title:"Переглянути в повному розмірі"},e[12]||(e[12]=[s("i",{class:"fas fa-eye"},null,-1)]))])]),s("div",k,[s("div",U,[s("img",{src:l.currentImageUrl,class:"social-card-image"},null,8,S),s("div",V,[s("h6",D,o(a.socialPreviewTitle),1),s("p",P,o(a.socialPreviewDescription),1),s("small",M,o(a.socialPreviewUrl),1)])])])])):(d(),n("div",{key:1,class:h(["meta-upload-zone",{"drag-over":r.isDragOver}]),onDrop:e[3]||(e[3]=(...i)=>l.handleDrop&&l.handleDrop(...i)),onDragover:e[4]||(e[4]=u(i=>r.isDragOver=!0,["prevent"])),onDragleave:e[5]||(e[5]=i=>r.isDragOver=!1),onClick:e[6]||(e[6]=(...i)=>l.triggerFileInput&&l.triggerFileInput(...i))},[s("div",C,[e[15]||(e[15]=s("i",{class:"fab fa-facebook-f upload-icon"},null,-1)),e[16]||(e[16]=s("i",{class:"fab fa-twitter upload-icon"},null,-1)),e[17]||(e[17]=s("i",{class:"fab fa-linkedin-in upload-icon"},null,-1)),e[18]||(e[18]=s("p",{class:"upload-text"}," Додати мета-зображення для соціальних мереж ",-1)),s("p",T,[e[13]||(e[13]=g(" Рекомендований розмір: 1200x630px")),e[14]||(e[14]=s("br",null,null,-1)),g(" "+o(l.uploadHint),1)])])],34)),r.uploading?(d(),n("div",z,[s("div",B,[s("div",{class:"progress-bar bg-info",style:f({width:r.uploadProgress+"%"})},null,4)]),s("p",A,"Завантаження мета-зображення: "+o(r.uploadProgress)+"%",1)])):m("",!0),r.error?(d(),n("div",L,o(r.error),1)):m("",!0)]),s("input",{ref:"fileInput",type:"file",accept:"image/*",style:{display:"none"},onChange:e[7]||(e[7]=(...i)=>l.handleFileSelect&&l.handleFileSelect(...i))},null,544),r.showImageViewer?(d(),n("div",{key:0,class:"modal fade show d-block",onClick:e[10]||(e[10]=(...i)=>l.closeImageViewer&&l.closeImageViewer(...i))},[s("div",{class:"modal-dialog modal-lg",onClick:e[9]||(e[9]=u(()=>{},["stop"]))},[s("div",O,[s("div",E,[s("h5",N,o(a.imageAlt),1),s("button",{type:"button",class:"btn-close",onClick:e[8]||(e[8]=(...i)=>l.closeImageViewer&&l.closeImageViewer(...i))})]),s("div",j,[s("img",{src:l.currentImageUrl,alt:a.imageAlt,class:"img-fluid"},null,8,H),s("div",q,[e[19]||(e[19]=s("h6",null,"Превью в соціальних мережах:",-1)),s("div",G,[s("img",{src:l.currentImageUrl,class:"social-card-image"},null,8,J),s("div",K,[s("h6",Q,o(a.socialPreviewTitle),1),s("p",R,o(a.socialPreviewDescription),1),s("small",W,o(a.socialPreviewUrl),1)])])])])])])])):m("",!0)])}const ee=p(v,[["render",X],["__scopeId","data-v-536fbff9"]]);export{ee as E};
