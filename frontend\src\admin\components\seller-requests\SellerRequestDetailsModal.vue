<template>
  <div class="admin-modal" :class="{ 'admin-modal-active': isOpen }">
    <div class="admin-modal-background" @click="$emit('close')"></div>
    <div class="admin-modal-content">
      <div class="admin-modal-header">
        <h3 class="admin-modal-title">Seller Application Details</h3>
        <button class="admin-modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="admin-modal-body" v-if="request">
        <!-- Application Status -->
        <div class="notification" :class="getStatusNotificationClass(request.status)">
          <p class="has-text-centered">
            <strong>Status: {{ formatStatus(request.status) }}</strong>
            <span v-if="request.status === 'rejected' && request.rejectionReason">
              <br>Reason: {{ request.rejectionReason }}
            </span>
          </p>
        </div>
        
        <!-- Applicant Information -->
        <div class="box">
          <h3 class="subtitle is-5">Applicant Information</h3>
          <div class="columns">
            <div class="column is-6">
              <p><strong>Name:</strong> {{ request.userName }}</p>
              <p><strong>Email:</strong> {{ request.userEmail }}</p>
              <p><strong>Phone:</strong> {{ request.phone || 'N/A' }}</p>
            </div>
            <div class="column is-6">
              <p><strong>User ID:</strong> {{ request.userId }}</p>
              <p><strong>Application Date:</strong> {{ formatDate(request.createdAt) }}</p>
              <p v-if="request.status !== 'pending'">
                <strong>Decision Date:</strong> {{ formatDate(request.updatedAt) }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- Store Information -->
        <div class="box">
          <h3 class="subtitle is-5">Store Information</h3>
          <div class="columns">
            <div class="column is-6">
              <p><strong>Store Name:</strong> {{ request.storeName }}</p>
              <p><strong>Store Description:</strong> {{ request.storeDescription || 'N/A' }}</p>
              <p><strong>Store Type:</strong> {{ request.storeType || 'N/A' }}</p>
            </div>
            <div class="column is-6">
              <p><strong>Business Address:</strong> {{ formatAddress(request.businessAddress) }}</p>
              <p><strong>Business Registration:</strong> {{ request.businessRegistration || 'N/A' }}</p>
              <p><strong>Tax ID:</strong> {{ request.taxId || 'N/A' }}</p>
            </div>
          </div>
        </div>
        
        <!-- Additional Information -->
        <div class="box" v-if="request.additionalInfo">
          <h3 class="subtitle is-5">Additional Information</h3>
          <p>{{ request.additionalInfo }}</p>
        </div>
        
        <!-- Rejection Form -->
        <div class="box" v-if="request.status === 'pending' && isRejecting">
          <h3 class="subtitle is-5">Rejection Reason</h3>
          <div class="field">
            <div class="control">
              <textarea 
                class="textarea" 
                placeholder="Please provide a reason for rejection" 
                v-model="rejectionReason"
                rows="3"
                required></textarea>
            </div>
          </div>
          <div class="field is-grouped">
            <div class="control">
              <button 
                class="button is-danger" 
                @click="confirmReject"
                :disabled="!rejectionReason.trim()">
                Confirm Rejection
              </button>
            </div>
            <div class="control">
              <button class="button is-light" @click="isRejecting = false">
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="admin-modal-footer" v-if="request && request.status === 'pending'">
        <button
          class="admin-btn admin-btn-success"
          @click="$emit('approve', request.id)"
          v-if="!isRejecting">
          <i class="fas fa-check"></i>
          Approve Application
        </button>
        <button
          class="admin-btn admin-btn-danger"
          @click="isRejecting = true"
          v-if="!isRejecting">
          <i class="fas fa-times"></i>
          Reject Application
        </button>
        <button class="admin-btn admin-btn-secondary" @click="$emit('close')">Close</button>
      </div>
      <div class="admin-modal-footer" v-else>
        <button class="admin-btn admin-btn-secondary" @click="$emit('close')">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  request: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'approve', 'reject']);

// Rejection state
const isRejecting = ref(false);
const rejectionReason = ref('');

// Format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Format status
const formatStatus = (status) => {
  if (!status) return 'Unknown';
  
  // Convert snake_case to Title Case
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Format address
const formatAddress = (address) => {
  if (!address) return 'N/A';
  
  return `${address.street}, ${address.city}, ${address.state} ${address.zipCode}, ${address.country}`;
};

// Get status notification class
const getStatusNotificationClass = (status) => {
  switch (status) {
    case 'pending':
      return 'is-warning';
    case 'approved':
      return 'is-success';
    case 'rejected':
      return 'is-danger';
    default:
      return 'is-light';
  }
};

// Confirm rejection
const confirmReject = () => {
  if (rejectionReason.value.trim() && props.request) {
    emit('reject', props.request.id, rejectionReason.value);
  }
};

// Reset form when modal closes
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    isRejecting.value = false;
    rejectionReason.value = '';
  }
});
</script>

<style scoped>
.modal-card {
  width: 90%;
  max-width: 800px;
}

.subtitle {
  margin-bottom: 1rem;
}

.box {
  margin-bottom: 1.5rem;
}

.box:last-child {
  margin-bottom: 0;
}

.notification {
  margin-bottom: 1.5rem;
}
</style>
