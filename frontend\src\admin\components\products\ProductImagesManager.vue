<template>
  <div class="product-images-manager">
    <div class="admin-section-header">
      <h4 class="admin-section-title">
        <i class="fas fa-images"></i>
        Product Images
      </h4>
      <p class="admin-section-description">
        Upload and manage product images. You can set one image as the main image for the product.
      </p>
    </div>

    <!-- Upload Section -->
    <div class="upload-section mb-5">
      <div
        class="drop-zone"
        :class="{ 'drop-zone--dragover': isDragOver, 'drop-zone--disabled': uploading }"
        @drop="handleDrop"
        @dragover.prevent="handleDragOver"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
      >
        <input 
          ref="fileInput"
          type="file" 
          accept="image/*,.jfif" 
          multiple
          @change="handleFileSelect"
          :disabled="uploading"
          style="display: none;"
        />
        
        <div class="drop-zone-content">
          <i class="fas fa-cloud-upload-alt upload-icon" v-if="!uploading"></i>
          <i class="fas fa-spinner fa-spin upload-icon" v-else></i>
          <p class="upload-text">
            <strong v-if="!uploading">Drop images here or click to browse</strong>
            <strong v-else>Uploading images...</strong>
          </p>
          <p class="upload-subtext">
            Supports: JPG, PNG, GIF, WebP, JFIF (max 5MB each)
          </p>
        </div>
      </div>
    </div>

    <!-- Upload Progress -->
    <div v-if="uploading" class="upload-progress mb-5">
      <div class="progress-info">
        <span class="progress-label">
          <i class="fas fa-spinner fa-spin"></i>
          Uploading {{ uploadProgress.current }} of {{ uploadProgress.total }} images...
        </span>
        <span class="progress-percentage">
          {{ Math.round((uploadProgress.current / uploadProgress.total) * 100) }}%
        </span>
      </div>
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: (uploadProgress.current / uploadProgress.total) * 100 + '%' }"
        ></div>
      </div>
    </div>

    <!-- Current Images -->
    <div v-if="images.length > 0" class="current-images mb-5">
      <label class="section-label">Current Images ({{ images.length }})</label>
      <div class="images-grid">
        <div v-for="image in images" :key="image.id" class="image-item">
          <div class="image-card">
            <div class="image-wrapper">
              <img 
                :src="image.imageUrl || image.url" 
                :alt="image.altText || 'Product image'"
                class="image-preview"
                @error="handleImageError"
                @click="openImageModal(image)"
              />
              <div class="image-overlay">
                <button
                  class="admin-btn admin-btn-sm admin-btn-secondary"
                  @click="openImageModal(image)"
                  title="View full size"
                >
                  <i class="fas fa-search-plus"></i>
                </button>
                <button
                  class="admin-btn admin-btn-sm admin-btn-info"
                  @click="moveImageUp(image.id)"
                  :disabled="isFirstImage(image)"
                  title="Move up"
                >
                  <i class="fas fa-arrow-up"></i>
                </button>
                <button
                  class="admin-btn admin-btn-sm admin-btn-info"
                  @click="moveImageDown(image.id)"
                  :disabled="isLastImage(image)"
                  title="Move down"
                >
                  <i class="fas fa-arrow-down"></i>
                </button>
                <button
                  class="admin-btn admin-btn-sm admin-btn-danger"
                  @click="confirmDeleteImage(image)"
                  title="Delete image"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
              <div class="order-badge">
                <i class="fas fa-sort-numeric-up"></i>
                {{ image.order + 1 }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Images -->
    <div v-if="pendingImages.length > 0" class="pending-images mb-5">
      <label class="section-label">Images to Upload ({{ pendingImages.length }})</label>
      <div class="notification is-warning is-light">
        <span class="icon-text">
          <span class="icon">
            <i class="fas fa-info-circle"></i>
          </span>
          <span>These images will be uploaded when you save the product.</span>
        </span>
      </div>
      
      <div class="images-grid">
        <div v-for="image in pendingImages" :key="image.id" class="image-item">
          <div class="image-card">
            <div class="image-wrapper">
              <img 
                :src="image.preview" 
                :alt="image.name"
                class="image-preview"
              />
              <div class="image-overlay">
                <button
                  class="admin-btn admin-btn-sm admin-btn-info"
                  @click="movePendingImageUp(image.id)"
                  :disabled="isFirstPendingImage(image)"
                  title="Move up"
                >
                  <i class="fas fa-arrow-up"></i>
                </button>
                <button
                  class="admin-btn admin-btn-sm admin-btn-info"
                  @click="movePendingImageDown(image.id)"
                  :disabled="isLastPendingImage(image)"
                  title="Move down"
                >
                  <i class="fas fa-arrow-down"></i>
                </button>
                <button
                  class="admin-btn admin-btn-sm admin-btn-danger"
                  @click="removePendingImage(image.id)"
                  title="Remove image"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
              <div class="order-badge">
                <i class="fas fa-sort-numeric-up"></i>
                {{ getPendingImageOrder(image) + 1 }}
              </div>
            </div>
            <div class="image-info">
              <span class="image-name">{{ image.name }}</span>
              <span class="image-size">{{ formatFileSize(image.size) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="images.length === 0 && pendingImages.length === 0" class="empty-state">
      <i class="fas fa-images empty-icon"></i>
      <p class="empty-text">No images uploaded</p>
      <p class="empty-subtext">Upload images to showcase your product</p>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="modal is-active">
      <div class="modal-background" @click="closeImageModal"></div>
      <div class="modal-content">
        <p class="image">
          <img :src="modalImageUrl" :alt="modalImageTitle" />
        </p>
      </div>
      <button class="modal-close is-large" @click="closeImageModal"></button>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal is-active">
      <div class="modal-background" @click="cancelDelete"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Confirm Delete</p>
          <button class="delete" @click="cancelDelete"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete this image? This action cannot be undone.</p>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" @click="deleteImage" :disabled="isDeleting">
            <span v-if="!isDeleting">Delete</span>
            <span v-else>
              <i class="fas fa-spinner fa-spin"></i>
              Deleting...
            </span>
          </button>
          <button class="button" @click="cancelDelete">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import api from '@/services/api';
import imageService from '@/services/image.service';

// Props
const props = defineProps({
  productId: {
    type: String,
    default: null
  },
  images: {
    type: Array,
    default: () => []
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'images-updated',
  'main-image-changed',
  'image-uploaded',
  'image-deleted',
  'pending-images-changed',
  'images-reordered'
]);

// State
const pendingImages = ref([]);
const uploading = ref(false);
const isDeleting = ref(false);
const uploadProgress = ref({ current: 0, total: 0 });
const isDragOver = ref(false);
const showImageModal = ref(false);
const showDeleteModal = ref(false);
const modalImageUrl = ref('');
const modalImageTitle = ref('');
const imageToDelete = ref(null);
const fileInput = ref(null);
const selectedFiles = ref([]);

// Methods
const triggerFileInput = () => {
  if (!uploading.value) {
    fileInput.value?.click();
  }
};

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files);
  processFiles(files);
  event.target.value = '';
};

const handleDrop = (event) => {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = false;

  if (uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files).filter(file => 
    file.type.startsWith('image/')
  );
  
  processFiles(files);
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = true;
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const processFiles = async (files) => {
  if (files.length === 0) return;

  // Validate files
  const validFiles = files.filter(file => {
    // Check file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert(`File ${file.name} is too large. Maximum size is 5MB.`);
      return false;
    }
    
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/jfif'];
    if (!allowedTypes.includes(file.type)) {
      alert(`File ${file.name} has invalid type. Please select a valid image file.`);
      return false;
    }
    
    return true;
  });

  if (validFiles.length === 0) return;

  selectedFiles.value = validFiles;

  // Create previews for valid files
  validFiles.forEach((file, index) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      const imagePreview = {
        id: `temp-${Date.now()}-${Math.random()}-${index}`,
        file: file,
        name: file.name,
        preview: e.target.result,
        size: file.size,
        isTemp: true,
        isMain: pendingImages.value.length === 0 && props.images.length === 0
      };

      pendingImages.value.push(imagePreview);

      await nextTick();
      emit('pending-images-changed', pendingImages.value);
    };
    reader.readAsDataURL(file);
  });
};

// Методи для переміщення існуючих зображень
const moveImageUp = async (imageId) => {
  const currentIndex = props.images.findIndex(img => img.id === imageId);
  if (currentIndex > 0) {
    // Swap with previous image
    const newImages = [...props.images];
    [newImages[currentIndex], newImages[currentIndex - 1]] = [newImages[currentIndex - 1], newImages[currentIndex]];

    // Update order values
    newImages.forEach((img, index) => {
      img.order = index;
    });

    // Save order changes to server if not in create mode
    if (!props.isCreate && props.productId) {
      try {
        await Promise.all(newImages.map(img =>
          imageService.updateProductImageOrder(props.productId, img.id, img.order)
        ));
        console.log('Image order updated successfully');
      } catch (error) {
        console.error('Error updating image order:', error);
      }
    }

    emit('images-reordered', newImages);
  }
};

const moveImageDown = async (imageId) => {
  const currentIndex = props.images.findIndex(img => img.id === imageId);
  if (currentIndex < props.images.length - 1) {
    // Swap with next image
    const newImages = [...props.images];
    [newImages[currentIndex], newImages[currentIndex + 1]] = [newImages[currentIndex + 1], newImages[currentIndex]];

    // Update order values
    newImages.forEach((img, index) => {
      img.order = index;
    });

    // Save order changes to server if not in create mode
    if (!props.isCreate && props.productId) {
      try {
        await Promise.all(newImages.map(img =>
          imageService.updateProductImageOrder(props.productId, img.id, img.order)
        ));
        console.log('Image order updated successfully');
      } catch (error) {
        console.error('Error updating image order:', error);
      }
    }

    emit('images-reordered', newImages);
  }
};

const isFirstImage = (image) => {
  return props.images.findIndex(img => img.id === image.id) === 0;
};

const isLastImage = (image) => {
  return props.images.findIndex(img => img.id === image.id) === props.images.length - 1;
};

// Методи для переміщення pending зображень
const movePendingImageUp = (imageId) => {
  const currentIndex = pendingImages.value.findIndex(img => img.id === imageId);
  if (currentIndex > 0) {
    [pendingImages.value[currentIndex], pendingImages.value[currentIndex - 1]] =
    [pendingImages.value[currentIndex - 1], pendingImages.value[currentIndex]];
    emit('pending-images-changed', pendingImages.value);
  }
};

const movePendingImageDown = (imageId) => {
  const currentIndex = pendingImages.value.findIndex(img => img.id === imageId);
  if (currentIndex < pendingImages.value.length - 1) {
    [pendingImages.value[currentIndex], pendingImages.value[currentIndex + 1]] =
    [pendingImages.value[currentIndex + 1], pendingImages.value[currentIndex]];
    emit('pending-images-changed', pendingImages.value);
  }
};

const isFirstPendingImage = (image) => {
  return pendingImages.value.findIndex(img => img.id === image.id) === 0;
};

const isLastPendingImage = (image) => {
  return pendingImages.value.findIndex(img => img.id === image.id) === pendingImages.value.length - 1;
};

const getPendingImageOrder = (image) => {
  return pendingImages.value.findIndex(img => img.id === image.id);
};

const confirmDeleteImage = (image) => {
  imageToDelete.value = image;
  showDeleteModal.value = true;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
  imageToDelete.value = null;
};

const deleteImage = async () => {
  if (!imageToDelete.value || !props.productId) return;

  isDeleting.value = true;

  try {
    // Use the specific product image deletion method
    const response = await imageService.deleteProductImage(imageToDelete.value.id);

    console.log('Product image deleted successfully:', response);
    emit('image-deleted', imageToDelete.value.id);
    emit('images-updated');
    showDeleteModal.value = false;
    imageToDelete.value = null;
  } catch (err) {
    console.error('Error deleting product image:', err);
    alert('Failed to delete image. Please try again.');
  } finally {
    isDeleting.value = false;
  }
};

const removePendingImage = (imageId) => {
  const index = pendingImages.value.findIndex(img => img.id === imageId);
  if (index !== -1) {
    pendingImages.value.splice(index, 1);
    emit('pending-images-changed', pendingImages.value);
  }
};

const openImageModal = (image) => {
  modalImageUrl.value = image.imageUrl || image.url || image.preview;
  modalImageTitle.value = image.altText || image.name || 'Product image';
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  modalImageUrl.value = '';
  modalImageTitle.value = '';
};

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.png';
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Upload pending images
const uploadPendingImages = async (productId) => {
  if (pendingImages.value.length === 0 || !productId) return;

  uploading.value = true;
  uploadProgress.value = { current: 0, total: pendingImages.value.length };

  try {
    for (const pendingImage of pendingImages.value) {
      if (!pendingImage.file) continue;

      // Use the new universal image service
      const response = await imageService.uploadImage(
        'product',
        productId,
        pendingImage.file,
        (progress) => {
          // Update individual image progress if needed
          console.log(`Upload progress for ${pendingImage.file.name}: ${progress}%`);
        }
      );

      // If this should be the main image, set it as main
      if (pendingImage.isMain && response.data && response.data.id) {
        await setAsMain(response.data.id);
      }

      uploadProgress.value.current++;
      emit('image-uploaded', response.data);
    }

    // Clear pending images after successful upload
    pendingImages.value = [];
    emit('pending-images-changed', pendingImages.value);
    emit('images-updated');

  } catch (error) {
    console.error('Error uploading images:', error);
    alert('Failed to upload some images. Please try again.');
  } finally {
    uploading.value = false;
    uploadProgress.value = { current: 0, total: 0 };
  }
};

// Expose methods for parent components
defineExpose({
  uploadPendingImages,
  getPendingImages: () => pendingImages.value,
  clearPendingImages: () => {
    pendingImages.value = [];
    emit('pending-images-changed', pendingImages.value);
  }
});
</script>

<style scoped>
.product-images-manager {
  width: 100%;
}

.admin-section-header {
  margin-bottom: var(--admin-space-lg);
}

.admin-section-title {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin: 0 0 var(--admin-space-xs) 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-section-description {
  margin: 0;
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
  line-height: 1.5;
}

.upload-section {
  margin-bottom: var(--admin-space-lg);
}

.drop-zone {
  border: 2px dashed var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--admin-transition-base);
  background: var(--admin-bg-secondary);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover:not(.drop-zone--disabled) {
  border-color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.05);
}

.drop-zone--dragover {
  border-color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.1);
}

.drop-zone--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-icon {
  font-size: 3rem;
  color: var(--admin-text-secondary);
  margin-bottom: var(--admin-space-md);
}

.upload-text {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.upload-subtext {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}

.upload-progress {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-sm);
}

.progress-label {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-primary);
}

.progress-percentage {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-primary);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--admin-bg-tertiary);
  border-radius: var(--admin-radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--admin-primary);
  transition: width var(--admin-transition-base);
}

.section-label {
  display: block;
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-md);
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--admin-space-md);
}

.image-item {
  position: relative;
}

.image-card {
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  background: var(--admin-bg-primary);
  transition: all var(--admin-transition-base);
}

.image-card:hover {
  border-color: var(--admin-border-medium);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-wrapper {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform var(--admin-transition-base);
}

.image-preview:hover {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-xs);
  opacity: 0;
  transition: opacity var(--admin-transition-base);
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.order-badge {
  position: absolute;
  top: var(--admin-space-xs);
  left: var(--admin-space-xs);
  background: var(--admin-info);
  color: white;
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  display: flex;
  align-items: center;
  gap: 4px;
}

.image-info {
  padding: var(--admin-space-sm);
  border-top: 1px solid var(--admin-border-light);
}

.image-name {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
  margin-bottom: 2px;
  word-break: break-all;
}

.image-size {
  display: block;
  font-size: var(--admin-text-xs);
  color: var(--admin-text-secondary);
}

.empty-state {
  text-align: center;
  padding: var(--admin-space-xl);
  color: var(--admin-text-secondary);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--admin-space-md);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-medium);
  margin-bottom: var(--admin-space-xs);
}

.empty-subtext {
  font-size: var(--admin-text-sm);
}

/* Button styles */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-xs);
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-base);
  text-decoration: none;
}

.admin-btn-sm {
  padding: 4px 8px;
  font-size: var(--admin-text-xs);
}

.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-primary:hover {
  background: #2563eb;
}

.admin-btn-secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  border-color: var(--admin-border-light);
}

.admin-btn-secondary:hover {
  background: var(--admin-bg-tertiary);
  border-color: var(--admin-border-medium);
}

.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
}

.admin-btn-danger:hover {
  background: #dc2626;
}

.admin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Notification styles */
.notification {
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  margin-bottom: var(--admin-space-md);
}

.notification.is-warning.is-light {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.icon-text {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  margin: 5% auto;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content img {
  max-width: 100%;
  max-height: 100%;
  border-radius: var(--admin-radius-md);
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:before {
  content: '×';
  font-size: 24px;
  line-height: 1;
}

.modal-card {
  position: relative;
  margin: 10% auto;
  background: white;
  border-radius: var(--admin-radius-md);
  max-width: 500px;
  width: 90%;
}

.modal-card-head {
  padding: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-card-title {
  margin: 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
}

.modal-card-body {
  padding: var(--admin-space-md);
}

.modal-card-foot {
  padding: var(--admin-space-md);
  border-top: 1px solid var(--admin-border-light);
  display: flex;
  gap: var(--admin-space-sm);
  justify-content: flex-end;
}

.delete {
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
}

.delete:before,
.delete:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 16px;
  background: var(--admin-text-secondary);
  transform: translate(-50%, -50%) rotate(45deg);
}

.delete:after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.button {
  padding: 8px 16px;
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-sm);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  cursor: pointer;
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-base);
}

.button.is-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.button:hover {
  background: var(--admin-bg-secondary);
}

.button.is-danger:hover {
  background: #dc2626;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
