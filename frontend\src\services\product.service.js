import apiClient from './api.service';

class ProductService {
  // Get all products with optional filtering and pagination
  async getAll(params = {}) {
    return await apiClient.get('/products', { params });
  }

  async getBySlug(slug) {
    return await apiClient.get(`/products/slug/${slug}`);
  }

  // Get product by ID
  async getById(id) {
    return await apiClient.get(`/products/${id}`);
  }

  async getTopProducts(params = {}) {
    // Мапимо параметри для API топ товарів
    const apiParams = {
      limit: params.pageSize || params.limit || 10,
      orderBy: params.orderBy || 'Sales',
      descending: params.descending !== false // за замовчуванням true
    };

    // Додаємо categorySlug якщо є
    if (params.categorySlug) {
      apiParams.categorySlug = params.categorySlug;
    }

    // Додаємо status якщо є
    if (params.status !== undefined) {
      apiParams.status = params.status;
    }

    return await apiClient.get('/products/top', { params: apiParams });
  }

  async getTopProductsByCategory(params = {}) {
    // Мапимо параметри для API топ товарів по категоріях
    const apiParams = {
      categorySlug: params.categorySlug,
      limit: params.pageSize || params.limit || 10,
      orderBy: params.orderBy || 'Sales',
      descending: params.descending !== false // за замовчуванням true
    };

    // Додаємо status якщо є
    if (params.status !== undefined) {
      apiParams.status = params.status;
    }

    return await apiClient.get('/products/top', { params: apiParams });
  }

  async getRecommendedProducts(params = {}) {
    // Мапимо параметри для API рекомендованих товарів
    const apiParams = {
      limit: params.pageSize || params.limit || 10
    };

    // Додаємо status якщо є
    if (params.status !== undefined) {
      apiParams.status = params.status;
    }

    return await apiClient.get('/products/recommended', { params: apiParams });
  }

  // Create new product
  async create(productData) {
    return await apiClient.post('/products', productData);
  }

  // Update existing product
  async update(id, productData) {
    return await apiClient.put(`/products/${id}`, productData);
  }

  // Delete product
  async delete(id) {
    return await apiClient.delete(`/products/${id}`);
  }

  // Upload product image
  async uploadImage(productId, formData) {
    return await apiClient.post(`/productimages/${productId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  // Delete product image
  async deleteImage(imageId) {
    return await apiClient.delete(`/productimages/${imageId}`);
  }

  // Get product statistics
  async getStats() {
    return await apiClient.get('/products/stats');
  }

  async getFilters(categorySlug) {
    return await apiClient.get(`/products/filters/${categorySlug}`)
  }

  // Search products
  async searchProducts(params = {}) {
    const apiParams = {
      filter: params.query, // Використовуємо filter замість query
      page: params.page || 1,
      pageSize: params.pageSize || 24,
      status: params.status || 1
    };

    return await apiClient.get('/products', { params: apiParams });
  }
}

export default new ProductService();
