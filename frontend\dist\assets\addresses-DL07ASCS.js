import{q as t}from"./index-L-hJxM_5.js";const n={async getAddresses(a={}){var s,e;try{return(await t.get("/api/admin/addresses",{params:a})).data}catch(r){throw console.error("Error fetching addresses:",r),new Error(((e=(s=r.response)==null?void 0:s.data)==null?void 0:e.message)||"Failed to load addresses")}},async getAddressById(a){var s,e;try{return(await t.get(`/api/admin/addresses/${a}`)).data}catch(r){throw console.error("Error fetching address:",r),new Error(((e=(s=r.response)==null?void 0:s.data)==null?void 0:e.message)||"Failed to load address details")}},async getAddressesByUserId(a,s={}){var e,r;try{return(await t.get(`/api/admin/addresses/user/${a}`,{params:s})).data}catch(d){throw console.error("Error fetching user addresses:",d),new Error(((r=(e=d.response)==null?void 0:e.data)==null?void 0:r.message)||"Failed to load user addresses")}},async createAddress(a){var s,e;try{return(await t.post("/api/admin/addresses",a)).data}catch(r){throw console.error("Error creating address:",r),new Error(((e=(s=r.response)==null?void 0:s.data)==null?void 0:e.message)||"Failed to create address")}},async updateAddress(a,s){var e,r;try{return(await t.put(`/api/admin/addresses/${a}`,s)).data}catch(d){throw console.error("Error updating address:",d),new Error(((r=(e=d.response)==null?void 0:e.data)==null?void 0:r.message)||"Failed to update address")}},async deleteAddress(a){var s,e;try{return(await t.delete(`/api/admin/addresses/${a}`)).data}catch(r){throw console.error("Error deleting address:",r),new Error(((e=(s=r.response)==null?void 0:s.data)==null?void 0:e.message)||"Failed to delete address")}},async getAddressStats(){var a,s;try{return(await t.get("/api/admin/addresses/stats")).data.data}catch(e){throw console.error("Error fetching address stats:",e),new Error(((s=(a=e.response)==null?void 0:a.data)==null?void 0:s.message)||"Failed to load address statistics")}}};export{n as a};
