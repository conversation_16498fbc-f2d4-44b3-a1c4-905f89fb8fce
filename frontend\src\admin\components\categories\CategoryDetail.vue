<template>
  <div class="category-detail">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Category Details</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/categories" class="button is-light">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Categories</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered py-6">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading category details...</p>
    </div>
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>
    <div v-else-if="!category.id" class="notification is-warning">
      <p>Category not found.</p>
      <router-link to="/admin/categories" class="button is-primary mt-4">
        Back to Categories
      </router-link>
    </div>
    <div v-else>
      <!-- Category Header -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="columns">
            <div class="column is-8">
              <h2 class="category-title">{{ category.name }}</h2>
              <p class="category-path">
                <span v-if="parentPath.length > 0">
                  <span 
                    v-for="(parent, index) in parentPath" 
                    :key="parent.id">
                    <router-link :to="`/admin/categories/${parent.id}`">
                      {{ parent.name }}
                    </router-link>
                    <span v-if="index < parentPath.length - 1" class="path-separator">
                      <i class="fas fa-chevron-right"></i>
                    </span>
                  </span>
                  <span class="path-separator">
                    <i class="fas fa-chevron-right"></i>
                  </span>
                  <span class="current-category">{{ category.name }}</span>
                </span>
                <span v-else class="root-category">
                  <i class="fas fa-folder"></i> Root Category
                </span>
              </p>
            </div>
            <div class="column is-4 has-text-right">
              <div class="buttons is-right">
                <router-link 
                  :to="`/admin/categories/${category.id}/edit`" 
                  class="button is-primary">
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                  <span>Edit</span>
                </router-link>
                <button 
                  class="button is-danger" 
                  @click="confirmDelete">
                  <span class="icon">
                    <i class="fas fa-trash"></i>
                  </span>
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="columns">
        <!-- Category Image and Info -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Category Image</p>
            </div>
            <div class="card-content">
              <div class="category-image">
                <img 
                  :src="category.image || 'https://via.placeholder.com/400?text=No+Image'" 
                  :alt="category.name"
                  @error="handleImageError">
              </div>
            </div>
          </div>

          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Category Information</p>
            </div>
            <div class="card-content">
              <div class="info-group">
                <h3 class="info-label">Status</h3>
                <p class="info-value">
                  <span 
                    class="tag" 
                    :class="category.isActive ? 'is-success' : 'is-danger'">
                    {{ category.isActive ? 'Active' : 'Inactive' }}
                  </span>
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Visibility</h3>
                <p class="info-value">
                  <span 
                    class="tag" 
                    :class="category.isVisible ? 'is-success' : 'is-warning'">
                    {{ category.isVisible ? 'Visible' : 'Hidden' }}
                  </span>
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Featured</h3>
                <p class="info-value">
                  <span 
                    class="tag" 
                    :class="category.isFeatured ? 'is-primary' : 'is-light'">
                    {{ category.isFeatured ? 'Featured' : 'Not Featured' }}
                  </span>
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Display Order</h3>
                <p class="info-value">{{ category.displayOrder || 0 }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Slug</h3>
                <p class="info-value">{{ category.slug || 'N/A' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Created</h3>
                <p class="info-value">{{ formatDate(category.createdAt) }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Last Updated</h3>
                <p class="info-value">{{ formatDate(category.updatedAt) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Category Content -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Description</p>
            </div>
            <div class="card-content">
              <div class="content">
                <p v-if="category.description">{{ category.description }}</p>
                <p v-else class="has-text-grey">No description provided</p>
              </div>
            </div>
          </div>

          <!-- SEO Information -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">SEO Information</p>
            </div>
            <div class="card-content">
              <div class="info-group">
                <h3 class="info-label">Meta Title</h3>
                <p class="info-value">{{ category.metaTitle || category.name || 'N/A' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Meta Description</h3>
                <p class="info-value">{{ category.metaDescription || 'N/A' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Meta Keywords</h3>
                <div v-if="category.metaKeywords" class="tags">
                  <span 
                    v-for="(keyword, index) in category.metaKeywords.split(',')" 
                    :key="index" 
                    class="tag is-info">
                    {{ keyword.trim() }}
                  </span>
                </div>
                <p v-else class="has-text-grey">No keywords provided</p>
              </div>
            </div>
          </div>

          <!-- Products in Category -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Products in Category</p>
              <div class="card-header-icon">
                <router-link 
                  :to="`/admin/products?categoryId=${category.id}`" 
                  class="button is-small is-primary is-outlined">
                  <span>View All</span>
                  <span class="icon is-small">
                    <i class="fas fa-arrow-right"></i>
                  </span>
                </router-link>
              </div>
            </div>
            <div class="card-content">
              <div v-if="loadingProducts" class="has-text-centered py-4">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
                <p class="mt-2">Loading products...</p>
              </div>
              <div v-else-if="!categoryProducts.length" class="has-text-centered py-4">
                <span class="icon is-large">
                  <i class="fas fa-box fa-2x"></i>
                </span>
                <p class="mt-2">No products in this category</p>
                <router-link 
                  :to="`/admin/products/create?categoryId=${category.id}`" 
                  class="button is-primary mt-4">
                  <span class="icon">
                    <i class="fas fa-plus"></i>
                  </span>
                  <span>Add Product to Category</span>
                </router-link>
              </div>
              <div v-else>
                <div class="table-container">
                  <table class="table is-fullwidth is-hoverable">
                    <thead>
                      <tr>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Price</th>
                        <th>Stock</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="product in categoryProducts" :key="product.id">
                        <td class="image-cell">
                          <figure class="image is-48x48">
                            <img 
                              :src="product.image || 'https://via.placeholder.com/48'" 
                              :alt="product.name"
                              @error="handleImageError">
                          </figure>
                        </td>
                        <td>{{ product.name }}</td>
                        <td>{{ formatCurrency(product.price) }}</td>
                        <td>{{ product.stock }}</td>
                        <td>
                          <span 
                            class="tag" 
                            :class="getStatusClass(product.status)">
                            {{ product.status }}
                          </span>
                        </td>
                        <td>
                          <div class="buttons are-small">
                            <router-link 
                              :to="`/admin/products/${product.id}`" 
                              class="button is-info" 
                              title="View">
                              <span class="icon is-small">
                                <i class="fas fa-eye"></i>
                              </span>
                            </router-link>
                            <router-link 
                              :to="`/admin/products/${product.id}/edit`" 
                              class="button is-primary" 
                              title="Edit">
                              <span class="icon is-small">
                                <i class="fas fa-edit"></i>
                              </span>
                            </router-link>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete Category"
      :message="`Are you sure you want to delete '${category.name}'? This may affect products in this category.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteCategory"
      @cancel="cancelDelete" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoriesService } from '@/admin/services/categories';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const loadingProducts = ref(false);
const error = ref(null);
const category = ref({});
const allCategories = ref([]);
const categoryProducts = ref([]);
const showDeleteModal = ref(false);

// Computed properties
const categoryId = computed(() => route.params.id);

const parentPath = computed(() => {
  if (!category.value.parentId) return [];
  
  const path = [];
  let currentParentId = category.value.parentId;
  
  while (currentParentId) {
    const parent = allCategories.value.find(c => c.id === currentParentId);
    if (parent) {
      path.unshift(parent);
      currentParentId = parent.parentId;
    } else {
      break;
    }
  }
  
  return path;
});

// Fetch category data
const fetchCategory = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const data = await categoriesService.getCategoryById(categoryId.value);
    category.value = data;
  } catch (err) {
    console.error('Error fetching category:', err);
    error.value = 'Failed to load category data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Fetch all categories for parent path
const fetchAllCategories = async () => {
  try {
    const response = await categoriesService.getCategories();
    if (response.categories) {
      allCategories.value = response.categories;
    }
  } catch (err) {
    console.error('Error fetching categories:', err);
  }
};

// Fetch products in category
const fetchCategoryProducts = async () => {
  loadingProducts.value = true;
  
  try {
    const response = await categoriesService.getCategoryProducts(categoryId.value, { limit: 5 });
    
    if (response.products) {
      categoryProducts.value = response.products;
    } else {
      categoryProducts.value = [];
    }
  } catch (err) {
    console.error('Error fetching category products:', err);
  } finally {
    loadingProducts.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Get status class
const getStatusClass = (status) => {
  if (!status) return 'is-light';
  
  switch (status.toLowerCase()) {
    case 'active':
      return 'is-success';
    case 'inactive':
      return 'is-warning';
    case 'draft':
      return 'is-info';
    default:
      return 'is-light';
  }
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/400?text=No+Image';
};

// Confirm delete
const confirmDelete = () => {
  showDeleteModal.value = true;
};

// Delete category
const deleteCategory = async () => {
  try {
    await categoriesService.deleteCategory(categoryId.value);
    router.push('/admin/categories');
  } catch (err) {
    console.error('Error deleting category:', err);
    error.value = 'Failed to delete category. Please try again.';
  } finally {
    showDeleteModal.value = false;
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
};

// Lifecycle hooks
onMounted(() => {
  fetchCategory();
  fetchAllCategories();
  fetchCategoryProducts();
});
</script>

<style scoped>
.category-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.category-path {
  font-size: 0.9rem;
  color: #7a7a7a;
}

.path-separator {
  margin: 0 0.25rem;
}

.current-category {
  font-weight: 500;
  color: #363636;
}

.root-category {
  font-weight: 500;
  color: #363636;
}

.category-image {
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 4px;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
}

.image-cell {
  width: 60px;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f5;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.button.is-primary.is-outlined {
  background-color: transparent;
  border-color: #ff7700;
  color: #ff7700;
}

.button.is-primary.is-outlined:hover {
  background-color: #ff7700;
  color: white;
}

.tag.is-primary {
  background-color: #ff7700;
}
</style>
