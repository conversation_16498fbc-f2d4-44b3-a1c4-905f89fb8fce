using Marketplace.Application.Queries.Company;
using Marketplace.Application.Queries.CompanySchedule;
using Marketplace.Application.Queries.Product;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Companies;

[ApiController]
[Route("api/companies")]
public class CompanyController : BasicApiController
{
    private readonly IMediator _mediator;

    public CompanyController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        // Використовуємо оптимізований запит для отримання затверджених компаній
        var query = new GetApprovedCompaniesQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize);

        var response = await _mediator.Send(query, cancellationToken);
        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetCompanyQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(response) : NotFound();
    }

    [HttpGet("slug/{slug}")]
    public async Task<IActionResult> GetBySlug([FromRoute] string slug, CancellationToken cancellationToken)
    {
        var query = new GetCompanyBySlugQuery(slug);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(response) : NotFound();
    }


    [HttpGet]
    [Route("{id}/schedule")]
    public async Task<IActionResult> GetCompanySchedule([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetCompanyScheduleByCompanyIdQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(response);
    }

    [HttpGet("{id}/products")]
    public async Task<IActionResult> GetProducts(
        [FromRoute] Guid id,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetProductsByCompanyQuery(
            id,
            filter,
            orderBy,
            descending,
            page,
            pageSize);

        var response = await _mediator.Send(query, cancellationToken);
        return Ok(ApiResponse<PaginatedResponse<ProductResponse>>.SuccessWithData(response));
    }

    [HttpGet("slug/{slug}/products")]
    public async Task<IActionResult> GetProductsBySlug(
        [FromRoute] string slug,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        // Спочатку отримуємо компанію за slug
        var companyQuery = new GetCompanyBySlugQuery(slug);
        var company = await _mediator.Send(companyQuery, cancellationToken);

        if (company == null)
            return NotFound();

        // Отримуємо продукти компанії
        var query = new GetProductsByCompanyQuery(
            company.Id,
            filter,
            orderBy,
            descending,
            page,
            pageSize);

        var response = await _mediator.Send(query, cancellationToken);
        return Ok(ApiResponse<PaginatedResponse<ProductResponse>>.SuccessWithData(response));
    }

    [HttpGet("{id}/profile")]
    public async Task<IActionResult> GetCompanyProfile([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetCompanyQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(response) : NotFound();
    }

    [HttpGet("slug/{slug}/profile")]
    public async Task<IActionResult> GetCompanyProfileBySlug([FromRoute] string slug, CancellationToken cancellationToken)
    {
        var query = new GetCompanyBySlugQuery(slug);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(response) : NotFound();
    }

    [HttpGet("{id}/stats")]
    public async Task<IActionResult> GetCompanyStats([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        // Для статистики компанії можемо використати існуючі запити
        // Отримуємо кількість товарів компанії
        var productsQuery = new GetProductsByCompanyQuery(id, null, null, false, 1, 1);
        var productsResponse = await _mediator.Send(productsQuery, cancellationToken);

        var stats = new
        {
            ApprovedProducts = productsResponse?.Total ?? 0,
            MemberSince = "1 січня 2023 року" // Можна додати реальну дату створення компанії
        };

        return Ok(stats);
    }

    [HttpGet("slug/{slug}/stats")]
    public async Task<IActionResult> GetCompanyStatsBySlug([FromRoute] string slug, CancellationToken cancellationToken)
    {
        // Спочатку отримуємо компанію за slug
        var companyQuery = new GetCompanyBySlugQuery(slug);
        var company = await _mediator.Send(companyQuery, cancellationToken);

        if (company == null)
            return NotFound();

        // Отримуємо статистику за ID
        return await GetCompanyStats(company.Id, cancellationToken);
    }


}
