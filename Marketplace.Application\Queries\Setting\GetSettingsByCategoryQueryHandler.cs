using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Queries.Setting;

public class GetSettingsByCategoryQueryHandler : IRequestHandler<GetSettingsByCategoryQuery, Dictionary<string, string>>
{
    private readonly ISettingRepository _settingRepository;

    public GetSettingsByCategoryQueryHandler(ISettingRepository settingRepository)
    {
        _settingRepository = settingRepository;
    }

    public async Task<Dictionary<string, string>> Handle(GetSettingsByCategoryQuery request, CancellationToken cancellationToken)
    {
        return await _settingRepository.GetSettingsDictionaryAsync(request.Category, cancellationToken);
    }
}
