<template>
  <div class="address-based-delivery">
    <h4 class="section-title">
      <i class="fas fa-map-marker-alt"></i>
      Доставка за адресою
    </h4>

    <!-- Вибір адреси користувача -->
    <div class="address-selection">
      <div v-if="loadingAddresses" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Завантаження адрес...
      </div>

      <div v-else-if="allAddresses.length === 0" class="no-addresses">
        <i class="fas fa-exclamation-triangle"></i>
        <p>У вас немає збережених адрес</p>
        <button @click="showAddAddressForm = true" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Додати адресу
        </button>
      </div>

      <div v-else class="addresses-list">
        <label class="section-label">Оберіть адресу доставки:</label>
        <div class="address-options">
          <!-- Адреса з профілю (якщо є) -->
          <div
            v-if="profileAddress"
            class="address-option profile-address"
            :class="{ 'selected': selectedAddress?.isProfile }"
            @click="selectAddress(profileAddress)"
          >
            <div class="address-radio">
              <input
                type="radio"
                id="profile-address"
                value="profile"
                v-model="selectedAddressId"
                @change="selectAddress(profileAddress)"
              />
              <label for="profile-address" class="radio-label"></label>
            </div>
            <div class="address-info">
              <div class="address-header">
                <i class="fas fa-user"></i>
                <span class="address-label">Основна адреса (з профілю)</span>
              </div>
              <div class="address-text">{{ formatAddress(profileAddress) }}</div>
              <div class="address-actions">
                <router-link to="/profile" class="btn-link">
                  <i class="fas fa-edit"></i>
                  Редагувати в профілі
                </router-link>
              </div>
            </div>
          </div>

          <!-- Додаткові адреси -->
          <div
            v-for="address in userAddresses"
            :key="address.id"
            class="address-option"
            :class="{ 'selected': selectedAddress?.id === address.id }"
            @click="selectAddress(address)"
          >
            <div class="address-radio">
              <input
                type="radio"
                :id="`address-${address.id}`"
                :value="address.id"
                v-model="selectedAddressId"
                @change="selectAddress(address)"
              />
              <label :for="`address-${address.id}`" class="radio-label"></label>
            </div>
            <div class="address-info">
              <div class="address-text">{{ formatAddress(address) }}</div>
              <div class="address-actions">
                <button @click.stop="editAddress(address)" class="btn-link">
                  <i class="fas fa-edit"></i>
                  Редагувати
                </button>
              </div>
            </div>
          </div>
        </div>

        <button @click="showAddAddressForm = true" class="btn btn-outline add-address-btn">
          <i class="fas fa-plus"></i>
          Додати нову адресу
        </button>
      </div>
    </div>

    <!-- Результати пошуку відділень -->
    <div v-if="selectedAddress && Object.keys(deliveryResults).length > 0" class="delivery-results">
      <h5 class="results-title">
        <i class="fas fa-truck"></i>
        Доступні варіанти доставки
      </h5>

      <div v-if="loadingDelivery" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Пошук варіантів доставки...
      </div>

      <div v-else class="delivery-options">
        <div
          v-for="(result, service) in deliveryResults"
          :key="service"
          class="delivery-option"
          v-if="result && result.offices && result.offices.length > 0"
        >
          <div class="service-header">
            <i :class="getServiceIcon(service)"></i>
            <span class="service-name">{{ getServiceName(service) }}</span>
            <span class="offices-count">({{ result.offices.length }} відділень)</span>
          </div>
          
          <div class="offices-preview">
            <div
              v-for="office in (result.offices || []).slice(0, 3)"
              :key="office.id || office.ref"
              class="office-preview"
              @click="selectDeliveryOption(service, office, result.city)"
            >
              <div class="office-name">{{ formatOfficeName(office, service) }}</div>
              <div class="office-address">{{ formatOfficeAddress(office, service) }}</div>
            </div>

            <button
              v-if="result.offices && result.offices.length > 3"
              @click="showAllOffices(service, result)"
              class="btn-link show-more"
            >
              Показати всі {{ result.offices.length }} відділень
            </button>
          </div>
        </div>

        <div v-if="deliveryResults && Object.values(deliveryResults).every(r => r && r.offices && r.offices.length === 0)" class="no-delivery">
          <i class="fas fa-exclamation-triangle"></i>
          <p>На жаль, доставка за вашою адресою недоступна</p>
          <p class="suggestion">Спробуйте вибрати відділення вручну</p>
        </div>
      </div>
    </div>

    <!-- Форма додавання/редагування адреси -->
    <div v-if="showAddAddressForm" class="address-form-overlay" @click="closeAddressForm">
      <div class="address-form" @click.stop>
        <h5>{{ editingAddress ? 'Редагувати адресу' : 'Додати нову адресу' }}</h5>
        
        <form @submit.prevent="saveAddress">
          <div class="form-group">
            <label>Регіон/Область</label>
            <input
              type="text"
              v-model="addressForm.region"
              required
              placeholder="Наприклад: Київська область"
            />
          </div>
          
          <div class="form-group">
            <label>Місто</label>
            <input
              type="text"
              v-model="addressForm.city"
              required
              placeholder="Наприклад: Київ"
            />
          </div>
          
          <div class="form-group">
            <label>Вулиця та номер будинку</label>
            <input
              type="text"
              v-model="addressForm.street"
              required
              placeholder="Наприклад: вул. Хрещатик, 1"
            />
          </div>
          
          <div class="form-group">
            <label>Поштовий індекс</label>
            <input
              type="text"
              v-model="addressForm.postalCode"
              required
              placeholder="Наприклад: 01001"
            />
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeAddressForm" class="btn btn-secondary">
              Скасувати
            </button>
            <button type="submit" class="btn btn-primary" :disabled="savingAddress">
              <i v-if="savingAddress" class="fas fa-spinner fa-spin"></i>
              {{ editingAddress ? 'Зберегти' : 'Додати' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import addressService from '@/services/address.service';
import { useToast } from '@/composables/useToast';

export default {
  name: 'AddressBasedDelivery',
  
  emits: ['delivery-selected'],

  data() {
    const { showToast } = useToast();
    
    return {
      userAddresses: [],
      profileAddress: null,
      selectedAddress: null,
      selectedAddressId: null,
      deliveryResults: {},
      loadingAddresses: false,
      loadingDelivery: false,
      showAddAddressForm: false,
      editingAddress: null,
      savingAddress: false,
      addressForm: {
        region: '',
        city: '',
        street: '',
        postalCode: ''
      },
      showToast
    };
  },

  async mounted() {
    await this.loadUserData();
  },

  computed: {
    allAddresses() {
      const addresses = [...this.userAddresses];
      if (this.profileAddress) {
        addresses.unshift(this.profileAddress);
      }
      return addresses;
    }
  },

  methods: {
    async loadUserData() {
      this.loadingAddresses = true;
      try {
        // Завантажуємо дані користувача з профілю
        await this.loadProfileAddress();

        // Завантажуємо додаткові адреси
        await this.loadUserAddresses();

        // Автоматично вибираємо профільну адресу, якщо вона єдина
        if (this.profileAddress && this.userAddresses.length === 0) {
          await this.selectAddress(this.profileAddress);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        this.showToast('Помилка завантаження даних користувача', 'error');
      } finally {
        this.loadingAddresses = false;
      }
    },

    async loadProfileAddress() {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          return;
        }

        const response = await fetch('/api/users/me', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const result = await response.json();
          const user = result.success ? result.data : result;

          // Перевіряємо, чи є адреса в профілі користувача
          if (user.addresses && user.addresses.length > 0) {
            // Використовуємо першу адресу як основну
            const primaryAddress = user.addresses[0];
            this.profileAddress = {
              id: 'profile',
              isProfile: true,
              region: primaryAddress.region,
              city: primaryAddress.city,
              street: primaryAddress.street,
              postalCode: primaryAddress.postalCode,
              fullAddress: primaryAddress.fullAddress
            };
          } else if (user.region && user.city && user.street && user.postalCode) {
            // Якщо адреса зберігається безпосередньо в полях користувача
            this.profileAddress = {
              id: 'profile',
              isProfile: true,
              region: user.region,
              city: user.city,
              street: user.street,
              postalCode: user.postalCode
            };
          }
        }
      } catch (error) {
        console.error('Error loading profile address:', error);
        // Не показуємо помилку, оскільки це не критично
      }
    },

    async loadUserAddresses() {
      try {
        const response = await addressService.getUserAddresses();
        if (response.success && response.data) {
          this.userAddresses = Array.isArray(response.data) ? response.data : response.data.items || [];
        }
      } catch (error) {
        console.error('Error loading user addresses:', error);
        // Не показуємо помилку, оскільки профільна адреса може бути доступна
        this.userAddresses = [];
      }
    },

    async selectAddress(address) {
      this.selectedAddress = address;
      this.selectedAddressId = address.isProfile ? 'profile' : address.id;

      // Шукаємо варіанти доставки для цієї адреси
      await this.findDeliveryOptions(address);
    },

    async findDeliveryOptions(address) {
      this.loadingDelivery = true;
      this.deliveryResults = {};

      try {
        const services = ['nova-poshta', 'ukr-poshta'];
        const results = {};

        for (const service of services) {
          try {
            const result = await addressService.findNearestOffices(address, service);
            results[service] = result || { offices: [], city: null };
          } catch (error) {
            console.error(`Error finding offices for ${service}:`, error);
            results[service] = { offices: [], city: null };
          }
        }

        this.deliveryResults = results;
      } catch (error) {
        console.error('Error finding delivery options:', error);
        this.showToast('Помилка пошуку варіантів доставки', 'error');
        this.deliveryResults = {
          'nova-poshta': { offices: [], city: null },
          'ukr-poshta': { offices: [], city: null }
        };
      } finally {
        this.loadingDelivery = false;
      }
    },

    selectDeliveryOption(service, office, city) {
      this.$emit('delivery-selected', {
        address: this.selectedAddress,
        service,
        office,
        city
      });
      
      this.showToast(`Обрано доставку ${this.getServiceName(service)}`, 'success');
    },

    showAllOffices(service, result) {
      // Тут можна відкрити модальне вікно з усіма відділеннями
      // Або перенаправити на окрему сторінку
      console.log('Show all offices for', service, result);
    },

    editAddress(address) {
      this.editingAddress = address;
      this.addressForm = {
        region: address.region || address.addressRegion || '',
        city: address.city || address.addressCity || '',
        street: address.street || address.addressStreet || '',
        postalCode: address.postalCode || address.addressPostalCode || ''
      };
      this.showAddAddressForm = true;
    },

    async saveAddress() {
      this.savingAddress = true;
      try {
        if (this.editingAddress) {
          // Оновлюємо існуючу адресу
          await addressService.updateAddress(this.editingAddress.id, this.addressForm);
          this.showToast('Адресу оновлено', 'success');
        } else {
          // Створюємо нову адресу
          await addressService.createAddress(this.addressForm);
          this.showToast('Адресу додано', 'success');
        }
        
        await this.loadUserData();
        this.closeAddressForm();
      } catch (error) {
        console.error('Error saving address:', error);
        this.showToast('Помилка збереження адреси', 'error');
      } finally {
        this.savingAddress = false;
      }
    },

    closeAddressForm() {
      this.showAddAddressForm = false;
      this.editingAddress = null;
      this.addressForm = {
        region: '',
        city: '',
        street: '',
        postalCode: ''
      };
    },

    formatAddress(address) {
      return addressService.formatAddress(address);
    },

    getServiceName(service) {
      const names = {
        'nova-poshta': 'Нова Пошта',
        'ukr-poshta': 'Укрпошта'
      };
      return names[service] || service;
    },

    getServiceIcon(service) {
      const icons = {
        'nova-poshta': 'fas fa-truck',
        'ukr-poshta': 'fas fa-envelope'
      };
      return icons[service] || 'fas fa-shipping-fast';
    },

    formatOfficeName(office, service) {
      if (service === 'nova-poshta') {
        return `Відділення №${office.number}`;
      } else if (service === 'ukr-poshta') {
        return office.name;
      }
      return office.name || office.description;
    },

    formatOfficeAddress(office, service) {
      if (service === 'nova-poshta') {
        return office.shortAddress || office.description;
      } else if (service === 'ukr-poshta') {
        return office.address;
      }
      return office.address || office.shortAddress;
    }
  }
};
</script>

<style scoped>
.address-based-delivery {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #007bff;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.no-addresses {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.no-addresses i {
  font-size: 20px;
  color: #ffc107;
  margin-bottom: 6px;
}

.no-addresses .btn {
  padding: 8px 16px;
  font-size: 14px;
}

.section-label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #555;
}

.address-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.address-option {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.address-option:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.address-option.selected {
  border-color: #007bff;
  background: #f8f9ff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.address-radio {
  margin-right: 12px;
  margin-top: 2px;
}

.address-radio input[type="radio"] {
  opacity: 0;
  position: absolute;
}

.radio-label {
  display: block;
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.address-radio input[type="radio"]:checked + .radio-label {
  border-color: #007bff;
  background: #007bff;
}

.address-radio input[type="radio"]:checked + .radio-label::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.address-header i {
  color: #007bff;
  font-size: 14px;
}

.address-label {
  font-weight: 600;
  color: #007bff;
  font-size: 14px;
}

.address-text {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.profile-address {
  border-color: #007bff;
  background: #f8f9ff;
}

.profile-address:hover {
  border-color: #0056b3;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.address-actions {
  display: flex;
  gap: 12px;
}

.btn-link {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 3px;
}

.btn-link:hover {
  text-decoration: underline;
}

.add-address-btn {
  width: 100%;
  padding: 10px;
  border: 2px dashed #007bff;
  background: transparent;
  color: #007bff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.add-address-btn:hover {
  background: #f8f9ff;
}

.delivery-results {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.results-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.delivery-option {
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.service-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.service-header i {
  color: #007bff;
}

.offices-count {
  color: #6c757d;
  font-weight: normal;
  font-size: 14px;
}

.offices-preview {
  padding: 16px;
}

.office-preview {
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.office-preview:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.office-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.office-address {
  font-size: 14px;
  color: #6c757d;
}

.show-more {
  margin-top: 6px;
  font-size: 12px;
  padding: 4px 0;
}

.no-delivery {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.no-delivery i {
  font-size: 24px;
  color: #ffc107;
  margin-bottom: 8px;
}

.suggestion {
  font-size: 14px;
  color: #007bff;
  margin-top: 8px;
}

.address-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.address-form {
  background: white;
  padding: 24px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  border: 1px solid #007bff;
  color: #007bff;
}

.btn-outline:hover {
  background: #007bff;
  color: white;
}

@media (max-width: 768px) {
  .address-form {
    width: 95%;
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn {
    width: 100%;
    padding: 10px 16px;
    font-size: 14px;
  }

  .add-address-btn {
    padding: 8px;
    font-size: 13px;
  }

  .btn-link {
    font-size: 11px;
  }
}
</style>
