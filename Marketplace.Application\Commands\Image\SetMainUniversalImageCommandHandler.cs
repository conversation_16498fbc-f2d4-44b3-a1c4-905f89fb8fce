using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Обробник універсальної команди встановлення головного зображення
/// </summary>
public class SetMainUniversalImageCommandHandler : IRequestHandler<SetMainUniversalImageCommand, bool>
{
    private readonly IImageService _imageService;
    private readonly ILogger<SetMainUniversalImageCommandHandler> _logger;

    public SetMainUniversalImageCommandHandler(
        IImageService imageService,
        ILogger<SetMainUniversalImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<bool> Handle(SetMainUniversalImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Setting main image {request.ImageId} for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.SetMainImageAsync(
                request.EntityType,
                request.EntityId,
                request.ImageId,
                cancellationToken);

            if (result)
            {
                _logger.LogInformation($"Successfully set main image {request.ImageId} for {request.EntityType} {request.EntityId}");
            }
            else
            {
                _logger.LogWarning($"Failed to set main image {request.ImageId} for {request.EntityType} {request.EntityId}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error setting main image {request.ImageId} for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
