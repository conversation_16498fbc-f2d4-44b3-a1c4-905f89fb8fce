<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="closeModal"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <span class="icon">
            <i class="fas fa-search"></i>
          </span>
          Select Product
        </p>
        <button class="delete" @click="closeModal"></button>
      </header>
      
      <section class="modal-card-body">
        <!-- Search Input -->
        <div class="field">
          <div class="control has-icons-left">
            <input 
              class="input" 
              type="text" 
              placeholder="Search products..."
              v-model="searchQuery"
              @input="searchProducts"
            >
            <span class="icon is-small is-left">
              <i class="fas fa-search"></i>
            </span>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="has-text-centered py-4">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading products...</p>
        </div>

        <!-- Products List -->
        <div v-else-if="products.length > 0" class="products-list">
          <div 
            v-for="product in products" 
            :key="product.id"
            class="product-item"
            @click="selectProduct(product)"
          >
            <div class="media">
              <div class="media-left" v-if="product.image">
                <figure class="image is-48x48">
                  <img :src="product.image" :alt="product.name" @error="handleImageError">
                </figure>
              </div>
              <div class="media-left" v-else>
                <div class="placeholder-image">
                  <i class="fas fa-image"></i>
                </div>
              </div>
              <div class="media-content">
                <div class="content">
                  <p>
                    <strong>{{ product.name }}</strong>
                    <br>
                    <small class="has-text-grey">{{ product.categoryName || 'No Category' }}</small>
                    <br>
                    <span class="tag is-primary is-light">{{ formatCurrency(product.priceAmount) }}</span>
                    <span v-if="product.stock !== undefined" class="tag" :class="getStockClass(product.stock)">
                      Stock: {{ product.stock }}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div v-else-if="!loading && searchQuery" class="has-text-centered py-4">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-search fa-2x"></i>
          </span>
          <p class="has-text-grey">No products found for "{{ searchQuery }}"</p>
        </div>

        <!-- Initial State -->
        <div v-else class="has-text-centered py-4">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-box fa-2x"></i>
          </span>
          <p class="has-text-grey">Start typing to search for products</p>
        </div>
      </section>
      
      <footer class="modal-card-foot">
        <button class="button" @click="closeModal">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { productsService } from '@/admin/services/products';

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close', 'select']);

// State
const searchQuery = ref('');
const products = ref([]);
const loading = ref(false);
const searchTimeout = ref(null);

// Methods
const closeModal = () => {
  emit('close');
  searchQuery.value = '';
  products.value = [];
};

const selectProduct = (product) => {
  emit('select', product);
  closeModal();
};

const searchProducts = async () => {
  // Clear previous timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  // Don't search if query is too short
  if (searchQuery.value.length < 2) {
    products.value = [];
    return;
  }

  // Debounce search
  searchTimeout.value = setTimeout(async () => {
    loading.value = true;
    try {
      const response = await productsService.getAll({
        search: searchQuery.value,
        pageSize: 20
      });
      
      if (response.success && response.data) {
        products.value = response.data;
      } else {
        products.value = [];
      }
    } catch (error) {
      console.error('Error searching products:', error);
      products.value = [];
    } finally {
      loading.value = false;
    }
  }, 300);
};

const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const getStockClass = (stock) => {
  if (stock === 0) return 'is-danger';
  if (stock < 10) return 'is-warning';
  return 'is-success';
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
  event.target.parentElement.innerHTML = '<div class="placeholder-image"><i class="fas fa-image"></i></div>';
};

// Watch for modal open/close
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    searchQuery.value = '';
    products.value = [];
  }
});
</script>

<style scoped>
.products-list {
  max-height: 400px;
  overflow-y: auto;
}

.product-item {
  padding: 0.75rem;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-item:hover {
  background-color: #f5f5f5;
  border-color: #3273dc;
}

.placeholder-image {
  width: 48px;
  height: 48px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dbdbdb;
}

.media-content {
  overflow: hidden;
}

.tag {
  margin-right: 0.25rem;
}
</style>
