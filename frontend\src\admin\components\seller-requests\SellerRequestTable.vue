<template>
  <div class="admin-seller-request-table">
    <div class="admin-table-container">
      <table class="admin-table">
        <thead class="admin-table-header">
          <tr>
            <th class="admin-table-th">ID</th>
            <th class="admin-table-th">User</th>
            <th class="admin-table-th">Business Name</th>
            <th class="admin-table-th">Submitted</th>
            <th class="admin-table-th">Status</th>
            <th class="admin-table-th">Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && requests.length > 0" class="admin-table-body">
          <tr v-for="request in requests" :key="request.id" class="admin-table-row">
            <td class="admin-table-td">
              <span class="admin-seller-request-id">{{ request.id }}</span>
            </td>
            <td class="admin-table-td">
              <div class="admin-user-info">
                <div class="admin-user-avatar">
                  <img
                    :src="getUserAvatarUrl(request.user)"
                    :alt="request.userName || 'User Avatar'"
                    @error="handleAvatarError($event)"
                    class="admin-avatar-image"
                  />
                </div>
                <div class="admin-user-details">
                  <div class="admin-user-name">{{ request.userName }}</div>
                  <div class="admin-user-email" v-if="request.user?.email">{{ request.user.email }}</div>
                </div>
              </div>
            </td>
            <td class="admin-table-td">
              <div class="admin-business-info">
                <div class="admin-business-name">{{ request.businessName }}</div>
              </div>
            </td>
            <td class="admin-table-td">{{ formatDate(request.submittedAt) }}</td>
            <td class="admin-table-td">
              <status-badge :status="request.status" type="seller-request" />
            </td>
            <td class="admin-table-td">
              <div class="admin-table-actions">
                <button
                  class="admin-btn admin-btn-secondary admin-btn-sm"
                  @click="$emit('view', request.id)"
                  title="View Details">
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  v-if="request.status === 'Pending'"
                  class="admin-btn admin-btn-success admin-btn-sm"
                  @click="$emit('approve', request.id)">
                  <i class="fas fa-check"></i>
                </button>
                <button
                  v-if="request.status === 'Pending'"
                  class="admin-btn admin-btn-danger admin-btn-sm"
                  @click="$emit('reject', request.id)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading" class="admin-table-body">
          <tr>
            <td colspan="6" class="admin-table-td admin-table-loading">
              <div class="admin-loading-state">
                <div class="admin-spinner">
                  <i class="fas fa-spinner fa-pulse"></i>
                </div>
                <p class="admin-loading-text">Loading...</p>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else class="admin-table-body">
          <tr>
            <td colspan="6" class="admin-table-td admin-table-empty">
              <div class="admin-empty-state">
                <div class="admin-empty-icon">
                  <i class="fas fa-store"></i>
                </div>
                <p class="admin-empty-text">No seller applications found.</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

defineProps({
  requests: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['view', 'approve', 'reject']);

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Get user avatar URL with fallback
const getUserAvatarUrl = (user) => {
  if (!user) return getDefaultAvatarUrl();

  // Check if user has avatarUrl
  if (user.avatarUrl && user.avatarUrl.trim() !== '') {
    return user.avatarUrl;
  }

  // Check if user has avatar property
  if (user.avatar && user.avatar.trim() !== '') {
    return user.avatar;
  }

  // Return default avatar
  return getDefaultAvatarUrl();
};

// Get default avatar URL
const getDefaultAvatarUrl = () => {
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGM0Y0RjYiLz4KPHA+YXRoIGQ9Ik0yMCAyMkM5LjUgMjIgMTEuNSAyOCAxNiAzMEMyMC41IDMyIDI5LjUgMzIgMzQgMzBDMzguNSAyOCA0MC41IDIyIDMwIDIySDIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjE2IiByPSI2IiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=';
};

// Handle avatar loading error
const handleAvatarError = (event) => {
  event.target.src = getDefaultAvatarUrl();
};
</script>

<style scoped>
.admin-seller-request-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  border: 1px solid var(--admin-border-light);
  overflow: hidden;
}

.admin-table-actions {
  display: flex;
  gap: var(--admin-space-xs);
  justify-content: flex-start;
}

.admin-table-loading,
.admin-table-empty {
  text-align: center;
  padding: var(--admin-space-2xl);
}

.admin-seller-request-id {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-user-avatar {
  flex-shrink: 0;
}

.admin-avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--admin-border-light);
}

.admin-user-details {
  flex: 1;
  min-width: 0;
}

.admin-user-name {
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
  margin-bottom: 2px;
}

.admin-user-email {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.admin-business-name {
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
}
</style>
