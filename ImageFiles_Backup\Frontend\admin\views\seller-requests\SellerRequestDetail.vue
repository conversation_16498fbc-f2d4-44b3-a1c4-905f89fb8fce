<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Seller Requests
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-store admin-page-icon"></i>
          Seller Request Details
        </h1>
        <p class="admin-page-subtitle">Review and manage seller application</p>
      </div>
      <div class="admin-page-actions">
        <Transition name="fade" mode="out-in">
          <div v-if="isPending" key="pending-actions" class="admin-action-buttons">
            <button
              class="admin-btn admin-btn-success"
              @click="confirmApprove"
              :disabled="processing">
              <i class="fas fa-check"></i>
              Approve Request
            </button>
            <button
              class="admin-btn admin-btn-danger"
              @click="confirmReject"
              :disabled="processing">
              <i class="fas fa-times"></i>
              Reject Request
            </button>
          </div>
          <div v-else-if="isApproved" key="approved-status" class="admin-status-badge admin-status-approved">
            <i class="fas fa-check-circle"></i>
            Approved
          </div>
          <div v-else-if="isRejected" key="rejected-status" class="admin-status-badge admin-status-rejected">
            <i class="fas fa-times-circle"></i>
            Rejected
          </div>
        </Transition>
      </div>
    </div>

    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading seller request details...</p>
    </div>
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
      {{ error }}
    </div>
    <div v-else-if="!request.id" class="admin-alert admin-alert-warning">
      <p>Seller request not found.</p>
      <router-link to="/admin/seller-requests" class="admin-btn admin-btn-primary">
        Back to Seller Requests
      </router-link>
    </div>
    <!-- Seller Request Details Content -->
    <div v-else class="admin-seller-request-content">
      <div class="admin-seller-request-layout">
        <!-- Applicant Information Card -->
        <div class="admin-card admin-seller-request-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-user admin-card-icon"></i>
              Applicant Information
            </h3>
          </div>
          <div class="admin-card-content">
            <!-- Applicant Profile -->
            <div class="applicant-profile-section">
              <div class="applicant-avatar">
                {{ getUserInitials(request.user?.username) }}
              </div>
              <div class="applicant-info">
                <h4 class="applicant-name">{{ request.user?.username || 'Unknown User' }}</h4>
                <p class="applicant-email">
                  <i class="fas fa-envelope"></i>
                  {{ request.user?.email || 'No email provided' }}
                </p>
                <div class="applicant-badges">
                  <span class="admin-badge admin-badge-info">{{ request.user?.role || 'User' }}</span>
                  <status-badge
                    :status="currentStatus"
                    type="seller-request" />
                </div>
              </div>
            </div>

            <!-- Applicant Details -->
            <div class="admin-info-grid admin-info-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Registered Since</label>
                <div class="admin-info-value">{{ formatDate(request.user?.createdAt) }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Email Status</label>
                <div class="admin-info-value">
                  <i class="fas fa-check-circle admin-text-success" v-if="request.user?.emailConfirmed"></i>
                  <i class="fas fa-times-circle admin-text-danger" v-else></i>
                  {{ request.user?.emailConfirmed ? 'Confirmed' : 'Not Confirmed' }}
                </div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Request Submitted</label>
                <div class="admin-info-value">{{ formatDate(request.createdAt) }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Request Status</label>
                <div class="admin-info-value">
                  <status-badge
                    :status="currentStatus"
                    type="seller-request" />
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="admin-card-actions">
              <router-link
                v-if="request.userId"
                :to="`/admin/users/${request.userId}`"
                class="admin-btn admin-btn-secondary admin-btn-sm">
                <i class="fas fa-external-link-alt"></i>
                View Full Profile
              </router-link>
            </div>
          </div>
        </div>

        <!-- Company Information Card -->
        <div class="admin-card admin-seller-request-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-building admin-card-icon"></i>
              Company Information
            </h3>
          </div>
          <div class="admin-card-content">
            <!-- Company Image -->
            <div v-if="request.companyRequestData?.imageUrl" class="company-image-section">
              <img :src="request.companyRequestData.imageUrl"
                   :alt="request.companyRequestData?.name"
                   class="company-image" />
            </div>

            <!-- Basic Information -->
            <div class="admin-info-grid admin-info-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Company Name</label>
                <div class="admin-info-value">{{ request.companyRequestData?.name || 'N/A' }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Contact Email</label>
                <div class="admin-info-value">
                  <a v-if="request.companyRequestData?.contactEmail"
                     :href="`mailto:${request.companyRequestData.contactEmail}`"
                     class="admin-link">
                    <i class="fas fa-envelope"></i>
                    {{ request.companyRequestData.contactEmail }}
                  </a>
                  <span v-else class="admin-text-muted">N/A</span>
                </div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Contact Phone</label>
                <div class="admin-info-value">
                  <a v-if="request.companyRequestData?.contactPhone"
                     :href="`tel:${request.companyRequestData.contactPhone}`"
                     class="admin-link">
                    <i class="fas fa-phone"></i>
                    {{ request.companyRequestData.contactPhone }}
                  </a>
                  <span v-else class="admin-text-muted">N/A</span>
                </div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Meta Title</label>
                <div class="admin-info-value">{{ request.companyRequestData?.metaTitle || 'N/A' }}</div>
              </div>
            </div>

            <!-- Description -->
            <div class="admin-info-group">
              <label class="admin-info-label">Description</label>
              <div class="admin-info-value admin-info-value-multiline">{{ request.companyRequestData?.description || 'No description provided' }}</div>
            </div>

            <!-- Address -->
            <div v-if="getFullAddress()" class="admin-info-group">
              <label class="admin-info-label">Address</label>
              <div class="admin-info-value">
                <i class="fas fa-map-marker-alt"></i>
                {{ getFullAddress() }}
              </div>
            </div>

            <!-- Meta Description -->
            <div v-if="request.companyRequestData?.metaDescription" class="admin-info-group">
              <label class="admin-info-label">Meta Description</label>
              <div class="admin-info-value admin-info-value-multiline">{{ request.companyRequestData.metaDescription }}</div>
            </div>
          </div>
        </div>

        <!-- Finance Information -->
        <div class="admin-card" style="grid-area: finance;">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-credit-card admin-card-icon"></i>
              Financial Information
            </h3>
          </div>
          <div class="admin-card-content">

            <!-- Banking Information -->
            <div class="finance-section">
              <h4 class="finance-section-title">
                <i class="fas fa-university"></i>
                Banking Details
              </h4>
              <div class="admin-info-grid admin-info-grid-2">
                <div class="admin-info-group">
                  <label class="admin-info-label">Bank Name</label>
                  <div class="admin-info-value">{{ request.financeRequestData?.bankName || 'N/A' }}</div>
                </div>

                <div class="admin-info-group">
                  <label class="admin-info-label">Bank Code</label>
                  <div class="admin-info-value">{{ request.financeRequestData?.bankCode || 'N/A' }}</div>
                </div>

                <div class="admin-info-group">
                  <label class="admin-info-label">Bank Account</label>
                  <div class="admin-info-value admin-info-value-code">{{ formatBankAccount(request.financeRequestData?.bankAccount) }}</div>
                </div>

                <div class="admin-info-group">
                  <label class="admin-info-label">Tax ID</label>
                  <div class="admin-info-value admin-info-value-code">{{ request.financeRequestData?.taxId || 'N/A' }}</div>
                </div>
              </div>
            </div>

            <!-- Payment Information -->
            <div v-if="request.financeRequestData?.paymentDetails" class="finance-section">
              <h4 class="finance-section-title">
                <i class="fas fa-file-invoice-dollar"></i>
                Payment Information
              </h4>
              <div class="admin-info-group">
                <label class="admin-info-label">Payment Details</label>
                <div class="admin-info-value admin-info-value-multiline">{{ request.financeRequestData.paymentDetails }}</div>
              </div>
            </div>

            <!-- Security Notice -->
            <div class="security-notice">
              <i class="fas fa-shield-alt"></i>
              <span>Financial information is encrypted and securely stored</span>
            </div>

          </div>
        </div>

        <!-- Schedule Information Card -->
        <div class="admin-card admin-seller-request-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-clock admin-card-icon"></i>
              Schedule Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div v-if="request.scheduleRequestData?.daySchedules && request.scheduleRequestData.daySchedules.length > 0" class="schedule-table-wrapper">
              <table class="admin-table schedule-table">
                <thead>
                  <tr>
                    <th class="day-column">Day</th>
                    <th class="status-column">Status</th>
                    <th class="time-column">Working Hours</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="schedule in request.scheduleRequestData.daySchedules" :key="schedule.day" class="schedule-row">
                    <td class="day-cell">
                      <div class="day-info">
                        <span class="day-name">{{ getDayName(schedule.day) }}</span>
                      </div>
                    </td>
                    <td class="status-cell">
                      <span
                        class="status-badge"
                        :class="schedule.isClosed ? 'status-closed' : 'status-open'">
                        <i class="fas" :class="schedule.isClosed ? 'fa-times' : 'fa-check'"></i>
                        {{ schedule.isClosed ? 'Closed' : 'Open' }}
                      </span>
                    </td>
                    <td class="time-cell">
                      <div v-if="!schedule.isClosed" class="time-range">
                        <span class="time-start">{{ schedule.openTime || 'Not set' }}</span>
                        <span class="time-separator">-</span>
                        <span class="time-end">{{ schedule.closeTime || 'Not set' }}</span>
                      </div>
                      <span v-else class="time-closed">-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="admin-empty-state">
              <i class="fas fa-clock admin-empty-icon"></i>
              <p class="admin-empty-text">No schedule information provided</p>
            </div>
          </div>
        </div>

        <!-- Additional Information Card -->
        <div class="admin-card admin-seller-request-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-info-circle admin-card-icon"></i>
              Additional Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div v-if="request.additionalInformation" class="admin-info-group">
              <label class="admin-info-label">Additional Notes</label>
              <div class="admin-info-value admin-info-value-multiline">{{ request.additionalInformation }}</div>
            </div>
            <div v-else class="admin-empty-state">
              <i class="fas fa-info-circle admin-empty-icon"></i>
              <p class="admin-empty-text">No additional information provided</p>
            </div>
          </div>
        </div>

        <!-- Documents Card -->
        <div v-if="request.documents && request.documents.length > 0" class="admin-card admin-seller-request-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-file-alt admin-card-icon"></i>
              Documents
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-documents-list">
              <div
                v-for="(document, index) in request.documents"
                :key="index"
                class="admin-document-item">
                <div class="admin-document-icon">
                  <i :class="getDocumentIcon(document.type)"></i>
                </div>
                <div class="admin-document-info">
                  <h4 class="admin-document-title">{{ document.name }}</h4>
                  <p class="admin-document-type">{{ document.type }}</p>
                </div>
                <div class="admin-document-actions">
                  <a
                    :href="document.url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="admin-btn admin-btn-sm admin-btn-primary">
                    <i class="fas fa-download"></i>
                    Download
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Rejection Reason Card -->
        <Transition name="fade" mode="out-in">
          <div v-if="hasRejectionReason" key="rejection-reason" class="admin-card admin-card-danger admin-seller-request-section">
            <div class="admin-card-header">
              <h3 class="admin-card-title">
                <i class="fas fa-times-circle admin-card-icon"></i>
                Rejection Reason
              </h3>
            </div>
            <div class="admin-card-content">
              <div class="admin-info-group">
                <label class="admin-info-label">Reason</label>
                <div class="admin-info-value admin-info-value-multiline">{{ request.rejectionReason || 'No reason provided' }}</div>
              </div>
            </div>
          </div>
        </Transition>

      </div>
    </div>

    <!-- Approve Confirmation Modal -->
    <confirm-dialog
      :is-open="showApproveModal"
      title="Approve Seller Request"
      :message="`Are you sure you want to approve ${request.user?.firstName || 'this user'} ${request.user?.lastName || ''}'s seller request for '${request.storeName || 'this store'}'?`"
      confirm-text="Approve"
      cancel-text="Cancel"
      confirm-button-class="is-success"
      @confirm="approveRequest"
      @cancel="cancelProcess" />

    <!-- Reject Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="cancelProcess"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Seller Request</p>
          <button class="delete" aria-label="close" @click="cancelProcess"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to reject {{ request.user?.firstName || 'this user' }}'s seller request for '{{ request.storeName || 'this store' }}'?</p>

          <div class="admin-form-group admin-mt-4">
            <label class="admin-form-label">Reason for Rejection (Optional)</label>
            <textarea
              class="admin-form-textarea"
              v-model="rejectionReason"
              placeholder="Provide a reason for rejection"
              rows="3">
            </textarea>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="admin-btn admin-btn-danger"
            @click="rejectRequest"
            :disabled="processing">
            <i v-if="processing" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-times"></i>
            {{ processing ? 'Rejecting...' : 'Reject' }}
          </button>
          <button class="admin-btn admin-btn-secondary" @click="cancelProcess">
            <i class="fas fa-times"></i>
            Cancel
          </button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { sellerRequestsService } from '@/admin/services/seller-requests';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const error = ref(null);
const request = ref({});
const processing = ref(false);

// Modal state
const showApproveModal = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Computed properties
const requestId = computed(() => route.params.id);

// Computed status to ensure reactivity
const currentStatus = computed(() => getStatusString(request.value?.status));

// Computed flags for status checks
const isPending = computed(() => currentStatus.value === 'pending');
const isApproved = computed(() => currentStatus.value === 'approved');
const isRejected = computed(() => currentStatus.value === 'rejected');
const hasRejectionReason = computed(() => isRejected.value && request.value?.rejectionReason);

// Navigation
const goBack = () => {
  router.push('/admin/seller-requests');
};

// Fetch request data
const fetchRequest = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await sellerRequestsService.getSellerRequest(requestId.value);
    request.value = response.data;
  } catch (err) {
    console.error('Error fetching seller request:', err);
    error.value = 'Failed to load seller request data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Get full address
const getFullAddress = () => {
  const data = request.value?.companyRequestData;
  if (!data) return 'N/A';

  const parts = [
    data.addressStreet,
    data.addressCity,
    data.addressRegion,
    data.addressPostalCode
  ].filter(Boolean);

  return parts.length > 0 ? parts.join(', ') : 'N/A';
};

// Get day name
const getDayName = (dayValue) => {
  // Handle null, undefined
  if (dayValue === null || dayValue === undefined) {
    return 'Not specified';
  }

  // If it's already a string from enum (Monday, Tuesday, etc.)
  if (typeof dayValue === 'string') {
    // Map enum string values to display names
    const dayMap = {
      'Monday': 'Monday',
      'Tuesday': 'Tuesday',
      'Wednesday': 'Wednesday',
      'Thursday': 'Thursday',
      'Friday': 'Friday',
      'Saturday': 'Saturday',
      'Sunday': 'Sunday'
    };

    return dayMap[dayValue] || dayValue; // Return the mapped value or original if not found
  }

  // Handle numeric values (legacy support)
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  // Convert to number if it's a string number
  let dayIndex = typeof dayValue === 'string' ? parseInt(dayValue, 10) : dayValue;

  // Handle NaN
  if (isNaN(dayIndex)) {
    return 'Not specified';
  }

  // Handle 1-based indexing (Monday = 1, Sunday = 7)
  if (dayIndex >= 1 && dayIndex <= 7) {
    // Convert to 0-based indexing where Sunday = 0
    dayIndex = dayIndex === 7 ? 0 : dayIndex;
    return days[dayIndex] || 'Invalid day';
  }
  // Handle 0-based indexing (Sunday = 0)
  else if (dayIndex >= 0 && dayIndex <= 6) {
    return days[dayIndex] || 'Invalid day';
  }

  return 'Invalid day';
};

// Convert status enum to string
const getStatusString = (status) => {
  if (typeof status === 'string') {
    return status.toLowerCase();
  }

  switch (status) {
    case 0: return 'pending';
    case 1: return 'approved';
    case 2: return 'rejected';
    default: return 'pending';
  }
};

// Get status CSS class
const getStatusClass = (status) => {
  const statusString = getStatusString(status);
  return `status-${statusString}`;
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/150?text=No+Image';
};

// Get social class
const getSocialClass = (platform) => {
  switch (platform.toLowerCase()) {
    case 'facebook':
      return 'link';
    case 'twitter':
      return 'info';
    case 'instagram':
      return 'danger';
    case 'linkedin':
      return 'link';
    case 'youtube':
      return 'danger';
    default:
      return 'light';
  }
};

// Get document icon
const getDocumentIcon = (type) => {
  if (!type) return 'fas fa-file';

  type = type.toLowerCase();

  if (type.includes('pdf')) {
    return 'fas fa-file-pdf';
  } else if (type.includes('word') || type.includes('doc')) {
    return 'fas fa-file-word';
  } else if (type.includes('excel') || type.includes('xls')) {
    return 'fas fa-file-excel';
  } else if (type.includes('image') || type.includes('jpg') || type.includes('png')) {
    return 'fas fa-file-image';
  } else {
    return 'fas fa-file';
  }
};

// Format bank account number
const formatBankAccount = (account) => {
  if (!account) return 'N/A';

  // Format bank account number with spaces for better readability
  // Example: **************** -> 1234 5678 9012 3456
  const cleaned = account.replace(/\s/g, '');
  const formatted = cleaned.replace(/(.{4})/g, '$1 ').trim();

  return formatted;
};

// Confirm approve
const confirmApprove = () => {
  showApproveModal.value = true;
};

// Confirm reject
const confirmReject = () => {
  rejectionReason.value = '';
  showRejectModal.value = true;
};

// Cancel process
const cancelProcess = () => {
  showApproveModal.value = false;
  showRejectModal.value = false;
  rejectionReason.value = '';
};

// Approve request
const approveRequest = async () => {
  if (processing.value) return; // Prevent double-click

  processing.value = true;

  try {
    await sellerRequestsService.approveSellerRequest(requestId.value);

    // Close modal first
    showApproveModal.value = false;

    // Wait for DOM update
    await nextTick();

    // Create new request object to trigger reactivity
    const updatedRequest = {
      ...request.value,
      status: 1, // Approved
      approvedAt: new Date().toISOString()
    };

    // Update request data
    request.value = updatedRequest;

    // Force another DOM update
    await nextTick();

  } catch (err) {
    console.error('Error approving seller request:', err);
    error.value = 'Failed to approve seller request. Please try again.';
  } finally {
    processing.value = false;
  }
};

// Reject request
const rejectRequest = async () => {
  if (processing.value) return; // Prevent double-click

  processing.value = true;

  try {
    await sellerRequestsService.rejectSellerRequest(
      requestId.value,
      rejectionReason.value
    );

    // Store rejection reason before clearing
    const reason = rejectionReason.value || '';

    // Close modal first
    showRejectModal.value = false;
    rejectionReason.value = '';

    // Wait for DOM update
    await nextTick();

    // Create new request object to trigger reactivity
    const updatedRequest = {
      ...request.value,
      status: 2, // Rejected
      rejectedAt: new Date().toISOString(),
      rejectionReason: reason
    };

    // Update request data
    request.value = updatedRequest;

    // Force another DOM update
    await nextTick();

  } catch (err) {
    console.error('Error rejecting seller request:', err);
    error.value = 'Failed to reject seller request. Please try again.';
  } finally {
    processing.value = false;
  }
};

// Utility functions
const getUserInitials = (username) => {
  if (!username) return '?';
  return username.substring(0, 2).toUpperCase();
};

// Lifecycle hooks
onMounted(() => {
  fetchRequest();
});
</script>

<style scoped>
/* Enhanced Page Styles */
.admin-seller-request-detail {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 0;
}

/* Enhanced Page Header */
.admin-page-header-enhanced {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 2rem 2rem 3rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.admin-page-header-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
}

.admin-header-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.admin-breadcrumb-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.2s ease;
}

.admin-breadcrumb-link:hover {
  color: white;
}

.admin-breadcrumb-separator {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
}

.admin-breadcrumb-current {
  color: white;
  font-weight: 500;
}

.admin-page-title-enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0;
}

.admin-title-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.admin-title-content {
  display: flex;
  flex-direction: column;
}

.admin-title-main {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  line-height: 1.2;
}

.admin-title-sub {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  margin-top: 0.25rem;
}

.admin-btn-secondary-enhanced {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.admin-btn-secondary-enhanced:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

/* Seller Request Layout */
.admin-seller-request-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--admin-space-lg);
  background: var(--admin-bg-primary);
  min-height: calc(100vh - 200px);
}

.admin-seller-request-layout {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
  padding: var(--admin-space-lg) 0;
}

.admin-seller-request-section {
  margin-bottom: var(--admin-space-xl);
  box-shadow: var(--admin-shadow-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-border-radius-lg);
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.admin-seller-request-section:hover {
  box-shadow: var(--admin-shadow-md);
}

/* Enhanced Cards */
.admin-card-enhanced {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.admin-card-enhanced:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.admin-request-summary {
  margin-bottom: 2rem;
}

/* Enhanced Request Header */
.admin-seller-request-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  padding: 2rem;
}

.admin-seller-request-info-enhanced {
  flex: 1;
}

.admin-company-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-company-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.admin-company-details {
  flex: 1;
}

.admin-company-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.admin-company-description {
  color: #718096;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
}

.admin-request-meta-enhanced {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.admin-meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.admin-meta-item i {
  color: #667eea;
  width: 16px;
}

/* Enhanced Action Buttons */
.admin-seller-request-actions-enhanced {
  flex-shrink: 0;
}

.admin-action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.admin-btn-success-enhanced {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
}

.admin-btn-success-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

.admin-btn-danger-enhanced {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
}

.admin-btn-danger-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4);
}

/* Status Badges */
.admin-status-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.admin-status-approved {
  background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.admin-status-rejected {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  color: #742a2a;
  border: 1px solid #feb2b2;
}

.admin-status-badge i {
  font-size: 1.25rem;
}

.admin-status-content {
  display: flex;
  flex-direction: column;
}

.admin-status-title {
  font-size: 1rem;
  font-weight: 700;
}

.admin-status-date {
  font-size: 0.875rem;
  opacity: 0.8;
}

.admin-card-company {
  grid-area: company;
}

.admin-card:nth-child(1) {
  grid-area: user;
}

.admin-card:nth-child(3) {
  grid-area: finance;
}

.admin-card:nth-child(4) {
  grid-area: schedule;
}

.admin-card:nth-child(5) {
  grid-area: additional;
}

.admin-card:nth-child(6) {
  grid-area: documents;
}

.admin-card-danger {
  grid-area: rejection;
}

/* Seller Request Header */
.admin-seller-request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--admin-spacing-4);
}

.admin-seller-request-info {
  flex: 1;
}

.admin-seller-request-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-spacing-2) 0;
}

.admin-seller-request-meta {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-3);
  flex-wrap: wrap;
}

.admin-seller-request-date {
  color: var(--admin-text-secondary);
  font-size: 0.9rem;
}

.admin-seller-request-actions {
  flex-shrink: 0;
}

.admin-seller-request-status-actions {
  display: flex;
  gap: var(--admin-spacing-2);
}

/* Schedule Table */
.admin-schedule-table {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  overflow: hidden;
  background: var(--admin-white);
}

.admin-schedule-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 120px;
  background: var(--admin-gray-50);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-schedule-header-cell {
  padding: var(--admin-spacing-3);
  font-weight: 600;
  color: var(--admin-text-primary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-schedule-body {
  display: flex;
  flex-direction: column;
}

.admin-schedule-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 120px;
  border-bottom: 1px solid var(--admin-border-color);
  transition: background-color 0.2s ease;
}

.admin-schedule-row:last-child {
  border-bottom: none;
}

.admin-schedule-row:hover {
  background: var(--admin-gray-25);
}

.admin-schedule-cell {
  padding: var(--admin-spacing-3);
  display: flex;
  align-items: center;
  color: var(--admin-text-primary);
}

.admin-schedule-day {
  font-weight: 500;
}

@media (max-width: 768px) {
  .admin-schedule-header,
  .admin-schedule-row {
    grid-template-columns: 1fr;
  }

  .admin-schedule-header-cell,
  .admin-schedule-cell {
    padding: var(--admin-spacing-2);
  }

  .admin-schedule-row {
    border: 1px solid var(--admin-border-color);
    border-radius: var(--admin-border-radius);
    margin-bottom: var(--admin-spacing-2);
    background: var(--admin-white);
  }

  .admin-schedule-header {
    display: none;
  }

  .admin-schedule-cell:before {
    content: attr(data-label);
    font-weight: 600;
    color: var(--admin-text-secondary);
    margin-right: var(--admin-spacing-2);
    min-width: 80px;
  }
}

/* User Avatar Section */
.admin-user-avatar-section {
  text-align: center;
  margin-bottom: var(--admin-spacing-4);
}

.admin-user-avatar-large {
  width: 80px;
  height: 80px;
  font-size: 24px;
  margin: 0 auto;
}

/* Info Groups */
.admin-info-group {
  margin-bottom: var(--admin-spacing-3);
}

.admin-info-group-full {
  grid-column: span 2;
}

.admin-info-label {
  display: block;
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-1);
}

.admin-info-value {
  color: var(--admin-text-primary);
  font-weight: 500;
}

.admin-info-value-multiline {
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Image Preview */
.admin-image-preview {
  max-width: 200px;
}

.admin-image-preview-img {
  width: 100%;
  height: auto;
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-color);
}

/* Spacing utilities */
.admin-mt-4 {
  margin-top: var(--admin-spacing-4);
}

/* Enhanced Document Styles */
.admin-documents-list {
  display: grid;
  gap: 1rem;
}

.admin-document-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-border-radius-lg);
  transition: all 0.2s ease;
}

.admin-document-item:hover {
  background: var(--admin-bg-tertiary);
  border-color: var(--admin-primary);
  transform: translateY(-1px);
  box-shadow: var(--admin-shadow-md);
}

.admin-document-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
  color: white;
  border-radius: var(--admin-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.admin-document-info {
  flex: 1;
}

.admin-document-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0 0 0.25rem 0;
}

.admin-document-type {
  font-size: 0.875rem;
  color: var(--admin-text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.admin-document-actions {
  flex-shrink: 0;
}

/* Enhanced Empty State */
.admin-empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--admin-text-muted);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.admin-empty-text {
  font-size: 1.125rem;
  margin: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .admin-seller-request-content {
    padding: 0 var(--admin-space-md);
  }

  .admin-info-grid-2 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .admin-seller-request-content {
    padding: 0 var(--admin-space-sm);
  }

  .admin-seller-request-layout {
    gap: var(--admin-space-lg);
    padding: var(--admin-space-md) 0;
  }

  .applicant-profile-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    padding: 1rem;
  }

  .applicant-avatar {
    width: 64px;
    height: 64px;
    font-size: 1.25rem;
  }

  .applicant-name {
    font-size: 1.25rem;
  }

  .schedule-table-wrapper {
    font-size: 0.875rem;
  }

  .schedule-table th,
  .schedule-table td {
    padding: 0.75rem 0.5rem;
  }

  .time-range {
    font-size: 0.8rem;
    padding: 0.375rem 0.5rem;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 0.375rem 0.5rem;
  }

  .admin-document-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .admin-document-actions {
    width: 100%;
  }

  .admin-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .admin-grid-seller-request {
    grid-template-columns: 1fr;
    gap: var(--admin-spacing-3);
  }

  .admin-form-grid-2 {
    grid-template-columns: 1fr;
  }

  .admin-info-group-full {
    grid-column: span 1;
  }

  .admin-seller-request-header {
    flex-direction: column;
    gap: var(--admin-spacing-3);
  }

  .admin-seller-request-actions {
    width: 100%;
  }

  .admin-seller-request-status-actions {
    display: flex;
    gap: var(--admin-spacing-2);
    width: 100%;
  }

  .admin-seller-request-status-actions .admin-btn {
    flex: 1;
  }
}

.seller-request-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.request-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.request-subtitle {
  font-size: 1rem;
  color: #7a7a7a;
}

.user-avatar {
  width: 150px;
  height: 150px;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
}

.social-links {
  display: flex;
  flex-wrap: wrap;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.document-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: #7a7a7a;
}

.document-info {
  flex: 1;
}

.document-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.document-type {
  font-size: 0.8rem;
  color: #7a7a7a;
}

.document-actions {
  margin-left: 1rem;
}

/* Info Groups */
.admin-info-group {
  margin-bottom: var(--admin-spacing-3);
}

.admin-info-group-full {
  grid-column: span 2;
}

.admin-info-label {
  display: block;
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-1);
}

.admin-info-value {
  color: var(--admin-text-primary);
  font-weight: 500;
}

.admin-info-value-multiline {
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Grid Layout */
.admin-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--admin-spacing-4);
}

/* Spacing utilities */
.admin-mt-4 {
  margin-top: var(--admin-spacing-4);
}

/* Empty State */
.admin-empty-state {
  text-align: center;
  padding: var(--admin-spacing-6);
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 48px;
  margin-bottom: var(--admin-spacing-3);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: 16px;
  margin: 0;
}

/* Documents */
.admin-documents-list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-3);
}

.admin-document-item {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-3);
  padding: var(--admin-spacing-3);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  background: var(--admin-bg-secondary);
}

.admin-document-icon {
  font-size: 24px;
  color: var(--admin-primary);
  min-width: 40px;
  text-align: center;
}

.admin-document-info {
  flex: 1;
}

.admin-document-title {
  font-weight: 600;
  margin: 0 0 var(--admin-spacing-1) 0;
  color: var(--admin-text-primary);
}

.admin-document-type {
  font-size: 14px;
  color: var(--admin-text-secondary);
  margin: 0;
  text-transform: capitalize;
}

/* Modern Design Styles */
.seller-request-detail {
  min-height: 100vh;
  background: #f8fafc;
  padding: 1rem;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modern-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .modern-card-content {
    padding: 1rem;
  }

  .modern-profile-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

/* Admin User Profile Styles */
.admin-user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-border-radius);
}

.admin-user-avatar {
  width: 60px;
  height: 60px;
  background: var(--admin-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  flex-shrink: 0;
}

.admin-user-info {
  flex: 1;
}

.admin-user-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0 0 0.25rem 0;
}

.admin-user-email {
  color: var(--admin-text-secondary);
  margin: 0 0 0.5rem 0;
}

.admin-user-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Admin Company Styles */
.admin-company-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-company-logo {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.admin-company-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-color);
}

.admin-company-placeholder {
  width: 100%;
  height: 100%;
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-text-secondary);
  font-size: 2rem;
}

.admin-company-info {
  flex: 1;
}

.admin-company-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0 0 0.5rem 0;
}

.admin-company-description {
  color: var(--admin-text-secondary);
  line-height: 1.5;
  margin: 0;
  line-height: 1.4;
}

.admin-document-actions {
  margin-left: auto;
}

/* Card Variants */
.admin-card-danger {
  border-left: 4px solid var(--admin-danger);
}

.admin-card-danger .admin-card-title {
  color: var(--admin-danger);
}

/* Finance Section Styles */
.finance-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--admin-border-light);
}

.finance-section:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.finance-section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0 0 1rem 0;
}

.finance-section-title i {
  color: var(--admin-primary);
  font-size: 0.875rem;
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-border-radius);
  color: var(--admin-text-secondary);
  font-size: 0.875rem;
}

.security-notice i {
  color: var(--admin-success);
}

/* Applicant Profile Styles */
.applicant-profile-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--admin-bg-secondary) 0%, var(--admin-bg-tertiary) 100%);
  border-radius: var(--admin-border-radius-lg);
  border: 1px solid var(--admin-border-light);
  position: relative;
  overflow: hidden;
}

.applicant-profile-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary), var(--admin-secondary));
}

.applicant-avatar {
  width: 72px;
  height: 72px;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: var(--admin-shadow-md);
  border: 3px solid white;
}

.applicant-info {
  flex: 1;
}

.applicant-name {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--admin-text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.applicant-email {
  color: var(--admin-text-secondary);
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.applicant-email i {
  color: var(--admin-primary);
}

.applicant-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* Company Image Styles */
.company-image-section {
  margin-bottom: 2rem;
  text-align: center;
  padding: 1.5rem;
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-border-radius-lg);
  border: 1px solid var(--admin-border-light);
}

.company-image {
  max-width: 240px;
  max-height: 140px;
  object-fit: contain;
  border-radius: var(--admin-border-radius-lg);
  border: 2px solid var(--admin-border-light);
  box-shadow: var(--admin-shadow-sm);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.company-image:hover {
  transform: scale(1.02);
  box-shadow: var(--admin-shadow-md);
}

/* Enhanced Info Groups */
.admin-info-group {
  margin-bottom: 1.5rem;
}

.admin-info-group:last-child {
  margin-bottom: 0;
}

.admin-info-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.admin-info-value {
  font-size: 1rem;
  color: var(--admin-text-primary);
  line-height: 1.5;
}

.admin-info-value-multiline {
  background: var(--admin-bg-secondary);
  padding: 1rem;
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-light);
  white-space: pre-wrap;
  line-height: 1.6;
}

.admin-info-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.admin-link {
  color: var(--admin-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.2s ease;
}

.admin-link:hover {
  color: var(--admin-primary-dark);
  text-decoration: underline;
}

.admin-text-muted {
  color: var(--admin-text-muted);
  font-style: italic;
}

/* Schedule Table Styles */
.schedule-table-wrapper {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--admin-border-radius-lg);
  border: 1px solid var(--admin-border-light);
  background: white;
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.schedule-table th,
.schedule-table td {
  padding: 1rem 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admin-border-light);
}

.schedule-table th {
  background: linear-gradient(135deg, var(--admin-bg-secondary) 0%, var(--admin-bg-tertiary) 100%);
  font-weight: 700;
  color: var(--admin-text-primary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--admin-border-medium);
  position: sticky;
  top: 0;
  z-index: 1;
}

.day-column {
  width: 30%;
}

.status-column {
  width: 25%;
}

.time-column {
  width: 45%;
}

.schedule-row {
  transition: background-color 0.2s ease;
}

.schedule-row:hover {
  background: var(--admin-bg-tertiary);
}

.schedule-row:last-child td {
  border-bottom: none;
}

.day-cell {
  font-weight: 600;
}

.day-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.day-name {
  font-weight: 700;
  color: var(--admin-text-primary);
  font-size: 1rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--admin-border-radius-lg);
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  box-shadow: var(--admin-shadow-sm);
  transition: all 0.2s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--admin-shadow-md);
}

.status-open {
  background: linear-gradient(135deg, var(--admin-success), #22c55e);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-closed {
  background: linear-gradient(135deg, var(--admin-danger), #ef4444);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.time-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  background: var(--admin-bg-secondary);
  padding: 0.5rem 0.75rem;
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-light);
}

.time-start,
.time-end {
  color: var(--admin-primary);
  font-weight: 700;
}

.time-separator {
  color: var(--admin-text-secondary);
  font-weight: 400;
  margin: 0 0.25rem;
}

.time-closed {
  color: var(--admin-text-muted);
  font-style: italic;
  font-size: 0.9rem;
}

/* Fade Transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .admin-grid-3 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .admin-grid-3 {
    grid-template-columns: 1fr;
  }

  .admin-form-grid-2 {
    grid-template-columns: 1fr;
  }

  .admin-info-group-full {
    grid-column: span 1;
  }

  .admin-document-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .admin-document-actions {
    margin-left: 0;
    align-self: stretch;
  }
}
</style>
