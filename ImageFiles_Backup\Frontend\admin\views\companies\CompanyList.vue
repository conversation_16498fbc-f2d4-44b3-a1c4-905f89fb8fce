<template>
  <div class="admin-companies">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-building admin-page-icon"></i>
          Companies
        </h1>
        <p class="admin-page-subtitle">Manage and monitor company profiles</p>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Companies"
      search-placeholder="Search by name or description..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="companies"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div v-if="loading && isFirstLoad" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading companies...</p>
    </div>

    <!-- Error -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
      {{ error }}
    </div>

    <!-- No Data -->
    <div v-else-if="!loading && !items.length" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-building"></i>
      </div>
      <h3 class="admin-empty-title">No companies found</h3>
      <p class="admin-empty-text">Try adjusting your search criteria or filters</p>
    </div>

    <!-- Companies Table -->
    <div v-else class="admin-company-table">
      <div class="admin-table-container" :class="{ 'admin-table-loading': loading && !isFirstLoad }">
        <table class="admin-table">
          <thead class="admin-table-header">
            <tr>
              <th class="admin-table-th">Logo</th>
              <th class="admin-table-th">Name</th>
              <th class="admin-table-th">Contact</th>
              <th class="admin-table-th">Location</th>
              <th class="admin-table-th">Featured</th>
              <th class="admin-table-th">Actions</th>
            </tr>
          </thead>
          <tbody class="admin-table-body">
            <tr v-for="company in items" :key="company.id" class="admin-table-row">
              <td class="admin-table-td admin-logo-cell">
                <div class="admin-company-logo-wrapper">
                  <img
                    v-if="company.imageUrl && !company.imageError"
                    :src="company.imageUrl"
                    :alt="company.name + ' logo'"
                    class="admin-company-logo clickable"
                    loading="lazy"
                    @click="openImageViewer(company)"
                    @error="handleImageError($event, company)"
                    @load="handleImageLoad(company)"
                    title="Click to view full size">
                  <div v-else class="admin-logo-placeholder">
                    <i class="fas fa-building"></i>
                  </div>
                </div>
              </td>
              <td class="admin-table-td">
                <div class="admin-company-info">
                  <div class="admin-company-details">
                    <h4 class="admin-company-name">{{ company.name }}</h4>
                    <p class="admin-company-slug">{{ company.slug }}</p>
                  </div>
                </div>
              </td>
              <td class="admin-table-td">
                <div class="admin-company-contact">
                  <p class="admin-contact-email">{{ company.contactEmail }}</p>
                  <p class="admin-contact-phone">{{ company.contactPhone }}</p>
                </div>
              </td>
              <td class="admin-table-td">
                <div class="admin-company-location">
                  <p class="admin-location-city">{{ company.addressCity }}, {{ company.addressRegion }}</p>
                  <p class="admin-location-street">{{ company.addressStreet }}</p>
                </div>
              </td>
              <td class="admin-table-td">
                <span v-if="company.isFeatured" class="admin-badge admin-badge-primary">Featured</span>
                <span v-else class="admin-badge admin-badge-secondary">Regular</span>
              </td>
              <td class="admin-table-td">
                <div class="admin-table-actions">
                  <router-link
                    :to="{ name: 'AdminCompanyDetail', params: { id: company.id } }"
                    class="admin-btn admin-btn-secondary admin-btn-sm">
                    <i class="fas fa-eye"></i>
                    <span>View</span>
                  </router-link>
                  <router-link
                    :to="{ name: 'AdminCompanyEdit', params: { id: company.id } }"
                    class="admin-btn admin-btn-warning admin-btn-sm">
                    <i class="fas fa-edit"></i>
                    <span>Edit</span>
                  </router-link>
                  <button
                    class="admin-btn admin-btn-danger admin-btn-sm"
                    @click="deleteCompany(company.id)"
                    :disabled="actionLoading">
                    <i class="fas fa-trash"></i>
                    <span>Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Loading overlay -->
        <div v-if="loading && !isFirstLoad" class="table-loading-overlay">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-pulse"></i>
          </div>
        </div>
      </div>

        <!-- Pagination -->
        <Pagination
          :current-page="currentPage"
          :total-pages="totalPages"
          @page-changed="handlePageChange"
          v-if="totalPages > 1"
        />
      </div>
    </div>

    <!-- Logo Viewer Modal -->
    <div
      v-if="showImageModal"
      class="modal fade show d-block"
      @click="closeImageViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-building me-2"></i>
              {{ selectedCompany?.name }} - Company Logo
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeImageViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <div v-if="selectedCompany?.imageUrl" class="company-logo-viewer">
              <img
                :src="selectedCompany.imageUrl"
                :alt="`${selectedCompany.name} logo`"
                class="img-fluid rounded"
                style="max-height: 500px;"
              />
              <div class="mt-3">
                <h6>{{ selectedCompany.name }}</h6>
                <small class="text-muted">{{ selectedCompany.contactEmail || 'No email' }}</small>
              </div>
            </div>
            <div v-else class="no-logo-message">
              <i class="fas fa-building fa-3x text-muted mb-3"></i>
              <p>No logo available for this company</p>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { companiesService } from '@/admin/services/companies';
import { useAdminSearch } from '@/composables/useAdminSearch';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/admin/components/common/Pagination.vue';

// Action loading state
const actionLoading = ref(false);

// Image viewer state
const showImageModal = ref(false);
const selectedCompany = ref(null);

// Filter configuration
const filterFields = [
  {
    key: 'featured',
    label: 'Featured',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: 'all', label: 'All Companies' },
      { value: 'true', label: 'Featured Only' },
      { value: 'false', label: 'Regular Only' }
    ]
  },
  {
    key: 'hasImage',
    label: 'Logo',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: 'all', label: 'All' },
      { value: 'true', label: 'With Logo' },
      { value: 'false', label: 'No Logo' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    allOption: false, // Прибираємо "All" опцію
    options: [
      { value: 'ApprovedAt', label: 'Approved Date' },
      { value: 'Name', label: 'Name' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: companiesService.getCompanies,
  defaultFilters: {
    featured: 'all',
    sortBy: 'ApprovedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;

  // Handle sorting separately
  if (filterKey === 'sortBy' || filterKey === 'sortOrder') {
    // Update the composable's sort values
    if (filterKey === 'sortBy') {
      // The composable will handle this through the filters watcher
    }
  }
};

const handleResetFilters = () => {
  // Повертаємо фільтри до значень за замовчуванням
  filters.search = '';
  filters.featured = 'all';
  filters.sortBy = 'ApprovedAt';
  filters.sortOrder = 'desc';
};

// Company actions
const deleteCompany = async (id) => {
  if (!confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
    return;
  }

  actionLoading.value = true;
  try {
    await companiesService.deleteCompany(id);
    await fetchData(); // Refresh data using composable method
  } catch (err) {
    error.value = err.message || 'Failed to delete company';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods (removed unused methods)

// Image viewer methods
const openImageViewer = (company) => {
  selectedCompany.value = company;
  showImageModal.value = true;
};

const closeImageViewer = () => {
  showImageModal.value = false;
  selectedCompany.value = null;
};

const handleImageError = (event, company) => {
  // Mark image as error to prevent further loading attempts
  company.imageError = true;
  console.warn(`Failed to load image for company ${company.name}:`, company.imageUrl);
};

const handleImageLoad = (company) => {
  // Mark image as successfully loaded
  company.imageError = false;
};

// Lifecycle
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
@import '@/assets/css/admin/core/variables.css';

.admin-companies {
  padding: var(--admin-space-2xl);
  background: var(--admin-bg-primary);
  min-height: 100vh;
}



.admin-company-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  border: 1px solid var(--admin-border-light);
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
}

.admin-table-container {
  overflow-x: auto;
  position: relative;
}

.admin-table-container.admin-table-loading {
  opacity: 0.7;
  transition: opacity 0.3s;
}

.admin-company-info {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-company-logo {
  width: 48px;
  height: 48px;
  border-radius: var(--admin-radius-lg);
  object-fit: cover;
  border: 2px solid var(--admin-border-light);
  flex-shrink: 0;
}

.admin-company-details {
  flex: 1;
  min-width: 0;
}

.admin-company-name {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-xs) 0;
}

.admin-company-slug {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-500);
  margin: 0;
}

.admin-company-contact {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-contact-email {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-contact-phone {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  margin: 0;
}

.admin-company-location {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-location-city {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-location-street {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  margin: 0;
}

.admin-table-actions {
  display: flex;
  gap: var(--admin-space-xs);
  justify-content: flex-start;
}

@media (max-width: 768px) {
  .admin-table-actions {
    flex-direction: column;
  }

  .admin-company-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-sm);
  }

  .admin-company-logo {
    width: 40px;
    height: 40px;
  }
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

/* Logo styles */
.admin-logo-cell {
  padding: 8px;
  width: 80px;
}

.admin-company-logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--admin-border-light);
  background: #f8f9fa;
}

.admin-company-logo.clickable {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.admin-company-logo.clickable:hover {
  transform: scale(1.05);
  opacity: 0.9;
}

.admin-logo-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #6c757d;
  font-size: 24px;
}

/* Modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.company-logo-viewer {
  padding: 1rem;
}

.no-logo-message {
  padding: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-logo-cell {
    width: 60px;
  }

  .admin-company-logo-wrapper {
    width: 40px;
    height: 40px;
  }

  .admin-logo-placeholder {
    font-size: 16px;
  }
}
</style>
