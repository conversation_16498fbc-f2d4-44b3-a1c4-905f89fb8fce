﻿﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class UpdateSellerProductImageCommandHandler : IRequestHandler<UpdateSellerProductImageCommand, bool>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyUserRepository _companyUserRepository;

    public UpdateSellerProductImageCommandHandler(
        IProductImageRepository productImageRepository,
        IProductRepository productRepository,
        ICompanyUserRepository companyUserRepository)
    {
        _productImageRepository = productImageRepository;
        _productRepository = productRepository;
        _companyUserRepository = companyUserRepository;
    }

    public async Task<bool> Handle(UpdateSellerProductImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо компанії, до яких належить продавець
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.SellerId,
            cancellationToken: cancellationToken
        );

        if (!companyUsers.Any())
            return false;

        // Отримуємо ID компаній продавця
        var companyIds = companyUsers.Select(cu => cu.CompanyId).ToList();

        // Отримуємо продукт
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null || !companyIds.Contains(product.CompanyId))
            return false;

        // Отримуємо зображення продукту
        var productImage = await _productImageRepository.GetByIdAsync(request.ImageId, cancellationToken);
        if (productImage == null || productImage.ProductId != request.ProductId)
            return false;

        // Оновлюємо зображення продукту
        productImage.Update(
            image: request.Image != null ? new Url(request.Image) : null,
            order: request.Order
        );

        await _productImageRepository.UpdateAsync(productImage, cancellationToken);
        return true;
    }
}
