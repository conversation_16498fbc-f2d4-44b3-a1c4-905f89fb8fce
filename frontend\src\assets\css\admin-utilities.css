/* Admin Utilities
   This file contains utility classes and CSS variables for the admin panel.
   Consolidated from admin-readability.css and other files.
*/

/* ===== CSS Variables ===== */
:root {
  /* Font sizes */
  --font-xs: 0.75rem;    /* 12px */
  --font-sm: 0.875rem;   /* 14px */
  --font-base: 1rem;     /* 16px */
  --font-md: 1.125rem;   /* 18px */
  --font-lg: 1.25rem;    /* 20px */
  --font-xl: 1.5rem;     /* 24px */
  --font-2xl: 1.875rem;  /* 30px */
  --font-3xl: 2.25rem;   /* 36px */

  /* Font weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line heights */
  --line-tight: 1.25;
  --line-snug: 1.375;
  --line-normal: 1.5;
  --line-relaxed: 1.625;
  --line-loose: 2;

  /* Spacing */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */

  /* Colors with improved contrast */
  --text-dark: #1a1a1a;
  --text-medium: #333333;
  --text-light: #f8f9fa;
  --text-muted: #555555;
  --text-primary: #f3f4f6;
  --text-secondary: #9ca3af;

  /* Background colors */
  --bg-light: #ffffff;
  --bg-subtle: #f8f9fa;
  --bg-muted: #e9ecef;
  --card-bg: #1e293b;
  --darker-bg: #0f172a;
  --border-color: #374151;
  --accent-color: #3b82f6;

  /* Improved status colors for better contrast */
  --status-success-bg: #0d6e32;
  --status-success-text: #ffffff;
  --status-success-light-bg: #d1e7dd;
  --status-success-light-text: #0d6e32;

  --status-warning-bg: #cc8a00;
  --status-warning-text: #ffffff;
  --status-warning-light-bg: #fff3cd;
  --status-warning-light-text: #664400;

  --status-danger-bg: #b71c1c;
  --status-danger-text: #ffffff;
  --status-danger-light-bg: #f8d7da;
  --status-danger-light-text: #842029;

  --status-info-bg: #0a58ca;
  --status-info-text: #ffffff;
  --status-info-light-bg: #cfe2ff;
  --status-info-light-text: #0a3977;

  --status-primary-bg: #0d6efd;
  --status-primary-text: #ffffff;
  --status-primary-light-bg: #cfe2ff;
  --status-primary-light-text: #084298;
}

/* ===== Margin Utilities ===== */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1, 0.25rem) !important; }
.mb-2 { margin-bottom: var(--space-2, 0.5rem) !important; }
.mb-3 { margin-bottom: var(--space-3, 0.75rem) !important; }
.mb-4 { margin-bottom: var(--space-4, 1rem) !important; }
.mb-5 { margin-bottom: var(--space-5, 1.25rem) !important; }
.mb-6 { margin-bottom: var(--space-6, 1.5rem) !important; }
.mb-8 { margin-bottom: var(--space-8, 2rem) !important; }
.mb-10 { margin-bottom: var(--space-10, 2.5rem) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1, 0.25rem) !important; }
.mt-2 { margin-top: var(--space-2, 0.5rem) !important; }
.mt-3 { margin-top: var(--space-3, 0.75rem) !important; }
.mt-4 { margin-top: var(--space-4, 1rem) !important; }
.mt-5 { margin-top: var(--space-5, 1.25rem) !important; }
.mt-6 { margin-top: var(--space-6, 1.5rem) !important; }
.mt-8 { margin-top: var(--space-8, 2rem) !important; }
.mt-10 { margin-top: var(--space-10, 2.5rem) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--space-1, 0.25rem) !important; }
.ml-2 { margin-left: var(--space-2, 0.5rem) !important; }
.ml-3 { margin-left: var(--space-3, 0.75rem) !important; }
.ml-4 { margin-left: var(--space-4, 1rem) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--space-1, 0.25rem) !important; }
.mr-2 { margin-right: var(--space-2, 0.5rem) !important; }
.mr-3 { margin-right: var(--space-3, 0.75rem) !important; }
.mr-4 { margin-right: var(--space-4, 1rem) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--space-1, 0.25rem) !important; margin-right: var(--space-1, 0.25rem) !important; }
.mx-2 { margin-left: var(--space-2, 0.5rem) !important; margin-right: var(--space-2, 0.5rem) !important; }
.mx-3 { margin-left: var(--space-3, 0.75rem) !important; margin-right: var(--space-3, 0.75rem) !important; }
.mx-4 { margin-left: var(--space-4, 1rem) !important; margin-right: var(--space-4, 1rem) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--space-1, 0.25rem) !important; margin-bottom: var(--space-1, 0.25rem) !important; }
.my-2 { margin-top: var(--space-2, 0.5rem) !important; margin-bottom: var(--space-2, 0.5rem) !important; }
.my-3 { margin-top: var(--space-3, 0.75rem) !important; margin-bottom: var(--space-3, 0.75rem) !important; }
.my-4 { margin-top: var(--space-4, 1rem) !important; margin-bottom: var(--space-4, 1rem) !important; }

/* ===== Padding Utilities ===== */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1, 0.25rem) !important; }
.p-2 { padding: var(--space-2, 0.5rem) !important; }
.p-3 { padding: var(--space-3, 0.75rem) !important; }
.p-4 { padding: var(--space-4, 1rem) !important; }
.p-5 { padding: var(--space-5, 1.25rem) !important; }
.p-6 { padding: var(--space-6, 1.5rem) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--space-1, 0.25rem) !important; padding-right: var(--space-1, 0.25rem) !important; }
.px-2 { padding-left: var(--space-2, 0.5rem) !important; padding-right: var(--space-2, 0.5rem) !important; }
.px-3 { padding-left: var(--space-3, 0.75rem) !important; padding-right: var(--space-3, 0.75rem) !important; }
.px-4 { padding-left: var(--space-4, 1rem) !important; padding-right: var(--space-4, 1rem) !important; }
.px-5 { padding-left: var(--space-5, 1.25rem) !important; padding-right: var(--space-5, 1.25rem) !important; }
.px-6 { padding-left: var(--space-6, 1.5rem) !important; padding-right: var(--space-6, 1.5rem) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--space-1, 0.25rem) !important; padding-bottom: var(--space-1, 0.25rem) !important; }
.py-2 { padding-top: var(--space-2, 0.5rem) !important; padding-bottom: var(--space-2, 0.5rem) !important; }
.py-3 { padding-top: var(--space-3, 0.75rem) !important; padding-bottom: var(--space-3, 0.75rem) !important; }
.py-4 { padding-top: var(--space-4, 1rem) !important; padding-bottom: var(--space-4, 1rem) !important; }
.py-5 { padding-top: var(--space-5, 1.25rem) !important; padding-bottom: var(--space-5, 1.25rem) !important; }
.py-6 { padding-top: var(--space-6, 1.5rem) !important; padding-bottom: var(--space-6, 1.5rem) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--space-1, 0.25rem) !important; }
.pt-2 { padding-top: var(--space-2, 0.5rem) !important; }
.pt-3 { padding-top: var(--space-3, 0.75rem) !important; }
.pt-4 { padding-top: var(--space-4, 1rem) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--space-1, 0.25rem) !important; }
.pb-2 { padding-bottom: var(--space-2, 0.5rem) !important; }
.pb-3 { padding-bottom: var(--space-3, 0.75rem) !important; }
.pb-4 { padding-bottom: var(--space-4, 1rem) !important; }

/* ===== Flex Utilities ===== */
.d-flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }

.gap-1 { gap: var(--space-1, 0.25rem) !important; }
.gap-2 { gap: var(--space-2, 0.5rem) !important; }
.gap-3 { gap: var(--space-3, 0.75rem) !important; }
.gap-4 { gap: var(--space-4, 1rem) !important; }

/* ===== Text Utilities ===== */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-small { font-size: var(--font-sm, 0.875rem) !important; }
.text-large { font-size: var(--font-lg, 1.25rem) !important; }
.text-muted { color: var(--text-muted, #555555) !important; }
.text-bold { font-weight: var(--font-bold, 700) !important; }
.text-semibold { font-weight: var(--font-semibold, 600) !important; }
.text-medium { font-weight: var(--font-medium, 500) !important; }

.text-dark { color: #1a1a1a !important; }
.text-medium-dark { color: #333333 !important; }
.text-medium { color: #555555 !important; }
.text-visible { color: #1a1a1a !important; font-weight: 500 !important; }
.text-important { color: #1a1a1a !important; font-weight: 600 !important; }

/* ===== Display Utilities ===== */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

/* ===== Width Utilities ===== */
.w-auto { width: auto !important; }
.w-100 { width: 100% !important; }
.w-75 { width: 75% !important; }
.w-50 { width: 50% !important; }
.w-25 { width: 25% !important; }

/* ===== Height Utilities ===== */
.h-auto { height: auto !important; }
.h-100 { height: 100% !important; }

/* ===== Position Utilities ===== */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* ===== Visibility Utilities ===== */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* ===== Border Utilities ===== */
.border { border: 1px solid var(--border-color, #374151) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--border-color, #374151) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color, #374151) !important; }
.border-left { border-left: 1px solid var(--border-color, #374151) !important; }
.border-right { border-right: 1px solid var(--border-color, #374151) !important; }
.rounded { border-radius: 4px !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: 9999px !important; }

/* ===== Responsive Utilities ===== */
@media screen and (max-width: 768px) {
  .hide-mobile { display: none !important; }
  .show-mobile { display: block !important; }
  .text-center-mobile { text-align: center !important; }
  .w-100-mobile { width: 100% !important; }
}

@media screen and (min-width: 769px) and (max-width: 1023px) {
  .hide-tablet { display: none !important; }
  .show-tablet { display: block !important; }
}

@media screen and (min-width: 1024px) {
  .hide-desktop { display: none !important; }
  .show-desktop { display: block !important; }
}
