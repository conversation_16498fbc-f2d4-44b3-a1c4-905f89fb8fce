import api from '@/services/api';

export const settingsService = {
  async getGeneralSettings() {
    try {
      const response = await api.get('/api/admin/settings/general');
      return response.data;
    } catch (error) {
      console.error('Error fetching general settings:', error);
      // Return mock data if API fails
      return {
        siteName: 'Klondike',
        siteDescription: 'Your one-stop marketplace for all your needs',
        siteEmail: '<EMAIL>',
        sitePhone: '+****************',
        siteAddress: '123 Main St, New York, NY 10001',
        logo: 'https://via.placeholder.com/200x50',
        favicon: 'https://via.placeholder.com/32x32',
        currency: 'UAH',
        currencySymbol: '₴',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        timezone: 'Europe/Kiev',
        maintenanceMode: false,
        maintenanceMessage: 'We are currently performing maintenance. Please check back later.'
      };
    }
  },

  async updateGeneralSettings(settings) {
    try {
      const response = await api.put('/api/admin/settings/general', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating general settings:', error);
      // Return mock success response
      return {
        success: true,
        settings
      };
    }
  },

  async getPaymentSettings() {
    try {
      const response = await api.get('/api/admin/settings/payment');
      return response.data;
    } catch (error) {
      console.error('Error fetching payment settings:', error);
      // Return mock data if API fails
      return {
        paymentMethods: [
          {
            id: 'stripe',
            name: 'Stripe',
            enabled: true,
            testMode: true,
            config: {
              publishableKey: 'pk_test_*****',
              secretKey: 'sk_test_*****'
            }
          },
          {
            id: 'paypal',
            name: 'PayPal',
            enabled: true,
            testMode: true,
            config: {
              clientId: 'client_id_*****',
              clientSecret: 'client_secret_*****'
            }
          },
          {
            id: 'cod',
            name: 'Cash on Delivery',
            enabled: true,
            config: {
              fee: 5.00
            }
          }
        ],
        defaultCurrency: 'UAH',
        taxRate: 20,
        taxIncluded: true,
        invoicePrefix: 'INV-'
      };
    }
  },

  async updatePaymentSettings(settings) {
    try {
      const response = await api.put('/api/admin/settings/payment', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating payment settings:', error);
      // Return mock success response
      return {
        success: true,
        settings
      };
    }
  },

  async getShippingSettings() {
    try {
      const response = await api.get('/api/admin/settings/shipping');
      return response.data;
    } catch (error) {
      console.error('Error fetching shipping settings:', error);
      // Return mock data if API fails
      return {
        shippingMethods: [
          {
            id: 'standard',
            name: 'Standard Shipping',
            enabled: true,
            cost: 10.00,
            freeThreshold: 100.00,
            estimatedDays: '3-5'
          },
          {
            id: 'express',
            name: 'Express Shipping',
            enabled: true,
            cost: 25.00,
            freeThreshold: null,
            estimatedDays: '1-2'
          },
          {
            id: 'pickup',
            name: 'Local Pickup',
            enabled: true,
            cost: 0.00,
            freeThreshold: null,
            estimatedDays: '0'
          }
        ],
        defaultShippingMethod: 'standard',
        shippingOrigin: {
          address: '123 Main St',
          city: 'New York',
          state: 'NY',
          postalCode: '10001',
          country: 'USA'
        },
        shippingCountries: ['USA', 'Canada', 'Mexico', 'UK', 'Australia']
      };
    }
  },

  async updateShippingSettings(settings) {
    try {
      const response = await api.put('/api/admin/settings/shipping', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating shipping settings:', error);
      // Return mock success response
      return {
        success: true,
        settings
      };
    }
  },

  async getEmailSettings() {
    try {
      const response = await api.get('/api/admin/settings/email');
      return response.data;
    } catch (error) {
      console.error('Error fetching email settings:', error);
      // Return mock data if API fails
      return {
        smtpHost: 'smtp.example.com',
        smtpPort: 587,
        smtpUsername: '<EMAIL>',
        smtpPassword: '********',
        smtpEncryption: 'tls',
        fromEmail: '<EMAIL>',
        fromName: 'Klondike Marketplace',
        emailTemplates: [
          {
            id: 'welcome',
            name: 'Welcome Email',
            subject: 'Welcome to Klondike Marketplace',
            enabled: true
          },
          {
            id: 'order_confirmation',
            name: 'Order Confirmation',
            subject: 'Your Order #{{order_id}} has been confirmed',
            enabled: true
          },
          {
            id: 'shipping_confirmation',
            name: 'Shipping Confirmation',
            subject: 'Your Order #{{order_id}} has been shipped',
            enabled: true
          },
          {
            id: 'password_reset',
            name: 'Password Reset',
            subject: 'Reset your Klondike Marketplace password',
            enabled: true
          }
        ]
      };
    }
  },

  async updateEmailSettings(settings) {
    try {
      const response = await api.put('/api/admin/settings/email', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating email settings:', error);
      // Return mock success response
      return {
        success: true,
        settings
      };
    }
  },

  async testEmailSettings(settings) {
    try {
      const response = await api.post('/api/admin/settings/email/test', settings);
      return response.data;
    } catch (error) {
      console.error('Error testing email settings:', error);
      // Return mock error response
      throw new Error('Failed to send test email. Please check your settings and try again.');
    }
  },

  async getSystemInfo() {
    try {
      const response = await api.get('/api/admin/settings/system-info');
      return response.data;
    } catch (error) {
      console.error('Error fetching system info:', error);
      // Return mock data if API fails
      return {
        version: '1.0.0',
        phpVersion: '8.1.0',
        mysqlVersion: '8.0.27',
        serverOS: 'Linux',
        webServer: 'Nginx',
        diskSpace: {
          total: 50000000000, // 50 GB
          used: 25000000000, // 25 GB
          free: 25000000000 // 25 GB
        },
        memory: {
          total: 8000000000, // 8 GB
          used: 4000000000, // 4 GB
          free: 4000000000 // 4 GB
        },
        lastBackup: new Date(Date.now() - 1000 * 60 * 60 * 24),
        installedPlugins: [
          {
            name: 'Payment Gateway',
            version: '1.2.0',
            status: 'Active'
          },
          {
            name: 'SEO Optimizer',
            version: '2.1.0',
            status: 'Active'
          },
          {
            name: 'Security Suite',
            version: '1.5.0',
            status: 'Active'
          }
        ]
      };
    }
  },

  async testEmailSettings(settings) {
    try {
      const response = await api.post('/api/admin/settings/email/test', settings);
      return response.data;
    } catch (error) {
      console.error('Error testing email settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to test email settings');
    }
  },

  async getApiSettings() {
    try {
      const response = await api.get('/api/admin/settings/api');
      return response.data;
    } catch (error) {
      console.error('Error fetching API settings:', error);
      // Return mock data if API fails
      return {
        api_rate_limit: '60',
        webhooks_enabled: 'false'
      };
    }
  },

  async updateApiSettings(settings) {
    try {
      const response = await api.put('/api/admin/settings/api', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating API settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to update API settings');
    }
  },

  async getCategories() {
    try {
      const response = await api.get('/api/admin/settings/categories');
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error fetching setting categories:', error);
      return [
        { key: 'general', name: 'General Settings', description: 'Basic site configuration', icon: 'fas fa-cog' },
        { key: 'email', name: 'Email Settings', description: 'Email server and notification settings', icon: 'fas fa-envelope' },
        { key: 'payment', name: 'Payment Settings', description: 'Payment gateway configuration', icon: 'fas fa-credit-card' },
        { key: 'api', name: 'API Settings', description: 'API keys and external service configuration', icon: 'fas fa-code' }
      ];
    }
  },

  async validateSettings(settings) {
    try {
      const response = await api.post('/api/admin/settings/validate', { settings });
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error validating settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to validate settings');
    }
  },

  async backupSettings() {
    try {
      const response = await api.post('/api/admin/settings/backup');
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error creating settings backup:', error);
      throw new Error(error.response?.data?.message || 'Failed to create settings backup');
    }
  },

  async downloadBackup() {
    try {
      const backup = await this.backupSettings();

      const dataStr = JSON.stringify(backup, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `settings-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      return { success: true, message: 'Settings backup downloaded successfully' };
    } catch (error) {
      throw new Error(error.message || 'Failed to download settings backup');
    }
  },

  validateSetting(key, value, type = 'string') {
    const errors = [];

    if (!this.validateRequired(value)) {
      errors.push('This field is required');
      return errors;
    }

    switch (type) {
      case 'email':
        if (!this.validateEmail(value)) {
          errors.push('Please enter a valid email address');
        }
        break;
      case 'url':
        if (!this.validateUrl(value)) {
          errors.push('Please enter a valid URL');
        }
        break;
    }

    return errors;
  },

  isSensitiveSetting(key) {
    const sensitiveKeys = ['smtp_password', 'stripe_secret_key', 'paypal_secret', 'api_secret'];
    return sensitiveKeys.includes(key);
  }
};
