using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Review;

public class GetCategoryReviewsQueryHandler :
    PaginatedQueryHandler<GetCategoryReviewsQuery, Domain.Entities.Review, ReviewResponse>,
    IRequestHandler<GetCategoryReviewsQuery, PaginatedResponse<ReviewResponse>>
{
    private readonly IReviewRepository _repository;
    private readonly ICategoryRepository _categoryRepository;

    public GetCategoryReviewsQueryHandler(
        IReviewRepository repository,
        ICategoryRepository categoryRepository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _repository = repository;
        _categoryRepository = categoryRepository;
    }

    public async Task<PaginatedResponse<ReviewResponse>> Handle(GetCategoryReviewsQuery request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи існує категорія з таким slug
        var category = await _categoryRepository.GetBySlugAsync(request.CategorySlug, cancellationToken);
        if (category == null)
        {
            return CreateEmptyPaginatedResponse(request, $"api/categories/{request.CategorySlug}/reviews");
        }

        // Базовий фільтр - тільки відгуки для товарів цієї категорії
        Expression<Func<Domain.Entities.Review, bool>> filter = r => r.Product.CategoryId == category.Id;

        // Додаємо додатковий фільтр, якщо він є
        if (!string.IsNullOrEmpty(request.Filter))
        {
            var searchFilter = request.Filter.ToLower();
            filter = r => r.Product.CategoryId == category.Id && 
                         (r.Comment != null && r.Comment.ToLower().Contains(searchFilter) ||
                          r.Product.Name.ToLower().Contains(searchFilter));
        }

        // Отримуємо пагіновані дані
        var pagedResult = await _repository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy ?? "CreatedAt",
            descending: request.OrderBy == null ? true : request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Review, object>>[] {
                r => r.Product,
                r => r.Rating,
                r => r.User
            }
        );

        // Створюємо пагіновану відповідь
        return CreatePaginatedResponse(request, pagedResult, $"api/categories/{request.CategorySlug}/reviews");
    }
}
