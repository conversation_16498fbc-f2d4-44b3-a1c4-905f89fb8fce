<template>
  <div class="admin-page-container">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-page-header-content">
        <div class="admin-breadcrumb">
          <router-link to="/admin/addresses" class="admin-breadcrumb-item">
            <i class="fas fa-map-marker-alt"></i>
            Addresses
          </router-link>
          <span class="admin-breadcrumb-separator">/</span>
          <span class="admin-breadcrumb-item admin-breadcrumb-current">Address Details</span>
        </div>
        <h1 class="admin-page-title">Address Details</h1>
      </div>
      <div class="admin-page-actions">
        <button
          class="admin-btn admin-btn--secondary admin-btn--sm"
          @click="fetchAddress"
          :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-container" v-if="loading && !address.id">
      <div class="admin-loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="admin-loading-text">Loading address details...</p>
    </div>

    <!-- Error State -->
    <div class="admin-alert admin-alert--danger" v-else-if="error">
      <div class="admin-alert-content">
        <i class="fas fa-exclamation-circle"></i>
        <span>{{ error }}</span>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Address Details -->
    <div v-else-if="address.id" class="admin-content-grid">
      <!-- Main Content -->
      <div class="admin-content-main">
        <div class="admin-card">
          <div class="admin-card-header">
            <h2 class="admin-card-title">
              <i class="fas fa-info-circle"></i>
              Address Information
            </h2>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid admin-form-grid--2-col">
              <!-- Left Column -->
              <div class="admin-form-section">
                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-user"></i>
                    User
                  </label>
                  <div class="admin-info-value">
                    <router-link
                      v-if="address.userId"
                      :to="{ name: 'AdminUserDetail', params: { id: address.userId } }"
                      class="admin-link admin-link--primary">
                      {{ address.userName || 'Unknown User' }}
                    </router-link>
                    <span v-else class="admin-text-muted">No User Assigned</span>
                    <div v-if="address.userEmail" class="admin-info-meta">
                      {{ address.userEmail }}
                    </div>
                  </div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-chart-line"></i>
                    Activity Status
                  </label>
                  <div class="admin-info-value">
                    <span
                      class="admin-status-badge"
                      :class="`admin-status-badge--${getActivityStatus(address).color}`">
                      {{ getActivityStatus(address).text }}
                    </span>
                    <div class="admin-info-meta">
                      Orders: {{ address.ordersCount || 0 }} |
                      Last used: {{ formatLastUsed(address.lastUsedAt) }}
                    </div>
                  </div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-map"></i>
                    Region
                  </label>
                  <div class="admin-info-value">{{ address.addressVO?.region || address.region || 'N/A' }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-city"></i>
                    City
                  </label>
                  <div class="admin-info-value">{{ address.addressVO?.city || address.city || 'N/A' }}</div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="admin-form-section">
                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-road"></i>
                    Street
                  </label>
                  <div class="admin-info-value">{{ address.addressVO?.street || address.street || 'N/A' }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-mail-bulk"></i>
                    Postal Code
                  </label>
                  <div class="admin-info-value">{{ address.addressVO?.postalCode || address.postalCode || 'N/A' }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    <i class="fas fa-calendar-plus"></i>
                    Created At
                  </label>
                  <div class="admin-info-value">{{ formatDateTime(address.createdAt) }}</div>
                </div>

                <div class="admin-form-group" v-if="address.updatedAt">
                  <label class="admin-form-label">
                    <i class="fas fa-calendar-edit"></i>
                    Updated At
                  </label>
                  <div class="admin-info-value">{{ formatDateTime(address.updatedAt) }}</div>
                </div>
              </div>
            </div>

            <!-- Full Address Display -->
            <div class="admin-form-group admin-form-group--full">
              <label class="admin-form-label">
                <i class="fas fa-map-marker-alt"></i>
                Full Address
              </label>
              <div class="admin-address-preview">
                <div class="admin-address-preview-content">
                  {{ formatFullAddress(address) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="admin-content-sidebar">
        <!-- Actions Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-cogs"></i>
              Actions
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-action-buttons">
              <button
                class="admin-btn admin-btn-warning admin-btn-block"
                @click="editAddress"
                :disabled="actionLoading">
                <i class="fas fa-edit"></i>
                Edit Address
              </button>
              <button
                class="admin-btn admin-btn-danger admin-btn-block"
                @click="showDeleteModal = true"
                :disabled="actionLoading">
                <i class="fas fa-trash"></i>
                Delete Address
              </button>
            </div>
          </div>
        </div>

        <!-- Information Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-info"></i>
              Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-info-list">
              <div class="admin-info-item">
                <label class="admin-info-label">Address ID</label>
                <div class="admin-info-value admin-info-value--mono">{{ address.id }}</div>
              </div>
              <div class="admin-info-item" v-if="address.userId">
                <label class="admin-info-label">User ID</label>
                <div class="admin-info-value admin-info-value--mono">{{ address.userId }}</div>
              </div>
              <div class="admin-info-item">
                <label class="admin-info-label">Status</label>
                <span class="admin-status-badge admin-status-badge--success">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Modal -->
    <div class="admin-modal" v-if="showDeleteModal" @click.self="showDeleteModal = false">
      <div class="admin-modal-content">
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">
            <i class="fas fa-exclamation-triangle"></i>
            Delete Address
          </h3>
          <button class="admin-modal-close" @click="showDeleteModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-body">
          <p class="admin-modal-text">
            Are you sure you want to delete this address? This action cannot be undone.
          </p>
          <div class="admin-address-preview admin-address-preview--warning">
            <div class="admin-address-preview-content">
              {{ formatFullAddress(address) }}
            </div>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button
            class="admin-btn admin-btn-danger"
            @click="deleteAddress"
            :disabled="actionLoading">
            <i class="fas fa-trash" :class="{ 'fa-spin': actionLoading }"></i>
            {{ actionLoading ? 'Deleting...' : 'Delete Address' }}
          </button>
          <button class="admin-btn admin-btn-secondary" @click="showDeleteModal = false">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { addressesService } from '@/admin/services/addresses';

const route = useRoute();
const router = useRouter();

// Reactive data
const address = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showDeleteModal = ref(false);

// Methods
const fetchAddress = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const response = await addressesService.getAddressById(route.params.id);
    address.value = response;
  } catch (err) {
    error.value = err.message || 'Failed to load address details';
  } finally {
    loading.value = false;
  }
};

const editAddress = () => {
  router.push({ name: 'AdminAddressEdit', params: { id: address.value.id } });
};

const deleteAddress = async () => {
  actionLoading.value = true;
  try {
    await addressesService.deleteAddress(address.value.id);
    router.push({ name: 'AdminAddresses' });
  } catch (err) {
    error.value = err.message || 'Failed to delete address';
  } finally {
    actionLoading.value = false;
    showDeleteModal.value = false;
  }
};

// Utility methods
const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString('uk-UA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatFullAddress = (addr) => {
  if (!addr) return 'N/A';

  const parts = [];
  const addressData = addr.addressVO || addr;

  if (addressData.street) parts.push(addressData.street);
  if (addressData.city) parts.push(addressData.city);
  if (addressData.region) parts.push(addressData.region);
  if (addressData.postalCode) parts.push(addressData.postalCode);

  return parts.length > 0 ? parts.join(', ') : 'N/A';
};

const getActivityStatus = (address) => {
  return addressesService.getActivityStatus(address);
};

const formatLastUsed = (lastUsedAt) => {
  return addressesService.formatLastUsed(lastUsedAt);
};

// Lifecycle
onMounted(() => {
  fetchAddress();
});
</script>

<style scoped>
/* Import admin styles */
@import '@/assets/css/admin/admin.css';

/* Address preview component */
.admin-address-preview {
  background: var(--admin-gray-50);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  margin-top: var(--admin-space-sm);
}

.admin-address-preview--warning {
  background: var(--admin-warning-bg);
  border-color: var(--admin-warning);
  color: var(--admin-warning-dark);
}

.admin-address-preview-content {
  font-weight: var(--admin-font-semibold);
  font-size: var(--admin-text-base);
  line-height: var(--admin-line-height-relaxed);
}

/* Info components */
.admin-info-value {
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
  margin-top: var(--admin-space-xs);
}

.admin-info-value--mono {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  display: inline-block;
}

.admin-info-meta {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-600);
  margin-top: var(--admin-space-xs);
}

/* Action buttons */
.admin-action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

/* Info list */
.admin-info-list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-info-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-700);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-info-label i {
  color: var(--admin-gray-500);
  width: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-content-grid {
    grid-template-columns: 1fr;
  }

  .admin-form-grid--2-col {
    grid-template-columns: 1fr;
  }
}
</style>
