using Marketplace.Application.Queries.Product;
using Marketplace.Application.Queries.User;
using Marketplace.Application.Requests.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Sellers;

[ApiController]
[Route("api/sellers")]
public class SellerPublicController : BasicApiController
{
    private readonly IMediator _mediator;

    public SellerPublicController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Отримати товари конкретного продавця (публічний доступ)
    /// </summary>
    /// <param name="sellerId">ID продавця</param>
    /// <param name="request">Параметри пагінації та фільтрації</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Пагінований список товарів продавця</returns>
    [HttpGet("{sellerId}/products")]
    public async Task<IActionResult> GetSellerProducts(
        [FromRoute] Guid sellerId,
        [FromQuery] PaginationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Перевіряємо, чи існує користувач і чи є він продавцем
            var userQuery = new GetUserQuery(sellerId);
            var user = await _mediator.Send(userQuery, cancellationToken);
            
            if (user == null)
            {
                return NotFound(ApiResponse.Failure("Продавця не знайдено."));
            }

            // Перевіряємо, чи користувач має роль продавця
            if (user.Role != Role.Seller && user.Role != Role.Admin && user.Role != Role.Moderator)
            {
                return BadRequest(ApiResponse.Failure("Користувач не є продавцем."));
            }

            // Отримуємо тільки схвалені товари продавця
            var query = new GetSellerProductsQuery(
                sellerId,
                request.Filter,
                request.OrderBy,
                request.Descending,
                request.Page,
                request.PageSize);
            
            var response = await _mediator.Send(query, cancellationToken);

            return Ok(ApiResponse<PaginatedResponse<ProductResponse>>.SuccessWithData(response));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка отримання товарів продавця: {ex.Message}"));
        }
    }

    /// <summary>
    /// Отримати публічний профіль продавця
    /// </summary>
    /// <param name="sellerId">ID продавця</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Публічна інформація про продавця</returns>
    [HttpGet("{sellerId}/profile")]
    public async Task<IActionResult> GetSellerProfile(
        [FromRoute] Guid sellerId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetUserQuery(sellerId);
            var response = await _mediator.Send(query, cancellationToken);

            if (response == null)
            {
                return NotFound(ApiResponse.Failure("Профіль продавця не знайдено."));
            }

            // Перевіряємо, чи користувач має роль продавця
            if (response.Role != Role.Seller && response.Role != Role.Admin && response.Role != Role.Moderator)
            {
                return BadRequest(ApiResponse.Failure("Користувач не є продавцем."));
            }

            // Створюємо публічну версію профілю (без чутливих даних)
            var publicProfile = new
            {
                response.Id,
                response.Username,
                response.FirstName,
                response.LastName,
                response.AvatarUrl,
                response.Role,
                response.IsApproved,
                response.ApprovedAt,
                response.LastSeenAt,
                // Не включаємо Email, Phone та інші чутливі дані
            };

            return Ok(ApiResponse<object>.SuccessWithData(publicProfile));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка отримання профілю продавця: {ex.Message}"));
        }
    }

    /// <summary>
    /// Отримати статистику продавця (публічна інформація)
    /// </summary>
    /// <param name="sellerId">ID продавця</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Публічна статистика продавця</returns>
    [HttpGet("{sellerId}/stats")]
    public async Task<IActionResult> GetSellerStats(
        [FromRoute] Guid sellerId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Перевіряємо, чи існує продавець
            var userQuery = new GetUserQuery(sellerId);
            var user = await _mediator.Send(userQuery, cancellationToken);
            
            if (user == null)
            {
                return NotFound(ApiResponse.Failure("Продавця не знайдено."));
            }

            if (user.Role != Role.Seller && user.Role != Role.Admin && user.Role != Role.Moderator)
            {
                return BadRequest(ApiResponse.Failure("Користувач не є продавцем."));
            }

            // Отримуємо товари продавця для підрахунку статистики
            var productsQuery = new GetSellerProductsQuery(
                sellerId,
                null, // без фільтра
                null, // без сортування
                false,
                1,
                1000); // отримуємо багато товарів для статистики
            
            var products = await _mediator.Send(productsQuery, cancellationToken);

            // Підраховуємо статистику
            var stats = new
            {
                TotalProducts = products.Total,
                ApprovedProducts = products.Data.Count(p => p.Status == ProductStatus.Approved),
                PendingProducts = products.Data.Count(p => p.Status == ProductStatus.Pending),
                RejectedProducts = products.Data.Count(p => p.Status == ProductStatus.Rejected),
                AveragePrice = products.Data.Any() ? products.Data.Average(p => p.PriceAmount) : 0,
                MemberSince = user.ApprovedAt?.ToString("yyyy-MM-dd") ?? "N/A",
                LastSeen = user.LastSeenAt.ToString("yyyy-MM-dd HH:mm")
            };

            return Ok(ApiResponse<object>.SuccessWithData(stats));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка отримання статистики продавця: {ex.Message}"));
        }
    }
}
