<template>
  <div class="api-test-page">
    <h1>API Test Page</h1>
    
    <div class="test-section">
      <h2>Companies API Test</h2>
      <button @click="testCompaniesAPI" :disabled="loadingCompanies">
        {{ loadingCompanies ? 'Loading...' : 'Test Companies API' }}
      </button>
      <div v-if="companiesResult" class="result">
        <h3>Companies Result:</h3>
        <pre>{{ JSON.stringify(companiesResult, null, 2) }}</pre>
      </div>
      <div v-if="companiesError" class="error">
        <h3>Companies Error:</h3>
        <pre>{{ companiesError }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>Categories API Test</h2>
      <button @click="testCategoriesAPI" :disabled="loadingCategories">
        {{ loadingCategories ? 'Loading...' : 'Test Categories API' }}
      </button>
      <div v-if="categoriesResult" class="result">
        <h3>Categories Result:</h3>
        <pre>{{ JSON.stringify(categoriesResult, null, 2) }}</pre>
      </div>
      <div v-if="categoriesError" class="error">
        <h3>Categories Error:</h3>
        <pre>{{ categoriesError }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>Product API Test</h2>
      <input v-model="productId" placeholder="Enter Product ID" />
      <button @click="testProductAPI" :disabled="loadingProduct">
        {{ loadingProduct ? 'Loading...' : 'Test Product API' }}
      </button>
      <div v-if="productResult" class="result">
        <h3>Product Result:</h3>
        <pre>{{ JSON.stringify(productResult, null, 2) }}</pre>
      </div>
      <div v-if="productError" class="error">
        <h3>Product Error:</h3>
        <pre>{{ productError }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { productsService } from '../../services/products.js';

// Reactive data
const loadingCompanies = ref(false);
const companiesResult = ref(null);
const companiesError = ref(null);

const loadingCategories = ref(false);
const categoriesResult = ref(null);
const categoriesError = ref(null);

const loadingProduct = ref(false);
const productResult = ref(null);
const productError = ref(null);
const productId = ref('');

// Test methods
const testCompaniesAPI = async () => {
  try {
    loadingCompanies.value = true;
    companiesError.value = null;
    
    console.log('Testing companies API...');
    const result = await productsService.getCompanies({ search: '', pageSize: 10 });
    console.log('Companies API result:', result);
    
    companiesResult.value = result;
  } catch (error) {
    console.error('Companies API error:', error);
    companiesError.value = error.message || 'Unknown error';
  } finally {
    loadingCompanies.value = false;
  }
};

const testCategoriesAPI = async () => {
  try {
    loadingCategories.value = true;
    categoriesError.value = null;
    
    console.log('Testing categories API...');
    const result = await productsService.getCategories({ search: '', pageSize: 10 });
    console.log('Categories API result:', result);
    
    categoriesResult.value = result;
  } catch (error) {
    console.error('Categories API error:', error);
    categoriesError.value = error.message || 'Unknown error';
  } finally {
    loadingCategories.value = false;
  }
};

const testProductAPI = async () => {
  if (!productId.value) {
    productError.value = 'Please enter a product ID';
    return;
  }
  
  try {
    loadingProduct.value = true;
    productError.value = null;
    
    console.log('Testing product API for ID:', productId.value);
    const result = await productsService.getProductById(productId.value);
    console.log('Product API result:', result);
    
    productResult.value = result;
  } catch (error) {
    console.error('Product API error:', error);
    productError.value = error.message || 'Unknown error';
  } finally {
    loadingProduct.value = false;
  }
};
</script>

<style scoped>
.api-test-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

button {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 1rem;
}

button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 1rem;
  width: 200px;
}

.result {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.error {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}
</style>
