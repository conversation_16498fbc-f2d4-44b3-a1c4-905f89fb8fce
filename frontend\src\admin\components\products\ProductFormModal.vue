<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">{{ product ? 'Edit Product' : 'Add Product' }}</p>
        <button class="delete" aria-label="close" @click="$emit('close')"></button>
      </header>
      <section class="modal-card-body">
        <form @submit.prevent="submitForm">
          <!-- Basic Info -->
          <div class="field">
            <label class="label">Name</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                placeholder="Product name" 
                v-model="form.name"
                required>
            </div>
          </div>
          
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea 
                class="textarea" 
                placeholder="Product description" 
                v-model="form.description"
                rows="3"></textarea>
            </div>
          </div>
          
          <div class="columns">
            <div class="column is-6">
              <div class="field">
                <label class="label">Category</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.categoryId" required>
                      <option value="" disabled>Select category</option>
                      <option v-for="category in categories" :key="category.id" :value="category.id">
                        {{ category.name }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-6">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.status" required>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="out_of_stock">Out of Stock</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pricing and Inventory -->
          <div class="columns">
            <div class="column is-4">
              <div class="field">
                <label class="label">Price ($)</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="number" 
                    step="0.01" 
                    min="0" 
                    placeholder="0.00" 
                    v-model="form.price"
                    required>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Sale Price ($)</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="number" 
                    step="0.01" 
                    min="0" 
                    placeholder="0.00" 
                    v-model="form.salePrice">
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Stock</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="number" 
                    min="0" 
                    placeholder="0" 
                    v-model="form.stock"
                    required>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Image URL -->
          <div class="field">
            <label class="label">Image URL</label>
            <div class="control">
              <input 
                class="input" 
                type="url" 
                placeholder="https://example.com/image.jpg" 
                v-model="form.imageUrl">
            </div>
          </div>
          
          <!-- SKU and Slug -->
          <div class="columns">
            <div class="column is-6">
              <div class="field">
                <label class="label">SKU</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    placeholder="SKU-123" 
                    v-model="form.sku">
                </div>
              </div>
            </div>
            <div class="column is-6">
              <div class="field">
                <label class="label">Slug</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    placeholder="product-slug" 
                    v-model="form.slug">
                </div>
              </div>
            </div>
          </div>
        </form>
      </section>
      <footer class="modal-card-foot">
        <button class="button is-primary" @click="submitForm" :disabled="isSubmitting">
          <span v-if="isSubmitting">
            <span class="icon">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span>Saving...</span>
          </span>
          <span v-else>Save</span>
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { productsService } from '@/admin/services/products';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  product: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'save']);

// Form state
const form = reactive({
  id: null,
  name: '',
  description: '',
  categoryId: '',
  price: 0,
  salePrice: null,
  stock: 0,
  status: 'active',
  imageUrl: '',
  sku: '',
  slug: ''
});

// Submission state
const isSubmitting = ref(false);

// Categories for dropdown
const categories = ref([]);

// Fetch categories
const fetchCategories = async () => {
  try {
    const response = await productsService.getCategories();
    categories.value = response;
  } catch (error) {
    console.error('Error fetching categories:', error);
  }
};

// Submit form
const submitForm = async () => {
  isSubmitting.value = true;
  
  try {
    // Create a clean form object
    const productData = { ...form };
    
    // Convert string numbers to actual numbers
    productData.price = parseFloat(productData.price);
    productData.stock = parseInt(productData.stock);
    
    if (productData.salePrice) {
      productData.salePrice = parseFloat(productData.salePrice);
    }
    
    emit('save', productData);
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form
const resetForm = () => {
  form.id = null;
  form.name = '';
  form.description = '';
  form.categoryId = '';
  form.price = 0;
  form.salePrice = null;
  form.stock = 0;
  form.status = 'active';
  form.imageUrl = '';
  form.sku = '';
  form.slug = '';
};

// Watch for product changes to update form
watch(() => props.product, (newProduct) => {
  if (newProduct) {
    // Populate form with product data
    Object.keys(form).forEach(key => {
      if (key in newProduct) {
        form[key] = newProduct[key];
      }
    });
  } else {
    resetForm();
  }
}, { immediate: true });

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});

onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
.modal-card {
  width: 80%;
  max-width: 800px;
}
</style>
