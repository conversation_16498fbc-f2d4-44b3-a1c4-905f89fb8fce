<template>
  <div class="rating-edit">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Edit Rating</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link :to="{ name: 'AdminRatings' }" class="button">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Ratings</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered">
      <div class="spinner"></div>
      <p>Loading rating...</p>
    </div>

    <div v-else-if="error" class="notification is-danger">
      {{ error }}
    </div>

    <div v-else-if="rating" class="card">
      <div class="card-content">
        <form @submit.prevent="updateRating">
          <div class="columns">
            <div class="column is-6">
              <div class="field">
                <label class="label">Product</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    :value="rating.productName" 
                    readonly
                  >
                </div>
              </div>
            </div>
            <div class="column is-6">
              <div class="field">
                <label class="label">User</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    :value="rating.userName || rating.userEmail" 
                    readonly
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="columns">
            <div class="column is-4">
              <div class="field">
                <label class="label">Service Rating</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.service">
                      <option value="1">1 Star</option>
                      <option value="2">2 Stars</option>
                      <option value="3">3 Stars</option>
                      <option value="4">4 Stars</option>
                      <option value="5">5 Stars</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Delivery Time Rating</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.deliveryTime">
                      <option value="1">1 Star</option>
                      <option value="2">2 Stars</option>
                      <option value="3">3 Stars</option>
                      <option value="4">4 Stars</option>
                      <option value="5">5 Stars</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Accuracy Rating</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.accuracy">
                      <option value="1">1 Star</option>
                      <option value="2">2 Stars</option>
                      <option value="3">3 Stars</option>
                      <option value="4">4 Stars</option>
                      <option value="5">5 Stars</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <button 
                type="submit" 
                class="button is-primary" 
                :class="{ 'is-loading': saving }"
                :disabled="saving"
              >
                Update Rating
              </button>
              <router-link 
                :to="{ name: 'AdminRatings' }" 
                class="button ml-3"
              >
                Cancel
              </router-link>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import api from '../../../services/api';

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const saving = ref(false);
const error = ref(null);
const rating = ref(null);

const form = ref({
  service: 1,
  deliveryTime: 1,
  accuracy: 1
});

const ratingId = computed(() => route.params.id);

const fetchRating = async () => {
  try {
    loading.value = true;
    error.value = null;
    
    const response = await api.get(`/admin/ratings/${ratingId.value}`);
    
    if (response.data.success) {
      rating.value = response.data.data;
      form.value = {
        service: rating.value.service,
        deliveryTime: rating.value.deliveryTime,
        accuracy: rating.value.accuracy
      };
    } else {
      error.value = response.data.message || 'Failed to load rating';
    }
  } catch (err) {
    console.error('Error fetching rating:', err);
    error.value = 'Failed to load rating';
  } finally {
    loading.value = false;
  }
};

const updateRating = async () => {
  try {
    saving.value = true;
    
    const response = await api.put(`/admin/ratings/${ratingId.value}`, form.value);
    
    if (response.data.success) {
      alert('Rating updated successfully');
      router.push({ name: 'AdminRatings' });
    } else {
      alert(response.data.message || 'Failed to update rating');
    }
  } catch (err) {
    console.error('Error updating rating:', err);
    alert('Failed to update rating');
  } finally {
    saving.value = false;
  }
};

onMounted(() => {
  fetchRating();
});
</script>

<style scoped>
.rating-edit {
  padding: 1.5rem;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.title {
  color: var(--text-primary);
}

.label {
  color: var(--text-primary);
}

.input, .select select {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.button.is-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.button.is-primary:hover {
  background-color: var(--primary-color-dark);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}
</style>
