import{_,g as u,c as i,a as t,b as n,k as p,d as v,t as c,x as E,D as T,o as m}from"./index-L-hJxM_5.js";import{E as g,a as y}from"./EnhancedProductAttributesEditor-lcyAKYjd.js";import{E as h}from"./EnhancedCategorySelector-v7wVF3eo.js";import"./products-Bpq90UOX.js";const x={class:"enhanced-components-test"},I={class:"test-sections"},w={class:"test-section"},k={class:"test-form"},N={key:0,class:"test-result"},U={class:"test-section"},A={class:"test-form"},D={key:0,class:"test-result"},P={class:"test-section"},B={class:"test-form"},M={key:0,class:"test-result"},O={class:"test-section"},q={class:"test-form"},J={class:"form-row"},L={class:"form-col"},j={class:"form-col"},z={class:"form-row"},G={class:"form-col"},R={class:"test-result"},X={__name:"EnhancedComponentsTest",setup(F){const a=u(null),d=u(null),r=u({Color:["Red","Blue","Green"],Size:["S","M","L","XL"],Material:["Cotton"]}),s=u({name:"",companyId:null,categoryId:null,attributes:{}}),C=l=>{console.log("Company changed:",l)},V=l=>{console.log("Company selected:",l)},f=l=>{console.log("Category changed:",l)},S=l=>{console.log("Category selected:",l)},b=l=>{console.log("Attributes changed:",l)};return(l,e)=>(m(),i("div",x,[e[16]||(e[16]=t("div",{class:"admin-page-header"},[t("h1",{class:"admin-page-title"},"Enhanced Components Test"),t("p",{class:"admin-page-description"},"Test the new enhanced product components")],-1)),t("div",I,[t("div",w,[e[8]||(e[8]=t("h2",null,"Enhanced Company Selector",-1)),t("div",k,[n(g,{modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=o=>a.value=o),label:"Test Company Selector",placeholder:"Search and select a company...","help-text":"This selector shows all companies with search functionality",onChange:C,onSelect:V},null,8,["modelValue"]),a.value?(m(),i("div",N,[e[7]||(e[7]=t("strong",null,"Selected Company ID:",-1)),v(" "+c(a.value),1)])):p("",!0)])]),t("div",U,[e[10]||(e[10]=t("h2",null,"Enhanced Category Selector",-1)),t("div",A,[n(h,{modelValue:d.value,"onUpdate:modelValue":e[1]||(e[1]=o=>d.value=o),label:"Test Category Selector",placeholder:"Search and select a category...","help-text":"This selector shows all categories with hierarchy",onChange:f,onSelect:S},null,8,["modelValue"]),d.value?(m(),i("div",D,[e[9]||(e[9]=t("strong",null,"Selected Category ID:",-1)),v(" "+c(d.value),1)])):p("",!0)])]),t("div",P,[e[12]||(e[12]=t("h2",null,"Enhanced Product Attributes Editor",-1)),t("div",B,[n(y,{modelValue:r.value,"onUpdate:modelValue":[e[2]||(e[2]=o=>r.value=o),b]},null,8,["modelValue"]),Object.keys(r.value).length>0?(m(),i("div",M,[e[11]||(e[11]=t("strong",null,"Current Attributes:",-1)),t("pre",null,c(JSON.stringify(r.value,null,2)),1)])):p("",!0)])]),t("div",O,[e[15]||(e[15]=t("h2",null,"Combined Test",-1)),t("div",q,[t("div",J,[t("div",L,[n(g,{modelValue:s.value.companyId,"onUpdate:modelValue":e[3]||(e[3]=o=>s.value.companyId=o),label:"Company",required:""},null,8,["modelValue"])]),t("div",j,[n(h,{modelValue:s.value.categoryId,"onUpdate:modelValue":e[4]||(e[4]=o=>s.value.categoryId=o),label:"Category",required:""},null,8,["modelValue"])])]),t("div",z,[t("div",G,[e[13]||(e[13]=t("label",{class:"admin-form-label"},"Product Name",-1)),E(t("input",{"onUpdate:modelValue":e[5]||(e[5]=o=>s.value.name=o),type:"text",class:"admin-form-input",placeholder:"Enter product name"},null,512),[[T,s.value.name]])])]),n(y,{modelValue:s.value.attributes,"onUpdate:modelValue":e[6]||(e[6]=o=>s.value.attributes=o)},null,8,["modelValue"]),t("div",R,[e[14]||(e[14]=t("strong",null,"Test Product Data:",-1)),t("pre",null,c(JSON.stringify(s.value,null,2)),1)])])])])]))}},Y=_(X,[["__scopeId","data-v-3924cadf"]]);export{Y as default};
