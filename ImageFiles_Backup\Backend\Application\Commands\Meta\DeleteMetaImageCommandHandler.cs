﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Meta;

/// <summary>
/// Обробник команди для завантаження зображення для Meta
/// </summary>
public class DeleteMetaImageCommandHandler : IRequestHandler<DeleteMetaImageCommand, bool>
{
    private readonly ICategoryRepository _categoryRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<DeleteMetaImageCommandHandler> _logger;

    public DeleteMetaImageCommandHandler(
        ICategoryRepository categoryRepository,
        IProductRepository productRepository,
        ICompanyRepository companyRepository,
        IFileService fileService,
        ILogger<DeleteMetaImageCommandHandler> logger)
    {
        _categoryRepository = categoryRepository;
        _productRepository = productRepository;
        _companyRepository = companyRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteMetaImageCommand request, CancellationToken cancellationToken)
    {
        // Оновлюємо Meta в залежності від типу сутності
        switch (request.EntityType.ToLower())
        {
            case "category":
                await DeleteCategoryMetaMetaImage(request.Id, cancellationToken);
                break;
            case "product":
                await DeleteProductMetaMetaImage(request.Id, cancellationToken);
                break;
            case "company":
                await DeleteCompanyMetaMetaImage(request.Id, cancellationToken);
                break;
            default:
                throw new InvalidOperationException($"Невідомий тип сутності: {request.EntityType}");
        }

        // Повертаємо результат
        return true;
    }

    private async Task DeleteCategoryMetaMetaImage(Guid categoryId, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Категорію з ID {categoryId} не знайдено.");

        // Створюємо нову Meta з оновленим зображенням

        await _fileService.DeleteFileAsync(
            category.Meta.Image!.Value,
            cancellationToken);

        var updatedMeta = new Domain.ValueObjects.Meta(
            category.Meta.Title,
            category.Meta.Description,
            null);

        // Оновлюємо Meta
        category.UpdateMeta(updatedMeta);

        // Зберігаємо зміни
        await _categoryRepository.UpdateAsync(category, cancellationToken);
    }

    private async Task DeleteProductMetaMetaImage(Guid productId, CancellationToken cancellationToken)
    {
        var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
        if (product == null)
            throw new InvalidOperationException($"Продукт з ID {productId} не знайдено.");

        // Перевіряємо, чи є метазображення для видалення
        if (product.Meta.Image != null)
        {
            await _fileService.DeleteFileAsync(
                product.Meta.Image.Value,
                cancellationToken);
        }

        var updatedMeta = new Domain.ValueObjects.Meta(
            product.Meta.Title,
            product.Meta.Description,
            null);

        // Оновлюємо Meta
        product.UpdateMeta(updatedMeta);

        // Зберігаємо зміни
        await _productRepository.UpdateAsync(product, cancellationToken);
    }

    private async Task DeleteCompanyMetaMetaImage(Guid companyId, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {companyId} не знайдено.");

        // Спочатку видаляємо файл з файлової системи (якщо він існує)
        if (company.Meta.Image != null)
        {
            try
            {
                await _fileService.DeleteFileAsync(company.Meta.Image.Value, cancellationToken);
                _logger.LogInformation($"Файл мета-зображення {company.Meta.Image.Value} успішно видалено.");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Не вдалося видалити файл мета-зображення {company.Meta.Image.Value}: {ex.Message}");
                // Продовжуємо виконання, щоб очистити посилання в БД
            }
        }

        // Створюємо нову Meta з очищеним зображенням
        var updatedMeta = new Domain.ValueObjects.Meta(
            company.Meta.Title,
            company.Meta.Description,
            null);

        // Оновлюємо Meta в сутності
        company.UpdateMeta(updatedMeta);

        // Зберігаємо зміни в БД
        await _companyRepository.UpdateAsync(company, cancellationToken);

        _logger.LogInformation($"Мета-зображення компанії {companyId} успішно видалено з БД.");
    }
}
