<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Orders
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-edit admin-page-icon"></i>
          Edit Order
        </h1>
        <p class="admin-page-subtitle">Update order status and information</p>
      </div>
      <div class="admin-page-actions">
        <button @click="resetForm" class="admin-btn admin-btn-secondary">
          <i class="fas fa-undo"></i>
          Reset
        </button>
        <button @click="cancelEdit" class="admin-btn admin-btn-ghost">
          <i class="fas fa-times"></i>
          Cancel
        </button>
        <button
          @click="saveOrder"
          :disabled="saving || !hasChanges"
          class="admin-btn admin-btn-primary"
        >
          <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': saving }"></i>
          {{ saving ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div class="has-text-centered py-6" v-if="loading">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading order details...</p>
    </div>

    <!-- Error State -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchOrderDetails">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Order Not Found -->
    <div class="notification is-warning" v-else-if="!order">
      <p>Order not found.</p>
      <button class="button is-light mt-2" @click="goBack">
        <span class="icon"><i class="fas fa-arrow-left"></i></span>
        <span>Back to Orders</span>
      </button>
    </div>

    <!-- Order Edit Form -->
    <div v-else>
      <!-- Order Header Info -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="level">
            <div class="level-left">
              <div class="level-item">
                <div>
                  <h1 class="title is-4">
                    Edit Order #{{ order.id }}
                  </h1>
                  <p class="subtitle is-6">
                    Placed on {{ formatDate(order.createdAt) }} at {{ formatTime(order.createdAt) }}
                  </p>
                </div>
              </div>
            </div>
            <div class="level-right">
              <div class="level-item">
                <div class="tags">
                  <span class="tag is-medium" :class="getOrderStatusClass(editableOrder.status)">
                    {{ getOrderStatusText(editableOrder.status) }}
                  </span>
                  <span class="tag is-medium" :class="getPaymentStatusClass(editableOrder.paymentStatus)">
                    {{ getPaymentStatusText(editableOrder.paymentStatus) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="columns">
        <!-- Left Column - Order Information -->
        <div class="column is-8">
          <!-- Editable Order Details using our component structure -->
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-receipt"></i>
                </span>
                Order Information
              </p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Order ID</label>
                    <div class="control">
                      <input class="input" type="text" :value="order.id" readonly>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Customer Name</label>
                    <div class="control">
                      <input class="input" type="text" :value="order.customerName" readonly>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Customer Email</label>
                    <div class="control">
                      <input class="input" type="email" :value="order.customerEmail" readonly>
                    </div>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Order Date</label>
                    <div class="control">
                      <input class="input" type="text" :value="formatDate(order.createdAt)" readonly>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Order Time</label>
                    <div class="control">
                      <input class="input" type="text" :value="formatTime(order.createdAt)" readonly>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Total Amount</label>
                    <div class="control">
                      <input class="input" type="text" :value="formatCurrency(calculateTotal())" readonly>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Editable Shipping Address -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-shipping-fast"></i>
                </span>
                Shipping Address
              </p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Address Line</label>
                    <div class="control">
                      <input
                        class="input"
                        type="text"
                        v-model="editableOrder.shippingAddress.address1"
                        @input="markAsChanged"
                      >
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">City</label>
                    <div class="control">
                      <input
                        class="input"
                        type="text"
                        v-model="editableOrder.shippingAddress.city"
                        @input="markAsChanged"
                      >
                    </div>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Country</label>
                    <div class="control">
                      <input
                        class="input"
                        type="text"
                        v-model="editableOrder.shippingAddress.country"
                        @input="markAsChanged"
                      >
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Shipping Method</label>
                    <div class="control">
                      <input
                        class="input"
                        type="text"
                        v-model="editableOrder.shippingMethodName"
                        @input="markAsChanged"
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Editable Notes Section -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon">
                  <i class="fas fa-sticky-note"></i>
                </span>
                Notes
              </p>
            </div>
            <div class="card-content">
              <div class="field">
                <div class="control">
                  <textarea
                    class="textarea"
                    v-model="editableOrder.notes"
                    @input="markAsChanged"
                    rows="3"
                    placeholder="Add notes about this order..."
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column - Status Information -->
        <div class="column is-4">
          <!-- Order Status Update Component -->
          <OrderStatusUpdate
            :order-id="order.id"
            :current-order-status="editableOrder.status"
            :current-payment-status="editableOrder.paymentStatus"
            :customer-name="order.customerName"
            :status-history="order.statusHistory || []"
            :loading="updating"
            @update-order-status="handleUpdateOrderStatus"
            @update-payment-status="handleUpdatePaymentStatus"
          />
        </div>
      </div>

      <!-- Editable Order Items Table -->
      <div class="mt-4">
        <OrderItemsTable
          :items="editableOrder.items || []"
          :tax="0"
          :shipping="editableOrder.shipping || 0"
          :discount="editableOrder.discount || 0"
          :editable="true"
          @update-quantity="handleUpdateQuantity"
          @remove-item="handleRemoveItem"
          @add-item="handleAddItem"
        />
      </div>
    </div>

    <!-- Product Selector Modal -->
    <ProductSelector
      :is-open="showProductSelector"
      @close="showProductSelector = false"
      @select="handleProductSelected"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import OrderItemsTable from '@/admin/components/orders/OrderItemsTable.vue';
import OrderStatusUpdate from '@/admin/components/orders/OrderStatusUpdate.vue';
import ProductSelector from '@/admin/components/orders/ProductSelector.vue';
import { ordersService } from '@/admin/services/orders';
import { eventBus, EVENTS, updateFlags } from '@/admin/utils/eventBus';
import {
  ORDER_STATUS_OPTIONS,
  PAYMENT_STATUS_OPTIONS,
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass,
  parseOrderStatus,
  parsePaymentStatus,
  isValidOrderStatus,
  isValidPaymentStatus,
  canTransitionOrderStatus,
  canTransitionPaymentStatus
} from '@/admin/utils/orderConstants';

// Router
const route = useRoute();
const router = useRouter();

// State
const order = ref(null);
const editableOrder = ref({});
const loading = ref(false);
const saving = ref(false);
const updating = ref(false);
const error = ref(null);
const hasChanges = ref(false);
const showProductSelector = ref(false);

// Computed
const orderId = computed(() => route.params.id);

// Methods
const fetchOrderDetails = async () => {
  if (!orderId.value) {
    error.value = 'Order ID is required';
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    console.log('Fetching order details for editing, ID:', orderId.value);
    const orderData = await ordersService.getOrderById(orderId.value);

    if (orderData) {
      order.value = orderData;
      // Create editable copy
      editableOrder.value = JSON.parse(JSON.stringify(orderData));

      // Ensure required nested objects exist
      if (!editableOrder.value.shippingAddress) {
        editableOrder.value.shippingAddress = {
          address1: '',
          city: '',
          country: ''
        };
      }
      if (!editableOrder.value.items || !Array.isArray(editableOrder.value.items)) {
        editableOrder.value.items = [];
      }
      if (!editableOrder.value.notes) {
        editableOrder.value.notes = '';
      }

      console.log('Order details loaded for editing:', orderData);
    } else {
      error.value = 'Order not found';
    }
  } catch (err) {
    console.error('Error fetching order details:', err);
    error.value = err.message || 'Failed to load order details';
  } finally {
    loading.value = false;
  }
};

const markAsChanged = () => {
  hasChanges.value = true;
};

const calculateTotal = () => {
  if (!editableOrder.value.items || !Array.isArray(editableOrder.value.items)) return 0;

  const subtotal = editableOrder.value.items.reduce((sum, item) => {
    return sum + ((item?.price || 0) * (item?.quantity || 0));
  }, 0);

  return subtotal + (editableOrder.value.shipping || 0) - (editableOrder.value.discount || 0);
};

const handleUpdateQuantity = (index, quantity) => {
  if (editableOrder.value.items && Array.isArray(editableOrder.value.items) && editableOrder.value.items[index]) {
    editableOrder.value.items[index].quantity = quantity;
    editableOrder.value.items[index].total = editableOrder.value.items[index].price * quantity;
    markAsChanged();
  }
};

const handleRemoveItem = (index) => {
  if (editableOrder.value.items && Array.isArray(editableOrder.value.items)) {
    editableOrder.value.items.splice(index, 1);
    markAsChanged();
  }
};

const handleAddItem = () => {
  showProductSelector.value = true;
};

const handleProductSelected = (product) => {
  if (!editableOrder.value.items) {
    editableOrder.value.items = [];
  }

  const newItem = {
    id: Date.now().toString(),
    productId: product.id,
    productName: product.name,
    quantity: 1,
    price: product.priceAmount || 0,
    total: product.priceAmount || 0
  };

  editableOrder.value.items.push(newItem);
  markAsChanged();
};

const handleUpdateOrderStatus = async (orderId, newStatus) => {
  updating.value = true;
  try {
    await ordersService.updateOrderStatus(orderId, newStatus);
    editableOrder.value.status = newStatus;
    markAsChanged();

    // Emit event to notify other components
    eventBus.emit(EVENTS.ORDER_UPDATED, { orderId, type: 'status', newValue: newStatus });

    // Mark orders for refresh in case user navigates to list
    updateFlags.markOrdersForRefresh();

    console.log('Order status updated successfully');
  } catch (err) {
    console.error('Error updating order status:', err);
    error.value = 'Failed to update order status';
  } finally {
    updating.value = false;
  }
};

const handleUpdatePaymentStatus = async (orderId, newStatus) => {
  updating.value = true;
  try {
    console.log('Updating payment status:', { orderId, newStatus });

    await ordersService.updatePaymentStatus(orderId, newStatus);
    editableOrder.value.paymentStatus = newStatus;
    markAsChanged();
    console.log('Payment status updated successfully');
  } catch (err) {
    console.error('Error updating payment status:', err);
    error.value = 'Failed to update payment status';
  } finally {
    updating.value = false;
  }
};

const saveOrder = async () => {
  // Validate before saving
  const validationErrors = [];

  // Validate order status
  if (!isValidOrderStatus(editableOrder.value.status)) {
    validationErrors.push('Invalid order status');
  }

  // Validate payment status
  if (!isValidPaymentStatus(editableOrder.value.paymentStatus)) {
    validationErrors.push('Invalid payment status');
  }

  // Validate items
  if (!editableOrder.value.items || editableOrder.value.items.length === 0) {
    validationErrors.push('Order must have at least one item');
  }

  // Check for invalid items
  const invalidItems = editableOrder.value.items.filter(item =>
    !item.id || !item.quantity || item.quantity <= 0 || !item.price || item.price <= 0
  );
  if (invalidItems.length > 0) {
    validationErrors.push('All items must have valid ID, quantity, and price');
  }

  // Show validation errors
  if (validationErrors.length > 0) {
    error.value = 'Validation errors: ' + validationErrors.join(', ');
    return;
  }

  saving.value = true;
  try {
    // Parse and normalize statuses before saving
    const orderToSave = {
      ...editableOrder.value,
      status: parseOrderStatus(editableOrder.value.status),
      paymentStatus: parsePaymentStatus(editableOrder.value.paymentStatus)
    };

    await ordersService.update(orderId.value, orderToSave);
    hasChanges.value = false;
    console.log('✅ Order saved successfully');

    // Emit event to notify other components about the update
    eventBus.emit(EVENTS.ORDER_UPDATED, {
      orderId: orderId.value,
      type: 'full_update',
      orderData: orderToSave
    });

    // Mark orders for refresh in case user navigates to list
    updateFlags.markOrdersForRefresh();

    // Navigate back to view page
    router.push(`/admin/orders/${orderId.value}/view`);
  } catch (err) {
    console.error('❌ Error saving order:', err);
    error.value = 'Failed to save order changes: ' + (err.message || 'Unknown error');
  } finally {
    saving.value = false;
  }
};

const cancelEdit = () => {
  if (hasChanges.value) {
    if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
      goBack();
    }
  } else {
    goBack();
  }
};

const goBack = () => {
  router.push(`/admin/orders/${orderId.value}/view`);
};

const resetForm = () => {
  if (order.value) {
    // Reset form to original order data
    editableOrder.value = {
      ...order.value,
      // Ensure nested objects are properly copied
      shippingAddress: { ...order.value.shippingAddress },
      billingAddress: { ...order.value.billingAddress },
      items: order.value.items.map(item => ({ ...item }))
    };

    // Clear any validation errors
    errors.value = {};

    console.log('Form reset to original order data');
  }
};

// Utility functions (same as OrderView.vue)
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// Status utility functions are now imported from orderConstants

// Lifecycle
onMounted(() => {
  fetchOrderDetails();
});
</script>

<style scoped>
.admin-order-edit {
  padding: 1rem;
}

.breadcrumb {
  margin-bottom: 0;
}

.breadcrumb a {
  display: flex;
  align-items: center;
}

.breadcrumb .icon {
  margin-right: 0.25rem;
}

.level {
  margin-bottom: 1.5rem;
}

.tags {
  gap: 0.5rem;
}

.tag.is-medium {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.card {
  box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
}

.card-header-title {
  align-items: center;
}

.card-header-title .icon {
  margin-right: 0.5rem;
}
</style>
