using Marketplace.Domain.ValueObjects;

namespace Marketplace.Domain.Entities;

public enum ProductStatus
{
    Pending,
    Approved,
    Rejected
}

public class Product : IEntity
{
    private string _name;
    private Slug _slug;
    private string _description;
    private Money _price;
    private uint _stock;
    private uint _sales;
    private Guid _categoryId;
    private Meta _meta;
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public Company Company { get; set; }
    public Guid SellerId { get; set; }
    public User Seller { get; set; }
    public string Name
    {
        get => _name;
        set => UpdateName(value);
    }
    public Slug Slug
    {
        get => _slug;
        set => UpdateSlug(value);
    }
    public string Description
    {
        get => _description;
        set => UpdateDescription(value);
    }
    public Money Price
    {
        get => _price;
        set => UpdatePrice(value);
    }
    public uint Stock
    {
        get => _stock;
        set => UpdateStock(value);
    }
    public uint Sales
    {
        get => _sales;
        set => UpdateSales(value);
    }
    public ProductStatus Status { get; set; }
    public Guid CategoryId
    {
        get => _categoryId;
        set => UpdateCategoryId(value);
    }

    public Category Category { get; set; }
    public Dictionary<string, string>? Attributes { get; set; }
    public bool IsApproved { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public Guid? ApprovedByUserId { get; set; }
    public User ApprovedByUser { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Meta Meta
    {
        get => _meta;
        set => UpdateMeta(value);
    }

    // Navigation properties for images
    public ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();

    private Product() { }

    public Product(
        Guid companyId,
        Guid sellerId,
        string name,
        Slug slug,
        string description,
        Money price,
        uint stock,
        uint sales,
        Guid categoryId,
        Dictionary<string, string>? attributes,
        Meta meta
    )
    {
        ValidateCreation(companyId, sellerId, name, description, categoryId);

        Id = Guid.NewGuid();
        CompanyId = companyId;
        SellerId = sellerId;
        Name = name;
        Slug = slug;
        Description = description;
        Price = price;
        Stock = stock;
        Sales = sales;
        CategoryId = categoryId;
        Status = ProductStatus.Pending;
        Attributes = attributes;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = null;
        Meta = meta;
    }

    public void Approve(Guid approvedById)
    {
        IsApproved = true;
        ApprovedAt = DateTime.UtcNow;
        ApprovedByUserId = approvedById;
        Status = ProductStatus.Approved;
    }

    public void Reject(string reason)
    {
        IsApproved = false;
        ApprovedAt = null;
        ApprovedByUserId = null;
        Status = ProductStatus.Rejected;
    }
    public void Update(
        string? name = null,
        Slug? slug = null,
        string? description = null,
        //Money? price = null,
        Currency? priceCurrency = null,
        decimal? priceAmount = null,
        uint? stock = null,
        uint? sales = null,
        Guid? categoryId = null,
        ProductStatus? status = null,
        Dictionary<string, string>? attributes = null,
        string? metaTitle = null,
        string? metaDescription = null,
        Url? metaImage = null
    )
    {
        if (name != null) UpdateName(name);
        if (slug != null) UpdateSlug(slug);
        if (description != null) UpdateDescription(description);
        if (priceCurrency != null || priceAmount != null)
        {
            UpdatePrice(new Money(
                priceAmount ?? Price.Amount,
                priceCurrency ?? Price.Currency));
        }
        if (stock != null) UpdateStock((uint)stock);
        if (sales != null) UpdateSales((uint)sales);
        if (categoryId != null) UpdateCategoryId(categoryId.Value);
        if (status != null) UpdateStatus(status.Value);
        if (attributes != null) UpdateAttributes(attributes);
        if (metaTitle != null || metaDescription != null || metaImage != null)
        {
            UpdateMeta(new Meta(
                metaTitle ?? Meta.Title,
                metaDescription ?? Meta.Description,
                metaImage ?? Meta.Image));
        }

        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateName(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name), "Ім'я не може бути порожнім.");
        if (name.Length > 100) throw new ArgumentException("Ім'я не має перевищувати 100 символів.", nameof(name));
        _name = name;
    }

    public void UpdateSlug(Slug slug)
    {
        _slug = slug ?? throw new ArgumentNullException(nameof(slug), "Slug не може бути порожнім.");
    }

    public void UpdateDescription(string description)
    {
        if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description), "Опис не може бути порожнім.");
        if (description.Length > 500) throw new ArgumentException("Опис не має перевищувати 500 символів.", nameof(description));
        _description = description;
    }

    public void UpdatePrice(Money price)
    {
        _price = price ?? throw new ArgumentNullException(nameof(price), "Ціна не може бути порожньою.");
    }

    public void UpdateStock(uint stock)
    {
        if (stock == 0) throw new ArgumentException("Кількість має бути більшою за 0.", nameof(stock));
        _stock = stock;
    }

    public void UpdateSales(uint sales)
    {
        // Sales може бути 0 (новий товар без продажів)
        _sales = sales;
    }

    public void UpdateCategoryId(Guid categoryId)
    {
        if (categoryId == Guid.Empty) throw new ArgumentNullException(nameof(categoryId), "Категорія не може бути порожньою.");
        _categoryId = categoryId;
    }

    public void UpdateStatus(ProductStatus status)
    {
        Status = status;
    }

    public void UpdateAttributes(Dictionary<string, string>? attributes)
    {
        Attributes = attributes;
    }

    public void UpdateMeta(Meta? meta)
    {
        _meta = meta ?? throw new ArgumentNullException(nameof(meta), "Метадані не можуть бути порожніми.");
    }

    private void ValidateCreation(Guid companyId, Guid sellerId, string name, string description, Guid categoryId)
    {
        if (companyId == Guid.Empty)
            throw new ArgumentNullException(nameof(companyId), "Компанія не може бути порожньою.");
        if (sellerId == Guid.Empty)
            throw new ArgumentNullException(nameof(sellerId), "Продавець не може бути порожнім.");
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(name), "Ім'я не може бути порожнім.");
        if (name.Length > 100)
            throw new ArgumentException("Ім'я не має перевищувати 100 символів.", nameof(name));
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentNullException(nameof(description), "Опис не може бути порожнім.");
        if (description.Length > 500)
            throw new ArgumentException("Опис не має перевищувати 500 символів.", nameof(description));
        if (categoryId == Guid.Empty)
            throw new ArgumentNullException(nameof(categoryId), "Категорія не може бути порожньою.");
    }
}
