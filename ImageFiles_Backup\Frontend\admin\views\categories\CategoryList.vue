<template>
  <error-boundary>
    <div class="category-list">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <h1 class="title">Categories</h1>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="field has-addons">
              <div class="control">
                <button
                  class="button"
                  :class="{ 'is-primary': viewMode === 'grid' }"
                  @click="viewMode = 'grid'">
                  <span class="icon">
                    <i class="fas fa-th"></i>
                  </span>
                  <span>Grid</span>
                </button>
              </div>
              <div class="control">
                <button
                  class="button"
                  :class="{ 'is-primary': viewMode === 'hierarchy' }"
                  @click="viewMode = 'hierarchy'">
                  <span class="icon">
                    <i class="fas fa-sitemap"></i>
                  </span>
                  <span>Hierarchy</span>
                </button>
              </div>
            </div>
          </div>
          <div class="level-item">
            <router-link to="/admin/categories/create" class="button is-primary">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Add Category</span>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="columns">
            <div class="column is-4">
              <div class="field">
                <label class="label">Search</label>
                <div class="control has-icons-left">
                  <input
                    class="input"
                    type="text"
                    placeholder="Search categories..."
                    v-model="filters.search"
                    @input="debouncedSearch">
                  <span class="icon is-small is-left">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="filters.status" @change="fetchCategories">
                      <option value="">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">&nbsp;</label>
                <div class="field is-grouped">
                  <div class="control">
                    <button
                      class="button is-light"
                      @click="resetFilters">
                      Reset
                    </button>
                  </div>
                  <div class="control">
                    <button
                      class="button is-primary"
                      @click="fetchCategories"
                      :class="{ 'is-loading': loading }">
                      Apply Filters
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- Categories Content -->
      <div class="card">
        <div class="card-content">
          <!-- Loading Skeleton -->
          <category-skeleton
            v-if="loading && !categories.length"
            :view-mode="viewMode"
            :count="5" />

          <!-- Error State -->
          <div v-else-if="error" class="has-text-centered py-6">
            <span class="icon is-large has-text-danger">
              <i class="fas fa-exclamation-triangle fa-2x"></i>
            </span>
            <p class="mt-2 has-text-danger has-text-weight-bold">{{ error }}</p>
            <div class="mt-4">
              <button class="button is-primary" @click="fetchCategories">
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Try Again</span>
              </button>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else-if="!categories.length" class="has-text-centered py-6">
            <span class="icon is-large">
              <i class="fas fa-folder-open fa-2x"></i>
            </span>
            <p class="mt-2">No categories found</p>
            <p class="mt-2">Create your first category to organize your products</p>
            <div class="mt-4">
              <router-link to="/admin/categories/create" class="button is-primary">
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Add Category</span>
              </router-link>
            </div>
          </div>

          <!-- Content -->
          <div v-else>
            <!-- Grid View -->
            <div v-if="viewMode === 'grid'" class="categories-grid">
              <div v-for="category in categories" :key="category.id" class="category-card">
                <div class="category-header">
                  <div class="category-icon">
                    <img
                      v-if="category.image"
                      :src="category.image"
                      :alt="category.name"
                      class="category-image clickable"
                      @click="openImageViewer(category)"
                      @error="handleImageError"
                      title="Click to view full size">
                    <i v-else class="fas fa-folder category-placeholder"></i>
                  </div>
                  <div class="category-info">
                    <h3 class="category-name">{{ category.name }}</h3>
                    <p class="category-slug">{{ category.slug }}</p>
                    <p class="category-description">{{ category.description || 'Підкатегорія для товарів типу ' + category.name.toLowerCase() }}</p>
                  </div>
                </div>

                <div class="category-badges">
                  <span class="badge badge-child">
                    <i class="fas fa-sitemap"></i>
                    Child
                  </span>
                  <span class="badge badge-products">
                    <i class="fas fa-box"></i>
                    {{ getProductCount(category.id) }}
                  </span>
                </div>

                <div class="category-actions">
                  <router-link
                    :to="`/admin/categories/${category.id}`"
                    class="action-btn action-btn-view"
                    title="View">
                    <i class="fas fa-external-link-alt"></i>
                  </router-link>
                  <router-link
                    :to="`/admin/categories/${category.id}/edit`"
                    class="action-btn action-btn-edit"
                    title="Edit">
                    <i class="fas fa-edit"></i>
                  </router-link>
                  <button
                    class="action-btn action-btn-add"
                    title="Add Subcategory">
                    <i class="fas fa-plus"></i>
                  </button>
                  <button
                    class="action-btn action-btn-delete"
                    @click="confirmDelete(category)"
                    title="Delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Hierarchy View -->
            <div v-else-if="viewMode === 'hierarchy'" class="hierarchy-view">
              <div v-for="category in hierarchyCategories" :key="category.id" class="hierarchy-item">
                <div class="hierarchy-card" :class="{ 'expanded': category.expanded }">
                  <div class="hierarchy-header">
                    <div class="hierarchy-icon">
                      <img
                        v-if="category.image"
                        :src="category.image"
                        :alt="category.name"
                        class="hierarchy-image clickable"
                        @click="openImageViewer(category)"
                        @error="handleImageError"
                        title="Click to view full size">
                      <i v-else class="fas fa-folder hierarchy-placeholder"></i>
                    </div>
                    <div class="hierarchy-info">
                      <h3 class="hierarchy-name">{{ category.name }}</h3>
                      <p class="hierarchy-description">{{ category.description || 'Категорія для товарів типу ' + category.name.toLowerCase() }}</p>
                    </div>
                    <div class="hierarchy-badges">
                      <span class="badge badge-root">
                        <i class="fas fa-home"></i>
                        Root
                      </span>
                      <span class="badge badge-children">
                        <i class="fas fa-sitemap"></i>
                        {{ category.children ? category.children.length : 0 }} children
                      </span>
                      <span class="badge badge-products">
                        <i class="fas fa-box"></i>
                        {{ getProductCount(category.id) }}
                      </span>
                    </div>
                    <div class="hierarchy-actions">
                      <router-link
                        :to="`/admin/categories/${category.id}`"
                        class="action-btn action-btn-view"
                        title="View">
                        <i class="fas fa-external-link-alt"></i>
                      </router-link>
                      <router-link
                        :to="`/admin/categories/${category.id}/edit`"
                        class="action-btn action-btn-edit"
                        title="Edit">
                        <i class="fas fa-edit"></i>
                      </router-link>
                      <button
                        class="action-btn action-btn-add"
                        title="Add Subcategory">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button
                        class="action-btn action-btn-delete"
                        @click="confirmDelete(category)"
                        title="Delete">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div v-if="category.expanded" class="hierarchy-expand">
                    <button class="expand-btn" @click="toggleExpand(category)">
                      <i class="fas fa-chevron-up"></i>
                      Expand ({{ category.children ? category.children.length : 0 }})
                    </button>
                  </div>
                  <div v-else class="hierarchy-expand">
                    <button class="expand-btn" @click="toggleExpand(category)">
                      <i class="fas fa-chevron-down"></i>
                      Expand ({{ category.children ? category.children.length : 0 }})
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete Category"
      :message="`Are you sure you want to delete '${categoryToDelete?.name}'? This may affect products in this category.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteCategory"
      @cancel="cancelDelete" />

    <!-- Image Viewer Modal -->
    <div
      v-if="showImageModal"
      class="modal fade show d-block"
      @click="closeImageViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-folder me-2"></i>
              {{ selectedCategory?.name }} - Category Image
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeImageViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <div v-if="selectedCategory?.image" class="category-image-viewer">
              <img
                :src="selectedCategory.image"
                :alt="`${selectedCategory.name} category image`"
                class="img-fluid rounded"
                style="max-height: 500px;"
              />
              <div class="mt-3">
                <h6>{{ selectedCategory.name }}</h6>
                <small class="text-muted">{{ selectedCategory.description || 'No description' }}</small>
              </div>
            </div>
            <div v-else class="no-image-message">
              <i class="fas fa-folder fa-3x text-muted mb-3"></i>
              <p>No image available for this category</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </error-boundary>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, inject } from 'vue';
import { categoriesService } from '@/admin/services/categories';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import CategorySkeleton from '@/admin/components/categories/CategorySkeleton.vue';
import ErrorBoundary from '@/components/ErrorBoundary.vue';



// Error boundary
const errorBoundary = inject('errorBoundary', {
  setError: () => {},
  clearError: () => {}
});

// State
const categories = ref([]);
const loading = ref(false);
const error = ref(null);
const showDeleteModal = ref(false);
const categoryToDelete = ref(null);
const searchTimeout = ref(null);
const viewMode = ref('grid');

// Image viewer state
const showImageModal = ref(false);
const selectedCategory = ref(null);

// Filters
const filters = reactive({
  search: '',
  status: ''
});

// Debounced search
const debouncedSearch = () => {
  // Clear any existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  // Set a new timeout
  searchTimeout.value = setTimeout(() => {
    fetchCategories();
    searchTimeout.value = null;
  }, 300); // 300ms debounce
};

// Fetch categories directly from service
const fetchCategories = async () => {
  try {
    // Clear any error
    errorBoundary.clearError();
    error.value = null;

    // Set loading state
    loading.value = true;

    // Fetch categories directly from service
    const response = await categoriesService.getCategories(filters);

    console.log('Categories API response:', response);

    // Handle different response structures
    if (response && response.categories) {
      categories.value = response.categories;
    } else if (response && response.data) {
      categories.value = response.data;
    } else if (Array.isArray(response)) {
      categories.value = response;
    } else {
      categories.value = [];
    }

    console.log('Categories loaded:', categories.value.length);
  } catch (err) {
    console.error('Error fetching categories:', err);

    // Set local error state for component-level error display
    error.value = err.message || 'Failed to load categories. Please try again.';

    // Only set error boundary for non-404 errors (to avoid full-page error)
    if (!err.originalError || err.originalError.response?.status !== 404) {
      errorBoundary.setError(err);
    }

    categories.value = [];
  } finally {
    loading.value = false;
  }
};



// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.status = '';
  fetchCategories();
};

// Get product count for category
const getProductCount = (categoryId) => {
  // Тут можна додати реальний підрахунок продуктів з API
  // Поки що повертаємо випадкове число для демонстрації
  const category = categories.value.find(c => c.id === categoryId);
  return category?.productCount || Math.floor(Math.random() * 50);
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/48?text=No+Image';
};

// Image viewer methods
const openImageViewer = (category) => {
  selectedCategory.value = category;
  showImageModal.value = true;
};

const closeImageViewer = () => {
  showImageModal.value = false;
  selectedCategory.value = null;
};



// Confirm delete
const confirmDelete = (category) => {
  categoryToDelete.value = category;
  showDeleteModal.value = true;
};

// Delete category
const deleteCategory = async () => {
  if (!categoryToDelete.value) return;

  try {
    // Clear any error
    errorBoundary.clearError();

    // Delete category using service
    await categoriesService.deleteCategory(categoryToDelete.value.id);

    // Remove from local list
    categories.value = categories.value.filter(c => c.id !== categoryToDelete.value.id);

    showDeleteModal.value = false;
    categoryToDelete.value = null;
  } catch (error) {
    console.error('Error deleting category:', error);
    errorBoundary.setError(error);
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  categoryToDelete.value = null;
};

// Computed for hierarchy view
const hierarchyCategories = computed(() => {
  // For now, just return root categories (those without parentId)
  return categories.value.filter(cat => !cat.parentId).map(cat => ({
    ...cat,
    expanded: false,
    children: categories.value.filter(child => child.parentId === cat.id)
  }));
});

// Toggle expand for hierarchy
const toggleExpand = (category) => {
  category.expanded = !category.expanded;
};

// Lifecycle hooks
onMounted(() => {
  // Fetch initial data
  fetchCategories();
});

// Clean up resources when component is unmounted
onUnmounted(() => {
  // Clear any pending timeouts
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
});
</script>

<style scoped>
@import '@/assets/css/admin/pages/categories.css';
.category-list {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1rem;
  padding: 1rem 0;
}

.category-card {
  background: #2c3e50;
  border-radius: 8px;
  padding: 1.5rem;
  color: white;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.category-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: white;
}

.category-slug {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 0.5rem 0;
  font-family: monospace;
}

.category-description {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.4;
}

.category-badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-child {
  background: #3498db;
  color: white;
}

.badge-products {
  background: #f39c12;
  color: white;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 0.875rem;
}

.action-btn-view {
  background: #3498db;
  color: white;
}

.action-btn-view:hover {
  background: #2980b9;
  color: white;
}

.action-btn-edit {
  background: #2ecc71;
  color: white;
}

.action-btn-edit:hover {
  background: #27ae60;
  color: white;
}

.action-btn-add {
  background: #9b59b6;
  color: white;
}

.action-btn-add:hover {
  background: #8e44ad;
}

.action-btn-delete {
  background: #e74c3c;
  color: white;
}

.action-btn-delete:hover {
  background: #c0392b;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

/* Hierarchy View */
.hierarchy-view {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.hierarchy-item {
  width: 100%;
}

.hierarchy-card {
  background: #2c3e50;
  border-radius: 8px;
  padding: 1.5rem;
  color: white;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hierarchy-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hierarchy-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hierarchy-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.hierarchy-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.hierarchy-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
}

.hierarchy-info {
  flex: 1;
  min-width: 0;
}

.hierarchy-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: white;
}

.hierarchy-description {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.4;
}

.hierarchy-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.hierarchy-actions {
  display: flex;
  gap: 0.5rem;
}

.hierarchy-expand {
  margin-top: 1rem;
  text-align: center;
}

.expand-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.expand-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.expand-btn i {
  margin-right: 0.5rem;
}

/* Clickable images */
.category-image.clickable,
.hierarchy-image.clickable {
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.category-image.clickable:hover,
.hierarchy-image.clickable:hover {
  transform: scale(1.05);
  opacity: 0.9;
}

/* Modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.category-image-viewer {
  padding: 1rem;
}

.no-image-message {
  padding: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
  .categories-grid {
    grid-template-columns: 1fr;
  }

  .category-card {
    padding: 1rem;
  }

  .hierarchy-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .hierarchy-badges {
    width: 100%;
    justify-content: flex-start;
  }

  .hierarchy-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
