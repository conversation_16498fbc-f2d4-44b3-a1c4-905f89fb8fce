import{_ as k,g as i,h as B,H as C,i as _,c,a as t,b as q,x as p,y as f,F as V,p as x,t as F,Z as N,f as T,e as A,o as g}from"./index-L-hJxM_5.js";const G={class:"top-products-page"},U={class:"filters-section"},$={class:"filter-group"},h=["value"],D={class:"filter-group"},E={class:"filter-group"},I={__name:"TopProductsPage",setup(M){const n=T(),y=A(),d=i([]),a=i(""),l=i("Sales"),r=i("20"),u=i(null),P=B(()=>{var o;const s=a.value?(o=d.value.find(w=>w.slug===a.value))==null?void 0:o.name:null,e={Sales:"продажами",Stock:"наявністю",PriceAmount:"ціною"}[l.value]||"продажами";return s?`Топ товари в категорії "${s}" за ${e}`:`Топ товари за ${e}`}),S=async()=>{try{const e=await(await fetch("/api/categories")).json();e.success&&e.data?d.value=e.data:d.value=e}catch(s){console.error("Error loading categories:",s)}},v=()=>{const s={};a.value&&(s.category=a.value),l.value!=="Sales"&&(s.sortBy=l.value),r.value!=="20"&&(s.limit=r.value),y.replace({query:s}),u.value&&u.value.refresh()},b=()=>{a.value="",l.value="Sales",r.value="20",v()},m=()=>{a.value=n.query.category||"",l.value=n.query.sortBy||"Sales",r.value=n.query.limit||"20"};return C(()=>n.query,()=>{m(),u.value&&u.value.refresh()}),_(async()=>{await S(),m(),u.value&&u.value.refresh()}),(s,e)=>(g(),c("div",G,[e[9]||(e[9]=t("div",{class:"page-header"},[t("h1",{class:"page-title"},"Топ товари за продажами"),t("p",{class:"page-description"}," Найпопулярніші товари на нашій платформі, відсортовані за кількістю продажів ")],-1)),t("div",U,[t("div",$,[e[4]||(e[4]=t("label",{for:"category-filter"},"Категорія:",-1)),p(t("select",{id:"category-filter","onUpdate:modelValue":e[0]||(e[0]=o=>a.value=o),onChange:v},[e[3]||(e[3]=t("option",{value:""},"Всі категорії",-1)),(g(!0),c(V,null,x(d.value,o=>(g(),c("option",{key:o.slug,value:o.slug},F(o.name),9,h))),128))],544),[[f,a.value]])]),t("div",D,[e[6]||(e[6]=t("label",{for:"sort-filter"},"Сортування:",-1)),p(t("select",{id:"sort-filter","onUpdate:modelValue":e[1]||(e[1]=o=>l.value=o),onChange:v},e[5]||(e[5]=[t("option",{value:"Sales"},"За продажами",-1),t("option",{value:"Stock"},"За наявністю",-1),t("option",{value:"PriceAmount"},"За ціною (зростання)",-1),t("option",{value:"PriceAmount","data-desc":"true"},"За ціною (спадання)",-1)]),544),[[f,l.value]])]),t("div",E,[e[8]||(e[8]=t("label",{for:"limit-filter"},"Кількість:",-1)),p(t("select",{id:"limit-filter","onUpdate:modelValue":e[2]||(e[2]=o=>r.value=o),onChange:v},e[7]||(e[7]=[t("option",{value:"10"},"10 товарів",-1),t("option",{value:"20"},"20 товарів",-1),t("option",{value:"50"},"50 товарів",-1),t("option",{value:"100"},"100 товарів",-1)]),544),[[f,r.value]])]),t("button",{onClick:b,class:"reset-btn"}," Скинути фільтри ")]),q(N,{ref_key:"topProductsGrid",ref:u,title:P.value,"category-slug":a.value,limit:parseInt(r.value),"order-by":l.value,"show-controls":!1,"show-view-all":!1,"auto-load":!1},null,8,["title","category-slug","limit","order-by"])]))}},j=k(I,[["__scopeId","data-v-4a62032b"]]);export{j as default};
