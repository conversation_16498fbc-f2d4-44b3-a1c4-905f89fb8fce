using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Обробник універсальної команди завантаження множинних зображень
/// </summary>
public class UploadMultipleUniversalImagesCommandHandler : IRequestHandler<UploadMultipleUniversalImagesCommand, List<FileUploadResult>>
{
    private readonly IImageService _imageService;
    private readonly ILogger<UploadMultipleUniversalImagesCommandHandler> _logger;

    public UploadMultipleUniversalImagesCommandHandler(
        IImageService imageService,
        ILogger<UploadMultipleUniversalImagesCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<List<FileUploadResult>> Handle(UploadMultipleUniversalImagesCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Uploading multiple images for {request.EntityType} {request.EntityId}");

        try
        {
            var results = await _imageService.UploadMultipleImagesAsync(
                request.EntityType,
                request.EntityId,
                request.Files,
                cancellationToken);

            _logger.LogInformation($"Successfully uploaded {results.Count} images for {request.EntityType} {request.EntityId}");
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to upload multiple images for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
