﻿using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Application.Services;
using Marketplace.Domain.Repositories;
using MediatR;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Product;

public class GetProductQueryHandler : IRequestHandler<GetProductQuery, ProductResponse>
{
    private readonly IProductRepository _repository;
    private readonly ICategoryHierarchyService _categoryHierarchyService;
    private readonly IMapper _mapper;

    public GetProductQueryHandler(IProductRepository repository, ICategoryHierarchyService categoryHierarchyService, IMapper mapper)
    {
        _repository = repository;
        _categoryHierarchyService = categoryHierarchyService;
        _mapper = mapper;
    }

    public async Task<ProductResponse> Handle(GetProductQuery request, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(request.Id, cancellationToken,
            new Expression<Func<Domain.Entities.Product, object>>[] {
                p => p.Category,
                p => p.Company,
                p => p.Seller
            });
        if (item == null)
            return null;

        var response = _mapper.Map<ProductResponse>(item);

        // Заповнюємо CategoryName з ієрархією категорій
        if (item.CategoryId != Guid.Empty)
        {
            response.CategoryName = await _categoryHierarchyService.GetCategoryHierarchyAsync(item.CategoryId, cancellationToken);
        }

        return response;
    }
}
