<template>
  <AdminCard 
    title="Product Images" 
    :loading="loading"
    shadow="default"
  >
    <template #actions>
      <button
        v-if="editable"
        class="admin-btn admin-btn-xs admin-btn-secondary"
        @click="$emit('edit')"
      >
        <i class="fas fa-edit"></i>
        Edit
      </button>
    </template>

    <div class="admin-images-container">
      <!-- Product Images -->
      <div v-if="allProductImages.length > 0" class="admin-product-images-section">
        <h4 class="admin-image-section-title">
          <i class="fas fa-images"></i>
          Product Images ({{ allProductImages.length }})
        </h4>
        <div class="admin-images-row">
          <div
            v-for="(image, index) in allProductImages"
            :key="image.id || index"
            class="admin-image-item"
            :class="{ 'admin-image-main': image.isMain }"
          >
            <div class="admin-image-wrapper">
              <img 
                :src="image.url" 
                :alt="image.alt"
                class="admin-grid-image"
                @click="openImageModal(image.url, image.alt)"
                @error="handleImageError"
              />
              <div v-if="image.isMain" class="admin-main-badge">
                <i class="fas fa-star"></i>
                Main
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Images State -->
      <div v-if="!allProductImages.length && !product?.metaImage" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-image"></i>
        </div>
        <h4 class="admin-empty-title">No Images</h4>
        <p class="admin-empty-description">
          This product doesn't have any images uploaded.
        </p>
        <button 
          v-if="editable"
          class="admin-btn admin-btn-primary"
          @click="$emit('upload-image')"
        >
          <i class="fas fa-upload"></i>
          Upload Image
        </button>
      </div>
    </div>

    <!-- Image Modal -->
    <AdminModal 
      v-model="showImageModal" 
      :title="modalImageTitle"
      size="lg"
      centered
    >
      <div class="admin-image-modal-content">
        <img 
          v-if="modalImageUrl" 
          :src="modalImageUrl" 
          :alt="modalImageTitle"
          class="admin-modal-image"
        />
      </div>
      
      <template #footer>
        <button 
          class="admin-btn admin-btn-secondary"
          @click="showImageModal = false"
        >
          Close
        </button>
        <button 
          class="admin-btn admin-btn-primary"
          @click="downloadImage(modalImageUrl, 'full-size-image')"
        >
          <i class="fas fa-download"></i>
          Download
        </button>
      </template>
    </AdminModal>
  </AdminCard>
</template>

<script setup>
import { ref, computed } from 'vue';
import AdminCard from '../common/AdminCard.vue';
import AdminModal from '../common/AdminModal.vue';

// Props
const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits(['edit', 'upload-image']);

// Reactive data
const showImageModal = ref(false);
const modalImageUrl = ref('');
const modalImageTitle = ref('');

// Computed
const allProductImages = computed(() => {
  let images = [];
  
  // Check for images array (new structure)
  if (props.product?.images && Array.isArray(props.product.images)) {
    images = props.product.images.map(image => ({
      id: image.id || image.Id,
      url: image.image || image.Image || image.imageUrl || image.ImageUrl,
      alt: image.altText || image.AltText || 'Product image',
      isMain: image.isMain || image.IsMain || false,
      order: image.order || image.Order || 0
    }));
  }
  
  // Check for productImages array (legacy structure)
  else if (props.product?.productImages && Array.isArray(props.product.productImages)) {
    images = props.product.productImages.map(image => ({
      id: image.id || image.Id,
      url: image.image || image.Image || image.imageUrl || image.ImageUrl,
      alt: image.altText || image.AltText || 'Product image',
      isMain: image.isMain || image.IsMain || false,
      order: image.order || image.Order || 0
    }));
  }
  
  // Filter out invalid images and sort
  const validImages = images.filter(img => img.url && img.url.trim() !== '');
  
  // Sort by order, but put main image first
  return validImages.sort((a, b) => {
    if (a.isMain && !b.isMain) return -1;
    if (!a.isMain && b.isMain) return 1;
    return a.order - b.order;
  });
});

// Methods
const openImageModal = (imageUrl, title) => {
  modalImageUrl.value = imageUrl;
  modalImageTitle.value = title;
  showImageModal.value = true;
};

const downloadImage = async (imageUrl, filename) => {
  if (!imageUrl) return;
  
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading image:', error);
  }
};

const handleImageError = (event) => {
  console.error('Image failed to load:', event.target.src);
  event.target.style.display = 'none';
};
</script>

<style scoped>
.admin-images-container {
  padding: 0;
}

.admin-product-images-section {
  margin-bottom: 2rem;
}

.admin-image-section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--admin-text-primary);
}

.admin-images-row {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.admin-image-item {
  flex-shrink: 0;
  position: relative;
}

.admin-image-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: var(--admin-bg-secondary);
  border: 2px solid var(--admin-border);
  transition: all 0.2s ease;
}

.admin-grid-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  cursor: pointer;
  display: block;
}

.admin-main-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: var(--admin-success);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 2;
}

.admin-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.admin-empty-title {
  margin-bottom: 0.5rem;
  color: var(--admin-text-primary);
}

.admin-empty-description {
  margin-bottom: 1.5rem;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.admin-image-modal-content {
  text-align: center;
}

.admin-modal-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
}
</style>
