﻿using FluentValidation;
using Marketplace.Application;
using Marketplace.Infrastructure;
using Marketplace.Infrastructure.DatabaseSeeder;
using Marketplace.Presentation.Filters;
using Marketplace.Presentation.Middleware;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.Text;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        ConfigureServices(builder);

        var app = builder.Build();

        ConfigureMiddleware(app);

        SeedDatabase(app);

        app.Run();
    }

    private static void ConfigureServices(WebApplicationBuilder builder)
    {
        builder.Services.Configure<RouteOptions>(options => options.LowercaseUrls = true);

        // Add CORS
        builder.Services.AddCors(options =>
        {
            options.AddPolicy("VueCorsPolicy", policy =>
            {
                policy.WithOrigins("http://localhost:3000", "http://localhost:3001", "http://localhost:3002")
                      .AllowAnyHeader()
                      .AllowAnyMethod()
                      .AllowCredentials();
            });
        });

        builder.Services.AddControllers(options =>
        {
            options.Filters.Add<ApiExceptionFilterAttribute>();
        });

        // Configure form options for file uploads
        builder.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(options =>
        {
            options.ValueLengthLimit = int.MaxValue;
            options.MultipartBodyLengthLimit = int.MaxValue; // or use a specific limit like 100MB
            options.MultipartHeadersLengthLimit = int.MaxValue;
        });

        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "Marketplace API",
                Version = "v1",
                Description = "API для Marketplace"
            });
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Please enter JWT with Bearer into field",
                Name = "Authorization",
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });
            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" }
                    },
                    Array.Empty<string>()
                }
            });

            c.OperationFilter<AddFileUploadParams>();
            // Додаємо підтримку для multipart/form-data
            c.CustomSchemaIds(type => type.FullName);
        });

        string connectionString = builder.Configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
        builder.Services.AddInfrastructure(connectionString, builder.Configuration);

        builder.Services.AddApplication();

        // Register Reports Services
        builder.Services.AddScoped<Marketplace.Application.Services.MockReportsService>();

        // Реєстрація background service для скидання статистики відвідувань
        builder.Services.AddHostedService<Marketplace.Presentation.Services.CategoryVisitsResetService>();

        // Реєстрація валідаторів презентаційного рівня
        builder.Services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = builder.Configuration["Jwt:Issuer"],
                    ValidAudience = builder.Configuration["Jwt:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? "default-secret-key"))
                };

            });

        builder.Services.AddCors(options =>
        {
            options.AddPolicy("AllowVueApp", policy =>
            {
                policy.WithOrigins("http://localhost:3000", "http://localhost:8081", "http://localhost:5173")
                      .AllowAnyMethod()
                      .AllowAnyHeader()
                      .AllowCredentials();
            });
        });

        builder.Services.AddAuthorization(options =>
        {
            options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
        });
    }

    private static void ConfigureMiddleware(WebApplication app)
    {
        // Global exception handling middleware should be first in the pipeline


        app.UseGlobalExceptionHandling();

        // Add request logging middleware for debugging
        if (app.Environment.IsDevelopment())
        {
            app.UseMiddleware<Marketplace.Presentation.Middleware.RequestLoggingMiddleware>();
        }

        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Marketplace API v1");
                c.RoutePrefix = string.Empty;
            });
        }

        app.UseRouting();

        // Налаштування статичних файлів для аватарок
        app.UseStaticFiles();

        // Use CORS before authentication
        app.UseCors("VueCorsPolicy");

        // Використовуємо middleware для обслуговування файлів
        app.UseMiddleware<FileMiddleware>();

        app.UseAuthentication();
        app.UseAuthorization();

        // Middleware для відстеження відвідувань категорій
        app.UseMiddleware<Marketplace.Presentation.Middleware.CategoryVisitTrackingMiddleware>();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
    }

    private static void SeedDatabase(WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var seeder = scope.ServiceProvider.GetRequiredService<DatabaseSeeder>();
        // Збільшуємо кількість даних для реалістичного тестування:
        // 150 компаній, ~1000 замовлень, ~500 користувачів, ~1500 продуктів
        seeder.Seed(150, 1500);
    }
}