<template>
  <div class="admin-page">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-store admin-page-icon"></i>
          Seller Requests
        </h1>
        <p class="admin-page-subtitle">Review and manage seller application requests</p>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-card">
      <div class="admin-card-content">
        <div class="admin-form-grid admin-form-grid-2">
          <div class="admin-form-field">
            <label class="admin-form-label">Search</label>
            <div class="admin-form-control">
              <input
                class="admin-form-input"
                type="text"
                placeholder="Search by username, email, or company info..."
                v-model="searchQuery"
                @input="debouncedSearch"
              />
            </div>
          </div>

          <div class="admin-form-field">
            <label class="admin-form-label">Status</label>
            <div class="admin-form-control">
              <select class="admin-form-select" v-model="filters.status" @change="search">
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="admin-card">
      <div class="admin-card-content">
        <div v-if="loading && !requests.length" class="admin-loading-state">
          <div class="admin-spinner">
            <i class="fas fa-spinner fa-pulse"></i>
          </div>
          <p class="admin-loading-text">Loading seller requests...</p>
        </div>
        <div v-else-if="!requests.length" class="admin-empty-state">
          <div class="admin-empty-icon">
            <i class="fas fa-store"></i>
          </div>
          <h3 class="admin-empty-title">No seller requests found</h3>
          <p class="admin-empty-text">There are currently no seller requests to review</p>
        </div>
        <div v-else>
          <div class="admin-seller-requests-grid">
            <div
              v-for="request in requests"
              :key="request.id"
              class="admin-seller-request-card">
              <div class="admin-seller-request-content">
                  <div class="admin-seller-request-header">
                    <div class="admin-seller-request-avatar">
                      <img
                        :src="request.user?.avatar || 'https://via.placeholder.com/64'"
                        :alt="(request.user?.firstName || '') + ' ' + (request.user?.lastName || '')"
                        @error="handleImageError">
                    </div>
                    <div class="admin-seller-request-info">
                      <h3 class="admin-seller-request-title">{{ request.user?.username || 'Unknown User' }}</h3>
                      <p class="admin-seller-request-email">
                        <a :href="`mailto:${request.user?.email?.value || request.user?.email}`">{{ request.user?.email?.value || request.user?.email }}</a>
                      </p>
                      <div class="admin-seller-request-meta">
                        <span class="admin-badge" :class="getStatusBadgeClass(request.status)">
                          {{ getStatusString(request.status) }}
                        </span>
                        <span class="admin-seller-request-date">{{ formatDate(request.createdAt) }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="admin-seller-request-details">
                    <div class="admin-info-group">
                      <label class="admin-info-label">Company Name</label>
                      <p class="admin-info-value">{{ request.companyRequestData?.name || 'N/A' }}</p>
                    </div>

                    <div class="admin-info-group">
                      <label class="admin-info-label">Company Description</label>
                      <p class="admin-info-value">{{ request.companyRequestData?.description || 'N/A' }}</p>
                    </div>

                    <div class="admin-info-group">
                      <label class="admin-info-label">Contact Email</label>
                      <p class="admin-info-value">{{ request.companyRequestData?.contactEmail || 'N/A' }}</p>
                    </div>

                    <div class="admin-info-group">
                      <label class="admin-info-label">Contact Phone</label>
                      <p class="admin-info-value">{{ request.companyRequestData?.contactPhone || 'N/A' }}</p>
                    </div>

                    <div v-if="request.additionalInformation" class="admin-info-group">
                      <label class="admin-info-label">Additional Information</label>
                      <p class="admin-info-value">{{ request.additionalInformation }}</p>
                    </div>

                  </div>

                  <div class="admin-seller-request-actions">
                    <router-link
                      :to="{ name: 'AdminSellerRequestDetail', params: { id: request.id } }"
                      class="admin-btn admin-btn-secondary admin-btn-sm">
                      <i class="fas fa-eye"></i>
                      <span>View Details</span>
                    </router-link>

                    <div v-if="getStatusString(request.status) === 'pending'" class="admin-seller-request-status-actions">
                      <button
                        class="admin-btn admin-btn-success admin-btn-sm"
                        @click="confirmApprove(request)"
                        :disabled="request.processing">
                        <i class="fas fa-check"></i>
                        Approve
                      </button>
                      <button
                        class="admin-btn admin-btn-danger admin-btn-sm"
                        @click="confirmReject(request)"
                        :disabled="request.processing">
                        <i class="fas fa-times"></i>
                        Reject
                      </button>
                    </div>

                    <div v-else-if="getStatusString(request.status) === 'approved'" class="admin-seller-request-user-link">
                      <router-link
                        :to="`/admin/users/${request.userId}`"
                        class="admin-btn admin-btn-primary admin-btn-sm">
                        <i class="fas fa-user"></i>
                        View Seller Profile
                      </router-link>
                    </div>

                    <div v-else-if="getStatusString(request.status) === 'rejected'" class="field mt-4">
                      <p class="has-text-danger">
                        <span class="icon">
                          <i class="fas fa-info-circle"></i>
                        </span>
                        <span>Rejected on {{ formatDate(request.updatedAt) }}</span>
                      </p>
                      <p v-if="request.rejectionReason" class="mt-2">
                        <strong>Reason:</strong> {{ request.rejectionReason }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <pagination
            :current-page="currentPage"
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- Approve Confirmation Modal -->
    <confirm-dialog
      :is-open="showApproveModal"
      title="Approve Seller Request"
      :message="`Are you sure you want to approve ${requestToProcess?.user?.username}'s seller request?`"
      confirm-text="Approve"
      cancel-text="Cancel"
      confirm-button-class="is-success"
      @confirm="approveRequest"
      @cancel="cancelProcess" />

    <!-- Reject Modal -->
    <div class="admin-modal" :class="{ 'admin-modal-active': showRejectModal }">
      <div class="admin-modal-backdrop" @click="cancelProcess"></div>
      <div class="admin-modal-content">
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">
            <i class="fas fa-times-circle"></i>
            Reject Seller Request
          </h3>
          <button class="admin-modal-close" @click="cancelProcess">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-body">
          <p class="admin-text-secondary mb-4">
            Are you sure you want to reject <strong>{{ requestToProcess?.user?.username }}</strong>'s seller request?
          </p>

          <div class="admin-form-field">
            <label class="admin-form-label">Reason for Rejection (Optional)</label>
            <div class="admin-form-control">
              <textarea
                class="admin-form-textarea"
                v-model="rejectionReason"
                placeholder="Provide a reason for rejection"
                rows="4">
              </textarea>
            </div>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button
            class="admin-btn admin-btn-danger"
            @click="rejectRequest"
            :disabled="processing">
            <i class="fas fa-times" :class="{ 'fa-spinner fa-pulse': processing }"></i>
            Reject
          </button>
          <button class="admin-btn admin-btn-secondary" @click="cancelProcess">
            <i class="fas fa-ban"></i>
            Cancel
          </button>
        </div>
      </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { sellerRequestsService } from '@/admin/services/seller-requests';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
// Utility function for debouncing
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// State
const requests = ref([]);
const loading = ref(false);
const searchQuery = ref('');

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(6);

// Process state
const showApproveModal = ref(false);
const showRejectModal = ref(false);
const requestToProcess = ref(null);
const rejectionReason = ref('');
const processing = ref(false);

// Filters
const filters = reactive({
  status: ''
});

// Fetch seller requests
const fetchRequests = async (page = 1) => {
  loading.value = true;
  currentPage.value = page;

  try {
    const params = {
      page: currentPage.value,
      pageSize: itemsPerPage.value,
      filter: searchQuery.value,
      status: filters.status
    };

    console.log('🔍 Fetching seller requests with params:', params);
    console.log('🔍 Search query value:', searchQuery.value);
    console.log('🔍 Filter status value:', filters.status);

    const response = await sellerRequestsService.getSellerRequests(params);
    console.log('📥 Raw API response:', response);
    console.log('📥 Response success:', response?.success);
    console.log('📥 Response data keys:', response?.data ? Object.keys(response.data) : 'no data');

    // Обробляємо відповідь від сервісу
    let requestsData = [];
    let paginationData = {};

    if (response && response.success && response.data) {
      const data = response.data;
      console.log('📊 Data structure:', data);
      console.log('📊 Data keys:', Object.keys(data));

      // Дані знаходяться в data.data (PaginatedResponse.Data)
      if (data.data && Array.isArray(data.data)) {
        requestsData = data.data;
        paginationData = data;
        console.log('📋 Found data in data.data:', requestsData.length, 'items');
      }
      // Альтернативний варіант - дані безпосередньо в data
      else if (Array.isArray(data)) {
        requestsData = data;
        console.log('📋 Found data directly in data:', requestsData.length, 'items');
      }
      else {
        console.warn('📋 Unexpected data structure:', data);
      }
    }

    console.log('📋 Final requests data:', requestsData);
    console.log('📊 Final pagination data:', paginationData);

    // Встановлюємо дані
    requests.value = requestsData.map(request => ({
      ...request,
      processing: false
    }));

    // Встановлюємо пагінацію
    totalPages.value = paginationData.lastPage || paginationData.totalPages || Math.ceil((paginationData.total || requestsData.length) / (paginationData.pageSize || itemsPerPage.value));
    totalItems.value = paginationData.total || paginationData.totalItems || requestsData.length;
    currentPage.value = paginationData.currentPage || 1;

    console.log('📊 Pagination details:', {
      lastPage: paginationData.lastPage,
      totalPages: paginationData.totalPages,
      total: paginationData.total,
      totalItems: paginationData.totalItems,
      currentPage: paginationData.currentPage,
      pageSize: paginationData.pageSize
    });

    console.log('✅ Final state:', {
      requestsCount: requests.value.length,
      totalPages: totalPages.value,
      totalItems: totalItems.value,
      currentPage: currentPage.value
    });
  } catch (error) {
    console.error('❌ Error fetching seller requests:', error);
    console.error('❌ Error details:', error.response?.data || error.message);
  } finally {
    loading.value = false;
  }
};

const search = () => {
  console.log('🔍 Search triggered with query:', searchQuery.value);
  console.log('🔍 Search triggered with status filter:', filters.status);
  currentPage.value = 1;
  fetchRequests();
};

const debouncedSearch = debounce(search, 300);

// Handle page change
const handlePageChange = (page) => {
  fetchRequests(page);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Convert status enum to string
const getStatusString = (status) => {
  if (typeof status === 'string') {
    return status.toLowerCase();
  }

  // Handle enum values (0 = Pending, 1 = Approved, 2 = Rejected)
  switch (status) {
    case 0:
      return 'pending';
    case 1:
      return 'approved';
    case 2:
      return 'rejected';
    default:
      return 'pending';
  }
};

// Get status badge class
const getStatusBadgeClass = (status) => {
  const statusString = getStatusString(status);
  switch (statusString) {
    case 'pending':
      return 'admin-badge-warning';
    case 'approved':
      return 'admin-badge-success';
    case 'rejected':
      return 'admin-badge-danger';
    default:
      return 'admin-badge-secondary';
  }
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/64?text=No+Image';
};

// Confirm approve
const confirmApprove = (request) => {
  requestToProcess.value = request;
  showApproveModal.value = true;
};

// Confirm reject
const confirmReject = (request) => {
  requestToProcess.value = request;
  rejectionReason.value = '';
  showRejectModal.value = true;
};

// Cancel process
const cancelProcess = () => {
  console.log('cancelProcess called');
  showApproveModal.value = false;
  showRejectModal.value = false;
  requestToProcess.value = null;
  rejectionReason.value = '';
  console.log('Modals closed');
};

// Global escape key handler
const handleEscapeKey = (event) => {
  if (event.key === 'Escape') {
    if (showRejectModal.value || showApproveModal.value) {
      cancelProcess();
    }
  }
};

// Approve request
const approveRequest = async () => {
  if (!requestToProcess.value) return;

  // Set processing flag
  const index = requests.value.findIndex(r => r.id === requestToProcess.value.id);
  if (index !== -1) {
    requests.value[index].processing = true;
  }

  try {
    await sellerRequestsService.approveSellerRequest(requestToProcess.value.id);

    // Update request in list
    if (index !== -1) {
      requests.value[index].status = 1; // Approved
      requests.value[index].updatedAt = new Date();
      requests.value[index].processing = false;
    }

    // Close modal
    showApproveModal.value = false;
    requestToProcess.value = null;
  } catch (error) {
    console.error('Error approving seller request:', error);

    // Reset processing flag
    if (index !== -1) {
      requests.value[index].processing = false;
    }
  }
};

// Reject request
const rejectRequest = async () => {
  if (!requestToProcess.value) return;

  processing.value = true;

  // Set processing flag
  const index = requests.value.findIndex(r => r.id === requestToProcess.value.id);
  if (index !== -1) {
    requests.value[index].processing = true;
  }

  try {
    await sellerRequestsService.rejectSellerRequest(
      requestToProcess.value.id,
      rejectionReason.value
    );

    // Update request in list
    if (index !== -1) {
      requests.value[index].status = 2; // Rejected
      requests.value[index].updatedAt = new Date();
      requests.value[index].rejectionReason = rejectionReason.value;
      requests.value[index].processing = false;
    }

    // Close modal
    showRejectModal.value = false;
    requestToProcess.value = null;
    rejectionReason.value = '';
  } catch (error) {
    console.error('Error rejecting seller request:', error);

    // Reset processing flag
    if (index !== -1) {
      requests.value[index].processing = false;
    }
  } finally {
    processing.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchRequests();

  // Add global escape key handler
  document.addEventListener('keydown', handleEscapeKey);
});

// Cleanup on unmount
import { onUnmounted } from 'vue';
onUnmounted(() => {
  document.removeEventListener('keydown', handleEscapeKey);
});
</script>

<style scoped>
.admin-seller-request-list {
  padding: var(--admin-space-2xl);
  background: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-seller-requests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--admin-space-xl);
}

.admin-seller-request-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  border: 1px solid var(--admin-border-light);
  box-shadow: var(--admin-shadow-sm);
  transition: all 0.2s ease;
  overflow: hidden;
}

.admin-seller-request-card:hover {
  box-shadow: var(--admin-shadow-md);
  transform: translateY(-2px);
}

.admin-seller-request-content {
  padding: var(--admin-space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-seller-request-header {
  display: flex;
  gap: var(--admin-space-md);
  align-items: flex-start;
}

.admin-seller-request-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--admin-border-light);
  flex-shrink: 0;
}

.admin-seller-request-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-seller-request-info {
  flex: 1;
  min-width: 0;
}

.admin-seller-request-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-xs) 0;
}

.admin-seller-request-email {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-seller-request-email a {
  color: var(--admin-primary);
  text-decoration: none;
}

.admin-seller-request-email a:hover {
  text-decoration: underline;
}

.admin-seller-request-meta {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-seller-request-date {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
}

.admin-seller-request-details {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-seller-request-actions {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
}

.admin-seller-request-status-actions {
  display: flex;
  gap: var(--admin-space-sm);
}

.admin-seller-request-user-link {
  display: flex;
}

@media (max-width: 768px) {
  .admin-seller-requests-grid {
    grid-template-columns: 1fr;
  }

  .admin-seller-request-status-actions {
    flex-direction: column;
  }
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.tag.is-primary {
  background-color: #ff7700;
}

.tag.is-primary.is-light {
  background-color: #fff2e5;
  color: #ff7700;
}
</style>
