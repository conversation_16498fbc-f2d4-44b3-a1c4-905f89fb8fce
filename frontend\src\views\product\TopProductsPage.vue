<template>
  <div class="top-products-page">
    <div class="page-header">
      <h1 class="page-title">Топ товари за продажами</h1>
      <p class="page-description">
        Найпопулярніші товари на нашій платформі, відсортовані за кількістю продажів
      </p>
    </div>

    <div class="filters-section">
      <div class="filter-group">
        <label for="category-filter">Категорія:</label>
        <select id="category-filter" v-model="selectedCategory" @change="updateProducts">
          <option value="">Всі категорії</option>
          <option v-for="category in categories" :key="category.slug" :value="category.slug">
            {{ category.name }}
          </option>
        </select>
      </div>

      <div class="filter-group">
        <label for="sort-filter">Сортування:</label>
        <select id="sort-filter" v-model="sortBy" @change="updateProducts">
          <option value="Sales">За продажами</option>
          <option value="Stock">За наявністю</option>
          <option value="PriceAmount">За ціною (зростання)</option>
          <option value="PriceAmount" data-desc="true">За ціною (спадання)</option>
        </select>
      </div>

      <div class="filter-group">
        <label for="limit-filter">Кількість:</label>
        <select id="limit-filter" v-model="limit" @change="updateProducts">
          <option value="10">10 товарів</option>
          <option value="20">20 товарів</option>
          <option value="50">50 товарів</option>
          <option value="100">100 товарів</option>
        </select>
      </div>

      <button @click="resetFilters" class="reset-btn">
        Скинути фільтри
      </button>
    </div>

    <TopProductsGrid
      ref="topProductsGrid"
      :title="dynamicTitle"
      :category-slug="selectedCategory"
      :limit="parseInt(limit)"
      :order-by="sortBy"
      :show-controls="false"
      :show-view-all="false"
      :auto-load="false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import TopProductsGrid from '@/components/products/TopProductsGrid.vue';

const route = useRoute();
const router = useRouter();

// Reactive data
const categories = ref([]);
const selectedCategory = ref('');
const sortBy = ref('Sales');
const limit = ref('20');
const topProductsGrid = ref(null);

// Computed
const dynamicTitle = computed(() => {
  const categoryName = selectedCategory.value 
    ? categories.value.find(c => c.slug === selectedCategory.value)?.name 
    : null;
  
  const sortName = {
    'Sales': 'продажами',
    'Stock': 'наявністю',
    'PriceAmount': 'ціною'
  }[sortBy.value] || 'продажами';

  if (categoryName) {
    return `Топ товари в категорії "${categoryName}" за ${sortName}`;
  }
  
  return `Топ товари за ${sortName}`;
});

// Methods
const loadCategories = async () => {
  try {
    const response = await fetch('/api/categories');
    const data = await response.json();
    
    if (data.success && data.data) {
      categories.value = data.data;
    } else {
      categories.value = data;
    }
  } catch (err) {
    console.error('Error loading categories:', err);
  }
};

const updateProducts = () => {
  // Update URL with current filters
  const query = {};
  if (selectedCategory.value) query.category = selectedCategory.value;
  if (sortBy.value !== 'Sales') query.sortBy = sortBy.value;
  if (limit.value !== '20') query.limit = limit.value;

  router.replace({ query });

  // Reload products
  if (topProductsGrid.value) {
    topProductsGrid.value.refresh();
  }
};

const resetFilters = () => {
  selectedCategory.value = '';
  sortBy.value = 'Sales';
  limit.value = '20';
  updateProducts();
};

const initializeFromQuery = () => {
  // Initialize filters from URL query parameters
  selectedCategory.value = route.query.category || '';
  sortBy.value = route.query.sortBy || 'Sales';
  limit.value = route.query.limit || '20';
};

// Watchers
watch(() => route.query, () => {
  initializeFromQuery();
  if (topProductsGrid.value) {
    topProductsGrid.value.refresh();
  }
});

// Lifecycle
onMounted(async () => {
  await loadCategories();
  initializeFromQuery();
  
  // Load products after component is ready
  if (topProductsGrid.value) {
    topProductsGrid.value.refresh();
  }
});
</script>

<style scoped>
.top-products-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin: 0 0 10px 0;
}

.page-description {
  font-size: 16px;
  color: #666;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

.filters-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
}

.reset-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  height: fit-content;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background: #5a6268;
}

@media (max-width: 768px) {
  .top-products-page {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    min-width: auto;
  }

  .reset-btn {
    align-self: center;
    margin-top: 10px;
  }
}
</style>
