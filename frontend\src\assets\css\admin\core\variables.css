/* ===== ADMIN DESIGN SYSTEM VARIABLES ===== */
/* Based on Reports page design patterns */

:root {
  /* ===== COLORS ===== */
  
  /* Primary Colors */
  --admin-primary: #667eea;
  --admin-primary-dark: #5a67d8;
  --admin-primary-light: #a5b4fc;
  --admin-primary-bg: #f0f4ff;
  
  /* Secondary Colors */
  --admin-secondary: #764ba2;
  --admin-secondary-dark: #6b46c1;
  --admin-secondary-light: #c4b5fd;
  --admin-secondary-bg: #f3f0ff;
  
  /* Status Colors */
  --admin-success: #10b981;
  --admin-success-dark: #059669;
  --admin-success-light: #6ee7b7;
  --admin-success-bg: #f0fdf4;
  
  --admin-warning: #f59e0b;
  --admin-warning-dark: #d97706;
  --admin-warning-light: #fbbf24;
  --admin-warning-bg: #fffbeb;
  
  --admin-danger: #ef4444;
  --admin-danger-dark: #dc2626;
  --admin-danger-light: #f87171;
  --admin-danger-bg: #fef2f2;
  
  --admin-info: #3b82f6;
  --admin-info-dark: #2563eb;
  --admin-info-light: #93c5fd;
  --admin-info-bg: #f8fafc;
  
  /* Neutral Colors */
  --admin-white: #ffffff;
  --admin-gray-50: #f9fafb;
  --admin-gray-100: #f3f4f6;
  --admin-gray-200: #e5e7eb;
  --admin-gray-300: #d1d5db;
  --admin-gray-400: #9ca3af;
  --admin-gray-500: #6b7280;
  --admin-gray-600: #4b5563;
  --admin-gray-700: #374151;
  --admin-gray-800: #1f2937;
  --admin-gray-900: #111827;
  
  /* Background Colors */
  --admin-bg-primary: #f8fafc;
  --admin-bg-secondary: #ffffff;
  --admin-bg-tertiary: #f9fafb;

  /* Text Colors */
  --admin-text-primary: var(--admin-gray-900);
  --admin-text-secondary: var(--admin-gray-700);
  --admin-text-muted: var(--admin-gray-500);
  --admin-text-light: var(--admin-gray-400);

  /* Border Colors */
  --admin-border-light: var(--admin-gray-200);
  
  /* ===== SPACING ===== */
  --admin-space-xs: 4px;
  --admin-space-sm: 8px;
  --admin-space-md: 12px;
  --admin-space-lg: 16px;
  --admin-space-xl: 20px;
  --admin-space-2xl: 24px;
  --admin-space-3xl: 32px;
  --admin-space-4xl: 48px;
  
  /* ===== TYPOGRAPHY ===== */
  --admin-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  
  /* Font Sizes */
  --admin-text-xs: 0.75rem;    /* 12px */
  --admin-text-sm: 0.875rem;   /* 14px */
  --admin-text-base: 1rem;     /* 16px */
  --admin-text-lg: 1.125rem;   /* 18px */
  --admin-text-xl: 1.25rem;    /* 20px */
  --admin-text-2xl: 1.5rem;    /* 24px */
  --admin-text-3xl: 1.875rem;  /* 30px */
  --admin-text-4xl: 2.25rem;   /* 36px */
  --admin-text-5xl: 3rem;      /* 48px */
  
  /* Font Weights */
  --admin-font-normal: 400;
  --admin-font-medium: 500;
  --admin-font-semibold: 600;
  --admin-font-bold: 700;
  
  /* Line Heights */
  --admin-leading-tight: 1.25;
  --admin-leading-normal: 1.5;
  --admin-leading-relaxed: 1.625;
  
  /* ===== BORDERS ===== */
  --admin-border-width: 1px;
  --admin-border-color: var(--admin-gray-200);
  --admin-border-color-light: var(--admin-gray-100);
  --admin-border-color-dark: var(--admin-gray-300);
  
  /* Border Radius */
  --admin-radius-sm: 4px;
  --admin-radius-md: 6px;
  --admin-radius-lg: 8px;
  --admin-radius-xl: 12px;
  --admin-radius-2xl: 16px;
  --admin-radius-full: 9999px;
  
  /* ===== SHADOWS ===== */
  --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --admin-shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --admin-shadow-xl: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --admin-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Focus Shadow */
  --admin-shadow-focus: 0 0 0 3px rgba(102, 126, 234, 0.1);
  
  /* ===== TRANSITIONS ===== */
  --admin-transition-fast: 0.15s ease;
  --admin-transition-normal: 0.2s ease;
  --admin-transition-slow: 0.3s ease;
  
  /* ===== Z-INDEX ===== */
  --admin-z-dropdown: 1000;
  --admin-z-sticky: 1020;
  --admin-z-fixed: 1030;
  --admin-z-modal-backdrop: 1040;
  --admin-z-modal: 1050;
  --admin-z-popover: 1060;
  --admin-z-tooltip: 1070;
  
  /* ===== LAYOUT ===== */
  --admin-container-max-width: 1500px;
  --admin-sidebar-width: 320px;
  --admin-header-height: 64px;
  
  /* ===== GRADIENTS ===== */
  --admin-gradient-primary: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-secondary) 100%);
  --admin-gradient-success: linear-gradient(135deg, var(--admin-success-bg) 0%, var(--admin-gray-50) 100%);
  --admin-gradient-warning: linear-gradient(135deg, var(--admin-warning-bg) 0%, var(--admin-gray-50) 100%);
  --admin-gradient-danger: linear-gradient(135deg, var(--admin-danger-bg) 0%, var(--admin-gray-50) 100%);
  
  /* ===== COMPONENT SPECIFIC ===== */
  
  /* Cards */
  --admin-card-bg: var(--admin-white);
  --admin-card-border: var(--admin-border-color);
  --admin-card-shadow: var(--admin-shadow-md);
  --admin-card-radius: var(--admin-radius-xl);
  --admin-card-padding: var(--admin-space-2xl);
  
  /* Buttons */
  --admin-btn-padding-sm: var(--admin-space-sm) var(--admin-space-lg);
  --admin-btn-padding-md: var(--admin-space-md) var(--admin-space-2xl);
  --admin-btn-padding-lg: var(--admin-space-lg) var(--admin-space-3xl);
  --admin-btn-radius: var(--admin-radius-lg);
  
  /* Forms */
  --admin-input-padding: var(--admin-space-md);
  --admin-input-border: var(--admin-border-color);
  --admin-input-border-focus: var(--admin-primary);
  --admin-input-radius: var(--admin-radius-md);
  --admin-input-shadow-focus: var(--admin-shadow-focus);
  
  /* Tables */
  --admin-table-border: var(--admin-border-color);
  --admin-table-header-bg: var(--admin-gray-50);
  --admin-table-row-hover: var(--admin-gray-50);
  
  /* Metrics */
  --admin-metric-card-bg: var(--admin-gray-50);
  --admin-metric-card-border: var(--admin-border-color);
  --admin-metric-card-padding: var(--admin-space-xl);
  --admin-metric-card-radius: var(--admin-radius-lg);
}
