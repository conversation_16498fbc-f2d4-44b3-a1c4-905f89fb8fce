import apiClient from './api.service';

class CompanyService {
  /**
   * Отримати компанію за ID
   * @param {string} id - ID компанії
   * @returns {Promise} - Інформація про компанію
   */
  async getById(id) {
    try {
      const response = await apiClient.get(`/companies/${id}`);
      return response;
    } catch (error) {
      console.error(`Error fetching company ${id}:`, error);
      throw error;
    }
  }

  /**
   * Отримати компанію за slug
   * @param {string} slug - Slug компанії
   * @returns {Promise} - Інформація про компанію
   */
  async getBySlug(slug) {
    try {
      const response = await apiClient.get(`/companies/slug/${slug}`);
      return response;
    } catch (error) {
      console.error(`Error fetching company by slug ${slug}:`, error);
      throw error;
    }
  }

  /**
   * Отримати публічний профіль компанії
   * @param {string} companySlug - Slug компанії
   * @returns {Promise} - Публічна інформація про компанію
   */
  async getCompanyProfile(companySlug) {
    try {
      const response = await apiClient.get(`/companies/slug/${companySlug}/profile`);
      return response;
    } catch (error) {
      console.error(`Error fetching company profile for ${companySlug}:`, error);
      throw error;
    }
  }

  /**
   * Отримати всі компанії з пагінацією
   * @param {Object} params - Параметри запиту (page, pageSize, filter, orderBy, descending)
   * @returns {Promise} - Пагінований список компаній
   */
  async getAll(params = {}) {
    try {
      const response = await apiClient.get('/companies', { params });
      return response;
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  }

  /**
   * Отримати користувачів компанії
   * @param {string} companyId - ID компанії
   * @returns {Promise} - Список користувачів компанії
   */
  async getCompanyUsers(companyId) {
    try {
      const response = await apiClient.get(`/companies/${companyId}/users`);
      return response;
    } catch (error) {
      console.error(`Error fetching company users for ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Отримати товари компанії
   * @param {string} companySlug - Slug компанії
   * @param {Object} params - Параметри запиту
   * @returns {Promise} - Список товарів компанії
   */
  async getCompanyProducts(companySlug, params = {}) {
    try {
      const response = await apiClient.get(`/companies/slug/${companySlug}/products`, { params });
      return response;
    } catch (error) {
      console.error(`Error fetching company products for ${companySlug}:`, error);
      throw error;
    }
  }

  /**
   * Отримати статистику компанії
   * @param {string} companySlug - Slug компанії
   * @returns {Promise} - Статистика компанії
   */
  async getCompanyStats(companySlug) {
    try {
      const response = await apiClient.get(`/companies/slug/${companySlug}/stats`);
      return response;
    } catch (error) {
      console.error(`Error fetching company stats for ${companySlug}:`, error);
      throw error;
    }
  }

  /**
   * Отримати рейтинг компанії
   * @param {string} companySlug - Slug компанії
   * @returns {Promise} - Рейтинг компанії
   */
  async getCompanyRating(companySlug) {
    try {
      const response = await apiClient.get(`/companies/slug/${companySlug}/rating`);
      return response;
    } catch (error) {
      console.error(`Error fetching company rating for ${companySlug}:`, error);
      throw error;
    }
  }

  /**
   * Пошук компаній
   * @param {Object} params - Параметри пошуку
   * @returns {Promise} - Результати пошуку
   */
  async search(params = {}) {
    try {
      const response = await apiClient.get('/companies/search', { params });
      return response;
    } catch (error) {
      console.error('Error searching companies:', error);
      throw error;
    }
  }

  /**
   * Отримати рекомендовані компанії
   * @param {number} limit - Кількість компаній
   * @returns {Promise} - Список рекомендованих компаній
   */
  async getRecommended(limit = 5) {
    try {
      const response = await apiClient.get('/companies/recommended', {
        params: { limit }
      });
      return response;
    } catch (error) {
      console.error('Error fetching recommended companies:', error);
      throw error;
    }
  }

  /**
   * Отримати топ компанії
   * @param {number} limit - Кількість компаній
   * @returns {Promise} - Список топ компаній
   */
  async getTop(limit = 10) {
    try {
      const response = await apiClient.get('/companies/top', {
        params: { limit }
      });
      return response;
    } catch (error) {
      console.error('Error fetching top companies:', error);
      throw error;
    }
  }
}

export default new CompanyService();
