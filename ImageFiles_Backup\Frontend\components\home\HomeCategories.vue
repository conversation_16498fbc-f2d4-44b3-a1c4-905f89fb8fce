<template>
  <section class="categories-section">
    <div class="container">
      <div v-if="categories" class="categories-grid">
          <div v-for="category in categories" :key="category.id" class="category-item">
            <div class="category-icon">
              <img v-if="category.image" :src="category.image || '@assets/images/icons/placeholder-icon.svg'" :alt="category.name">
            </div>
            <a :href="`/catalog/${category.slug}`" class="category-name" v-if="category.name">{{ category.name }}</a>
          </div>
        </div>
      <div v-else>Loading categories...</div>
  </div>
  </section>
</template>

<script>
export default {
  props: {
    categories: {
      type: Array,
      default: () => [],
    }
  }
}
</script>

<style scoped>

.categories-section {
  margin-bottom: 48px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  margin: 0 auto;
  max-width: 1200px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 16px;
}

.category-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.category-icon img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.category-name {
  font-size: 15px;
  font-weight: 500;
  color: black;
  max-width: 120px;
  line-height: 1.2;
}

.category-name:hover {
  color: #ABAAAA;
}

@media (max-width: 1024px) {
  .categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .categories-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>