using Marketplace.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Marketplace.Infrastructure.Persistence.Configurations;

public class SettingConfiguration : IEntityTypeConfiguration<Setting>
{
    public void Configure(EntityTypeBuilder<Setting> builder)
    {
        builder.HasKey(s => s.Id);

        builder.Property(s => s.Key)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(s => s.Value)
            .HasMaxLength(2000)
            .IsRequired();

        builder.Property(s => s.Category)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(s => s.Description)
            .HasMaxLength(500);

        builder.Property(s => s.Type)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(s => s.IsPublic)
            .IsRequired();

        builder.Property(s => s.CreatedAt)
            .IsRequired();

        builder.Property(s => s.UpdatedAt);

        // Indexes
        builder.HasIndex(s => s.Key)
            .IsUnique();

        builder.HasIndex(s => s.Category);

        builder.HasIndex(s => s.IsPublic);

        // Seed default settings - temporarily disabled for migration
        // SeedDefaultSettings(builder);
    }

    private void SeedDefaultSettings(EntityTypeBuilder<Setting> builder)
    {
        var now = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        builder.HasData(
            // General Settings
            new
            {
                Id = new Guid("11111111-1111-1111-1111-111111111111"),
                Key = "site_name",
                Value = "Marketplace",
                Category = "general",
                Type = (int)SettingType.String,
                Description = "Site name displayed in header",
                IsPublic = true,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            new
            {
                Id = new Guid("11111111-1111-1111-1111-111111111112"),
                Key = "site_description",
                Value = "Your one-stop marketplace for all your needs",
                Category = "general",
                Type = (int)SettingType.String,
                Description = "Site description for SEO",
                IsPublic = true,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            new
            {
                Id = new Guid("11111111-1111-1111-1111-111111111113"),
                Key = "site_email",
                Value = "<EMAIL>",
                Category = "general",
                Type = (int)SettingType.Email,
                Description = "Main contact email",
                IsPublic = true,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            new
            {
                Id = new Guid("11111111-1111-1111-1111-111111111114"),
                Key = "currency",
                Value = "UAH",
                Category = "general",
                Type = (int)SettingType.String,
                Description = "Default currency",
                IsPublic = true,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            new
            {
                Id = new Guid("11111111-1111-1111-1111-111111111115"),
                Key = "currency_symbol",
                Value = "₴",
                Category = "general",
                Type = (int)SettingType.String,
                Description = "Currency symbol",
                IsPublic = true,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            new
            {
                Id = new Guid("11111111-1111-1111-1111-111111111116"),
                Key = "maintenance_mode",
                Value = "false",
                Category = "general",
                Type = (int)SettingType.Boolean,
                Description = "Enable maintenance mode",
                IsPublic = false,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            // Payment Settings
            new
            {
                Id = new Guid("*************-2222-2222-************"),
                Key = "commission_rate",
                Value = "5",
                Category = "payment",
                Type = (int)SettingType.Decimal,
                Description = "Commission rate percentage",
                IsPublic = false,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            // Email Settings
            new
            {
                Id = new Guid("*************-3333-3333-************"),
                Key = "from_email",
                Value = "<EMAIL>",
                Category = "email",
                Type = (int)SettingType.Email,
                Description = "From email address",
                IsPublic = false,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            new
            {
                Id = new Guid("*************-3333-3333-************"),
                Key = "from_name",
                Value = "Marketplace",
                Category = "email",
                Type = (int)SettingType.String,
                Description = "From name",
                IsPublic = false,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            },
            // API Settings
            new
            {
                Id = new Guid("*************-4444-4444-************"),
                Key = "api_rate_limit",
                Value = "60",
                Category = "api",
                Type = (int)SettingType.Integer,
                Description = "API rate limit per minute",
                IsPublic = false,
                CreatedAt = now,
                UpdatedAt = (DateTime?)null
            }
        );
    }
}
