<template>
  <div class="rating-grid-container">
    <!-- Loading State -->
    <div v-if="displayLoading" class="loading-container">
      <div class="loader"></div>
      <p>Завантаження відгуків...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="displayError" class="error-container">
      <h3>Помилка завантаження</h3>
      <p>{{ displayError }}</p>
      <button v-if="autoFetch" @click="fetchRating" class="retry-btn">Спробувати знову</button>
    </div>

    <!-- Empty State -->
    <div v-else-if="!displayRatingData || displayRatingData.length === 0" class="empty-container">
      <h3>Відгуки не знайдено</h3>
      <p>{{ emptyMessage || 'Відгуків поки немає' }}</p>
    </div>

    <!-- Rating List -->
    <div
      v-if="displayRatingData && displayRatingData.length > 0"
      class="rating-list rating-list-scrollable"
      :style="{ maxHeight: scrollHeight }"
    >
      <div
        v-for="rating in displayRatingData"
        :key="rating.id"
        class="rating-card card-hover"
        tabindex="0"
        @click="handleRatingClick(rating)"
      >
        <div class="rating-card-header">
          <div class="rating-product-info">
            <div class="rating-product-name">{{ rating.productName }}</div>
            <div class="rating-stars">
              <span v-for="n in 5" :key="n">
                <i :class="n <= (rating.averageRating || rating.rating || rating.stars) ? 'fa-solid fa-star star-filled' : 'fa-regular fa-star star-empty'"></i>
              </span>
            </div>
            <div v-if="rating.createdAt || rating.date" class="rating-date">
              {{ formatDate(rating.createdAt || rating.date) }}
            </div>
          </div>
        </div>
        <div class="rating-text">{{ rating.comment || rating.text }}</div>
        <div v-if="showUserInfo && (rating.userName || rating.userEmail)" class="rating-user-info">
          <span class="rating-user-name">{{ rating.userName || rating.userEmail }}</span>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="showPagination && displayRatingData && displayRatingData.length > 0"
      :current-page="displayCurrentPage"
      :total-items="displayTotalItems"
      :page-size="displayPageSize"
      @page-changed="handlePageChange"
    />
  </div>
</template>

<script>
import Pagination from '@/components/catalog/Pagination.vue';
import { useToast } from '@/composables/useToast';

export default {
  name: 'ratingGrid',
  components: {
    Pagination
  },
  setup() {
    const { showToast } = useToast();
    return { showToast };
  },
  props: {
    // Fetch parameters (for auto-fetch mode)
    fetchParams: {
      type: Object,
      default: () => ({})
    },
    // External data (for manual mode)
    rating: {
      type: Array,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: null
    },
    // Pagination props (for manual mode)
    currentPage: {
      type: Number,
      default: 1
    },
    totalItems: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    },
    // Display options
    emptyMessage: {
      type: String,
      default: 'Відгуків поки немає'
    },
    showPagination: {
      type: Boolean,
      default: false
    },
    showUserInfo: {
      type: Boolean,
      default: false
    },
    // Auto-fetch on mount
    autoFetch: {
      type: Boolean,
      default: true
    },
    // API endpoint for fetching rating
    apiEndpoint: {
      type: String,
      default: '/api/rating'
    },
    // Scroll height for the rating list
    scrollHeight: {
      type: String,
      default: '600px'
    }
  },
  data() {
    return {
      placeholderImage: '/placeholder-product.svg',
      internalRating: [],
      internalLoading: false,
      internalError: null,
      internalCurrentPage: 1,
      internalTotalItems: 0,
      internalPageSize: 10
    }
  },
  computed: {
    // Use external data if provided, otherwise use internal data
    displayRating() {
      //console.log('Displaying rating:', this.rating !== null);
      //console.log('Rating data: ', this.rating);
      //console.log('Internal rating: ', this.internalRating);
      return this.rating !== null ? this.rating : this.internalRating;
    },
    displayRatingData() {
      const rating = this.displayRating;
      if (!rating) return [];

      // Якщо rating має структуру з data властивістю
      if (rating.data && Array.isArray(rating.data)) {
        return rating.data;
      }

      // Якщо rating - це простий масив
      if (Array.isArray(rating)) {
        return rating;
      }

      return [];
    },
    displayLoading() {
      return this.rating !== null ? this.loading : this.internalLoading;
    },
    displayError() {
      return this.rating !== null ? this.error : this.internalError;
    },
    displayCurrentPage() {
      return this.rating !== null ? this.currentPage : this.internalCurrentPage;
    },
    displayTotalItems() {
      return this.rating !== null ? this.totalItems : this.internalTotalItems;
    },
    displayPageSize() {
      return this.rating !== null ? this.pageSize : this.internalPageSize;
    }
  },
  async mounted() {
    if (this.autoFetch && this.rating === null) {
      await this.fetchRating();
    }
  },
  methods: {
    async fetchRating() {
      if (this.rating !== null) return; // Don't fetch if external data is provided

      this.internalLoading = true;
      this.internalError = null;

      try {
        const params = {
          page: this.internalCurrentPage,
          pageSize: this.internalPageSize,
          ...this.fetchParams
        };

        const response = await fetch(`${this.apiEndpoint}?${new URLSearchParams(params)}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.success) {
          this.internalRating = data.data?.items || data.data || [];
          this.internalTotalItems = data.data?.totalItems || data.totalItems || 0;
          this.internalCurrentPage = data.data?.currentPage || data.currentPage || 1;
          this.internalPageSize = data.data?.pageSize || data.pageSize || 10;
        } else {
          throw new Error(data.message || 'Помилка завантаження відгуків');
        }
      } catch (error) {
        console.error('Error fetching rating:', error);
        this.internalError = error.message || 'Помилка завантаження відгуків';
        this.showToast('Помилка завантаження відгуків', 'error');
      } finally {
        this.internalLoading = false;
      }
    },
    async handlePageChange(page) {
      if (this.rating !== null) {
        // External pagination - emit event
        this.$emit('page-changed', page);
      } else {
        // Internal pagination - fetch new data
        this.internalCurrentPage = page;
        await this.fetchRating();
      }
    },
    handleRatingClick(rating) {
      this.$emit('rating-clicked', rating);
    },
    handleImageError(event) {
      event.target.src = this.placeholderImage;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('uk-UA', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      } catch (error) {
        return dateString;
      }
    }
  }
}
</script>

<style scoped>
.rating-grid-container {
  width: 100%;
}

/* Loading, Error, Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container h3,
.empty-container h3 {
  color: #666;
  margin-bottom: 10px;
  font-size: 18px;
}

.error-container p,
.empty-container p {
  color: #999;
  margin-bottom: 20px;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #0056b3;
}

/* Rating List */
.rating-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.rating-list-scrollable {
  overflow-y: auto;
  padding-right: 8px;
}

/* Custom scrollbar styling */
.rating-list-scrollable::-webkit-scrollbar {
  width: 8px;
}

.rating-list-scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.rating-list-scrollable::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.rating-list-scrollable::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Firefox scrollbar styling */
.rating-list-scrollable {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.rating-card {
  background: #fff;
  border-radius: 14px;
  border: 2px solid #e0e0e0;
  padding: 18px 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rating-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* .rating-card:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
} */

.rating-card-header {
  display: flex;
  align-items: center;
  gap: 18px;
}

.rating-product-img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 8px;
  background: #f3f6fa;
  flex-shrink: 0;
}

.rating-product-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.rating-product-name {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  line-height: 1.3;
}

.rating-stars {
  font-size: 18px;
  color: #ffb400;
}

.star-filled {
  color: #ffb400;
}

.star-empty {
  color: #ddd;
}

.rating-date {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.rating-text {
  font-size: 16px;
  color: #222;
  line-height: 1.5;
  margin-top: -8px;
}

.rating-user-info {
  margin-left: 78px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
}

.rating-user-name {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .rating-list-scrollable:not([style*="max-height"]) {
    max-height: 400px;
  }

  .rating-card {
    padding: 16px;
  }

  .rating-card-header {
    gap: 12px;
  }

  .rating-product-img {
    width: 50px;
    height: 50px;
  }

  .rating-text {
    margin-left: 62px;
  }

  .rating-user-info {
    margin-left: 62px;
  }
}

@media (max-width: 480px) {
  .rating-list-scrollable:not([style*="max-height"]) {
    max-height: 300px;
  }

  .rating-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .rating-text,
  .rating-user-info {
    margin-left: 0;
  }

  .rating-product-img {
    align-self: center;
  }
}
</style>
