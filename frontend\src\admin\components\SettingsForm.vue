<template>
  <div class="settings-form">
    <div class="box">
      <h3 class="subtitle">{{ title }}</h3>
      <p v-if="description" class="has-text-grey mb-4">{{ description }}</p>

      <form @submit.prevent="handleSubmit">
        <div v-for="field in fields" :key="field.key" class="field">
          <label class="label">
            {{ field.label }}
            <span v-if="field.required" class="has-text-danger">*</span>
          </label>
          
          <!-- Text Input -->
          <div v-if="field.type === 'text'" class="control">
            <input
              class="input"
              :class="{ 'is-danger': hasError(field.key) }"
              type="text"
              :placeholder="field.placeholder"
              v-model="formData[field.key]"
              @blur="validateField(field.key)"
            />
          </div>

          <!-- Email Input -->
          <div v-else-if="field.type === 'email'" class="control">
            <input
              class="input"
              :class="{ 'is-danger': hasError(field.key) }"
              type="email"
              :placeholder="field.placeholder"
              v-model="formData[field.key]"
              @blur="validateField(field.key)"
            />
          </div>

          <!-- Password Input -->
          <div v-else-if="field.type === 'password'" class="control has-icons-right">
            <input
              class="input"
              :class="{ 'is-danger': hasError(field.key) }"
              :type="showPassword[field.key] ? 'text' : 'password'"
              :placeholder="field.placeholder"
              v-model="formData[field.key]"
              @blur="validateField(field.key)"
            />
            <span class="icon is-small is-right" @click="togglePassword(field.key)">
              <i :class="showPassword[field.key] ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </span>
          </div>

          <!-- Number Input -->
          <div v-else-if="field.type === 'number'" class="control">
            <input
              class="input"
              :class="{ 'is-danger': hasError(field.key) }"
              type="number"
              :placeholder="field.placeholder"
              :min="field.min"
              :max="field.max"
              :step="field.step"
              v-model="formData[field.key]"
              @blur="validateField(field.key)"
            />
          </div>

          <!-- Textarea -->
          <div v-else-if="field.type === 'textarea'" class="control">
            <textarea
              class="textarea"
              :class="{ 'is-danger': hasError(field.key) }"
              :placeholder="field.placeholder"
              :rows="field.rows || 3"
              v-model="formData[field.key]"
              @blur="validateField(field.key)"
            ></textarea>
          </div>

          <!-- Select -->
          <div v-else-if="field.type === 'select'" class="control">
            <div class="select is-fullwidth">
              <select
                :class="{ 'is-danger': hasError(field.key) }"
                v-model="formData[field.key]"
                @change="validateField(field.key)"
              >
                <option value="">{{ field.placeholder || 'Select an option' }}</option>
                <option
                  v-for="option in field.options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
            </div>
          </div>

          <!-- Boolean/Checkbox -->
          <div v-else-if="field.type === 'boolean'" class="control">
            <label class="checkbox">
              <input
                type="checkbox"
                v-model="formData[field.key]"
                @change="validateField(field.key)"
              />
              {{ field.checkboxLabel || 'Enable this option' }}
            </label>
          </div>

          <!-- URL Input -->
          <div v-else-if="field.type === 'url'" class="control">
            <input
              class="input"
              :class="{ 'is-danger': hasError(field.key) }"
              type="url"
              :placeholder="field.placeholder"
              v-model="formData[field.key]"
              @blur="validateField(field.key)"
            />
          </div>

          <!-- Error Messages -->
          <p v-if="hasError(field.key)" class="help is-danger">
            {{ getError(field.key) }}
          </p>

          <!-- Help Text -->
          <p v-if="field.help" class="help">
            {{ field.help }}
          </p>
        </div>

        <!-- Form Actions -->
        <div class="field is-grouped">
          <div class="control">
            <button
              type="submit"
              class="button is-primary"
              :class="{ 'is-loading': loading }"
              :disabled="!isFormValid || loading"
            >
              <span class="icon"><i class="fas fa-save"></i></span>
              <span>{{ submitLabel || 'Save Settings' }}</span>
            </button>
          </div>
          <div class="control">
            <button
              type="button"
              class="button is-light"
              @click="handleReset"
              :disabled="loading"
            >
              <span class="icon"><i class="fas fa-undo"></i></span>
              <span>Reset</span>
            </button>
          </div>
          <div v-if="showValidate" class="control">
            <button
              type="button"
              class="button is-info"
              @click="handleValidate"
              :disabled="loading"
            >
              <span class="icon"><i class="fas fa-check-circle"></i></span>
              <span>Validate</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { settingsService } from '@/admin/services/settings';

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  fields: {
    type: Array,
    required: true
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  submitLabel: {
    type: String,
    default: 'Save Settings'
  },
  showValidate: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['submit', 'reset', 'validate', 'change']);

// Reactive data
const formData = ref({});
const errors = ref({});
const showPassword = ref({});

// Computed
const isFormValid = computed(() => {
  return Object.keys(errors.value).length === 0 && 
         props.fields.filter(f => f.required).every(f => formData.value[f.key]);
});

// Methods
const initializeForm = () => {
  // Initialize form data
  const data = {};
  props.fields.forEach(field => {
    data[field.key] = props.initialData[field.key] || getDefaultValue(field);
  });
  formData.value = data;

  // Initialize password visibility
  const passwordFields = {};
  props.fields.filter(f => f.type === 'password').forEach(field => {
    passwordFields[field.key] = false;
  });
  showPassword.value = passwordFields;
};

const getDefaultValue = (field) => {
  switch (field.type) {
    case 'boolean':
      return false;
    case 'number':
      return field.min || 0;
    case 'select':
      return field.options?.[0]?.value || '';
    default:
      return '';
  }
};

const validateField = (key) => {
  const field = props.fields.find(f => f.key === key);
  if (!field) return;

  const value = formData.value[key];
  const fieldErrors = settingsService.validateSetting(key, value, field.type);

  if (fieldErrors.length > 0) {
    errors.value[key] = fieldErrors[0];
  } else {
    delete errors.value[key];
  }
};

const validateAllFields = () => {
  props.fields.forEach(field => {
    validateField(field.key);
  });
};

const hasError = (key) => {
  return !!errors.value[key];
};

const getError = (key) => {
  return errors.value[key];
};

const togglePassword = (key) => {
  showPassword.value[key] = !showPassword.value[key];
};

const handleSubmit = () => {
  validateAllFields();
  if (isFormValid.value) {
    emit('submit', { ...formData.value });
  }
};

const handleReset = () => {
  initializeForm();
  errors.value = {};
  emit('reset');
};

const handleValidate = () => {
  validateAllFields();
  emit('validate', { 
    isValid: isFormValid.value, 
    errors: { ...errors.value },
    data: { ...formData.value }
  });
};

// Watch for changes
watch(formData, (newData) => {
  emit('change', newData);
}, { deep: true });

watch(() => props.initialData, () => {
  initializeForm();
}, { deep: true });

// Lifecycle
onMounted(() => {
  initializeForm();
});
</script>

<style scoped>
.settings-form {
  margin-bottom: 2rem;
}

.icon.is-right {
  cursor: pointer;
  pointer-events: all;
}

.field:not(:last-child) {
  margin-bottom: 1.5rem;
}

.help {
  margin-top: 0.25rem;
}

.checkbox {
  font-weight: normal;
}

.is-grouped .control:not(:last-child) {
  margin-right: 0.75rem;
}
</style>
