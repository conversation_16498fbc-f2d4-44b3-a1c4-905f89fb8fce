<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Products
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-box admin-page-icon"></i>
          Product Details
        </h1>
        <p class="admin-page-subtitle">View and manage product information</p>
      </div>
      <div class="admin-page-actions">
        <button @click="toggleStatus" :disabled="loading" class="admin-btn admin-btn-secondary">
          <i class="fas fa-toggle-on" v-if="product?.isActive"></i>
          <i class="fas fa-toggle-off" v-else></i>
          {{ product?.isActive ? 'Deactivate' : 'Activate' }}
        </button>
        <router-link
          :to="`/admin/products/${product?.id}/edit`"
          class="admin-btn admin-btn-primary">
          <i class="fas fa-edit"></i>
          Edit Product
        </router-link>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading product details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error loading product</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="loadProduct" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-retry"></i>
        Retry
      </button>
    </div>

    <!-- Not Found State -->
    <div v-else-if="!product?.id" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-box"></i>
      </div>
      <h3 class="admin-empty-title">Product not found</h3>
      <p class="admin-empty-text">The product you're looking for doesn't exist or has been removed.</p>
      <button @click="goBack" class="admin-btn admin-btn-primary">
        <i class="fas fa-arrow-left"></i>
        Back to Products
      </button>
    </div>
    <div v-else>
      <!-- Product Header -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="columns">
            <div class="column is-8">
              <h2 class="product-title">{{ product.name }}</h2>
              <p class="product-sku">SKU: {{ product.sku || 'N/A' }}</p>
            </div>
            <div class="column is-4 has-text-right">
              <div class="buttons is-right">
                <router-link 
                  :to="`/admin/products/${product.id}/edit`" 
                  class="button is-primary">
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                  <span>Edit</span>
                </router-link>
                <button 
                  class="button is-danger" 
                  @click="confirmDelete">
                  <span class="icon">
                    <i class="fas fa-trash"></i>
                  </span>
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="columns">
        <!-- Product Images -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Product Images</p>
            </div>
            <div class="card-content">
              <AdminProductImagesViewer
                :product="product"
                @edit="goToEdit"
              />
            </div>
          </div>

          <!-- Meta Image Section -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Meta Image (SEO)</p>
            </div>
            <div class="card-content">
              <div v-if="product.metaImage" class="meta-image-container">
                <div class="meta-image-wrapper">
                  <img
                    :src="product.metaImage"
                    :alt="product.name + ' - Meta Image'"
                    class="meta-image"
                    @click="openMetaImageModal"
                    @error="handleImageError"
                  />
                  <div class="meta-image-overlay">
                    <button
                      class="admin-btn admin-btn-sm admin-btn-secondary"
                      @click="openMetaImageModal"
                      title="View full size"
                    >
                      <i class="fas fa-search-plus"></i>
                    </button>
                  </div>
                </div>
                <p class="meta-image-description">
                  This image is used for SEO and social media sharing.
                </p>
              </div>
              <div v-else class="no-meta-image">
                <div class="no-meta-image-icon">
                  <i class="fas fa-image"></i>
                </div>
                <p class="no-meta-image-text">No Meta Image</p>
                <p class="no-meta-image-description">
                  No meta image has been set for this product. A meta image helps with SEO and social media sharing.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Product Information -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Product Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Price</h3>
                    <p class="info-value">{{ formatCurrency(product.price) }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Status</h3>
                    <p class="info-value">
                      <status-badge 
                        :status="product.status" 
                        type="product" />
                    </p>
                  </div>
                </div>
              </div>

              <div class="columns">
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Category</h3>
                    <p class="info-value">{{ product.categoryName || 'N/A' }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Status</h3>
                    <p class="info-value">
                      <status-badge :status="product.status" type="product" />
                    </p>
                  </div>
                </div>
              </div>

              <div class="columns">
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Stock</h3>
                    <p class="info-value">
                      <span
                        class="tag"
                        :class="getStockClass(product.stock)">
                        {{ product.stock }}
                      </span>
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Sales</h3>
                    <p class="info-value">
                      <span class="tag is-success">
                        {{ product.sales || 0 }} sold
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              <div class="info-group">
                <h3 class="info-label">Description</h3>
                <div class="content">
                  <p>{{ product.description }}</p>
                </div>
              </div>

              <div v-if="product.tags && product.tags.length > 0" class="info-group">
                <h3 class="info-label">Tags</h3>
                <div class="tags">
                  <span 
                    v-for="(tag, index) in product.tags" 
                    :key="index" 
                    class="tag is-primary">
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Details -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Additional Details</p>
            </div>
            <div class="card-content">
              <div class="columns is-multiline">
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Compare at Price</h3>
                    <p class="info-value">
                      {{ product.compareAtPrice ? formatCurrency(product.compareAtPrice) : 'N/A' }}
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Cost Price</h3>
                    <p class="info-value">
                      {{ product.costPrice ? formatCurrency(product.costPrice) : 'N/A' }}
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Weight</h3>
                    <p class="info-value">
                      {{ product.weight ? `${product.weight} kg` : 'N/A' }}
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Featured</h3>
                    <p class="info-value">
                      <span class="icon">
                        <i 
                          class="fas" 
                          :class="product.isFeatured ? 'fa-check has-text-success' : 'fa-times has-text-danger'">
                        </i>
                      </span>
                      {{ product.isFeatured ? 'Yes' : 'No' }}
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Created At</h3>
                    <p class="info-value">
                      {{ formatDate(product.createdAt) }}
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="info-group">
                    <h3 class="info-label">Last Updated</h3>
                    <p class="info-value">
                      {{ formatDate(product.updatedAt) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete Product"
      :message="`Are you sure you want to delete '${product.name}'? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteProduct"
      @cancel="cancelDelete" />

    <!-- Meta Image Modal -->
    <div v-if="showMetaImageModal" class="modal is-active">
      <div class="modal-background" @click="closeMetaImageModal"></div>
      <div class="modal-content">
        <p class="image">
          <img :src="metaImageModalUrl" :alt="product.name + ' - Meta Image'" />
        </p>
      </div>
      <button class="modal-close is-large" @click="closeMetaImageModal"></button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { productsService } from '@/admin/services/products';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import AdminProductImagesViewer from '@/admin/components/products/AdminProductImagesViewer.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const error = ref(null);
const product = ref({});
const activeImageIndex = ref(0);
const showDeleteModal = ref(false);
const showMetaImageModal = ref(false);
const metaImageModalUrl = ref('');

// Computed properties
const productId = computed(() => route.params.id);
const mainImage = computed(() => {
  if (!product.value.images || product.value.images.length === 0) {
    return 'https://via.placeholder.com/500?text=No+Image';
  }
  
  const mainImg = product.value.images.find(img => img.isMain);
  if (mainImg) {
    return mainImg.url;
  }
  
  return product.value.images[activeImageIndex.value].url;
});

// Fetch product data
const fetchProduct = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const data = await productsService.getProductByIdWithImages(productId.value);
    product.value = data;
    
    // Ensure images is an array and convert from API format
    if (!Array.isArray(product.value.images)) {
      product.value.images = [];
    } else {
      // Convert API images to display format
      product.value.images = product.value.images.map(image => ({
        id: image.id || image.Id,
        url: image.image || image.Image || image.imageUrl || image.ImageUrl,
        alt: image.altText || image.AltText || 'Product image',
        isMain: image.isMain || image.IsMain || false,
        order: image.order || image.Order || 0
      })).sort((a, b) => a.order - b.order);
    }
    
    // Handle tags if they come as a string
    if (typeof product.value.tags === 'string') {
      product.value.tags = product.value.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    }
  } catch (err) {
    console.error('Error fetching product:', err);
    error.value = 'Failed to load product data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Format currency
const formatCurrency = (value) => {
  if (!value || isNaN(value)) return '₴0.00';

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH', // Display as UAH for user, but backend uses HRN
    minimumFractionDigits: 2
  }).format(value);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    // Handle backend date format: "2025-06-02 20:35:40.231835+03"
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  } catch (e) {
    console.error('Error formatting date:', e, dateString);
    return dateString;
  }
};

// Get stock class
const getStockClass = (stock) => {
  if (stock <= 0) {
    return 'is-danger';
  } else if (stock <= 5) {
    return 'is-warning';
  } else {
    return 'is-success';
  }
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/500?text=No+Image';
};

// Meta image modal methods
const openMetaImageModal = () => {
  metaImageModalUrl.value = product.value.metaImage;
  showMetaImageModal.value = true;
};

const closeMetaImageModal = () => {
  showMetaImageModal.value = false;
  metaImageModalUrl.value = '';
};

// Set active image
const setActiveImage = (index) => {
  activeImageIndex.value = index;
};

// Confirm delete
const confirmDelete = () => {
  showDeleteModal.value = true;
};

// Delete product
const deleteProduct = async () => {
  try {
    await productsService.deleteProduct(productId.value);
    router.push('/admin/products');
  } catch (err) {
    console.error('Error deleting product:', err);
    error.value = 'Failed to delete product. Please try again.';
  } finally {
    showDeleteModal.value = false;
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
};

// Go back to products list
const goBack = () => {
  router.push('/admin/products');
};

// Go to edit page
const goToEdit = () => {
  router.push(`/admin/products/${productId.value}/edit`);
};

// Toggle product status
const toggleStatus = async () => {
  try {
    loading.value = true;
    const newStatus = !product.value.isActive;

    await productsService.updateProductStatus(productId.value, newStatus);
    product.value.isActive = newStatus;

    // Show success message (you can implement toast notifications)
    console.log(`Product ${newStatus ? 'activated' : 'deactivated'} successfully`);

  } catch (err) {
    console.error('Error toggling product status:', err);
    error.value = 'Failed to update product status. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Load product (alias for fetchProduct for consistency)
const loadProduct = () => {
  fetchProduct();
};

// Lifecycle hooks
onMounted(() => {
  fetchProduct();
});
</script>

<style scoped>
.product-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.product-sku {
  font-size: 0.9rem;
  color: #7a7a7a;
}

.product-images {
  display: flex;
  flex-direction: column;
}

.main-image {
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-thumbnails {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}

.thumbnail.is-active {
  border-color: #ff7700;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #7a7a7a;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.tag.is-primary {
  background-color: #ff7700;
}

/* Meta Image Styles */
.meta-image-container {
  text-align: center;
}

.meta-image-wrapper {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.meta-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.meta-image:hover {
  transform: scale(1.05);
}

.meta-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.meta-image-wrapper:hover .meta-image-overlay {
  opacity: 1;
}

.meta-image-description {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.no-meta-image {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-meta-image-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.no-meta-image-text {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.no-meta-image-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.admin-btn-sm {
  padding: 4px 8px;
  font-size: 0.75rem;
}

.admin-btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

.admin-btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  margin: 5% auto;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Product section styles */
.product-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.section-title i {
  color: #007bff;
}

/* Enhanced info-group styles */
.info-group {
  margin-bottom: 1rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
  color: #212529;
  font-weight: 500;
}

/* Sales tag enhancement */
.tag.is-success {
  background-color: #28a745;
  color: white;
  font-weight: 600;
}

.modal-close:before {
  content: '×';
  font-size: 24px;
  line-height: 1;
}
</style>
