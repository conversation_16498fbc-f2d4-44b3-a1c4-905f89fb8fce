<template>
  <div class="entity-image-manager">
    <div class="image-container">
      <!-- Поточне зображення -->
      <div v-if="currentImageUrl" class="current-image">
        <img 
          :src="currentImageUrl" 
          :alt="imageAlt"
          class="image-preview"
          @click="openImageViewer"
        />
        <div class="image-overlay">
          <button 
            type="button"
            class="btn btn-sm btn-danger"
            @click="removeImage"
            :disabled="uploading"
          >
            <i class="fas fa-trash"></i>
          </button>
          <button 
            type="button"
            class="btn btn-sm btn-primary"
            @click="openImageViewer"
          >
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </div>

      <!-- Зона завантаження -->
      <div 
        v-else
        class="upload-zone"
        :class="{ 'drag-over': isDragOver }"
        @drop="handleDrop"
        @dragover.prevent="isDragOver = true"
        @dragleave="isDragOver = false"
        @click="triggerFileInput"
      >
        <div class="upload-content">
          <i class="fas fa-cloud-upload-alt upload-icon"></i>
          <p class="upload-text">
            Перетягніть зображення сюди або натисніть для вибору
          </p>
          <p class="upload-hint">
            {{ uploadHint }}
          </p>
        </div>
      </div>

      <!-- Прогрес завантаження -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress">
          <div 
            class="progress-bar" 
            :style="{ width: uploadProgress + '%' }"
          ></div>
        </div>
        <p class="progress-text">Завантаження: {{ uploadProgress }}%</p>
      </div>

      <!-- Помилки -->
      <div v-if="error" class="alert alert-danger mt-2">
        {{ error }}
      </div>

      <!-- Індикатор локальних змін -->
      <div v-if="hasChanges && isInLocalMode" class="local-changes-indicator">
        <div class="changes-badge">
          <i class="fas fa-clock"></i>
          <span v-if="pendingRemoval">Зображення буде видалено</span>
          <span v-else-if="pendingFile">Нове зображення буде завантажено</span>
          <span v-else>Є незбережені зміни</span>
        </div>
      </div>

      <!-- Попередній перегляд перед завантаженням -->
      <div v-if="previewImage && !currentImageUrl" class="preview-container">
        <div class="preview-header">
          <h6>Попередній перегляд</h6>
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary"
            @click="clearPreview"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="preview-image-wrapper">
          <img
            :src="previewImage"
            :alt="imageAlt + ' preview'"
            class="preview-image"
          />
        </div>
        <div class="preview-actions">
          <button
            type="button"
            class="btn btn-sm btn-primary"
            @click="confirmUpload"
            :disabled="uploading"
          >
            <i class="fas fa-check"></i>
            Підтвердити
          </button>
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            @click="clearPreview"
            :disabled="uploading"
          >
            <i class="fas fa-times"></i>
            Скасувати
          </button>
        </div>
      </div>
    </div>

    <!-- Прихований input для файлів -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- Модальне вікно для перегляду зображення -->
    <div 
      v-if="showImageViewer" 
      class="modal fade show d-block"
      @click="closeImageViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ imageAlt }}</h5>
            <button 
              type="button" 
              class="btn-close"
              @click="closeImageViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <img 
              :src="currentImageUrl" 
              :alt="imageAlt"
              class="img-fluid"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import imageService from '@/services/image.service';

export default {
  name: 'EntityImageManager',
  props: {
    entityType: {
      type: String,
      required: true,
      validator: (value) => ['user', 'product', 'category', 'company'].includes(value)
    },
    entityId: {
      type: String,
      default: null
    },
    currentImage: {
      type: String,
      default: null
    },
    imageId: {
      type: [String, Number],
      default: null
    },
    imageAlt: {
      type: String,
      default: 'Зображення'
    },
    maxSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    },
    allowedTypes: {
      type: Array,
      default: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    localMode: {
      type: Boolean,
      default: false // Якщо true, зображення зберігається локально до підтвердження
    },
    minWidth: {
      type: Number,
      default: 0
    },
    minHeight: {
      type: Number,
      default: 0
    },
    maxWidth: {
      type: Number,
      default: 4096
    },
    maxHeight: {
      type: Number,
      default: 4096
    },
    enableCompression: {
      type: Boolean,
      default: true
    },
    compressionQuality: {
      type: Number,
      default: 0.8
    }
  },
  emits: ['image-uploaded', 'image-removed', 'image-changed'],
  data() {
    return {
      isDragOver: false,
      uploading: false,
      uploadProgress: 0,
      error: null,
      showImageViewer: false,
      localImageUrl: null,
      pendingFile: null,
      previewImage: null,
      previewFile: null,
      pendingRemoval: false
    };
  },
  computed: {
    currentImageUrl() {
      // Якщо зображення позначено для видалення, не показуємо його
      if (this.pendingRemoval) {
        return null;
      }
      return this.localImageUrl || this.currentImage;
    },
    uploadHint() {
      const maxSizeMB = Math.round(this.maxSize / 1024 / 1024);
      const types = this.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ');
      let hint = `Максимальний розмір: ${maxSizeMB}MB. Формати: ${types}`;

      if (this.minWidth > 0 || this.minHeight > 0) {
        hint += `. Мінімальні розміри: ${this.minWidth}x${this.minHeight}px`;
      }

      if (this.maxWidth < 4096 || this.maxHeight < 4096) {
        hint += `. Максимальні розміри: ${this.maxWidth}x${this.maxHeight}px`;
      }

      if (this.enableCompression) {
        hint += '. Автоматичне стиснення увімкнено';
      }

      return hint;
    },
    hasChanges() {
      return this.pendingRemoval || !!this.pendingFile;
    },
    isInLocalMode() {
      return this.localMode || !this.entityId;
    }
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.processFile(file);
      }
    },
    
    handleDrop(event) {
      event.preventDefault();
      this.isDragOver = false;
      
      const files = event.dataTransfer.files;
      if (files.length > 0) {
        this.processFile(files[0]);
      }
    },
    
    async processFile(file) {
      this.error = null;

      // Базова валідація файлу
      const basicValidation = this.validateBasicFile(file);
      if (!basicValidation.isValid) {
        this.error = basicValidation.errors.join(', ');
        return;
      }

      // Валідація розмірів зображення
      try {
        const dimensionValidation = await this.validateImageDimensions(file);
        if (!dimensionValidation.isValid) {
          this.error = dimensionValidation.errors.join(', ');
          return;
        }
      } catch (error) {
        this.error = 'Помилка перевірки зображення';
        return;
      }

      // Стиснення зображення якщо потрібно
      let processedFile = file;
      if (this.enableCompression && this.shouldCompressImage(file)) {
        try {
          processedFile = await this.compressImage(file);
        } catch (error) {
          console.warn('Compression failed, using original file:', error);
        }
      }

      // Перевіряємо режим роботи
      if (this.isInLocalMode) {
        // Локальний режим - зберігаємо файл для подальшого завантаження
        await this.handleLocalMode(processedFile);
      } else {
        // Негайне завантаження (для зворотної сумісності)
        await this.uploadFile(processedFile);
      }
    },
    
    async handleLocalMode(file) {
      try {
        // Створюємо превью для попереднього перегляду
        this.previewImage = URL.createObjectURL(file);
        this.previewFile = file;
        this.pendingFile = file; // Зберігаємо файл для processPendingOperations
        this.pendingRemoval = false; // Скидаємо прапор видалення

        console.log('EntityImageManager local mode - file processed:', {
          fileName: file.name,
          fileSize: file.size,
          entityType: this.entityType,
          entityId: this.entityId
        });

        // Емітимо подію про зміну
        this.$emit('image-changed', {
          type: 'upload',
          file,
          previewUrl: this.previewImage,
          isLocal: true
        });
      } catch (error) {
        console.error('Error in handleLocalMode:', error);
        this.error = 'Помилка створення превью зображення';
      }
    },
    
    async uploadFile(file) {
      if (!this.entityId) {
        this.error = 'ID сутності не вказано';
        return;
      }
      
      this.uploading = true;
      this.uploadProgress = 0;
      
      try {
        const response = await imageService.uploadImage(
          this.entityType,
          this.entityId,
          file,
          (progress) => {
            this.uploadProgress = progress;
          }
        );
        
        this.localImageUrl = null;
        this.pendingFile = null;
        
        this.$emit('image-uploaded', response.data);
        
      } catch (error) {
        this.error = error.message;
      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
      }
    },
    
    async removeImage() {
      console.log('EntityImageManager.removeImage called', {
        isInLocalMode: this.isInLocalMode,
        entityId: this.entityId,
        currentImage: this.currentImage
      });

      if (this.isInLocalMode) {
        // Локальний режим - позначаємо для відкладеного видалення
        this.localImageUrl = null;
        this.pendingFile = null;
        this.pendingRemoval = true;

        console.log('Local mode: marking for deferred removal');
        this.$emit('image-changed', {
          type: 'removal',
          originalImage: this.currentImage
        });
      } else {
        // Негайне видалення (для зворотної сумісності)
        try {
          await this.deleteCurrentImage();
        } catch (error) {
          this.error = 'Помилка видалення зображення: ' + error.message;
        }
      }
    },
    
    openImageViewer() {
      if (this.currentImageUrl) {
        this.showImageViewer = true;
      }
    },
    
    closeImageViewer() {
      this.showImageViewer = false;
    },

    // Валідація базових параметрів файлу
    validateBasicFile(file) {
      const errors = [];

      // Перевірка типу файлу
      if (!this.allowedTypes.includes(file.type)) {
        errors.push(`Непідтримуваний тип файлу. Дозволені: ${this.allowedTypes.join(', ')}`);
      }

      // Перевірка розміру файлу
      if (file.size > this.maxSize) {
        const maxSizeMB = (this.maxSize / (1024 * 1024)).toFixed(1);
        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
        errors.push(`Розмір файлу ${fileSizeMB}MB перевищує максимальний ${maxSizeMB}MB`);
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    },

    // Валідація розмірів зображення
    validateImageDimensions(file) {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          const errors = [];

          if (img.width < this.minWidth) {
            errors.push(`Ширина зображення ${img.width}px менша за мінімальну ${this.minWidth}px`);
          }
          if (img.height < this.minHeight) {
            errors.push(`Висота зображення ${img.height}px менша за мінімальну ${this.minHeight}px`);
          }
          if (img.width > this.maxWidth) {
            errors.push(`Ширина зображення ${img.width}px перевищує максимальну ${this.maxWidth}px`);
          }
          if (img.height > this.maxHeight) {
            errors.push(`Висота зображення ${img.height}px перевищує максимальну ${this.maxHeight}px`);
          }

          resolve({
            isValid: errors.length === 0,
            errors,
            dimensions: { width: img.width, height: img.height }
          });
        };
        img.onerror = () => {
          resolve({
            isValid: false,
            errors: ['Неможливо завантажити зображення']
          });
        };
        img.src = URL.createObjectURL(file);
      });
    },

    // Перевірка чи потрібно стискати зображення
    shouldCompressImage(file) {
      const maxUncompressedSize = 1024 * 1024; // 1MB
      return file.size > maxUncompressedSize || file.type === 'image/png';
    },

    // Стиснення зображення
    compressImage(file) {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
          // Розрахунок нових розмірів
          let { width, height } = img;
          const maxDimension = Math.max(this.maxWidth, this.maxHeight);

          if (width > maxDimension || height > maxDimension) {
            const ratio = Math.min(maxDimension / width, maxDimension / height);
            width *= ratio;
            height *= ratio;
          }

          canvas.width = width;
          canvas.height = height;

          // Малювання та стиснення
          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob((blob) => {
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            });
            resolve(compressedFile);
          }, 'image/jpeg', this.compressionQuality);
        };

        img.src = URL.createObjectURL(file);
      });
    },

    // Методи для попереднього перегляду
    clearPreview() {
      if (this.previewImage) {
        URL.revokeObjectURL(this.previewImage);
      }
      this.previewImage = null;
      this.previewFile = null;
      this.error = null;
    },

    async confirmUpload() {
      if (!this.previewFile) return;

      try {
        // Створюємо локальне превью для форми
        this.localImageUrl = this.previewImage;
        this.pendingFile = this.previewFile;
        this.pendingRemoval = false; // Скидаємо прапор видалення

        console.log('Confirming upload in local mode:', {
          fileName: this.previewFile.name,
          fileSize: this.previewFile.size,
          hasPreviewUrl: !!this.previewImage
        });

        this.$emit('image-changed', {
          type: 'upload',
          file: this.previewFile,
          previewUrl: this.previewImage,
          isLocal: true
        });

        // Очищуємо попередній перегляд
        this.previewImage = null;
        this.previewFile = null;
      } catch (error) {
        this.error = 'Помилка підтвердження зображення';
        console.error('Error confirming upload:', error);
      }
    },

    // Публічний метод для завантаження файлу (використовується в localMode)
    async uploadPendingFile() {
      if (this.pendingFile && this.entityId) {
        console.log('Uploading pending file:', this.pendingFile.name);
        await this.uploadFile(this.pendingFile);
      }
    },

    // Публічний метод для скидання локальних змін
    resetLocalChanges() {
      console.log('Resetting local changes');

      // Очищуємо URL об'єкти для запобігання витоків пам'яті
      if (this.localImageUrl && this.localImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.localImageUrl);
      }
      if (this.previewImage) {
        URL.revokeObjectURL(this.previewImage);
      }

      this.localImageUrl = null;
      this.pendingFile = null;
      this.pendingRemoval = false;
      this.previewImage = null;
      this.previewFile = null;
      this.error = null;
    },

    // Публічний метод для отримання стану змін
    getChangeState() {
      return {
        hasChanges: this.hasChanges,
        pendingRemoval: this.pendingRemoval,
        hasPendingFile: !!this.pendingFile,
        pendingFileName: this.pendingFile?.name || null
      };
    },

    // Публічний метод для обробки відкладених операцій при збереженні
    async processPendingOperations() {
      console.log('EntityImageManager.processPendingOperations called:', {
        pendingRemoval: this.pendingRemoval,
        hasPendingFile: !!this.pendingFile,
        hasCurrentImage: !!this.currentImage,
        entityId: this.entityId,
        entityType: this.entityType
      });

      const results = {
        uploaded: false,
        removed: false,
        errors: []
      };

      try {
        // Спочатку обробляємо видалення (якщо є поточне зображення)
        if (this.pendingRemoval && this.currentImage) {
          console.log('Processing pending removal');
          try {
            await this.deleteCurrentImage();
            results.removed = true;
            console.log('Image removed successfully');
          } catch (error) {
            console.error('Error removing image:', error);
            results.errors.push(`Помилка видалення зображення: ${error.message}`);
          }
        }

        // Потім обробляємо завантаження нового файлу
        if (this.pendingFile && this.entityId) {
          console.log('Processing pending file upload');
          try {
            await this.uploadFile(this.pendingFile);
            results.uploaded = true;
            console.log('File uploaded successfully');
          } catch (error) {
            console.error('Error uploading file:', error);
            results.errors.push(`Помилка завантаження файлу: ${error.message}`);
          }
        }

        // Очищуємо відкладені операції тільки якщо не було помилок
        if (results.errors.length === 0) {
          console.log('Clearing pending operations');
          this.resetLocalChanges();
        }

        return results;
      } catch (error) {
        console.error('Unexpected error in processPendingOperations:', error);
        results.errors.push(`Неочікувана помилка: ${error.message}`);
        return results;
      }
    },

    // Приватний метод для видалення поточного зображення через API
    async deleteCurrentImage() {
      console.log('EntityImageManager.deleteCurrentImage called:', {
        entityId: this.entityId,
        entityType: this.entityType,
        currentImage: this.currentImage
      });

      if (!this.entityId || !this.currentImage) {
        console.warn('Cannot delete image: missing entityId or currentImage');
        return;
      }

      try {
        // Використовуємо універсальний API для всіх типів сутностей
        const imageType = this.getImageTypeForEntity();
        console.log('Deleting entity image:', {
          entityType: this.entityType,
          entityId: this.entityId,
          imageType
        });

        await imageService.deleteEntityImage(this.entityType, this.entityId, imageType);
        console.log('Image deleted successfully via API');

      } catch (error) {
        console.error('Error deleting image via API:', error);
        // Якщо зображення вже не існує (404), це не помилка
        if (error.status === 404 || error.response?.status === 404) {
          console.log('Image already deleted, continuing...');
          return;
        }
        throw error;
      }
    },

    // Допоміжний метод для визначення типу зображення для сутності
    getImageTypeForEntity() {
      switch (this.entityType) {
        case 'user':
          return 'main'; // Для user аватар обробляється як main image
        case 'product':
          return 'main';
        case 'category':
          return 'main';
        case 'company':
          return 'main';
        default:
          return 'main';
      }
    }
  }
};
</script>

<style scoped>
.entity-image-manager {
  width: 100%;
  max-width: 300px;
}

.image-container {
  position: relative;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.current-image {
  position: relative;
  width: 100%;
  height: 200px;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.current-image:hover .image-overlay {
  opacity: 1;
}

.upload-zone {
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.upload-icon {
  font-size: 3rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

.upload-text {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  padding: 1rem;
}

.progress {
  height: 8px;
  margin-bottom: 0.5rem;
}

.progress-text {
  margin: 0;
  font-size: 0.875rem;
  text-align: center;
}

.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Preview styles */
.preview-container {
  margin-top: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.preview-header h6 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
}

.preview-image-wrapper {
  padding: 1rem;
  text-align: center;
  background: #f8f9fa;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border-top: 1px solid #dee2e6;
}

/* Local changes indicator */
.local-changes-indicator {
  margin-top: 0.5rem;
}

.changes-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #856404;
}

.changes-badge i {
  font-size: 0.75rem;
}
</style>
