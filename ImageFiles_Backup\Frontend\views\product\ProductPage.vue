<template>
  <div class="product-page">
    <!-- Breadcrumbs -->
    <div class="breadcrumbs">
      <span class="fas fa-house"></span> 
      <span class="separator">/</span> 
      <span>Каталог</span>
      <span class="separator">/</span> 
      <span v-if="product.category">{{ product.category.name }} 
      <span class="separator">/</span>  
      </span> 
      <span v-if="product.name">{{ product.name }}</span>
      <span v-else>Завантаження...</span>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="loader"></div>
      <p>Завантаження товару...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <h2>Помилка завантаження</h2>
      <p>{{ error }}</p>
      <button @click="fetchProduct" class="retry-btn">Спробувати знову</button>
    </div>

    <!-- Product Content -->
    <div v-else-if="product" class="product-content">
      <div class="product-main">
        <!-- Product Images -->
        <div class="product-images">
          <div class="main-image">
            <img
              :src="selectedImage || product.mainImage || placeholderImage"
              :alt="product.name"
              @error="handleImageError"
            />
            <div class="image-navigation">
              <button 
                @click="previousImage" 
                :disabled="currentImageIndex === 0"
                class="nav-btn fas fa-arrow-left"
              >
                &#8249;
              </button>
              <button 
                @click="nextImage" 
                :disabled="currentImageIndex === productImages.length - 1"
                class="nav-btn fas fa-arrow-right"
              >
                &#8250;
              </button>
            </div>
          </div>
          
          <div class="thumbnail-images">
            <img 
              v-for="(image, index) in productImages" 
              :key="index"
              :src="image"
              :alt="`${product.name} ${index + 1}`"
              :class="{ active: selectedImage === image }"
              @click="selectImage(image, index)"
              @error="handleImageError"
            />
          </div>
        </div>

        <!-- Product Info -->
        <div class="product-info">
          <h1 class="product-title">{{ product.name }}</h1>
          
          <div class="seller-info">
            <span class="seller-label">Продавець:</span>
            <router-link :to="`/seller/${product.company?.slug}`" class="seller-link">
              {{ product.company?.name }}
            </router-link>
          </div>

          <!-- Product Options -->
          <div class="product-options">
            <!-- Dynamic Attribute Selection -->
            <div v-for="(options, attributeName) in selectableAttributes" :key="attributeName" class="option-group">
              <label class="option-label">{{ attributeName }}:</label>
              <div class="attribute-options">
                <button
                  v-for="option in options"
                  :key="option"
                  :class="['attribute-option', { active: selectedAttributes[attributeName] === option }]"
                  @click="selectAttribute(attributeName, option)"
                >
                  {{ option }}
                </button>
              </div>
            </div>
          </div>

          <!-- Price and Actions -->
          <div class="product-actions">
            <div class="price-section">
              <span class="price">{{ formatPrice(product) }}</span>
            </div>
            
            <div class="action-buttons">
              <button 
                @click="addToCart" 
                :disabled="!product.inStock"
                class="buy-btn"
              >
                {{ product.inStock ? 'Купити' : 'Немає в наявності' }}
              </button>
              
              <button 
                @click="toggleFavorite" 
                :class="['favorite-btn', { active: isFavorite }]"
                :title="isFavorite ? 'Видалити з обраного' : 'Додати в обране'"
              >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
              </button>
            </div>
          </div>

          <!-- Product Details in Right Column -->
          <div class="product-details-right">
            <!-- Characteristics Section -->
            <div class="details-section">
              <h2>Характеристики</h2>
              <div class="characteristics">
                <div v-for="(value, key) in product.attributes" :key="key" class="characteristic-row">
                  <span class="characteristic-label">{{ key }}</span>
                  <span class="characteristic-value">
                    <template v-if="typeof value === 'string' && value.includes(',')">
                      <span v-for="(item, index) in value.split(',').map(v => v.trim())" :key="index" class="characteristic-value-item">
                        {{ item }}<br v-if="index < value.split(',').length - 1">
                      </span>
                    </template>
                    <template v-else>{{ value }}</template>
                  </span>
                </div>
              </div>
            </div>

            <!-- Reviews Section -->
            <div class="reviews-section">
              <h2>Відгуки</h2>
              <div v-if="reviews.length === 0" class="no-reviews">
                <p>Поки що немає відгуків про цей товар</p>
              </div>
              <div v-else class="reviews-list">
                <div v-for="review in reviews" :key="review.id" class="review-item">
                  <div class="review-header">
                    <span class="reviewer-name">{{ review.user.name }}</span>
                    <span class="review-date">{{ formatDate(review.createdAt) }}</span>
                  </div>
                  <div class="review-rating">
                    <span v-for="i in 5" :key="i" :class="['star', { filled: i <= review.rating }]">★</span>
                  </div>
                  <p class="review-text">{{ review.comment }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <section class="recommended-section" v-if="product.category && product.category.slug">
        <div class="container">
          <h2 class="section-title">Випадкові товари з цієї категорії</h2>
          <ProductGrid
            :fetch-params="{ categorySlug: product.category.slug, status: 1, count: 4, random: true }"
            :grid-columns="4"
            empty-message="Рандомні товари поки що недоступні"
          />
        </div>
      </section>


      <section class="recommended-section">
        <div class="container">
          <h2 class="section-title">Разом з цим товаром дивляться ще</h2>
          <ProductGrid
            :fetch-params="{ type: 'recommended', status: 1, pageSize: 8 }"
            :grid-columns="4"
            empty-message="Рекомендовані товари поки що недоступні"
          />
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import productService from '@/services/product.service';
import reviewService from '@/services/review.service'
import categoryService from '@/services/category.service';
import ProductGrid from '@/components/common/ProductGrid.vue';

export default {
  name: 'ProductPage',
  components: {
    ProductGrid
  },
  data() {
    return {
      product: {},
      reviews: [],
      loading: true,
      error: null,
      selectedImage: '',
      currentImageIndex: 0,
      selectedAttributes: {},
      isFavorite: false,
      placeholderImage: '/placeholder-product.svg'
    }
  },
  computed: {
    productImages() {
      if (!this.product.images || this.product.images.length === 0) {
        return [this.product.mainImage || this.placeholderImage]
      }
      return this.product.images.map(img => img.url || img)
    },
    selectableAttributes() {
      if (!this.product.attributes) return {}

      const selectable = {}

      // Проходимо по всіх атрибутах товару
      // Приклад структури атрибутів:
      // {
      //   "Колір": "червоний, зелений, синій",
      //   "Пам'ять": "64ГБ, 128ГБ, 256ГБ",
      //   "Діагональ": "6.1 дюйм",  // не буде показано як вибір
      //   "Бренд": "Apple"         // не буде показано як вибір
      // }
      Object.entries(this.product.attributes).forEach(([key, value]) => {
        if (typeof value === 'string' && value.includes(',')) {
          // Якщо значення містить кому, розділяємо на варіанти
          const options = value.split(',').map(option => option.trim()).filter(option => option)
          if (options.length > 1) {
            selectable[key] = options
          }
        } else if (Array.isArray(value) && value.length > 1) {
          // Якщо значення - це масив з кількома елементами
          selectable[key] = value
        }
      })

      return selectable
    }
  },
  async mounted() {
    await this.fetchProduct()
  },
  watch: {
    '$route.params.slug'() {
      if (this.$route.params.slug) {
        this.fetchProduct()
      }
    }
  },
  methods: {
    async fetchProduct() {
      try {
        this.loading = true
        this.error = null

        const slug = this.$route.params.slug
        const response = await productService.getBySlug(slug)

        console.log(response);

        this.product = response.data
        this.selectedImage = this.productImages[0]

        if(!this.product.category && this.product.categoryId) {
          this.fetchCategory(this.product.categoryId)
        }

        // Set default selections for selectable attributes
        this.setDefaultAttributeSelections()

        // Fetch reviews
        await this.fetchReviews(slug)

      } catch (err) {
        this.error = 'Не вдалося завантажити товар'
        console.error('Error fetching product:', err)
      } finally {
        this.loading = false
      }
    },
    async fetchReviews(productId) {
      try {
        const response = await reviewService.getByProductId(productId)
        this.reviews = response.data.data || []
      } catch (err) {
        console.error('Error fetching reviews:', err)
      }
    },
    async fetchCategory(categoryId) {
      try {
        const response = await categoryService.getById(categoryId);
        this.product.category = response.data;


        console.log('Fetching category:', categoryId)
      } catch (err) {
        console.error('Error fetching category:', err)
      }
    },
    setDefaultAttributeSelections() {
      // Встановлюємо перший варіант як вибраний за замовчуванням для кожного атрибута
      Object.entries(this.selectableAttributes).forEach(([attributeName, options]) => {
        if (options.length > 0) {
          this.selectedAttributes[attributeName] = options[0]
        }
      })
    },
    selectAttribute(attributeName, value) {
      this.selectedAttributes[attributeName] = value
    },
    selectImage(image, index) {
      this.selectedImage = image
      this.currentImageIndex = index
    },
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
        this.selectedImage = this.productImages[this.currentImageIndex]
      }
    },
    nextImage() {
      if (this.currentImageIndex < this.productImages.length - 1) {
        this.currentImageIndex++
        this.selectedImage = this.productImages[this.currentImageIndex]
      }
    },
    handleImageError(event) {
      event.target.src = this.placeholderImage
    },
    formatPrice(product) {
      if (!product.priceAmount) return 'Ціна не вказана'
      if (product.priceAmount == 0) return 'Ціна не вказана'
      product.priceAmount = Math.round(product.priceAmount);
      if (!product.priceCurrency) return `${product.priceAmount} ₴`
      else if(product.priceCurrency == "UAH") return `${product.priceAmount} ₴`
      else if(product.priceCurrency == "USD") return `${product.priceAmount} $`
      else if(product.priceCurrency == "EUR") return `${product.priceAmount} €`
      return `${product.priceAmount} ${product.priceCurrency}`
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('uk-UA')
    },
    addToCart() {
      // TODO: Implement add to cart functionality
      console.log('Adding to cart:', {
        product: this.product,
        selectedAttributes: this.selectedAttributes
      })
    },
    toggleFavorite() {
      // TODO: Implement favorite functionality
      this.isFavorite = !this.isFavorite
    }
  }
}
</script>

<style scoped>
.recommended-section {
  margin-bottom: 48px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  text-align: left;
}

.product-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Breadcrumbs */
.breadcrumbs {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.breadcrumbs a {
  color: #007bff;
  text-decoration: none;
}

.breadcrumbs a:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
  color: #ccc;
}

.current {
  color: #333;
  font-weight: 500;
}

/* Loading and Error States */
.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #0056b3;
}

/* Product Content */
.product-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

/* Product Images */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-image {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  transform: translateY(-50%);
}

.nav-btn {
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.nav-btn:hover:not(:disabled) {
  transform: scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.thumbnail-images {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
}

.thumbnail-images img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.thumbnail-images img:hover {
  border-color: #007bff;
}

.thumbnail-images img.active {
  border-color: #007bff;
  box-shadow: 0 0 0 1px #007bff;
}

/* Product Info */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Product Details in Right Column */
.product-details-right {
  margin-top: 30px;
  padding-top: 30px;
  /* border-top: 1px solid #e9ecef; */
}

.product-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.seller-info {
  font-size: 14px;
  color: #666;
}

.seller-label {
  margin-right: 8px;
}

.seller-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.seller-link:hover {
  text-decoration: underline;
}

/* Product Options */
.product-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.attribute-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.attribute-option {
  padding: 8px 16px;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  min-width: 60px;
  text-align: center;
}

.attribute-option:hover {
  border-color: #007bff;
}

.attribute-option.active {
  border-color: #007bff;
  background-color: #007bff;
  color: white;
}

/* Product Actions */
.product-actions {
  margin-top: auto;
  padding-top: 20px;
  /* border-top: 1px solid #e9ecef; */
}

.price-section {
  margin-bottom: 20px;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.buy-btn {
  flex: 1;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.buy-btn:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.buy-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.favorite-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 8px;
  background-color: #f8f9fa;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn:hover {
  border-color: #dc3545;
  color: #dc3545;
}

.favorite-btn.active {
  border-color: #dc3545;
  background-color: #dc3545;
  color: white;
}

/* Product Details - moved to right column */

.details-section h2, .reviews-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

/* Characteristics */
.characteristics {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 50px;
}

.characteristic-row {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  /* border-bottom: 1px solid #f1f3f4; */
}

.characteristic-label {
  font-weight: 500;
  color: #666;
  flex: 1;
}

.characteristic-value {
  color: #2F7CDF;
  text-align: right;
  flex: 1;
  white-space: pre-line;
}

.characteristic-value-item {
  color: #2F7CDF;
}

/* Reviews */
.no-reviews {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-item {
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reviewer-name {
  font-weight: 600;
  color: #333;
}

.review-date {
  font-size: 12px;
  color: #666;
}

.review-rating {
  margin-bottom: 12px;
}

.star {
  color: #ddd;
  font-size: 16px;
}

.star.filled {
  color: #ffc107;
}

.review-text {
  color: #555;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-details-right {
    margin-top: 20px;
    padding-top: 20px;
  }

  .product-title {
    font-size: 20px;
  }

  .price {
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .favorite-btn {
    width: 100%;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .product-page {
    padding: 15px;
  }

  .thumbnail-images img {
    width: 60px;
    height: 60px;
  }

  .characteristic-row {
    flex-direction: column;
    gap: 4px;
  }

  .characteristic-value {
    text-align: left;
  }
}
</style>
