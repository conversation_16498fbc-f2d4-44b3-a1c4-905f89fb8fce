<template>
  <div class="products-report">
    <!-- Product Metrics Grid -->
    <div class="metrics-section">
      <h3 class="section-title">
        <i class="fas fa-box"></i>
        Product Performance
      </h3>
      
      <div class="metrics-grid">
        <div 
          v-for="metric in productMetrics" 
          :key="metric.key"
          class="metric-card"
          :class="metric.trend"
        >
          <div class="metric-header">
            <div class="metric-icon">
              <i :class="metric.icon"></i>
            </div>
            <div class="metric-change" v-if="metric.changePercentage">
              <i :class="getChangeIcon(metric.changePercentage)"></i>
              <span>{{ Math.abs(metric.changePercentage).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-comparison" v-if="metric.previousValue">
              vs {{ formatMetricValue(metric.previousValue, metric.type) }} last period
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Performance Charts -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- Views vs Sales Chart -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Product Views vs Sales</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="performanceChartCanvas" id="performance-chart"></canvas>
          </div>
        </div>

        <!-- Category Distribution -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Products by Category</h3>
          </div>
          
          <div class="chart-content">
            <canvas ref="categoryChartCanvas" id="category-chart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Performing Products -->
    <div class="top-products-section">
      <h3 class="section-title">
        <i class="fas fa-star"></i>
        Top Performing Products
      </h3>
      
      <div class="performance-tabs">
        <button 
          v-for="tab in performanceTabs" 
          :key="tab.key"
          @click="activeTab = tab.key"
          class="tab-button"
          :class="{ active: activeTab === tab.key }"
        >
          <i :class="tab.icon"></i>
          {{ tab.label }}
        </button>
      </div>
      
      <div class="products-grid">
        <div 
          v-for="(product, index) in topProductsByTab" 
          :key="product.id"
          class="product-card"
        >
          <div class="product-rank">
            <span class="rank-number">#{{ index + 1 }}</span>
            <div class="rank-badge" :class="`rank-${index + 1}`">
              <i v-if="index === 0" class="fas fa-crown"></i>
              <i v-else-if="index === 1" class="fas fa-medal"></i>
              <i v-else-if="index === 2" class="fas fa-award"></i>
              <i v-else class="fas fa-star"></i>
            </div>
          </div>
          
          <div class="product-image">
            <img :src="product.image || '/placeholder-product.jpg'" :alt="product.name" />
          </div>
          
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-category">{{ product.category }}</div>
            <div class="product-sku">SKU: {{ product.sku }}</div>
          </div>
          
          <div class="product-stats">
            <div class="stat-item">
              <span class="stat-label">{{ getStatLabel(activeTab) }}</span>
              <span class="stat-value">{{ formatStatValue(product[activeTab], activeTab) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Rating</span>
              <span class="stat-value rating">
                <i class="fas fa-star"></i>
                {{ product.rating?.toFixed(1) || 'N/A' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Analytics -->
    <div class="analytics-section">
      <h3 class="section-title">
        <i class="fas fa-chart-bar"></i>
        Product Analytics
      </h3>
      
      <div class="analytics-grid">
        <!-- Conversion Funnel -->
        <div class="analytics-card">
          <div class="analytics-header">
            <h4>Conversion Funnel</h4>
            <i class="fas fa-funnel-dollar"></i>
          </div>
          <div class="funnel-steps">
            <div 
              v-for="step in conversionFunnel" 
              :key="step.name"
              class="funnel-step"
            >
              <div class="step-bar">
                <div 
                  class="step-fill" 
                  :style="{ width: step.percentage + '%' }"
                ></div>
              </div>
              <div class="step-info">
                <span class="step-name">{{ step.name }}</span>
                <span class="step-value">{{ formatNumber(step.value) }}</span>
                <span class="step-percentage">{{ step.percentage.toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Price Analysis -->
        <div class="analytics-card">
          <div class="analytics-header">
            <h4>Price Analysis</h4>
            <i class="fas fa-tags"></i>
          </div>
          <div class="price-ranges">
            <div 
              v-for="range in priceRanges" 
              :key="range.range"
              class="price-range"
            >
              <div class="range-label">{{ range.range }}</div>
              <div class="range-bar">
                <div 
                  class="range-fill" 
                  :style="{ width: range.percentage + '%' }"
                ></div>
              </div>
              <div class="range-stats">
                <span class="range-count">{{ range.count }} products</span>
                <span class="range-percentage">{{ range.percentage.toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Stock Status -->
        <div class="analytics-card">
          <div class="analytics-header">
            <h4>Stock Status</h4>
            <i class="fas fa-warehouse"></i>
          </div>
          <div class="stock-overview">
            <div 
              v-for="status in stockStatus" 
              :key="status.status"
              class="stock-item"
              :class="status.status.toLowerCase()"
            >
              <div class="stock-icon">
                <i :class="status.icon"></i>
              </div>
              <div class="stock-info">
                <div class="stock-label">{{ status.status }}</div>
                <div class="stock-count">{{ status.count }} products</div>
                <div class="stock-percentage">{{ status.percentage.toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Insights -->
    <div class="insights-section">
      <h3 class="section-title">
        <i class="fas fa-lightbulb"></i>
        Product Insights
      </h3>
      
      <div class="insights-grid">
        <div 
          v-for="insight in productInsights" 
          :key="insight.id"
          class="insight-card"
          :class="insight.type"
        >
          <div class="insight-icon">
            <i :class="insight.icon"></i>
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
            <div class="insight-action" v-if="insight.action">
              <button class="action-btn" @click="handleInsightAction(insight)">
                {{ insight.action }}
              </button>
            </div>
          </div>
          <div class="insight-priority">
            <div class="priority-indicator" :class="`priority-${insight.priority}`">
              {{ insight.priority }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Products Table -->
    <div class="products-table-section">
      <h3 class="section-title">
        <i class="fas fa-table"></i>
        Product Performance Details
      </h3>
      
      <div class="table-controls">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            v-model="productSearch" 
            type="text" 
            placeholder="Search products..."
            class="search-input"
          />
        </div>
        
        <div class="filter-controls">
          <select v-model="categoryFilter" class="filter-select">
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category" :value="category">
              {{ category }}
            </option>
          </select>
          
          <select v-model="statusFilter" class="filter-select">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="out_of_stock">Out of Stock</option>
          </select>
        </div>
      </div>
      
      <div class="table-container">
        <table class="products-table">
          <thead>
            <tr>
              <th @click="sortBy('name')" class="sortable">
                Product
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('category')" class="sortable">
                Category
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('views')" class="sortable">
                Views
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('sales')" class="sortable">
                Sales
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('revenue')" class="sortable">
                Revenue
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('rating')" class="sortable">
                Rating
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('stock')" class="sortable">
                Stock
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="product in paginatedProducts" :key="product.id" class="product-row">
              <td>
                <div class="product-cell">
                  <img :src="product.image || '/placeholder-product.jpg'" :alt="product.name" class="product-thumbnail" />
                  <div class="product-details">
                    <div class="product-name">{{ product.name }}</div>
                    <div class="product-sku">{{ product.sku }}</div>
                  </div>
                </div>
              </td>
              <td>
                <span class="category-badge">{{ product.category }}</span>
              </td>
              <td>
                <span class="views-count">{{ formatNumber(product.views) }}</span>
              </td>
              <td>
                <span class="sales-count">{{ formatNumber(product.sales) }}</span>
              </td>
              <td>
                <span class="revenue-amount">{{ formatCurrency(product.revenue) }}</span>
              </td>
              <td>
                <div class="rating-display">
                  <i class="fas fa-star"></i>
                  <span>{{ product.rating?.toFixed(1) || 'N/A' }}</span>
                </div>
              </td>
              <td>
                <span class="stock-indicator" :class="getStockClass(product.stock)">
                  {{ product.stock }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button @click="viewProduct(product)" class="action-btn view-btn">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button @click="editProduct(product)" class="action-btn edit-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="currentPage = 1" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button 
          @click="currentPage--" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-left"></i>
        </button>
        
        <span class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        
        <button 
          @click="currentPage++" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button 
          @click="currentPage = totalPages" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'ProductsReport',
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // Refs
    const performanceChartCanvas = ref(null)
    const categoryChartCanvas = ref(null)
    const chartPeriod = ref('daily')
    const activeTab = ref('sales')
    const productSearch = ref('')
    const categoryFilter = ref('')
    const statusFilter = ref('')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const sortField = ref('sales')
    const sortDirection = ref('desc')

    // Chart instances
    let performanceChart = null
    let categoryChart = null

    // Performance tabs
    const performanceTabs = [
      { key: 'sales', label: 'Sales', icon: 'fas fa-shopping-cart' },
      { key: 'views', label: 'Views', icon: 'fas fa-eye' },
      { key: 'revenue', label: 'Revenue', icon: 'fas fa-dollar-sign' },
      { key: 'rating', label: 'Rating', icon: 'fas fa-star' }
    ]

    // Computed properties
    const productMetrics = computed(() => {
      return props.data?.metrics?.items || []
    })

    const productInsights = computed(() => {
      return props.data?.insights || []
    })

    const productsData = computed(() => {
      return props.data?.table?.data || []
    })

    const categories = computed(() => {
      const uniqueCategories = [...new Set(productsData.value.map(item => item.category).filter(Boolean))]
      return uniqueCategories.sort()
    })

    const topProductsByTab = computed(() => {
      return productsData.value
        .sort((a, b) => (b[activeTab.value] || 0) - (a[activeTab.value] || 0))
        .slice(0, 6)
    })

    const conversionFunnel = computed(() => {
      const totalViews = productsData.value.reduce((sum, p) => sum + (p.views || 0), 0)
      const totalAddToCart = Math.floor(totalViews * 0.15) // 15% conversion to cart
      const totalPurchases = productsData.value.reduce((sum, p) => sum + (p.sales || 0), 0)

      const steps = [
        { name: 'Product Views', value: totalViews, percentage: 100 },
        { name: 'Add to Cart', value: totalAddToCart, percentage: totalViews > 0 ? (totalAddToCart / totalViews) * 100 : 0 },
        { name: 'Purchases', value: totalPurchases, percentage: totalViews > 0 ? (totalPurchases / totalViews) * 100 : 0 }
      ]

      return steps
    })

    const priceRanges = computed(() => {
      const ranges = [
        { range: '< ₴500', min: 0, max: 500 },
        { range: '₴500 - ₴1,000', min: 500, max: 1000 },
        { range: '₴1,000 - ₴2,500', min: 1000, max: 2500 },
        { range: '₴2,500 - ₴5,000', min: 2500, max: 5000 },
        { range: '> ₴5,000', min: 5000, max: Infinity }
      ]

      const total = productsData.value.length

      return ranges.map(range => {
        const count = productsData.value.filter(p => {
          const price = p.price || 0
          return price >= range.min && price < range.max
        }).length

        return {
          ...range,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const stockStatus = computed(() => {
      const statuses = [
        { status: 'In Stock', icon: 'fas fa-check-circle', filter: (stock) => stock > 10 },
        { status: 'Low Stock', icon: 'fas fa-exclamation-triangle', filter: (stock) => stock > 0 && stock <= 10 },
        { status: 'Out of Stock', icon: 'fas fa-times-circle', filter: (stock) => stock === 0 }
      ]

      const total = productsData.value.length

      return statuses.map(status => {
        const count = productsData.value.filter(p => status.filter(p.stock || 0)).length

        return {
          ...status,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const filteredProducts = computed(() => {
      let filtered = productsData.value

      // Apply search filter
      if (productSearch.value) {
        const query = productSearch.value.toLowerCase()
        filtered = filtered.filter(product =>
          product.name?.toLowerCase().includes(query) ||
          product.sku?.toLowerCase().includes(query) ||
          product.category?.toLowerCase().includes(query)
        )
      }

      // Apply category filter
      if (categoryFilter.value) {
        filtered = filtered.filter(product => product.category === categoryFilter.value)
      }

      // Apply status filter
      if (statusFilter.value) {
        filtered = filtered.filter(product => {
          switch (statusFilter.value) {
            case 'active':
              return product.status === 'active'
            case 'inactive':
              return product.status === 'inactive'
            case 'out_of_stock':
              return (product.stock || 0) === 0
            default:
              return true
          }
        })
      }

      // Apply sorting
      filtered.sort((a, b) => {
        const aVal = a[sortField.value]
        const bVal = b[sortField.value]

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return sortDirection.value === 'asc' ? aVal - bVal : bVal - aVal
        }

        const aStr = String(aVal).toLowerCase()
        const bStr = String(bVal).toLowerCase()

        if (sortDirection.value === 'asc') {
          return aStr.localeCompare(bStr)
        } else {
          return bStr.localeCompare(aStr)
        }
      })

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredProducts.value.length / itemsPerPage.value)
    })

    const paginatedProducts = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage.value
      const end = start + itemsPerPage.value
      return filteredProducts.value.slice(start, end)
    })

    // Methods
    const formatMetricValue = (value, type) => {
      switch (type) {
        case 'currency':
          return reportsService.formatCurrency(value)
        case 'percentage':
          return reportsService.formatPercentage(value)
        case 'number':
          return reportsService.formatNumber(value)
        default:
          return String(value)
      }
    }

    const formatCurrency = (value) => {
      return reportsService.formatCurrency(value)
    }

    const formatNumber = (value) => {
      return reportsService.formatNumber(value)
    }

    const getChangeIcon = (changePercentage) => {
      if (changePercentage > 0) return 'fas fa-arrow-up'
      if (changePercentage < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const getStatLabel = (tab) => {
      const labels = {
        sales: 'Sales',
        views: 'Views',
        revenue: 'Revenue',
        rating: 'Rating'
      }
      return labels[tab] || tab
    }

    const formatStatValue = (value, type) => {
      switch (type) {
        case 'revenue':
          return formatCurrency(value)
        case 'rating':
          return value?.toFixed(1) || 'N/A'
        default:
          return formatNumber(value)
      }
    }

    const getStockClass = (stock) => {
      if (stock === 0) return 'out-of-stock'
      if (stock <= 10) return 'low-stock'
      return 'in-stock'
    }

    const sortBy = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const updateChart = () => {
      createPerformanceChart()
    }

    const viewProduct = (product) => {
      // TODO: Navigate to product details
      console.log('View product:', product)
    }

    const editProduct = (product) => {
      // TODO: Navigate to product edit
      console.log('Edit product:', product)
    }

    const handleInsightAction = (insight) => {
      // TODO: Handle insight actions
      console.log('Handle insight action:', insight)
    }

    const createPerformanceChart = async () => {
      await nextTick()

      if (performanceChart) {
        performanceChart.destroy()
      }

      if (!performanceChartCanvas.value || !props.data?.charts?.primary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = performanceChartCanvas.value.getContext('2d')

        performanceChart = new Chart(ctx, {
          type: 'line',
          data: props.data.charts.primary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + formatNumber(context.parsed.y)
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: 'Count'
                },
                ticks: {
                  callback: function(value) {
                    return formatNumber(value)
                  }
                }
              }
            },
            interaction: {
              mode: 'nearest',
              axis: 'x',
              intersect: false
            }
          }
        })
      } catch (error) {
        console.error('Error creating performance chart:', error)
      }
    }

    const createCategoryChart = async () => {
      await nextTick()

      if (categoryChart) {
        categoryChart.destroy()
      }

      if (!categoryChartCanvas.value || !props.data?.charts?.secondary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = categoryChartCanvas.value.getContext('2d')

        categoryChart = new Chart(ctx, {
          type: 'doughnut',
          data: props.data.charts.secondary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const total = context.dataset.data.reduce((sum, value) => sum + value, 0)
                    const percentage = ((context.parsed / total) * 100).toFixed(1)
                    return context.label + ': ' + formatNumber(context.parsed) + ' products (' + percentage + '%)'
                  }
                }
              }
            }
          }
        })
      } catch (error) {
        console.error('Error creating category chart:', error)
      }
    }

    // Watchers
    watch(() => props.data, () => {
      nextTick(() => {
        createPerformanceChart()
        createCategoryChart()
      })
    }, { deep: true })

    watch([productSearch, categoryFilter, statusFilter], () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      nextTick(() => {
        createPerformanceChart()
        createCategoryChart()
      })
    })

    onUnmounted(() => {
      if (performanceChart) {
        performanceChart.destroy()
      }
      if (categoryChart) {
        categoryChart.destroy()
      }
    })

    return {
      performanceChartCanvas,
      categoryChartCanvas,
      chartPeriod,
      activeTab,
      productSearch,
      categoryFilter,
      statusFilter,
      currentPage,
      itemsPerPage,
      performanceTabs,
      productMetrics,
      productInsights,
      categories,
      topProductsByTab,
      conversionFunnel,
      priceRanges,
      stockStatus,
      filteredProducts,
      totalPages,
      paginatedProducts,
      formatMetricValue,
      formatCurrency,
      formatNumber,
      getChangeIcon,
      getStatLabel,
      formatStatValue,
      getStockClass,
      sortBy,
      updateChart,
      viewProduct,
      editProduct,
      handleInsightAction
    }
  }
}
</script>

<style scoped>
.products-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Metrics Section */
.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.metric-card.positive {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #f8fafc 100%);
}

.metric-card.negative {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #f8fafc 100%);
}

.metric-card.neutral {
  border-left: 4px solid #6b7280;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.positive .metric-change {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.negative .metric-change {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.neutral .metric-change {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-comparison {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Charts Section */
.charts-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
}

.chart-container {
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.chart-content {
  height: 300px;
  position: relative;
}

/* Top Products Section */
.top-products-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.performance-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-button:hover {
  color: #374151;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.product-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.product-rank {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 60px;
}

.rank-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #374151;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
}

.rank-1 { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); }
.rank-2 { background: linear-gradient(135deg, #c0c0c0 0%, #e5e7eb 100%); }
.rank-3 { background: linear-gradient(135deg, #cd7f32 0%, #d97706 100%); }
.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.product-category {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 2px;
}

.product-sku {
  font-size: 0.75rem;
  color: #9ca3af;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.product-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 100px;
}

.stat-item {
  text-align: right;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 2px;
  text-transform: uppercase;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.stat-value.rating {
  color: #f59e0b;
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .product-stats {
    width: 100%;
    flex-direction: row;
    justify-content: space-around;
  }

  .stat-item {
    text-align: center;
  }

  .chart-content {
    height: 250px;
  }

  .metric-value {
    font-size: 1.5rem;
  }
}

/* Analytics Section */
.analytics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.analytics-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.analytics-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.analytics-header i {
  font-size: 1.25rem;
  color: #667eea;
}

/* Conversion Funnel */
.funnel-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.funnel-step {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.step-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-name {
  font-weight: 500;
  color: #374151;
}

.step-value {
  font-weight: 600;
  color: #111827;
}

.step-percentage {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Price Ranges */
.price-ranges {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-range {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.range-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.range-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.range-fill {
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

.range-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.range-count {
  color: #374151;
}

.range-percentage {
  color: #6b7280;
}

/* Stock Status */
.stock-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stock-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.2s;
}

.stock-item.in-stock {
  background: rgba(16, 185, 129, 0.1);
}

.stock-item.low-stock {
  background: rgba(245, 158, 11, 0.1);
}

.stock-item.out-of-stock {
  background: rgba(239, 68, 68, 0.1);
}

.stock-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.in-stock .stock-icon {
  background: #10b981;
  color: white;
}

.low-stock .stock-icon {
  background: #f59e0b;
  color: white;
}

.out-of-stock .stock-icon {
  background: #ef4444;
  color: white;
}

.stock-info {
  flex: 1;
}

.stock-label {
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.stock-count {
  font-size: 0.875rem;
  color: #6b7280;
}

.stock-percentage {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Insights Section */
.insights-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.insights-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
  background: #f9fafb;
  transition: all 0.2s;
}

.insight-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.insight-card.positive {
  background: linear-gradient(135deg, #f0fdf4 0%, #f9fafb 100%);
  border-left-color: #10b981;
}

.insight-card.negative {
  background: linear-gradient(135deg, #fef2f2 0%, #f9fafb 100%);
  border-left-color: #ef4444;
}

.insight-card.warning {
  background: linear-gradient(135deg, #fffbeb 0%, #f9fafb 100%);
  border-left-color: #f59e0b;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.positive .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.negative .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.insight-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 12px;
}

.insight-action {
  margin-top: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.insight-priority {
  display: flex;
  align-items: center;
}

.priority-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.priority-1 { background: #9ca3af; }
.priority-2 { background: #6b7280; }
.priority-3 { background: #f59e0b; }
.priority-4 { background: #ef4444; }
.priority-5 { background: #dc2626; }

@media (max-width: 768px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .funnel-step,
  .price-range {
    gap: 6px;
  }

  .stock-item {
    padding: 8px;
  }

  .insight-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

/* Products Table Section */
.products-table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 300px;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.products-table th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  white-space: nowrap;
}

.products-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.products-table th.sortable:hover {
  background: #f3f4f6;
}

.sort-icon {
  margin-left: 8px;
  opacity: 0.5;
  font-size: 0.75rem;
}

.products-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.product-row:hover {
  background: #f9fafb;
}

.product-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-name {
  font-weight: 500;
  color: #111827;
}

.product-sku {
  font-size: 0.75rem;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.category-badge {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
}

.views-count,
.sales-count {
  font-weight: 600;
  color: #111827;
}

.revenue-amount {
  font-weight: 600;
  color: #10b981;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f59e0b;
  font-weight: 500;
}

.stock-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.stock-indicator.in-stock {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stock-indicator.low-stock {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.stock-indicator.out-of-stock {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.view-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.view-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.edit-btn {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.edit-btn:hover {
  background: rgba(16, 185, 129, 0.2);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 16px;
}

@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
    min-width: auto;
  }

  .products-table {
    font-size: 0.875rem;
  }

  .products-table th,
  .products-table td {
    padding: 8px 12px;
  }

  .product-cell {
    gap: 8px;
  }

  .product-thumbnail {
    width: 40px;
    height: 40px;
  }
}
</style>
