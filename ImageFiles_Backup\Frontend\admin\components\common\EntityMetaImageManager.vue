<template>
  <div class="entity-meta-image-manager">
    <div class="meta-image-header">
      <h6 class="mb-2">
        <i class="fas fa-share-alt me-2"></i>
        Мета-зображення для соціальних мереж
      </h6>
      <p class="text-muted small mb-3">
        Це зображення буде відображатися при поширенні посилання в соціальних мережах.
        Рекомендований розмір: 1200x630 пікселів.
      </p>
    </div>

    <div class="meta-image-container">
      <!-- Поточне мета-зображення -->
      <div v-if="currentImageUrl" class="current-meta-image">
        <div class="meta-image-preview">
          <img 
            :src="currentImageUrl" 
            :alt="imageAlt"
            class="meta-image"
            @click="openImageViewer"
          />
          <div class="meta-image-overlay">
            <button 
              type="button"
              class="btn btn-sm btn-danger"
              @click="removeImage"
              :disabled="uploading"
              title="Видалити мета-зображення"
            >
              <i class="fas fa-trash"></i>
            </button>
            <button 
              type="button"
              class="btn btn-sm btn-primary"
              @click="openImageViewer"
              title="Переглянути в повному розмірі"
            >
              <i class="fas fa-eye"></i>
            </button>
          </div>
        </div>
        
        <!-- Превью соціальної картки -->
        <div class="social-preview mt-3">
          <div class="social-card">
            <img :src="currentImageUrl" class="social-card-image" />
            <div class="social-card-content">
              <h6 class="social-card-title">{{ socialPreviewTitle }}</h6>
              <p class="social-card-description">{{ socialPreviewDescription }}</p>
              <small class="social-card-url">{{ socialPreviewUrl }}</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Зона завантаження -->
      <div 
        v-else
        class="meta-upload-zone"
        :class="{ 'drag-over': isDragOver }"
        @drop="handleDrop"
        @dragover.prevent="isDragOver = true"
        @dragleave="isDragOver = false"
        @click="triggerFileInput"
      >
        <div class="upload-content">
          <i class="fab fa-facebook-f upload-icon"></i>
          <i class="fab fa-twitter upload-icon"></i>
          <i class="fab fa-linkedin-in upload-icon"></i>
          <p class="upload-text">
            Додати мета-зображення для соціальних мереж
          </p>
          <p class="upload-hint">
            Рекомендований розмір: 1200x630px<br>
            {{ uploadHint }}
          </p>
        </div>
      </div>

      <!-- Прогрес завантаження -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress">
          <div 
            class="progress-bar bg-info" 
            :style="{ width: uploadProgress + '%' }"
          ></div>
        </div>
        <p class="progress-text">Завантаження мета-зображення: {{ uploadProgress }}%</p>
      </div>

      <!-- Помилки -->
      <div v-if="error" class="alert alert-danger mt-2">
        {{ error }}
      </div>

      <!-- Індикатор локальних змін -->
      <div v-if="hasChanges && isInLocalMode" class="local-changes-indicator">
        <div class="changes-badge">
          <i class="fas fa-clock"></i>
          <span v-if="pendingRemoval">Мета-зображення буде видалено</span>
          <span v-else-if="pendingFile">Нове мета-зображення буде завантажено</span>
          <span v-else>Є незбережені зміни</span>
        </div>
      </div>
    </div>

    <!-- Прихований input для файлів -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- Модальне вікно для перегляду зображення -->
    <div 
      v-if="showImageViewer" 
      class="modal fade show d-block"
      @click="closeImageViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ imageAlt }}</h5>
            <button 
              type="button" 
              class="btn-close"
              @click="closeImageViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <img 
              :src="currentImageUrl" 
              :alt="imageAlt"
              class="img-fluid"
            />
            <div class="mt-3">
              <h6>Превью в соціальних мережах:</h6>
              <div class="social-card mx-auto" style="max-width: 500px;">
                <img :src="currentImageUrl" class="social-card-image" />
                <div class="social-card-content">
                  <h6 class="social-card-title">{{ socialPreviewTitle }}</h6>
                  <p class="social-card-description">{{ socialPreviewDescription }}</p>
                  <small class="social-card-url">{{ socialPreviewUrl }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import imageService from '@/services/image.service';

export default {
  name: 'EntityMetaImageManager',
  props: {
    entityType: {
      type: String,
      required: true,
      validator: (value) => ['product', 'category', 'company'].includes(value)
    },
    entityId: {
      type: String,
      default: null
    },
    currentImage: {
      type: String,
      default: null
    },
    imageAlt: {
      type: String,
      default: 'Мета-зображення'
    },
    socialPreviewTitle: {
      type: String,
      default: 'Заголовок сторінки'
    },
    socialPreviewDescription: {
      type: String,
      default: 'Опис сторінки для соціальних мереж'
    },
    socialPreviewUrl: {
      type: String,
      default: 'https://example.com'
    },
    maxSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    },
    allowedTypes: {
      type: Array,
      default: () => ['image/jpeg', 'image/png', 'image/webp']
    },
    localMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['meta-image-uploaded', 'meta-image-removed', 'meta-image-changed'],
  data() {
    return {
      isDragOver: false,
      uploading: false,
      uploadProgress: 0,
      error: null,
      showImageViewer: false,
      localImageUrl: null,
      pendingFile: null,
      pendingRemoval: false
    };
  },
  computed: {
    currentImageUrl() {
      // Якщо мета-зображення позначено для видалення, не показуємо його
      if (this.pendingRemoval) {
        return null;
      }
      return this.localImageUrl || this.currentImage;
    },
    uploadHint() {
      const maxSizeMB = Math.round(this.maxSize / 1024 / 1024);
      const types = this.allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ');
      return `Максимальний розмір: ${maxSizeMB}MB. Формати: ${types}`;
    },
    hasChanges() {
      return this.pendingRemoval || !!this.pendingFile;
    },
    isInLocalMode() {
      return this.localMode || !this.entityId;
    }
  },
  methods: {
    triggerFileInput() {
      console.log('EntityMetaImageManager: triggerFileInput called');
      if (this.$refs.fileInput) {
        this.$refs.fileInput.click();
      } else {
        console.error('EntityMetaImageManager: fileInput ref not found');
      }
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.processFile(file);
      }
    },
    
    handleDrop(event) {
      event.preventDefault();
      this.isDragOver = false;
      
      const files = event.dataTransfer.files;
      if (files.length > 0) {
        this.processFile(files[0]);
      }
    },
    
    async processFile(file) {
      this.error = null;

      // Валідація файлу
      const validation = imageService.validateImageFile(file, {
        maxSize: this.maxSize,
        allowedTypes: this.allowedTypes
      });

      if (!validation.isValid) {
        this.error = validation.errors.join(', ');
        return;
      }

      // Перевіряємо режим роботи
      if (this.isInLocalMode) {
        // Локальний режим - зберігаємо файл для подальшого завантаження
        await this.handleLocalMode(file);
      } else {
        // Негайне завантаження (для зворотної сумісності)
        await this.uploadFile(file);
      }
    },
    
    async handleLocalMode(file) {
      try {
        this.localImageUrl = await imageService.createImagePreview(file);
        this.pendingFile = file;
        this.pendingRemoval = false; // Скидаємо прапор видалення

        console.log('MetaImage local mode - file processed:', {
          fileName: file.name,
          fileSize: file.size,
          previewUrl: this.localImageUrl,
          entityType: this.entityType,
          entityId: this.entityId
        });

        this.$emit('meta-image-changed', {
          type: 'upload',
          file,
          previewUrl: this.localImageUrl,
          isLocal: true
        });
      } catch (error) {
        console.error('Error in handleLocalMode:', error);
        this.error = 'Помилка створення превью зображення';
      }
    },
    
    async uploadFile(file) {
      console.log('EntityMetaImageManager.uploadFile called:', {
        entityType: this.entityType,
        entityId: this.entityId,
        fileName: file?.name,
        fileSize: file?.size,
        fileType: file?.type
      });

      if (!this.entityId) {
        this.error = 'ID сутності не вказано';
        console.error('EntityMetaImageManager: No entity ID provided');
        return;
      }

      this.uploading = true;
      this.uploadProgress = 0;

      try {
        console.log('Calling imageService.uploadMetaImage...');
        const response = await imageService.uploadMetaImage(
          this.entityType,
          this.entityId,
          file,
          (progress) => {
            this.uploadProgress = progress;
          }
        );

        console.log('Meta image upload successful:', response);

        this.localImageUrl = null;
        this.pendingFile = null;

        this.$emit('meta-image-uploaded', response.data);

        return response;

      } catch (error) {
        console.error('Meta image upload failed:', error);
        console.error('Error response:', error.response?.data);
        this.error = error.message;
        throw error; // Re-throw to be caught by processPendingOperations
      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
      }
    },
    
    removeImage() {
      console.log('EntityMetaImageManager: removeImage called', {
        isInLocalMode: this.isInLocalMode,
        entityId: this.entityId,
        currentImage: this.currentImage
      });

      if (this.isInLocalMode) {
        // Локальний режим - позначаємо для відкладеного видалення
        this.localImageUrl = null;
        this.pendingFile = null;
        this.pendingRemoval = true;

        console.log('Local mode: marking meta image for deferred removal');
        this.$emit('meta-image-changed', {
          type: 'removal',
          originalImage: this.currentImage
        });
      } else {
        // Негайне видалення (для зворотної сумісності)
        this.deleteCurrentMetaImage();
      }
    },
    
    openImageViewer() {
      if (this.currentImageUrl) {
        this.showImageViewer = true;
      }
    },
    
    closeImageViewer() {
      this.showImageViewer = false;
    },
    
    async uploadPendingFile() {
      if (this.pendingFile && this.entityId) {
        await this.uploadFile(this.pendingFile);
      }
    },
    
    resetLocalChanges() {
      console.log('Resetting meta image local changes');

      // Очищуємо URL об'єкти для запобігання витоків пам'яті
      if (this.localImageUrl && this.localImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.localImageUrl);
      }

      this.localImageUrl = null;
      this.pendingFile = null;
      this.pendingRemoval = false;
      this.error = null;
    },

    // Публічний метод для отримання стану змін
    getChangeState() {
      return {
        hasChanges: this.hasChanges,
        pendingRemoval: this.pendingRemoval,
        hasPendingFile: !!this.pendingFile,
        pendingFileName: this.pendingFile?.name || null
      };
    },

    // Публічний метод для обробки відкладених операцій при збереженні
    async processPendingOperations() {
      console.log('MetaImage processPendingOperations called:', {
        pendingRemoval: this.pendingRemoval,
        hasPendingFile: !!this.pendingFile,
        hasCurrentImage: !!this.currentImage,
        entityId: this.entityId,
        entityType: this.entityType,
        pendingFileName: this.pendingFile?.name,
        pendingFileSize: this.pendingFile?.size
      });

      const results = {
        uploaded: false,
        removed: false,
        errors: []
      };

      try {
        // Спочатку обробляємо видалення (якщо є поточне зображення)
        if (this.pendingRemoval && this.currentImage) {
          console.log('Processing pending meta image removal');
          try {
            await this.deleteCurrentMetaImage();
            results.removed = true;
            console.log('Meta image removed successfully');
          } catch (error) {
            console.error('Error removing meta image:', error);
            results.errors.push(`Помилка видалення мета-зображення: ${error.message}`);
          }
        }

        // Потім обробляємо завантаження нового файлу
        if (this.pendingFile && this.entityId) {
          console.log('Processing pending meta image upload');
          console.log('Pending file details:', {
            name: this.pendingFile.name,
            size: this.pendingFile.size,
            type: this.pendingFile.type
          });
          try {
            const response = await this.uploadFile(this.pendingFile);
            console.log('Meta image upload response:', response);
            results.uploaded = true;
            console.log('Meta image uploaded successfully');
          } catch (error) {
            console.error('Error uploading meta image:', error);
            console.error('Error details:', error.response?.data || error.message);
            results.errors.push(`Помилка завантаження мета-зображення: ${error.message}`);
          }
        }

        // Очищуємо відкладені операції тільки якщо не було помилок
        if (results.errors.length === 0) {
          console.log('Clearing pending meta image operations');
          this.resetLocalChanges();
        }

        return results;
      } catch (error) {
        console.error('Unexpected error in meta image processPendingOperations:', error);
        results.errors.push(`Неочікувана помилка: ${error.message}`);
        return results;
      }
    },

    // Приватний метод для видалення поточного мета-зображення через API
    async deleteCurrentMetaImage() {
      console.log('deleteCurrentMetaImage called:', {
        entityId: this.entityId,
        entityType: this.entityType,
        currentImage: this.currentImage
      });

      if (!this.entityId || !this.currentImage) {
        console.warn('Cannot delete meta image: missing entityId or currentImage');
        return;
      }

      try {
        // Використовуємо універсальний API для мета-зображень
        console.log('Deleting meta image via API:', {
          entityType: this.entityType,
          entityId: this.entityId
        });

        await imageService.deleteMetaImage(this.entityType, this.entityId);
        console.log('Meta image deleted successfully via API');

      } catch (error) {
        console.error('Error deleting meta image via API:', error);
        // Якщо зображення вже не існує (404), це не помилка
        if (error.status === 404 || error.response?.status === 404) {
          console.log('Meta image already deleted, continuing...');
          return;
        }
        throw error;
      }
    }
  }
};
</script>

<style scoped>
.entity-meta-image-manager {
  width: 100%;
}

.meta-image-header {
  margin-bottom: 1rem;
}

.meta-image-container {
  position: relative;
  border: 2px dashed #17a2b8;
  border-radius: 8px;
  overflow: hidden;
}

.current-meta-image {
  padding: 1rem;
}

.meta-image-preview {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.meta-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.meta-image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.meta-image-preview:hover .meta-image-overlay {
  opacity: 1;
}

.meta-upload-zone {
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.meta-upload-zone:hover,
.meta-upload-zone.drag-over {
  border-color: #138496;
  background-color: #f8f9fa;
}

.upload-icon {
  font-size: 2rem;
  color: #17a2b8;
  margin: 0 0.5rem 1rem;
}

.upload-text {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #17a2b8;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

.social-preview {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

.social-card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.social-card-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.social-card-content {
  padding: 0.75rem;
}

.social-card-title {
  margin: 0 0 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1a1a1a;
}

.social-card-description {
  margin: 0 0 0.5rem;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.3;
}

.social-card-url {
  color: #17a2b8;
  font-size: 0.75rem;
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  padding: 1rem;
}

.progress {
  height: 8px;
  margin-bottom: 0.5rem;
}

.progress-text {
  margin: 0;
  font-size: 0.875rem;
  text-align: center;
}

.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Local changes indicator */
.local-changes-indicator {
  margin-top: 0.5rem;
}

.changes-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #856404;
}

.changes-badge i {
  font-size: 0.75rem;
}
</style>
