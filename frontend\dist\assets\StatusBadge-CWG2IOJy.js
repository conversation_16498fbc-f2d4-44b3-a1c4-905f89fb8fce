/* empty css                                                                    */import{_ as s,c as a,o as r,t as c,n as u}from"./index-L-hJxM_5.js";const n={name:"StatusBadge",props:{status:{type:[String,Number],required:!0},type:{type:String,default:"general"}},computed:{statusClass(){const e=this.status.toString().toLowerCase();if(this.type==="product")switch(e){case"0":return"status-pending";case"1":return"status-success";case"2":return"status-danger";case"3":return"status-default";default:return"status-default"}switch(e){case"approved":case"active":case"completed":case"success":case"1":return"status-success";case"pending":case"in progress":case"processing":case"0":return"status-pending";case"rejected":case"failed":case"cancelled":case"inactive":case"-1":case"2":return"status-danger";default:return"status-default"}},statusText(){const e=this.status.toString().toLowerCase();if(this.type==="product")switch(e){case"0":return"Pending";case"1":return"Approved";case"2":return"Rejected";case"3":return"Archived";default:return"Unknown"}switch(e){case"1":return"Approved";case"0":return"Pending";case"-1":case"2":return"Rejected";default:return e.charAt(0).toUpperCase()+e.slice(1)}}}};function d(e,i,o,p,l,t){return r(),a("span",{class:u(["status-badge",t.statusClass])},c(t.statusText),3)}const h=s(n,[["render",d],["__scopeId","data-v-8041c5d7"]]);export{h as S};
