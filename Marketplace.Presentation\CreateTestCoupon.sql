-- Створення тестових купонів для перевірки функціоналу
-- Видаляємо існуючі тестові купони, якщо вони є
DELETE FROM "Coupons" WHERE "Code" IN ('TEST10', 'TEST50', 'WELCOME20');

-- Створюємо тестові купони
INSERT INTO "Coupons" ("Id", "Code", "Discount", "DiscountType", "UsageLimit", "ExpiresAt", "CreatedAt", "UpdatedAt")
VALUES 
    (gen_random_uuid(), 'TEST10', 10, 'Percentage', 100, NOW() + INTERVAL '30 days', NOW(), NULL),
    (gen_random_uuid(), 'TEST50', 50, 'Fixed', 50, NOW() + INTERVAL '60 days', NOW(), NULL),
    (gen_random_uuid(), 'WELCOME20', 20, 'Percentage', NULL, NULL, NOW(), NULL);

-- Перевіряємо створені купони
SELECT "Code", "Discount", "DiscountType", "UsageLimit", "ExpiresAt" 
FROM "Coupons" 
WHERE "Code" IN ('TEST10', 'TEST50', 'WELCOME20');
