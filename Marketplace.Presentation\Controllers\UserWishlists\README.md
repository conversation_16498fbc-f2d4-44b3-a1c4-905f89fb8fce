# UserWishlist Controllers

Контролери для роботи зі списками бажань користувачів в системі Marketplace.

## UserWishlistItemController

Контролер для управління елементами списку бажань користувача. **Оновлено для роботи з Product ID замість WishlistItem ID**.

### Маршрути

#### 1. Отримати всі елементи wishlist
```
GET /api/users/me/wishlist/items
```
Повертає всі товари в списку бажань поточного користувача з пагінацією.

**Параметри запиту:**
- `filter` (string, optional) - Фільтр для пошуку
- `orderBy` (string, optional) - Поле для сортування
- `descending` (bool, optional) - Сортування за спаданням
- `page` (int, optional) - Номер сторінки
- `pageSize` (int, optional) - Розмір сторінки

#### 2. Додати товар до wishlist
```
POST /api/users/me/wishlist/items
```
Додає товар до списку бажань користувача.

**Тіло запиту:**
```json
{
  "productId": "guid"
}
```

#### 3. Отримати товар з wishlist за Product ID
```
GET /api/users/me/wishlist/items/product/{productId}
```
Повертає інформацію про товар в списку бажань за його Product ID.

#### 4. Перевірити, чи товар в wishlist
```
GET /api/users/me/wishlist/items/check/{productId}
```
Перевіряє, чи знаходиться товар в списку бажань користувача.

**Відповідь:**
```json
{
  "success": true,
  "data": {
    "isInWishlist": true
  }
}
```

#### 5. Видалити товар з wishlist за Product ID
```
DELETE /api/users/me/wishlist/items/{productId}
```
Видаляє товар зі списку бажань користувача за його Product ID.

## Ключові зміни

### До оновлення:
- `GET /api/users/me/wishlist/items/{itemId}` - отримати за WishlistItem ID
- `DELETE /api/users/me/wishlist/items/{itemId}` - видалити за WishlistItem ID

### Після оновлення:
- `GET /api/users/me/wishlist/items/product/{productId}` - отримати за Product ID
- `DELETE /api/users/me/wishlist/items/{productId}` - видалити за Product ID
- `GET /api/users/me/wishlist/items/check/{productId}` - перевірити наявність

## Переваги нового підходу

1. **Простота для фронтенду**: Не потрібно зберігати WishlistItem ID
2. **Інтуїтивність**: Операції виконуються з Product ID, який відомий фронтенду
3. **Менше запитів**: Не потрібно спочатку шукати WishlistItem ID
4. **Кращий UX**: Швидші операції додавання/видалення

## Безпека

- Всі ендпоінти вимагають авторизації
- Користувач може працювати тільки зі своїм wishlist
- Автоматична перевірка належності wishlist користувачу

## Обробка помилок

- `404 Not Found` - якщо wishlist або товар не знайдено
- `403 Forbidden` - якщо користувач намагається отримати доступ до чужого wishlist
- `400 Bad Request` - якщо товар вже є в wishlist (при додаванні)

## Приклади використання

### JavaScript (Frontend)
```javascript
// Додати товар до wishlist
await wishlistService.addToWishlist(productId);

// Видалити товар з wishlist
await wishlistService.removeFromWishlist(productId);

// Перевірити, чи товар в wishlist
const response = await wishlistService.checkProductInWishlist(productId);
const isInWishlist = response.data.data.isInWishlist;

// Отримати інформацію про товар в wishlist
const item = await wishlistService.getWishlistItem(productId);
```

## Сумісність

Новий API повністю замінює старий підхід з WishlistItem ID. Фронтенд тепер може працювати тільки з Product ID, що значно спрощує логіку.
