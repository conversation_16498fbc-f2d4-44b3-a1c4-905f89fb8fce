<template>
  <div class="simple-attributes-editor">
    <div class="admin-attributes-header">
      <h4 class="admin-attributes-title">
        <i class="fas fa-tags"></i>
        Product Attributes
      </h4>
    </div>

    <!-- Add New Attribute -->
    <div class="admin-add-attribute">
      <div class="admin-add-form">
        <div class="admin-field-group">
          <label class="admin-field-label">Attribute Name</label>
          <input
            v-model="newKey"
            type="text"
            class="admin-field-input"
            placeholder="e.g., Color, Size, Material"
          />
        </div>
        <div class="admin-field-group">
          <label class="admin-field-label">Value</label>
          <input
            v-model="newValue"
            type="text"
            class="admin-field-input"
            placeholder="Enter attribute value"
          />
        </div>
        <button
          type="button"
          class="admin-btn admin-btn-primary"
          @click="addAttribute"
          :disabled="!newKey.trim() || !newValue.trim()"
        >
          <i class="fas fa-plus"></i>
          Add
        </button>
      </div>
    </div>

    <!-- Attributes Table -->
    <div v-if="Object.keys(attributes).length > 0" class="admin-attributes-table">
      <table class="admin-table">
        <thead>
          <tr>
            <th>Attribute Name</th>
            <th>Values</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(values, key) in attributes"
            :key="key"
            class="admin-attribute-row"
          >
            <td class="admin-attribute-key">
              <div class="admin-key-cell">
                <i class="fas fa-grip-vertical admin-drag-handle"></i>
                <span class="admin-key-name">{{ key }}</span>
              </div>
            </td>
            <td class="admin-attribute-values">
              <div class="admin-values-list">
                <span
                  v-for="(value, valueIndex) in values"
                  :key="valueIndex"
                  class="admin-value-tag"
                >
                  {{ value }}
                  <button
                    type="button"
                    class="admin-value-remove"
                    @click="removeValue(key, valueIndex)"
                    title="Remove this value"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
            </td>
            <td class="admin-attribute-actions">
              <div class="admin-action-buttons">
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-secondary"
                  @click="moveAttributeUp(key)"
                  :disabled="isFirstAttribute(key)"
                  title="Move up"
                >
                  <i class="fas fa-arrow-up"></i>
                </button>
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-secondary"
                  @click="moveAttributeDown(key)"
                  :disabled="isLastAttribute(key)"
                  title="Move down"
                >
                  <i class="fas fa-arrow-down"></i>
                </button>
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-danger"
                  @click="removeAttribute(key)"
                  title="Remove entire attribute"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div v-else class="admin-empty-state">
      <i class="fas fa-tags admin-empty-icon"></i>
      <p class="admin-empty-text">No attributes added yet</p>
      <p class="admin-empty-subtext">Add attributes to describe your product features</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// State
const newKey = ref('');
const newValue = ref('');
const attributes = ref({});

// Initialize attributes from props
const initializeAttributes = (value) => {
  if (!value) return {};

  // Convert any format to our standard format (key: [values])
  const result = {};

  Object.keys(value).forEach(key => {
    const val = value[key];
    if (Array.isArray(val)) {
      result[key] = [...val];
    } else if (typeof val === 'string') {
      // Split by comma if contains comma, otherwise single value
      result[key] = val.includes(',') ? val.split(',').map(v => v.trim()) : [val];
    } else {
      result[key] = [String(val)];
    }
  });

  return result;
};

// Initialize
attributes.value = initializeAttributes(props.modelValue);

// Computed
const attributeKeys = computed(() => Object.keys(attributes.value));

// Methods
const addAttribute = () => {
  const key = newKey.value.trim();
  const value = newValue.value.trim();

  if (!key || !value) return;

  // If key exists, add value to existing array
  if (attributes.value[key]) {
    if (!attributes.value[key].includes(value)) {
      attributes.value[key].push(value);
    }
  } else {
    // Create new attribute with single value
    attributes.value[key] = [value];
  }

  // Clear form
  newKey.value = '';
  newValue.value = '';

  // Emit changes
  emit('update:modelValue', { ...attributes.value });
};

const removeValue = (key, valueIndex) => {
  if (attributes.value[key] && attributes.value[key].length > valueIndex) {
    attributes.value[key].splice(valueIndex, 1);

    // Remove entire attribute if no values left
    if (attributes.value[key].length === 0) {
      delete attributes.value[key];
    }

    emit('update:modelValue', { ...attributes.value });
  }
};

const removeAttribute = (key) => {
  delete attributes.value[key];
  emit('update:modelValue', { ...attributes.value });
};

const moveAttributeUp = (key) => {
  const keys = attributeKeys.value;
  const currentIndex = keys.indexOf(key);

  if (currentIndex > 0) {
    const newAttributes = {};
    keys.forEach((k, index) => {
      if (index === currentIndex - 1) {
        newAttributes[key] = attributes.value[key];
      } else if (index === currentIndex) {
        newAttributes[keys[currentIndex - 1]] = attributes.value[keys[currentIndex - 1]];
      } else {
        newAttributes[k] = attributes.value[k];
      }
    });

    attributes.value = newAttributes;
    emit('update:modelValue', { ...attributes.value });
  }
};

const moveAttributeDown = (key) => {
  const keys = attributeKeys.value;
  const currentIndex = keys.indexOf(key);

  if (currentIndex < keys.length - 1) {
    const newAttributes = {};
    keys.forEach((k, index) => {
      if (index === currentIndex) {
        newAttributes[keys[currentIndex + 1]] = attributes.value[keys[currentIndex + 1]];
      } else if (index === currentIndex + 1) {
        newAttributes[key] = attributes.value[key];
      } else {
        newAttributes[k] = attributes.value[k];
      }
    });

    attributes.value = newAttributes;
    emit('update:modelValue', { ...attributes.value });
  }
};

const isFirstAttribute = (key) => {
  return attributeKeys.value.indexOf(key) === 0;
};

const isLastAttribute = (key) => {
  return attributeKeys.value.indexOf(key) === attributeKeys.value.length - 1;
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  attributes.value = initializeAttributes(newValue);
}, { deep: true, immediate: true });
</script>

<style scoped>
.simple-attributes-editor {
  width: 100%;
}

.admin-attributes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-md);
}

.admin-attributes-title {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin: 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-add-attribute {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  margin-bottom: var(--admin-space-md);
}

.admin-add-form {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--admin-space-md);
  align-items: end;
}

.admin-field-group {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-field-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
}

.admin-field-input {
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-field-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: none;
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-base);
}

.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-primary:hover:not(:disabled) {
  background: var(--admin-primary-dark);
}

.admin-btn-secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  border: 1px solid var(--admin-border-light);
}

.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
}

.admin-btn-xs {
  padding: 4px 8px;
  font-size: var(--admin-text-xs);
}

.admin-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-attributes-table {
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background: var(--admin-bg-secondary);
  padding: var(--admin-space-sm);
  text-align: left;
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  border-bottom: 1px solid var(--admin-border-light);
}

.admin-table td {
  padding: var(--admin-space-sm);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: top;
}

.admin-attribute-row:last-child td {
  border-bottom: none;
}

.admin-key-cell {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-drag-handle {
  color: var(--admin-text-secondary);
  cursor: grab;
}

.admin-key-name {
  font-weight: var(--admin-font-medium);
}

.admin-values-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-xs);
}

.admin-value-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: 4px 8px;
  background: var(--admin-primary);
  color: white;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
}

.admin-value-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: var(--admin-text-xs);
  opacity: 0.8;
}

.admin-value-remove:hover {
  opacity: 1;
}

.admin-action-buttons {
  display: flex;
  gap: var(--admin-space-xs);
}

.admin-empty-state {
  text-align: center;
  padding: var(--admin-space-xl);
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--admin-space-md);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-medium);
  margin-bottom: var(--admin-space-xs);
}

.admin-empty-subtext {
  font-size: var(--admin-text-sm);
}

@media (max-width: 768px) {
  .admin-add-form {
    grid-template-columns: 1fr;
  }
  
  .admin-table {
    font-size: var(--admin-text-sm);
  }
  
  .admin-action-buttons {
    flex-direction: column;
  }
}
</style>
