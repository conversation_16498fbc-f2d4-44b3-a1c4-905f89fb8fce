<template>
  <div class="chat-detail">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <nav class="breadcrumb">
            <ul>
              <li><router-link to="/admin/chats">Chats</router-link></li>
              <li class="is-active"><a>Chat Details</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="has-text-centered" v-if="loading && !chat.id">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-3x"></i>
      </span>
      <p class="mt-3">Loading chat details...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- Chat <PERSON> -->
    <div v-else-if="chat.id">
      <div class="columns">
        <!-- Chat Info -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Chat Information</p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Buyer</label>
                <p class="info-value">
                  <router-link :to="{ name: 'AdminUserDetail', params: { id: chat.buyerId } }">
                    {{ chat.buyerName }}
                  </router-link>
                </p>
              </div>
              <div class="field">
                <label class="label">Seller</label>
                <p class="info-value">
                  <router-link :to="{ name: 'AdminUserDetail', params: { id: chat.sellerId } }">
                    {{ chat.sellerName }}
                  </router-link>
                </p>
              </div>
              <div class="field">
                <label class="label">Created At</label>
                <p class="info-value">{{ formatDateTime(chat.createdAt) }}</p>
              </div>
              <div class="field">
                <label class="label">Last Message</label>
                <p class="info-value">{{ formatDateTime(chat.lastMessageAt) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Messages -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Messages</p>
            </div>
            <div class="card-content">
              <div class="chat-messages" v-if="chat.messages && chat.messages.length > 0">
                <div 
                  v-for="message in chat.messages" 
                  :key="message.id"
                  class="message-item"
                  :class="{ 'is-buyer': message.senderId === chat.buyerId }">
                  <div class="message-header">
                    <strong>{{ message.senderName }}</strong>
                    <span class="message-time">{{ formatDateTime(message.createdAt) }}</span>
                  </div>
                  <div class="message-content">
                    {{ message.content }}
                  </div>
                </div>
              </div>
              <div v-else class="has-text-centered py-4">
                <p class="has-text-grey">No messages in this chat</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { chatsService } from '@/admin/services/chats';

const route = useRoute();

// Reactive data
const chat = ref({});
const loading = ref(false);
const error = ref(null);

// Methods
const fetchChat = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const response = await chatsService.getChatById(route.params.id);
    chat.value = response;
  } catch (err) {
    error.value = err.message || 'Failed to load chat details';
  } finally {
    loading.value = false;
  }
};

// Utility methods
const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
  fetchChat();
});
</script>

<style scoped>
.chat-detail {
  padding: 1rem;
}

.info-value {
  font-weight: 500;
  color: #363636;
}

.chat-messages {
  max-height: 500px;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.message-item {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #e3f2fd;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.message-item.is-buyer {
  background-color: #f3e5f5;
  border-left-color: #9c27b0;
  margin-left: 2rem;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-time {
  font-size: 0.8rem;
  color: #666;
}

.message-content {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
