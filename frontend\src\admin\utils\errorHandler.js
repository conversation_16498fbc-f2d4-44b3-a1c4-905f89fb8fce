// Error handling utilities for admin panel

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',
  VALIDATION: 'VALIDATION',
  AUTHENTICATION: 'AUTHENTICATION',
  AUTHORIZATION: 'AUTHORIZATION',
  NOT_FOUND: 'NOT_FOUND',
  SERVER: 'SERVER',
  UNKNOWN: 'UNKNOWN'
};

// Error messages mapping
export const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: 'Network error. Please check your connection and try again.',
  [ERROR_TYPES.VALIDATION]: 'Please check your input and try again.',
  [ERROR_TYPES.AUTHENTICATION]: 'Authentication failed. Please log in again.',
  [ERROR_TYPES.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
  [ERROR_TYPES.SERVER]: 'Server error. Please try again later.',
  [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try again.'
};

// Specific error messages for orders
export const ORDER_ERROR_MESSAGES = {
  ORDER_NOT_FOUND: 'Order not found. It may have been deleted or you may not have access to it.',
  ORDER_UPDATE_FAILED: 'Failed to update order. Please check your input and try again.',
  ORDER_STATUS_UPDATE_FAILED: 'Failed to update order status. Please try again.',
  PAYMENT_STATUS_UPDATE_FAILED: 'Failed to update payment status. Please try again.',
  ORDER_ITEMS_LOAD_FAILED: 'Failed to load order items. Please refresh the page.',
  ORDER_LIST_LOAD_FAILED: 'Failed to load orders list. Please refresh the page.',
  ORDER_DELETE_FAILED: 'Failed to delete order. Please try again.',
  ORDER_NOTE_ADD_FAILED: 'Failed to add order note. Please try again.'
};

/**
 * Determines the error type based on the error object
 * @param {Error} error - The error object
 * @returns {string} - The error type
 */
export const getErrorType = (error) => {
  if (!error) return ERROR_TYPES.UNKNOWN;

  // Network errors
  if (!error.response) {
    return ERROR_TYPES.NETWORK;
  }

  // HTTP status code based classification
  const status = error.response.status;
  
  switch (status) {
    case 400:
      return ERROR_TYPES.VALIDATION;
    case 401:
      return ERROR_TYPES.AUTHENTICATION;
    case 403:
      return ERROR_TYPES.AUTHORIZATION;
    case 404:
      return ERROR_TYPES.NOT_FOUND;
    case 422:
      return ERROR_TYPES.VALIDATION;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_TYPES.SERVER;
    default:
      return ERROR_TYPES.UNKNOWN;
  }
};

/**
 * Gets a user-friendly error message
 * @param {Error} error - The error object
 * @param {string} context - The context where the error occurred (optional)
 * @returns {string} - User-friendly error message
 */
export const getErrorMessage = (error, context = null) => {
  const errorType = getErrorType(error);
  
  // Try to get specific error message from response
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.errors) {
    // Handle validation errors
    if (Array.isArray(error.response.data.errors)) {
      return error.response.data.errors.join(', ');
    }
    
    if (typeof error.response.data.errors === 'object') {
      const errorMessages = Object.values(error.response.data.errors).flat();
      return errorMessages.join(', ');
    }
  }
  
  // Context-specific error messages
  if (context && ORDER_ERROR_MESSAGES[context]) {
    return ORDER_ERROR_MESSAGES[context];
  }
  
  // Fallback to generic error message
  return ERROR_MESSAGES[errorType] || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];
};

/**
 * Logs detailed error information for debugging
 * @param {Error} error - The error object
 * @param {string} context - The context where the error occurred
 * @param {Object} additionalData - Additional data to log
 */
export const logError = (error, context = 'Unknown', additionalData = {}) => {
  const errorType = getErrorType(error);
  
  console.group(`🚨 ${errorType} Error in ${context}`);
  console.error('Error message:', error.message);
  console.error('Error type:', errorType);
  
  if (error.response) {
    console.error('Response status:', error.response.status);
    console.error('Response statusText:', error.response.statusText);
    console.error('Response data:', error.response.data);
    console.error('Response headers:', error.response.headers);
  }
  
  if (error.request) {
    console.error('Request:', error.request);
  }
  
  if (error.config) {
    console.error('Request config:', {
      url: error.config.url,
      method: error.config.method,
      data: error.config.data,
      params: error.config.params
    });
  }
  
  if (Object.keys(additionalData).length > 0) {
    console.error('Additional data:', additionalData);
  }
  
  console.error('Stack trace:', error.stack);
  console.groupEnd();
};

/**
 * Handles errors in a standardized way
 * @param {Error} error - The error object
 * @param {string} context - The context where the error occurred
 * @param {Object} options - Options for error handling
 * @param {boolean} options.showAlert - Whether to show an alert to the user
 * @param {boolean} options.logError - Whether to log the error
 * @param {Function} options.onError - Custom error handler function
 * @returns {string} - User-friendly error message
 */
export const handleError = (error, context = 'Unknown', options = {}) => {
  const {
    showAlert = false,
    logError: shouldLog = true,
    onError = null
  } = options;
  
  const errorMessage = getErrorMessage(error, context);
  
  if (shouldLog) {
    logError(error, context);
  }
  
  if (showAlert) {
    // Використовуємо console.error замість alert для кращого UX
    console.error('Error Alert:', errorMessage);
    // Можна додати toast notification тут, якщо потрібно
  }
  
  if (onError && typeof onError === 'function') {
    onError(error, errorMessage);
  }
  
  return errorMessage;
};

/**
 * Creates a standardized error handler for Vue components
 * @param {string} context - The context where errors will be handled
 * @param {Object} options - Options for error handling
 * @returns {Function} - Error handler function
 */
export const createErrorHandler = (context, options = {}) => {
  return (error) => {
    return handleError(error, context, options);
  };
};

// Specific error handlers for common operations
export const orderErrorHandlers = {
  loadOrders: createErrorHandler('ORDER_LIST_LOAD_FAILED', { showAlert: true }),
  loadOrder: createErrorHandler('ORDER_NOT_FOUND', { showAlert: true }),
  updateOrder: createErrorHandler('ORDER_UPDATE_FAILED', { showAlert: true }),
  updateOrderStatus: createErrorHandler('ORDER_STATUS_UPDATE_FAILED', { showAlert: true }),
  updatePaymentStatus: createErrorHandler('PAYMENT_STATUS_UPDATE_FAILED', { showAlert: true }),
  deleteOrder: createErrorHandler('ORDER_DELETE_FAILED', { showAlert: true }),
  addOrderNote: createErrorHandler('ORDER_NOTE_ADD_FAILED', { showAlert: true })
};
