import{_ as B,c as u,o as g,a as e,F as G,p as W,t as M,n as E,b as _,C as ae,g as k,H as T,i as H,m as ie,x as v,D,y as L,k as ne,d as re,z as c,I as de,e as ce}from"./index-L-hJxM_5.js";import{S as ue}from"./SearchAndFilters-B3kez0yT.js";import{S as ge}from"./StatusBadge-CWG2IOJy.js";import{p as $}from"./products-Bpq90UOX.js";import{P as me}from"./Pagination-DX2plTiq.js";import{C as pe}from"./ConfirmDialog-hd0r6dWx.js";import{u as ve}from"./useAdminSearch-BxXMp5oH.js";/* empty css                                                                    */const Ie={class:"admin-product-table"},fe={class:"table-container"},be={class:"table"},ye={key:0},he={class:"image is-64x64"},De=["src","alt"],Me={class:"has-text-grey"},Ce={style:{width:"140px"}},ke={class:"admin-product-actions"},Se=["onClick"],Pe=["onClick"],Ae=["onClick"],_e={key:1},Ne={key:2},we={__name:"ProductTable",props:{products:{type:Array,required:!0},categories:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["view","edit","delete"],setup(I){const C=a=>!a||isNaN(a)?"₴0.00":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",minimumFractionDigits:2}).format(a),f=a=>{const n=parseInt(a)||0;return n<=0?"is-danger":n<=10?"is-warning":"is-success"},S=a=>{let n=null;return a.imageUrl&&a.imageUrl.trim()!==""?n=a.imageUrl:a.mainImageUrl&&a.mainImageUrl.trim()!==""?n=a.mainImageUrl:a.image&&a.image.trim()!==""&&(n=a.image),n||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K"},t=a=>{a.target.dataset.errorHandled||(a.target.dataset.errorHandled="true",a.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K",a.target.classList.add("image-error"))};return(a,n)=>(g(),u("div",Ie,[e("div",fe,[e("table",be,[n[6]||(n[6]=e("thead",null,[e("tr",null,[e("th",{style:{width:"100px"}},"Image"),e("th",null,"Name"),e("th",null,"Category"),e("th",null,"Price"),e("th",null,"Stock"),e("th",null,"Status"),e("th",{style:{width:"140px"}},"Actions")])],-1)),!I.loading&&I.products.length>0?(g(),u("tbody",ye,[(g(!0),u(G,null,W(I.products,d=>(g(),u("tr",{key:d.id},[e("td",null,[e("figure",he,[e("img",{src:S(d),alt:d.name,class:"product-thumbnail",onError:t},null,40,De)])]),e("td",null,[e("div",null,[e("strong",null,M(d.name),1),n[0]||(n[0]=e("br",null,null,-1)),e("small",Me,M(d.slug),1)])]),e("td",null,M(d.categoryName||"Unknown"),1),e("td",null,M(C(d.priceAmount||d.price||d.priceValue)),1),e("td",null,[e("span",{class:E(["tag",f(d.stock||d.stockQuantity||0)])},M(d.stock||d.stockQuantity||0),3)]),e("td",null,[_(ge,{status:d.status,type:"product"},null,8,["status"])]),e("td",Ce,[e("div",ke,[e("button",{class:"button is-info is-small",onClick:b=>a.$emit("view",d),title:"View Product"},n[1]||(n[1]=[e("i",{class:"fas fa-eye"},null,-1)]),8,Se),e("button",{class:"button is-primary is-small",onClick:b=>a.$emit("edit",d),title:"Edit Product"},n[2]||(n[2]=[e("i",{class:"fas fa-edit"},null,-1)]),8,Pe),e("button",{class:"button is-danger is-small",onClick:b=>a.$emit("delete",d),title:"Delete Product"},n[3]||(n[3]=[e("i",{class:"fas fa-trash"},null,-1)]),8,Ae)])])]))),128))])):I.loading?(g(),u("tbody",_e,n[4]||(n[4]=[e("tr",null,[e("td",{colspan:"7",class:"has-text-centered"},[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"})])])],-1)]))):(g(),u("tbody",Ne,n[5]||(n[5]=[e("tr",null,[e("td",{colspan:"7",class:"has-text-centered"}," No products found. ")],-1)])))])])]))}},Ue=B(we,[["__scopeId","data-v-1074f36c"]]),xe={class:"modal-card"},$e={class:"modal-card-head"},je={class:"modal-card-title"},Oe={class:"modal-card-body"},ze={class:"field"},Fe={class:"control"},Be={class:"field"},Ee={class:"control"},Qe={class:"columns"},Ze={class:"column is-6"},Re={class:"field"},Te={class:"control"},Le={class:"select is-fullwidth"},Ge=["value"],We={class:"column is-6"},He={class:"field"},Ve={class:"control"},Je={class:"select is-fullwidth"},qe={class:"columns"},Ye={class:"column is-4"},Ke={class:"field"},Xe={class:"control"},es={class:"column is-4"},ss={class:"field"},ts={class:"control"},ls={class:"column is-4"},os={class:"field"},as={class:"control"},is={class:"field"},ns={class:"control"},rs={class:"columns"},ds={class:"column is-6"},cs={class:"field"},us={class:"control"},gs={class:"column is-6"},ms={class:"field"},ps={class:"control"},vs={class:"modal-card-foot"},Is=["disabled"],fs={key:0},bs={key:1},ys={__name:"ProductFormModal",props:{isOpen:{type:Boolean,required:!0},product:{type:Object,default:null}},emits:["close","save"],setup(I,{emit:C}){const f=I,S=C,t=ae({id:null,name:"",description:"",categoryId:"",price:0,salePrice:null,stock:0,status:"active",imageUrl:"",sku:"",slug:""}),a=k(!1),n=k([]),d=async()=>{try{const l=await $.getCategories();n.value=l}catch(l){console.error("Error fetching categories:",l)}},b=async()=>{a.value=!0;try{const l={...t};l.price=parseFloat(l.price),l.stock=parseInt(l.stock),l.salePrice&&(l.salePrice=parseFloat(l.salePrice)),S("save",l)}catch(l){console.error("Error submitting form:",l)}finally{a.value=!1}},N=()=>{t.id=null,t.name="",t.description="",t.categoryId="",t.price=0,t.salePrice=null,t.stock=0,t.status="active",t.imageUrl="",t.sku="",t.slug=""};return T(()=>f.product,l=>{l?Object.keys(t).forEach(s=>{s in l&&(t[s]=l[s])}):N()},{immediate:!0}),T(()=>f.isOpen,l=>{l||N()}),H(()=>{d()}),(l,s)=>(g(),u("div",{class:E(["modal",{"is-active":I.isOpen}])},[e("div",{class:"modal-background",onClick:s[0]||(s[0]=r=>l.$emit("close"))}),e("div",xe,[e("header",$e,[e("p",je,M(I.product?"Edit Product":"Add Product"),1),e("button",{class:"delete","aria-label":"close",onClick:s[1]||(s[1]=r=>l.$emit("close"))})]),e("section",Oe,[e("form",{onSubmit:ie(b,["prevent"])},[e("div",ze,[s[13]||(s[13]=e("label",{class:"label"},"Name",-1)),e("div",Fe,[v(e("input",{class:"input",type:"text",placeholder:"Product name","onUpdate:modelValue":s[2]||(s[2]=r=>t.name=r),required:""},null,512),[[D,t.name]])])]),e("div",Be,[s[14]||(s[14]=e("label",{class:"label"},"Description",-1)),e("div",Ee,[v(e("textarea",{class:"textarea",placeholder:"Product description","onUpdate:modelValue":s[3]||(s[3]=r=>t.description=r),rows:"3"},null,512),[[D,t.description]])])]),e("div",Qe,[e("div",Ze,[e("div",Re,[s[16]||(s[16]=e("label",{class:"label"},"Category",-1)),e("div",Te,[e("div",Le,[v(e("select",{"onUpdate:modelValue":s[4]||(s[4]=r=>t.categoryId=r),required:""},[s[15]||(s[15]=e("option",{value:"",disabled:""},"Select category",-1)),(g(!0),u(G,null,W(n.value,r=>(g(),u("option",{key:r.id,value:r.id},M(r.name),9,Ge))),128))],512),[[L,t.categoryId]])])])])]),e("div",We,[e("div",He,[s[18]||(s[18]=e("label",{class:"label"},"Status",-1)),e("div",Ve,[e("div",Je,[v(e("select",{"onUpdate:modelValue":s[5]||(s[5]=r=>t.status=r),required:""},s[17]||(s[17]=[e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1),e("option",{value:"out_of_stock"},"Out of Stock",-1)]),512),[[L,t.status]])])])])])]),e("div",qe,[e("div",Ye,[e("div",Ke,[s[19]||(s[19]=e("label",{class:"label"},"Price ($)",-1)),e("div",Xe,[v(e("input",{class:"input",type:"number",step:"0.01",min:"0",placeholder:"0.00","onUpdate:modelValue":s[6]||(s[6]=r=>t.price=r),required:""},null,512),[[D,t.price]])])])]),e("div",es,[e("div",ss,[s[20]||(s[20]=e("label",{class:"label"},"Sale Price ($)",-1)),e("div",ts,[v(e("input",{class:"input",type:"number",step:"0.01",min:"0",placeholder:"0.00","onUpdate:modelValue":s[7]||(s[7]=r=>t.salePrice=r)},null,512),[[D,t.salePrice]])])])]),e("div",ls,[e("div",os,[s[21]||(s[21]=e("label",{class:"label"},"Stock",-1)),e("div",as,[v(e("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":s[8]||(s[8]=r=>t.stock=r),required:""},null,512),[[D,t.stock]])])])])]),e("div",is,[s[22]||(s[22]=e("label",{class:"label"},"Image URL",-1)),e("div",ns,[v(e("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":s[9]||(s[9]=r=>t.imageUrl=r)},null,512),[[D,t.imageUrl]])])]),e("div",rs,[e("div",ds,[e("div",cs,[s[23]||(s[23]=e("label",{class:"label"},"SKU",-1)),e("div",us,[v(e("input",{class:"input",type:"text",placeholder:"SKU-123","onUpdate:modelValue":s[10]||(s[10]=r=>t.sku=r)},null,512),[[D,t.sku]])])])]),e("div",gs,[e("div",ms,[s[24]||(s[24]=e("label",{class:"label"},"Slug",-1)),e("div",ps,[v(e("input",{class:"input",type:"text",placeholder:"product-slug","onUpdate:modelValue":s[11]||(s[11]=r=>t.slug=r)},null,512),[[D,t.slug]])])])])])],32)]),e("footer",vs,[e("button",{class:"button is-primary",onClick:b,disabled:a.value},[a.value?(g(),u("span",fs,s[25]||(s[25]=[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1),e("span",null,"Saving...",-1)]))):(g(),u("span",bs,"Save"))],8,Is),e("button",{class:"button",onClick:s[12]||(s[12]=r=>l.$emit("close"))},"Cancel")])])],2))}},hs=B(ys,[["__scopeId","data-v-fd05cff4"]]),Ds={class:"admin-products"},Ms={class:"admin-product-filters"},Cs={key:0,class:"admin-loading-state"},ks={key:1,class:"admin-alert admin-alert-danger"},Ss={class:"admin-alert-content"},Ps={key:2,class:"admin-empty-state"},As={key:3,class:"admin-product-table"},_s={key:4,class:"pagination-wrapper"},Ns={__name:"Products",setup(I){const C=ce(),f=k([{key:"categoryId",label:"Category",type:"select",columnClass:"is-3",allOption:"All Categories",options:[]},{key:"status",label:"Status",type:"select",columnClass:"is-3",allOption:"All Statuses",options:[{value:"0",label:"Pending"},{value:"1",label:"Approved"},{value:"2",label:"Rejected"}]},{key:"stock",label:"Stock Level",type:"select",columnClass:"is-3",allOption:"All Stock Levels",options:[{value:"in-stock",label:"In Stock (>10)"},{value:"low-stock",label:"Low Stock (1-10)"},{value:"out-of-stock",label:"Out of Stock (0)"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",allOption:!1,options:[{value:"Name",label:"Name"},{value:"CreatedAt",label:"Created Date"},{value:"Stock",label:"Stock"},{value:"Status",label:"Status"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}]),{items:S,loading:t,error:a,isFirstLoad:n,currentPage:d,totalPages:b,totalItems:N,filters:l,fetchData:s,handlePageChange:r}=ve({fetchFunction:$.getProducts,defaultFilters:{search:"",categoryId:"",status:"",stock:"",sortBy:"CreatedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:20,clientSideSearch:!1}),Q=k(!1),j=k(!1),w=k(null),O=k([]),V=async()=>{try{console.log("🗂️ Loading categories...");const o=await de.getCategories();console.log("📋 Categories service response:",o);const i=o.data||o.categories||[];console.log("📊 Categories data:",i),O.value=i,console.log("✅ Categories stored for table:",O.value.length,"categories");const p=(m,x="")=>{let F=[];return m.forEach(A=>{const R=x?`${x} > ${A.name}`:A.name;F.push({value:A.id,label:R}),A.children&&A.children.length>0&&(F=F.concat(p(A.children,R)))}),F},z=p(i);console.log("🔄 Flattened categories for filter:",z);const y=[],h=new Set,U=new Set;z.forEach(m=>{const x=m.label.toLowerCase();!h.has(x)&&!U.has(m.value)?(y.push(m),h.add(x),U.add(m.value)):console.log("🔄 Removing duplicate category:",m.label)}),console.log("✅ Unique categories after deduplication:",y.length);const P=f.value.find(m=>m.key==="categoryId");P?(P.options=y,console.log("✅ Category filter updated with",y.length,"options"),console.log("📋 Sample categories:",y.slice(0,3)),f.value=[...f.value]):console.error("❌ Category filter field not found")}catch(o){console.error("❌ Error loading categories:",o)}},J=o=>{l.search=o},q=(o,i)=>{if(console.log(`🔄 Filter changed: ${o} = "${i}"`),o==="categoryId"&&i){const p=Y(i);console.log("📂 Selected category includes subcategories:",p),l.categoryIds=p,l[o]=i}else o==="categoryId"&&!i&&delete l.categoryIds,l[o]=i;console.log("📊 Current filters state:",{...l})},Y=o=>{const i=[o],p=(z,y)=>{for(const h of z){if(h.id===y){const U=P=>{P.children&&P.children.length>0&&P.children.forEach(m=>{i.push(m.id),U(m)})};return U(h),!0}if(h.children&&h.children.length>0&&p(h.children,y))return!0}return!1};return p(O.value,o),i},K=()=>{Object.keys(l).forEach(o=>{o==="search"?l[o]="":o==="sortBy"?l[o]="CreatedAt":o==="sortOrder"?l[o]="desc":l[o]=""}),delete l.categoryIds,s(1)},X=()=>{C.push("/admin/products/create")},ee=o=>{C.push(`/admin/products/${o.id}/view`)},se=o=>{C.push(`/admin/products/${o.id}/edit`)},Z=()=>{Q.value=!1,w.value=null},te=async o=>{try{o.id?await $.updateProduct(o.id,o):await $.createProduct(o),Z(),s(d.value)}catch(i){console.error("Error saving product:",i)}},le=o=>{w.value=o,j.value=!0},oe=async()=>{if(w.value)try{await $.deleteProduct(w.value.id),j.value=!1,s(d.value)}catch(o){console.error("Error deleting product:",o)}};return H(async()=>{await V(),s(1)}),(o,i)=>(g(),u("div",Ds,[e("div",{class:"admin-page-header"},[i[3]||(i[3]=e("div",{class:"admin-page-title-section"},[e("h1",{class:"admin-page-title"},[e("i",{class:"fas fa-box admin-page-icon"}),re(" Products Management ")]),e("p",{class:"admin-page-subtitle"},"Manage product catalog and inventory")],-1)),e("div",{class:"admin-page-actions"},[e("button",{class:"admin-btn admin-btn-primary",onClick:X},i[2]||(i[2]=[e("i",{class:"fas fa-plus"},null,-1),e("span",null,"Create Product",-1)]))])]),e("div",Ms,[_(ue,{filters:c(l),"filter-fields":f.value,"search-label":"Search Products","search-placeholder":"Search by name, description, or category...","search-column-class":"is-4","total-items":c(N),"item-name":"products",loading:c(t),onSearchChanged:J,onFilterChanged:q,onResetFilters:K},null,8,["filters","filter-fields","total-items","loading"])]),c(t)&&c(n)?(g(),u("div",Cs,i[4]||(i[4]=[e("div",{class:"admin-spinner"},[e("i",{class:"fas fa-spinner fa-pulse"})],-1),e("p",{class:"admin-loading-text"},"Loading products...",-1)]))):c(a)?(g(),u("div",ks,[e("div",Ss,[e("p",null,M(c(a)),1),e("button",{class:"admin-btn admin-btn-secondary admin-btn-sm",onClick:i[0]||(i[0]=(...p)=>c(s)&&c(s)(...p))},i[5]||(i[5]=[e("i",{class:"fas fa-redo"},null,-1),e("span",null,"Retry",-1)]))])])):!c(t)&&c(S).length===0?(g(),u("div",Ps,i[6]||(i[6]=[e("div",{class:"admin-empty-icon"},[e("i",{class:"fas fa-box"})],-1),e("h3",{class:"admin-empty-title"},"No products found",-1),e("p",{class:"admin-empty-text"},"There are no products matching your search criteria.",-1)]))):(g(),u("div",As,[e("div",{class:E(["admin-table-container",{"admin-table-loading":c(t)&&!c(n)}])},[_(Ue,{products:c(S),categories:O.value,loading:c(t),onView:ee,onEdit:se,onDelete:le},null,8,["products","categories","loading"])],2)])),c(b)>1||c(N)>0?(g(),u("div",_s,[_(me,{"current-page":c(d),"total-pages":c(b),onPageChanged:c(r)},null,8,["current-page","total-pages","onPageChanged"])])):ne("",!0),_(hs,{"is-open":Q.value,product:w.value,onClose:Z,onSave:te},null,8,["is-open","product"]),_(pe,{"is-open":j.value,title:"Delete Product",message:"Are you sure you want to delete this product? This action cannot be undone.",onConfirm:oe,onCancel:i[1]||(i[1]=p=>j.value=!1)},null,8,["is-open"])]))}},Bs=B(Ns,[["__scopeId","data-v-00ce3c8a"]]);export{Bs as default};
