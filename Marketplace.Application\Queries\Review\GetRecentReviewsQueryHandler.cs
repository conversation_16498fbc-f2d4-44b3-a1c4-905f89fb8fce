using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Review;

public class GetRecentReviewsQueryHandler :
    PaginatedQueryHandler<GetRecentReviewsQuery, Domain.Entities.Review, ReviewResponse>,
    IRequestHandler<GetRecentReviewsQuery, PaginatedResponse<ReviewResponse>>
{
    private readonly IReviewRepository _repository;

    public GetRecentReviewsQueryHandler(
        IReviewRepository repository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _repository = repository;
    }

    public async Task<PaginatedResponse<ReviewResponse>> Handle(GetRecentReviewsQuery request, CancellationToken cancellationToken)
    {
        // Базовий фільтр - тільки останні відгуки (за останні 30 днів)
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
        Expression<Func<Domain.Entities.Review, bool>> filter = r => r.CreatedAt >= thirtyDaysAgo;

        // Додаємо додатковий фільтр, якщо він є
        if (!string.IsNullOrEmpty(request.Filter))
        {
            var searchFilter = request.Filter.ToLower();
            filter = r => r.CreatedAt >= thirtyDaysAgo && 
                         (r.Comment != null && r.Comment.ToLower().Contains(searchFilter) ||
                          r.Product.Name.ToLower().Contains(searchFilter));
        }

        // Отримуємо пагіновані дані
        var pagedResult = await _repository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy ?? "CreatedAt",
            descending: request.OrderBy == null ? true : request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Review, object>>[] {
                r => r.Product,
                r => r.Rating,
                r => r.User
            }
        );

        // Створюємо пагіновану відповідь
        return CreatePaginatedResponse(request, pagedResult, "api/reviews/recent");
    }
}
