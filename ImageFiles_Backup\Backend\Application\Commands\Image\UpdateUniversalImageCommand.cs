using Marketplace.Domain.Services;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Універсальна команда для оновлення зображення
/// </summary>
public record UpdateUniversalImageCommand(
    string EntityType,
    Guid EntityId,
    IFormFile File,
    string ImageType = "main",
    Guid? ImageId = null
) : IRequest<FileUploadResult>;
