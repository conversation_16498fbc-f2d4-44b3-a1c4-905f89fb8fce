<template>
  <div class="category-skeleton">
    <!-- List View Skeleton -->
    <div v-if="viewMode === 'list'" class="table-container">
      <table class="table is-fullwidth">
        <thead>
          <tr>
            <th>Image</th>
            <th>Name</th>
            <th>Parent</th>
            <th>Products</th>
            <th>Slug</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="i in count" :key="i">
            <td class="image-cell">
              <div class="skeleton-image"></div>
            </td>
            <td><div class="skeleton-text"></div></td>
            <td><div class="skeleton-text skeleton-text-sm"></div></td>
            <td><div class="skeleton-text skeleton-text-xs"></div></td>
            <td><div class="skeleton-text skeleton-text-md"></div></td>
            <td>
              <div class="skeleton-actions">
                <div class="skeleton-button"></div>
                <div class="skeleton-button"></div>
                <div class="skeleton-button"></div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Tree View Skeleton -->
    <div v-else-if="viewMode === 'tree'" class="tree-container">
      <ul class="tree-root">
        <li v-for="i in Math.ceil(count / 2)" :key="i" class="tree-item">
          <div class="tree-node">
            <div class="node-content">
              <div class="skeleton-icon"></div>
              <div class="skeleton-image skeleton-image-sm"></div>
              <div class="skeleton-text skeleton-text-md"></div>
            </div>
            <div class="skeleton-actions">
              <div class="skeleton-button"></div>
              <div class="skeleton-button"></div>
              <div class="skeleton-button"></div>
            </div>
          </div>
          <ul class="tree-children" v-if="i % 2 === 0">
            <li v-for="j in 2" :key="j" class="tree-item">
              <div class="tree-node">
                <div class="node-content">
                  <div class="skeleton-icon"></div>
                  <div class="skeleton-image skeleton-image-sm"></div>
                  <div class="skeleton-text skeleton-text-md"></div>
                </div>
                <div class="skeleton-actions">
                  <div class="skeleton-button"></div>
                  <div class="skeleton-button"></div>
                  <div class="skeleton-button"></div>
                </div>
              </div>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
defineProps({
  count: {
    type: Number,
    default: 5
  },
  viewMode: {
    type: String,
    default: 'list'
  }
});
</script>

<style scoped>
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-text, .skeleton-image, .skeleton-button, .skeleton-icon {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-text {
  height: 16px;
  width: 100%;
}

.skeleton-text-xs {
  width: 30%;
}

.skeleton-text-sm {
  width: 60%;
}

.skeleton-text-md {
  width: 80%;
}

.skeleton-image {
  width: 48px;
  height: 48px;
  border-radius: 4px;
}

.skeleton-image-sm {
  width: 24px;
  height: 24px;
}

.skeleton-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: inline-block;
  margin-right: 4px;
}

.skeleton-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.skeleton-actions {
  display: flex;
}

.image-cell {
  width: 60px;
}

.tree-root {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tree-children {
  list-style: none;
  padding-left: 2rem;
  margin-top: 0.5rem;
}

.tree-item {
  margin-bottom: 0.5rem;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
}

.node-content {
  display: flex;
  align-items: center;
}
</style>
