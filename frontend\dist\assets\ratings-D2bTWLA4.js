import{q as s}from"./index-L-hJxM_5.js";const i={async getRatings(t={}){var a,r;try{return(await s.get("/api/admin/ratings",{params:t})).data.data}catch(e){throw console.error("Error fetching ratings:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to load ratings")}},async getRatingById(t){var a,r;try{return(await s.get(`/api/admin/ratings/${t}`)).data.data}catch(e){throw console.error("Error fetching rating:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to load rating details")}},async updateRating(t,a){var r,e;try{return(await s.put(`/api/admin/ratings/${t}`,a)).data}catch(n){throw console.error("Error updating rating:",n),new Error(((e=(r=n.response)==null?void 0:r.data)==null?void 0:e.message)||"Failed to update rating")}},async deleteRating(t){var a,r;try{return(await s.delete(`/api/admin/ratings/${t}`)).data}catch(e){throw console.error("Error deleting rating:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to delete rating")}},async bulkDeleteRatings(t){var a,r;try{return(await s.post("/api/admin/ratings/bulk-delete",{ids:t})).data}catch(e){throw console.error("Error bulk deleting ratings:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to delete ratings")}},async getRatingStats(){var t,a;try{return(await s.get("/api/admin/ratings/stats")).data.data}catch(r){throw console.error("Error fetching rating stats:",r),new Error(((a=(t=r.response)==null?void 0:t.data)==null?void 0:a.message)||"Failed to load rating statistics")}}};export{i as r};
