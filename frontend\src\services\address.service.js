import api from './api.service';

class AddressService {
  /**
   * Отримує всі адреси поточного користувача
   * @returns {Promise} - Список адрес користувача
   */
  async getUserAddresses() {
    try {
      const response = await api.get('/users/me/addresses');
      return response.data;
    } catch (error) {
      console.error('Error fetching user addresses:', error);
      throw error;
    }
  }

  /**
   * Отримує конкретну адресу за ID
   * @param {string} addressId - ID адреси
   * @returns {Promise} - Адреса
   */
  async getAddressById(addressId) {
    try {
      const response = await api.get(`/addresses/${addressId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching address:', error);
      throw error;
    }
  }

  /**
   * Створює нову адресу
   * @param {Object} addressData - Дані адреси
   * @returns {Promise} - Створена адреса
   */
  async createAddress(addressData) {
    try {
      const response = await api.post('/addresses', addressData);
      return response.data;
    } catch (error) {
      console.error('Error creating address:', error);
      throw error;
    }
  }

  /**
   * Оновлює існуючу адресу
   * @param {string} addressId - ID адреси
   * @param {Object} addressData - Нові дані адреси
   * @returns {Promise} - Оновлена адреса
   */
  async updateAddress(addressId, addressData) {
    try {
      const response = await api.put(`/addresses/${addressId}`, addressData);
      return response.data;
    } catch (error) {
      console.error('Error updating address:', error);
      throw error;
    }
  }

  /**
   * Видаляє адресу
   * @param {string} addressId - ID адреси
   * @returns {Promise}
   */
  async deleteAddress(addressId) {
    try {
      const response = await api.delete(`/addresses/${addressId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting address:', error);
      throw error;
    }
  }

  /**
   * Знаходить найближчі відділення до адреси користувача
   * @param {Object} userAddress - Адреса користувача
   * @param {string} deliveryService - Тип служби доставки ('nova-poshta' або 'ukr-poshta')
   * @returns {Promise} - Список найближчих відділень
   */
  async findNearestOffices(userAddress, deliveryService) {
    try {
      // Використовуємо місто з адреси користувача для пошуку відділень
      const cityName = userAddress.city || userAddress.addressCity;
      
      if (!cityName) {
        throw new Error('Місто не вказано в адресі користувача');
      }

      // Імпортуємо сервіси доставки
      const { default: novaPoshtaService } = await import('./novaposhta.service');
      const { default: ukrPoshtaService } = await import('./ukrposhta.service');

      let cities = [];
      let offices = [];

      if (deliveryService === 'nova-poshta') {
        // Шукаємо місто в Новій Пошті
        cities = await novaPoshtaService.getCities(cityName);
        
        if (cities.length > 0) {
          // Беремо перше знайдене місто (найбільш релевантне)
          const city = cities[0];
          offices = await novaPoshtaService.getWarehouses(city.ref);
        }
      } else if (deliveryService === 'ukr-poshta') {
        // Шукаємо місто в Укрпошті
        cities = await ukrPoshtaService.getCities(cityName);
        
        if (cities.length > 0) {
          // Беремо перше знайдене місто
          const city = cities[0];
          offices = await ukrPoshtaService.getPostOffices(city.id);
        }
      }

      return {
        userAddress,
        city: cities[0] || null,
        offices: offices || [],
        deliveryService
      };
    } catch (error) {
      console.error('Error finding nearest offices:', error);
      throw error;
    }
  }

  /**
   * Розраховує вартість доставки до адреси користувача
   * @param {Object} userAddress - Адреса користувача
   * @param {string} deliveryService - Тип служби доставки
   * @param {Object} packageInfo - Інформація про посилку (вага, вартість)
   * @returns {Promise} - Розрахунок вартості доставки
   */
  async calculateDeliveryToAddress(userAddress, deliveryService, packageInfo = {}) {
    try {
      const { weight = 1, declaredValue = 100 } = packageInfo;
      
      // Імпортуємо сервіси доставки
      const { default: novaPoshtaService } = await import('./novaposhta.service');
      const { default: ukrPoshtaService } = await import('./ukrposhta.service');

      const cityName = userAddress.city || userAddress.addressCity;
      
      if (!cityName) {
        throw new Error('Місто не вказано в адресі користувача');
      }

      let deliveryCost = null;

      if (deliveryService === 'nova-poshta') {
        // Для Нової Пошти потрібно знайти референс міста
        const cities = await novaPoshtaService.getCities(cityName);
        
        if (cities.length > 0) {
          const cityRef = cities[0].ref;
          
          // Розраховуємо вартість (потрібен референс міста відправника)
          // Для демо використовуємо Київ як місто відправника
          const kyivRef = 'e71abb60-4b33-11e4-ab6d-005056801329';
          
          deliveryCost = await novaPoshtaService.calculateDeliveryCost({
            citySender: kyivRef,
            cityRecipient: cityRef,
            weight,
            cost: declaredValue
          });
        }
      } else if (deliveryService === 'ukr-poshta') {
        // Для Укрпошти використовуємо спрощений розрахунок
        const cities = await ukrPoshtaService.getCities(cityName);
        
        if (cities.length > 0) {
          const cityId = cities[0].id;
          
          deliveryCost = await ukrPoshtaService.calculateDeliveryCost({
            fromCityId: '8000000000', // Київ
            toCityId: cityId,
            weight,
            declaredValue
          });
        }
      }

      return {
        userAddress,
        deliveryService,
        packageInfo,
        deliveryCost,
        cityFound: deliveryCost !== null
      };
    } catch (error) {
      console.error('Error calculating delivery to address:', error);
      throw error;
    }
  }

  /**
   * Форматує адресу для відображення
   * @param {Object} address - Об'єкт адреси
   * @returns {string} - Форматована адреса
   */
  formatAddress(address) {
    if (!address) return '';

    // Якщо є готова повна адреса
    if (address.fullAddress) {
      return address.fullAddress;
    }

    const parts = [];

    // Обробляємо різні формати полів адреси
    const street = address.street || address.addressStreet;
    const city = address.city || address.addressCity;
    const region = address.region || address.addressRegion;
    const postalCode = address.postalCode || address.addressPostalCode;

    if (street) parts.push(street);
    if (city) parts.push(city);
    if (region) parts.push(region);
    if (postalCode) parts.push(postalCode);

    return parts.join(', ');
  }

  /**
   * Перевіряє, чи адреса знаходиться в межах зони доставки
   * @param {Object} address - Адреса для перевірки
   * @param {string} deliveryService - Служба доставки
   * @returns {Promise<boolean>} - Чи доступна доставка
   */
  async isDeliveryAvailable(address, deliveryService) {
    try {
      const result = await this.findNearestOffices(address, deliveryService);
      return result.offices.length > 0;
    } catch (error) {
      console.error('Error checking delivery availability:', error);
      return false;
    }
  }
}

export default new AddressService();
