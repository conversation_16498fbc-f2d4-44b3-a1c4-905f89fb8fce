<template>
  <div class="admin-company-edit">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <nav class="admin-breadcrumb">
            <router-link to="/admin/companies" class="admin-breadcrumb-item">
              <i class="fas fa-building"></i>
              Companies
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <router-link 
              v-if="company.id" 
              :to="`/admin/companies/${company.id}`" 
              class="admin-breadcrumb-item">
              {{ company.name || 'Company Details' }}
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <span class="admin-breadcrumb-item admin-breadcrumb-current">
              Edit
            </span>
          </nav>
          <h1 class="admin-page-title">
            <i class="fas fa-edit"></i>
            Edit Company
          </h1>
          <p class="admin-page-subtitle">
            Update company information, financial details, and settings
          </p>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-secondary" @click="resetForm" :disabled="saving">
            <i class="fas fa-undo"></i>
            <span>Reset</span>
          </button>
          <button class="admin-btn admin-btn-primary" @click="saveCompany" :disabled="saving">
            <i class="fas fa-save" :class="{ 'fa-spin': saving }"></i>
            <span>{{ saving ? 'Saving...' : 'Save Changes' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-state" v-if="loading && !company.id">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading company data...</p>
    </div>

    <!-- Error State -->
    <div class="admin-alert admin-alert-danger" v-else-if="error">
      <div class="admin-alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-alert-content">
        <div class="admin-alert-message">{{ error }}</div>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Edit Form -->
    <div v-else-if="company.id" class="admin-company-content">
      <form @submit.prevent="saveCompany" class="admin-company-form">
        
        <!-- Basic Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-building admin-card-icon"></i>
              Basic Information
            </h3>
            <p class="admin-card-subtitle">Company name, description, and contact details</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Company Name *
                </label>
                <input
                  type="text"
                  v-model="form.name"
                  class="admin-form-control"
                  placeholder="Enter company name"
                  required
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Contact Email *
                </label>
                <input
                  type="email"
                  v-model="form.contactEmail"
                  class="admin-form-control"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Contact Phone
                </label>
                <input
                  type="tel"
                  v-model="form.contactPhone"
                  class="admin-form-control"
                  placeholder="+380 XX XXX XXXX"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Company Image URL
                </label>
                <input 
                  type="url" 
                  v-model="form.imageUrl"
                  class="admin-form-control"
                  placeholder="https://example.com/image.jpg"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Description
                </label>
                <textarea 
                  v-model="form.description"
                  class="admin-form-control"
                  rows="4"
                  placeholder="Describe the company..."
                ></textarea>
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Featured Company
                </label>
                <div class="admin-form-checkbox">
                  <input 
                    type="checkbox" 
                    id="featured"
                    v-model="form.isFeatured"
                    class="admin-checkbox"
                  />
                  <label for="featured" class="admin-checkbox-label">
                    Mark as featured company
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- SEO Meta Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-search admin-card-icon"></i>
              SEO Meta Information
            </h3>
            <p class="admin-card-subtitle">Search engine optimization and social media metadata</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Meta Title
                </label>
                <input
                  type="text"
                  v-model="form.metaTitle"
                  class="admin-form-control"
                  placeholder="Company Name - Best Services"
                  maxlength="100"
                />
                <p class="admin-form-help">
                  Title that appears in search results (max 100 characters)
                </p>
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Meta Description
                </label>
                <textarea
                  v-model="form.metaDescription"
                  class="admin-form-control"
                  rows="3"
                  placeholder="Brief description of the company for search engines..."
                  maxlength="300"
                ></textarea>
                <p class="admin-form-help">
                  Description that appears in search results (max 300 characters)
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-map-marker-alt admin-card-icon"></i>
              Address Information
            </h3>
            <p class="admin-card-subtitle">Company location and address details</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Region
                </label>
                <input 
                  type="text" 
                  v-model="form.addressRegion"
                  class="admin-form-control"
                  placeholder="e.g., Kyiv Oblast"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  City
                </label>
                <input 
                  type="text" 
                  v-model="form.addressCity"
                  class="admin-form-control"
                  placeholder="e.g., Kyiv"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Postal Code
                </label>
                <input 
                  type="text" 
                  v-model="form.addressPostalCode"
                  class="admin-form-control"
                  placeholder="e.g., 01001"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Street Address
                </label>
                <input 
                  type="text" 
                  v-model="form.addressStreet"
                  class="admin-form-control"
                  placeholder="e.g., 123 Main Street, Building A"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Financial Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-credit-card admin-card-icon"></i>
              Financial Information
            </h3>
            <p class="admin-card-subtitle">Banking and tax details for payments</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Bank Name
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.bankName"
                  class="admin-form-control"
                  placeholder="e.g., PrivatBank"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Bank Code
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.bankCode"
                  class="admin-form-control"
                  placeholder="e.g., 305299"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Bank Account Number
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.bankAccount"
                  class="admin-form-control"
                  placeholder="e.g., 2600600**********123456789"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Tax ID
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.taxId"
                  class="admin-form-control"
                  placeholder="e.g., **********"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Payment Details
                </label>
                <textarea 
                  v-model="form.finance.paymentDetails"
                  class="admin-form-control"
                  rows="3"
                  placeholder="Additional payment information..."
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- Working Schedule -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-clock admin-card-icon"></i>
              Working Schedule
            </h3>
            <p class="admin-card-subtitle">Set business hours for this company</p>
          </div>
          <div class="admin-card-content">
            <div class="schedule-grid">
              <div
                v-for="(day, index) in weekDays"
                :key="index"
                class="schedule-day"
              >
                <div class="schedule-day-header">
                  <label class="admin-form-label">{{ day.name }}</label>
                  <div class="admin-form-checkbox">
                    <input
                      type="checkbox"
                      :id="`closed-${index}`"
                      v-model="day.isClosed"
                      class="admin-form-checkbox-input"
                    />
                    <label :for="`closed-${index}`" class="admin-form-checkbox-label">
                      Closed
                    </label>
                  </div>
                </div>
                <div v-if="!day.isClosed" class="schedule-times">
                  <div class="admin-form-group">
                    <label class="admin-form-label">Open Time</label>
                    <input
                      type="time"
                      v-model="day.openTime"
                      class="admin-form-control"
                    />
                  </div>
                  <div class="admin-form-group">
                    <label class="admin-form-label">Close Time</label>
                    <input
                      type="time"
                      v-model="day.closeTime"
                      class="admin-form-control"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Images Section -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-images admin-card-icon"></i>
              Company Images
            </h3>
            <p class="admin-card-subtitle">
              Manage company logo and meta image for SEO
            </p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <!-- Company Logo -->
              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Company Logo
                </label>
                <EntityImageManager
                  ref="companyImageManager"
                  entity-type="company"
                  :entity-id="company.id"
                  :current-image="form.imageUrl"
                  image-alt="Company Logo"
                  :local-mode="true"
                  @image-uploaded="handleImageUploaded"
                  @image-removed="handleImageDeleted"
                  @image-changed="handlePendingImageChanged"
                />
                <p class="admin-form-help">
                  Upload a logo for this company. Recommended size: 200x200 pixels.
                </p>
              </div>

              <!-- Meta Image -->
              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Meta Image (SEO)
                </label>
                <EntityMetaImageManager
                  ref="metaImageManager"
                  entity-type="company"
                  :entity-id="company.id"
                  :current-image="form.metaImage"
                  :social-preview-title="form.name || 'Company Name'"
                  :social-preview-description="form.description || 'Company Description'"
                  :social-preview-url="`https://marketplace.com/companies/${form.slug || company.id}`"
                  :local-mode="true"
                  @meta-image-uploaded="handleMetaImageUploaded"
                  @meta-image-removed="handleMetaImageDeleted"
                  @meta-image-changed="handlePendingMetaImageChanged"
                />
                <p class="admin-form-help">
                  Upload a meta image for SEO and social media sharing.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="admin-form-actions">
          <router-link 
            :to="`/admin/companies/${company.id}`"
            class="admin-btn admin-btn-secondary">
            <i class="fas fa-times"></i>
            <span>Cancel</span>
          </router-link>
          
          <button 
            type="button"
            @click="resetForm"
            class="admin-btn admin-btn-secondary"
            :disabled="saving">
            <i class="fas fa-undo"></i>
            <span>Reset</span>
          </button>
          
          <button 
            type="submit"
            class="admin-btn admin-btn-primary"
            :disabled="saving">
            <i class="fas fa-save" :class="{ 'fa-spin': saving }"></i>
            <span>{{ saving ? 'Saving...' : 'Save Changes' }}</span>
          </button>
        </div>

      </form>

      <!-- Upload Progress -->
      <div v-if="pendingUploads.length > 0" class="mt-3">
        <UploadProgress
          :uploads="pendingUploads"
          @retry-upload="retryUpload"
          @cancel-upload="cancelUpload"
        />
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { companiesService } from '@/admin/services/companies';
import EntityImageManager from '@/admin/components/common/EntityImageManager.vue';
import EntityMetaImageManager from '@/admin/components/common/EntityMetaImageManager.vue';
import UploadProgress from '@/admin/components/common/UploadProgress.vue';
import imageService from '@/services/image.service';

const route = useRoute();
const router = useRouter();

// Reactive data
const company = ref({});
const loading = ref(false);
const saving = ref(false);
const error = ref(null);

// Component refs
const companyImageManager = ref(null);
const metaImageManager = ref(null);

// Upload management
const pendingUploads = ref([]);
const pendingImageChange = ref(null);
const pendingMetaImageChange = ref(null);

// Working schedule data
const weekDays = ref([
  { name: 'Monday', day: 0, openTime: '09:00', closeTime: '18:00', isClosed: false },
  { name: 'Tuesday', day: 1, openTime: '09:00', closeTime: '18:00', isClosed: false },
  { name: 'Wednesday', day: 2, openTime: '09:00', closeTime: '18:00', isClosed: false },
  { name: 'Thursday', day: 3, openTime: '09:00', closeTime: '18:00', isClosed: false },
  { name: 'Friday', day: 4, openTime: '09:00', closeTime: '18:00', isClosed: false },
  { name: 'Saturday', day: 5, openTime: '10:00', closeTime: '16:00', isClosed: false },
  { name: 'Sunday', day: 6, openTime: '10:00', closeTime: '16:00', isClosed: true }
]);

// Form data
const form = reactive({
  name: '',
  slug: '',
  description: '',
  contactEmail: '',
  contactPhone: '',
  addressRegion: '',
  addressCity: '',
  addressStreet: '',
  addressPostalCode: '',
  imageUrl: '',
  metaTitle: '',
  metaDescription: '',
  metaImage: '',
  isFeatured: false,
  finance: {
    bankName: '',
    bankAccount: '',
    bankCode: '',
    taxId: '',
    paymentDetails: ''
  }
});

// Original data for reset functionality
const originalData = reactive({
  company: {},
  finance: {}
});

// Methods
const fetchCompany = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;
    
    // Populate form with current data
    populateForm(response.data);
    
    // Store original data for reset
    storeOriginalData(response.data);
  } catch (err) {
    error.value = err.message || 'Failed to load company data';
  } finally {
    loading.value = false;
  }
};

const populateForm = (data) => {
  // Company data
  Object.assign(form, {
    name: data.name || '',
    slug: data.slug || '',
    description: data.description || '',
    contactEmail: data.contactEmail || '',
    contactPhone: data.contactPhone || '',
    addressRegion: data.addressRegion || '',
    addressCity: data.addressCity || '',
    addressStreet: data.addressStreet || '',
    addressPostalCode: data.addressPostalCode || '',
    imageUrl: data.imageUrl || '',
    metaTitle: data.metaTitle || '',
    metaDescription: data.metaDescription || '',
    metaImage: data.metaImage || '',
    isFeatured: data.isFeatured || false
  });

  // Finance data
  if (data.finance) {
    Object.assign(form.finance, {
      bankName: data.finance.bankName || '',
      bankAccount: data.finance.bankAccount || '',
      bankCode: data.finance.bankCode || '',
      taxId: data.finance.taxId || '',
      paymentDetails: data.finance.paymentDetails || ''
    });
  }

  // Schedule data
  if (data.schedule && data.schedule.length > 0) {
    data.schedule.forEach(scheduleItem => {
      const dayIndex = scheduleItem.day;
      if (dayIndex >= 0 && dayIndex < weekDays.value.length) {
        weekDays.value[dayIndex] = {
          ...weekDays.value[dayIndex],
          openTime: scheduleItem.openTime || '09:00',
          closeTime: scheduleItem.closeTime || '18:00',
          isClosed: scheduleItem.isClosed || false
        };
      }
    });
  }
};

const storeOriginalData = (data) => {
  originalData.company = { ...form.company };
  originalData.finance = { ...form.finance };
};

const resetForm = () => {
  Object.assign(form, originalData);
  pendingImageChange.value = null;
  pendingMetaImageChange.value = null;
  pendingUploads.value = [];
};

// Image handlers
const handleImageUploaded = (data) => {
  form.imageUrl = data.fileUrl;
  pendingImageChange.value = null;
  console.log('Company logo uploaded:', data);
};

const handleImageDeleted = () => {
  form.imageUrl = null;
  pendingImageChange.value = null;
  console.log('Company logo deleted');
};

const handlePendingImageChanged = (change) => {
  pendingImageChange.value = change;
  console.log('Company logo changed (local):', change);
};

const handleMetaImageUploaded = (data) => {
  form.metaImage = data.fileUrl;
  pendingMetaImageChange.value = null;
  console.log('Company meta image uploaded:', data);
};

const handleMetaImageDeleted = () => {
  form.metaImage = null;
  pendingMetaImageChange.value = null;
  console.log('Company meta image deleted');
};

const handlePendingMetaImageChanged = (change) => {
  console.log('Company meta image changed (local):', change);

  if (change && change.type === 'removal') {
    // Handle removal
    console.log('Meta image marked for removal');
    form.metaImage = '';
    pendingMetaImageChange.value = change;
  } else if (change && change.file) {
    // Handle new file upload
    console.log('Meta image file changed');
    form.metaImage = change.previewUrl || '';
    pendingMetaImageChange.value = change;
  } else {
    // Clear everything
    form.metaImage = '';
    pendingMetaImageChange.value = null;
  }
};

// Upload progress methods
const retryUpload = async (uploadId) => {
  const upload = pendingUploads.value.find(u => u.id === uploadId);
  if (upload) {
    upload.status = 'uploading';
    upload.progress = 0;
    upload.error = null;

    try {
      console.log('Retrying upload:', uploadId);
    } catch (error) {
      upload.status = 'error';
      upload.error = error.message;
    }
  }
};

const cancelUpload = (uploadId) => {
  const index = pendingUploads.value.findIndex(u => u.id === uploadId);
  if (index !== -1) {
    pendingUploads.value.splice(index, 1);
  }
};

const saveCompany = async () => {
  saving.value = true;
  error.value = null;

  try {
    const updateData = {
      name: form.name,
      slug: form.slug,
      description: form.description,
      contactEmail: form.contactEmail,
      contactPhone: form.contactPhone,
      addressRegion: form.addressRegion,
      addressCity: form.addressCity,
      addressStreet: form.addressStreet,
      addressPostalCode: form.addressPostalCode,
      imageUrl: form.imageUrl ? imageService.normalizeImageUrl(form.imageUrl) : null,
      metaTitle: form.metaTitle,
      metaDescription: form.metaDescription,
      metaImageUrl: form.metaImage ? imageService.normalizeImageUrl(form.metaImage) : null,
      isFeatured: form.isFeatured,
      finance: form.finance,
      schedule: weekDays.value.map(day => ({
        day: day.day,
        openTime: day.openTime,
        closeTime: day.closeTime,
        isClosed: day.isClosed
      }))
    };

    // Process pending image uploads BEFORE updating company data
    if (companyImageManager.value && companyImageManager.value.processPendingOperations) {
      await companyImageManager.value.processPendingOperations();
    }

    // Process pending meta image uploads BEFORE updating company data
    if (metaImageManager.value && metaImageManager.value.processPendingOperations) {
      await metaImageManager.value.processPendingOperations();
    }

    // Update company data (this will handle image deletions via null values)
    await companiesService.updateDetailedCompany(route.params.id, updateData);

    // Redirect to company detail page
    router.push(`/admin/companies/${route.params.id}`);
  } catch (err) {
    error.value = err.message || 'Failed to update company';
  } finally {
    saving.value = false;
  }
};

// Initialize
onMounted(() => {
  fetchCompany();
});
</script>

<style scoped>
.admin-company-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.admin-form-section {
  margin-bottom: 0;
}

.admin-card-subtitle {
  font-size: 0.875rem;
  color: var(--admin-text-muted);
  margin-top: 0.25rem;
}

.admin-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.admin-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-form-group-full {
  grid-column: 1 / -1;
}

.admin-form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  margin-bottom: 0.25rem;
}

.admin-form-control {
  padding: 0.75rem;
  border: 1px solid var(--admin-border-light);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
}

.admin-form-control:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(50, 115, 220, 0.1);
}

.admin-form-control::placeholder {
  color: var(--admin-text-light);
}

.admin-form-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--admin-primary);
}

.admin-checkbox-label {
  font-size: 0.9rem;
  color: var(--admin-text-primary);
  cursor: pointer;
  margin: 0;
}

.admin-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem 0;
  border-top: 1px solid var(--admin-border-light);
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-form-grid {
    grid-template-columns: 1fr;
  }

  .admin-form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .admin-form-actions .admin-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 1200px) {
  .admin-form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Form Validation States */
.admin-form-control:invalid {
  border-color: var(--admin-danger);
}

.admin-form-control:invalid:focus {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 3px rgba(241, 70, 104, 0.1);
}

/* Enhanced form styling */
.admin-form-control[type="email"],
.admin-form-control[type="tel"],
.admin-form-control[type="url"] {
  font-family: 'Courier New', monospace;
}

textarea.admin-form-control {
  resize: vertical;
  min-height: 100px;
}

/* Loading and disabled states */
.admin-form-control:disabled {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
  cursor: not-allowed;
}

.admin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Success state for saved forms */
.form-saved {
  border-color: var(--admin-success);
  background: rgba(72, 199, 116, 0.05);
}

.form-saved:focus {
  border-color: var(--admin-success);
  box-shadow: 0 0 0 3px rgba(72, 199, 116, 0.1);
}

/* Schedule styles */
.schedule-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-day {
  border: 1px solid var(--admin-border-light);
  border-radius: 6px;
  padding: 1rem;
  background: var(--admin-bg-secondary);
}

.schedule-day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.schedule-times {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .schedule-times {
    grid-template-columns: 1fr;
  }
}
</style>
