<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Users
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-user-edit admin-page-icon"></i>
          {{ isEditMode ? 'Edit User' : 'Add User' }}
        </h1>
        <p class="admin-page-subtitle">{{ isEditMode ? 'Update user information and settings' : 'Create a new user account' }}</p>
      </div>
      <div class="admin-page-actions">
        <button @click="resetForm" class="admin-btn admin-btn-secondary">
          <i class="fas fa-undo"></i>
          Reset
        </button>
        <button @click="saveUser" :disabled="loading" class="admin-btn admin-btn-primary">
          <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': loading }"></i>
          {{ loading ? 'Saving...' : (isEditMode ? 'Save Changes' : 'Create User') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading user data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error loading user data</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="loadUser" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-retry"></i>
        Retry
      </button>
    </div>

    <div v-else class="admin-user-edit-content">
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-user-circle"></i>
            User Information
          </h3>
        </div>
        <div class="admin-card-content">
          <form @submit.prevent="saveUser" class="admin-user-form">
            <!-- Basic Information -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Basic Information</h4>

              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label admin-required">
                    Username *
                  </label>
                  <input
                    class="admin-form-input"
                    type="text"
                    placeholder="Enter username"
                    v-model="form.username"
                    :class="{ 'admin-form-input-error': errors.username }"
                    required>
                  <div v-if="errors.username" class="admin-form-error">{{ errors.username }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label admin-required">
                    Email *
                  </label>
                  <input
                    class="admin-form-input"
                    type="email"
                    placeholder="Enter email address"
                    v-model="form.email"
                    :class="{ 'admin-form-input-error': errors.email }"
                    required>
                  <div v-if="errors.email" class="admin-form-error">{{ errors.email }}</div>
                </div>
              </div>

              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label admin-required">
                    Role *
                  </label>
                  <select
                    class="admin-form-select"
                    v-model="form.role"
                    :class="{ 'admin-form-input-error': errors.role }"
                    required>
                    <option value="">Select role</option>
                    <option value="Buyer">Buyer</option>
                    <option value="Seller">Seller</option>
                    <option value="SellerOwner">Seller Owner</option>
                    <option value="Moderator">Moderator</option>
                    <option value="Admin">Admin</option>
                  </select>
                  <div v-if="errors.role" class="admin-form-error">{{ errors.role }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    Birthday
                  </label>
                  <input
                    class="admin-form-input"
                    type="date"
                    v-model="form.birthday"
                    :class="{ 'admin-form-input-error': errors.birthday }">
                  <div class="admin-form-hint">Optional - Date of birth</div>
                  <div v-if="errors.birthday" class="admin-form-error">{{ errors.birthday }}</div>
                </div>
              </div>

              <!-- Personal Information -->
              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label">
                    First Name
                  </label>
                  <input
                    class="admin-form-input"
                    type="text"
                    placeholder="Enter first name"
                    v-model="form.firstName"
                    :class="{ 'admin-form-input-error': errors.firstName }">
                  <div v-if="errors.firstName" class="admin-form-error">{{ errors.firstName }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    Last Name
                  </label>
                  <input
                    class="admin-form-input"
                    type="text"
                    placeholder="Enter last name"
                    v-model="form.lastName"
                    :class="{ 'admin-form-input-error': errors.lastName }">
                  <div v-if="errors.lastName" class="admin-form-error">{{ errors.lastName }}</div>
                </div>
              </div>

              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label">
                    Phone
                  </label>
                  <input
                    class="admin-form-input"
                    type="tel"
                    placeholder="+380XXXXXXXXX"
                    v-model="form.phone"
                    :class="{ 'admin-form-input-error': errors.phone }">
                  <div class="admin-form-hint">Format: +380XXXXXXXXX</div>
                  <div v-if="errors.phone" class="admin-form-error">{{ errors.phone }}</div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label">
                    Gender
                  </label>
                  <select
                    class="admin-form-select"
                    v-model="form.gender"
                    :class="{ 'admin-form-input-error': errors.gender }">
                    <option value="">Select gender</option>
                    <option value="0">Male</option>
                    <option value="1">Female</option>
                  </select>
                  <div v-if="errors.gender" class="admin-form-error">{{ errors.gender }}</div>
                </div>
              </div>

              <div class="admin-form-row">
                <div class="admin-form-group">
                  <label class="admin-form-label">
                    Language
                  </label>
                  <select
                    class="admin-form-select"
                    v-model="form.language"
                    :class="{ 'admin-form-input-error': errors.language }">
                    <option value="">Select language</option>
                    <option value="0">Ukrainian</option>
                    <option value="1">English</option>
                    <option value="2">Russian</option>
                  </select>
                  <div v-if="errors.language" class="admin-form-error">{{ errors.language }}</div>
                </div>
              </div>
            </div>

            <!-- Avatar Section -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">
                <i class="fas fa-user-circle me-2"></i>
                User Avatar
              </h4>
              <div class="admin-form-row">
                <div class="admin-form-group admin-form-group-full">
                  <EntityImageManager
                    entity-type="user"
                    :entity-id="form.id"
                    :current-image="form.avatarUrl"
                    image-alt="User Avatar"
                    :local-mode="true"
                    @image-uploaded="handleAvatarUploaded"
                    @image-removed="handleAvatarRemoved"
                    @image-changed="handleAvatarChanged"
                    ref="avatarManager"
                  />
                  <div class="admin-form-hint">
                    Upload a profile picture for this user. Recommended size: 200x200 pixels.
                  </div>
                </div>
              </div>
            </div>

            <!-- Password Section -->
            <div class="admin-form-section">
              <h4 class="admin-form-section-title">Password</h4>
              <div class="admin-form-group">
                <label class="admin-form-label" :class="{ 'admin-required': !isEditMode }">
                  Password {{ !isEditMode ? '*' : '' }}
                </label>
                <input
                  class="admin-form-input"
                  type="password"
                  :placeholder="isEditMode ? 'Leave empty to keep current password' : 'Enter password'"
                  v-model="form.password"
                  :class="{ 'admin-form-input-error': errors.password }"
                  :required="!isEditMode">
                <div v-if="isEditMode" class="admin-form-hint">
                  Залиште порожнім, щоб не змінювати
                </div>
                <div v-if="errors.password" class="admin-form-error">{{ errors.password }}</div>
              </div>
            </div>

            <!-- Form Legend -->
            <div class="admin-form-legend">
              <p class="admin-form-legend-text">
                <span class="admin-required-indicator">*</span> - обов'язкові поля
              </p>
            </div>

            <!-- Form Actions -->
            <div class="admin-form-actions">
              <button type="submit" class="admin-btn admin-btn-primary" :disabled="saving">
                <span v-if="saving">
                  <i class="fas fa-spinner fa-spin"></i>
                  <span>Saving...</span>
                </span>
                <span v-else>
                  <i class="fas fa-save"></i>
                  <span>{{ isEditMode ? 'Update User' : 'Create User' }}</span>
                </span>
              </button>
              <router-link to="/admin/users" class="admin-btn admin-btn-secondary">
                <i class="fas fa-times"></i>
                <span>Cancel</span>
              </router-link>
            </div>
          </form>
        </div>
      </div>

      <!-- Upload Progress -->
      <div v-if="pendingUploads.length > 0" class="mt-3">
        <UploadProgress
          :uploads="pendingUploads"
          @retry-upload="retryUpload"
          @cancel-upload="cancelUpload"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usersService } from '@/admin/services/users';
import EntityImageManager from '@/admin/components/common/EntityImageManager.vue';
import UploadProgress from '@/admin/components/common/UploadProgress.vue';
import imageService from '@/services/image.service';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const saving = ref(false);
const initialLoading = ref(false);
const error = ref(null);

// Form data
const form = reactive({
  id: null,
  email: '',
  username: '',
  password: '',
  role: '',
  birthday: '',
  firstName: '',
  lastName: '',
  phone: '',
  gender: '',
  language: '',
  avatarUrl: null
});

// Avatar management
const avatarManager = ref(null);
const pendingUploads = ref([]);
const pendingAvatarChange = ref(null);
const originalForm = ref({});

// Form errors
const errors = reactive({
  email: '',
  username: '',
  password: '',
  role: '',
  birthday: '',
  firstName: '',
  lastName: '',
  phone: '',
  gender: '',
  language: ''
});

// Computed
const isEditMode = computed(() => !!route.params.id);
const userId = computed(() => route.params.id);

// Validation
const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key] = '';
  });
};

const validateForm = () => {
  clearErrors();
  let isValid = true;

  // Username validation
  if (!form.username.trim()) {
    errors.username = 'Username is required';
    isValid = false;
  } else if (form.username.length < 3) {
    errors.username = 'Username must be at least 3 characters';
    isValid = false;
  }

  // Email validation
  if (!form.email.trim()) {
    errors.email = 'Email is required';
    isValid = false;
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Please enter a valid email address';
    isValid = false;
  }

  // Password validation (required for new users)
  if (!isEditMode.value && !form.password.trim()) {
    errors.password = 'Password is required';
    isValid = false;
  } else if (form.password && form.password.length < 8) {
    errors.password = 'Password must be at least 8 characters';
    isValid = false;
  } else if (form.password && !/[A-Z]/.test(form.password)) {
    errors.password = 'Password must contain at least one uppercase letter';
    isValid = false;
  } else if (form.password && !/[0-9]/.test(form.password)) {
    errors.password = 'Password must contain at least one number';
    isValid = false;
  } else if (form.password && !/[!@#$%^&*]/.test(form.password)) {
    errors.password = 'Password must contain at least one special character (!@#$%^&*)';
    isValid = false;
  }

  // Role validation
  if (!form.role) {
    errors.role = 'Role is required';
    isValid = false;
  }

  return isValid;
};

// Methods
const loadUser = async () => {
  if (!isEditMode.value) return;

  initialLoading.value = true;
  error.value = null;

  try {
    const user = await usersService.getUserById(userId.value);

    // Populate form with only the fields we need
    form.id = user.id;
    form.email = user.email || '';
    form.username = user.username || '';

    // Convert role from backend format to frontend format
    if (user.role !== undefined && user.role !== null) {
      const { getRoleKey } = await import('@/admin/services/roles');
      form.role = getRoleKey(user.role);
    } else {
      form.role = '';
    }

    form.birthday = user.birthday ? user.birthday.split('T')[0] : '';
    form.firstName = user.firstName || '';
    form.lastName = user.lastName || '';
    form.phone = user.phone || '';
    form.gender = user.gender !== null && user.gender !== undefined ? user.gender.toString() : '';
    form.language = user.language !== null && user.language !== undefined ? user.language.toString() : '';
    form.avatarUrl = user.avatarUrl || null; // Load avatar URL
    form.password = ''; // Always empty for editing
  } catch (err) {
    console.error('Error loading user:', err);
    error.value = 'Failed to load user data. Please try again.';
  } finally {
    initialLoading.value = false;
  }
};

const saveUser = async () => {
  // Validate form first
  if (!validateForm()) {
    return;
  }

  saving.value = true;
  error.value = null;
  clearErrors();

  try {
    const userData = { ...form };

    // Remove password if editing and not provided
    if (isEditMode.value && !userData.password) {
      delete userData.password;
    }

    // Convert birthday to proper format or remove if empty
    if (!userData.birthday) {
      delete userData.birthday;
    } else {
      // Convert date string to ISO format for backend
      userData.birthday = new Date(userData.birthday).toISOString();
    }

    // Convert gender and language to numbers or remove if empty
    if (userData.gender === '') {
      delete userData.gender;
    } else {
      userData.gender = parseInt(userData.gender);
    }

    if (userData.language === '') {
      delete userData.language;
    } else {
      userData.language = parseInt(userData.language);
    }

    // Remove empty fields
    if (!userData.firstName) delete userData.firstName;
    if (!userData.lastName) delete userData.lastName;
    if (!userData.phone) delete userData.phone;

    // Remove avatarUrl from userData as it will be handled separately
    delete userData.avatarUrl;

    let result;
    if (isEditMode.value) {
      result = await usersService.updateUser(userId.value, userData);
    } else {
      result = await usersService.createUser(userData);
      // Set the new user ID for image operations
      if (result && result.id) {
        form.id = result.id;
      }
    }

    // Process pending avatar operations (upload or delete)
    if (avatarManager.value && avatarManager.value.processPendingOperations) {
      console.log('Processing pending avatar operations...');
      try {
        const avatarResults = await avatarManager.value.processPendingOperations();
        console.log('Avatar operations completed:', avatarResults);

        // Оновлюємо form.avatarUrl на основі результатів
        if (avatarResults.removed && !avatarResults.uploaded) {
          form.avatarUrl = null;
        }
        // Якщо було завантажено нове зображення, URL буде оновлено через подію image-uploaded

        // Якщо були помилки, показуємо їх
        if (avatarResults.errors && avatarResults.errors.length > 0) {
          console.warn('Avatar operation errors:', avatarResults.errors);
          // Можна показати попередження, але не блокувати збереження
        }
      } catch (avatarError) {
        console.error('Error processing avatar operations:', avatarError);
        // Не блокуємо збереження користувача через помилки з аватаром
      }
    }

    // Redirect to user detail page if created, or back to list
    if (isEditMode.value) {
      router.push(`/admin/users/${userId.value}`);
    } else if (result && result.id) {
      router.push(`/admin/users/${result.id}`);
    } else {
      router.push('/admin/users');
    }
  } catch (err) {
    console.error('Error saving user:', err);

    // Handle validation errors from server
    if (err.response && err.response.data && err.response.data.errors) {
      const serverErrors = err.response.data.errors;
      Object.keys(serverErrors).forEach(field => {
        if (field.toLowerCase() in errors) {
          errors[field.toLowerCase()] = serverErrors[field][0] || serverErrors[field];
        }
      });
    } else {
      error.value = err.response?.data?.message || 'Failed to save user. Please try again.';
    }
  } finally {
    saving.value = false;
  }
};

const goBack = () => {
  router.push('/admin/users');
};

const resetForm = () => {
  clearErrors();
  pendingAvatarChange.value = null;
  pendingUploads.value = [];

  // Скидаємо локальні зміни в компоненті аватара
  if (avatarManager.value && avatarManager.value.resetLocalChanges) {
    avatarManager.value.resetLocalChanges();
  }

  if (isEditMode.value) {
    // Reload user data
    loadUser();
  } else {
    // Reset to empty form
    form.id = null;
    form.email = '';
    form.username = '';
    form.password = '';
    form.role = '';
    form.birthday = '';
    form.firstName = '';
    form.lastName = '';
    form.phone = '';
    form.gender = '';
    form.language = '';
    form.avatarUrl = null;
  }
};

// Avatar methods
const handleAvatarUploaded = (data) => {
  // Оновлюємо URL аватара після успішного завантаження
  form.avatarUrl = data.fileUrl;
  pendingAvatarChange.value = null;
  console.log('Avatar uploaded successfully:', data);

  // Можна додати повідомлення про успіх
  // toast.success('Аватар успішно завантажено');
};

const handleAvatarRemoved = () => {
  // В локальному режимі не змінюємо form.avatarUrl відразу
  // Це буде оброблено при збереженні
  pendingAvatarChange.value = null;
  console.log('Avatar marked for removal');
};

const handleAvatarChanged = (change) => {
  pendingAvatarChange.value = change;
  console.log('Avatar changed (local):', change);

  // Оновлюємо локальний стан для відображення
  if (change.type === 'removal') {
    // Позначаємо для видалення, але не змінюємо form.avatarUrl
    console.log('Avatar marked for deferred removal');
  } else if (change.type === 'upload' && change.previewUrl) {
    // Показуємо превью нового зображення
    console.log('Avatar preview updated');
  }
};

const uploadPendingAvatar = async () => {
  if (!pendingAvatarChange.value || !form.id) return;

  const uploadId = Date.now().toString();
  const upload = {
    id: uploadId,
    filename: pendingAvatarChange.value.file.name,
    size: pendingAvatarChange.value.file.size,
    type: pendingAvatarChange.value.file.type,
    status: 'uploading',
    progress: 0,
    retryable: true
  };

  pendingUploads.value.push(upload);

  try {
    const response = await imageService.uploadImage(
      'user',
      form.id,
      pendingAvatarChange.value.file,
      (progress) => {
        upload.progress = progress;
      }
    );

    upload.status = 'completed';
    form.avatarUrl = response.data.fileUrl;
    pendingAvatarChange.value = null;

    console.log('Avatar uploaded successfully:', response.data);

  } catch (error) {
    upload.status = 'error';
    upload.error = error.message;
    console.error('Error uploading avatar:', error);
    throw error;
  }
};

const retryUpload = async (uploadId) => {
  const upload = pendingUploads.value.find(u => u.id === uploadId);
  if (upload && pendingAvatarChange.value) {
    upload.status = 'uploading';
    upload.progress = 0;
    upload.error = null;

    try {
      await uploadPendingAvatar();
    } catch (error) {
      // Error already handled in uploadPendingAvatar
    }
  }
};

// Метод для перевірки наявності незбережених змін
const hasUnsavedChanges = () => {
  // Перевіряємо зміни в аватарі
  const avatarChanges = avatarManager.value?.hasChanges || false;

  // Можна додати перевірку інших полів форми якщо потрібно
  // const formChanges = /* логіка перевірки змін в формі */;

  return avatarChanges;
};

// Метод для отримання стану змін аватара
const getAvatarChangeState = () => {
  if (avatarManager.value && avatarManager.value.getChangeState) {
    return avatarManager.value.getChangeState();
  }
  return { hasChanges: false };
};

const cancelUpload = (uploadId) => {
  const index = pendingUploads.value.findIndex(u => u.id === uploadId);
  if (index !== -1) {
    pendingUploads.value.splice(index, 1);
  }
};

// Lifecycle
onMounted(() => {
  loadUser();
});
</script>

<style scoped>
.admin-user-edit-content {
  max-width: 800px;
  margin: 0 auto;
}

.admin-user-form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-2xl);
}

.admin-form-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-form-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  display: flex;
  align-items: center;
  color: var(--admin-gray-900);
  margin: 0;
}

/* Required field styles */
.admin-required {
  position: relative;
}

.admin-required::after {
  content: ' *';
  color: var(--admin-danger);
  font-weight: bold;
}

.admin-required-indicator {
  color: var(--admin-danger);
  font-weight: bold;
}

/* Form validation styles */
.admin-form-input-error {
  border-color: var(--admin-danger) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.admin-form-error {
  color: var(--admin-danger);
  font-size: var(--admin-text-sm);
  margin-top: var(--admin-space-xs);
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-form-error::before {
  content: '⚠';
  font-size: var(--admin-text-xs);
}

.admin-form-hint {
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
  margin-top: var(--admin-space-xs);
  font-style: italic;
}

/* Form legend */
.admin-form-legend {
  padding: var(--admin-space-md);
  background-color: var(--admin-bg-secondary);
  border-radius: var(--admin-radius);
  border-left: 4px solid var(--admin-primary);
}

.admin-form-legend-text {
  margin: 0;
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}

.admin-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--admin-space-lg);
}

.admin-form-actions {
  display: flex;
  gap: var(--admin-space-md);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
}

.admin-form-group-full {
  grid-column: 1 / -1;
}

@media (max-width: 768px) {
  .admin-form-row {
    grid-template-columns: 1fr;
  }
  
  .admin-form-actions {
    flex-direction: column;
  }
}
</style>
