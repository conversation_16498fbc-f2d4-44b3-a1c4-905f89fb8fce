import axios from 'axios';

// Store for active request cancellation tokens
const pendingRequests = new Map();

/**
 * Generate a unique key for a request based on its URL and method
 * @param {string} url - The request URL
 * @param {string} method - The request method
 * @returns {string} A unique key for the request
 */
const getRequestKey = (url, method) => `${method}:${url}`;

/**
 * Cancel all pending requests
 */
const cancelAllRequests = () => {
  pendingRequests.forEach((controller, key) => {
    controller.abort();
    pendingRequests.delete(key);
  });
};

/**
 * Cancel pending requests for a specific route
 * @param {string} routePath - The route path to match against request URLs
 */
const cancelRequestsForRoute = (routePath) => {
  pendingRequests.forEach((controller, key) => {
    if (key.includes(routePath)) {
      controller.abort();
      pendingRequests.delete(key);
    }
  });
};

/**
 * Create a cancellable request
 * @param {Function} apiMethod - The API method to call
 * @param {string} url - The request URL
 * @param {Object} options - Additional options for the request
 * @returns {Promise} The request promise
 */
const createCancellableRequest = async (apiMethod, url, options = {}) => {
  // Cancel any existing request with the same key
  const method = options.method || 'GET';
  const requestKey = getRequestKey(url, method);
  
  if (pendingRequests.has(requestKey)) {
    pendingRequests.get(requestKey).abort();
    pendingRequests.delete(requestKey);
  }
  
  // Create a new AbortController
  const controller = new AbortController();
  pendingRequests.set(requestKey, controller);
  
  try {
    // Add the signal to the request options
    const response = await apiMethod(url, {
      ...options,
      signal: controller.signal
    });
    
    // Clean up after successful request
    pendingRequests.delete(requestKey);
    return response;
  } catch (error) {
    // Clean up after failed request
    pendingRequests.delete(requestKey);
    
    // Rethrow if it's not an abort error
    if (!axios.isCancel(error) && error.name !== 'AbortError') {
      throw error;
    }
    
    // Return a standardized response for aborted requests
    return { aborted: true };
  }
};

export default {
  cancelAllRequests,
  cancelRequestsForRoute,
  createCancellableRequest,
  pendingRequests
};
