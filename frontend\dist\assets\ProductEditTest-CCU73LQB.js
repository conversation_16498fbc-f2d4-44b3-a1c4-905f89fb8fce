import{_ as E,g as p,c as a,a as o,k as g,t as r,b as C,x as k,D as w,F as b,p as L,n as N,o as i}from"./index-L-hJxM_5.js";import O from"./ProductCreate-Bg9PcwCl.js";import $ from"./ProductEdit-CKOjAklg.js";import"./AdminProductHeader-CEj_aj3M.js";import"./EnhancedProductAttributesEditor-lcyAKYjd.js";import"./EnhancedCategorySelector-v7wVF3eo.js";import"./products-Bpq90UOX.js";import"./image.service-DOD4lHqw.js";import"./EntityMetaImageManager-oIAk4B-a.js";const x={class:"product-edit-test"},j={class:"test-section"},J={key:0,class:"test-container"},U={class:"test-section"},V={class:"test-controls"},B={key:0,class:"test-container"},I={class:"test-section"},D={class:"test-results"},F={class:"console-output"},H={class:"log-time"},z={class:"log-message"},M={__name:"ProductEditTest",setup(R){const c=p(!1),d=p(!1),u=p(""),l=p([]),n=(t,e)=>{l.value.push({time:new Date().toLocaleTimeString(),type:t,message:typeof e=="object"?JSON.stringify(e,null,2):e}),l.value.length>50&&(l.value=l.value.slice(-50))},v=t=>{n("info",`Product saved: ${JSON.stringify(t,null,2)}`),console.log("Product saved:",t)},f=t=>{n("success",`Product created: ${JSON.stringify(t,null,2)}`),console.log("Product created:",t)},_=t=>{n("success",`Product updated: ${JSON.stringify(t,null,2)}`),console.log("Product updated:",t)},m=()=>{n("info","Operation cancelled"),console.log("Operation cancelled")},P=()=>{l.value=[]},y=console.log,h=console.error,T=console.warn;return console.log=(...t)=>{n("info",t.join(" ")),y.apply(console,t)},console.error=(...t)=>{n("error",t.join(" ")),h.apply(console,t)},console.warn=(...t)=>{n("warning",t.join(" ")),T.apply(console,t)},(t,e)=>(i(),a("div",x,[e[7]||(e[7]=o("h1",null,"Product Edit Test Page",-1)),o("div",j,[e[3]||(e[3]=o("h2",null,"Test ProductCreate Component",-1)),o("button",{onClick:e[0]||(e[0]=s=>c.value=!c.value),class:"test-btn"},r(c.value?"Hide":"Show")+" ProductCreate Test ",1),c.value?(i(),a("div",J,[C(O,{onSave:v,onCancel:m,onCreated:f})])):g("",!0)]),o("div",U,[e[4]||(e[4]=o("h2",null,"Test ProductEdit Component",-1)),o("div",V,[k(o("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>u.value=s),placeholder:"Enter Product ID for edit test",class:"test-input"},null,512),[[w,u.value]]),o("button",{onClick:e[2]||(e[2]=s=>d.value=!d.value),class:"test-btn"},r(d.value?"Hide":"Show")+" ProductEdit Test ",1)]),d.value&&u.value?(i(),a("div",B,[C($,{"product-id":u.value,"is-create":!1,onSave:v,onCancel:m,onUpdated:_},null,8,["product-id"])])):g("",!0)]),o("div",I,[e[6]||(e[6]=o("h2",null,"Test Results",-1)),o("div",D,[e[5]||(e[5]=o("h3",null,"Console Output:",-1)),o("div",F,[(i(!0),a(b,null,L(l.value,(s,S)=>(i(),a("div",{key:S,class:"log-entry"},[o("span",H,r(s.time),1),o("span",{class:N(["log-type",`log-${s.type}`])},r(s.type.toUpperCase()),3),o("span",z,r(s.message),1)]))),128))]),o("button",{onClick:P,class:"test-btn"},"Clear Logs")])])]))}},tt=E(M,[["__scopeId","data-v-507a425e"]]);export{tt as default};
