<template>
  <div class="delivery-office-selector" v-if="showSelector">
    <h4 class="selector-title">
      <i :class="getServiceIcon()"></i>
      Оберіть відділення {{ getServiceName() }}
    </h4>

    <!-- Пошук міста -->
    <div class="city-search">
      <label for="city-search">Місто доставки:</label>
      <input
        id="city-search"
        type="text"
        v-model="cityQuery"
        @input="searchCities"
        placeholder="Введіть назву міста..."
        class="search-input"
      />
      
      <div v-if="cities.length > 0 && showCityDropdown" class="dropdown cities-dropdown">
        <div
          v-for="city in cities"
          :key="city.id || city.ref"
          @click="selectCity(city)"
          class="dropdown-item"
        >
          <strong>{{ city.name || city.description }}</strong>
          <span v-if="city.region || city.areaDescription" class="city-region">
            {{ city.region || city.areaDescription }}
          </span>
        </div>
      </div>
    </div>

    <!-- Вибране місто -->
    <div v-if="selectedCity" class="selected-city">
      <i class="fas fa-map-marker-alt"></i>
      <span>{{ selectedCity.name || selectedCity.description }}</span>
      <button @click="clearCity" class="clear-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Список відділень -->
    <div v-if="selectedCity" class="offices-section">
      <div v-if="loadingOffices" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
        Завантаження відділень...
      </div>

      <div v-else-if="offices.length > 0" class="offices-list">
        <div
          v-for="office in offices"
          :key="office.id || office.ref"
          @click="selectOffice(office)"
          class="office-item"
          :class="{ 'selected': selectedOffice?.id === office.id || selectedOffice?.ref === office.ref }"
        >
          <div class="office-info">
            <div class="office-name">
              {{ formatOfficeName(office) }}
            </div>
            <div class="office-address">
              {{ formatOfficeAddress(office) }}
            </div>
            <div v-if="office.phone" class="office-phone">
              <i class="fas fa-phone"></i>
              {{ office.phone }}
            </div>
            <div v-if="office.workingHours || office.schedule" class="office-hours">
              <i class="fas fa-clock"></i>
              {{ office.workingHours || office.schedule }}
            </div>
          </div>
          <div class="office-select">
            <i class="fas fa-check-circle" v-if="selectedOffice?.id === office.id || selectedOffice?.ref === office.ref"></i>
            <i class="far fa-circle" v-else></i>
          </div>
        </div>
      </div>

      <div v-else class="no-offices">
        <i class="fas fa-exclamation-triangle"></i>
        Відділення не знайдено
      </div>
    </div>
  </div>
</template>

<script>
import novaPoshtaService from '@/services/novaposhta.service';
import ukrPoshtaService from '@/services/ukrposhta.service';
import { useToast } from '@/composables/useToast';

export default {
  name: 'DeliveryOfficeSelector',
  
  props: {
    shippingMethod: {
      type: Object,
      required: true
    },
    selectedOffice: {
      type: Object,
      default: null
    }
  },

  emits: ['office-selected'],

  data() {
    const { showToast } = useToast();
    
    return {
      cityQuery: '',
      cities: [],
      selectedCity: null,
      offices: [],
      loadingOffices: false,
      showCityDropdown: false,
      searchTimeout: null,
      showToast
    };
  },

  computed: {
    showSelector() {
      // Показуємо селектор тільки для Нової Пошти та Укрпошти
      return this.shippingMethod && (
        this.shippingMethod.id === 'nova-poshta' ||
        this.shippingMethod.id === 'ukr-poshta'
      );
    },

    deliveryService() {
      if (this.shippingMethod.id === 'nova-poshta') return novaPoshtaService;
      if (this.shippingMethod.id === 'ukr-poshta') return ukrPoshtaService;
      return null;
    }
  },

  watch: {
    shippingMethod: {
      handler(newMethod) {
        if (newMethod) {
          this.resetSelector();
        }
      },
      immediate: true
    }
  },

  methods: {
    getServiceName() {
      if (this.shippingMethod.id === 'nova-poshta') return 'Нової Пошти';
      if (this.shippingMethod.id === 'ukr-poshta') return 'Укрпошти';
      return '';
    },

    getServiceIcon() {
      if (this.shippingMethod.id === 'nova-poshta') return 'fas fa-truck';
      if (this.shippingMethod.id === 'ukr-poshta') return 'fas fa-envelope';
      return 'fas fa-shipping-fast';
    },

    async searchCities() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(async () => {
        if (this.cityQuery.length < 2) {
          this.cities = [];
          this.showCityDropdown = false;
          return;
        }

        try {
          console.log('Searching cities for:', this.cityQuery);
          this.cities = await this.deliveryService.getCities(this.cityQuery);
          console.log('Found cities:', this.cities);
          this.showCityDropdown = this.cities.length > 0;
        } catch (error) {
          console.error('Error searching cities:', error);
          this.showToast('Помилка пошуку міст', 'error');
          this.cities = [];
          this.showCityDropdown = false;
        }
      }, 300);
    },

    selectCity(city) {
      this.selectedCity = city;
      this.cityQuery = city.name || city.description;
      this.showCityDropdown = false;
      this.loadOffices();
    },

    clearCity() {
      this.selectedCity = null;
      this.cityQuery = '';
      this.offices = [];
      this.cities = [];
      this.showCityDropdown = false;
      this.$emit('office-selected', null);
    },

    async loadOffices() {
      if (!this.selectedCity) return;

      this.loadingOffices = true;
      try {
        const cityRef = this.selectedCity.ref || this.selectedCity.id;
        console.log('Loading offices for city:', this.selectedCity, 'cityRef:', cityRef);

        if (this.shippingMethod.id === 'nova-poshta') {
          // Нова Пошта
          console.log('Loading Nova Poshta warehouses...');
          this.offices = await this.deliveryService.getWarehouses(cityRef);
        } else if (this.shippingMethod.id === 'ukr-poshta') {
          // Укрпошта
          console.log('Loading UkrPoshta offices...');
          this.offices = await this.deliveryService.getPostOffices(cityRef);
        }

        console.log('Loaded offices:', this.offices);
      } catch (error) {
        console.error('Error loading offices:', error);
        this.showToast('Помилка завантаження відділень', 'error');
        this.offices = [];
      } finally {
        this.loadingOffices = false;
      }
    },

    selectOffice(office) {
      this.$emit('office-selected', {
        city: this.selectedCity,
        office: office,
        service: this.getServiceName()
      });
    },

    formatOfficeName(office) {
      if (this.shippingMethod.id === 'nova-poshta') {
        // Нова Пошта
        return `Відділення №${office.number}`;
      } else if (this.shippingMethod.id === 'ukr-poshta') {
        // Укрпошта
        return office.name;
      }
      return office.description || office.name;
    },

    formatOfficeAddress(office) {
      if (this.shippingMethod.id === 'nova-poshta') {
        // Нова Пошта
        return office.shortAddress || office.description;
      } else if (this.shippingMethod.id === 'ukr-poshta') {
        // Укрпошта
        return office.address;
      }
      return office.address || office.shortAddress;
    },

    resetSelector() {
      this.clearCity();
    }
  }
};
</script>

<style scoped>
.delivery-office-selector {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.city-search {
  position: relative;
  margin-bottom: 16px;
}

.city-search label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.search-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.dropdown-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.city-region {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.selected-city {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 6px;
  margin-bottom: 16px;
}

.clear-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  margin-left: auto;
}

.clear-btn:hover {
  color: #333;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.offices-list {
  max-height: 300px;
  overflow-y: auto;
}

.office-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.office-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.office-item.selected {
  border-color: #007bff;
  background: #f8f9ff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.office-info {
  flex: 1;
}

.office-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.office-address {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.office-phone,
.office-hours {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
}

.office-phone i,
.office-hours i {
  margin-right: 4px;
  width: 12px;
}

.office-select {
  color: #007bff;
  font-size: 18px;
}

.no-offices {
  text-align: center;
  padding: 20px;
  color: #666;
}

@media (max-width: 768px) {
  .delivery-office-selector {
    padding: 12px;
  }
  
  .office-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .office-select {
    align-self: flex-end;
    margin-top: 8px;
  }
}
</style>
