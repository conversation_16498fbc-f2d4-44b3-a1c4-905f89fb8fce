import apiClient from './api.service';

class SellerService {
  /**
   * Отримати публічний профіль продавця
   * @param {string} sellerId - ID продавця
   * @returns {Promise} - Публічна інформація про продавця
   */
  async getSellerProfile(sellerId) {
    try {
      const response = await apiClient.get(`/sellers/${sellerId}/profile`);
      return response;
    } catch (error) {
      console.error(`Error fetching seller profile for ${sellerId}:`, error);
      throw error;
    }
  }

  /**
   * Отримати товари продавця
   * @param {string} sellerId - ID продавця
   * @param {Object} params - Параметри запиту (page, pageSize, filter, orderBy, descending)
   * @returns {Promise} - Пагінований список товарів продавця
   */
  async getSellerProducts(sellerId, params = {}) {
    try {
      const response = await apiClient.get(`/sellers/${sellerId}/products`, { params });
      return response;
    } catch (error) {
      console.error(`Error fetching seller products for ${sellerId}:`, error);
      throw error;
    }
  }

  /**
   * Отримати статистику продавця
   * @param {string} sellerId - ID продавця
   * @returns {Promise} - Публічна статистика продавця
   */
  async getSellerStats(sellerId) {
    try {
      const response = await apiClient.get(`/sellers/${sellerId}/stats`);
      return response;
    } catch (error) {
      console.error(`Error fetching seller stats for ${sellerId}:`, error);
      throw error;
    }
  }

  /**
   * Пошук продавців за критеріями
   * @param {Object} params - Параметри пошуку
   * @returns {Promise} - Список продавців
   */
  async searchSellers(params = {}) {
    try {
      // Використовуємо загальний endpoint користувачів з фільтром по ролі
      const searchParams = {
        ...params,
        role: 'Seller'
      };
      const response = await apiClient.get('/users', { params: searchParams });
      return response;
    } catch (error) {
      console.error('Error searching sellers:', error);
      throw error;
    }
  }

  /**
   * Отримати топ продавців
   * @param {number} limit - Кількість продавців для отримання
   * @returns {Promise} - Список топ продавців
   */
  async getTopSellers(limit = 10) {
    try {
      const response = await apiClient.get('/users', {
        params: {
          role: 'Seller',
          pageSize: limit,
          orderBy: 'ApprovedAt',
          descending: false
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching top sellers:', error);
      throw error;
    }
  }

  /**
   * Перевірити, чи є користувач продавцем
   * @param {string} userId - ID користувача
   * @returns {Promise<boolean>} - true, якщо користувач є продавцем
   */
  async isUserSeller(userId) {
    try {
      const response = await this.getSellerProfile(userId);
      return response && (response.role === 'Seller' || response.role === 'Admin' || response.role === 'Moderator');
    } catch (error) {
      // Якщо помилка 404 або 400, то користувач не є продавцем
      if (error.response && (error.response.status === 404 || error.response.status === 400)) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Отримати рекомендованих продавців
   * @param {number} limit - Кількість продавців
   * @returns {Promise} - Список рекомендованих продавців
   */
  async getRecommendedSellers(limit = 5) {
    try {
      // Отримуємо схвалених продавців, відсортованих за датою останньої активності
      const response = await apiClient.get('/users', {
        params: {
          role: 'Seller',
          isApproved: true,
          pageSize: limit,
          orderBy: 'LastSeenAt',
          descending: true
        }
      });
      return response;
    } catch (error) {
      console.error('Error fetching recommended sellers:', error);
      throw error;
    }
  }
}

export default new SellerService();
