import{_ as N,g as u,h as V,c as a,a as e,k as f,d as x,t as i,F as C,p as b,n as y,x as U,y as A,o as n}from"./index-L-hJxM_5.js";const E={class:"container"},F={class:"columns"},I={class:"column"},L={class:"box"},z={key:0,class:"notification is-danger"},H={key:1,class:"notification is-success"},J={class:"table-container"},j={class:"table is-fullwidth is-striped is-hoverable"},q={class:"buttons"},G=["onClick"],K=["onClick","disabled"],O=["onClick"],Q={class:"modal-card"},R={class:"modal-card-head"},W={class:"modal-card-title"},X={class:"modal-card-body"},Y={key:0},Z={key:0},ee={key:1,class:"has-text-grey"},te={class:"mt-4"},se={key:2},oe={key:3,class:"has-text-grey"},le={class:"modal-card"},ae={class:"modal-card-head"},ne={class:"modal-card-title"},ie={class:"modal-card-body"},ue={class:"field"},de={class:"control"},ce={class:"select is-fullwidth"},re=["value"],ve={class:"help"},pe={class:"modal-card-foot"},me=["disabled"],_e={__name:"CategoryTestPage",setup(Ce){const p=u([{id:"1",name:"Electronics",productsCount:5,subcategoriesCount:2,products:[{id:"1",name:"Laptop",price:999},{id:"2",name:"Phone",price:599},{id:"3",name:"Tablet",price:399},{id:"4",name:"Headphones",price:199},{id:"5",name:"Smartwatch",price:299}],subcategories:[{id:"11",name:"Computers"},{id:"12",name:"Mobile Devices"}]},{id:"2",name:"Clothing",productsCount:3,subcategoriesCount:0,products:[{id:"6",name:"T-Shirt",price:25},{id:"7",name:"Jeans",price:75},{id:"8",name:"Sneakers",price:120}],subcategories:[]},{id:"3",name:"Books",productsCount:0,subcategoriesCount:1,products:[],subcategories:[{id:"31",name:"Fiction"}]},{id:"4",name:"Empty Category",productsCount:0,subcategoriesCount:0,products:[],subcategories:[]}]),c=u(""),r=u(""),h=u(!1),g=u(!1),l=u(null),d=u(""),m=u(!1),P=V(()=>p.value.filter(o=>{var t;return o.id!==((t=l.value)==null?void 0:t.id)})),T=o=>{l.value=o,h.value=!0},k=()=>{h.value=!1,l.value=null},S=o=>{l.value=o,d.value="",g.value=!0},_=()=>{g.value=!1,l.value=null,d.value=""},B=o=>{if(c.value="",r.value="",o.subcategoriesCount>0){c.value=`Неможливо видалити категорію "${o.name}", оскільки вона містить ${o.subcategoriesCount} підкатегорій. Спочатку видаліть або перемістіть всі підкатегорії.`;return}if(o.productsCount>0){c.value=`Неможливо видалити категорію "${o.name}", оскільки вона містить ${o.productsCount} продуктів. Спочатку перемістіть або видаліть всі продукти з цієї категорії.`;return}const t=p.value.findIndex(v=>v.id===o.id);t!==-1&&(p.value.splice(t,1),r.value=`Категорію "${o.name}" успішно видалено.`)},D=()=>{!d.value||!l.value||(m.value=!0,setTimeout(()=>{const o=p.value.find(v=>v.id===d.value),t=l.value;o&&t&&(o.products.push(...t.products),o.productsCount+=t.productsCount,t.products=[],t.productsCount=0,r.value=`Успішно переміщено ${t.productsCount||"всі"} продуктів з "${t.name}" до "${o.name}".`),m.value=!1,_()},1e3))};return(o,t)=>{var v,$,w;return n(),a("div",E,[e("div",F,[e("div",I,[e("div",L,[t[4]||(t[4]=e("h1",{class:"title"},"Category Management Test Page",-1)),t[5]||(t[5]=e("p",{class:"subtitle"},"Test category deletion validation and bulk product category updates with mock data",-1)),c.value?(n(),a("div",z,[e("button",{class:"delete",onClick:t[0]||(t[0]=s=>c.value="")}),x(" "+i(c.value),1)])):f("",!0),r.value?(n(),a("div",H,[e("button",{class:"delete",onClick:t[1]||(t[1]=s=>r.value="")}),x(" "+i(r.value),1)])):f("",!0),e("div",J,[e("table",j,[t[3]||(t[3]=e("thead",null,[e("tr",null,[e("th",null,"Name"),e("th",null,"Products Count"),e("th",null,"Subcategories Count"),e("th",null,"Actions")])],-1)),e("tbody",null,[(n(!0),a(C,null,b(p.value,s=>(n(),a("tr",{key:s.id},[e("td",null,i(s.name),1),e("td",null,i(s.productsCount),1),e("td",null,i(s.subcategoriesCount),1),e("td",null,[e("div",q,[e("button",{class:"button is-small is-info",onClick:M=>T(s)}," View ",8,G),e("button",{class:"button is-small is-warning",onClick:M=>S(s),disabled:s.productsCount===0}," Move Products ",8,K),e("button",{class:"button is-small is-danger",onClick:M=>B(s)}," Delete ",8,O)])])]))),128))])])])])])]),e("div",{class:y(["modal",{"is-active":h.value}])},[e("div",{class:"modal-background",onClick:k}),e("div",Q,[e("header",R,[e("p",W,"Category Details: "+i((v=l.value)==null?void 0:v.name),1),e("button",{class:"delete",onClick:k})]),e("section",X,[l.value?(n(),a("div",Y,[e("p",null,[e("strong",null,"Products ("+i(l.value.productsCount)+"):",1)]),l.value.products.length>0?(n(),a("ul",Z,[(n(!0),a(C,null,b(l.value.products,s=>(n(),a("li",{key:s.id},i(s.name)+" - $"+i(s.price),1))),128))])):(n(),a("p",ee,"No products in this category")),e("p",te,[e("strong",null,"Subcategories ("+i(l.value.subcategoriesCount)+"):",1)]),l.value.subcategories.length>0?(n(),a("ul",se,[(n(!0),a(C,null,b(l.value.subcategories,s=>(n(),a("li",{key:s.id},i(s.name),1))),128))])):(n(),a("p",oe,"No subcategories"))])):f("",!0)]),e("footer",{class:"modal-card-foot"},[e("button",{class:"button",onClick:k},"Close")])])],2),e("div",{class:y(["modal",{"is-active":g.value}])},[e("div",{class:"modal-background",onClick:_}),e("div",le,[e("header",ae,[e("p",ne,'Move All Products from "'+i(($=l.value)==null?void 0:$.name)+'"',1),e("button",{class:"delete",onClick:_})]),e("section",ie,[e("div",ue,[t[7]||(t[7]=e("label",{class:"label"},"Select Target Category",-1)),e("div",de,[e("div",ce,[U(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>d.value=s)},[t[6]||(t[6]=e("option",{value:""},"Choose a category...",-1)),(n(!0),a(C,null,b(P.value,s=>(n(),a("option",{key:s.id,value:s.id},i(s.name),9,re))),128))],512),[[A,d.value]])])]),e("p",ve,"All "+i(((w=l.value)==null?void 0:w.productsCount)||0)+" products will be moved to the selected category.",1)])]),e("footer",pe,[e("button",{class:y(["button is-warning",{"is-loading":m.value}]),onClick:D,disabled:!d.value||m.value}," Move Products ",10,me),e("button",{class:"button",onClick:_},"Cancel")])])],2)])}}},he=N(_e,[["__scopeId","data-v-64be869b"]]);export{he as default};
