<template>
  <div class="log-detail">
    <!-- Header -->
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <nav class="breadcrumb">
            <ul>
              <li><router-link to="/admin/security">Security & Logs</router-link></li>
              <li class="is-active"><a>Log Details</a></li>
            </ul>
          </nav>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button class="button is-primary" @click="fetchLog" :class="{ 'is-loading': loading }">
              <span class="icon"><i class="fas fa-sync-alt"></i></span>
              <span>Refresh</span>
            </button>
            <router-link to="/admin/security" class="button">
              <span class="icon"><i class="fas fa-arrow-left"></i></span>
              <span>Back to Logs</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="has-text-centered" v-if="loading && !log.id">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-3x"></i>
      </span>
      <p class="mt-3">Loading log details...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- Log Details -->
    <div v-else-if="log.id">
      <div class="columns">
        <!-- Main Info -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon" :class="`has-text-${getLogLevelColor(log.level)}`">
                  <i :class="getLogLevelIcon(log.level)"></i>
                </span>
                Log Entry Details
              </p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Level</label>
                    <div class="info-value">
                      <span class="tag" :class="`is-${getLogLevelColor(log.level)}`">
                        {{ log.level }}
                      </span>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Category</label>
                    <p class="info-value">{{ log.category || 'N/A' }}</p>
                  </div>
                  <div class="field">
                    <label class="label">Timestamp</label>
                    <p class="info-value">{{ formatTimestamp(log.timestamp) }}</p>
                  </div>
                  <div class="field">
                    <label class="label">IP Address</label>
                    <p class="info-value">{{ log.ipAddress || 'N/A' }}</p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">User</label>
                    <div class="info-value">
                      <router-link
                        v-if="log.userId"
                        :to="{ name: 'AdminUserDetail', params: { id: log.userId } }"
                        class="user-link">
                        {{ log.userName || 'Unknown User' }}
                      </router-link>
                      <span v-else class="has-text-grey">System</span>
                      <div v-if="log.userEmail" class="user-email">
                        <small class="has-text-grey">{{ log.userEmail }}</small>
                      </div>
                    </div>
                  </div>
                  <div class="field" v-if="log.entityId">
                    <label class="label">Related Entity</label>
                    <div class="info-value">
                      <span class="tag is-info">{{ log.entityType || 'Unknown' }}</span>
                      <div class="entity-id">
                        <small class="has-text-grey">ID: {{ log.entityId }}</small>
                      </div>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Relative Time</label>
                    <p class="info-value">{{ getRelativeTime(log.timestamp) }}</p>
                  </div>
                </div>
              </div>
              
              <!-- Message -->
              <div class="field">
                <label class="label">Message</label>
                <div class="content">
                  <div class="box has-background-light">
                    <p class="has-text-weight-semibold">{{ log.message }}</p>
                  </div>
                </div>
              </div>

              <!-- User Agent -->
              <div class="field" v-if="log.userAgent">
                <label class="label">User Agent</label>
                <div class="content">
                  <div class="box has-background-light">
                    <p class="is-family-monospace">{{ log.userAgent }}</p>
                  </div>
                </div>
              </div>

              <!-- Additional Data -->
              <div class="field" v-if="log.additionalData">
                <label class="label">Additional Data</label>
                <div class="content">
                  <div class="box has-background-light">
                    <pre v-if="isJsonData" class="json-data">{{ formatJsonData(log.additionalData) }}</pre>
                    <p v-else class="is-family-monospace">{{ log.additionalData }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="column is-4">
          <!-- Quick Actions -->
          <div class="card mb-5">
            <div class="card-header">
              <p class="card-header-title">Quick Actions</p>
            </div>
            <div class="card-content">
              <div class="buttons is-fullwidth">
                <button 
                  class="button is-info is-fullwidth"
                  @click="viewRelatedLogs"
                  :disabled="!log.userId && !log.ipAddress">
                  <span class="icon"><i class="fas fa-search"></i></span>
                  <span>View Related Logs</span>
                </button>
                <button 
                  class="button is-warning is-fullwidth"
                  @click="copyLogData"
                  title="Copy log data to clipboard">
                  <span class="icon"><i class="fas fa-copy"></i></span>
                  <span>Copy Log Data</span>
                </button>
                <button 
                  v-if="log.ipAddress"
                  class="button is-danger is-fullwidth"
                  @click="blockIpAddress"
                  title="Block this IP address">
                  <span class="icon"><i class="fas fa-ban"></i></span>
                  <span>Block IP</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Log Statistics -->
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Context Information</p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Log ID</label>
                <p class="info-value is-family-monospace">{{ log.id }}</p>
              </div>
              <div class="field" v-if="log.ipAddress">
                <label class="label">IP Location</label>
                <p class="info-value">{{ getIpLocation(log.ipAddress) }}</p>
              </div>
              <div class="field" v-if="log.userAgent">
                <label class="label">Browser/Device</label>
                <p class="info-value">{{ parseUserAgent(log.userAgent) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Logs -->
      <div v-if="relatedLogs.length > 0" class="card mt-5">
        <div class="card-header">
          <p class="card-header-title">Related Logs</p>
        </div>
        <div class="card-content">
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Time</th>
                  <th>Level</th>
                  <th>Category</th>
                  <th>Message</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="relatedLog in relatedLogs" :key="relatedLog.id">
                  <td>{{ formatTimestamp(relatedLog.timestamp) }}</td>
                  <td>
                    <span class="tag" :class="`is-${getLogLevelColor(relatedLog.level)}`">
                      {{ relatedLog.level }}
                    </span>
                  </td>
                  <td>{{ relatedLog.category }}</td>
                  <td>{{ formatLogMessage(relatedLog.message, 80) }}</td>
                  <td>
                    <router-link
                      :to="{ name: 'AdminLogDetail', params: { id: relatedLog.id } }"
                      class="button is-small is-info">
                      <span class="icon"><i class="fas fa-eye"></i></span>
                    </router-link>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { securityService } from '@/admin/services/security';

const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const error = ref(null);
const log = ref({});
const relatedLogs = ref([]);

// Computed properties
const isJsonData = computed(() => {
  return log.value.additionalData && securityService.isJsonString(log.value.additionalData);
});

// Methods
const fetchLog = async () => {
  loading.value = true;
  error.value = null;

  try {
    const logId = route.params.id;
    const response = await securityService.getLogById(logId);
    log.value = response;

    // Fetch related logs
    await fetchRelatedLogs();
  } catch (err) {
    error.value = err.message || 'Failed to load log details';
  } finally {
    loading.value = false;
  }
};

const fetchRelatedLogs = async () => {
  try {
    const filters = {};

    // Find related logs by user or IP
    if (log.value.userId) {
      filters.userId = log.value.userId;
    } else if (log.value.ipAddress) {
      filters.ipAddress = log.value.ipAddress;
    }

    if (Object.keys(filters).length > 0) {
      filters.pageSize = 10;
      const response = await securityService.getLogsWithFilters(filters);
      relatedLogs.value = (response.items || response.data || [])
        .filter(l => l.id !== log.value.id)
        .slice(0, 5);
    }
  } catch (err) {
    console.error('Error fetching related logs:', err);
  }
};

const viewRelatedLogs = () => {
  const filters = {};
  if (log.value.userId) {
    filters.userId = log.value.userId;
  } else if (log.value.ipAddress) {
    filters.ipAddress = log.value.ipAddress;
  }

  router.push({
    name: 'AdminSecurity',
    query: filters
  });
};

const copyLogData = async () => {
  try {
    const logData = {
      id: log.value.id,
      timestamp: log.value.timestamp,
      level: log.value.level,
      category: log.value.category,
      message: log.value.message,
      user: log.value.userName || 'System',
      ipAddress: log.value.ipAddress,
      additionalData: log.value.additionalData
    };

    await navigator.clipboard.writeText(JSON.stringify(logData, null, 2));
    // TODO: Show success notification
    console.log('Log data copied to clipboard');
  } catch (err) {
    console.error('Failed to copy log data:', err);
  }
};

const blockIpAddress = async () => {
  if (!log.value.ipAddress) return;

  try {
    const confirmed = confirm(`Are you sure you want to block IP address ${log.value.ipAddress}?`);
    if (!confirmed) return;

    // TODO: Implement IP blocking
    console.log('Blocking IP:', log.value.ipAddress);
  } catch (err) {
    console.error('Failed to block IP:', err);
  }
};

// Utility methods
const getLogLevelColor = (level) => {
  return securityService.getLogLevelColor(level);
};

const getLogLevelIcon = (level) => {
  return securityService.getLogLevelIcon(level);
};

const formatTimestamp = (timestamp) => {
  return securityService.formatTimestamp(timestamp);
};

const getRelativeTime = (timestamp) => {
  return securityService.getRelativeTime(timestamp);
};

const formatLogMessage = (message, maxLength = 100) => {
  return securityService.formatLogMessage(message, maxLength);
};

const formatJsonData = (jsonString) => {
  return securityService.formatJsonData(jsonString);
};

const getIpLocation = (ipAddress) => {
  // TODO: Implement IP geolocation
  return 'Unknown Location';
};

const parseUserAgent = (userAgent) => {
  if (!userAgent) return 'Unknown';

  // Simple user agent parsing
  if (userAgent.includes('Chrome')) return 'Chrome Browser';
  if (userAgent.includes('Firefox')) return 'Firefox Browser';
  if (userAgent.includes('Safari')) return 'Safari Browser';
  if (userAgent.includes('Edge')) return 'Edge Browser';

  return 'Unknown Browser';
};

// Lifecycle
onMounted(() => {
  fetchLog();
});
</script>

<style scoped>
.log-detail {
  padding: 1rem;
}

.info-value {
  font-weight: 500;
  color: #363636;
}

.user-link {
  color: #3273dc;
  text-decoration: none;
}

.user-link:hover {
  text-decoration: underline;
}

.user-email {
  margin-top: 0.25rem;
}

.entity-id {
  margin-top: 0.25rem;
}

.json-data {
  background-color: #f5f5f5;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  padding: 1rem;
  font-size: 0.875rem;
  overflow-x: auto;
  white-space: pre-wrap;
}

.box.has-background-light {
  background-color: #f5f5f5 !important;
}

.is-family-monospace {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.breadcrumb {
  margin-bottom: 0;
}
</style>
