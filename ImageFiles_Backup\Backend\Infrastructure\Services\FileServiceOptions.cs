﻿namespace Marketplace.Infrastructure.Services;

/// <summary>
/// Опції для сервісу файлів
/// </summary>
public class FileServiceOptions
{
    /// <summary>
    /// Кореневий шлях для зберігання файлів
    /// </summary>
    public string StoragePath { get; set; }

    /// <summary>
    /// Базовий URL для доступу до файлів
    /// </summary>
    public string BaseUrl { get; set; }

    /// <summary>
    /// Максимальний розмір файлу в байтах
    /// </summary>
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10 MB за замовчуванням

    /// <summary>
    /// Дозволені типи файлів (MIME-типи)
    /// </summary>
    public string[] AllowedFileTypes { get; set; } = new[]
    {
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp",
        "application/pdf"
    };
}
