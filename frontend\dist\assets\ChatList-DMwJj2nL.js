import{_ as G,g as l,h as M,i as J,c as d,a as e,b as F,s as K,k as x,t as o,n as A,F as X,p as Y,x as Z,W as ee,d as L,w as se,r as te,o as r}from"./index-L-hJxM_5.js";import{c as k}from"./chats-Dp7kITFn.js";import{S as ae}from"./SearchAndFilters-B3kez0yT.js";import{P as le}from"./Pagination-DX2plTiq.js";const ne={class:"chat-list"},oe={class:"level"},ie={class:"level-right"},re={class:"level-item"},ue={class:"buttons"},ce=["disabled"],de={key:0,class:"has-text-centered py-6"},ve={key:1,class:"notification is-danger"},ge={key:2,class:"card"},he={class:"card-content"},me={class:"table is-fullwidth is-hoverable"},pe={class:"checkbox"},fe=["checked","indeterminate"],be={class:"checkbox"},ye=["value"],Ce={class:"participants"},ke={class:"last-message"},_e={class:"message-preview"},we={class:"message-time"},Se={class:"tag is-info"},Me={class:"buttons"},Fe=["onClick","disabled"],xe={__name:"ChatList",setup(Ae){const N=(t,s)=>{let f;return function(...b){const q=()=>{clearTimeout(f),t(...b)};clearTimeout(f),f=setTimeout(q,s)}},u=l([]),h=l(!1),v=l(null),g=l(!1),m=l(""),n=l([]),y=l(!0),p=l({status:"",hasMessages:""}),P=l({status:{type:"select",label:"Status",options:[{value:"active",label:"Active"},{value:"inactive",label:"Inactive"},{value:"moderated",label:"Moderated"},{value:"blocked",label:"Blocked"}]},hasMessages:{type:"select",label:"Messages",options:[{value:"true",label:"Has Messages"},{value:"false",label:"No Messages"}]}}),B=l("createdAt"),D=l("desc"),c=l(1),C=l(1),_=l(0),U=l(15),w=M(()=>u.value.length>0&&n.value.length===u.value.length),V=M(()=>n.value.length>0&&n.value.length<u.value.length),i=async()=>{h.value=!0,v.value=null;try{const t={filter:m.value,status:p.value.status,hasMessages:p.value.hasMessages,orderBy:B.value,descending:D.value==="desc",page:c.value,pageSize:U.value},s=await k.getChats(t);u.value=s.data||[],c.value=s.currentPage||1,C.value=s.totalPages||1,_.value=s.totalItems||0,n.value=[]}catch(t){v.value=t.message||"Failed to load chats",u.value=[]}finally{h.value=!1,y.value=!1}},I=t=>{m.value=t,c.value=1,$()},T=()=>{c.value=1,i()},z=()=>{m.value="",p.value.status="",p.value.hasMessages="",c.value=1,i()},R=t=>{c.value=t,i()},$=N(i,300),E=()=>{w.value?n.value=[]:n.value=u.value.map(t=>t.id)},H=async t=>{if(confirm("Are you sure you want to moderate this chat?")){g.value=!0;try{await k.moderateChat(t,"moderate"),await i()}catch(s){v.value=s.message||"Failed to moderate chat"}finally{g.value=!1}}},O=async()=>{if(confirm(`Are you sure you want to moderate ${n.value.length} selected chats?`)){g.value=!0;try{for(const t of n.value)await k.moderateChat(t,"moderate");await i()}catch(t){v.value=t.message||"Failed to moderate chats"}finally{g.value=!1}}},S=t=>t?new Date(t).toLocaleDateString():"N/A",Q=(t,s)=>t?t.length>s?t.substring(0,s)+"...":t:"No messages",W=t=>(t==null?void 0:t.charAt(0).toUpperCase())+(t==null?void 0:t.slice(1))||"Unknown",j=t=>{switch(t==null?void 0:t.toLowerCase()){case"active":return"is-success";case"inactive":return"is-light";case"moderated":return"is-warning";case"blocked":return"is-danger";default:return"is-light"}};return J(()=>{i()}),(t,s)=>{const f=te("router-link");return r(),d("div",ne,[e("div",oe,[s[3]||(s[3]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Chat Management")])],-1)),e("div",ie,[e("div",re,[e("div",ue,[n.value.length>0?(r(),d("button",{key:0,class:"button is-warning",onClick:O,disabled:n.value.length===0||g.value},[s[2]||(s[2]=e("span",{class:"icon"},[e("i",{class:"fas fa-gavel"})],-1)),e("span",null,"Moderate Selected ("+o(n.value.length)+")",1)],8,ce)):x("",!0)])])])]),F(ae,{search:m.value,"onUpdate:search":s[0]||(s[0]=a=>m.value=a),filters:p.value,"filter-fields":P.value,"search-label":"Search Chats","search-placeholder":"Search by participants or chat content...","search-column-class":"is-4","total-items":_.value,"item-name":"chats",loading:h.value,onSearchChanged:I,onFilterChanged:T,onResetFilters:z},null,8,["search","filters","filter-fields","total-items","loading"]),h.value&&y.value?(r(),d("div",de,s[4]||(s[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading chats...",-1)]))):v.value?(r(),d("div",ve,[e("p",null,o(v.value),1),e("button",{class:"button is-light mt-2",onClick:i},s[5]||(s[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(r(),d("div",ge,[e("div",he,[e("div",{class:A(["table-container",{"is-loading":h.value&&!y.value}])},[e("table",me,[e("thead",null,[e("tr",null,[e("th",null,[e("label",pe,[e("input",{type:"checkbox",onChange:E,checked:w.value,indeterminate:V.value},null,40,fe)])]),s[6]||(s[6]=e("th",null,"Participants",-1)),s[7]||(s[7]=e("th",null,"Last Message",-1)),s[8]||(s[8]=e("th",null,"Messages Count",-1)),s[9]||(s[9]=e("th",null,"Status",-1)),s[10]||(s[10]=e("th",null,"Created",-1)),s[11]||(s[11]=e("th",null,"Actions",-1))])]),e("tbody",null,[(r(!0),d(X,null,Y(u.value,a=>(r(),d("tr",{key:a.id},[e("td",null,[e("label",be,[Z(e("input",{type:"checkbox",value:a.id,"onUpdate:modelValue":s[1]||(s[1]=b=>n.value=b)},null,8,ye),[[ee,n.value]])])]),e("td",null,[e("div",Ce,[e("div",null,[s[12]||(s[12]=e("strong",null,"Buyer:",-1)),L(" "+o(a.buyerName||"Unknown"),1)]),e("div",null,[s[13]||(s[13]=e("strong",null,"Seller:",-1)),L(" "+o(a.sellerName||"Unknown"),1)])])]),e("td",null,[e("div",ke,[e("div",_e,o(Q(a.lastMessage,50)),1),e("div",we,o(S(a.lastMessageAt)),1)])]),e("td",null,[e("span",Se,o(a.messageCount||0),1)]),e("td",null,[e("span",{class:A(["tag",j(a.status)])},o(W(a.status)),3)]),e("td",null,o(S(a.createdAt)),1),e("td",null,[e("div",Me,[F(f,{to:{name:"AdminChatDetail",params:{id:a.id}},class:"button is-small is-info"},{default:se(()=>s[14]||(s[14]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)])),_:2},1032,["to"]),e("button",{class:"button is-small is-warning",onClick:b=>H(a.id),disabled:g.value},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-gavel"})],-1),e("span",null,"Moderate",-1)]),8,Fe)])])]))),128))])])],2)])])),C.value>1?(r(),K(le,{key:3,"current-page":c.value,"total-pages":C.value,onPageChanged:R},null,8,["current-page","total-pages"])):x("",!0)])}}},De=G(xe,[["__scopeId","data-v-a344139f"]]);export{De as default};
