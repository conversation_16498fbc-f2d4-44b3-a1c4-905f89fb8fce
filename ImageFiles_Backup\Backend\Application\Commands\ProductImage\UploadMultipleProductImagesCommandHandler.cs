using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;

namespace Marketplace.Application.Commands.ProductImage;

public class UploadMultipleProductImagesCommandHandler : IRequestHandler<UploadMultipleProductImagesCommand, List<ProductImageResponse>>
{
    private readonly IProductRepository _productRepository;
    private readonly IProductImageRepository _productImageRepository;
    private readonly IFileService _fileService;
    private readonly string _baseUrl;

    public UploadMultipleProductImagesCommandHandler(
        IProductRepository productRepository,
        IProductImageRepository productImageRepository,
        IFileService fileService,
        IConfiguration configuration)
    {
        _productRepository = productRepository;
        _productImageRepository = productImageRepository;
        _fileService = fileService;
        _baseUrl = configuration["FileService:BaseUrl"] ?? "http://localhost:5296";
    }

    public async Task<List<ProductImageResponse>> Handle(UploadMultipleProductImagesCommand request, CancellationToken cancellationToken)
    {
        Console.WriteLine($"Handler: Processing upload for product {request.ProductId}");
        Console.WriteLine($"Handler: Number of files to process: {request.Files?.Count ?? 0}");

        // Перевіряємо, чи продукт існує
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
        {
            throw new InvalidOperationException($"Продукт з ID {request.ProductId} не знайдено.");
        }

        // Отримуємо поточні зображення для визначення порядку
        var existingImages = await _productImageRepository.GetAllAsync(
            filter: pi => pi.ProductId == request.ProductId,
            cancellationToken: cancellationToken);

        var nextOrder = existingImages.Any() ? existingImages.Max(pi => pi.Order) + 1 : 0;
        var hasMainImage = existingImages.Any(pi => pi.IsMain);

        var results = new List<ProductImageResponse>();

        foreach (var file in request.Files)
        {
            if (file == null || file.Length == 0) continue;

            // Завантажуємо файл
            var fileUrl = await _fileService.SaveFileAsync(
                file.OpenReadStream(),
                file.FileName,
                file.ContentType,
                $"images/products/{request.ProductId}",
                cancellationToken);

            var imageUrl = $"{_baseUrl}/uploads/{fileUrl}";

            // Створюємо нове зображення продукту
            var productImage = new Domain.Entities.ProductImage(
                request.ProductId,
                new Url(imageUrl),
                nextOrder,
                !hasMainImage && results.Count == 0, // Перше зображення стає головним, якщо немає головного
                file.FileName
            );

            await _productImageRepository.AddAsync(productImage, cancellationToken);

            results.Add(new ProductImageResponse
            {
                Id = productImage.Id,
                ProductId = productImage.ProductId,
                Image = imageUrl,
                Order = productImage.Order,
                IsMain = productImage.IsMain,
                AltText = productImage.AltText,
                CreatedAt = productImage.CreatedAt
            });

            nextOrder++;
            if (!hasMainImage && results.Count == 1)
            {
                hasMainImage = true; // Перше зображення стало головним
            }
        }

        return results;
    }
}
