<template>
  <div class="company-image-manager">
    <!-- Company Logo -->
    <div class="image-section">
      <h4>Company Logo</h4>
      <div class="image-container">
        <div v-if="currentLogo" class="image-preview">
          <img :src="currentLogo" alt="Company Logo" class="preview-image" />
          <button 
            type="button" 
            @click="removeLogo" 
            class="remove-btn"
            :disabled="disabled">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div v-else class="image-placeholder">
          <i class="fas fa-building"></i>
          <span>No logo uploaded</span>
        </div>
        
        <div class="image-actions">
          <input 
            ref="logoInput"
            type="file" 
            accept="image/*" 
            @change="handleLogoUpload"
            style="display: none"
            :disabled="disabled" />
          <button 
            type="button" 
            @click="$refs.logoInput.click()"
            class="upload-btn"
            :disabled="disabled">
            <i class="fas fa-upload"></i>
            {{ currentLogo ? 'Change Logo' : 'Upload Logo' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Meta Image -->
    <div class="image-section">
      <h4>Meta Image (SEO)</h4>
      <div class="image-container">
        <div v-if="currentMetaImage" class="image-preview">
          <img :src="currentMetaImage" alt="Meta Image" class="preview-image" />
          <button 
            type="button" 
            @click="removeMetaImage" 
            class="remove-btn"
            :disabled="disabled">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div v-else class="image-placeholder">
          <i class="fas fa-image"></i>
          <span>No meta image uploaded</span>
        </div>
        
        <div class="image-actions">
          <input 
            ref="metaImageInput"
            type="file" 
            accept="image/*" 
            @change="handleMetaImageUpload"
            style="display: none"
            :disabled="disabled" />
          <button 
            type="button" 
            @click="$refs.metaImageInput.click()"
            class="upload-btn"
            :disabled="disabled">
            <i class="fas fa-upload"></i>
            {{ currentMetaImage ? 'Change Meta Image' : 'Upload Meta Image' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Pending Changes Info -->
    <div v-if="hasPendingChanges" class="pending-changes">
      <i class="fas fa-info-circle"></i>
      <span>You have unsaved image changes. Click "Save Changes" to apply them.</span>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'CompanyImageManager',
  props: {
    companyId: {
      type: String,
      required: true
    },
    initialLogo: {
      type: String,
      default: null
    },
    initialMetaImage: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['logo-changed', 'meta-image-changed'],
  setup(props, { emit }) {
    // Current images (what's displayed)
    const currentLogo = ref(props.initialLogo);
    const currentMetaImage = ref(props.initialMetaImage);
    
    // Pending changes (what will be applied on save)
    const pendingLogoFile = ref(null);
    const pendingMetaImageFile = ref(null);
    const pendingLogoRemoval = ref(false);
    const pendingMetaImageRemoval = ref(false);
    
    // Computed
    const hasPendingChanges = computed(() => {
      return pendingLogoFile.value || 
             pendingMetaImageFile.value || 
             pendingLogoRemoval.value || 
             pendingMetaImageRemoval.value;
    });

    // Watch for prop changes
    watch(() => props.initialLogo, (newValue) => {
      currentLogo.value = newValue;
      resetPendingChanges();
    });

    watch(() => props.initialMetaImage, (newValue) => {
      currentMetaImage.value = newValue;
      resetPendingChanges();
    });

    // Methods
    const handleLogoUpload = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      currentLogo.value = previewUrl;
      
      // Store pending change
      pendingLogoFile.value = file;
      pendingLogoRemoval.value = false;
      
      // Emit change
      emit('logo-changed', {
        type: 'upload',
        file: file,
        previewUrl: previewUrl
      });
    };

    const handleMetaImageUpload = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      currentMetaImage.value = previewUrl;
      
      // Store pending change
      pendingMetaImageFile.value = file;
      pendingMetaImageRemoval.value = false;
      
      // Emit change
      emit('meta-image-changed', {
        type: 'upload',
        file: file,
        previewUrl: previewUrl
      });
    };

    const removeLogo = () => {
      currentLogo.value = null;
      pendingLogoFile.value = null;
      pendingLogoRemoval.value = true;
      
      emit('logo-changed', {
        type: 'remove'
      });
    };

    const removeMetaImage = () => {
      currentMetaImage.value = null;
      pendingMetaImageFile.value = null;
      pendingMetaImageRemoval.value = true;
      
      emit('meta-image-changed', {
        type: 'remove'
      });
    };

    const resetPendingChanges = () => {
      pendingLogoFile.value = null;
      pendingMetaImageFile.value = null;
      pendingLogoRemoval.value = false;
      pendingMetaImageRemoval.value = false;
    };

    const getPendingChanges = () => {
      return {
        logo: {
          file: pendingLogoFile.value,
          remove: pendingLogoRemoval.value
        },
        metaImage: {
          file: pendingMetaImageFile.value,
          remove: pendingMetaImageRemoval.value
        }
      };
    };

    const reset = () => {
      currentLogo.value = props.initialLogo;
      currentMetaImage.value = props.initialMetaImage;
      resetPendingChanges();
    };

    return {
      currentLogo,
      currentMetaImage,
      hasPendingChanges,
      handleLogoUpload,
      handleMetaImageUpload,
      removeLogo,
      removeMetaImage,
      getPendingChanges,
      reset
    };
  }
};
</script>

<style scoped>
.company-image-manager {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.image-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
}

.image-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.image-container {
  text-align: center;
}

.image-preview {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.preview-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #cc0000;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  background: #f5f5f5;
  border: 2px dashed #ccc;
  border-radius: 4px;
  margin-bottom: 1rem;
  color: #666;
}

.image-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.upload-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.upload-btn:hover {
  background: #0056b3;
}

.upload-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.pending-changes {
  grid-column: 1 / -1;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #856404;
}

.pending-changes i {
  color: #f39c12;
}

@media (max-width: 768px) {
  .company-image-manager {
    grid-template-columns: 1fr;
  }
}
</style>
