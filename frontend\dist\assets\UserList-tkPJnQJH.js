import{_ as ae,g,h as y,u as te,c as r,a as e,k as E,b as f,d as v,w as h,r as le,x as ne,y as ie,z as a,t as i,s as F,F as oe,p as de,n as R,m as re,o as d}from"./index-L-hJxM_5.js";import{u as O}from"./users-D6yG63l9.js";import{u as ce}from"./useAdminSearch-BxXMp5oH.js";import{S as me}from"./SearchAndFilters-B3kez0yT.js";import{P as ue}from"./Pagination-DX2plTiq.js";import{C as ve}from"./ConfirmDialog-hd0r6dWx.js";import{ROLE_DISPLAY_NAMES as c,ROLE_KEYS as n,getRoleDisplayName as T,getRoleClass as fe}from"./roles-D6TbD4pL.js";const he={class:"admin-users"},_e={class:"admin-page-header"},pe={class:"admin-page-actions"},be={class:"admin-user-filters"},ge={class:"admin-filter-group"},ye=["value"],Ee=["value"],Re=["value"],Ae=["value"],ke=["value"],Ue={key:0,class:"admin-loading-state"},Ce={key:1,class:"admin-error-state"},De={class:"admin-error-message"},Le={key:2,class:"admin-empty-state"},Se={class:"admin-empty-message"},xe={key:3,class:"admin-user-table"},we={class:"admin-card-header"},Me={class:"admin-card-actions"},Ne={class:"admin-results-count"},Fe={class:"admin-card-content"},Oe={class:"admin-table-container"},Te={class:"admin-table"},Pe={class:"admin-table-body"},Ie={class:"admin-table-td"},Be={class:"admin-user-avatar-cell"},Ve=["onClick"],$e=["src","alt"],Ye={key:1,class:"fas fa-user avatar-placeholder"},We={class:"admin-table-td"},ze={class:"admin-user-info"},Ke={class:"admin-user-details"},je={class:"admin-user-name"},qe={class:"admin-user-id"},Ge={class:"admin-table-td"},He={class:"admin-email"},Je={class:"admin-table-td"},Qe={class:"admin-table-td"},Xe={class:"admin-status-badges"},Ze={class:"admin-table-td"},es={class:"admin-date"},ss={class:"admin-table-td admin-table-td-actions"},as={class:"admin-table-actions"},ts=["onClick"],ls={class:"modal-content"},ns={class:"modal-header"},is={class:"modal-title"},os={class:"modal-body text-center"},ds={key:0,class:"avatar-viewer"},rs=["src","alt"],cs={class:"mt-3"},ms={class:"text-muted"},us={key:1,class:"no-avatar-message"},vs={__name:"UserList",setup(fs){const U=te(),{items:C,loading:D,error:L,isFirstLoad:P,currentPage:I,totalPages:B,totalItems:V,filters:o,fetchData:A,handlePageChange:$}=ce({fetchFunction:O.getUsers,defaultFilters:{role:""},debounceTime:300,defaultPageSize:10,clientSideSearch:!1}),_=g(!1),u=g(null),k=g(!1),m=g(null),Y=y(()=>[{key:"role",label:"Role",type:"select",options:[{value:"",label:"All Roles"},{value:n.ADMIN,label:c[n.ADMIN]},{value:n.MODERATOR,label:c[n.MODERATOR]},{value:n.SELLER_OWNER,label:c[n.SELLER_OWNER]},{value:n.SELLER,label:c[n.SELLER]},{value:n.BUYER,label:c[n.BUYER]}]}]);y(()=>{const l=[];return o.search&&l.push({key:"search",label:"Search",value:o.search}),o.role&&l.push({key:"role",label:"Role",value:T(o.role)}),l});const S=y(()=>U.getters["auth/isAdmin"]),p=y(()=>U.getters["auth/isModerator"]),W=l=>S.value?!0:p.value&&l.role==="admin"?!1:!!p.value,z=l=>S.value?!0:p.value&&l.role==="admin"?!1:!!p.value,K=()=>{o.search="",o.role=""},j=()=>{},q=l=>{o.search=l},G=(l,s)=>{o[l]=s},H=l=>l?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(l)):"",J=l=>{m.value=l,k.value=!0},x=()=>{k.value=!1,m.value=null},Q=l=>{l.target.style.display="none";const s=l.target.nextElementSibling;s&&(s.style.display="block")},X=l=>{u.value=l,_.value=!0},Z=()=>{_.value=!1,u.value=null},ee=async()=>{if(u.value)try{await O.deleteUser(u.value.id),await A(),_.value=!1,u.value=null}catch(l){console.error("Error deleting user:",l)}};return(l,s)=>{var w,M,N;const b=le("router-link");return d(),r("div",he,[e("div",_e,[s[4]||(s[4]=e("div",{class:"admin-page-title-section"},[e("h1",{class:"admin-page-title"},[e("i",{class:"fas fa-users admin-page-icon"}),v(" Users Management ")]),e("p",{class:"admin-page-subtitle"},"Manage user accounts, roles, and permissions")],-1)),e("div",pe,[f(b,{to:"/admin/users/create",class:"admin-btn admin-btn-primary"},{default:h(()=>s[3]||(s[3]=[e("i",{class:"fas fa-plus"},null,-1),v(" Add User ")])),_:1})])]),e("div",be,[f(me,{search:a(o).search,filters:a(o),"filter-fields":Y.value,loading:a(D),onSearchChanged:q,onFilterChanged:G,onClearFilters:K,onApplyFilters:j},{"custom-filters":h(()=>[e("div",ge,[s[6]||(s[6]=e("label",{class:"admin-filter-label"},"Role",-1)),ne(e("select",{"onUpdate:modelValue":s[0]||(s[0]=t=>a(o).role=t),class:"admin-filter-select"},[s[5]||(s[5]=e("option",{value:""},"All Roles",-1)),e("option",{value:a(n).ADMIN},i(a(c)[a(n).ADMIN]),9,ye),e("option",{value:a(n).MODERATOR},i(a(c)[a(n).MODERATOR]),9,Ee),e("option",{value:a(n).SELLER},i(a(c)[a(n).SELLER]),9,Re),e("option",{value:a(n).SELLER_OWNER},i(a(c)[a(n).SELLER_OWNER]),9,Ae),e("option",{value:a(n).BUYER},i(a(c)[a(n).BUYER]),9,ke)],512),[[ie,a(o).role]])])]),_:1},8,["search","filters","filter-fields","loading"])]),a(D)&&a(P)?(d(),r("div",Ue,s[7]||(s[7]=[e("div",{class:"admin-spinner"},[e("i",{class:"fas fa-spinner fa-pulse"})],-1),e("p",{class:"admin-loading-text"},"Loading users...",-1)]))):a(L)?(d(),r("div",Ce,[s[9]||(s[9]=e("div",{class:"admin-error-icon"},[e("i",{class:"fas fa-exclamation-triangle"})],-1)),s[10]||(s[10]=e("h3",{class:"admin-error-title"},"Error Loading Users",-1)),e("p",De,i(a(L)),1),e("button",{onClick:s[1]||(s[1]=(...t)=>a(A)&&a(A)(...t)),class:"admin-btn admin-btn-primary"},s[8]||(s[8]=[e("i",{class:"fas fa-refresh"},null,-1),v(" Try Again ")]))])):a(C).length?(d(),r("div",xe,[e("div",we,[s[14]||(s[14]=e("h3",{class:"admin-card-title"},[e("i",{class:"fas fa-table"}),v(" Users List ")],-1)),e("div",Me,[e("span",Ne,i(a(V))+" users found",1)])]),e("div",Fe,[e("div",Oe,[e("table",Te,[s[18]||(s[18]=e("thead",{class:"admin-table-header"},[e("tr",null,[e("th",{class:"admin-table-th"},"Avatar"),e("th",{class:"admin-table-th"},"Username"),e("th",{class:"admin-table-th"},"Email"),e("th",{class:"admin-table-th"},"Role"),e("th",{class:"admin-table-th"},"Status"),e("th",{class:"admin-table-th"},"Registered"),e("th",{class:"admin-table-th admin-table-th-actions"},"Actions")])],-1)),e("tbody",Pe,[(d(!0),r(oe,null,de(a(C),t=>(d(),r("tr",{key:t.id,class:"admin-table-row"},[e("td",Ie,[e("div",Be,[e("div",{class:R(["admin-user-avatar",{"has-image":t.avatarUrl}]),onClick:se=>J(t)},[t.avatarUrl?(d(),r("img",{key:0,src:t.avatarUrl,alt:`${t.username} avatar`,class:"avatar-image",onError:Q},null,40,$e)):(d(),r("i",Ye))],10,Ve)])]),e("td",We,[e("div",ze,[e("div",Ke,[e("div",je,i(t.username),1),e("div",qe,"ID: "+i(t.id.substring(0,8))+"...",1)])])]),e("td",Ge,[e("div",He,i(t.email),1)]),e("td",Je,[e("span",{class:R(["admin-badge",a(fe)(t.role)])},i(a(T)(t.role)),3)]),e("td",Qe,[e("div",Xe,[e("span",{class:R(["admin-badge admin-badge-xs",t.emailConfirmed?"admin-badge-success":"admin-badge-warning"])},i(t.emailConfirmed?"Email ✓":"Email ✗"),3),e("span",{class:R(["admin-badge admin-badge-xs",t.isApproved?"admin-badge-success":"admin-badge-warning"])},i(t.isApproved?"Approved ✓":"Pending ✗"),3)])]),e("td",Ze,[e("div",es,i(H(t.createdAt||t.emailConfirmedAt)),1)]),e("td",ss,[e("div",as,[f(b,{to:`/admin/users/${t.id}`,class:"admin-btn admin-btn-sm admin-btn-secondary",title:"View Details"},{default:h(()=>s[15]||(s[15]=[e("i",{class:"fas fa-eye"},null,-1)])),_:2},1032,["to"]),z(t)?(d(),F(b,{key:0,to:`/admin/users/${t.id}/edit`,class:"admin-btn admin-btn-sm admin-btn-primary",title:"Edit User"},{default:h(()=>s[16]||(s[16]=[e("i",{class:"fas fa-edit"},null,-1)])),_:2},1032,["to"])):E("",!0),W(t)?(d(),r("button",{key:1,class:"admin-btn admin-btn-sm admin-btn-danger",onClick:se=>X(t),title:"Delete User"},s[17]||(s[17]=[e("i",{class:"fas fa-trash"},null,-1)]),8,ts)):E("",!0)])])]))),128))])])])]),f(ue,{"current-page":a(I),"total-pages":a(B),onPageChanged:a($)},null,8,["current-page","total-pages","onPageChanged"])])):(d(),r("div",Le,[s[12]||(s[12]=e("div",{class:"admin-empty-icon"},[e("i",{class:"fas fa-users"})],-1)),s[13]||(s[13]=e("h3",{class:"admin-empty-title"},"No Users Found",-1)),e("p",Se,i(a(o).search||a(o).role?"No users match your current filters.":"No users have been created yet."),1),!a(o).search&&!a(o).role?(d(),F(b,{key:0,to:"/admin/users/create",class:"admin-btn admin-btn-primary"},{default:h(()=>s[11]||(s[11]=[e("i",{class:"fas fa-plus"},null,-1),v(" Add First User ")])),_:1})):E("",!0)])),k.value?(d(),r("div",{key:4,class:"modal fade show d-block",onClick:x},[e("div",{class:"modal-dialog modal-lg",onClick:s[2]||(s[2]=re(()=>{},["stop"]))},[e("div",ls,[e("div",ns,[e("h5",is,[s[19]||(s[19]=e("i",{class:"fas fa-user me-2"},null,-1)),v(" "+i((w=m.value)==null?void 0:w.username)+" - Avatar ",1)]),e("button",{type:"button",class:"btn-close",onClick:x})]),e("div",os,[(M=m.value)!=null&&M.avatarUrl?(d(),r("div",ds,[e("img",{src:m.value.avatarUrl,alt:`${m.value.username} avatar`,class:"img-fluid rounded",style:{"max-height":"400px"}},null,8,rs),e("div",cs,[e("small",ms," User: "+i(m.value.username)+" ("+i(m.value.email)+") ",1)])])):(d(),r("div",us,s[20]||(s[20]=[e("i",{class:"fas fa-user-circle fa-5x text-muted mb-3"},null,-1),e("p",{class:"text-muted"},"This user doesn't have an avatar",-1)])))])])])])):E("",!0),f(ve,{"is-open":_.value,title:"Delete User",message:`Are you sure you want to delete ${(N=u.value)==null?void 0:N.username}? This action cannot be undone.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:ee,onCancel:Z},null,8,["is-open","message"])])}}},Rs=ae(vs,[["__scopeId","data-v-fe51acb1"]]);export{Rs as default};
