<template>
  <div class="category-menu-Button">
    <button class="catalog-btn" @click="toggleMenu">
        <img src="@/assets/images/icons/catalog-icon.svg" alt="Klondike" class="catalog-pic"/>
        Каталог
    </button>
    
    <category-menu :is-open="isMenuOpen" @close="closeMenu" />
  </div>
</template>

<script>
import { ref } from 'vue';
import CategoryMenu from './CategoryMenu.vue';

export default {
  name: 'CategoryMenuButton',
  components: {
    CategoryMenu
  },
  setup() {
    const isMenuOpen = ref(false);
    
    const toggleMenu = () => {
      isMenuOpen.value = !isMenuOpen.value;
    };
    
    const closeMenu = () => {
      isMenuOpen.value = false;
    };
    
    return {
      isMenuOpen,
      toggleMenu,
      closeMenu
    };
  }
};
</script>

<style scoped>
.catalog-pic {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  margin-left: -7px;
}

.category-menu-Button {
  display: inline-block;
}

.catalog-btn {
  height: 40px;
  background-color: white;
  color: black;
  border: solid black 2px;
  border-radius: 7px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.catalog-btn:hover {
  background-color: #ABAAAA;
}

.catalog-btn i {
  margin-right: 8px;
}
</style>
