/* Admin Layout Fix - Resolves CSS Framework Conflicts */
/* This file fixes the layout issues in the admin panel by overriding Bootstrap/Bulma conflicts */

/* ===== ADMIN LAYOUT OVERRIDE ===== */
/* Force proper layout structure for admin panel */

.admin-layout {
  min-height: 100vh !important;
  background-color: #f8f9fa !important;
}

/* Fix Bulma columns to work properly in admin context */
.admin-layout .columns {
  display: flex !important;
  margin: 0 !important;
  padding: 0 !important;
  min-height: 100vh !important;
  flex-wrap: nowrap !important;
}

.admin-layout .column {
  padding: 0 !important;
  margin: 0 !important;
}

.admin-layout .column.is-one-quarter {
  flex: none !important;
  width: 470px !important;
  min-width: 470px !important;
  max-width: 470px !important;
  position: relative !important;
}

.admin-layout .column.is-three-quarters {
  flex: 1 !important;
  width: calc(100% - 470px) !important;
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar-column {
  background: #2c3e50 !important;
  position: relative !important;
  z-index: 1000 !important;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-container {
  height: 100vh !important;
  min-height: 100vh !important;
  background: #2c3e50 !important;
  padding: 1rem !important;
  position: relative !important;
  border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
  overflow-y: hidden !important;
}

.sidebar-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 2rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.logo-container {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.sidebar-container .logo-container .logo {
  height: 32px !important;
  width: auto !important;
  filter: brightness(0) invert(1) !important;
}

.logo-container .title {
  color: white !important;
  margin: 0 !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
}

/* Sidebar Navigation */
.sidebar .panel {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

.sidebar .panel-block {
  background: transparent !important;
  border: none !important;
  color: #ecf0f1 !important;
  padding: 0.75rem 1rem !important;
  margin-bottom: 0.25rem !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  text-decoration: none !important;
  cursor: pointer !important;
  border-left: 3px solid transparent !important;
}

.sidebar .panel-block:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  transform: translateX(5px) !important;
  border-left-color: rgba(255, 255, 255, 0.3) !important;
}

.sidebar .panel-block.is-active {
  background: #3498db !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3) !important;
  border-left-color: #2980b9 !important;
}

.sidebar .panel-icon {
  margin-right: 0.75rem !important;
  width: 20px !important;
  text-align: center !important;
  font-size: 1rem !important;
}

.sidebar .menu-label {
  color: #bdc3c7 !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  font-size: 0.75rem !important;
  letter-spacing: 0.1em !important;
  margin-top: 1.5rem !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1rem !important;
}

.sidebar .menu-label:first-of-type {
  margin-top: 0 !important;
}

/* ===== MAIN CONTENT AREA ===== */
.admin-app .main-content,
.admin-layout .main-content {
  display: flex !important;
  flex-direction: column !important;
  background-color: #f8f9fa !important;
  min-height: 100vh !important;
  position: relative !important;
  width: 100% !important;
}

/* ===== HEADER STYLES ===== */
.admin-header {
  background: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  padding: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1100 !important;
  width: 100% !important;
  flex-shrink: 0 !important;
}

.admin-header .navbar {
  padding: 0.5rem 1.5rem !important;
  min-height: 60px !important;
  margin: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.admin-header .navbar-brand .title {
  color: #2c3e50 !important;
  margin: 0 !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
}

/* ===== NAVBAR MENU STYLES ===== */
.admin-header .navbar-menu {
  display: flex !important;
  align-items: center !important;
  background: transparent !important;
  box-shadow: none !important;
}

.admin-header .navbar-end {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
}

.admin-header .navbar-item {
  padding: 0.5rem 0.75rem !important;
  color: #2c3e50 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  font-size: 0.95rem !important;
  font-weight: 500 !important;
}

.admin-header .navbar-item:hover {
  background-color: rgba(44, 62, 80, 0.1) !important;
  color: #2c3e50 !important;
}

.admin-header .navbar-item .icon {
  font-size: 1.1rem !important;
}

/* ===== NAVBAR DROPDOWN FIXES ===== */
.admin-header .navbar-item.has-dropdown {
  position: relative !important;
}

.admin-header .navbar-link {
  padding: 0.5rem 0.75rem !important;
  color: #2c3e50 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  font-size: 0.95rem !important;
  font-weight: 500 !important;
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
}

.admin-header .navbar-link:hover {
  background-color: rgba(44, 62, 80, 0.1) !important;
  color: #2c3e50 !important;
}

.admin-header .navbar-link .icon {
  font-size: 1.1rem !important;
}

.admin-header .navbar-dropdown {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  min-width: 200px !important;
  z-index: 1150 !important;
  display: none !important;
}

.admin-header .navbar-item.has-dropdown.is-active .navbar-dropdown {
  display: block !important;
}

.admin-header .navbar-dropdown .navbar-item {
  padding: 0.75rem 1rem !important;
  color: #333 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  transition: background-color 0.2s ease !important;
}

.admin-header .navbar-dropdown .navbar-item:hover {
  background-color: #f5f5f5 !important;
  color: #ff7700 !important;
}

.admin-header .navbar-dropdown .navbar-divider {
  margin: 0.5rem 0 !important;
  border-color: #e0e0e0 !important;
}

/* Notification badge */
.admin-header .notification-badge {
  position: absolute !important;
  top: -5px !important;
  right: -5px !important;
  background-color: #ff3860 !important;
  color: white !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  font-size: 0.7rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
}

/* Notifications dropdown specific styles */
.admin-header .notifications-dropdown {
  min-width: 300px !important;
}

.admin-header .notifications-header {
  padding: 1rem !important;
  border-bottom: 1px solid #e0e0e0 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.admin-header .notifications-list {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.admin-header .notification-item {
  padding: 1rem !important;
  border-bottom: 1px solid #f0f0f0 !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 0.75rem !important;
}

.admin-header .notification-item.is-unread {
  background-color: #f8f9ff !important;
}

.admin-header .notification-content {
  flex: 1 !important;
}

.admin-header .notification-text {
  margin: 0 0 0.25rem 0 !important;
  font-size: 0.875rem !important;
  line-height: 1.4 !important;
}

.admin-header .notification-time {
  margin: 0 !important;
  font-size: 0.75rem !important;
  color: #666 !important;
}

.admin-header .notifications-empty {
  padding: 2rem !important;
  text-align: center !important;
  color: #666 !important;
}

.sidebar-toggle {
  background: transparent !important;
  border: none !important;
  color: #2c3e50 !important;
  font-size: 1.25rem !important;
  cursor: pointer !important;
  padding: 0.5rem !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  display: none !important;
}

.sidebar-toggle:hover {
  background: rgba(44, 62, 80, 0.1) !important;
}

/* ===== PAGE CONTAINER ===== */
.admin-page-container {
  padding: 0 !important;
  max-width: none !important;
  flex: 1 !important;
  background: #f8f9fa !important;
  width: 100% !important;
}

.admin-page-container .section {
  padding: 0 !important;
}

/* Remove default section padding in admin layout */
.admin-layout .section {
  padding: 0 !important;
  margin: 0 !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 1023px) {
  .admin-layout .column.is-one-quarter {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 470px !important;
    height: 100vh !important;
    z-index: 1001 !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease !important;
  }
  
  .admin-layout .column.is-one-quarter:not(.is-hidden-mobile) {
    transform: translateX(0) !important;
  }
  
  .admin-layout .column.is-three-quarters {
    width: 100% !important;
    margin-left: 0 !important;
  }
  
  .sidebar-toggle {
    display: block !important;
  }
  
  .sidebar-close {
    position: absolute !important;
    top: 1rem !important;
    right: 1rem !important;
    background: transparent !important;
    border: none !important;
    color: white !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    z-index: 1002 !important;
    padding: 0.5rem !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
  }
  
  .sidebar-close:hover {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}

/* ===== MOBILE OVERLAY ===== */
@media screen and (max-width: 1023px) {
  .admin-layout .column.is-one-quarter:not(.is-hidden-mobile)::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 1000 !important;
  }
}

/* ===== UTILITY CLASSES ===== */
.is-hidden-mobile {
  display: none !important;
}

@media screen and (min-width: 1024px) {
  .is-hidden-desktop {
    display: none !important;
  }
}

@media screen and (max-width: 1023px) {
  .is-hidden-mobile {
    display: block !important;
  }
}

/* ===== ADDITIONAL FIXES ===== */
/* Ensure proper box-sizing for all admin elements */
.admin-layout *,
.admin-layout *::before,
.admin-layout *::after {
  box-sizing: border-box !important;
}

/* Fix any potential overflow issues */
.admin-layout {
  overflow-x: hidden !important;
}

/* Ensure sidebar doesn't cause horizontal scroll */
.admin-layout .columns {
  overflow: hidden !important;
}

/* Fix for Bulma's default margins */
.admin-layout .columns:not(.is-desktop) {
  display: flex !important;
}

/* Ensure proper height for mobile */
@media screen and (max-width: 1023px) {
  .admin-layout {
    min-height: 100vh !important;
    height: 100vh !important;
    overflow: hidden !important;
  }
}
