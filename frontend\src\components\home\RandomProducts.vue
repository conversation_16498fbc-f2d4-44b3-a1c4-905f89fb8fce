<template>
  <section class="random-products-section">
    <div class="container">
      <h2 class="section-title">{{ title }}</h2>
      <ProductGrid
        :fetch-params="fetchParams"
        :grid-columns="gridColumns"
        :empty-message="emptyMessage"
        @product-added-to-cart="$emit('product-added-to-cart', $event)"
        @product-added-to-wishlist="$emit('product-added-to-wishlist', $event)"
      />
    </div>
  </section>
</template>

<script>
import ProductGrid from '@/components/common/ProductGrid.vue';

export default {
  name: 'RandomProducts',
  components: {
    ProductGrid
  },
  props: {
    title: {
      type: String,
      default: 'Випадкові товари'
    },
    categorySlug: {
      type: String,
      required: true
    },
    count: {
      type: Number,
      default: 6
    },
    status: {
      type: Number,
      default: 1
    },
    gridColumns: {
      type: Number,
      default: 3
    },
    emptyMessage: {
      type: String,
      default: 'Рандомні товари поки що недоступні'
    }
  },
  computed: {
    fetchParams() {
      return {
        categorySlug: this.categorySlug,
        status: this.status,
        count: this.count,
        random: true
      };
    }
  }
}
</script>

<style scoped>
.random-products-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #000;
  text-align: left;
}
</style>
