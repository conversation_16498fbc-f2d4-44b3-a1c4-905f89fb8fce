const R={BUYER:0,SELLER:1,SELLER_OWNER:2,MODERATOR:3,ADMIN:4},e={BUYER:"Buyer",SELLER:"Seller",SELLER_OWNER:"SellerOwner",MODERATOR:"Moderator",ADMIN:"Admin"},s={[e.BUYER]:"Buyer",[e.SELLER]:"Seller",[e.SELLER_OWNER]:"Seller Owner",[e.MODERATOR]:"Moderator",[e.ADMIN]:"Admin",unknown:"Unknown"},i={[R.BUYER]:e.BUYER,[R.SELLER]:e.SELLER,[R.SELLER_OWNER]:e.SELLER_OWNER,[R.MODERATOR]:e.MODERATOR,[R.ADMIN]:e.ADMIN},O={[e.BUYER]:R.BUYER,[e.SELLER]:<PERSON><PERSON><PERSON>LL<PERSON>,[e.SELLER_OWNER]:<PERSON><PERSON>_<PERSON>WNER,[e.MODERATOR]:<PERSON><PERSON>,[e.ADMIN]:<PERSON>.ADMIN},S={buyer:e.BUYER,seller:e.SELLER,sellerowner:e.SELLER_OWNER,moderator:e.MODERATOR,admin:e.ADMIN};e.ADMIN+"",e.MODERATOR+"",e.SELLER_OWNER+"",e.SELLER+"",e.BUYER+"";e.ADMIN+"",e.MODERATOR+"",e.SELLER+"",e.SELLER_OWNER+"",e.BUYER+"";function r(E){if(E==null)return"unknown";if(typeof E=="number")return i[E]||"unknown";if(typeof E=="string"){if(Object.values(e).includes(E))return E;const n=E.toLowerCase(),t=S[n];if(t)return t;const L=Object.keys(s).find(o=>s[o].toLowerCase()===n);return L||"unknown"}if(typeof E=="object"){if(E.hasOwnProperty("name"))return r(E.name);if(E.hasOwnProperty("value"))return r(E.value)}return"unknown"}function A(E){const n=r(E);return O[n]!==void 0?O[n]:-1}function a(E){const n=r(E);return s[n]||"Unknown"}const M=E=>r(E),N=E=>{switch(r(E)){case e.ADMIN:return"is-admin";case e.MODERATOR:return"is-moderator";case e.SELLER_OWNER:return"is-sellerowner";case e.SELLER:return"is-seller";case e.BUYER:return"is-buyer";default:return"is-buyer"}},u={[e.ADMIN]:0,[e.MODERATOR]:1,[e.SELLER_OWNER]:2,[e.SELLER]:3,[e.BUYER]:4},D=(E,n)=>{const t=r(E),L=r(n),o=u[t]??999,c=u[L]??999;return o-c};export{s as ROLE_DISPLAY_NAMES,e as ROLE_KEYS,O as ROLE_KEY_TO_NUMBER,S as ROLE_LOWERCASE_TO_BACKEND,i as ROLE_NUMBER_TO_KEY,R as ROLE_VALUES,D as compareRoles,N as getRoleClass,a as getRoleDisplayName,M as getRoleForBackend,r as getRoleKey,A as getRoleValue};
