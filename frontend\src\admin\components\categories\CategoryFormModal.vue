<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">{{ category ? 'Edit Category' : 'Add Category' }}</p>
        <button class="delete" aria-label="close" @click="$emit('close')"></button>
      </header>
      <section class="modal-card-body">
        <form @submit.prevent="submitForm">
          <!-- Basic Info -->
          <div class="field">
            <label class="label">Name</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                placeholder="Category name" 
                v-model="form.name"
                @input="generateSlug"
                required>
            </div>
          </div>
          
          <div class="field">
            <label class="label">Slug</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                placeholder="category-slug" 
                v-model="form.slug"
                required>
            </div>
            <p class="help">URL-friendly version of the name. Auto-generated but can be edited.</p>
          </div>
          
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea 
                class="textarea" 
                placeholder="Category description" 
                v-model="form.description"
                rows="3"></textarea>
            </div>
          </div>
          
          <!-- Parent Category -->
          <div class="field">
            <label class="label">Parent Category</label>
            <div class="control">
              <div class="dropdown" :class="{ 'is-active': isParentDropdownOpen }">
                <div class="dropdown-trigger">
                  <div class="field has-addons">
                    <div class="control is-expanded">
                      <input
                        class="input"
                        type="text"
                        placeholder="Search for parent category (leave empty for top level)..."
                        v-model="parentSearchQuery"
                        @input="onParentSearchInput"
                        @focus="openParentDropdown"
                        @blur="onParentBlur"
                      />
                    </div>
                    <div class="control">
                      <button
                        class="button"
                        type="button"
                        @click="toggleParentDropdown"
                      >
                        <span class="icon">
                          <i class="fas fa-chevron-down" :class="{ 'fa-rotate-180': isParentDropdownOpen }"></i>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="dropdown-menu" role="menu">
                  <div class="dropdown-content">
                    <a
                      class="dropdown-item"
                      :class="{ 'is-active': !form.parentId }"
                      @mousedown.prevent="selectParentCategory(null)"
                    >
                      <div class="category-item">
                        <div class="category-name">None (Top Level)</div>
                        <div class="category-slug has-text-grey is-size-7">Root category</div>
                      </div>
                    </a>
                    <a
                      v-for="category in filteredParentCategories"
                      :key="category.id"
                      class="dropdown-item"
                      :class="{ 'is-active': form.parentId === category.id }"
                      @mousedown.prevent="selectParentCategory(category)"
                    >
                      <div class="category-item">
                        <div class="category-name">{{ category.name }}</div>
                        <div class="category-slug has-text-grey is-size-7">{{ category.slug }}</div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <p v-if="selectedParentCategory" class="help">
              Selected: {{ selectedParentCategory.name }}
            </p>
          </div>
          
          <!-- Image Upload -->
          <div class="field">
            <label class="label">Category Image</label>

            <!-- Current Image Preview -->
            <div v-if="form.imageUrl" class="mb-3">
              <figure class="image is-128x128">
                <img :src="form.imageUrl" :alt="form.name" class="is-rounded">
              </figure>
              <button
                type="button"
                class="button is-small is-danger mt-2"
                @click="removeImage">
                <span class="icon">
                  <i class="fas fa-trash"></i>
                </span>
                <span>Remove Image</span>
              </button>
            </div>

            <!-- Upload Options -->
            <div class="tabs is-small">
              <ul>
                <li :class="{ 'is-active': uploadMethod === 'file' }">
                  <a @click="uploadMethod = 'file'">Upload File</a>
                </li>
                <li :class="{ 'is-active': uploadMethod === 'url' }">
                  <a @click="uploadMethod = 'url'">Image URL</a>
                </li>
              </ul>
            </div>

            <!-- File Upload -->
            <div v-if="uploadMethod === 'file'" class="field">
              <div class="file has-name is-fullwidth">
                <label class="file-label">
                  <input
                    class="file-input"
                    type="file"
                    accept="image/*"
                    @change="handleFileUpload"
                    ref="fileInput">
                  <span class="file-cta">
                    <span class="icon">
                      <i class="fas fa-upload"></i>
                    </span>
                    <span class="file-label">Choose image...</span>
                  </span>
                  <span class="file-name" v-if="selectedFile">
                    {{ selectedFile.name }}
                  </span>
                </label>
              </div>
              <p class="help">Supported formats: JPG, PNG, GIF. Max size: 5MB</p>
            </div>

            <!-- URL Input -->
            <div v-if="uploadMethod === 'url'" class="field">
              <div class="control">
                <input
                  class="input"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  v-model="form.imageUrl">
              </div>
              <p class="help">Enter a direct link to an image</p>
            </div>
          </div>
          
          <!-- Display Order -->
          <div class="field">
            <label class="label">Display Order</label>
            <div class="control">
              <input
                class="input"
                type="number"
                min="0"
                placeholder="0"
                v-model.number="form.displayOrder">
            </div>
            <p class="help">Categories with lower numbers will be displayed first.</p>
          </div>

          <!-- SEO Section -->
          <div class="field">
            <label class="label">
              <span class="icon-text">
                <span class="icon">
                  <i class="fas fa-search"></i>
                </span>
                <span>SEO Settings</span>
              </span>
            </label>
          </div>

          <div class="field">
            <label class="label">Meta Title</label>
            <div class="control">
              <input
                class="input"
                type="text"
                placeholder="SEO title for search engines"
                v-model="form.metaTitle"
                maxlength="60">
            </div>
            <p class="help">
              <span :class="{ 'has-text-danger': form.metaTitle && form.metaTitle.length > 60 }">
                {{ form.metaTitle ? form.metaTitle.length : 0 }}/60 characters
              </span>
            </p>
          </div>

          <div class="field">
            <label class="label">Meta Description</label>
            <div class="control">
              <textarea
                class="textarea"
                placeholder="SEO description for search engines"
                v-model="form.metaDescription"
                maxlength="160"
                rows="3"></textarea>
            </div>
            <p class="help">
              <span :class="{ 'has-text-danger': form.metaDescription && form.metaDescription.length > 160 }">
                {{ form.metaDescription ? form.metaDescription.length : 0 }}/160 characters
              </span>
            </p>
          </div>
        </form>
      </section>
      <footer class="modal-card-foot">
        <button class="button is-primary" @click="submitForm" :disabled="isSubmitting">
          <span v-if="isSubmitting">
            <span class="icon">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span>Saving...</span>
          </span>
          <span v-else>Save</span>
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  category: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['close', 'save']);

// Form state
const form = reactive({
  id: null,
  name: '',
  slug: '',
  description: '',
  parentId: '',
  imageUrl: '',
  displayOrder: 0,
  metaTitle: '',
  metaDescription: ''
});

// Upload state
const uploadMethod = ref('file');
const selectedFile = ref(null);
const fileInput = ref(null);

// Parent category dropdown state
const isParentDropdownOpen = ref(false);
const parentSearchQuery = ref('');
const selectedParentCategory = ref(null);

// Submission state
const isSubmitting = ref(false);

// Available parent categories (exclude self to prevent circular references)
const availableParentCategories = computed(() => {
  return props.categories.filter(cat => cat.id !== form.id);
});

// Filtered parent categories for dropdown
const filteredParentCategories = computed(() => {
  const available = availableParentCategories.value;

  if (!parentSearchQuery.value.trim()) {
    return available;
  }

  const query = parentSearchQuery.value.toLowerCase().trim();
  return available.filter(category => {
    return (
      category.name.toLowerCase().includes(query) ||
      category.slug.toLowerCase().includes(query)
    );
  });
});

// Generate slug from name
const generateSlug = () => {
  if (!form.name) return;
  
  // Only auto-generate if user hasn't manually edited the slug
  if (!form.slug || form.slug === slugify(props.category?.name || '')) {
    form.slug = slugify(form.name);
  }
};

// Slugify text
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')    // Remove all non-word characters
    .replace(/\-\-+/g, '-')      // Replace multiple - with single -
    .replace(/^-+/, '')          // Trim - from start of text
    .replace(/-+$/, '');         // Trim - from end of text
};

// Handle file upload
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // Validate file type
  if (!file.type.startsWith('image/')) {
    alert('Please select an image file');
    return;
  }

  // Validate file size (5MB max)
  if (file.size > 5 * 1024 * 1024) {
    alert('File size must be less than 5MB');
    return;
  }

  selectedFile.value = file;

  // Create preview URL
  const reader = new FileReader();
  reader.onload = (e) => {
    form.imageUrl = e.target.result;
  };
  reader.readAsDataURL(file);
};

// Remove image
const removeImage = () => {
  form.imageUrl = '';
  selectedFile.value = null;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// Parent category dropdown methods
const selectParentCategory = (category) => {
  if (category) {
    selectedParentCategory.value = category;
    form.parentId = category.id;
    parentSearchQuery.value = category.name;
  } else {
    selectedParentCategory.value = null;
    form.parentId = '';
    parentSearchQuery.value = '';
  }
  isParentDropdownOpen.value = false;
};

const openParentDropdown = () => {
  isParentDropdownOpen.value = true;
};

const toggleParentDropdown = () => {
  if (!isParentDropdownOpen.value) {
    if (selectedParentCategory.value && parentSearchQuery.value === selectedParentCategory.value.name) {
      parentSearchQuery.value = '';
    }
    openParentDropdown();
  } else {
    isParentDropdownOpen.value = false;
  }
};

const onParentSearchInput = () => {
  if (!isParentDropdownOpen.value) {
    isParentDropdownOpen.value = true;
  }
};

const onParentBlur = () => {
  setTimeout(() => {
    isParentDropdownOpen.value = false;

    if (!selectedParentCategory.value) {
      parentSearchQuery.value = '';
    } else {
      parentSearchQuery.value = selectedParentCategory.value.name;
    }
  }, 200);
};

// Submit form
const submitForm = async () => {
  isSubmitting.value = true;
  
  try {
    // Create a clean form object
    const categoryData = { ...form };
    
    // Convert string numbers to actual numbers
    categoryData.displayOrder = parseInt(categoryData.displayOrder);
    
    // If parentId is empty string, set to null
    if (categoryData.parentId === '') {
      categoryData.parentId = null;
    }
    
    emit('save', categoryData);
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form
const resetForm = () => {
  form.id = null;
  form.name = '';
  form.slug = '';
  form.description = '';
  form.parentId = '';
  form.imageUrl = '';
  form.displayOrder = 0;
  form.metaTitle = '';
  form.metaDescription = '';

  // Reset upload state
  selectedFile.value = null;
  uploadMethod.value = 'file';
  if (fileInput.value) {
    fileInput.value.value = '';
  }

  // Reset parent category dropdown state
  selectedParentCategory.value = null;
  parentSearchQuery.value = '';
  isParentDropdownOpen.value = false;
};

// Watch for category changes to update form
watch(() => props.category, (newCategory) => {
  if (newCategory) {
    // Populate form with category data
    Object.keys(form).forEach(key => {
      if (key in newCategory) {
        form[key] = newCategory[key];
      }
    });

    // Set parent category if exists
    if (newCategory.parentId && props.categories) {
      const parentCategory = props.categories.find(cat => cat.id === newCategory.parentId);
      if (parentCategory) {
        selectedParentCategory.value = parentCategory;
        parentSearchQuery.value = parentCategory.name;
      }
    }
  } else {
    resetForm();
  }
}, { immediate: true });

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>

<style scoped>
.modal-card {
  width: 80%;
  max-width: 600px;
}

.dropdown {
  width: 100%;
}

.dropdown-menu {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.category-item {
  padding: 0.25rem 0;
}

.category-name {
  font-weight: 500;
}

.category-slug {
  margin-top: 0.125rem;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item.is-active {
  background-color: #3273dc;
  color: white;
}

.dropdown-item.is-active .category-slug {
  color: #e8e8e8;
}

.fa-rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}
</style>
