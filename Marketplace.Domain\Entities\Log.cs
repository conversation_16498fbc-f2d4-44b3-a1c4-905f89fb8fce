namespace Marketplace.Domain.Entities;

public class Log : IEntity
{
    private string _message;
    private string _category;
    private string? _additionalData;

    public Guid Id { get; set; }
    public DateTime Timestamp { get; set; }
    public LogLevel Level { get; set; }
    
    public string Category
    {
        get => _category;
        set => UpdateCategory(value);
    }
    
    public string Message
    {
        get => _message;
        set => UpdateMessage(value);
    }
    
    public Guid? UserId { get; set; }
    public User? User { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    
    public string? AdditionalData
    {
        get => _additionalData;
        set => UpdateAdditionalData(value);
    }
    
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }

    private Log() { }

    public Log(
        LogLevel level,
        string category,
        string message,
        Guid? userId = null,
        Guid? entityId = null,
        string? entityType = null,
        string? additionalData = null,
        string? ipAddress = null,
        string? userAgent = null
    )
    {
        ValidateCreation(category, message);

        Id = Guid.NewGuid();
        Timestamp = DateTime.UtcNow;
        Level = level;
        Category = category;
        Message = message;
        UserId = userId;
        EntityId = entityId;
        EntityType = entityType;
        AdditionalData = additionalData;
        IpAddress = ipAddress;
        UserAgent = userAgent;
    }

    private void UpdateCategory(string category)
    {
        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Log category cannot be empty.", nameof(category));
        if (category.Length > 50)
            throw new ArgumentException("Log category cannot exceed 50 characters.", nameof(category));
        _category = category.Trim();
    }

    private void UpdateMessage(string message)
    {
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Log message cannot be empty.", nameof(message));
        if (message.Length > 2000)
            throw new ArgumentException("Log message cannot exceed 2000 characters.", nameof(message));
        _message = message.Trim();
    }

    private void UpdateAdditionalData(string? additionalData)
    {
        if (additionalData != null && additionalData.Length > 4000)
            throw new ArgumentException("Additional data cannot exceed 4000 characters.", nameof(additionalData));
        _additionalData = additionalData?.Trim();
    }

    private void ValidateCreation(string category, string message)
    {
        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Log category cannot be empty.", nameof(category));
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Log message cannot be empty.", nameof(message));
    }
}

public enum LogLevel
{
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3
}

public static class LogCategories
{
    public const string User = "User";
    public const string Product = "Product";
    public const string Order = "Order";
    public const string Payment = "Payment";
    public const string Authentication = "Authentication";
    public const string Authorization = "Authorization";
    public const string System = "System";
    public const string Database = "Database";
    public const string Api = "Api";
    public const string Security = "Security";
}
