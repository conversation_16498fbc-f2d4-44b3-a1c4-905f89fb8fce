<template>
  <div class="order-detail-new">
    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="loading-text">Loading order details...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="error-title">Error Loading Order</h3>
      <p class="error-message">{{ error }}</p>
      <button class="retry-btn" @click="fetchOrder(route.params.id)">
        <i class="fas fa-redo"></i>
        Try Again
      </button>
    </div>

    <!-- Order Not Found -->
    <div v-else-if="!order" class="not-found-state">
      <div class="not-found-icon">
        <i class="fas fa-shopping-cart"></i>
      </div>
      <h3 class="not-found-title">Order Not Found</h3>
      <p class="not-found-message">The requested order does not exist or has been deleted.</p>
      <router-link to="/admin/orders" class="back-btn">
        <i class="fas fa-arrow-left"></i>
        Back to Orders
      </router-link>
    </div>

    <!-- Order Content -->
    <div v-else class="order-content">
      <!-- Header -->
      <div class="order-header">
        <div class="header-content">
          <nav class="breadcrumb">
            <router-link to="/admin" class="breadcrumb-item">Dashboard</router-link>
            <span class="breadcrumb-separator">/</span>
            <router-link to="/admin/orders" class="breadcrumb-item">Orders</router-link>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item breadcrumb-current">Order #{{ order.id }}</span>
          </nav>
          <h1 class="order-title">Order #{{ order.id }}</h1>
          <p class="order-subtitle">{{ formatDate(order.createdAt) }}</p>
        </div>
        <div class="header-actions">
          <button class="action-btn action-btn-secondary" @click="printOrder">
            <i class="fas fa-print"></i>
            Print
          </button>
          <button class="action-btn action-btn-info" @click.prevent="openStatusModal">
            <i class="fas fa-cog"></i>
            Update Status
          </button>
          <router-link :to="`/admin/orders/${order.id}/edit`" class="action-btn action-btn-primary">
            <i class="fas fa-edit"></i>
            Edit Order
          </router-link>
        </div>
      </div>

      <!-- Order Information Section -->
      <div class="info-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-info-circle"></i>
            Order Information
          </h2>
        </div>
        
        <div class="info-grid">
          <!-- Basic Info Card -->
          <div class="info-card info-card-main">
            <div class="info-card-header">
              <h3 class="info-card-title">Order Details</h3>
            </div>
            <div class="info-card-content">
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Order ID</label>
                  <span class="info-value">{{ order.id }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">Customer</label>
                  <span class="info-value info-value-link" @click="viewCustomer(order.customerId)">
                    {{ customerName }}
                  </span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Total Price</label>
                  <span class="info-value info-value-price">{{ formatCurrency(order.totalPriceAmount || order.totalPrice?.amount || 0) }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">Status</label>
                  <span class="status-badge" :class="getStatusClass(order.status)">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Payment Status</label>
                  <span class="status-badge" :class="getPaymentStatusClass(order.paymentStatus)">
                    {{ getPaymentStatusText(order.paymentStatus) || 'Pending' }}
                  </span>
                </div>
                <div class="info-item">
                  <label class="info-label">Created At</label>
                  <span class="info-value">{{ formatDateTime(order.createdAt) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Shipping Info Card -->
          <div class="info-card">
            <div class="info-card-header">
              <h3 class="info-card-title">
                <i class="fas fa-shipping-fast"></i>
                Shipping Information
              </h3>
            </div>
            <div class="info-card-content">
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Address Line</label>
                  <span class="info-value">{{ order.shippingAddress?.address1 || order.shippingAddressLine || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">Country</label>
                  <span class="info-value">{{ order.shippingAddress?.country || order.shippingCountry || 'N/A' }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">City</label>
                  <span class="info-value">{{ order.shippingAddress?.city || order.shippingCity || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">Shipping Method</label>
                  <span class="info-value">{{ order.shippingMethodName || 'N/A' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Customer Info Card -->
          <div class="info-card">
            <div class="info-card-header">
              <h3 class="info-card-title">
                <i class="fas fa-user"></i>
                Customer Information
              </h3>
            </div>
            <div class="info-card-content">
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Customer Name</label>
                  <span class="info-value info-value-link" @click="viewCustomer(order.customerId)">
                    {{ customerName }}
                  </span>
                </div>
                <div class="info-item">
                  <label class="info-label">Email</label>
                  <span class="info-value">{{ order.customerEmail || 'N/A' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Payment Info Card -->
          <div class="info-card">
            <div class="info-card-header">
              <h3 class="info-card-title">
                <i class="fas fa-credit-card"></i>
                Payment Information
              </h3>
            </div>
            <div class="info-card-content">
              <div class="info-row">
                <div class="info-item">
                  <label class="info-label">Payment Method</label>
                  <span class="info-value">{{ order.paymentMethodText || 'N/A' }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">Payment Status</label>
                  <span class="status-badge" :class="getPaymentStatusClass(order.paymentStatus)">
                    {{ getPaymentStatusText(order.paymentStatus) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Items Section -->
      <div class="items-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-box"></i>
            Order Items ({{ orderItems.length }})
          </h2>
          <div class="section-actions">
            <button 
              class="action-btn action-btn-secondary"
              @click="refreshOrderItems"
              :disabled="loadingItems">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loadingItems }"></i>
              Refresh
            </button>
          </div>
        </div>

        <!-- Items Loading -->
        <div v-if="loadingItems" class="items-loading">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="loading-text">Loading order items...</p>
        </div>

        <!-- No Items -->
        <div v-else-if="orderItems.length === 0" class="no-items">
          <div class="no-items-icon">
            <i class="fas fa-box-open"></i>
          </div>
          <h3 class="no-items-title">No Items Found</h3>
          <p class="no-items-message">This order doesn't have any items.</p>
        </div>

        <!-- Items Table -->
        <div v-else class="items-table-wrapper">
          <div class="table-header">
            <div class="table-info">
              <span class="table-count">{{ orderItems.length }} items in this order</span>
              <span class="table-total">Total: {{ formatCurrency(calculateTotal()) }}</span>
            </div>
          </div>

          <div class="items-table">
            <table class="table">
              <thead>
                <tr>
                  <th>Product</th>
                  <th>Unit Price</th>
                  <th>Quantity</th>
                  <th>Total Price</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in orderItems" :key="item.id" class="table-row">
                  <td class="product-cell">
                    <div class="product-info">
                      <div class="product-image" v-if="item.productImage">
                        <img :src="item.productImage" :alt="item.productName" @error="handleProductImageError">
                      </div>
                      <div class="product-details">
                        <h4 class="product-name">{{ item.productName || 'Unknown Product' }}</h4>
                        <p class="product-id">Product ID: {{ item.productId }}</p>
                      </div>
                    </div>
                  </td>
                  <td class="price-cell">
                    <span class="price-tag">{{ formatCurrency(item.price || item.priceAmount || 0) }}</span>
                  </td>
                  <td class="quantity-cell">
                    <span class="quantity-badge">{{ item.quantity }}</span>
                  </td>
                  <td class="total-cell">
                    <span class="total-price">{{ formatCurrency(item.total || (item.price * item.quantity) || 0) }}</span>
                  </td>
                  <td class="actions-cell">
                    <div class="action-buttons">
                      <router-link
                        :to="`/admin/products/${item.productId}/view`"
                        class="action-btn action-btn-sm action-btn-info"
                        title="View Product">
                        <i class="fas fa-eye"></i>
                      </router-link>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Order Total -->
          <div class="order-total-section">
            <div class="order-total-card">
              <div class="total-row">
                <span class="total-label">Order Total:</span>
                <span class="total-amount">{{ formatCurrency(order.totalPriceAmount || order.totalPrice?.amount || 0) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes Section -->
      <div class="notes-section" v-if="orderNotes.length > 0 || showAddNote">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-sticky-note"></i>
            Order Notes
          </h2>
          <div class="section-actions">
            <button
              class="action-btn action-btn-primary"
              @click="showAddNote = !showAddNote">
              <i class="fas fa-plus"></i>
              Add Note
            </button>
          </div>
        </div>

        <!-- Add Note Form -->
        <div v-if="showAddNote" class="add-note-form">
          <div class="form-field">
            <label class="form-label">Add Note</label>
            <textarea
              v-model="newNote"
              class="form-textarea"
              placeholder="Enter your note here..."
              rows="3">
            </textarea>
          </div>
          <div class="form-actions">
            <button
              class="action-btn action-btn-primary"
              @click="addNote"
              :disabled="!newNote.trim() || addingNote">
              <i class="fas fa-spinner fa-spin" v-if="addingNote"></i>
              <i class="fas fa-save" v-else></i>
              {{ addingNote ? 'Adding...' : 'Add Note' }}
            </button>
            <button
              class="action-btn action-btn-secondary"
              @click="showAddNote = false; newNote = ''">
              Cancel
            </button>
          </div>
        </div>

        <!-- Notes List -->
        <div v-if="orderNotes.length > 0" class="notes-list">
          <div v-for="note in orderNotes" :key="note.id" class="note-item">
            <div class="note-header">
              <span class="note-author">{{ note.authorName || 'Admin' }}</span>
              <span class="note-date">{{ formatDateTime(note.createdAt) }}</span>
            </div>
            <div class="note-content">{{ note.content || note.note }}</div>
          </div>
        </div>

        <!-- No Notes -->
        <div v-else-if="!showAddNote" class="no-notes">
          <div class="no-notes-icon">
            <i class="fas fa-sticky-note"></i>
          </div>
          <p class="no-notes-text">No notes available for this order.</p>
        </div>
      </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal" :class="{ 'modal-active': showStatusModal }">
      <div class="modal-backdrop" @click="closeStatusModal"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Update Order Status</h3>
          <button class="modal-close" @click="closeStatusModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-field">
            <label class="form-label">Order Status</label>
            <select v-model="statusForm.status" class="form-select">
              <option value="processing">Processing</option>
              <option value="pending">Pending</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div class="form-field">
            <label class="form-label">Payment Status</label>
            <select v-model="statusForm.paymentStatus" class="form-select">
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="action-btn action-btn-primary"
            @click="updateOrderStatus"
            :disabled="updatingStatus"
            :class="{ 'action-btn-loading': updatingStatus }">
            <i class="fas fa-spinner fa-spin" v-if="updatingStatus"></i>
            <i class="fas fa-save" v-else></i>
            {{ updatingStatus ? 'Updating...' : 'Update Status' }}
          </button>
          <button class="action-btn action-btn-secondary" @click="closeStatusModal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ordersService } from '@/admin/services/orders';
import {
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass
} from '@/admin/utils/orderConstants';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const loadingItems = ref(false);
const loadingNotes = ref(false);
const orderNotes = ref([]);
const showAddNote = ref(false);
const newNote = ref('');
const addingNote = ref(false);
const error = ref('');
const order = ref(null);
const orderItems = ref([]);
const showStatusModal = ref(false);
const updatingStatus = ref(false);

// Status form
const statusForm = ref({
  status: '',
  paymentStatus: ''
});

// Computed properties
const customerName = computed(() => {
  return order.value?.customerName || order.value?.customer?.name || 'Unknown Customer';
});

// Methods
const fetchOrder = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    const orderData = await ordersService.getById(id);
    order.value = orderData;

    // Initialize status form - convert numeric status to string
    statusForm.value.status = getOrderStatusText(orderData.status).toLowerCase() || 'pending';
    statusForm.value.paymentStatus = getPaymentStatusText(orderData.paymentStatus).toLowerCase() || 'pending';

    // Fetch order items and notes
    await Promise.all([
      fetchOrderItems(orderData.id),
      fetchOrderNotes(orderData.id)
    ]);

  } catch (err) {
    console.error('Error fetching order:', err);
    if (err.name !== 'CanceledError' && err.code !== 'ERR_CANCELED') {
      error.value = 'Failed to load order. Please try again.';
    }
  } finally {
    loading.value = false;
  }
};

const fetchOrderItems = async (orderId) => {
  loadingItems.value = true;
  try {
    const response = await ordersService.getOrderItems(orderId);
    orderItems.value = response.data || response || [];
  } catch (err) {
    console.error('Error fetching order items:', err);
    orderItems.value = [];
  } finally {
    loadingItems.value = false;
  }
};

const refreshOrderItems = () => {
  if (order.value?.id) {
    fetchOrderItems(order.value.id);
  }
};

// Fetch order notes
const fetchOrderNotes = async (orderId) => {
  if (!orderId) return;

  loadingNotes.value = true;
  try {
    const response = await ordersService.getOrderNotes(orderId);
    if (response && response.data) {
      orderNotes.value = Array.isArray(response.data) ? response.data : [];
    } else {
      orderNotes.value = [];
    }
  } catch (err) {
    console.error('Error fetching order notes:', err);
    orderNotes.value = [];
  } finally {
    loadingNotes.value = false;
  }
};

// Add new note
const addNote = async () => {
  if (!newNote.value.trim() || !order.value?.id) return;

  addingNote.value = true;
  try {
    await ordersService.addOrderNote(order.value.id, newNote.value.trim());
    newNote.value = '';
    showAddNote.value = false;
    // Refresh notes
    await fetchOrderNotes(order.value.id);
  } catch (err) {
    console.error('Error adding note:', err);
    error.value = 'Failed to add note: ' + (err.message || 'Unknown error');
  } finally {
    addingNote.value = false;
  }
};

// Utility methods
const formatCurrency = (amount) => {
  if (amount == null) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(amount);
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('uk-UA');
};

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString('uk-UA');
};

const calculateTotal = () => {
  return orderItems.value.reduce((total, item) => total + (item.totalPrice || 0), 0);
};

const getStatusClass = (status) => {
  if (!status || typeof status !== 'string') return 'status-badge-light';

  switch (status.toLowerCase()) {
    case 'delivered':
    case 'completed':
      return 'status-badge-success';
    case 'cancelled':
    case 'failed':
      return 'status-badge-danger';
    case 'processing':
    case 'shipped':
      return 'status-badge-info';
    case 'pending':
    case 'confirmed':
      return 'status-badge-warning';
    default:
      return 'status-badge-light';
  }
};



// Image error handlers
const handleProductImageError = (event) => {
  event.target.style.display = 'none';
};

// Navigation handlers
const viewCustomer = (customerId) => {
  if (customerId) {
    router.push(`/admin/customers/${customerId}`);
  }
};

const printOrder = () => {
  window.print();
};

// Status modal handlers
const openStatusModal = () => {
  console.log('Opening status modal...');
  // Initialize form with current values
  statusForm.value.status = getOrderStatusText(order.value?.status).toLowerCase() || 'processing';
  statusForm.value.paymentStatus = getPaymentStatusText(order.value?.paymentStatus).toLowerCase() || 'pending';
  showStatusModal.value = true;
  console.log('showStatusModal.value:', showStatusModal.value);
  console.log('statusForm.value:', statusForm.value);
};

const closeStatusModal = () => {
  showStatusModal.value = false;
  // Reset form to current values - convert numeric to string
  statusForm.value.status = getOrderStatusText(order.value?.status).toLowerCase() || 'processing';
  statusForm.value.paymentStatus = getPaymentStatusText(order.value?.paymentStatus).toLowerCase() || 'pending';
};

const updateOrderStatus = async () => {
  if (!order.value?.id) return;

  updatingStatus.value = true;
  try {
    await ordersService.updateOrderStatus(order.value.id, {
      status: statusForm.value.status,
      paymentStatus: statusForm.value.paymentStatus
    });

    // Update local order data
    order.value.status = statusForm.value.status;
    order.value.paymentStatus = statusForm.value.paymentStatus;

    closeStatusModal();

    // Show success message
    alert('Order status updated successfully!');

  } catch (err) {
    console.error('Error updating order status:', err);
    alert('Failed to update order status. Please try again.');
  } finally {
    updatingStatus.value = false;
  }
};

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchOrder(newId);
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  const orderId = route.params.id;
  if (orderId) {
    fetchOrder(orderId);
  }
});
</script>

<style scoped>
/* Base Styles */
.order-detail-new {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* Loading States */
.loading-state,
.items-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1rem;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Not Found State */
.not-found-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.not-found-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.not-found-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.not-found-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.back-btn {
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

/* Header */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

.breadcrumb-current {
  color: #1f2937;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d1d5db;
}

.order-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.order-subtitle {
  color: #6b7280;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-secondary {
  background: #6b7280;
  color: white;
}

.action-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-info {
  background: #0ea5e9;
  color: white;
}

.action-btn-info:hover {
  background: #0284c7;
  transform: translateY(-1px);
}

.action-btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}

.action-btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Sections */
.info-section,
.items-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title i {
  color: #6b7280;
}

.section-actions {
  display: flex;
  gap: 1rem;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.info-card-main {
  grid-column: 1 / -1;
}

.info-card-header {
  background: #f8fafc;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.info-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-card-content {
  padding: 1.5rem;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.info-value {
  font-size: 1rem;
  color: #1f2937;
}

.info-value-link {
  color: #3b82f6;
  cursor: pointer;
  font-weight: 500;
}

.info-value-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

.info-value-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #059669;
}

/* Status Badges */
.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge-success {
  background: #10b981;
  color: white;
}

.status-badge-warning {
  background: #f59e0b;
  color: white;
}

.status-badge-danger {
  background: #ef4444;
  color: white;
}

.status-badge-info {
  background: #3b82f6;
  color: white;
}

.status-badge-light {
  background: #f3f4f6;
  color: #6b7280;
}

/* No Items State */
.no-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.no-items-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.no-items-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.no-items-message {
  color: #6b7280;
}

/* Items Table */
.items-table-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.table-count {
  font-size: 0.875rem;
  color: #6b7280;
}

.table-total {
  font-size: 1rem;
  font-weight: 600;
  color: #059669;
}

.items-table {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f8fafc;
  padding: 1rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
}

.table td {
  padding: 1rem 1.5rem;
  vertical-align: middle;
}

/* Product Cell */
.product-cell {
  min-width: 250px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.product-id {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Price Cell */
.price-tag {
  background: #3b82f6;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Quantity Cell */
.quantity-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Total Cell */
.total-price {
  font-size: 1rem;
  font-weight: 600;
  color: #059669;
}

/* Actions Cell */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-active {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.modal-active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

/* Form Elements */
.form-field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-select {
  width: 100%;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Order Total Section */
.order-total-section {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.order-total-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  min-width: 200px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.total-label {
  font-weight: 600;
  color: #374151;
  font-size: 1.1rem;
}

.total-amount {
  font-weight: 700;
  color: #059669;
  font-size: 1.25rem;
}

/* Notes Section */
.notes-section {
  margin-top: 2rem;
}

.add-note-form {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.note-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  transition: box-shadow 0.2s ease;
}

.note-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.note-author {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.note-date {
  color: #6b7280;
  font-size: 0.75rem;
}

.note-content {
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
}

.no-notes {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-notes-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-notes-text {
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .order-detail-new {
    padding: 1.5rem;
  }

  .order-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-card-main {
    grid-column: 1;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .table-info {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .order-detail-new {
    padding: 1rem;
  }

  .order-header {
    padding: 1.5rem;
  }

  .order-title {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    justify-content: center;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .section-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .info-card-content {
    padding: 1rem;
  }

  .table-header {
    padding: 1rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 1rem;
  }

  .product-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .product-image {
    width: 40px;
    height: 40px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn-sm {
    padding: 0.5rem;
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    flex-wrap: wrap;
  }

  .order-title {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .items-table {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.75rem;
  }

  .product-name {
    font-size: 0.875rem;
  }

  .product-id {
    font-size: 0.75rem;
  }
}

/* Print Styles */
@media print {
  .order-detail-new {
    padding: 0;
    background: white;
  }

  .header-actions,
  .section-actions,
  .action-buttons,
  .modal {
    display: none !important;
  }

  .order-header,
  .info-card,
  .items-table-wrapper {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }

  .breadcrumb {
    display: none;
  }
}
</style>
