import api from '@/services/api';

export const shippingMethodsService = {
  async getShippingMethods(params = {}) {
    try {
      const response = await api.get('/api/admin/shipping-methods', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching shipping methods:', error);
      throw new Error(error.response?.data?.message || 'Failed to load shipping methods');
    }
  },

  async getShippingMethodById(id) {
    try {
      const response = await api.get(`/api/admin/shipping-methods/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching shipping method:', error);
      throw new Error(error.response?.data?.message || 'Failed to load shipping method details');
    }
  },

  async createShippingMethod(data) {
    try {
      const response = await api.post('/api/admin/shipping-methods', data);
      return response.data;
    } catch (error) {
      console.error('Error creating shipping method:', error);
      throw new Error(error.response?.data?.message || 'Failed to create shipping method');
    }
  },

  async updateShippingMethod(id, data) {
    try {
      const response = await api.put(`/api/admin/shipping-methods/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating shipping method:', error);
      throw new Error(error.response?.data?.message || 'Failed to update shipping method');
    }
  },

  async deleteShippingMethod(id) {
    try {
      const response = await api.delete(`/api/admin/shipping-methods/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting shipping method:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete shipping method');
    }
  }
};
