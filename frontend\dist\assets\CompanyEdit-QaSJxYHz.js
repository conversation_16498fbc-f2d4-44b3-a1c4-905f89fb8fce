import{_ as z,g as d,C as S,i as G,c as v,a,k as E,b,s as H,w as F,d as c,r as J,t as U,n as P,x as o,D as i,W as Q,m as Y,f as Z,e as aa,o as g,q as _}from"./index-L-hJxM_5.js";import{c as h}from"./companies-B97mkaMy.js";import{E as R}from"./EntityImageManager-DFnfDtsC.js";import"./image.service-DOD4lHqw.js";const ea={class:"admin-company-edit"},na={class:"admin-page-header"},sa={class:"admin-header-content"},la={class:"admin-header-left"},oa={class:"admin-breadcrumb"},ia={class:"admin-header-right"},ta=["disabled"],da=["disabled"],ma={key:0,class:"admin-loading-state"},ra={key:1,class:"admin-alert admin-alert-danger"},ca={class:"admin-alert-content"},pa={class:"admin-alert-message"},ua={key:2,class:"admin-company-content"},fa={class:"admin-card admin-form-section"},ga={class:"admin-card-content"},ya={class:"admin-form-grid"},va={class:"admin-form-group"},ba={class:"admin-form-group"},Ua={class:"admin-form-group"},Ca={class:"admin-form-group admin-form-group-full"},Ia={class:"admin-form-group"},ka={class:"admin-form-checkbox"},xa={class:"admin-card admin-form-section"},Ma={class:"admin-card-content"},Da={class:"admin-form-grid"},Fa={class:"admin-form-group"},wa={class:"admin-form-group"},Va={class:"admin-form-group"},Sa={class:"admin-form-group admin-form-group-full"},Ea={class:"admin-card admin-form-section"},Pa={class:"admin-card-content"},_a={class:"admin-form-grid"},ha={class:"admin-form-group"},Ra={class:"admin-form-group"},Na={class:"admin-card admin-form-section"},Ba={class:"admin-card-content"},Aa={class:"admin-form-grid"},Xa={class:"admin-form-group"},Oa={class:"admin-form-group"},La={class:"admin-form-group admin-form-group-full"},$a={class:"admin-form-group"},Wa={class:"admin-form-group admin-form-group-full"},ja={class:"admin-form-actions"},qa=["disabled"],Ta=["disabled"],Ka={__name:"CompanyEdit",setup(za){const p=Z(),N=aa(),m=d({}),C=d(!1),t=d(!1),r=d(null),s=S({company:{name:"",description:"",contactEmail:"",contactPhone:"",addressRegion:"",addressCity:"",addressStreet:"",addressPostalCode:"",imageUrl:"",metaImageUrl:"",isFeatured:!1},finance:{bankName:"",bankAccount:"",bankCode:"",taxId:"",paymentDetails:""}}),u=d(null),f=d(null),I=d(""),k=d(""),x=d(null),M=d(null),y=S({company:{},finance:{}}),B=async()=>{C.value=!0,r.value=null;try{const n=await h.getDetailedCompany(p.params.id);m.value=n.data,A(n.data),X(n.data)}catch(n){r.value=n.message||"Failed to load company data"}finally{C.value=!1}},A=n=>{Object.assign(s.company,{name:n.name||"",description:n.description||"",contactEmail:n.contactEmail||"",contactPhone:n.contactPhone||"",addressRegion:n.addressRegion||"",addressCity:n.addressCity||"",addressStreet:n.addressStreet||"",addressPostalCode:n.addressPostalCode||"",imageUrl:n.imageUrl||"",metaImageUrl:n.metaImage||n.metaImageUrl||"",isFeatured:n.isFeatured||!1}),I.value=n.imageUrl||"",k.value=n.metaImage||n.metaImageUrl||"",n.finance&&Object.assign(s.finance,{bankName:n.finance.bankName||"",bankAccount:n.finance.bankAccount||"",bankCode:n.finance.bankCode||"",taxId:n.finance.taxId||"",paymentDetails:n.finance.paymentDetails||""})},X=n=>{y.company={...s.company},y.finance={...s.finance}},w=()=>{Object.assign(s.company,y.company),Object.assign(s.finance,y.finance),u.value=null,f.value=null},O=n=>{console.log("Logo image uploaded:",n),s.company.imageUrl=n.imageUrl||n.url},L=n=>{n&&n.isLocal?(u.value=n.file,s.company.imageUrl=n.previewUrl):n||(u.value=null,s.company.imageUrl="")},$=async n=>{try{n&&n.imageUrl&&!n.imageUrl.startsWith("data:")&&await T(p.params.id,n.imageUrl),u.value=null,s.company.imageUrl="",I.value=""}catch(e){console.error("Failed to remove image:",e),r.value="Failed to remove image: "+(e.message||"Unknown error")}},W=n=>{console.log("Meta image uploaded:",n),s.company.metaImageUrl=n.imageUrl||n.url},j=n=>{n&&n.isLocal?(f.value=n.file,s.company.metaImageUrl=n.previewUrl):n||(f.value=null,s.company.metaImageUrl="")},q=async n=>{try{n&&n.imageUrl&&!n.imageUrl.startsWith("data:")&&await K(p.params.id),f.value=null,s.company.metaImageUrl="",k.value=""}catch(e){console.error("Failed to remove meta image:",e),r.value="Failed to remove meta image: "+(e.message||"Unknown error")}},T=async(n,e)=>{await _.delete(`/api/admin/companies/${n}/images/current`)},K=async n=>{await _.delete(`/api/admin/companies/${n}/meta-image`)},V=async()=>{t.value=!0,r.value=null;try{const n={...s.company};n.imageUrl&&n.imageUrl.startsWith("data:")&&(n.imageUrl=I.value),n.metaImageUrl&&n.metaImageUrl.startsWith("data:")&&(n.metaImageUrl=k.value),n.imageUrl===""&&(n.imageUrl=null),n.metaImageUrl===""&&(n.metaImageUrl=null);const e={company:{...n,metaImageUrl:n.metaImageUrl},finance:s.finance};await h.updateDetailedCompany(p.params.id,e),x.value&&u.value&&await x.value.uploadPendingFile(),M.value&&f.value&&await M.value.uploadPendingFile(),N.push(`/admin/companies/${p.params.id}`)}catch(n){r.value=n.message||"Failed to update company"}finally{t.value=!1}};return G(()=>{B()}),(n,e)=>{const D=J("router-link");return g(),v("div",ea,[a("div",na,[a("div",sa,[a("div",la,[a("nav",oa,[b(D,{to:"/admin/companies",class:"admin-breadcrumb-item"},{default:F(()=>e[15]||(e[15]=[a("i",{class:"fas fa-building"},null,-1),c(" Companies ")])),_:1}),e[16]||(e[16]=a("span",{class:"admin-breadcrumb-separator"},"/",-1)),m.value.id?(g(),H(D,{key:0,to:`/admin/companies/${m.value.id}`,class:"admin-breadcrumb-item"},{default:F(()=>[c(U(m.value.name||"Company Details"),1)]),_:1},8,["to"])):E("",!0),e[17]||(e[17]=a("span",{class:"admin-breadcrumb-separator"},"/",-1)),e[18]||(e[18]=a("span",{class:"admin-breadcrumb-item admin-breadcrumb-current"}," Edit ",-1))]),e[19]||(e[19]=a("h1",{class:"admin-page-title"},[a("i",{class:"fas fa-edit"}),c(" Edit Company ")],-1)),e[20]||(e[20]=a("p",{class:"admin-page-subtitle"}," Update company information, financial details, and settings ",-1))]),a("div",ia,[a("button",{class:"admin-btn admin-btn-secondary",onClick:w,disabled:t.value},e[21]||(e[21]=[a("i",{class:"fas fa-undo"},null,-1),a("span",null,"Reset",-1)]),8,ta),a("button",{class:"admin-btn admin-btn-primary",onClick:V,disabled:t.value},[a("i",{class:P(["fas fa-save",{"fa-spin":t.value}])},null,2),a("span",null,U(t.value?"Saving...":"Save Changes"),1)],8,da)])])]),C.value&&!m.value.id?(g(),v("div",ma,e[22]||(e[22]=[a("div",{class:"admin-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-loading-text"},"Loading company data...",-1)]))):r.value?(g(),v("div",ra,[e[24]||(e[24]=a("div",{class:"admin-alert-icon"},[a("i",{class:"fas fa-exclamation-circle"})],-1)),a("div",ca,[a("div",pa,U(r.value),1)]),a("button",{class:"admin-alert-close",onClick:e[0]||(e[0]=l=>r.value=null)},e[23]||(e[23]=[a("i",{class:"fas fa-times"},null,-1)]))])):m.value.id?(g(),v("div",ua,[a("form",{onSubmit:Y(V,["prevent"]),class:"admin-company-form"},[a("div",fa,[e[31]||(e[31]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-building admin-card-icon"}),c(" Basic Information ")]),a("p",{class:"admin-card-subtitle"},"Company name, description, and contact details")],-1)),a("div",ga,[a("div",ya,[a("div",va,[e[25]||(e[25]=a("label",{class:"admin-form-label"}," Company Name * ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=l=>s.company.name=l),class:"admin-form-control",placeholder:"Enter company name",required:""},null,512),[[i,s.company.name]])]),a("div",ba,[e[26]||(e[26]=a("label",{class:"admin-form-label"}," Contact Email * ",-1)),o(a("input",{type:"email","onUpdate:modelValue":e[2]||(e[2]=l=>s.company.contactEmail=l),class:"admin-form-control",placeholder:"<EMAIL>",required:""},null,512),[[i,s.company.contactEmail]])]),a("div",Ua,[e[27]||(e[27]=a("label",{class:"admin-form-label"}," Contact Phone ",-1)),o(a("input",{type:"tel","onUpdate:modelValue":e[3]||(e[3]=l=>s.company.contactPhone=l),class:"admin-form-control",placeholder:"+380 XX XXX XXXX"},null,512),[[i,s.company.contactPhone]])]),a("div",Ca,[e[28]||(e[28]=a("label",{class:"admin-form-label"}," Description ",-1)),o(a("textarea",{"onUpdate:modelValue":e[4]||(e[4]=l=>s.company.description=l),class:"admin-form-control",rows:"4",placeholder:"Describe the company..."},null,512),[[i,s.company.description]])]),a("div",Ia,[e[30]||(e[30]=a("label",{class:"admin-form-label"}," Featured Company ",-1)),a("div",ka,[o(a("input",{type:"checkbox",id:"featured","onUpdate:modelValue":e[5]||(e[5]=l=>s.company.isFeatured=l),class:"admin-checkbox"},null,512),[[Q,s.company.isFeatured]]),e[29]||(e[29]=a("label",{for:"featured",class:"admin-checkbox-label"}," Mark as featured company ",-1))])])])])]),a("div",xa,[e[36]||(e[36]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-map-marker-alt admin-card-icon"}),c(" Address Information ")]),a("p",{class:"admin-card-subtitle"},"Company location and address details")],-1)),a("div",Ma,[a("div",Da,[a("div",Fa,[e[32]||(e[32]=a("label",{class:"admin-form-label"}," Region ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[6]||(e[6]=l=>s.company.addressRegion=l),class:"admin-form-control",placeholder:"e.g., Kyiv Oblast"},null,512),[[i,s.company.addressRegion]])]),a("div",wa,[e[33]||(e[33]=a("label",{class:"admin-form-label"}," City ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[7]||(e[7]=l=>s.company.addressCity=l),class:"admin-form-control",placeholder:"e.g., Kyiv"},null,512),[[i,s.company.addressCity]])]),a("div",Va,[e[34]||(e[34]=a("label",{class:"admin-form-label"}," Postal Code ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[8]||(e[8]=l=>s.company.addressPostalCode=l),class:"admin-form-control",placeholder:"e.g., 01001"},null,512),[[i,s.company.addressPostalCode]])]),a("div",Sa,[e[35]||(e[35]=a("label",{class:"admin-form-label"}," Street Address ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[9]||(e[9]=l=>s.company.addressStreet=l),class:"admin-form-control",placeholder:"e.g., 123 Main Street, Building A"},null,512),[[i,s.company.addressStreet]])])])])]),a("div",Ea,[e[39]||(e[39]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-images admin-card-icon"}),c(" Company Images ")]),a("p",{class:"admin-card-subtitle"},"Upload company logo and meta image for SEO")],-1)),a("div",Pa,[a("div",_a,[a("div",ha,[e[37]||(e[37]=a("label",{class:"admin-form-label"},"Company Logo",-1)),b(R,{"entity-type":"company","entity-id":m.value.id,"current-image":s.company.imageUrl,"image-alt":"Company Logo","local-mode":!0,onImageUploaded:O,onImageRemoved:$,onImageChanged:L,ref_key:"logoImageManager",ref:x},null,8,["entity-id","current-image"])]),a("div",Ra,[e[38]||(e[38]=a("label",{class:"admin-form-label"},"Meta Image (SEO)",-1)),b(R,{"entity-type":"company","entity-id":m.value.id,"current-image":s.company.metaImageUrl,"image-alt":"Company Meta Image","local-mode":!0,onImageUploaded:W,onImageRemoved:q,onImageChanged:j,ref_key:"metaImageManager",ref:M},null,8,["entity-id","current-image"])])])])]),a("div",Na,[e[45]||(e[45]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-credit-card admin-card-icon"}),c(" Financial Information ")]),a("p",{class:"admin-card-subtitle"},"Banking and tax details for payments")],-1)),a("div",Ba,[a("div",Aa,[a("div",Xa,[e[40]||(e[40]=a("label",{class:"admin-form-label"}," Bank Name ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[10]||(e[10]=l=>s.finance.bankName=l),class:"admin-form-control",placeholder:"e.g., PrivatBank"},null,512),[[i,s.finance.bankName]])]),a("div",Oa,[e[41]||(e[41]=a("label",{class:"admin-form-label"}," Bank Code ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[11]||(e[11]=l=>s.finance.bankCode=l),class:"admin-form-control",placeholder:"e.g., 305299"},null,512),[[i,s.finance.bankCode]])]),a("div",La,[e[42]||(e[42]=a("label",{class:"admin-form-label"}," Bank Account Number ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[12]||(e[12]=l=>s.finance.bankAccount=l),class:"admin-form-control",placeholder:"e.g., 2600600**********123456789"},null,512),[[i,s.finance.bankAccount]])]),a("div",$a,[e[43]||(e[43]=a("label",{class:"admin-form-label"}," Tax ID ",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[13]||(e[13]=l=>s.finance.taxId=l),class:"admin-form-control",placeholder:"e.g., **********"},null,512),[[i,s.finance.taxId]])]),a("div",Wa,[e[44]||(e[44]=a("label",{class:"admin-form-label"}," Payment Details ",-1)),o(a("textarea",{"onUpdate:modelValue":e[14]||(e[14]=l=>s.finance.paymentDetails=l),class:"admin-form-control",rows:"3",placeholder:"Additional payment information..."},null,512),[[i,s.finance.paymentDetails]])])])])]),a("div",ja,[b(D,{to:`/admin/companies/${m.value.id}`,class:"admin-btn admin-btn-secondary"},{default:F(()=>e[46]||(e[46]=[a("i",{class:"fas fa-times"},null,-1),a("span",null,"Cancel",-1)])),_:1},8,["to"]),a("button",{type:"button",onClick:w,class:"admin-btn admin-btn-secondary",disabled:t.value},e[47]||(e[47]=[a("i",{class:"fas fa-undo"},null,-1),a("span",null,"Reset",-1)]),8,qa),a("button",{type:"submit",class:"admin-btn admin-btn-primary",disabled:t.value},[a("i",{class:P(["fas fa-save",{"fa-spin":t.value}])},null,2),a("span",null,U(t.value?"Saving...":"Save Changes"),1)],8,Ta)])],32)])):E("",!0)])}}},Ya=z(Ka,[["__scopeId","data-v-69f69a35"]]);export{Ya as default};
