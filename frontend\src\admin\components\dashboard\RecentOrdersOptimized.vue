<template>
  <div class="admin-recent-orders">
    <div class="admin-recent-orders__header">
      <h3 class="admin-recent-orders__title">
        <i class="admin-recent-orders__icon fas fa-shopping-cart"></i>
        Recent Orders
      </h3>
      <router-link 
        to="/admin/orders" 
        class="admin-recent-orders__view-all"
      >
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </router-link>
    </div>

    <div class="admin-recent-orders__content">
      <!-- Loading State -->
      <div v-if="loading" class="admin-recent-orders__loading">
        <div class="admin-recent-orders__loading-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-recent-orders__loading-text">Loading recent orders...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="!orders || orders.length === 0" class="admin-recent-orders__empty">
        <div class="admin-recent-orders__empty-icon">
          <i class="fas fa-shopping-cart"></i>
        </div>
        <h4 class="admin-recent-orders__empty-title">No Recent Orders</h4>
        <p class="admin-recent-orders__empty-text">
          Orders will appear here once customers start making purchases.
        </p>
      </div>

      <!-- Orders List -->
      <div v-else class="admin-recent-orders__list">
        <div
          v-for="order in orders"
          :key="order.id"
          class="admin-recent-orders__card"
          @click="navigateToOrder(order.id)"
        >
          <!-- Card Header -->
          <div class="admin-recent-orders__card-header">
            <div class="admin-recent-orders__order-meta">
              <span class="admin-recent-orders__order-id">#{{ order.id.slice(-8) }}</span>
              <span class="admin-recent-orders__order-date">{{ formatDate(order.createdAt) }}</span>
            </div>
            <div class="admin-recent-orders__status-wrapper">
              <status-badge :status="order.status" type="order" />
            </div>
          </div>

          <!-- Card Body -->
          <div class="admin-recent-orders__card-body">
            <!-- Customer Section -->
            <div class="admin-recent-orders__customer-section">
              <div class="admin-recent-orders__customer-avatar">
                <i class="fas fa-user"></i>
              </div>
              <div class="admin-recent-orders__customer-details">
                <div class="admin-recent-orders__customer-name">{{ order.customerName }}</div>
                <div class="admin-recent-orders__customer-email">{{ order.customerEmail }}</div>
              </div>
            </div>

            <!-- Order Summary -->
            <div class="admin-recent-orders__order-summary">
              <div class="admin-recent-orders__total-section">
                <div class="admin-recent-orders__total-label">Total</div>
                <div class="admin-recent-orders__total-amount">{{ formatCurrency(order.total) }}</div>
              </div>
              <div class="admin-recent-orders__items-section">
                <div class="admin-recent-orders__items-count">{{ order.itemsCount }} items</div>
              </div>
            </div>
          </div>

          <!-- Card Footer -->
          <div class="admin-recent-orders__card-footer">
            <button
              class="admin-recent-orders__view-btn"
              @click.stop="navigateToOrder(order.id)"
              :title="`View order #${order.id}`"
            >
              <i class="fas fa-arrow-right"></i>
              <span>View Details</span>
            </button>
            <button
              v-if="order.status === 'pending'"
              class="admin-recent-orders__process-btn"
              @click.stop="processOrder(order.id)"
              :title="`Process order #${order.id}`"
            >
              <i class="fas fa-check"></i>
              <span>Process</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const props = defineProps({
  orders: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['process-order']);

const router = useRouter();

// Computed properties
const hasOrders = computed(() => props.orders && props.orders.length > 0);

// Methods
const formatCurrency = (value) => {
  const numValue = typeof value === 'string' ? Number(value) : value;
  
  if (isNaN(numValue)) {
    return 'UAH 0.00';
  }

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    currencyDisplay: 'code'
  }).format(numValue).replace('UAH', 'UAH');
};

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown';
  
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

const navigateToOrder = (orderId) => {
  router.push(`/admin/orders/${orderId}`);
};

const processOrder = (orderId) => {
  emit('process-order', orderId);
};
</script>

<style scoped>
.admin-recent-orders {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-recent-orders__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.admin-recent-orders__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-recent-orders__icon {
  color: #3b82f6;
  font-size: 1rem;
}

.admin-recent-orders__view-all {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.admin-recent-orders__view-all:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  color: white;
  text-decoration: none;
}

.admin-recent-orders__content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.admin-recent-orders__loading,
.admin-recent-orders__empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
}

.admin-recent-orders__loading-spinner,
.admin-recent-orders__empty-icon {
  font-size: 2rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.admin-recent-orders__loading-spinner {
  color: #3b82f6;
}

.admin-recent-orders__loading-text,
.admin-recent-orders__empty-text {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
}

.admin-recent-orders__empty-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.admin-recent-orders__list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Card Container */
.admin-recent-orders__card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-recent-orders__card:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.admin-recent-orders__card:last-child {
  margin-bottom: 0;
}

/* Card Header */
.admin-recent-orders__card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.admin-recent-orders__order-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-recent-orders__order-id {
  font-weight: 700;
  color: #1e293b;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', monospace;
}

.admin-recent-orders__order-date {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.admin-recent-orders__status-wrapper {
  flex-shrink: 0;
}

/* Card Body */
.admin-recent-orders__card-body {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Customer Section */
.admin-recent-orders__customer-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.admin-recent-orders__customer-avatar {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.admin-recent-orders__customer-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  min-width: 0;
}

.admin-recent-orders__customer-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-recent-orders__customer-email {
  font-size: 0.75rem;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Order Summary */
.admin-recent-orders__order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.admin-recent-orders__total-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-recent-orders__total-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-recent-orders__total-amount {
  font-weight: 700;
  color: #059669;
  font-size: 1.125rem;
  font-family: 'Monaco', 'Menlo', monospace;
}

.admin-recent-orders__items-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-recent-orders__items-count {
  font-size: 0.875rem;
  color: #475569;
  font-weight: 500;
  background: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: 1px solid #cbd5e1;
}

/* Card Footer */
.admin-recent-orders__card-footer {
  padding: 1rem 1.25rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.admin-recent-orders__view-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-recent-orders__view-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.admin-recent-orders__process-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #059669;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-recent-orders__process-btn:hover {
  background: #047857;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .admin-recent-orders {
    padding: 1rem;
  }

  .admin-recent-orders__card-header {
    padding: 0.75rem 1rem;
  }

  .admin-recent-orders__card-body {
    padding: 1rem;
  }

  .admin-recent-orders__card-footer {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .admin-recent-orders__view-btn,
  .admin-recent-orders__process-btn {
    width: 100%;
    justify-content: center;
  }

  .admin-recent-orders__order-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .admin-recent-orders__customer-name,
  .admin-recent-orders__customer-email {
    white-space: normal;
  }
}
</style>
