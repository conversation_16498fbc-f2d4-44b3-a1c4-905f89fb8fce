import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { ordersService } from './orders';

class ExportService {
  /**
   * Export orders to Excel file
   * @param {Object} filters - Filters to apply when fetching orders
   * @param {string} filename - Name of the exported file
   */
  async exportOrdersToExcel(filters = {}, filename = 'orders_export') {
    try {
      // Fetch all orders with filters (without pagination)
      const allOrders = await this.fetchAllOrders(filters);
      
      if (!allOrders || allOrders.length === 0) {
        throw new Error('No orders found to export');
      }

      // Prepare data for Excel
      const excelData = this.prepareOrdersForExcel(allOrders);
      
      // Create workbook
      const workbook = XLSX.utils.book_new();
      
      // Create main orders sheet
      const ordersSheet = XLSX.utils.json_to_sheet(excelData.orders);
      XLSX.utils.book_append_sheet(workbook, ordersSheet, 'Orders');
      
      // Create order items sheet if there are items
      if (excelData.orderItems && excelData.orderItems.length > 0) {
        const itemsSheet = XLSX.utils.json_to_sheet(excelData.orderItems);
        XLSX.utils.book_append_sheet(workbook, itemsSheet, 'Order Items');
      }
      
      // Create summary sheet
      const summarySheet = XLSX.utils.json_to_sheet(excelData.summary);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
      
      // Style the sheets
      this.styleWorkbook(workbook);
      
      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { 
        bookType: 'xlsx', 
        type: 'array',
        cellStyles: true 
      });
      
      // Save file
      const data = new Blob([excelBuffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      saveAs(data, `${filename}_${timestamp}.xlsx`);
      
      return {
        success: true,
        message: `Successfully exported ${allOrders.length} orders`,
        filename: `${filename}_${timestamp}.xlsx`
      };
      
    } catch (error) {
      console.error('Export error:', error);
      throw new Error(`Export failed: ${error.message}`);
    }
  }

  /**
   * Fetch all orders with pagination handling
   */
  async fetchAllOrders(filters) {
    const allOrders = [];
    let currentPage = 1;
    let hasMorePages = true;
    const pageSize = 50; // Fetch in batches

    while (hasMorePages) {
      try {
        const response = await ordersService.getAll({
          ...filters,
          page: currentPage,
          pageSize: pageSize
        });

        if (response.data && response.data.length > 0) {
          allOrders.push(...response.data);
          
          // Check if there are more pages
          hasMorePages = response.data.length === pageSize && 
                        currentPage < (response.totalPages || 1);
          currentPage++;
        } else {
          hasMorePages = false;
        }
      } catch (error) {
        console.error(`Error fetching page ${currentPage}:`, error);
        hasMorePages = false;
      }
    }

    return allOrders;
  }

  /**
   * Convert order status enum to text
   */
  getOrderStatusText(status) {
    const statusMap = {
      0: 'Processing',
      1: 'Pending',
      2: 'Shipped',
      3: 'Delivered',
      4: 'Cancelled'
    };

    // Handle both numeric and string values
    if (typeof status === 'number') {
      return statusMap[status] || 'Unknown';
    }

    // If it's already a string, return as is (for backward compatibility)
    if (typeof status === 'string') {
      return status;
    }

    return 'Unknown';
  }

  /**
   * Convert payment status enum to text
   */
  getPaymentStatusText(status) {
    const statusMap = {
      0: 'Pending',
      1: 'Completed',
      2: 'Refunded',
      3: 'Failed'
    };

    // Handle both numeric and string values
    if (typeof status === 'number') {
      return statusMap[status] || 'Unknown';
    }

    // If it's already a string, return as is (for backward compatibility)
    if (typeof status === 'string') {
      return status;
    }

    return 'Unknown';
  }

  /**
   * Prepare orders data for Excel export
   */
  prepareOrdersForExcel(orders) {
    const ordersData = [];
    const orderItemsData = [];
    let totalRevenue = 0;
    const statusCounts = {};
    const paymentStatusCounts = {};

    orders.forEach(order => {
      // Convert status values to text - prefer backend text fields if available
      const orderStatusText = order.statusText || this.getOrderStatusText(order.status);
      const paymentStatusText = order.paymentStatusText || this.getPaymentStatusText(order.paymentStatus);

      // Main order data
      const orderRow = {
        'Order ID': order.id,
        'Customer Name': order.customerName || 'N/A',
        'Customer Email': order.customerEmail || 'N/A',
        'Order Date': this.formatDate(order.createdAt),
        'Total Amount': order.totalPriceAmount || 0,
        'Currency': order.totalPriceCurrency || 'USD',
        'Order Status': orderStatusText,
        'Payment Status': paymentStatusText,
        'Payment Method': order.paymentMethodText || 'Unknown',
        'Items Count': order.itemsCount || 0,
        'Shipping Method': order.shippingMethodName || 'N/A',
        'Shipping Address': this.formatShippingAddress(order),
        'Created At': this.formatDateTime(order.createdAt)
      };
      
      ordersData.push(orderRow);
      
      // Aggregate data for summary
      totalRevenue += order.totalPriceAmount || 0;

      statusCounts[orderStatusText] = (statusCounts[orderStatusText] || 0) + 1;
      paymentStatusCounts[paymentStatusText] = (paymentStatusCounts[paymentStatusText] || 0) + 1;
      
      // Order items (if available)
      if (order.items && order.items.length > 0) {
        order.items.forEach(item => {
          orderItemsData.push({
            'Order ID': order.id,
            'Product ID': item.productId,
            'Product Name': item.productName || 'N/A',
            'Quantity': item.quantity,
            'Unit Price': item.priceAmount,
            'Currency': item.priceCurrency,
            'Total Price': (item.quantity * item.priceAmount) || 0
          });
        });
      }
    });

    // Create summary data
    const summaryData = [
      { 'Metric': 'Total Orders', 'Value': orders.length },
      { 'Metric': 'Total Revenue', 'Value': totalRevenue.toFixed(2) },
      { 'Metric': 'Average Order Value', 'Value': (totalRevenue / orders.length).toFixed(2) },
      { 'Metric': '', 'Value': '' }, // Empty row
      { 'Metric': 'ORDER STATUSES', 'Value': '' },
      ...Object.entries(statusCounts).map(([status, count]) => ({
        'Metric': status,
        'Value': count
      })),
      { 'Metric': '', 'Value': '' }, // Empty row
      { 'Metric': 'PAYMENT STATUSES', 'Value': '' },
      ...Object.entries(paymentStatusCounts).map(([status, count]) => ({
        'Metric': status,
        'Value': count
      }))
    ];

    return {
      orders: ordersData,
      orderItems: orderItemsData,
      summary: summaryData
    };
  }

  /**
   * Format shipping address
   */
  formatShippingAddress(order) {
    const parts = [];
    if (order.shippingAddressLine) parts.push(order.shippingAddressLine);
    if (order.shippingCity) parts.push(order.shippingCity);
    if (order.shippingCountry) parts.push(order.shippingCountry);
    return parts.length > 0 ? parts.join(', ') : 'N/A';
  }

  /**
   * Format date for Excel
   */
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US');
  }

  /**
   * Format datetime for Excel
   */
  formatDateTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('en-US');
  }

  /**
   * Style the workbook
   */
  styleWorkbook(workbook) {
    // Add basic styling to sheets
    Object.keys(workbook.Sheets).forEach(sheetName => {
      const sheet = workbook.Sheets[sheetName];
      
      // Auto-fit columns
      const range = XLSX.utils.decode_range(sheet['!ref']);
      const colWidths = [];
      
      for (let col = range.s.c; col <= range.e.c; col++) {
        let maxWidth = 10;
        for (let row = range.s.r; row <= range.e.r; row++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = sheet[cellAddress];
          if (cell && cell.v) {
            const cellLength = cell.v.toString().length;
            maxWidth = Math.max(maxWidth, cellLength);
          }
        }
        colWidths.push({ width: Math.min(maxWidth + 2, 50) });
      }
      
      sheet['!cols'] = colWidths;
    });
  }

  /**
   * Export filtered orders based on search and filters
   */
  async exportFilteredOrders(searchQuery, filters, filename = 'filtered_orders') {
    const exportFilters = {
      ...filters,
      search: searchQuery
    };
    
    return await this.exportOrdersToExcel(exportFilters, filename);
  }
}

export const exportService = new ExportService();
