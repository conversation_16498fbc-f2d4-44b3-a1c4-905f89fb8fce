<template>
  <div class="product-form">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">{{ isEditMode ? 'Edit Product' : 'Add Product' }}</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/products" class="button is-light">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Products</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered py-6">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading product data...</p>
    </div>
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>
    <div v-else>
      <form @submit.prevent="saveProduct">
        <div class="columns">
          <!-- Main Product Information -->
          <div class="column is-8">
            <div class="card">
              <div class="card-header">
                <p class="card-header-title">Product Information</p>
              </div>
              <div class="card-content">
                <div class="field">
                  <label class="label">Product Name*</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="product.name" 
                      required
                      placeholder="Enter product name">
                  </div>
                </div>

                <div class="field">
                  <label class="label">Description*</label>
                  <div class="control">
                    <textarea 
                      class="textarea" 
                      v-model="product.description" 
                      required
                      placeholder="Enter product description"
                      rows="5"></textarea>
                  </div>
                </div>

                <div class="columns">
                  <div class="column is-6">
                    <div class="field">
                      <label class="label">SKU</label>
                      <div class="control">
                        <input 
                          class="input" 
                          type="text" 
                          v-model="product.sku" 
                          placeholder="Enter product SKU">
                      </div>
                    </div>
                  </div>
                  <div class="column is-6">
                    <div class="field">
                      <label class="label">Slug</label>
                      <div class="control">
                        <input 
                          class="input" 
                          type="text" 
                          v-model="product.slug" 
                          placeholder="Enter product slug">
                      </div>
                      <p class="help">Leave empty to auto-generate from name</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pricing and Inventory -->
            <div class="card mt-4">
              <div class="card-header">
                <p class="card-header-title">Pricing & Inventory</p>
              </div>
              <div class="card-content">
                <div class="columns">
                  <div class="column is-4">
                    <div class="field">
                      <label class="label">Price*</label>
                      <div class="control has-icons-left">
                        <input 
                          class="input" 
                          type="number" 
                          v-model="product.price" 
                          required
                          min="0" 
                          step="0.01"
                          placeholder="0.00">
                        <span class="icon is-small is-left">
                          <i class="fas fa-hryvnia"></i>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="column is-4">
                    <div class="field">
                      <label class="label">Compare at Price</label>
                      <div class="control has-icons-left">
                        <input 
                          class="input" 
                          type="number" 
                          v-model="product.compareAtPrice" 
                          min="0" 
                          step="0.01"
                          placeholder="0.00">
                        <span class="icon is-small is-left">
                          <i class="fas fa-hryvnia"></i>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="column is-4">
                    <div class="field">
                      <label class="label">Cost Price</label>
                      <div class="control has-icons-left">
                        <input 
                          class="input" 
                          type="number" 
                          v-model="product.costPrice" 
                          min="0" 
                          step="0.01"
                          placeholder="0.00">
                        <span class="icon is-small is-left">
                          <i class="fas fa-hryvnia"></i>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="columns">
                  <div class="column is-4">
                    <div class="field">
                      <label class="label">Stock*</label>
                      <div class="control">
                        <input 
                          class="input" 
                          type="number" 
                          v-model="product.stock" 
                          required
                          min="0" 
                          step="1"
                          placeholder="0">
                      </div>
                    </div>
                  </div>
                  <div class="column is-4">
                    <div class="field">
                      <label class="label">Low Stock Threshold</label>
                      <div class="control">
                        <input 
                          class="input" 
                          type="number" 
                          v-model="product.lowStockThreshold" 
                          min="0" 
                          step="1"
                          placeholder="5">
                      </div>
                    </div>
                  </div>
                  <div class="column is-4">
                    <div class="field">
                      <label class="label">Weight (kg)</label>
                      <div class="control">
                        <input 
                          class="input" 
                          type="number" 
                          v-model="product.weight" 
                          min="0" 
                          step="0.01"
                          placeholder="0.00">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Product Images -->
            <div class="card mt-4">
              <div class="card-header">
                <p class="card-header-title">Product Images</p>
              </div>
              <div class="card-content">
                <ProductImagesManager
                  ref="productImagesManager"
                  :product-id="isEditMode ? productId : null"
                  :images="product.images || []"
                  :is-create="!isEditMode"
                  @images-updated="handleImagesUpdated"
                  @main-image-changed="handleMainImageChanged"
                  @image-uploaded="handleImageUploaded"
                  @image-deleted="handleImageDeleted"
                  @pending-images-changed="handlePendingImagesChanged"
                />
              </div>
            </div>

            <!-- Meta Image -->
            <div class="card mt-4">
              <div class="card-header">
                <p class="card-header-title">Meta Image (SEO)</p>
              </div>
              <div class="card-content">
                <MetaImageManager
                  ref="metaImageManager"
                  :product-id="isEditMode ? productId : null"
                  :meta-image="product.metaImage"
                  :is-create="!isEditMode"
                  @meta-image-updated="handleMetaImageUpdated"
                  @meta-image-uploaded="handleMetaImageUploaded"
                  @meta-image-deleted="handleMetaImageDeleted"
                  @pending-meta-image-changed="handlePendingMetaImageChanged"
                />
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="column is-4">
            <!-- Status and Visibility -->
            <div class="card">
              <div class="card-header">
                <p class="card-header-title">Status & Visibility</p>
              </div>
              <div class="card-content">
                <div class="field">
                  <label class="label">Status</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="product.status">
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="field">
                  <label class="label">Visibility</label>
                  <div class="control">
                    <label class="checkbox">
                      <input type="checkbox" v-model="product.isFeatured">
                      Featured product
                    </label>
                  </div>
                </div>

                <div class="field">
                  <label class="label">Publish Date</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="date" 
                      v-model="product.publishDate">
                  </div>
                </div>
              </div>
            </div>

            <!-- Organization -->
            <div class="card mt-4">
              <div class="card-header">
                <p class="card-header-title">Organization</p>
              </div>
              <div class="card-content">
                <div class="field">
                  <label class="label">Category*</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="product.categoryId" required>
                        <option value="" disabled>Select a category</option>
                        <option 
                          v-for="category in categories" 
                          :key="category.id" 
                          :value="category.id">
                          {{ category.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="field">
                  <label class="label">Tags</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="tagsInput" 
                      placeholder="Enter tags separated by commas">
                  </div>
                </div>

                <div v-if="product.tags && product.tags.length > 0" class="tags-container">
                  <span 
                    v-for="(tag, index) in product.tags" 
                    :key="index" 
                    class="tag is-primary is-medium">
                    {{ tag }}
                    <button 
                      class="delete is-small" 
                      type="button"
                      @click="removeTag(index)"></button>
                  </span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
              <div class="card-content">
                <div class="field is-grouped">
                  <div class="control is-expanded">
                    <button 
                      type="submit" 
                      class="button is-primary is-fullwidth"
                      :class="{ 'is-loading': saving }">
                      {{ isEditMode ? 'Update Product' : 'Create Product' }}
                    </button>
                  </div>
                  <div class="control">
                    <router-link 
                      to="/admin/products" 
                      class="button is-light">
                      Cancel
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';
import ProductImagesManager from '@/admin/components/products/ProductImagesManager.vue';
import MetaImageManager from '@/admin/components/products/MetaImageManager.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const categories = ref([]);
const uploadingImages = ref(false);
const tagsInput = ref('');

// Component refs
const productImagesManager = ref(null);
const metaImageManager = ref(null);

// Product data
const product = reactive({
  name: '',
  description: '',
  sku: '',
  slug: '',
  price: 0,
  compareAtPrice: null,
  costPrice: null,
  stock: 0,
  lowStockThreshold: 5,
  weight: null,
  status: 'active',
  isFeatured: false,
  publishDate: new Date().toISOString().split('T')[0],
  categoryId: '',
  tags: [],
  images: [],
  metaImage: null
});

// Computed properties
const isEditMode = computed(() => !!route.params.id);
const productId = computed(() => route.params.id);

// Watch tags input
watch(tagsInput, (newValue) => {
  if (newValue.includes(',')) {
    const tags = newValue.split(',').map(tag => tag.trim()).filter(tag => tag);
    product.tags = [...new Set([...product.tags, ...tags])];
    tagsInput.value = '';
  }
});

// Fetch product data if in edit mode
const fetchProduct = async () => {
  if (!isEditMode.value) return;
  
  loading.value = true;
  
  try {
    const data = await productsService.getProductById(productId.value);
    
    // Update product data
    Object.keys(product).forEach(key => {
      if (data[key] !== undefined) {
        product[key] = data[key];
      }
    });
    
    // Handle tags if they come as a string
    if (typeof data.tags === 'string') {
      product.tags = data.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    }
    
    // Ensure images is an array
    if (!Array.isArray(product.images)) {
      product.images = [];
    }
    
  } catch (err) {
    console.error('Error fetching product:', err);
    error.value = 'Failed to load product data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Fetch categories
const fetchCategories = async () => {
  try {
    const response = await categoriesService.getCategories();
    if (response.categories) {
      categories.value = response.categories;
    }
  } catch (err) {
    console.error('Error fetching categories:', err);
  }
};

// New image management methods
const handleImagesUpdated = async () => {
  // Reload product data to get updated images
  if (isEditMode.value) {
    await fetchProduct();
  }
};

const handleMainImageChanged = (imageId) => {
  console.log('Main image changed:', imageId);
  // The ProductImagesManager handles the API call
  // We just need to refresh the data if in edit mode
  if (isEditMode.value) {
    fetchProduct();
  }
};

const handleImageUploaded = (response) => {
  console.log('Image uploaded:', response);
  // Refresh product data if in edit mode
  if (isEditMode.value) {
    fetchProduct();
  }
};

const handleImageDeleted = (imageId) => {
  console.log('Image deleted:', imageId);
  // Refresh product data if in edit mode
  if (isEditMode.value) {
    fetchProduct();
  }
};

const handlePendingImagesChanged = (pendingImages) => {
  console.log('Pending images changed:', pendingImages);
  // Store pending images for later processing if needed
};

// Meta image management methods
const handleMetaImageUpdated = async () => {
  // Reload product data to get updated meta image
  if (isEditMode.value) {
    await fetchProduct();
  }
};

const handleMetaImageUploaded = (metaImageUrl) => {
  console.log('Meta image uploaded:', metaImageUrl);
  product.metaImage = metaImageUrl;
};

const handleMetaImageDeleted = () => {
  console.log('Meta image deleted');
  product.metaImage = null;
};

const handlePendingMetaImageChanged = (pendingMetaImage) => {
  console.log('Pending meta image changed:', pendingMetaImage);
  // Store pending meta image for later processing if needed
};

// Remove tag
const removeTag = (index) => {
  product.tags.splice(index, 1);
};

// Save product
const saveProduct = async () => {
  saving.value = true;
  error.value = null;
  
  try {
    // Add any remaining tags from input
    if (tagsInput.value.trim()) {
      const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);
      product.tags = [...new Set([...product.tags, ...tags])];
      tagsInput.value = '';
    }
    
    // Prepare product data
    const productData = { ...product };
    
    // Handle images for new product
    if (!isEditMode.value) {
      // Remove the actual File objects before sending to API
      delete productData.images;
    }
    
    let savedProduct;
    
    if (isEditMode.value) {
      // Update existing product
      savedProduct = await productsService.updateProduct(productId.value, productData);
    } else {
      // Create new product
      savedProduct = await productsService.createProduct(productData);

      // Upload pending images and meta image if any
      if (savedProduct.product && savedProduct.product.id) {
        const newProductId = savedProduct.product.id;

        // Upload pending images through ProductImagesManager
        if (productImagesManager.value) {
          await productImagesManager.value.uploadPendingImages(newProductId);
        }

        // Upload pending meta image through MetaImageManager
        if (metaImageManager.value) {
          await metaImageManager.value.uploadPendingImage();
        }
      }
    }
    
    // Redirect to product list
    router.push('/admin/products');
  } catch (err) {
    console.error('Error saving product:', err);
    error.value = 'Failed to save product. Please check your input and try again.';
  } finally {
    saving.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchCategories();
  fetchProduct();
});
</script>

<style scoped>
.product-form {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.product-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.product-image-item {
  display: flex;
  flex-direction: column;
}

.image-container {
  position: relative;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #dbdbdb;
}

.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  gap: 0.25rem;
  padding: 0.25rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-bottom-left-radius: 4px;
}

.image-info {
  margin-top: 0.5rem;
  text-align: center;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.button.is-primary.is-outlined {
  background-color: transparent;
  border-color: #ff7700;
  color: #ff7700;
}

.button.is-primary.is-outlined:hover {
  background-color: #ff7700;
  color: white;
}

.tag.is-primary {
  background-color: #ff7700;
}
</style>
