<template>
  <div class="admin-user-table">
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Email</th>
            <th>Role</th>
            <th>Registered</th>

            <th>Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && users.length > 0">
          <tr v-for="user in users" :key="user.id">
            <td>
              <span class="admin-user-id">{{ truncateId(user.id) }}</span>
            </td>
            <td>{{ user.username || 'N/A' }}</td>
            <td>{{ user.email }}</td>
            <td>
              <div class="admin-user-role-select">
                <div class="select">
                  <select
                    :value="user.role"
                    @change="roleChanged($event, user.id)"
                    :disabled="user.role === 'Admin' && currentUserRole !== 'Admin'">
                    <option value="Admin">Admin</option>
                    <option value="Moderator">Moderator</option>
                    <option value="Seller">Seller</option>
                    <option value="Buyer">Buyer</option>
                  </select>
                </div>
              </div>
            </td>
            <td>{{ formatDate(user.createdAt || user.registeredAt) }}</td>
            <td>
              <div class="admin-user-actions">
                <button
                  class="button is-info"
                  @click="$emit('edit', user)"
                  title="Edit User">
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  class="button is-danger"
                  @click="$emit('delete', user)"
                  :disabled="user.role === 'Admin' && currentUserRole !== 'Admin'"
                  title="Delete User">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="6" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="6" class="has-text-centered">
              No users found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';

const props = defineProps({
  users: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['edit', 'delete', 'change-role']);

const store = useStore();
const currentUserRole = computed(() => store.getters['auth/user']?.role || '');

// Format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Truncate ID for display
const truncateId = (id) => {
  if (!id) return 'N/A';
  if (typeof id === 'string' && id.length > 8) {
    return id.substring(0, 8) + '...';
  }
  return id;
};



// Handle role change
const roleChanged = (event, userId) => {
  const newRole = event.target.value;
  emit('change-role', userId, newRole);
};
</script>

<style scoped>
.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}
</style>
