namespace Marketplace.Application.Responses;

public class ActiveSessionResponse
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public DateTime LoginTime { get; set; }
    public DateTime LastActivity { get; set; }
    public bool IsCurrentSession { get; set; }
    public string Location { get; set; } = string.Empty;
    public string Device { get; set; } = string.Empty;
}

public class SuspiciousActivityResponse
{
    public Guid Id { get; set; }
    public string ActivityType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserEmail { get; set; }
    public DateTime Timestamp { get; set; }
    public string Severity { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public object? AdditionalData { get; set; }
}

public class BlockedIpResponse
{
    public Guid Id { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime BlockedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public Guid? BlockedByUserId { get; set; }
    public string? BlockedByUserName { get; set; }
    public bool IsActive { get; set; }
    public int AttemptsCount { get; set; }
    public string? Notes { get; set; }
}

public class SecurityStatisticsResponse
{
    public int TotalActiveSessions { get; set; }
    public int TotalSuspiciousActivities { get; set; }
    public int TotalBlockedIps { get; set; }
    public int FailedLoginAttempts24h { get; set; }
    public int SuccessfulLogins24h { get; set; }
    public int NewUsers24h { get; set; }
    public List<TopIpActivity> TopIpAddresses { get; set; } = new();
    public List<TopUserActivity> TopActiveUsers { get; set; } = new();
    public List<SecurityTrend> SecurityTrends { get; set; } = new();
}

public class TopIpActivity
{
    public string IpAddress { get; set; } = string.Empty;
    public int RequestsCount { get; set; }
    public int FailedAttempts { get; set; }
    public DateTime LastActivity { get; set; }
    public string Location { get; set; } = string.Empty;
}

public class TopUserActivity
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public int SessionsCount { get; set; }
    public DateTime LastLogin { get; set; }
    public string LastIpAddress { get; set; } = string.Empty;
}

public class SecurityTrend
{
    public DateTime Date { get; set; }
    public int LoginAttempts { get; set; }
    public int FailedLogins { get; set; }
    public int SuspiciousActivities { get; set; }
    public int BlockedIps { get; set; }
}
