<template>
  <div class="coupon-section">

    <div class="coupon-input-container">
      <div class="coupon-input-group">
        <input
          v-model="couponCode"
          type="text"
          class="coupon-input"
          placeholder="Промокод"
          :disabled="loading || appliedCoupon"
          @keyup.enter="validateCoupon"
          @input="clearError"
        />
        <button
          v-if="!appliedCoupon"
          @click="validateCoupon"
          :disabled="!couponCode.trim() || loading"
          class="coupon-apply-btn"
        >
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          {{ loading ? 'Перевірка...' : 'Додати' }}
        </button>
        <button
          v-else
          @click="removeCoupon"
          class="coupon-remove-btn"
        >
          <i class="fas fa-times"></i>
          Видалити
        </button>
      </div>

      <!-- Повідомлення про помилку -->
      <div v-if="error" class="coupon-error">
        <i class="fas fa-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Інформація про застосований купон -->
      <div v-if="appliedCoupon" class="coupon-success">
        <div class="coupon-info">
          <div class="coupon-info-header">
            <i class="fas fa-check-circle"></i>
            <span class="coupon-code">{{ appliedCoupon.code }}</span>
            <span v-if="appliedCoupon.discountType === 0" class="coupon-discount">{{ Math.round(formatDiscount(appliedCoupon)) }} %</span>
            <span v-if="appliedCoupon.discountType === 1" class="coupon-discount">{{ Math.round(formatDiscount(appliedCoupon)) }} ₴</span>
          </div>
          <div class="coupon-info-details">
            <span class="coupon-type">{{ formatDiscountType(appliedCoupon.discountType) }} знижка</span>
            <span v-if="appliedCoupon.expiresAt" class="coupon-expiry">
              Діє до: {{ formatDate(appliedCoupon.expiresAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import couponService from '@/services/coupon.service';
import { useToast } from '@/composables/useToast';

export default {
  name: 'CouponInput',
  
  props: {
    appliedCoupon: {
      type: Object,
      default: null
    }
  },

  emits: ['coupon-applied', 'coupon-removed'],

  data() {
    const { showToast } = useToast();
    
    return {
      couponCode: '',
      loading: false,
      error: null,
      showToast
    };
  },

  watch: {
    appliedCoupon(newCoupon) {
      if (newCoupon) {
        this.couponCode = newCoupon.code;
        this.error = null;
      } else {
        this.couponCode = '';
      }
    }
  },

  methods: {
    async validateCoupon() {
      if (!this.couponCode.trim()) {
        this.error = 'Введіть код купона';
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        const response = await couponService.validateCoupon(this.couponCode.trim());
        console.log('CouponInput - Validation response:', response);

        if (response.success && response.data.isValid) {
          // Купон валідний
          console.log('CouponInput - Valid coupon data:', response.data.coupon);
          this.$emit('coupon-applied', response.data.coupon);
          this.showToast('Промокод успішно застосовано!', 'success');
        } else {
          // Купон невалідний
          console.log('CouponInput - Invalid coupon:', response.data);
          this.error = response.data.message || 'Невірний промокод';
          this.showToast(this.error, 'error');
        }
      } catch (error) {
        console.error('Error validating coupon:', error);
        console.error('Error response data:', error.response?.data);
        this.error = error.response?.data?.message || 'Помилка при перевірці промокода';
        this.showToast(this.error, 'error');
      } finally {
        this.loading = false;
      }
    },

    removeCoupon() {
      this.couponCode = '';
      this.error = null;
      this.$emit('coupon-removed');
      this.showToast('Промокод видалено', 'info');
    },

    clearError() {
      this.error = null;
    },

    formatDiscount(coupon) {
      return couponService.formatDiscount(coupon);
    },

    formatDiscountType(discountType) {
      return couponService.formatDiscountType(discountType);
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  }
};
</script>

<style scoped>

.coupon-header {
  margin-bottom: 16px;
}

.coupon-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.coupon-title i {
  color: #007bff;
}

.coupon-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.coupon-input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.coupon-input {
  flex: 1;
  padding: 10px 14px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 15px;
  transition: border-color 0.3s ease;
}

.coupon-input:focus {
  outline: none;
  border-color: #007bff;
}

.coupon-input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
}

.coupon-apply-btn,
.coupon-remove-btn {
  padding: 10px 16px;
  border: 1px solid #007bff;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.coupon-apply-btn {
  color: #007bff;
}

.coupon-apply-btn:hover:not(:disabled) {
  background: #0056b3;
  color: white;
}

.coupon-apply-btn:disabled {
  cursor: not-allowed;
}

.coupon-remove-btn {
  background: #dc3545;
  color: white;
}

.coupon-remove-btn:hover {
  background: #c82333;
}

.coupon-error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc3545;
  font-size: 14px;
  padding: 8px 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
}

.coupon-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 12px;
}

.coupon-info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.coupon-info-header i {
  color: #28a745;
  font-size: 18px;
}

.coupon-code {
  font-weight: 600;
  color: #155724;
  font-size: 16px;
}

.coupon-discount {
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
}

.coupon-info-details {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #155724;
}

.coupon-type {
  font-weight: 500;
}

.coupon-expiry {
  color: #6c757d;
}

@media (max-width: 768px) {
  .coupon-input-group {
    flex-direction: column;
  }
  
  .coupon-info-details {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
