import{_ as W,h as k,g as x,c as o,a as e,b as q,s as _,k as w,t as d,z as i,n as h,F as p,p as g,x as G,W as H,w as F,r as J,d as K,e as Q,o as n}from"./index-L-hJxM_5.js";import{r as S}from"./ratings-D2bTWLA4.js";import{S as X}from"./SearchAndFilters-B3kez0yT.js";import{P as Y}from"./Pagination-DX2plTiq.js";import{u as Z}from"./useAdminSearch-BxXMp5oH.js";const ee={class:"rating-list"},se={class:"level"},le={class:"level-right"},te={class:"level-item"},ae={class:"buttons"},ne=["disabled"],oe={key:0,class:"has-text-centered py-6"},ie={key:1,class:"notification is-danger"},re={key:2,class:"card"},de={class:"card-content"},ue={class:"table is-fullwidth is-hoverable"},ce={class:"checkbox"},ve=["checked","indeterminate"],me={class:"checkbox"},he=["value"],pe={key:1,class:"has-text-grey"},ge={key:1,class:"has-text-grey"},fe={class:"stars"},be={class:"ml-1"},ye={class:"stars"},ke={class:"ml-1"},_e={class:"stars"},Se={class:"ml-1"},Ce={class:"stars"},Re={class:"ml-1"},Ae={class:"buttons"},De=["onClick","disabled"],xe=["onClick","disabled"],we={__name:"RatingList",setup(Fe){const P=Q(),C=[{key:"minRating",label:"Min Rating",type:"select",columnClass:"is-3",options:[{value:"",label:"Any"},{value:"1",label:"1 Star"},{value:"2",label:"2 Stars"},{value:"3",label:"3 Stars"},{value:"4",label:"4 Stars"},{value:"5",label:"5 Stars"}]},{key:"maxRating",label:"Max Rating",type:"select",columnClass:"is-3",options:[{value:"",label:"Any"},{value:"1",label:"1 Star"},{value:"2",label:"2 Stars"},{value:"3",label:"3 Stars"},{value:"4",label:"4 Stars"},{value:"5",label:"5 Stars"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",options:[{value:"CreatedAt",label:"Date Created"},{value:"UpdatedAt",label:"Date Updated"},{value:"Service",label:"Service Rating"},{value:"DeliveryTime",label:"Delivery Rating"},{value:"Accuracy",label:"Accuracy Rating"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}],{items:U,loading:b,error:f,isFirstLoad:R,currentPage:N,totalPages:A,totalItems:B,filters:c,fetchData:y,handlePageChange:L}=Z({fetchFunction:S.getRatings,defaultFilters:{minRating:"",maxRating:"",sortBy:"CreatedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:15,clientSideSearch:!1}),v=k(()=>U.value.map(l=>({...l,average:(l.service+l.deliveryTime+l.accuracy)/3}))),u=x(!1),r=x([]),D=k(()=>v.value.length>0&&r.value.length===v.value.length),I=k(()=>r.value.length>0&&r.value.length<v.value.length),T=l=>{c.search=l},V=(l,s)=>{c[l]=s},M=()=>{Object.keys(c).forEach(l=>{var s,m,t;l==="search"?c[l]="":c[l]=((t=(m=(s=C.find(a=>a.key===l))==null?void 0:s.options)==null?void 0:m[0])==null?void 0:t.value)||""}),y(1)},O=()=>{D.value?r.value=[]:r.value=v.value.map(l=>l.id)},z=async l=>{if(confirm("Are you sure you want to delete this rating?")){u.value=!0;try{await S.deleteRating(l),await y()}catch(s){f.value=s.message||"Failed to delete rating"}finally{u.value=!1}}},$=async()=>{if(confirm(`Are you sure you want to delete ${r.value.length} selected ratings?`)){u.value=!0;try{await S.bulkDeleteRatings(r.value),await y()}catch(l){f.value=l.message||"Failed to delete ratings"}finally{u.value=!1}}},E=l=>{P.push({name:"AdminRatingDetail",params:{id:l}})},j=l=>l?new Date(l).toLocaleDateString():"N/A";return(l,s)=>{const m=J("router-link");return n(),o("div",ee,[e("div",se,[s[3]||(s[3]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Ratings Management")])],-1)),e("div",le,[e("div",te,[e("div",ae,[r.value.length>0?(n(),o("button",{key:0,class:"button is-danger",onClick:$,disabled:r.value.length===0||u.value},[s[2]||(s[2]=e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)),e("span",null,"Delete Selected ("+d(r.value.length)+")",1)],8,ne)):w("",!0)])])])]),q(X,{filters:i(c),"filter-fields":C,"search-label":"Search Ratings","search-placeholder":"Search by product name, user name, or comment...","search-column-class":"is-4","total-items":i(B),"item-name":"ratings",loading:i(b),onSearchChanged:T,onFilterChanged:V,onResetFilters:M},null,8,["filters","total-items","loading"]),i(b)&&i(R)?(n(),o("div",oe,s[4]||(s[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading ratings...",-1)]))):i(f)?(n(),o("div",ie,[e("p",null,d(i(f)),1),e("button",{class:"button is-light mt-2",onClick:s[0]||(s[0]=(...t)=>l.fetchRatings&&l.fetchRatings(...t))},s[5]||(s[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(n(),o("div",re,[e("div",de,[e("div",{class:h(["table-container",{"is-loading":i(b)&&!i(R)}])},[e("table",ue,[e("thead",null,[e("tr",null,[e("th",null,[e("label",ce,[e("input",{type:"checkbox",onChange:O,checked:D.value,indeterminate:I.value},null,40,ve)])]),s[6]||(s[6]=e("th",null,"Product",-1)),s[7]||(s[7]=e("th",null,"User",-1)),s[8]||(s[8]=e("th",null,"Service",-1)),s[9]||(s[9]=e("th",null,"Delivery",-1)),s[10]||(s[10]=e("th",null,"Accuracy",-1)),s[11]||(s[11]=e("th",null,"Average",-1)),s[12]||(s[12]=e("th",null,"Created",-1)),s[13]||(s[13]=e("th",null,"Actions",-1))])]),e("tbody",null,[(n(!0),o(p,null,g(v.value,t=>(n(),o("tr",{key:t.id},[e("td",null,[e("label",me,[G(e("input",{type:"checkbox",value:t.id,"onUpdate:modelValue":s[1]||(s[1]=a=>r.value=a)},null,8,he),[[H,r.value]])])]),e("td",null,[t.productId?(n(),_(m,{key:0,to:{name:"AdminProductDetail",params:{id:t.productId}},class:"has-text-link"},{default:F(()=>[e("strong",null,d(t.productName||"Unknown Product"),1)]),_:2},1032,["to"])):(n(),o("strong",pe,d(t.productName||"Unknown Product"),1))]),e("td",null,[t.userId?(n(),_(m,{key:0,to:{name:"AdminUserDetail",params:{id:t.userId}},class:"has-text-link"},{default:F(()=>[K(d(t.userName||"Unknown User"),1)]),_:2},1032,["to"])):(n(),o("span",ge,d(t.userName||"Unknown User"),1))]),e("td",null,[e("div",fe,[(n(),o(p,null,g(5,a=>e("span",{key:a,class:h(["star",{"is-filled":a<=t.service}])}," ★ ",2)),64)),e("span",be,d(t.service),1)])]),e("td",null,[e("div",ye,[(n(),o(p,null,g(5,a=>e("span",{key:a,class:h(["star",{"is-filled":a<=t.deliveryTime}])}," ★ ",2)),64)),e("span",ke,d(t.deliveryTime),1)])]),e("td",null,[e("div",_e,[(n(),o(p,null,g(5,a=>e("span",{key:a,class:h(["star",{"is-filled":a<=t.accuracy}])}," ★ ",2)),64)),e("span",Se,d(t.accuracy),1)])]),e("td",null,[e("div",Ce,[(n(),o(p,null,g(5,a=>e("span",{key:a,class:h(["star",{"is-filled":a<=t.average}])}," ★ ",2)),64)),e("span",Re,d(t.average.toFixed(1)),1)])]),e("td",null,d(j(t.createdAt)),1),e("td",null,[e("div",Ae,[e("button",{class:"button is-small is-info",onClick:a=>E(t.id),disabled:u.value},s[14]||(s[14]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)]),8,De),e("button",{class:"button is-small is-danger",onClick:a=>z(t.id),disabled:u.value},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]),8,xe)])])]))),128))])])],2)])])),i(A)>1?(n(),_(Y,{key:3,"current-page":i(N),"total-pages":i(A),onPageChanged:i(L)},null,8,["current-page","total-pages","onPageChanged"])):w("",!0)])}}},Ie=W(we,[["__scopeId","data-v-3c2eb2c2"]]);export{Ie as default};
