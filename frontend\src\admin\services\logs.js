import api from '@/services/api';

export const logsService = {
  async getLogs(params = {}) {
    try {
      const response = await api.get('/api/admin/logs', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch logs');
    }
  },

  async getLogById(id) {
    try {
      const response = await api.get(`/api/admin/logs/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching log:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch log');
    }
  },

  async getLogLevels() {
    try {
      const response = await api.get('/api/admin/logs/levels');
      return response.data;
    } catch (error) {
      console.error('Error fetching log levels:', error);
      // Return default levels if API fails
      return {
        data: ['Info', 'Warning', 'Error', 'Critical']
      };
    }
  },

  async getLogCategories() {
    try {
      const response = await api.get('/api/admin/logs/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching log categories:', error);
      // Return default categories if API fails
      return {
        data: ['User', 'Product', 'Order', 'Payment', 'Authentication', 'Authorization', 'System', 'Database', 'Api', 'Security']
      };
    }
  },

  async getLogStats(params = {}) {
    try {
      const response = await api.get('/api/admin/logs/stats', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching log stats:', error);
      // Return mock data if API fails
      return {
        data: {
          totalLogs: 0,
          logsByLevel: {},
          logsByCategory: {},
          logsByDay: {},
          dateRange: {
            from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            to: new Date().toISOString()
          }
        }
      };
    }
  }
};
