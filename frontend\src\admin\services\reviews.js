import api from '@/services/api';

const reviewsService = {
  async getReviews(params = {}) {
    try {
      // Очищаємо пусті параметри
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== null && value !== undefined && value !== '')
      );

      const response = await api.get('/api/admin/reviews', { params: cleanParams });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.errors?.[0] ||
                          'Failed to load reviews';
      throw new Error(errorMessage);
    }
  },

  async getReviewsWithFilters(filters = {}) {
    try {
      const params = {
        filter: filters.search,
        orderBy: filters.sortBy || 'CreatedAt',
        descending: filters.sortOrder === 'desc',
        page: filters.page,
        pageSize: filters.pageSize,
        minRating: filters.minRating,
        maxRating: filters.maxRating,
        productId: filters.productId,
        productSlug: filters.productSlug,
        userId: filters.userId,
        userEmail: filters.userEmail
      };

      return await reviewsService.getReviews(params);
    } catch (error) {
      console.error('Error fetching reviews with filters:', error);
      throw error;
    }
  },

  async getReviewById(id) {
    try {
      const response = await api.get(`/api/admin/reviews/${id}`);
      return response.data.data;
    } catch (error) {
      throw reviewsService.handleApiError(error, 'Failed to load review details');
    }
  },

  async updateReview(id, data) {
    try {
      // Ensure the data structure matches ModeratorUpdateReviewCommand
      const payload = {
        comment: data.comment
      };
      const response = await api.put(`/api/admin/reviews/${id}`, payload);
      return response.data;
    } catch (error) {
      throw reviewsService.handleApiError(error, 'Failed to update review');
    }
  },

  async deleteReview(id) {
    try {
      const response = await api.delete(`/api/admin/reviews/${id}`);
      return response.data;
    } catch (error) {
      throw reviewsService.handleApiError(error, 'Failed to delete review');
    }
  },

  async bulkDeleteReviews(ids) {
    try {
      // Ensure the data structure matches ModeratorBulkDeleteReviewsCommand
      const payload = {
        reviewIds: ids
      };
      const response = await api.post('/api/admin/reviews/bulk-delete', payload);
      return response.data;
    } catch (error) {
      throw reviewsService.handleApiError(error, 'Failed to delete reviews');
    }
  },

  async getReviewStats() {
    try {
      const response = await api.get('/api/admin/reviews/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching review stats:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.errors?.[0] ||
                          'Failed to load review statistics';
      throw new Error(errorMessage);
    }
  },

  async getAverageRating(productId = null, productSlug = null) {
    try {
      const params = {};
      if (productId) params.productId = productId;
      if (productSlug) params.productSlug = productSlug;

      const response = await api.get('/api/admin/reviews/average-rating', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching average rating:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.errors?.[0] ||
                          'Failed to load average rating';
      throw new Error(errorMessage);
    }
  },

  // Утилітарні методи для роботи з фільтрами
  buildFilterParams(filters) {
    const params = {};

    if (filters.search) params.filter = filters.search;
    if (filters.sortBy) params.orderBy = filters.sortBy;
    if (filters.sortOrder) params.descending = filters.sortOrder === 'desc';
    if (filters.page) params.page = filters.page;
    if (filters.pageSize) params.pageSize = filters.pageSize;
    if (filters.minRating !== null && filters.minRating !== undefined) params.minRating = filters.minRating;
    if (filters.maxRating !== null && filters.maxRating !== undefined) params.maxRating = filters.maxRating;
    if (filters.productId) params.productId = filters.productId;
    if (filters.productSlug) params.productSlug = filters.productSlug;
    if (filters.userId) params.userId = filters.userId;
    if (filters.userEmail) params.userEmail = filters.userEmail;

    return params;
  },

  // Покращена обробка помилок
  handleApiError(error, defaultMessage = 'Operation failed') {
    console.error('API Error:', error);

    if (error.response) {
      // Сервер відповів з кодом помилки
      const message = error.response.data?.message ||
                     error.response.data?.errors?.[0] ||
                     `Server error: ${error.response.status}`;
      return new Error(message);
    } else if (error.request) {
      // Запит був відправлений, але відповіді не було
      return new Error('Network error: No response from server');
    } else {
      // Щось інше пішло не так
      return new Error(error.message || defaultMessage);
    }
  }
};

export default reviewsService;
