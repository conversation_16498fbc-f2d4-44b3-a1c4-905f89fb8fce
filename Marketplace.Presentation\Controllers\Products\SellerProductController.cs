﻿using Marketplace.Application.Commands.Product;
using Marketplace.Application.Commands.ProductImage;
using Marketplace.Application.Queries.Product;
using Marketplace.Application.Requests.Common;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Products;

[ApiController]
[Route("api/sellers/me/products")]
[Authorize(Roles = "Seller,SellerOwner,Moderator,Admin")]
public class SellerProductController : BasicApiController
{
    private readonly IMediator _mediator;

    public SellerProductController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] PaginationRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        var query = new GetSellerProductsQuery(userId.Value, request.Filter, request.OrderBy, request.Descending, request.Page, request.PageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ProductResponse>>.SuccessWithData(response));
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        var query = new GetSellerProductQuery(id, userId.Value);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null
            ? Ok(ApiResponse<ProductResponse>.SuccessWithData(response))
            : NotFound(ApiResponse.Failure("Продукт не знайдено або ви не маєте до нього доступу."));
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] StoreProductCommand command, CancellationToken cancellationToken)
    {
        var response = await _mediator.Send(command, cancellationToken);

        return response != null
            ? CreatedAtAction(nameof(GetById), new { id = response }, ApiResponse<Guid>.SuccessWithData(response, "Продукт успішно створено."))
            : BadRequest(ApiResponse.Failure("Не вдалося створити продукт."));
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update([FromRoute] Guid id, [FromBody] UpdateSellerProductCommand command, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        var commandWithId = command with { ProductId = id, SellerId = userId.Value };
        var result = await _mediator.Send(commandWithId, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Продукт успішно оновлено."))
            : NotFound(ApiResponse.Failure("Продукт не знайдено або ви не маєте до нього доступу."));
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        var command = new DeleteProductCommand(id, userId.Value);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound(ApiResponse.Failure("Продукт не знайдено або ви не маєте до нього доступу."));
    }

    [HttpPost("{id}/images")]
    public async Task<IActionResult> UploadImage([FromRoute] Guid id, IFormFile image, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();

        // Перевіряємо, чи продукт належить поточному продавцю
        var productQuery = new GetSellerProductQuery(id, userId.Value);
        var product = await _mediator.Send(productQuery, cancellationToken);

        if (product == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено або ви не маєте до нього доступу."));
        }

        // Створюємо команду для завантаження зображення
        var command = new UploadProductImageCommand(id, image);
        var result = await _mediator.Send(command, cancellationToken);

        return result != null
            ? Ok(ApiResponse<Guid>.SuccessWithData(result.Id, "Зображення успішно завантажено."))
            : BadRequest(ApiResponse.Failure("Не вдалося завантажити зображення."));
    }

    [HttpDelete("{productId}/images/{imageId}")]
    public async Task<IActionResult> DeleteImage([FromRoute] Guid productId, [FromRoute] Guid imageId, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();

        // Перевіряємо, чи продукт належить поточному продавцю
        var productQuery = new GetSellerProductQuery(productId, userId.Value);
        var product = await _mediator.Send(productQuery, cancellationToken);

        if (product == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено або ви не маєте до нього доступу."));
        }

        // Видаляємо зображення
        var command = new DeleteProductImageCommand(productId, imageId);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound(ApiResponse.Failure("Зображення не знайдено."));
    }
}
