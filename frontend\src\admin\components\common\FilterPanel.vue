<template>
  <div class="filter-panel">
    <div class="filter-panel-header">
      <h3 class="filter-panel-title">{{ title }}</h3>
      <button 
        v-if="showClearButton" 
        class="button is-small is-text" 
        @click="clearFilters">
        Clear Filters
      </button>
    </div>
    
    <div class="filter-panel-body">
      <slot></slot>
    </div>
    
    <div v-if="showFooter" class="filter-panel-footer">
      <button 
        class="button is-primary" 
        @click="applyFilters">
        Apply Filters
      </button>
      <button 
        class="button is-light" 
        @click="clearFilters">
        Clear
      </button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: 'Filters'
  },
  showClearButton: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: <PERSON>olean,
    default: false
  }
});

const emit = defineEmits(['apply', 'clear']);

const applyFilters = () => {
  emit('apply');
};

const clearFilters = () => {
  emit('clear');
};
</script>

<style scoped>
.filter-panel {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.filter-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f1f1f1;
}

.filter-panel-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #363636;
}

.filter-panel-body {
  padding: 1rem;
}

.filter-panel-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #f1f1f1;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.button.is-text {
  text-decoration: none;
  padding: 0;
  height: auto;
  color: #7a7a7a;
}

.button.is-text:hover {
  color: #363636;
}
</style>
