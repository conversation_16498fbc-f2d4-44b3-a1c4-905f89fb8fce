﻿using Marketplace.Domain.Services;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Company;

/// <summary>
/// Обробник команди для завантаження зображення компанії
/// </summary>
public class UploadCompanyImageCommandHandler : IRequestHandler<UploadCompanyImageCommand, FileUploadResult>
{
    private readonly string _baseUrl;
    private readonly ICompanyRepository _companyRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<UploadCompanyImageCommandHandler> _logger;

    public UploadCompanyImageCommandHandler(
        IConfiguration configuration,
        ICompanyRepository companyRepository,
        IFileService fileService,
        ILogger<UploadCompanyImageCommandHandler> logger)
    {
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
        _companyRepository = companyRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadCompanyImageCommand request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи існує компанія
        var company = await _companyRepository.GetByIdAsync(request.CompanyId, cancellationToken);
        if (company == null)
        {
            throw new InvalidOperationException($"Компанію з ID {request.CompanyId} не знайдено.");
        }

        // Завантажуємо файл
        var fileUrl = await _fileService.SaveFileAsync(
            request.File.OpenReadStream(),
            request.File.FileName,
            request.File.ContentType,
            $"companies/{request.CompanyId}",
            cancellationToken);

        // Формуємо повний URL для зображення
        var fullImageUrl = fileUrl.StartsWith("http") ? fileUrl : $"{_baseUrl}/uploads/{fileUrl}";

        // Оновлюємо зображення компанії
        company.UpdateImage(new Url(fullImageUrl));

        // Зберігаємо зміни
        await _companyRepository.UpdateAsync(company, cancellationToken);

        // Повертаємо результат
        return new FileUploadResult
        {
            FileUrl = fullImageUrl,
            FileName = request.File.FileName,
            ContentType = request.File.ContentType,
            Size = request.File.Length
        };
    }
}
