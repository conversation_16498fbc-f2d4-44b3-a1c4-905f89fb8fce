import{_ as O,g as D,H as F,r as _,s as U,o,w as h,a as t,t as f,n as N,d as v,c as r,k as p,h as z,F as tt,p as at,i as et,j as nt,b as E,m as st,J as R,T as it,K as dt,f as ot,e as lt}from"./index-L-hJxM_5.js";import{A as T,a as rt}from"./AdminProductHeader-CEj_aj3M.js";import{p as M}from"./products-Bpq90UOX.js";const ut={class:"admin-product-info"},mt={class:"admin-info-section"},ct={class:"admin-info-grid"},ft={class:"admin-info-item"},gt={class:"admin-info-value"},vt={class:"admin-info-item"},pt={class:"admin-info-value admin-info-value--code"},bt={class:"admin-info-item"},yt={class:"admin-info-value admin-info-value--text"},$t={class:"admin-info-section"},kt={class:"admin-info-grid admin-info-grid--four-columns"},wt={class:"admin-info-item"},Ct={class:"admin-info-value admin-info-value--price"},ht={class:"admin-info-item"},At={class:"admin-info-value"},It={class:"admin-info-item"},Pt={key:0,class:"admin-stock-status"},Et={class:"admin-info-item"},Dt={class:"admin-info-value admin-info-value--sales"},Nt={class:"admin-info-section"},St={class:"admin-info-grid"},Ut={class:"admin-info-item"},Tt={class:"admin-info-value"},Ot={key:0},Lt={key:1},Vt={key:1},Bt={class:"admin-info-item"},jt={class:"admin-info-value"},Mt={key:0,class:"admin-category-badge"},zt={key:0},Rt={key:1},Ft={key:1},xt={class:"admin-info-section"},Ht={class:"admin-info-grid"},qt={class:"admin-info-item"},Jt={class:"admin-info-item"},Wt={class:"admin-info-value admin-info-value--date"},Kt={class:"admin-info-item"},Gt={class:"admin-info-value admin-info-value--date"},Qt={__name:"AdminProductInfoCard",props:{product:{type:Object,default:null},loading:{type:Boolean,default:!1},editable:{type:Boolean,default:!0}},emits:["edit"],setup(n,{emit:S}){const m=n,u=D(""),g=D(""),$=D(!1),y=D(!1),w=(d,s="UAH")=>!d&&d!==0?"N/A":new Intl.NumberFormat("uk-UA",{style:"currency",currency:s||"UAH"}).format(d),c=d=>d?new Date(d).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",a=d=>{switch(d){case 0:return"PENDING";case 1:return"APPROVED";case 2:return"REJECTED";default:return"UNKNOWN"}},i=d=>{switch(d){case 0:return"admin-status-badge--warning";case 1:return"admin-status-badge--success";case 2:return"admin-status-badge--danger";default:return"admin-status-badge--secondary"}},l=d=>{switch(d){case 0:return"fa-clock";case 1:return"fa-check-circle";case 2:return"fa-times-circle";default:return"fa-question-circle"}},e=d=>d===0?"admin-info-value--danger":d<10?"admin-info-value--warning":"admin-info-value--success",k=async d=>{if(!d){u.value="";return}try{$.value=!0,console.log("Loading company for ID:",d);const s=await M.getCompanies({search:"",pageSize:1e3});console.log("Companies response:",s);let A=[];s.data&&Array.isArray(s.data)?A=s.data:s&&Array.isArray(s)&&(A=s),console.log("Companies data:",A);const C=A.find(P=>P.id===d||P.id===String(d));console.log("Found company:",C),u.value=(C==null?void 0:C.name)||"Unknown Company"}catch(s){console.error("Error loading company:",s),u.value="Unknown Company"}finally{$.value=!1}},I=async d=>{if(!d){g.value="";return}try{y.value=!0,console.log("Loading category for ID:",d);const s=await M.getCategories({search:"",pageSize:1e3});console.log("Categories response:",s);let A=[];s.data&&Array.isArray(s.data)?A=s.data:s&&Array.isArray(s)&&(A=s),console.log("Categories data:",A);const C=A.find(P=>P.id===d||P.id===String(d));console.log("Found category:",C),g.value=(C==null?void 0:C.name)||"Uncategorized"}catch(s){console.error("Error loading category:",s),g.value="Uncategorized"}finally{y.value=!1}};F(()=>{var d;return(d=m.product)==null?void 0:d.companyId},d=>{d?k(d):u.value=""},{immediate:!0}),F(()=>{var d;return(d=m.product)==null?void 0:d.categoryId},d=>{d?I(d):g.value=""},{immediate:!0});const b=d=>d===0?"Out of Stock":d<10?"Low Stock":"In Stock";return(d,s)=>{const A=_("router-link");return o(),U(T,{title:"Product Information",loading:n.loading,shadow:"default"},{actions:h(()=>[n.editable?(o(),r("button",{key:0,class:"admin-btn admin-btn-xs admin-btn-secondary",onClick:s[0]||(s[0]=C=>d.$emit("edit"))},s[1]||(s[1]=[t("i",{class:"fas fa-edit"},null,-1),v(" Edit ")]))):p("",!0)]),default:h(()=>{var C,P,L,V,B,j,x,H,q,J,W,K,G,Q,Y,X,Z;return[t("div",ut,[t("div",mt,[s[5]||(s[5]=t("h4",{class:"admin-info-section-title"},"Basic Information",-1)),t("div",ct,[t("div",ft,[s[2]||(s[2]=t("span",{class:"admin-info-label"},"Name",-1)),t("span",gt,f(((C=n.product)==null?void 0:C.name)||"N/A"),1)]),t("div",vt,[s[3]||(s[3]=t("span",{class:"admin-info-label"},"Slug",-1)),t("span",pt,f(((P=n.product)==null?void 0:P.slug)||"N/A"),1)]),t("div",bt,[s[4]||(s[4]=t("span",{class:"admin-info-label"},"Description",-1)),t("div",yt,f(((L=n.product)==null?void 0:L.description)||"No description provided"),1)])])]),t("div",$t,[s[11]||(s[11]=t("h4",{class:"admin-info-section-title"},"Pricing & Inventory",-1)),t("div",kt,[t("div",wt,[s[6]||(s[6]=t("span",{class:"admin-info-label"},"Price",-1)),t("span",Ct,f(w((V=n.product)==null?void 0:V.priceAmount,(B=n.product)==null?void 0:B.priceCurrency)),1)]),t("div",ht,[s[7]||(s[7]=t("span",{class:"admin-info-label"},"Currency",-1)),t("span",At,f(((j=n.product)==null?void 0:j.priceCurrency)||"UAH"),1)]),t("div",It,[s[8]||(s[8]=t("span",{class:"admin-info-label"},"Stock Quantity",-1)),t("span",{class:N(["admin-info-value",e((x=n.product)==null?void 0:x.stock)])},[v(f(((H=n.product)==null?void 0:H.stock)??"N/A")+" ",1),((q=n.product)==null?void 0:q.stock)!==void 0?(o(),r("span",Pt," ("+f(b(n.product.stock))+") ",1)):p("",!0)],2)]),t("div",Et,[s[10]||(s[10]=t("span",{class:"admin-info-label"},"Sales",-1)),t("span",Dt,[v(f(((J=n.product)==null?void 0:J.sales)??0)+" sold ",1),s[9]||(s[9]=t("i",{class:"fas fa-chart-line admin-sales-icon"},null,-1))])])])]),t("div",Nt,[s[16]||(s[16]=t("h4",{class:"admin-info-section-title"},"Organization",-1)),t("div",St,[t("div",Ut,[s[13]||(s[13]=t("span",{class:"admin-info-label"},"Company",-1)),t("div",Tt,[(W=n.product)!=null&&W.companyId?(o(),U(A,{key:0,to:`/admin/companies/${n.product.companyId}`,class:"admin-info-link"},{default:h(()=>[s[12]||(s[12]=t("i",{class:"fas fa-building"},null,-1)),$.value?(o(),r("span",Ot,"Loading...")):(o(),r("span",Lt,f(u.value||"Unknown Company"),1))]),_:1},8,["to"])):(o(),r("span",Vt,"N/A"))])]),t("div",Bt,[s[15]||(s[15]=t("span",{class:"admin-info-label"},"Category",-1)),t("div",jt,[(K=n.product)!=null&&K.categoryId?(o(),r("span",Mt,[s[14]||(s[14]=t("i",{class:"fas fa-tag"},null,-1)),y.value?(o(),r("span",zt,"Loading...")):(o(),r("span",Rt,f(g.value||"Uncategorized"),1))])):(o(),r("span",Ft,"Uncategorized"))])])])]),t("div",xt,[s[20]||(s[20]=t("h4",{class:"admin-info-section-title"},"Status & Dates",-1)),t("div",Ht,[t("div",qt,[s[17]||(s[17]=t("span",{class:"admin-info-label"},"Status",-1)),t("span",{class:N(["admin-status-badge",i((G=n.product)==null?void 0:G.status)])},[t("i",{class:N(["fas",l((Q=n.product)==null?void 0:Q.status)])},null,2),v(" "+f(a((Y=n.product)==null?void 0:Y.status)),1)],2)]),t("div",Jt,[s[18]||(s[18]=t("span",{class:"admin-info-label"},"Created",-1)),t("span",Wt,f(c((X=n.product)==null?void 0:X.createdAt)),1)]),t("div",Kt,[s[19]||(s[19]=t("span",{class:"admin-info-label"},"Last Updated",-1)),t("span",Gt,f(c((Z=n.product)==null?void 0:Z.updatedAt)),1)])])])])]}),_:1},8,["loading"])}}},Yt=O(Qt,[["__scopeId","data-v-265a045d"]]),Xt={class:"admin-attributes-container"},Zt={key:0,class:"admin-empty-state"},_t={key:1,class:"admin-attributes-table"},ta={class:"admin-table-container"},aa={class:"admin-table"},ea={key:0,class:"admin-table-header admin-table-header--actions"},na={class:"admin-table-cell"},sa={class:"admin-attribute-key"},ia={class:"admin-table-cell"},da={class:"admin-table-cell"},oa={key:0,class:"admin-table-cell admin-table-cell--actions"},la={class:"admin-table-actions"},ra=["onClick"],ua=["onClick"],ma={key:0,class:"admin-attributes-footer"},ca={__name:"AdminProductAttributesTable",props:{attributes:{type:[String,Object,Array],default:null},loading:{type:Boolean,default:!1},editable:{type:Boolean,default:!1}},emits:["edit","add-attribute","edit-attribute","delete-attribute"],setup(n,{emit:S}){const m=n,u=z(()=>{if(!m.attributes)return[];try{if(Array.isArray(m.attributes))return m.attributes.map(a=>({key:a.key||a.name||"Unknown",value:a.value||a.val||"",type:g(a.value||a.val)}));if(typeof m.attributes=="string"){const a=JSON.parse(m.attributes);return Array.isArray(a)?a.map(i=>({key:i.key||i.name||"Unknown",value:i.value||i.val||"",type:g(i.value||i.val)})):Object.entries(a).map(([i,l])=>({key:i,value:l,type:g(l)}))}return typeof m.attributes=="object"?Object.entries(m.attributes).map(([a,i])=>({key:a,value:i,type:g(i)})):[]}catch(a){return console.error("Error parsing attributes:",a),[]}}),g=a=>a==null?"null":typeof a=="boolean"?"boolean":typeof a=="number"?"number":typeof a=="string"?a.startsWith("http://")||a.startsWith("https://")?"url":a.includes("@")&&a.includes(".")?"email":!isNaN(Date.parse(a))&&a.includes("-")?"date":"string":Array.isArray(a)?"array":typeof a=="object"?"object":"unknown",$=a=>a==null?"N/A":typeof a=="boolean"?a?"Yes":"No":Array.isArray(a)?a.join(", "):typeof a=="object"?JSON.stringify(a):String(a),y=a=>{const i=g(a);return{"admin-attribute-value--boolean":i==="boolean","admin-attribute-value--number":i==="number","admin-attribute-value--url":i==="url","admin-attribute-value--email":i==="email","admin-attribute-value--date":i==="date","admin-attribute-value--null":i==="null"}},w=a=>({"admin-type-badge--string":a==="string","admin-type-badge--number":a==="number","admin-type-badge--boolean":a==="boolean","admin-type-badge--url":a==="url","admin-type-badge--email":a==="email","admin-type-badge--date":a==="date","admin-type-badge--array":a==="array","admin-type-badge--object":a==="object","admin-type-badge--null":a==="null"}),c=a=>{switch(a){case"string":return"fa-font";case"number":return"fa-hashtag";case"boolean":return"fa-toggle-on";case"url":return"fa-link";case"email":return"fa-envelope";case"date":return"fa-calendar";case"array":return"fa-list";case"object":return"fa-code";case"null":return"fa-ban";default:return"fa-question"}};return(a,i)=>(o(),U(T,{title:"Product Attributes",loading:n.loading,shadow:"default"},{actions:h(()=>[n.editable?(o(),r("button",{key:0,class:"admin-btn admin-btn-xs admin-btn-secondary",onClick:i[0]||(i[0]=l=>a.$emit("edit"))},i[3]||(i[3]=[t("i",{class:"fas fa-edit"},null,-1),v(" Edit ")]))):p("",!0)]),default:h(()=>[t("div",Xt,[!u.value||u.value.length===0?(o(),r("div",Zt,[i[5]||(i[5]=t("div",{class:"admin-empty-icon"},[t("i",{class:"fas fa-list-ul"})],-1)),i[6]||(i[6]=t("h4",{class:"admin-empty-title"},"No Attributes",-1)),i[7]||(i[7]=t("p",{class:"admin-empty-description"}," This product doesn't have any custom attributes defined. ",-1)),n.editable?(o(),r("button",{key:0,class:"admin-btn admin-btn-primary",onClick:i[1]||(i[1]=l=>a.$emit("add-attribute"))},i[4]||(i[4]=[t("i",{class:"fas fa-plus"},null,-1),v(" Add Attribute ")]))):p("",!0)])):(o(),r("div",_t,[t("div",ta,[t("table",aa,[t("thead",null,[t("tr",null,[i[8]||(i[8]=t("th",{class:"admin-table-header"},"Attribute",-1)),i[9]||(i[9]=t("th",{class:"admin-table-header"},"Value",-1)),i[10]||(i[10]=t("th",{class:"admin-table-header"},"Type",-1)),n.editable?(o(),r("th",ea,"Actions")):p("",!0)])]),t("tbody",null,[(o(!0),r(tt,null,at(u.value,(l,e)=>(o(),r("tr",{key:e,class:"admin-table-row"},[t("td",na,[t("div",sa,[i[11]||(i[11]=t("i",{class:"fas fa-tag admin-attribute-icon"},null,-1)),v(" "+f(l.key),1)])]),t("td",ia,[t("div",{class:N(["admin-attribute-value",y(l.value)])},f($(l.value)),3)]),t("td",da,[t("span",{class:N(["admin-type-badge",w(l.type)])},[t("i",{class:N(["fas",c(l.type)])},null,2),v(" "+f(l.type),1)],2)]),n.editable?(o(),r("td",oa,[t("div",la,[t("button",{class:"admin-btn admin-btn-xs admin-btn-secondary",onClick:k=>a.$emit("edit-attribute",l,e),title:"Edit attribute"},i[12]||(i[12]=[t("i",{class:"fas fa-edit"},null,-1)]),8,ra),t("button",{class:"admin-btn admin-btn-xs admin-btn-danger",onClick:k=>a.$emit("delete-attribute",l,e),title:"Delete attribute"},i[13]||(i[13]=[t("i",{class:"fas fa-trash"},null,-1)]),8,ua)])])):p("",!0)]))),128))])])]),n.editable?(o(),r("div",ma,[t("button",{class:"admin-btn admin-btn-secondary",onClick:i[2]||(i[2]=l=>a.$emit("add-attribute"))},i[14]||(i[14]=[t("i",{class:"fas fa-plus"},null,-1),v(" Add Attribute ")]))])):p("",!0)]))])]),_:1},8,["loading"]))}},fa=O(ca,[["__scopeId","data-v-f469f075"]]),ga={key:0,class:"admin-modal-header"},va={class:"admin-modal-title"},pa={key:1,class:"admin-modal-footer"},ba={__name:"AdminModal",props:{modelValue:{type:Boolean,required:!0},title:{type:String,default:""},size:{type:String,default:"default",validator:n=>["sm","default","lg","xl","full"].includes(n)},closable:{type:Boolean,default:!0},closeOnOverlay:{type:Boolean,default:!0},closeOnEscape:{type:Boolean,default:!0},scrollable:{type:Boolean,default:!0},centered:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["update:modelValue","close","opened","closed"],setup(n,{emit:S}){const m=n,u=S,g=z(()=>({[`admin-modal--${m.size}`]:m.size!=="default","admin-modal--centered":m.centered,"admin-modal--loading":m.loading})),$=z(()=>({"admin-modal-body--scrollable":m.scrollable})),y=()=>{u("update:modelValue",!1),u("close")},w=()=>{m.closeOnOverlay&&y()},c=a=>{a.key==="Escape"&&m.closeOnEscape&&m.modelValue&&y()};return et(()=>{m.closeOnEscape&&document.addEventListener("keydown",c),m.modelValue&&(document.body.style.overflow="hidden",u("opened"))}),nt(()=>{m.closeOnEscape&&document.removeEventListener("keydown",c),document.body.style.overflow="",m.modelValue&&u("closed")}),F(()=>m.modelValue,a=>{a?(document.body.style.overflow="hidden",u("opened")):(document.body.style.overflow="",u("closed"))}),(a,i)=>(o(),U(dt,{to:"body"},[E(it,{name:"admin-modal",appear:""},{default:h(()=>[n.modelValue?(o(),r("div",{key:0,class:"admin-modal-overlay",onClick:w},[t("div",{class:N(["admin-modal",g.value]),onClick:i[0]||(i[0]=st(()=>{},["stop"]))},[n.title||a.$slots.header?(o(),r("div",ga,[R(a.$slots,"header",{},()=>[t("h3",va,f(n.title),1)],!0),n.closable?(o(),r("button",{key:0,class:"admin-modal-close",onClick:y,type:"button","aria-label":"Close modal"},i[1]||(i[1]=[t("i",{class:"fas fa-times"},null,-1)]))):p("",!0)])):p("",!0),t("div",{class:N(["admin-modal-body",$.value])},[R(a.$slots,"default",{},void 0,!0)],2),a.$slots.footer?(o(),r("div",pa,[R(a.$slots,"footer",{},void 0,!0)])):p("",!0)],2)])):p("",!0)]),_:3})]))}},ya=O(ba,[["__scopeId","data-v-4490e336"]]),$a={class:"admin-images-container"},ka={key:0,class:"admin-main-image-section"},wa={class:"admin-main-image-container"},Ca={class:"admin-image-wrapper"},ha=["src","alt"],Aa={class:"admin-image-overlay"},Ia={class:"admin-image-info"},Pa={class:"admin-image-meta"},Ea=["href"],Da={key:1,class:"admin-additional-images-section"},Na={class:"admin-image-section-title"},Sa={class:"admin-images-grid"},Ua={class:"admin-image-wrapper"},Ta=["src","alt","onClick"],Oa={class:"admin-image-overlay"},La=["onClick"],Va=["onClick"],Ba={class:"admin-image-info"},ja={class:"admin-image-meta"},Ma={class:"admin-image-value"},za={key:2,class:"admin-empty-state"},Ra={class:"admin-image-modal-content"},Fa=["src","alt"],xa={__name:"AdminProductImagesViewer",props:{product:{type:Object,default:null},loading:{type:Boolean,default:!1},editable:{type:Boolean,default:!1}},emits:["edit","upload-image"],setup(n,{emit:S}){const m=n,u=D(!1),g=D(""),$=D(""),y=z(()=>{var l;return!((l=m.product)!=null&&l.images)||!Array.isArray(m.product.images)?[]:m.product.images.map(e=>({id:e.id||e.Id,url:e.image||e.Image||e.imageUrl||e.ImageUrl,alt:e.altText||e.AltText||"Product image",isMain:e.isMain||e.IsMain||!1,order:e.order||e.Order||0})).sort((e,k)=>e.order-k.order)}),w=l=>{var e;g.value=l,$.value=((e=m.product)==null?void 0:e.name)||"Product Image",u.value=!0},c=async(l,e)=>{try{const I=await(await fetch(l)).blob(),b=window.URL.createObjectURL(I),d=document.createElement("a");d.href=b,d.download=`${e}.jpg`,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(b)}catch(k){console.error("Error downloading image:",k)}},a=l=>{l.target.src="/placeholder-image.jpg",l.target.alt="Image not available"},i=l=>l?l.length<=50?l:l.substring(0,47)+"...":"";return(l,e)=>(o(),U(T,{title:"Product Images",loading:n.loading,shadow:"default"},{actions:h(()=>[n.editable?(o(),r("button",{key:0,class:"admin-btn admin-btn-xs admin-btn-secondary",onClick:e[0]||(e[0]=k=>l.$emit("edit"))},e[8]||(e[8]=[t("i",{class:"fas fa-edit"},null,-1),v(" Edit ")]))):p("",!0)]),default:h(()=>{var k,I;return[t("div",$a,[(k=n.product)!=null&&k.metaImage?(o(),r("div",ka,[e[12]||(e[12]=t("h4",{class:"admin-image-section-title"},[t("i",{class:"fas fa-image"}),v(" Main Image ")],-1)),t("div",wa,[t("div",Ca,[t("img",{src:n.product.metaImage,alt:n.product.name||"Product image",class:"admin-main-image",onClick:e[1]||(e[1]=b=>w(n.product.metaImage)),onError:a},null,40,ha),t("div",Aa,[t("button",{class:"admin-image-action",onClick:e[2]||(e[2]=b=>w(n.product.metaImage)),title:"View full size"},e[9]||(e[9]=[t("i",{class:"fas fa-search-plus"},null,-1)])),t("button",{class:"admin-image-action",onClick:e[3]||(e[3]=b=>c(n.product.metaImage,"main-image")),title:"Download image"},e[10]||(e[10]=[t("i",{class:"fas fa-download"},null,-1)]))])]),t("div",Ia,[t("div",Pa,[e[11]||(e[11]=t("span",{class:"admin-image-label"},"URL:",-1)),t("a",{href:n.product.metaImage,target:"_blank",class:"admin-image-link",rel:"noopener noreferrer"},f(i(n.product.metaImage)),9,Ea)])])])])):p("",!0),y.value.length>0?(o(),r("div",Da,[t("h4",Na,[e[13]||(e[13]=t("i",{class:"fas fa-images"},null,-1)),v(" Additional Images ("+f(y.value.length)+") ",1)]),t("div",Sa,[(o(!0),r(tt,null,at(y.value,(b,d)=>(o(),r("div",{key:d,class:"admin-image-item"},[t("div",Ua,[t("img",{src:b.url,alt:b.alt||`Product image ${d+1}`,class:"admin-grid-image",onClick:s=>w(b.url),onError:a},null,40,Ta),t("div",Oa,[t("button",{class:"admin-image-action",onClick:s=>w(b.url),title:"View full size"},e[14]||(e[14]=[t("i",{class:"fas fa-search-plus"},null,-1)]),8,La),t("button",{class:"admin-image-action",onClick:s=>c(b.url,`image-${d+1}`),title:"Download image"},e[15]||(e[15]=[t("i",{class:"fas fa-download"},null,-1)]),8,Va)])]),t("div",Ba,[t("div",ja,[e[16]||(e[16]=t("span",{class:"admin-image-label"},"Type:",-1)),t("span",Ma,f(b.type||"Additional"),1)])])]))),128))])])):p("",!0),!((I=n.product)!=null&&I.metaImage)&&y.value.length===0?(o(),r("div",za,[e[18]||(e[18]=t("div",{class:"admin-empty-icon"},[t("i",{class:"fas fa-image"})],-1)),e[19]||(e[19]=t("h4",{class:"admin-empty-title"},"No Images",-1)),e[20]||(e[20]=t("p",{class:"admin-empty-description"}," This product doesn't have any images uploaded. ",-1)),n.editable?(o(),r("button",{key:0,class:"admin-btn admin-btn-primary",onClick:e[4]||(e[4]=b=>l.$emit("upload-image"))},e[17]||(e[17]=[t("i",{class:"fas fa-upload"},null,-1),v(" Upload Image ")]))):p("",!0)])):p("",!0)]),E(ya,{modelValue:u.value,"onUpdate:modelValue":e[7]||(e[7]=b=>u.value=b),title:$.value,size:"lg",centered:""},{footer:h(()=>[t("button",{class:"admin-btn admin-btn-secondary",onClick:e[5]||(e[5]=b=>u.value=!1)}," Close "),t("button",{class:"admin-btn admin-btn-primary",onClick:e[6]||(e[6]=b=>c(g.value,"full-size-image"))},e[21]||(e[21]=[t("i",{class:"fas fa-download"},null,-1),v(" Download ")]))]),default:h(()=>[t("div",Ra,[g.value?(o(),r("img",{key:0,src:g.value,alt:$.value,class:"admin-modal-image"},null,8,Fa)):p("",!0)])]),_:1},8,["modelValue","title"])]}),_:1},8,["loading"]))}},Ha=O(xa,[["__scopeId","data-v-54d4af9f"]]),qa={class:"admin-metadata-container"},Ja={class:"admin-metadata-section"},Wa={class:"admin-metadata-grid"},Ka={class:"admin-metadata-item"},Ga={class:"admin-metadata-value"},Qa={key:0,class:"admin-metadata-stats"},Ya={class:"admin-metadata-item"},Xa={class:"admin-metadata-value admin-metadata-value--text"},Za={key:0,class:"admin-metadata-stats"},_a={class:"admin-metadata-item admin-metadata-item--full"},te={class:"admin-metadata-value"},ae={key:0,class:"admin-meta-image-preview"},ee=["src","alt"],ne={class:"admin-meta-image-info"},se={class:"admin-meta-image-url"},ie=["href"],de={key:1,class:"admin-meta-image-placeholder"},oe={class:"admin-metadata-section"},le={class:"admin-metadata-grid"},re={class:"admin-metadata-item"},ue={class:"admin-metadata-value admin-metadata-value--code"},me={class:"admin-metadata-item"},ce={class:"admin-metadata-value admin-metadata-value--code"},fe={class:"admin-metadata-item"},ge={class:"admin-metadata-value admin-metadata-value--date"},ve={class:"admin-metadata-item"},pe={class:"admin-metadata-value admin-metadata-value--date"},be={key:0,class:"admin-metadata-section"},ye={class:"admin-url-preview"},$e={class:"admin-url-preview-item"},ke={class:"admin-url-value"},we=["href"],Ce={key:1,class:"admin-metadata-section"},he={class:"admin-seo-preview"},Ae={class:"admin-seo-preview-title"},Ie={class:"admin-seo-preview-url"},Pe={class:"admin-seo-preview-description"},Ee={__name:"AdminProductMetadataCard",props:{product:{type:Object,default:null},loading:{type:Boolean,default:!1},editable:{type:Boolean,default:!1}},emits:["edit"],setup(n,{emit:S}){const m=c=>c?new Date(c).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",u=c=>{const a=(c==null?void 0:c.length)||0;return a<=50?"admin-stat-item--success":a<=60?"admin-stat-item--warning":"admin-stat-item--danger"},g=c=>{const a=(c==null?void 0:c.length)||0;return a<=140?"admin-stat-item--success":a<=160?"admin-stat-item--warning":"admin-stat-item--danger"},$=c=>{var a;c.target.style.display="none",(a=c.target.nextElementSibling)==null||a.classList.add("admin-meta-image-error")},y=c=>`${window.location.origin}/products/${c}`,w=async c=>{try{await navigator.clipboard.writeText(c),console.log("Copied to clipboard:",c)}catch(a){console.error("Failed to copy to clipboard:",a);const i=document.createElement("textarea");i.value=c,document.body.appendChild(i),i.select(),document.execCommand("copy"),document.body.removeChild(i)}};return(c,a)=>(o(),U(T,{title:"SEO & Metadata",loading:n.loading,shadow:"default"},{actions:h(()=>[n.editable?(o(),r("button",{key:0,class:"admin-btn admin-btn-xs admin-btn-secondary",onClick:a[0]||(a[0]=i=>c.$emit("edit"))},a[4]||(a[4]=[t("i",{class:"fas fa-edit"},null,-1),v(" Edit ")]))):p("",!0)]),default:h(()=>{var i,l,e,k,I,b,d,s,A,C,P,L,V,B;return[t("div",qa,[t("div",Ja,[a[13]||(a[13]=t("h4",{class:"admin-metadata-section-title"},[t("i",{class:"fas fa-search"}),v(" SEO Information ")],-1)),t("div",Wa,[t("div",Ka,[a[6]||(a[6]=t("span",{class:"admin-metadata-label"},"Meta Title",-1)),t("div",Ga,f(((i=n.product)==null?void 0:i.metaTitle)||"No meta title set"),1),(l=n.product)!=null&&l.metaTitle?(o(),r("div",Qa,[t("span",{class:N(["admin-stat-item",u(n.product.metaTitle)])},[a[5]||(a[5]=t("i",{class:"fas fa-ruler"},null,-1)),v(" "+f(n.product.metaTitle.length)+"/60 characters ",1)],2)])):p("",!0)]),t("div",Ya,[a[8]||(a[8]=t("span",{class:"admin-metadata-label"},"Meta Description",-1)),t("div",Xa,f(((e=n.product)==null?void 0:e.metaDescription)||"No meta description set"),1),(k=n.product)!=null&&k.metaDescription?(o(),r("div",Za,[t("span",{class:N(["admin-stat-item",g(n.product.metaDescription)])},[a[7]||(a[7]=t("i",{class:"fas fa-ruler"},null,-1)),v(" "+f(n.product.metaDescription.length)+"/160 characters ",1)],2)])):p("",!0)]),t("div",_a,[a[12]||(a[12]=t("span",{class:"admin-metadata-label"},"Meta Image",-1)),t("div",te,[(I=n.product)!=null&&I.metaImage?(o(),r("div",ae,[t("img",{src:n.product.metaImage,alt:n.product.name||"Meta image",class:"admin-meta-image",onError:$},null,40,ee),t("div",ne,[t("div",se,[a[9]||(a[9]=t("i",{class:"fas fa-link"},null,-1)),t("a",{href:n.product.metaImage,target:"_blank",rel:"noopener"},f(n.product.metaImage),9,ie)]),a[10]||(a[10]=t("div",{class:"admin-meta-image-usage"},[t("i",{class:"fas fa-info-circle"}),v(" Used for social media sharing and search engine previews ")],-1))])])):(o(),r("div",de,a[11]||(a[11]=[t("i",{class:"fas fa-image"},null,-1),t("span",null,"No meta image set",-1),t("small",null,"Add a meta image to improve social media sharing",-1)])))])])])]),t("div",oe,[a[20]||(a[20]=t("h4",{class:"admin-metadata-section-title"},[t("i",{class:"fas fa-cog"}),v(" Technical Information ")],-1)),t("div",le,[t("div",re,[a[15]||(a[15]=t("span",{class:"admin-metadata-label"},"Product ID",-1)),t("div",ue,f(((b=n.product)==null?void 0:b.id)||"N/A"),1),(d=n.product)!=null&&d.id?(o(),r("button",{key:0,class:"admin-copy-btn",onClick:a[1]||(a[1]=j=>w(n.product.id)),title:"Copy ID"},a[14]||(a[14]=[t("i",{class:"fas fa-copy"},null,-1)]))):p("",!0)]),t("div",me,[a[17]||(a[17]=t("span",{class:"admin-metadata-label"},"Slug",-1)),t("div",ce,f(((s=n.product)==null?void 0:s.slug)||"No slug set"),1),(A=n.product)!=null&&A.slug?(o(),r("button",{key:0,class:"admin-copy-btn",onClick:a[2]||(a[2]=j=>w(n.product.slug)),title:"Copy slug"},a[16]||(a[16]=[t("i",{class:"fas fa-copy"},null,-1)]))):p("",!0)]),t("div",fe,[a[18]||(a[18]=t("span",{class:"admin-metadata-label"},"Created Date",-1)),t("div",ge,f(m((C=n.product)==null?void 0:C.createdAt)),1)]),t("div",ve,[a[19]||(a[19]=t("span",{class:"admin-metadata-label"},"Last Modified",-1)),t("div",pe,f(m((P=n.product)==null?void 0:P.updatedAt)),1)])])]),(L=n.product)!=null&&L.slug?(o(),r("div",be,[a[24]||(a[24]=t("h4",{class:"admin-metadata-section-title"},[t("i",{class:"fas fa-link"}),v(" URL Preview ")],-1)),t("div",ye,[t("div",$e,[a[23]||(a[23]=t("span",{class:"admin-url-label"},"Product URL:",-1)),t("div",ke,[t("a",{href:y(n.product.slug),target:"_blank",class:"admin-url-link",rel:"noopener noreferrer"},[v(f(y(n.product.slug))+" ",1),a[21]||(a[21]=t("i",{class:"fas fa-external-link-alt"},null,-1))],8,we),t("button",{class:"admin-copy-btn",onClick:a[3]||(a[3]=j=>w(y(n.product.slug))),title:"Copy URL"},a[22]||(a[22]=[t("i",{class:"fas fa-copy"},null,-1)]))])])])])):p("",!0),(V=n.product)!=null&&V.metaTitle||(B=n.product)!=null&&B.metaDescription?(o(),r("div",Ce,[a[25]||(a[25]=t("h4",{class:"admin-metadata-section-title"},[t("i",{class:"fab fa-google"}),v(" Search Engine Preview ")],-1)),t("div",he,[t("div",Ae,f(n.product.metaTitle||n.product.name||"Product Title"),1),t("div",Ie,f(y(n.product.slug||"product-slug")),1),t("div",Pe,f(n.product.metaDescription||n.product.description||"Product description will appear here..."),1)])])):p("",!0)])]}),_:1},8,["loading"]))}},De=O(Ee,[["__scopeId","data-v-344b2878"]]),Ne={class:"admin-page-container"},Se={key:0,class:"admin-loading-state"},Ue={key:1,class:"admin-error-state"},Te={class:"admin-error-content"},Oe={class:"admin-error-message"},Le={key:2,class:"admin-page-content"},Ve={class:"admin-content-grid"},Be={key:3,class:"admin-not-found-state"},je={class:"admin-not-found-content"},Me={__name:"ProductView",setup(n){const S=ot(),m=lt(),u=D(null),g=D(!1),$=D(null),y=z(()=>u.value?{...u.value}:null),w=async()=>{try{g.value=!0,$.value=null;const l=S.params.id,e=await M.getProductByIdWithImages(l);u.value=e.data||e}catch(l){console.error("Error fetching product:",l),$.value=l.message||"Failed to load product details"}finally{g.value=!1}},c=()=>{m.push(`/admin/products/${u.value.id}/edit`)},a=async()=>{if(confirm("Are you sure you want to delete this product?"))try{await M.deleteProduct(u.value.id),m.push("/admin/products")}catch(l){console.error("Error deleting product:",l),$.value="Failed to delete product"}},i=async()=>{var l;try{const e={...u.value,name:`${u.value.name} (Copy)`,slug:`${u.value.slug}-copy`,id:void 0,createdAt:void 0,updatedAt:void 0},k=await M.createProduct(e),I=((l=k.data)==null?void 0:l.id)||k.id;m.push(`/admin/products/${I}/edit`)}catch(e){console.error("Error duplicating product:",e),$.value="Failed to duplicate product"}};return et(()=>{w()}),(l,e)=>{const k=_("router-link");return o(),r("div",Ne,[g.value?(o(),r("div",Se,e[0]||(e[0]=[t("div",{class:"admin-loading-spinner"},[t("i",{class:"fas fa-spinner fa-spin"})],-1),t("p",{class:"admin-loading-text"},"Loading product details...",-1)]))):$.value?(o(),r("div",Ue,[E(T,{variant:"danger",title:"Error Loading Product"},{default:h(()=>[t("div",Te,[e[2]||(e[2]=t("i",{class:"fas fa-exclamation-triangle admin-error-icon"},null,-1)),t("p",Oe,f($.value),1),t("button",{class:"admin-btn admin-btn-primary",onClick:w},e[1]||(e[1]=[t("i",{class:"fas fa-redo"},null,-1),v(" Try Again ")]))])]),_:1})])):u.value?(o(),r("div",Le,[E(rt,{mode:"view",product:u.value,title:u.value.name,subtitle:`Product ID: ${u.value.id}`,onDelete:a,onDuplicate:i},null,8,["product","title","subtitle"]),t("div",Ve,[E(Yt,{product:y.value,loading:g.value,onEdit:c},null,8,["product","loading"]),E(fa,{attributes:u.value.attributes,loading:g.value},null,8,["attributes","loading"]),E(Ha,{product:u.value,loading:g.value},null,8,["product","loading"]),E(De,{product:u.value,loading:g.value},null,8,["product","loading"])])])):(o(),r("div",Be,[E(T,{variant:"warning",title:"Product Not Found"},{default:h(()=>[t("div",je,[e[4]||(e[4]=t("i",{class:"fas fa-search admin-not-found-icon"},null,-1)),e[5]||(e[5]=t("p",{class:"admin-not-found-message"}," The requested product could not be found or may have been deleted. ",-1)),E(k,{to:"/admin/products",class:"admin-btn admin-btn-primary"},{default:h(()=>e[3]||(e[3]=[t("i",{class:"fas fa-arrow-left"},null,-1),v(" Back to Products ")])),_:1})])]),_:1})]))])}}},xe=O(Me,[["__scopeId","data-v-3ee1804c"]]);export{xe as default};
