import axios from 'axios';

class UkrPoshtaService {
  constructor() {
    // API ключ Укрпошти (в реальному проекті має бути в .env файлі)
    this.apiKey = 'your-ukrposhta-api-key'; // Потрібно отримати на сайті Укрпошти
    this.baseURL = 'https://www.ukrposhta.ua/api/';
    this.statusCheckURL = 'https://track.ukrposhta.ua/api/';
  }

  /**
   * Отримує список міст та населених пунктів
   * @param {string} query - Пошуковий запит
   * @returns {Promise} - Список міст
   */
  async getCities(query = '') {
    try {
      // Розширений список міст України для демонстрації
      const cities = [
        { id: '8000000000', name: '<PERSON><PERSON><PERSON><PERSON>', region: 'Київська область' },
        { id: '6100000000', name: 'Хар<PERSON>ів', region: 'Харківська область' },
        { id: '5100000000', name: 'Одеса', region: 'Одеська область' },
        { id: '1200000000', name: 'Дніпро', region: 'Дніпропетровська область' },
        { id: '1400000000', name: 'Донецьк', region: 'Донецька область' },
        { id: '2600000000', name: 'Запоріжжя', region: 'Запорізька область' },
        { id: '4600000000', name: 'Львів', region: 'Львівська область' },
        { id: '3200000000', name: 'Кривий Ріг', region: 'Дніпропетровська область' },
        { id: '4800000000', name: 'Миколаїв', region: 'Миколаївська область' },
        { id: '4400000000', name: 'Луганськ', region: 'Луганська область' },
        { id: '0500000000', name: 'Вінниця', region: 'Вінницька область' },
        { id: '7400000000', name: 'Полтава', region: 'Полтавська область' },
        { id: '7100000000', name: 'Чернігів', region: 'Чернігівська область' },
        { id: '7300000000', name: 'Черкаси', region: 'Черкаська область' },
        { id: '1800000000', name: 'Житомир', region: 'Житомирська область' },
        { id: '5900000000', name: 'Суми', region: 'Сумська область' },
        { id: '6500000000', name: 'Херсон', region: 'Херсонська область' },
        { id: '6800000000', name: 'Хмельницький', region: 'Хмельницька область' },
        { id: '7700000000', name: 'Чернівці', region: 'Чернівецька область' },
        { id: '2100000000', name: 'Ужгород', region: 'Закарпатська область' },
        { id: '2300000000', name: 'Івано-Франківськ', region: 'Івано-Франківська область' },
        { id: '4400000000', name: 'Луцьк', region: 'Волинська область' },
        { id: '3500000000', name: 'Кропивницький', region: 'Кіровоградська область' },
        { id: '5600000000', name: 'Рівне', region: 'Рівненська область' },
        { id: '6800000000', name: 'Тернопіль', region: 'Тернопільська область' }
      ];

      // Симулюємо затримку API
      await new Promise(resolve => setTimeout(resolve, 200));

      if (query && query.length > 0) {
        const searchTerm = query.toLowerCase();
        const filteredCities = cities.filter(city =>
          city.name.toLowerCase().includes(searchTerm) ||
          city.region.toLowerCase().includes(searchTerm)
        );
        return filteredCities;
      }

      return cities;
    } catch (error) {
      console.error('UkrPoshta getCities error:', error);
      throw error;
    }
  }

  /**
   * Отримує список відділень поштового зв'язку
   * @param {string} cityId - ID міста
   * @returns {Promise} - Список відділень
   */
  async getPostOffices(cityId) {
    try {
      // Симулюємо затримку API
      await new Promise(resolve => setTimeout(resolve, 400));

      // Генеруємо більше відділень для демонстрації
      const mockOffices = [
        {
          id: `${cityId}_001`,
          name: 'Відділення поштового зв\'язку №1',
          address: 'вул. Центральна, 1',
          phone: '+380 44 123-45-67',
          workingHours: 'Пн-Пт: 8:00-18:00, Сб: 9:00-15:00',
          services: ['Посилки', 'Листи', 'Грошові перекази']
        },
        {
          id: `${cityId}_002`,
          name: 'Відділення поштового зв\'язку №2',
          address: 'вул. Поштова, 15',
          phone: '+380 44 123-45-68',
          workingHours: 'Пн-Пт: 9:00-17:00, Сб: 10:00-14:00',
          services: ['Посилки', 'Листи', 'Платежі']
        },
        {
          id: `${cityId}_003`,
          name: 'Відділення поштового зв\'язку №3',
          address: 'пр. Миру, 25',
          phone: '+380 44 123-45-69',
          workingHours: 'Пн-Пт: 8:30-17:30, Сб: 9:00-13:00',
          services: ['Посилки', 'Експрес-доставка']
        },
        {
          id: `${cityId}_004`,
          name: 'Відділення поштового зв\'язку №4',
          address: 'вул. Шевченка, 42',
          phone: '+380 44 123-45-70',
          workingHours: 'Пн-Пт: 8:00-19:00, Сб: 9:00-16:00',
          services: ['Посилки', 'Листи', 'Платежі', 'Пенсії']
        },
        {
          id: `${cityId}_005`,
          name: 'Відділення поштового зв\'язку №5',
          address: 'пр. Незалежності, 88',
          phone: '+380 44 123-45-71',
          workingHours: 'Пн-Пт: 9:00-18:00, Сб: 10:00-15:00',
          services: ['Посилки', 'Експрес-доставка', 'Грошові перекази']
        },
        {
          id: `${cityId}_006`,
          name: 'Відділення поштового зв\'язку №6',
          address: 'вул. Перемоги, 156',
          phone: '+380 44 123-45-72',
          workingHours: 'Пн-Пт: 8:30-17:30, Сб: 9:30-14:30',
          services: ['Посилки', 'Листи', 'Комунальні платежі']
        }
      ];

      return mockOffices;
    } catch (error) {
      console.error('UkrPoshta getPostOffices error:', error);
      throw error;
    }
  }

  /**
   * Розраховує вартість доставки
   * @param {Object} params - Параметри для розрахунку
   * @returns {Promise} - Вартість доставки
   */
  async calculateDeliveryCost(params) {
    try {
      const {
        fromCityId,
        toCityId,
        weight = 1, // кг
        declaredValue = 100, // грн
        serviceType = 'standard' // standard, express
      } = params;

      // Базова вартість залежно від типу послуги
      let baseCost = serviceType === 'express' ? 60 : 40;
      
      // Додаткова вартість за вагу (понад 1 кг)
      if (weight > 1) {
        baseCost += (weight - 1) * 10;
      }

      // Додаткова вартість за оголошену цінність (1% від вартості)
      const insuranceCost = declaredValue * 0.01;

      const totalCost = baseCost + insuranceCost;

      return {
        cost: Math.round(totalCost * 100) / 100,
        baseCost,
        insuranceCost,
        weight,
        estimatedDays: serviceType === 'express' ? 2 : 5
      };
    } catch (error) {
      console.error('UkrPoshta calculateDeliveryCost error:', error);
      // Повертаємо стандартну ціну у випадку помилки
      return { cost: 40, baseCost: 40, insuranceCost: 0, estimatedDays: 5 };
    }
  }

  /**
   * Відстежує посилку за номером
   * @param {string} trackingNumber - Номер для відстеження
   * @returns {Promise} - Інформація про відстеження
   */
  async trackPackage(trackingNumber) {
    try {
      // В реальному проекті тут був би запит до API відстеження Укрпошти
      const mockTracking = {
        number: trackingNumber,
        status: 'В дорозі',
        statusCode: 'IN_TRANSIT',
        events: [
          {
            date: '2024-01-15 10:30',
            status: 'Прийнято до перевезення',
            location: 'Відділення №1, м. Київ'
          },
          {
            date: '2024-01-15 14:20',
            status: 'Відправлено з відділення',
            location: 'Відділення №1, м. Київ'
          },
          {
            date: '2024-01-16 09:15',
            status: 'Прибуло в сортувальний центр',
            location: 'Сортувальний центр, м. Київ'
          },
          {
            date: '2024-01-16 18:45',
            status: 'В дорозі',
            location: 'Між сортувальними центрами'
          }
        ],
        estimatedDelivery: '2024-01-18',
        recipient: {
          city: 'Харків',
          postOffice: 'Відділення №5'
        }
      };

      return mockTracking;
    } catch (error) {
      console.error('UkrPoshta trackPackage error:', error);
      throw error;
    }
  }

  /**
   * Отримує тарифи для різних типів послуг
   * @returns {Promise} - Список тарифів
   */
  async getTariffs() {
    try {
      return {
        standard: {
          name: 'Стандартна доставка',
          baseCost: 40,
          maxWeight: 20, // кг
          estimatedDays: '3-5',
          description: 'Звичайна поштова доставка'
        },
        express: {
          name: 'Експрес доставка',
          baseCost: 60,
          maxWeight: 10, // кг
          estimatedDays: '1-2',
          description: 'Прискорена доставка'
        },
        registered: {
          name: 'Рекомендована пошта',
          baseCost: 45,
          maxWeight: 20, // кг
          estimatedDays: '3-5',
          description: 'З повідомленням про вручення'
        }
      };
    } catch (error) {
      console.error('UkrPoshta getTariffs error:', error);
      throw error;
    }
  }

  /**
   * Форматує адресу відділення
   * @param {Object} office - Об'єкт відділення
   * @returns {string} - Форматована адреса
   */
  formatOfficeAddress(office) {
    return `${office.name} - ${office.address}`;
  }

  /**
   * Перевіряє доступність API
   * @returns {Promise<boolean>} - Статус доступності
   */
  async checkApiAvailability() {
    try {
      await this.getCities('Київ');
      return true;
    } catch (error) {
      console.warn('UkrPoshta API недоступний:', error.message);
      return false;
    }
  }
}

export default new UkrPoshtaService();
