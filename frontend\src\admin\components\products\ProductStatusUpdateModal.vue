<template>
  <AdminModal 
    v-model="isOpen"
    title="Update Product Status"
    size="default"
    :closable="!updating"
    :close-on-overlay="!updating"
    :close-on-escape="!updating"
    @close="handleClose"
  >
    <div class="admin-status-update-content">
      <!-- Product Information -->
      <div v-if="product" class="admin-product-info">
        <div class="admin-product-summary">
          <div class="admin-product-avatar">
            <img 
              v-if="product.metaImage" 
              :src="product.metaImage" 
              :alt="product.name"
              class="admin-product-image"
              @error="handleImageError"
            />
            <div v-else class="admin-product-placeholder">
              <i class="fas fa-box"></i>
            </div>
          </div>
          <div class="admin-product-details">
            <h4 class="admin-product-name">{{ product.name }}</h4>
            <div class="admin-product-meta">
              <span class="admin-product-id">ID: {{ product.id }}</span>
              <span v-if="product.companyName" class="admin-product-company">
                <i class="fas fa-building"></i>
                {{ product.companyName }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="admin-current-status">
          <span class="admin-status-label">Current Status:</span>
          <span class="admin-status-badge" :class="getStatusClass(product.status)">
            <i class="fas" :class="getStatusIcon(product.status)"></i>
            {{ formatStatus(product.status) }}
          </span>
        </div>
      </div>

      <!-- Status Update Form -->
      <form @submit.prevent="handleSubmit" class="admin-status-form">
        <div class="admin-form-section">
          <h5 class="admin-form-section-title">
            <i class="fas fa-edit"></i>
            Update Status
          </h5>
          
          <div class="admin-form-row">
            <div class="admin-form-col">
              <label class="admin-form-label admin-form-label--required">Product Status</label>
              <select
                v-model="formData.status"
                class="admin-form-select"
                :class="{ 'admin-form-select--error': errors.status }"
                required
                :disabled="updating"
              >
                <option value="">Select status...</option>
                <option value="0">Pending Review</option>
                <option value="1">Approved</option>
                <option value="2">Rejected</option>
              </select>
              <div v-if="errors.status" class="admin-form-error">
                <i class="fas fa-exclamation-triangle"></i>
                {{ errors.status }}
              </div>
              <div class="admin-form-help">
                Choose the appropriate status for this product
              </div>
            </div>
          </div>

          <!-- Status Change Reason (for rejected products) -->
          <div v-if="formData.status === '2'" class="admin-form-row">
            <div class="admin-form-col">
              <label class="admin-form-label">Rejection Reason</label>
              <textarea
                v-model="formData.rejectionReason"
                class="admin-form-textarea"
                placeholder="Please provide a reason for rejecting this product..."
                rows="3"
                :disabled="updating"
              ></textarea>
              <div class="admin-form-help">
                This reason will be visible to the product owner
              </div>
            </div>
          </div>

          <!-- Admin Notes -->
          <div class="admin-form-row">
            <div class="admin-form-col">
              <label class="admin-form-label">Admin Notes (Optional)</label>
              <textarea
                v-model="formData.adminNotes"
                class="admin-form-textarea"
                placeholder="Internal notes about this status change..."
                rows="2"
                :disabled="updating"
              ></textarea>
              <div class="admin-form-help">
                These notes are for internal use only
              </div>
            </div>
          </div>
        </div>

        <!-- Status Change Preview -->
        <div v-if="formData.status && formData.status !== String(product?.status)" class="admin-status-preview">
          <h5 class="admin-preview-title">
            <i class="fas fa-eye"></i>
            Status Change Preview
          </h5>
          <div class="admin-status-change">
            <div class="admin-status-from">
              <span class="admin-status-label">From:</span>
              <span class="admin-status-badge" :class="getStatusClass(product?.status)">
                <i class="fas" :class="getStatusIcon(product?.status)"></i>
                {{ formatStatus(product?.status) }}
              </span>
            </div>
            <div class="admin-status-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
            <div class="admin-status-to">
              <span class="admin-status-label">To:</span>
              <span class="admin-status-badge" :class="getStatusClass(parseInt(formData.status))">
                <i class="fas" :class="getStatusIcon(parseInt(formData.status))"></i>
                {{ formatStatus(parseInt(formData.status)) }}
              </span>
            </div>
          </div>
        </div>
      </form>
    </div>

    <template #footer>
      <button 
        type="button" 
        class="admin-btn admin-btn-secondary"
        @click="handleClose"
        :disabled="updating"
      >
        Cancel
      </button>
      <button 
        type="button"
        class="admin-btn admin-btn-primary"
        :class="{ 'admin-btn--loading': updating }"
        @click="handleSubmit"
        :disabled="!canSubmit || updating"
      >
        <i v-if="updating" class="fas fa-spinner fa-spin"></i>
        <i v-else class="fas fa-save"></i>
        {{ updating ? 'Updating...' : 'Update Status' }}
      </button>
    </template>
  </AdminModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import AdminModal from '../common/AdminModal.vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'updated', 'close']);

// Reactive data
const updating = ref(false);
const errors = ref({});

// Form data
const formData = ref({
  status: '',
  rejectionReason: '',
  adminNotes: ''
});

// Computed
const isOpen = computed({
  get: () => {
    console.log('Modal isOpen getter:', props.modelValue);
    return props.modelValue;
  },
  set: (value) => {
    console.log('Modal isOpen setter:', value);
    emit('update:modelValue', value);
  }
});

const canSubmit = computed(() => {
  return formData.value.status && 
         formData.value.status !== String(props.product?.status) &&
         !updating.value;
});

// Methods
const resetForm = () => {
  formData.value = {
    status: props.product?.status?.toString() || '',
    rejectionReason: '',
    adminNotes: ''
  };
  errors.value = {};
};

const validateForm = () => {
  errors.value = {};
  
  if (!formData.value.status) {
    errors.value.status = 'Status selection is required';
  }
  
  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }
  
  try {
    updating.value = true;
    
    const updateData = {
      status: parseInt(formData.value.status),
      rejectionReason: formData.value.rejectionReason || null,
      adminNotes: formData.value.adminNotes || null
    };
    
    const result = await productsService.updateProductStatus(props.product.id, updateData);
    
    emit('updated', {
      product: props.product,
      newStatus: parseInt(formData.value.status),
      result
    });
    
    handleClose();
  } catch (error) {
    console.error('Error updating product status:', error);
    errors.value.general = error.message || 'Failed to update product status';
  } finally {
    updating.value = false;
  }
};

const handleClose = () => {
  if (!updating.value) {
    emit('close');
    emit('update:modelValue', false);
  }
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// Status formatting methods
const formatStatus = (status) => {
  switch (status) {
    case 0: return 'PENDING';
    case 1: return 'APPROVED';
    case 2: return 'REJECTED';
    default: return 'UNKNOWN';
  }
};

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'admin-status-badge--warning';
    case 1: return 'admin-status-badge--success';
    case 2: return 'admin-status-badge--danger';
    default: return 'admin-status-badge--secondary';
  }
};

const getStatusIcon = (status) => {
  switch (status) {
    case 0: return 'fa-clock';
    case 1: return 'fa-check-circle';
    case 2: return 'fa-times-circle';
    default: return 'fa-question-circle';
  }
};

// Watchers
watch(() => props.product, (newProduct) => {
  if (newProduct) {
    resetForm();
  }
}, { immediate: true });

watch(() => props.modelValue, (newValue) => {
  if (newValue && props.product) {
    resetForm();
  }
});
</script>

<style scoped>
.admin-status-update-content {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

/* Product Info */
.admin-product-info {
  padding: var(--admin-space-lg);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
}

.admin-product-summary {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
  margin-bottom: var(--admin-space-md);
}

.admin-product-avatar {
  width: 60px;
  height: 60px;
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  background: var(--admin-bg-tertiary);
  flex-shrink: 0;
}

.admin-product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-product-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-text-muted);
  font-size: var(--admin-text-lg);
}

.admin-product-details {
  flex: 1;
  min-width: 0;
}

.admin-product-name {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-xs) 0;
}

.admin-product-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-md);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

.admin-product-id {
  font-family: var(--admin-font-mono);
}

.admin-product-company {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-current-status {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  padding-top: var(--admin-space-md);
  border-top: 1px solid var(--admin-border-light);
}

.admin-status-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-muted);
}

/* Form */
.admin-status-form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-form-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-form-section-title {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-form-section-title i {
  color: var(--admin-primary);
}

.admin-form-row {
  display: flex;
  gap: var(--admin-space-md);
}

.admin-form-col {
  flex: 1;
  min-width: 0;
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-form-select,
.admin-form-textarea {
  width: 100%;
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-form-select:focus,
.admin-form-textarea:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-form-select--error,
.admin-form-textarea--error {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-form-textarea {
  resize: vertical;
  min-height: 80px;
}

.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-danger);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
}

/* Status Preview */
.admin-status-preview {
  padding: var(--admin-space-lg);
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
}

.admin-preview-title {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-md) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-preview-title i {
  color: var(--admin-info);
}

.admin-status-change {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-status-from,
.admin-status-to {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-status-arrow {
  color: var(--admin-primary);
  font-size: var(--admin-text-lg);
}

/* Status Badge */
.admin-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-status-badge--success {
  background: var(--admin-success);
  color: var(--admin-text-white);
}

.admin-status-badge--warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-status-badge--danger {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

.admin-status-badge--secondary {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-product-summary {
    flex-direction: column;
    text-align: center;
  }
  
  .admin-product-meta {
    justify-content: center;
  }
  
  .admin-current-status {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .admin-status-change {
    flex-direction: column;
    gap: var(--admin-space-sm);
  }
  
  .admin-status-arrow {
    transform: rotate(90deg);
  }
}
</style>
