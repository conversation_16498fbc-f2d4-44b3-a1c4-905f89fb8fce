<template>
  <div class="attributes-test">
    <h2>Attributes Test</h2>
    
    <div class="test-section">
      <h3>Test with String Attributes (from DB)</h3>
      <SimpleProductAttributesEditor
        v-model="stringAttributes"
        @change="onStringAttributesChange"
      />
      <pre>{{ JSON.stringify(stringAttributes, null, 2) }}</pre>
    </div>
    
    <div class="test-section">
      <h3>Test with Object Attributes</h3>
      <SimpleProductAttributesEditor
        v-model="objectAttributes"
        @change="onObjectAttributesChange"
      />
      <pre>{{ JSON.stringify(objectAttributes, null, 2) }}</pre>
    </div>
    
    <div class="test-section">
      <h3>Test with Mixed Attributes</h3>
      <SimpleProductAttributesEditor
        v-model="mixedAttributes"
        @change="onMixedAttributesChange"
      />
      <pre>{{ JSON.stringify(mixedAttributes, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h3>Test with Empty Attributes</h3>
      <SimpleProductAttributesEditor
        v-model="emptyAttributes"
        @change="onEmptyAttributesChange"
      />
      <pre>{{ JSON.stringify(emptyAttributes, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SimpleProductAttributesEditor from './SimpleProductAttributesEditor.vue';

// Test data that might come from database (like your example)
const stringAttributes = ref({
  "Вага": "4",
  "Колір": "к",
  "Розміри": "7",
  "Матеріал": "W"
});

const objectAttributes = ref({
  "Color": ["Red", "Blue", "Green"],
  "Size": ["S", "M", "L", "XL"],
  "Material": ["Cotton", "Polyester"]
});

const mixedAttributes = ref({
  "Brand": "Nike",
  "Colors": ["Red", "Blue"],
  "Sizes": "S,M,L,XL",
  "Weight": 500
});

const emptyAttributes = ref({});

const onStringAttributesChange = (attributes) => {
  console.log('String attributes changed:', attributes);
};

const onObjectAttributesChange = (attributes) => {
  console.log('Object attributes changed:', attributes);
};

const onMixedAttributesChange = (attributes) => {
  console.log('Mixed attributes changed:', attributes);
};

const onEmptyAttributesChange = (attributes) => {
  console.log('Empty attributes changed:', attributes);
};
</script>

<style scoped>
.attributes-test {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 3rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

h2, h3 {
  margin-bottom: 1rem;
}

pre {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  margin-top: 1rem;
}
</style>
