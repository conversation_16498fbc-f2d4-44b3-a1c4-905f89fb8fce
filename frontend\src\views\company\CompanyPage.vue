<template>
  <div class="company-page">
    <div class="container">
      <div class="breadcrumbs">
        <span class="fas fa-house"></span> / Компанія
      </div>
      <div class="company-header-row">
        <div class="company-header-left">
          <div class="avatar-section">
            <div class="avatar-container">
              <img class="avatar" :src="companyImageUrl" alt="Company logo" />
            </div>
            <div class="company-info">
              <div class="company-name">
                {{ companyData ? companyData.name : company.name }}
              </div>
              <div class="company-header-value">{{ companyData?.contactEmail  || company.contactEmail }}</div>
            </div>
          </div>
        </div>
        <div class="company-header-center">
          <div class="company-header-label">На Klondike</div>
          <div class="company-header-value">
            з {{ companyStats ? companyStats.memberSince : company.registrationDate }}
          </div>
        </div>
        <div class="company-header-right">
          <div class="company-header-label">Товарів у продажу</div>
          <div class="company-header-value">{{ companyStats ? companyStats.approvedProducts : '...' }}</div>
        </div>
      </div>
      <!-- Company Rating Block -->
      <div class="company-rating-block">
        <div class="company-rating-title">Загальний<br>рейтинг компанії</div>
        <div v-if="companyRating" class="company-rating-summary">
          <div class="company-rating-score">{{ companyRating.averageRating.toFixed(1) }}</div>
          <div class="company-rating-stars">
            <template v-for="n in 5" :key="n">
              <svg v-if="n <= Math.floor(companyRating.averageRating)" :key="'full'+n" width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg" class="star-svg">
                <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z" fill="#FF9800"/>
              </svg>
              <svg v-else-if="n - companyRating.averageRating <= 0.5" :key="'half'+n" width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg" class="star-svg">
                <defs>
                  <linearGradient id="half">
                    <stop offset="50%" stop-color="#FF9800"/>
                    <stop offset="50%" stop-color="#E0E0E0"/>
                  </linearGradient>
                </defs>
                <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z" fill="url(#half)"/>
              </svg>
              <svg v-else :key="'empty'+n" width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg" class="star-svg">
                <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z" fill="#E0E0E0"/>
              </svg>
            </template>
          </div>
          <div class="rating-count">({{ companyRating.totalRatings }} відгуків)</div>
        </div>
        <div v-else class="company-rating-summary">
          <div class="company-rating-score">0.0</div>
          <div class="company-rating-stars">
            <template v-for="n in 5" :key="n">
              <svg width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg" class="star-svg">
                <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z" fill="#E0E0E0"/>
              </svg>
            </template>
          </div>
          <div class="rating-count">(0 відгуків)</div>
        </div>
        <div v-if="companyRating" class="company-rating-bars">
          <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="rating-bar-row">
            <span class="bar-label">{{ rating }}</span>
            <svg class="bar-star" width="18" height="18" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z"
                fill="#FF9800" stroke="#FF9800" stroke-width="1.5"/>
              <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z"
                fill="none" stroke="#C7C7C7" stroke-width="1.5"/>
            </svg>
            <div class="bar-bg">
              <div class="bar-fill" :style="{ width: (companyRating.totalRatings ? ((companyRating.ratingDistribution[rating] || 0) / companyRating.totalRatings * 100) : 0) + '%' }"></div>
            </div>
            <span class="bar-count">{{ companyRating.ratingDistribution[rating] || 0 }}</span>
          </div>
        </div>
        <div v-else class="company-rating-bars">
          <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="rating-bar-row">
            <span class="bar-label">{{ rating }}</span>
            <svg class="bar-star" width="18" height="18" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2L17.09 9.26L25 10.27L18.5 15.97L20.18 23.72L14 19.77L7.82 23.72L9.5 15.97L3 10.27L10.91 9.26L14 2Z"
                fill="#E0E0E0" stroke="#E0E0E0" stroke-width="1.5"/>
            </svg>
            <div class="bar-bg">
              <div class="bar-fill" style="width: 0%"></div>
            </div>
            <span class="bar-count">0</span>
          </div>
        </div>
      </div>
      <div class="company-products-title">
        Товари компанії
      </div>
      <section class="company-products-section">
      <div class="container">
        <ProductGrid
                :company-slug="companySlug"
                :fetch-params="companyProductsFetchParams"
                :show-pagination="true"
                :show-actions="false"
                :grid-columns="4"
                :auto-fetch="true"
                :empty-message="'У цього продавця поки немає товарів.'"
                :on-product-click="openProduct"
              />
      </div>
    </section>
    </div>
  </div>
</template>

<script setup>
import ProductGrid from '@/components/common/ProductGrid.vue';
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import companyService from '../../services/company.service';

const route = useRoute();
const router = useRouter();

// Fetch parameters for ProductGrid
const companyProductsFetchParams = ref({
  pageSize: 8,
  orderBy: 'CreatedAt',
  descending: true
});

// Company data
const companyData = ref(null);
const companyLoading = ref(false);
const companyStats = ref(null);
const companyRating = ref(null);

// Helper function to format date
function formatDate(dateString) {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('uk-UA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// Get company slug from route params
const companySlug = computed(() => route.params.slug);

// Extract company ID from slug (remove "company-" prefix)
const companyId = computed(() => {
  if (companySlug.value && companySlug.value.startsWith('company-')) {
    return companySlug.value.replace('company-', '');
  }
  return companySlug.value;
});

// Default fallback image URL
const defaultCompanyLogo = 'https://i.pinimg.com/736x/b5/97/93/b5979307abd0afa2313423c5fea16ad5.jpg';

// Reactive variable for company image URL
const companyImageUrl = ref(defaultCompanyLogo);

// Get company image URL
function getCompanyImageUrl() {
  if (companyData.value?.imageUrl && companyData.value.imageUrl !== null) {
    // Якщо є URL зображення з API, використовуємо його
    companyImageUrl.value = companyData.value.imageUrl;
    return companyData.value.imageUrl;
  } else if (companyData.value?.id) {
    // Асинхронно завантажуємо URL зображення
    loadCompanyImage(companyData.value.id);
    return companyImageUrl.value;
  } else {
    // Fallback зображення
    return defaultCompanyLogo;
  }
}

// Асинхронно завантажує URL зображення компанії
async function loadCompanyImage(companyId) {
  try {
    const apiUrl = import.meta.env.VUE_APP_API_URL || 'http://localhost:5296';
    const response = await fetch(`${apiUrl}/api/universal/images/company/${companyId}`);

    if (response.ok) {
      const data = await response.json();

      // Backend повертає JSON з структурою { success: true, data: [...] }
      let imageUrl = null;
      if (data.success && data.data && data.data.length > 0) {
        // Для компанії беремо перше зображення
        imageUrl = data.data[0].url;
      } else if (data.data) {
        imageUrl = data.data;
      }

      if (imageUrl) {
        companyImageUrl.value = imageUrl;
      } else {
        // Якщо зображення не знайдено, залишаємо fallback
        console.log(`No image found for company ${companyId}, using fallback`);
      }
    } else {
      console.warn(`Image not found for company ${companyId}: ${response.status}`);
      // Залишаємо fallback зображення
    }
  } catch (error) {
    console.error('Error loading company image:', companyId, error);
    // Залишаємо fallback зображення
  }
}

// Watch for changes in companyData to load image
watch(companyData, (newData) => {
  console.log('companyData changed:', newData);
  if (newData) {
    console.log('Calling getCompanyImageUrl for company:', newData.id);
    getCompanyImageUrl();
  }
}, { immediate: true });

// Load company profile data
async function loadCompanyProfile() {
  console.log('=== loadCompanyProfile START ===');
  console.log('companySlug.value:', companySlug.value);
  console.log('typeof companySlug.value:', typeof companySlug.value);

  if (!companySlug.value) {
    console.log('No company slug, returning');
    return;
  }

  companyLoading.value = true;
  console.log('companyLoading set to true');

  try {
    console.log('About to call companyService.getCompanyProfile with:', companySlug.value);
    console.log('companyService:', companyService);
    console.log('companyService.getCompanyProfile:', companyService.getCompanyProfile);

    const response = await companyService.getCompanyProfile(companySlug.value);
    console.log('Company profile response received:', response);
    console.log('Response data:', response.data);
    companyData.value = response.data;
  } catch (error) {
    console.error('Error loading company profile:', error);
    console.error('Error details:', error.message, error.stack);
    companyData.value = null;
  } finally {
    companyLoading.value = false;
    console.log('companyLoading set to false');
  }
  console.log('=== loadCompanyProfile END ===');
}

// ProductGrid now handles loading automatically

// Handle product click
function openProduct(product) {
  if (product.slug) {
    router.push(`/product/${product.slug}`);
  }
}

// ProductGrid handles all product interactions automatically

// Mock company data (fallback)
const company = {
  logo: defaultCompanyLogo,
  name: 'Компанія Приклад',
  description: 'Опис компанії',
  rating: 1.6,
  ratingCounts: [0, 8, 0, 1, 52], // 5★, 4★, 3★, 2★, 1★
  registrationDate: '1 січня 2023 року'
};



// Load company statistics
async function loadCompanyStats() {
  if (!companySlug.value) return;

  try {
    const response = await companyService.getCompanyStats(companySlug.value);
    companyStats.value = response.data;
    console.log('Company stats:', companyStats.value);
  } catch (error) {
    console.error('Error loading company stats:', error);
    companyStats.value = null;
  }
}

// Load company rating
async function loadCompanyRating() {
  if (!companySlug.value) return;

  try {
    const response = await companyService.getCompanyRating(companySlug.value);
    companyRating.value = response.data;
    console.log('Company rating:', companyRating.value);
  } catch (error) {
    console.error('Error loading company rating:', error);
    companyRating.value = null;
  }
}

// Load data on mount
onMounted(() => {
  console.log('onMounted called, route.params:', route.params);
  console.log('companySlug.value:', companySlug.value);
  console.log('companyId.value:', companyId.value);
  loadCompanyProfile();
  loadCompanyStats();
  loadCompanyRating();
});
</script>

<style scoped>
.company-page {
  background: #fff;
  min-height: 100vh;
  font-family: 'Rubik', Arial, sans-serif;
}
.container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 40px 0;
}
.breadcrumbs {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;
}

.company-header-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 60px;
  margin-bottom: 32px;
  position: sticky;
  width: 100%;
  top: 70px;
  z-index: 10;
  background: #fff;
  border-top: 7px solid white;
  border-bottom: 5px solid white;
}
.company-header-left {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}
.company-header-center, .company-header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 220px;
  margin-left: 40px;
}
.company-header-label {
  color: #888;
  font-size: 16px;
  margin-bottom: 8px;
}
.company-header-value {
  color: #222;
  font-size: 16px;
  font-weight: 500;
  margin-top: 3px;
}
.avatar-section {
  display: flex;
  align-items: center;
  position: relative;
  padding: 20px 24px;
  background: #fff;
  border-radius: 16px;
  height: 120px;
  overflow: visible;
}
.avatar-container {
  position: relative;
  margin-right: 20px;
  padding-left: 100px;
  z-index: 2;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar-container::before {
  content: '';
  position: absolute;
  top: 58%;
  left: 80%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #ff9800 0%, #ff5722 100%);
  border-radius: 50%;
  filter: blur(4px);
  opacity: 0.8;
  z-index: -1;
}
.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  display: block;
  border: 3px solid #ff5722;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.company-info {
  flex: 1;
  z-index: 2;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 80px;
}
.company-name {
  font-weight: 700;
  font-size: 24px;
  line-height: 1.2;
  margin-bottom: 4px;
  color: #222;
}
.company-description {
  font-size: 16px;
  color: #000000;
  font-weight: 400;
}

/* Company Rating Block Styles */

.company-rating-block {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 48px 0 0 0;
  font-family: 'Rubik', Arial, sans-serif;
  width: 100%;
}
.company-rating-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 220px;
  margin-right: 32px;
}
.company-rating-title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  margin-bottom: 18px;
  text-align: left;
  line-height: 1.2;
  font-family: 'Rubik', Arial, sans-serif;
  padding-bottom: 100px;
  margin-right: 150px;
}
.company-rating-score {
  font-size: 71px;
  color: #2979e7;
  font-weight: 400;
  margin-bottom: 12px;
  text-align: center;
  font-family: 'Rubik', Arial, sans-serif;
  margin-right: 170px;
}
.company-rating-stars {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 2px;
  margin-bottom: 8px;
  min-height: 26px;
  margin-right: 170px;
}
.star-svg {
  width: 28px;
  height: 26px;
  display: inline-block;
}
.company-rating-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 320px;
  margin-top: 0;
  margin-right: 40px;
}

.rating-bar-row {
  display: flex;
  align-items: center;
  font-size: 17px;
  gap: 8px;
  height: 26px;
  margin-bottom: 2px;
}
.bar-label {
  width: 18px;
  text-align: right;
  color: #222;
  font-weight: 600;
  font-size: 17px;
  margin-right: 2px;
}
.bar-star {
  width: 18px;
  height: 18px;
  margin-right: 2px;
  flex-shrink: 0;
  display: inline-block;
  vertical-align: middle;
}
.bar-bg {
  width: 200px;
  height: 10px;
  background: #eee;
  border-radius: 5px;
  margin: 0 8px;
  position: relative;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}
.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff9800 0%, #ff9800 100%);
  border-radius: 5px;
  transition: width 0.3s;
}
.bar-count {
  min-width: 18px;
  text-align: left;
  color: #222;
  font-size: 15px;
  font-weight: 400;
  margin-left: 2px;
}
.company-products-title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  margin-bottom: 18px;
  line-height: 1.2;
}

.products-count {
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin-left: 8px;
  font-family: 'Rubik', Arial, sans-serif;
  margin-left: 130px;
  margin-top: 25px;
}
</style>
<style>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700&display=swap');
</style>