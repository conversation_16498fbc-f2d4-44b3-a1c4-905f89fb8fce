﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.ProductImage;

/// <summary>
/// Обробник команди для завантаження зображення для 
/// </summary>
public class DeleteProductImageCommandHandler : IRequestHandler<DeleteProductImageCommand, bool>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<DeleteProductImageCommandHandler> _logger;

    public DeleteProductImageCommandHandler(
        IProductImageRepository productImageRepository,
        IFileService fileService,
        ILogger<DeleteProductImageCommandHandler> logger)
    {
        _productImageRepository = productImageRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteProductImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо зображення продукту
        var productImages = await _productImageRepository.GetByProductIdAsync(request.productId, cancellationToken);
        if (productImages == null)
            throw new InvalidOperationException($"Зображення для продукту з ID {request.productId} не знайдено.");

        var productImage = productImages.FirstOrDefault(x => x.Id == request.imageId);
        if (productImage == null)
            throw new InvalidOperationException($"Зображення з ID {request.imageId} для продукту з ID {request.productId} не знайдено.");

        // Видаляємо фізичний файл з диску
        await _fileService.DeleteFileAsync(
            productImage.Image!.Value,
            cancellationToken);

        // Видаляємо запис з бази даних
        await _productImageRepository.DeleteAsync(productImage.Id, cancellationToken);

        return true;

        // Оновлюємо 
        await _productImageRepository.DeleteAsync(productImage.Id, cancellationToken);

        // Повертаємо результат
        return true;
    }
}
