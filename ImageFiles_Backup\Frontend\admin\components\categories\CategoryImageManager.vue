<template>
  <div class="category-image-manager">
    <div class="image-section-header">
      <h4 class="image-section-title">
        <i class="fas fa-image"></i>
        Category Image
      </h4>
      <p class="image-section-description">
        Upload an image to represent this category. This image will be displayed in category listings and navigation.
      </p>
    </div>

    <!-- Current Image -->
    <div v-if="currentImage" class="current-image-section">
      <label class="section-label">Current Image</label>
      <div class="current-image-wrapper">
        <div class="image-preview-card">
          <img 
            :src="currentImage" 
            :alt="'Category image'"
            class="current-image"
            @error="handleImageError"
          />
          <div class="image-overlay">
            <button 
              class="image-btn image-btn-view"
              @click="openImageModal"
              title="View full size">
              <i class="fas fa-search-plus"></i>
            </button>
            <button 
              class="image-btn image-btn-delete"
              @click="confirmDelete"
              title="Delete image">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload Section -->
    <div class="upload-section">
      <label class="section-label">
        {{ currentImage ? 'Replace Image' : 'Upload Image' }}
      </label>
      
      <!-- Upload Methods -->
      <div class="upload-methods">
        <div class="upload-method-tabs">
          <button 
            class="upload-tab"
            :class="{ 'upload-tab-active': uploadMethod === 'file' }"
            @click="uploadMethod = 'file'">
            <i class="fas fa-upload"></i>
            Upload File
          </button>
          <button 
            class="upload-tab"
            :class="{ 'upload-tab-active': uploadMethod === 'url' }"
            @click="uploadMethod = 'url'">
            <i class="fas fa-link"></i>
            Image URL
          </button>
        </div>

        <!-- File Upload -->
        <div v-if="uploadMethod === 'file'" class="upload-method-content">
          <div
            class="drop-zone"
            :class="{ 
              'drop-zone-dragover': isDragOver, 
              'drop-zone-disabled': isUploading 
            }"
            @drop="handleDrop"
            @dragover.prevent="handleDragOver"
            @dragleave="handleDragLeave"
            @click="triggerFileInput">
            
            <input 
              ref="fileInput"
              type="file" 
              accept="image/*,.jfif" 
              @change="handleFileSelect"
              :disabled="isUploading"
              style="display: none;">

            <div class="drop-zone-content">
              <div class="drop-zone-icon">
                <i class="fas fa-cloud-upload-alt"></i>
              </div>
              <div class="drop-zone-text">
                <p class="drop-zone-primary">Drop image here or click to browse</p>
                <p class="drop-zone-secondary">Supports: JPG, PNG, GIF, WebP (max 5MB)</p>
              </div>
            </div>
          </div>

          <!-- Upload Progress -->
          <div v-if="isUploading" class="upload-progress">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
            </div>
            <p class="progress-text">Uploading... {{ uploadProgress }}%</p>
          </div>
        </div>

        <!-- URL Input -->
        <div v-if="uploadMethod === 'url'" class="upload-method-content">
          <div class="url-input-wrapper">
            <input 
              class="url-input" 
              type="url" 
              placeholder="https://example.com/image.jpg" 
              v-model="imageUrl"
              @input="handleUrlInput">
            <button 
              class="url-preview-btn"
              @click="previewUrl"
              :disabled="!imageUrl || isUploading">
              <i class="fas fa-eye"></i>
              Preview
            </button>
          </div>
          <p class="url-help">Enter a direct link to an image file</p>
          
          <!-- URL Preview -->
          <div v-if="urlPreview" class="url-preview">
            <img :src="urlPreview" @error="handleUrlPreviewError" @load="handleUrlPreviewLoad">
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Image Preview -->
    <div v-if="pendingImage" class="pending-image-section">
      <label class="section-label">New Image (will be uploaded on save)</label>
      <div class="pending-image-wrapper">
        <div class="image-preview-card">
          <img 
            :src="pendingImage.preview" 
            :alt="'New category image'"
            class="pending-image"
          />
          <div class="image-overlay">
            <button 
              class="image-btn image-btn-delete"
              @click="removePendingImage"
              title="Remove new image">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="pending-image-info">
          <p class="pending-image-name">{{ pendingImage.name }}</p>
          <p class="pending-image-size">{{ formatFileSize(pendingImage.size) }}</p>
        </div>
      </div>
    </div>

    <!-- Image Modal -->
    <div class="image-modal" :class="{ 'image-modal-active': showImageModal }" @click="closeImageModal">
      <div class="image-modal-content" @click.stop>
        <button class="image-modal-close" @click="closeImageModal">
          <i class="fas fa-times"></i>
        </button>
        <img :src="currentImage" :alt="'Category image full size'" class="image-modal-img">
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="delete-modal" :class="{ 'delete-modal-active': showDeleteModal }">
      <div class="delete-modal-backdrop" @click="cancelDelete"></div>
      <div class="delete-modal-content">
        <div class="delete-modal-header">
          <h3 class="delete-modal-title">Delete Category Image</h3>
        </div>
        <div class="delete-modal-body">
          <p>Are you sure you want to delete this category image? This action cannot be undone.</p>
        </div>
        <div class="delete-modal-footer">
          <button class="delete-btn delete-btn-danger" @click="deleteImage" :disabled="isDeleting">
            <i class="fas fa-spinner fa-spin" v-if="isDeleting"></i>
            <i class="fas fa-trash" v-else></i>
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
          <button class="delete-btn delete-btn-secondary" @click="cancelDelete">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { categoriesService } from '@/admin/services/categories';

// Props
const props = defineProps({
  categoryId: {
    type: [String, Number],
    default: null
  },
  currentImage: {
    type: String,
    default: ''
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'image-updated',
  'image-uploaded', 
  'image-deleted',
  'pending-image-changed'
]);

// Reactive data
const uploadMethod = ref('file');
const isDragOver = ref(false);
const isUploading = ref(false);
const uploadProgress = ref(0);
const imageUrl = ref('');
const urlPreview = ref('');
const pendingImage = ref(null);
const showImageModal = ref(false);
const showDeleteModal = ref(false);
const isDeleting = ref(false);

// Component refs
const fileInput = ref(null);

// Methods
const triggerFileInput = () => {
  if (!isUploading.value) {
    fileInput.value?.click();
  }
};

const handleDragOver = (e) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const handleDrop = (e) => {
  e.preventDefault();
  isDragOver.value = false;
  
  const files = e.dataTransfer.files;
  if (files.length > 0) {
    processFile(files[0]);
  }
};

const handleFileSelect = (e) => {
  const files = e.target.files;
  if (files.length > 0) {
    processFile(files[0]);
  }
};

const processFile = async (file) => {
  // Validate file size (5MB)
  if (file.size > 5 * 1024 * 1024) {
    alert('File is too large. Maximum size is 5MB.');
    return;
  }

  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    alert('Invalid file type. Please select a valid image file.');
    return;
  }

  if (props.isCreate) {
    // Store for later upload
    const reader = new FileReader();
    reader.onload = (e) => {
      pendingImage.value = {
        file: file,
        name: file.name,
        size: file.size,
        preview: e.target.result
      };
      emit('pending-image-changed', pendingImage.value);
    };
    reader.readAsDataURL(file);
  } else {
    // Upload immediately for edit mode
    await uploadImage(file);
  }
};

const uploadImage = async (file) => {
  if (!props.categoryId) return;

  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    // Simulate progress
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10;
      }
    }, 100);

    const response = await categoriesService.uploadCategoryImage(props.categoryId, file);
    
    clearInterval(progressInterval);
    uploadProgress.value = 100;

    if (response && response.success) {
      emit('image-uploaded', response.data);
      emit('image-updated');
    }
  } catch (error) {
    console.error('Error uploading category image:', error);
    alert('Failed to upload image. Please try again.');
  } finally {
    isUploading.value = false;
    uploadProgress.value = 0;
  }
};

const handleUrlInput = () => {
  urlPreview.value = '';
};

const previewUrl = () => {
  if (imageUrl.value) {
    urlPreview.value = imageUrl.value;
  }
};

const handleUrlPreviewError = () => {
  alert('Failed to load image from URL. Please check the URL and try again.');
  urlPreview.value = '';
};

const handleUrlPreviewLoad = () => {
  if (props.isCreate) {
    pendingImage.value = {
      url: imageUrl.value,
      name: 'Image from URL',
      size: 0,
      preview: imageUrl.value
    };
    emit('pending-image-changed', pendingImage.value);
  } else {
    // For edit mode, save the URL directly
    emit('image-uploaded', { url: imageUrl.value });
    emit('image-updated');
  }
};

const removePendingImage = () => {
  pendingImage.value = null;
  emit('pending-image-changed', null);
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
};

const openImageModal = () => {
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
};

const confirmDelete = () => {
  showDeleteModal.value = true;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
};

const deleteImage = async () => {
  if (!props.categoryId) return;

  isDeleting.value = true;
  try {
    await categoriesService.deleteCategoryImage(props.categoryId);
    emit('image-deleted');
    emit('image-updated');
    showDeleteModal.value = false;
  } catch (error) {
    console.error('Error deleting category image:', error);
    alert('Failed to delete image. Please try again.');
  } finally {
    isDeleting.value = false;
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Expose methods for parent component
const uploadPendingImages = async (categoryId) => {
  if (pendingImage.value && pendingImage.value.file) {
    await uploadImage(pendingImage.value.file);
    pendingImage.value = null;
  }
};

defineExpose({
  uploadPendingImages
});
</script>

<style scoped>
/* Base Styles */
.category-image-manager {
  width: 100%;
}

.image-section-header {
  margin-bottom: 1.5rem;
}

.image-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.image-section-title i {
  color: #6b7280;
}

.image-section-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.section-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.75rem;
}

/* Current Image */
.current-image-section {
  margin-bottom: 2rem;
}

.current-image-wrapper {
  display: flex;
  justify-content: flex-start;
}

.image-preview-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.image-preview-card:hover {
  transform: translateY(-2px);
}

.current-image,
.pending-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-preview-card:hover .image-overlay {
  opacity: 1;
}

.image-btn {
  background: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
}

.image-btn:hover {
  transform: scale(1.1);
}

.image-btn-view {
  color: #3b82f6;
}

.image-btn-delete {
  color: #ef4444;
}

/* Upload Section */
.upload-section {
  margin-bottom: 2rem;
}

.upload-methods {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.upload-method-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.upload-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.upload-tab:hover {
  background: #e5e7eb;
}

.upload-tab-active {
  background: white;
  color: #3b82f6;
  font-weight: 500;
}

.upload-method-content {
  padding: 1.5rem;
}

/* Drop Zone */
.drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafafa;
}

.drop-zone:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.drop-zone-dragover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.drop-zone-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.drop-zone-icon {
  font-size: 2rem;
  color: #6b7280;
}

.drop-zone-text {
  text-align: center;
}

.drop-zone-primary {
  font-size: 1rem;
  color: #374151;
  margin: 0 0 0.25rem 0;
}

.drop-zone-secondary {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Upload Progress */
.upload-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.5rem 0 0 0;
}

/* URL Input */
.url-input-wrapper {
  display: flex;
  gap: 0.5rem;
}

.url-input {
  flex: 1;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.url-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.url-preview-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.url-preview-btn:hover {
  background: #2563eb;
}

.url-preview-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.url-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0.5rem 0 0 0;
}

.url-preview {
  margin-top: 1rem;
  text-align: center;
}

.url-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Pending Image */
.pending-image-section {
  margin-bottom: 2rem;
}

.pending-image-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.pending-image-info {
  flex: 1;
}

.pending-image-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 0.25rem 0;
}

.pending-image-size {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

/* Image Modal */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.image-modal-active {
  opacity: 1;
  visibility: visible;
}

.image-modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.image-modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #374151;
  transition: all 0.2s ease;
}

.image-modal-close:hover {
  background: #f3f4f6;
}

.image-modal-img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Delete Modal */
.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.delete-modal-active {
  opacity: 1;
  visibility: visible;
}

.delete-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.delete-modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 90%;
  position: relative;
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.delete-modal-active .delete-modal-content {
  transform: scale(1);
}

.delete-modal-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
}

.delete-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.delete-modal-body {
  padding: 1rem 1.5rem;
}

.delete-modal-body p {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.delete-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.delete-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delete-btn-danger {
  background: #ef4444;
  color: white;
}

.delete-btn-danger:hover {
  background: #dc2626;
}

.delete-btn-danger:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.delete-btn-secondary:hover {
  background: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .current-image,
  .pending-image {
    width: 150px;
    height: 150px;
  }

  .pending-image-wrapper {
    flex-direction: column;
  }

  .upload-method-content {
    padding: 1rem;
  }

  .drop-zone {
    padding: 1.5rem 1rem;
  }

  .url-input-wrapper {
    flex-direction: column;
  }

  .url-preview-btn {
    align-self: flex-start;
  }

  .delete-modal-footer {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .current-image,
  .pending-image {
    width: 120px;
    height: 120px;
  }

  .upload-method-tabs {
    flex-direction: column;
  }

  .upload-tab {
    border-bottom: 1px solid #e5e7eb;
  }

  .upload-tab:last-child {
    border-bottom: none;
  }
}
</style>
