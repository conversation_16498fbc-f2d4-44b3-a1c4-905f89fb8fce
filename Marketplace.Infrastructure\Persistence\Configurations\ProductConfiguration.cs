using Marketplace.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Marketplace.Infrastructure.Persistence.Configurations;
class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.HasKey(p => p.Id);

        //builder.Property(p => p.Id)
        //    .ValueGeneratedNever();

        builder.Property(c => c.CompanyId)
            .IsRequired();

        builder.HasOne(p => p.Company)
            .WithMany(/*c => c.Products*/)
            .HasForeignKey(p => p.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.SellerId)
            .IsRequired();

        builder.HasOne(p => p.Seller)
            .WithMany()
            .HasForeignKey(p => p.SellerId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(c => c.CategoryId)
            .IsRequired();

        builder.HasOne(p => p.Category)
            .WithMany(/*c => c.Products*/)
            .HasForeignKey(p => p.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(c => c.ApprovedByUserId)
            .IsRequired(false);

        builder.HasOne(p => p.ApprovedByUser)
            .WithMany()
            .HasForeignKey(p => p.ApprovedByUserId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Property(p => p.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.OwnsOne(p => p.Slug);

        builder.Property(p => p.Description)
            .HasMaxLength(500)
            .IsRequired();

        builder.OwnsOne(p => p.Price, p =>
        {
            p.Property(pr => pr.Currency)
                .HasConversion<string>()
                .IsRequired();

            p.Property(pr => pr.Amount)
                .IsRequired();
        });

        builder.Property(p => p.Stock)
            .IsRequired();

        builder.Property(p => p.Sales)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(p => p.Status)
            .HasConversion<string>()
            .IsRequired();

        builder.Property(p => p.IsApproved)
            .HasDefaultValue(false);

        builder.Property(p => p.CreatedAt)
            .IsRequired();

        builder.Property(p => p.UpdatedAt)
            .IsRequired(false);

        builder.OwnsOne(c => c.Meta, m =>
        {
            m.Property(mt => mt.Title)
                .HasColumnName("MetaTitle")
                .HasMaxLength(100)
                .IsRequired();

            m.Property(mt => mt.Description)
                .HasColumnName("MetaDescription")
                .HasMaxLength(500)
                .IsRequired();

            m.OwnsOne(mt => mt.Image, mi =>
            {
                mi.Property(i => i.Value)
                    .HasColumnName("MetaImage")
                    .HasMaxLength(255)
                    .IsRequired(false);
            });
        });

        // Configure relationship with ProductImages
        builder.HasMany(p => p.Images)
            .WithOne(pi => pi.Product)
            .HasForeignKey(pi => pi.ProductId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
