<template>
  <div class="simple-image-uploader">
    <div class="admin-uploader-header">
      <h4 class="admin-uploader-title">
        <i class="fas fa-images"></i>
        {{ title }}
      </h4>
      <span v-if="multiple" class="admin-uploader-count">
        {{ images.length }} image{{ images.length !== 1 ? 's' : '' }}
      </span>
    </div>

    <!-- Upload Area -->
    <div class="admin-upload-area">
      <div
        class="admin-drop-zone"
        :class="{ 'admin-drop-zone--dragover': isDragOver, 'admin-drop-zone--disabled': uploading }"
        @drop="handleDrop"
        @dragover.prevent="handleDragOver"
        @dragleave="handleDragLeave"
        @click.prevent="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          :multiple="multiple"
          @change="handleFileSelect"
          :disabled="uploading"
          style="display: none;"
        />
        
        <div class="admin-drop-content">
          <i class="fas fa-cloud-upload-alt admin-upload-icon" v-if="!uploading"></i>
          <i class="fas fa-spinner fa-spin admin-upload-icon" v-else></i>
          <p class="admin-upload-text">
            <strong v-if="!uploading">{{ multiple ? 'Drop images here or click to browse' : 'Drop image here or click to browse' }}</strong>
            <strong v-else>Uploading...</strong>
          </p>
          <p class="admin-upload-subtext">
            Supports: JPG, PNG, GIF, WebP (max 5MB each)
          </p>
        </div>
      </div>
    </div>

    <!-- Images Preview -->
    <div v-if="images.length > 0" class="admin-images-preview">
      <div class="admin-images-grid" :class="{ 'admin-images-grid--single': !multiple }">
        <div
          v-for="(image, index) in images"
          :key="image.id || index"
          class="admin-image-item"
        >
          <div class="admin-image-wrapper">
            <img
              :src="getImageUrl(image)"
              :alt="image.alt || `Image ${index + 1}`"
              class="admin-image-preview"
              @error="handleImageError"
            />
            <div class="admin-image-overlay">
              <button
                type="button"
                class="admin-btn admin-btn-xs admin-btn-danger admin-image-remove"
                @click.prevent="removeImage(index)"
                title="Remove image"
              >
                <i class="fas fa-trash"></i>
              </button>
              <button
                v-if="multiple && index > 0"
                type="button"
                class="admin-btn admin-btn-xs admin-btn-secondary admin-image-move"
                @click.prevent="moveImageUp(index)"
                title="Move left"
              >
                <i class="fas fa-arrow-left"></i>
              </button>
              <button
                v-if="multiple && index < images.length - 1"
                type="button"
                class="admin-btn admin-btn-xs admin-btn-secondary admin-image-move"
                @click.prevent="moveImageDown(index)"
                title="Move right"
              >
                <i class="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>
          <div v-if="multiple" class="admin-image-info">
            <span class="admin-image-name">{{ getImageName(image) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="admin-empty-state">
      <i class="fas fa-images admin-empty-icon"></i>
      <p class="admin-empty-text">{{ multiple ? 'No images uploaded' : 'No image uploaded' }}</p>
    </div>

    <!-- Upload Progress -->
    <div v-if="uploading" class="admin-upload-progress">
      <div class="admin-progress-bar">
        <div class="admin-progress-fill" :style="{ width: uploadProgress + '%' }"></div>
      </div>
      <p class="admin-progress-text">Uploading... {{ uploadProgress }}%</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: 'Images'
  },
  multiple: {
    type: Boolean,
    default: true
  },
  maxFiles: {
    type: Number,
    default: 10
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'upload', 'remove']);

// State
const images = ref([...props.modelValue]);
const uploading = ref(false);
const uploadProgress = ref(0);
const isDragOver = ref(false);
const fileInput = ref(null);

// Methods
const triggerFileInput = (event) => {
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }
  if (!uploading.value) {
    fileInput.value?.click();
  }
};

const handleFileSelect = (event) => {
  event.preventDefault();
  event.stopPropagation();

  const files = Array.from(event.target.files);
  uploadFiles(files);
  event.target.value = '';
};

const handleDrop = (event) => {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = false;

  if (uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files).filter(file => 
    file.type.startsWith('image/')
  );
  
  uploadFiles(files);
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = true;
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const uploadFiles = async (files) => {
  console.log('uploadFiles called with:', files);

  if (files.length === 0) {
    console.log('No files to upload');
    return;
  }

  // Check file limits
  if (!props.multiple && files.length > 1) {
    console.log('Single file mode, taking first file only');
    files = [files[0]];
  }

  if (props.multiple && images.value.length + files.length > props.maxFiles) {
    console.log('File limit exceeded');
    alert(`Maximum ${props.maxFiles} images allowed`);
    return;
  }

  console.log('Starting upload process');
  uploading.value = true;
  uploadProgress.value = 0;
  
  try {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert(`File ${file.name} is too large. Maximum size is 5MB.`);
        continue;
      }
      
      // Create image object
      const imageObj = {
        id: Date.now() + i,
        file: file,
        url: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        uploaded: false
      };
      
      if (props.multiple) {
        images.value.push(imageObj);
      } else {
        images.value = [imageObj];
      }
      
      // Update progress
      uploadProgress.value = Math.round(((i + 1) / files.length) * 100);
    }
    
    console.log('Upload process completed successfully');
    updateModelValue();
    emit('upload', files);

  } catch (error) {
    console.error('Upload error:', error);
    console.error('Error stack:', error.stack);
    alert(`Upload failed: ${error.message}. Please try again.`);
  } finally {
    console.log('Upload process finished');
    uploading.value = false;
    uploadProgress.value = 0;
  }
};

const removeImage = (index) => {
  const removedImage = images.value[index];
  images.value.splice(index, 1);
  updateModelValue();
  emit('remove', removedImage, index);
};

const moveImageUp = (index) => {
  if (index > 0) {
    const temp = images.value[index];
    images.value[index] = images.value[index - 1];
    images.value[index - 1] = temp;
    updateModelValue();
  }
};

const moveImageDown = (index) => {
  if (index < images.value.length - 1) {
    const temp = images.value[index];
    images.value[index] = images.value[index + 1];
    images.value[index + 1] = temp;
    updateModelValue();
  }
};

const getImageUrl = (image) => {
  return image.url || image.src || image.path || '';
};

const getImageName = (image) => {
  return image.name || image.filename || `Image ${images.value.indexOf(image) + 1}`;
};

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.png';
};

const updateModelValue = () => {
  emit('update:modelValue', [...images.value]);
};

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  images.value = [...newValue];
}, { deep: true });
</script>

<style scoped>
.simple-image-uploader {
  width: 100%;
}

.admin-uploader-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-md);
}

.admin-uploader-title {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin: 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-uploader-count {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
  background: var(--admin-bg-secondary);
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm);
}

.admin-upload-area {
  margin-bottom: var(--admin-space-md);
}

.admin-drop-zone {
  border: 2px dashed var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--admin-transition-base);
  background: var(--admin-bg-secondary);
}

.admin-drop-zone:hover:not(.admin-drop-zone--disabled) {
  border-color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.05);
}

.admin-drop-zone--dragover {
  border-color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.1);
}

.admin-drop-zone--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.admin-upload-icon {
  font-size: 3rem;
  color: var(--admin-text-secondary);
  margin-bottom: var(--admin-space-md);
}

.admin-upload-text {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-upload-subtext {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}

.admin-images-preview {
  margin-bottom: var(--admin-space-md);
}

.admin-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--admin-space-md);
}

.admin-images-grid--single {
  grid-template-columns: 200px;
  justify-content: center;
}

.admin-image-item {
  position: relative;
}

.admin-image-wrapper {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  border: 1px solid var(--admin-border-light);
}

.admin-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-xs);
  opacity: 0;
  transition: opacity var(--admin-transition-base);
}

.admin-image-wrapper:hover .admin-image-overlay {
  opacity: 1;
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border: none;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  cursor: pointer;
  transition: all var(--admin-transition-base);
}

.admin-btn-xs {
  width: 24px;
  height: 24px;
  padding: 0;
}

.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
}

.admin-btn-secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
}

.admin-image-info {
  margin-top: var(--admin-space-xs);
  text-align: center;
}

.admin-image-name {
  font-size: var(--admin-text-xs);
  color: var(--admin-text-secondary);
  word-break: break-all;
}

.admin-empty-state {
  text-align: center;
  padding: var(--admin-space-xl);
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--admin-space-md);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: var(--admin-text-base);
}

.admin-upload-progress {
  margin-top: var(--admin-space-md);
}

.admin-progress-bar {
  width: 100%;
  height: 8px;
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-sm);
  overflow: hidden;
}

.admin-progress-fill {
  height: 100%;
  background: var(--admin-primary);
  transition: width var(--admin-transition-base);
}

.admin-progress-text {
  text-align: center;
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}
</style>
