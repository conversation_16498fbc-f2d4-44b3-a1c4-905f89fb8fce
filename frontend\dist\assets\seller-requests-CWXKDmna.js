import{q as a}from"./index-L-hJxM_5.js";const d={async getSellerRequests(s={}){var e,r;try{const t="/api/admin/seller-requests",c=Object.fromEntries(Object.entries(s).filter(([n,l])=>l!==""&&l!==null&&l!==void 0));console.log(`🌐 Making API call to ${t} with params:`,s),console.log("🧹 Clean params:",c);const o=await a.get(t,{params:c});if(console.log("🌐 Full API response:",o),console.log("🌐 Response status:",o.status),console.log("🌐 Response data:",o.data),o.data&&o.data.success&&o.data.data){const n=o.data.data;return console.log("📊 Paginated data structure:",n),{success:!0,data:n}}throw console.error("❌ Invalid response format:",o.data),console.error("❌ Expected structure: { success: true, data: { items: [...] } }"),new Error("Invalid response format from server")}catch(t){throw console.error("❌ Error fetching seller requests:",t),console.error("❌ Error response:",(e=t.response)==null?void 0:e.data),console.error("❌ Error status:",(r=t.response)==null?void 0:r.status),t}},async getSellerRequestById(s){try{const e=`/api/admin/seller-requests/${s}`;console.log(`Fetching seller request from ${e}`);const r=await a.get(e);if(r.data&&r.data.success&&r.data.data)return r.data.data;throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(e){throw console.error(`Error fetching seller request ${s}:`,e),e}},async approveSellerRequest(s){try{const e=`/api/admin/seller-requests/${s}/approve`;console.log(`Approving seller request at ${e}`);const r=await a.post(e);if(r.data&&r.data.success)return{success:!0,request:{id:s,status:"approved",updatedAt:new Date}};throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(e){throw console.error(`Error approving seller request ${s}:`,e),e}},async rejectSellerRequest(s,e=""){try{const r=`/api/admin/seller-requests/${s}/reject`;console.log(`Rejecting seller request at ${r} with reason: ${e}`);const t=await a.post(r,{reason:e});if(t.data&&t.data.success)return{success:!0,request:{id:s,status:"rejected",rejectionReason:e,updatedAt:new Date}};throw console.error("Invalid response format:",t.data),new Error("Invalid response format from server")}catch(r){throw console.error(`Error rejecting seller request ${s}:`,r),r}},async getSellerRequestStats(){try{const s="/api/admin/seller-requests/stats";console.log(`Fetching seller request stats from ${s}`);const e=await a.get(s);if(e.data&&e.data.success&&e.data.data)return e.data.data;throw console.error("Invalid response format:",e.data),new Error("Invalid response format from server")}catch(s){throw console.error("Error fetching seller request stats:",s),s}},async bulkApproveSellerRequests(s){try{const e="/api/admin/seller-requests/bulk-approve";console.log(`Bulk approving seller requests at ${e} with IDs:`,s);const r=await a.post(e,{requestIds:s});if(r.data&&r.data.success&&r.data.data)return{success:!0,approvedCount:r.data.data.approvedCount};throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(e){throw console.error("Error bulk approving seller requests:",e),e}},async bulkRejectSellerRequests(s,e=""){try{const r="/api/admin/seller-requests/bulk-reject";console.log(`Bulk rejecting seller requests at ${r} with IDs:`,s);const t=await a.post(r,{requestIds:s,reason:e});if(t.data&&t.data.success&&t.data.data)return{success:!0,rejectedCount:t.data.data.rejectedCount};throw console.error("Invalid response format:",t.data),new Error("Invalid response format from server")}catch(r){throw console.error("Error bulk rejecting seller requests:",r),r}},async getSellerRequest(s){try{const e=`/api/admin/seller-requests/${s}`;console.log(`Getting seller request from ${e}`);const r=await a.get(e);if(r.data&&r.data.success&&r.data.data)return r.data;throw console.error("Invalid response format:",r.data),new Error("Invalid response format from server")}catch(e){throw console.error("Error getting seller request:",e),e}}};export{d as s};
