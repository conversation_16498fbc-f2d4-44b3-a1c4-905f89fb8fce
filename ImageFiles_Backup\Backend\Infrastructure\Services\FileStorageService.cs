﻿using Marketplace.Infrastructure.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Marketplace.Infrastructure.Services;

public class FileStorageService : IFileStorageService
{
    private readonly FileServiceOptions _options;
    private readonly ILogger<FileStorageService> _logger;

    public FileStorageService(IOptions<FileServiceOptions> options, ILogger<FileStorageService> logger)
    {
        _options = options.Value;
        _logger = logger;

        // Створюємо кореневу папку, якщо вона не існує
        if (!Directory.Exists(_options.StoragePath))
        {
            Directory.CreateDirectory(_options.StoragePath);
            _logger.LogInformation("Створено кореневу папку для зберігання файлів: {Path}", _options.StoragePath);
        }
    }

    public async Task<string> SaveFileAsync(IFormFile file, string folderPath, string? fileName = null, CancellationToken cancellationToken = default)
    {

        // Перевіряємо, чи файл існує
        if (file == null || file.Length == 0)
            throw new InvalidOperationException("Файл не вибрано або він порожній.");

        // Створюємо шлях для збереження файлу
        var uploadsFolder = Path.Combine(_options.StoragePath, folderPath);
        if (!Directory.Exists(uploadsFolder))
            Directory.CreateDirectory(uploadsFolder);

        // Генеруємо унікальне ім'я файлу, якщо не вказано
        if (string.IsNullOrEmpty(fileName))
        {
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            fileName = $"{Guid.NewGuid()}{extension}";
        }

        var filePath = Path.Combine(uploadsFolder, fileName);

        // Зберігаємо файл
        using (var fileStream = new FileStream(filePath, FileMode.Create))
        {
            await file.CopyToAsync(fileStream, cancellationToken);
        }

        // Повертаємо URL-шлях до файлу
        return $"{_options.BaseUrl}/{folderPath.Replace('\\', '/')}/{fileName}";
    }

    public bool DeleteFile(string filePath)
    {
        var fullPath = GetFullPath(filePath);
        if (File.Exists(fullPath))
        {
            File.Delete(fullPath);
            _logger.LogInformation("Файл видалено: {FilePath}", fullPath);
            return true;
        }
        _logger.LogWarning("Файл не знайдено для видалення: {FilePath}", fullPath);
        return false;
    }

    public bool FileExists(string filePath)
    {
        var fullPath = GetFullPath(filePath);
        return File.Exists(fullPath);
    }

    public string GetFullPath(string relativePath)
    {
        // Отримуємо відносний шлях з URL
        var path = relativePath.Replace(_options.BaseUrl, "").TrimStart('/');

        // Формуємо повний шлях до файлу
        return Path.Combine(_options.StoragePath, path);
    }
}
