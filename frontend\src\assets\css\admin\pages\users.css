/* ===== ADMIN USERS PAGE STYLES ===== */
/* Based on Reports page design patterns */

/* ===== USERS PAGE LAYOUT ===== */
.admin-users {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-users .admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-users .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== USER FILTERS ===== */
.admin-user-filters {
  margin-bottom: var(--admin-space-2xl);
}

.admin-user-filters .field {
  margin-bottom: var(--admin-space-lg);
}

.admin-user-filters .label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-user-filters .input,
.admin-user-filters .select select {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-fast);
}

.admin-user-filters .input:focus,
.admin-user-filters .select select:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
  outline: none;
}

.admin-user-filters .control.has-icons-left .icon {
  color: var(--admin-gray-400);
}

.admin-user-filters .buttons {
  margin-top: var(--admin-space-xl);
  display: flex;
  justify-content: center;
  gap: var(--admin-space-md);
}

/* ===== USER TABLE ===== */
.admin-user-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

/* ===== USER INFO STYLING ===== */
.admin-user-info {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--admin-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.admin-user-details {
  min-width: 0;
}

.admin-user-name {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin-bottom: 2px;
  font-size: var(--admin-text-sm);
}

.admin-user-id {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-family: var(--admin-font-mono);
}

.admin-email {
  color: var(--admin-gray-700);
  word-break: break-word;
  font-size: var(--admin-text-sm);
}

.admin-date {
  color: var(--admin-gray-500);
  font-size: var(--admin-text-sm);
}

.admin-user-table .admin-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.admin-user-table .admin-table-th {
  background: var(--admin-gray-50);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-semibold);
  padding: var(--admin-space-lg);
  text-align: left;
  border-bottom: 2px solid var(--admin-border-color);
  font-size: var(--admin-text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-user-table .admin-table-td {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: middle;
  font-size: var(--admin-text-sm);
}

.admin-user-table .admin-table-row:hover {
  background: var(--admin-gray-25);
}

.admin-user-table .admin-table-row:last-child .admin-table-td {
  border-bottom: none;
}

/* ===== USER ROLE SELECT ===== */
.admin-user-role-select {
  position: relative;
}

.admin-user-role-select .select {
  width: 100%;
}

.admin-user-role-select .select select {
  width: 100%;
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  background: var(--admin-white);
  font-size: var(--admin-text-xs);
  transition: all var(--admin-transition-fast);
}

.admin-user-role-select .select select:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 2px var(--admin-primary-bg);
}

.admin-user-role-select .select select:disabled {
  background: var(--admin-gray-100);
  color: var(--admin-gray-500);
  cursor: not-allowed;
}

/* ===== USER ACTION BUTTONS ===== */
.admin-user-actions {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

.admin-user-actions .button {
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  border: 1px solid transparent;
  font-size: var(--admin-text-xs);
  transition: all var(--admin-transition-fast);
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-user-actions .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.admin-user-actions .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.admin-user-actions .button.is-warning {
  background: var(--admin-warning);
  color: white;
  border-color: var(--admin-warning);
}

.admin-user-actions .button.is-warning:hover {
  background: var(--admin-warning-dark);
  border-color: var(--admin-warning-dark);
  transform: translateY(-1px);
}

.admin-user-actions .button.is-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.admin-user-actions .button.is-danger:hover {
  background: var(--admin-danger-dark);
  border-color: var(--admin-danger-dark);
  transform: translateY(-1px);
}

/* ===== ROLE BADGES ===== */
.admin-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1;
}

.admin-badge.is-admin {
  background: linear-gradient(135deg, var(--admin-danger), var(--admin-danger-dark));
  color: white;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.admin-badge.is-moderator {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
}

.admin-badge.is-sellerowner {
  background: linear-gradient(135deg, var(--admin-info), var(--admin-info-dark));
  color: white;
  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.admin-badge.is-seller {
  background: linear-gradient(135deg, var(--admin-success), var(--admin-success-dark));
  color: white;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.admin-badge.is-buyer {
  background: linear-gradient(135deg, var(--admin-gray-600), var(--admin-gray-700));
  color: white;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

/* ===== USER ID TRUNCATION ===== */
.admin-user-id {
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-600);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  display: inline-block;
}

/* ===== USER STATUS INDICATORS ===== */
.admin-user-status {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-user-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.admin-user-status-dot.online {
  background: var(--admin-success);
}

.admin-user-status-dot.offline {
  background: var(--admin-gray-400);
}

.admin-user-status-text {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-600);
}

/* ===== LOADING STATES ===== */
.admin-user-table .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-user-table .admin-loading i {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-user-table .admin-empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-user-table .admin-empty i {
  font-size: 3rem;
  color: var(--admin-gray-300);
  margin-bottom: var(--admin-space-lg);
}

.admin-user-table .admin-empty-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.admin-user-table .admin-empty-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== PAGINATION WRAPPER ===== */
.admin-users .pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--admin-space-2xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .admin-user-table .table {
    font-size: var(--admin-text-xs);
  }
  
  .admin-user-table .table th,
  .admin-user-table .table td {
    padding: var(--admin-space-md);
  }
}

@media (max-width: 768px) {
  .admin-users {
    padding: var(--admin-space-lg);
  }
  
  .admin-users .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-users .admin-page-title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-user-filters {
    padding: var(--admin-space-lg);
  }
  
  .admin-user-table {
    padding: var(--admin-space-lg);
    overflow-x: auto;
  }
  
  .admin-user-table .table {
    min-width: 600px;
  }
  
  .admin-user-actions {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .admin-user-actions .button {
    width: 100%;
    min-width: auto;
  }
}
