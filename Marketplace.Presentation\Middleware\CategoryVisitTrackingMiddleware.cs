using Marketplace.Domain.Entities;
using Marketplace.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Text.RegularExpressions;

namespace Marketplace.Presentation.Middleware;

/// <summary>
/// Middleware для відстеження відвідувань категорій користувачами
/// </summary>
public class CategoryVisitTrackingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<CategoryVisitTrackingMiddleware> _logger;

    public CategoryVisitTrackingMiddleware(RequestDelegate next, ILogger<CategoryVisitTrackingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, MarketplaceDbContext dbContext)
    {
        // Спочатку виконуємо наступний middleware
        await _next(context);

        // Відстежуємо відвідування тільки для GET запитів зі статусом 200
        if (context.Request.Method == "GET" && context.Response.StatusCode == 200)
        {
            await TrackCategoryVisitAsync(context, dbContext);
        }
    }

    private async Task TrackCategoryVisitAsync(HttpContext context, MarketplaceDbContext dbContext)
    {
        try
        {
            // Перевіряємо, чи користувач авторизований
            if (!context.User.Identity?.IsAuthenticated == true)
                return;

            var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                return;

            var path = context.Request.Path.Value?.ToLower();
            if (string.IsNullOrEmpty(path))
                return;

            Guid? categoryId = null;

            // Перевіряємо різні шляхи для визначення категорії
            categoryId = await GetCategoryIdFromPathAsync(path, dbContext);

            if (categoryId.HasValue)
            {
                await UpdateUserCategoryVisitAsync(userId, categoryId.Value, dbContext);
            }
        }
        catch (Exception ex)
        {
            // Логуємо помилку, але не перериваємо запит
            _logger.LogError(ex, "Помилка при відстеженні відвідування категорії для користувача");
        }
    }

    private async Task<Guid?> GetCategoryIdFromPathAsync(string path, MarketplaceDbContext dbContext)
    {
        try
        {
            // Шаблони для різних типів сторінок
            var patterns = new[]
            {
                // Сторінка каталогу категорії: /catalog/category-slug
                @"^/catalog/([a-zA-Z0-9\-]+)$",
                // API категорії: /api/categories/category-slug/products
                @"^/api/categories/([a-zA-Z0-9\-]+)/products$",
                // Сторінка продукту: /product/product-slug
                @"^/product/([a-zA-Z0-9\-]+)$",
                // API продукту: /api/products/product-slug
                @"^/api/products/([a-zA-Z0-9\-]+)$"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(path, pattern);
                if (match.Success)
                {
                    var slug = match.Groups[1].Value;
                    
                    // Якщо це категорія
                    if (pattern.Contains("catalog") || pattern.Contains("categories"))
                    {
                        var category = await dbContext.Categories
                            .Where(c => c.Slug.Value == slug)
                            .Select(c => new { c.Id })
                            .FirstOrDefaultAsync();
                        
                        return category?.Id;
                    }
                    
                    // Якщо це продукт
                    if (pattern.Contains("product"))
                    {
                        var product = await dbContext.Products
                            .Where(p => p.Slug.Value == slug)
                            .Select(p => new { p.CategoryId })
                            .FirstOrDefaultAsync();
                        
                        return product?.CategoryId;
                    }
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при визначенні категорії з шляху: {Path}", path);
            return null;
        }
    }

    private async Task UpdateUserCategoryVisitAsync(Guid userId, Guid categoryId, MarketplaceDbContext dbContext)
    {
        try
        {
            var user = await dbContext.Users
                .Where(u => u.Id == userId)
                .FirstOrDefaultAsync();

            if (user != null)
            {
                // Додаємо відвідування категорії
                user.AddCategoryVisit(categoryId);
                
                // Зберігаємо зміни
                await dbContext.SaveChangesAsync();
                
                _logger.LogDebug("Відстежено відвідування категорії {CategoryId} користувачем {UserId}", 
                    categoryId, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при оновленні відвідувань категорії для користувача {UserId}", userId);
        }
    }
}
