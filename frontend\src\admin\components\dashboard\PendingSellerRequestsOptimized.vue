<template>
  <div class="admin-seller-requests">
    <div class="admin-seller-requests__header">
      <h3 class="admin-seller-requests__title">
        <i class="admin-seller-requests__icon fas fa-user-check"></i>
        Pending Seller Requests
        <span 
          v-if="requests && requests.length > 0" 
          class="admin-seller-requests__count"
        >
          {{ requests.length }}
        </span>
      </h3>
      <router-link 
        to="/admin/seller-requests" 
        class="admin-seller-requests__view-all"
      >
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </router-link>
    </div>

    <div class="admin-seller-requests__content">
      <!-- Loading State -->
      <div v-if="loading" class="admin-seller-requests__loading">
        <div class="admin-seller-requests__loading-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-seller-requests__loading-text">Loading seller requests...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="!requests || requests.length === 0" class="admin-seller-requests__empty">
        <div class="admin-seller-requests__empty-icon">
          <i class="fas fa-user-check"></i>
        </div>
        <h4 class="admin-seller-requests__empty-title">No Pending Requests</h4>
        <p class="admin-seller-requests__empty-text">
          All seller applications have been processed. New requests will appear here.
        </p>
      </div>

      <!-- Requests List -->
      <div v-else class="admin-seller-requests__list">
        <div 
          v-for="request in requests" 
          :key="request.id"
          class="admin-seller-requests__item"
        >
          <div class="admin-seller-requests__item-header">
            <div class="admin-seller-requests__user-info">
              <div class="admin-seller-requests__user-avatar">
                <i class="fas fa-user"></i>
              </div>
              <div class="admin-seller-requests__user-details">
                <span class="admin-seller-requests__user-name">{{ request.userName }}</span>
                <span class="admin-seller-requests__user-email">{{ request.userEmail }}</span>
              </div>
            </div>
            <div class="admin-seller-requests__request-date">
              <span class="admin-seller-requests__date-label">Submitted</span>
              <span class="admin-seller-requests__date-value">{{ formatDate(request.createdAt) }}</span>
            </div>
          </div>

          <div class="admin-seller-requests__item-content">
            <div class="admin-seller-requests__company-info">
              <h5 class="admin-seller-requests__company-name">{{ request.companyName }}</h5>
              <p class="admin-seller-requests__company-description">{{ request.description }}</p>
            </div>
          </div>

          <div class="admin-seller-requests__item-actions">
            <button 
              class="admin-seller-requests__action-btn admin-seller-requests__action-btn--approve"
              @click="approveRequest(request.id)"
              :title="`Approve ${request.userName}'s request`"
            >
              <i class="fas fa-check"></i>
              <span>Approve</span>
            </button>
            <button 
              class="admin-seller-requests__action-btn admin-seller-requests__action-btn--reject"
              @click="rejectRequest(request.id)"
              :title="`Reject ${request.userName}'s request`"
            >
              <i class="fas fa-times"></i>
              <span>Reject</span>
            </button>
            <router-link 
              :to="`/admin/seller-requests/${request.id}`"
              class="admin-seller-requests__action-btn admin-seller-requests__action-btn--view"
              :title="`View ${request.userName}'s full application`"
            >
              <i class="fas fa-eye"></i>
              <span>View</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  requests: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['approve-request', 'reject-request']);

// Computed properties
const hasRequests = computed(() => props.requests && props.requests.length > 0);

// Methods
const formatDate = (dateString) => {
  if (!dateString) return 'Unknown';
  
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

const approveRequest = (requestId) => {
  emit('approve-request', requestId);
};

const rejectRequest = (requestId) => {
  emit('reject-request', requestId);
};
</script>

<style scoped>
.admin-seller-requests {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-seller-requests__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.admin-seller-requests__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-seller-requests__icon {
  color: #f59e0b;
  font-size: 1rem;
}

.admin-seller-requests__count {
  background: #f59e0b;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-seller-requests__view-all {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.admin-seller-requests__view-all:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  color: white;
  text-decoration: none;
}

.admin-seller-requests__content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.admin-seller-requests__loading,
.admin-seller-requests__empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1rem;
}

.admin-seller-requests__loading-spinner,
.admin-seller-requests__empty-icon {
  font-size: 2rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.admin-seller-requests__loading-spinner {
  color: #f59e0b;
}

.admin-seller-requests__loading-text,
.admin-seller-requests__empty-text {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
}

.admin-seller-requests__empty-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.admin-seller-requests__list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-seller-requests__item {
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
}

.admin-seller-requests__item:hover {
  border-color: #f59e0b;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.admin-seller-requests__item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.admin-seller-requests__user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.admin-seller-requests__user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
}

.admin-seller-requests__user-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.admin-seller-requests__user-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.admin-seller-requests__user-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.admin-seller-requests__request-date {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.125rem;
}

.admin-seller-requests__date-label {
  font-size: 0.75rem;
  color: #6b7280;
}

.admin-seller-requests__date-value {
  font-size: 0.75rem;
  color: #374151;
  font-weight: 500;
}

.admin-seller-requests__item-content {
  margin-bottom: 1rem;
}

.admin-seller-requests__company-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.admin-seller-requests__company-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.admin-seller-requests__item-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.admin-seller-requests__action-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.admin-seller-requests__action-btn--approve {
  background: #dcfce7;
  color: #166534;
}

.admin-seller-requests__action-btn--approve:hover {
  background: #bbf7d0;
  color: #14532d;
}

.admin-seller-requests__action-btn--reject {
  background: #fef2f2;
  color: #991b1b;
}

.admin-seller-requests__action-btn--reject:hover {
  background: #fee2e2;
  color: #7f1d1d;
}

.admin-seller-requests__action-btn--view {
  background: #f3f4f6;
  color: #374151;
}

.admin-seller-requests__action-btn--view:hover {
  background: #e5e7eb;
  color: #1f2937;
  text-decoration: none;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .admin-seller-requests {
    padding: 1rem;
  }
  
  .admin-seller-requests__item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .admin-seller-requests__request-date {
    align-items: flex-start;
  }
  
  .admin-seller-requests__item-actions {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}
</style>
