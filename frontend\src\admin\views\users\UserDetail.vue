<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Users
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-user admin-page-icon"></i>
          User Details
        </h1>
        <p class="admin-page-subtitle">View and manage user information</p>
      </div>
      <div class="admin-page-actions">
        <router-link
          :to="`/admin/users/${userId}/edit`"
          class="admin-btn admin-btn-primary">
          <i class="fas fa-edit"></i>
          Edit User
        </router-link>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading user data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error loading user data</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="loadUser" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-retry"></i>
        Retry
      </button>
    </div>

    <!-- User Details Content -->
    <div v-else-if="user" class="admin-user-detail-content">
      <!-- Basic Information Card -->
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-user-circle"></i>
            User Information
          </h3>
        </div>
        <div class="admin-card-content">
          <div class="admin-user-detail-grid">
            <!-- Avatar Section -->
            <div class="admin-detail-section admin-avatar-section">
              <h4 class="admin-detail-section-title">
                <i class="fas fa-user-circle me-2"></i>
                User Avatar
              </h4>
              <div class="admin-avatar-display">
                <div
                  class="admin-user-avatar-large"
                  :class="{ 'has-image': user.avatarUrl }"
                  @click="openAvatarViewer"
                >
                  <img
                    v-if="user.avatarUrl"
                    :src="user.avatarUrl"
                    :alt="`${user.username} avatar`"
                    class="avatar-image-large"
                    @error="handleAvatarError"
                  />
                  <i v-else class="fas fa-user avatar-placeholder-large"></i>
                </div>
                <div class="admin-avatar-info">
                  <p class="admin-avatar-status">
                    {{ user.avatarUrl ? 'Avatar uploaded' : 'No avatar' }}
                  </p>
                  <button
                    v-if="user.avatarUrl"
                    class="admin-btn admin-btn-sm admin-btn-secondary"
                    @click="openAvatarViewer"
                  >
                    <i class="fas fa-eye me-1"></i>
                    View Full Size
                  </button>
                </div>
              </div>
            </div>

            <div class="admin-detail-section">
              <h4 class="admin-detail-section-title">Basic Information</h4>
              
              <div class="admin-detail-row">
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Username</label>
                  <div class="admin-detail-value">{{ user.username }}</div>
                </div>
                
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Email</label>
                  <div class="admin-detail-value">{{ user.email }}</div>
                </div>
              </div>

              <div class="admin-detail-row">
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Role</label>
                  <div class="admin-detail-value">
                    <span class="admin-badge" :class="getRoleClass(user.role)">
                      {{ getRoleDisplayName(user.role) }}
                    </span>
                  </div>
                </div>

                <div class="admin-detail-item">
                  <label class="admin-detail-label">Status</label>
                  <div class="admin-detail-value">
                    <span class="admin-badge admin-badge-success">Active</span>
                  </div>
                </div>
              </div>

              <div class="admin-detail-row">
                <div class="admin-detail-item">
                  <label class="admin-detail-label">Email Confirmed</label>
                  <div class="admin-detail-value">
                    <span class="admin-badge" :class="user.emailConfirmed ? 'admin-badge-success' : 'admin-badge-warning'">
                      {{ user.emailConfirmed ? 'Confirmed' : 'Not Confirmed' }}
                    </span>
                  </div>
                </div>

                <div class="admin-detail-item">
                  <label class="admin-detail-label">Approved</label>
                  <div class="admin-detail-value">
                    <span class="admin-badge" :class="user.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
                      {{ user.isApproved ? 'Approved' : 'Not Approved' }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Personal Information Section -->
              <div class="admin-detail-section">
                <h4 class="admin-detail-section-title">
                  <i class="fas fa-user"></i>
                  Personal Information
                </h4>
                <div class="admin-detail-grid">
                  <div class="admin-detail-item">
                    <label class="admin-detail-label">First Name</label>
                    <div class="admin-detail-value">{{ user.firstName || 'Not specified' }}</div>
                  </div>
                  <div class="admin-detail-item">
                    <label class="admin-detail-label">Last Name</label>
                    <div class="admin-detail-value">{{ user.lastName || 'Not specified' }}</div>
                  </div>
                  <div class="admin-detail-item">
                    <label class="admin-detail-label">Gender</label>
                    <div class="admin-detail-value">
                      <span class="admin-badge admin-badge-info">
                        {{ getGenderDisplayName(user.gender) }}
                      </span>
                    </div>
                  </div>
                  <div class="admin-detail-item" v-if="user.birthday">
                    <label class="admin-detail-label">Birthday</label>
                    <div class="admin-detail-value">{{ formatDate(user.birthday) }}</div>
                  </div>
                </div>
              </div>

              <!-- Contact Information Section -->
              <div class="admin-detail-section">
                <h4 class="admin-detail-section-title">
                  <i class="fas fa-address-book"></i>
                  Contact Information
                </h4>
                <div class="admin-detail-grid">
                  <div class="admin-detail-item">
                    <label class="admin-detail-label">Phone</label>
                    <div class="admin-detail-value">{{ user.phone || 'Not specified' }}</div>
                  </div>
                  <div class="admin-detail-item">
                    <label class="admin-detail-label">Language</label>
                    <div class="admin-detail-value">
                      <span class="admin-badge admin-badge-secondary">
                        {{ getLanguageDisplayName(user.language) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- System Information Section -->
              <div class="admin-detail-section">
                <h4 class="admin-detail-section-title">
                  <i class="fas fa-cog"></i>
                  System Information
                </h4>
                <div class="admin-detail-grid">
                  <div class="admin-detail-item">
                    <label class="admin-detail-label">Registered</label>
                    <div class="admin-detail-value">{{ formatDate(user.createdAt || user.emailConfirmedAt) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order History Card -->
      <div class="admin-card">
        <div class="admin-card-header">
          <h3 class="admin-card-title">
            <i class="fas fa-shopping-cart"></i>
            Order History
          </h3>
          <div class="admin-card-actions">
            <span class="admin-results-count">{{ totalOrders }} orders found</span>
          </div>
        </div>
        <div class="admin-card-content">
          <div v-if="loadingOrders" class="admin-loading-state">
            <div class="admin-spinner">
              <i class="fas fa-spinner fa-pulse"></i>
            </div>
            <p class="admin-loading-text">Loading orders...</p>
          </div>
          <div v-else-if="orders && orders.length > 0" class="admin-orders-list">
            <div v-for="order in orders" :key="order.id" class="admin-order-item">
              <div class="admin-order-header">
                <div class="admin-order-info">
                  <h4 class="admin-order-number" :title="order.id">#{{ order.id.substring(0, 8) }}...</h4>
                  <span class="admin-order-date">{{ formatDate(order.createdAt) }}</span>
                </div>
                <div class="admin-order-statuses">
                  <div class="admin-order-status">
                    <span class="admin-status-label">Order Status:</span>
                    <span class="admin-badge" :class="getOrderStatusClass(order.status)">
                      {{ getOrderStatusText(order.status) }}
                    </span>
                  </div>
                  <div class="admin-payment-status">
                    <span class="admin-status-label">Payment:</span>
                    <span class="admin-badge" :class="getPaymentStatusClass(order.paymentStatus)">
                      {{ getPaymentStatusText(order.paymentStatus) }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="admin-order-details">
                <div class="admin-order-amount">
                  <span class="admin-amount-label">Total:</span>
                  <strong>${{ formatOrderTotal(order) }}</strong>
                </div>
                <div class="admin-order-actions">
                  <router-link
                    :to="`/admin/orders/${order.id}`"
                    class="admin-btn admin-btn-sm admin-btn-primary">
                    <i class="fas fa-eye"></i>
                    View Details
                  </router-link>
                </div>
              </div>
            </div>
            <div v-if="ordersPagination && ordersPagination.totalPages > 1" class="admin-pagination">
              <button
                class="admin-btn admin-btn-sm admin-btn-secondary"
                :disabled="!ordersPagination || ordersPagination.page <= 1"
                @click="loadOrders((ordersPagination?.page || 1) - 1)">
                Previous
              </button>
              <span class="admin-pagination-info">
                Page {{ ordersPagination?.page || 1 }} of {{ ordersPagination?.totalPages || 1 }}
              </span>
              <button
                class="admin-btn admin-btn-sm admin-btn-secondary"
                :disabled="!ordersPagination || ordersPagination.page >= ordersPagination.totalPages"
                @click="loadOrders((ordersPagination?.page || 1) + 1)">
                Next
              </button>
            </div>
          </div>
          <div v-else class="admin-empty-state">
            <div class="admin-empty-icon">
              <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="admin-empty-title">No Orders Found</h3>
            <p class="admin-empty-message">
              This user hasn't placed any orders yet.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Avatar Viewer Modal -->
    <div
      v-if="showAvatarModal"
      class="modal fade show d-block"
      @click="closeAvatarViewer"
    >
      <div class="modal-dialog modal-lg" @click.stop>
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-user me-2"></i>
              {{ user?.username }} - Avatar
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeAvatarViewer"
            ></button>
          </div>
          <div class="modal-body text-center">
            <div v-if="user?.avatarUrl" class="avatar-viewer">
              <img
                :src="user.avatarUrl"
                :alt="`${user.username} avatar`"
                class="img-fluid rounded"
                style="max-height: 500px;"
              />
              <div class="mt-3">
                <h6>{{ user.username }}</h6>
                <small class="text-muted">{{ user.email }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usersService } from '@/admin/services/users';
import { ROLE_KEYS, ROLE_DISPLAY_NAMES, getRoleKey } from '@/admin/services/roles';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const error = ref(null);
const user = ref(null);
const totalOrders = ref(0);
const orders = ref([]);
const loadingOrders = ref(false);
const ordersPagination = ref({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 1
});

// Avatar viewer state
const showAvatarModal = ref(false);

// Computed
const userId = computed(() => route.params.id);

// Methods
const loadUser = async () => {
  loading.value = true;
  error.value = null;

  try {
    const userData = await usersService.getUserById(userId.value);
    user.value = userData;

    // Load orders after user data is loaded
    await loadOrders();
  } catch (err) {
    console.error('Error loading user:', err);
    error.value = 'Failed to load user data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Load user orders
const loadOrders = async (page = 1) => {
  loadingOrders.value = true;

  try {
    console.log('Loading orders for user:', userId.value);
    const response = await usersService.getUserOrders(userId.value, {
      page,
      pageSize: ordersPagination.value.limit,
      orderBy: 'CreatedAt',
      descending: true
    });

    console.log('📦 Orders response:', response);

    // Перевіряємо, що response має правильну структуру
    if (response && response.orders && response.pagination) {
      orders.value = response.orders || [];
      ordersPagination.value = {
        page: response.pagination.page || 1,
        limit: response.pagination.limit || 10,
        total: response.pagination.total || 0,
        totalPages: response.pagination.totalPages || 1
      };
      totalOrders.value = response.pagination.total || 0;

      console.log('✅ Orders loaded successfully:', {
        ordersCount: orders.value.length,
        pagination: ordersPagination.value,
        totalOrders: totalOrders.value
      });
    } else {
      console.log('⚠️ Unexpected response format, setting defaults');
      // Встановлюємо значення за замовчуванням
      orders.value = [];
      ordersPagination.value = {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 1
      };
      totalOrders.value = 0;
    }
  } catch (err) {
    console.error('Error loading orders:', err);
    // Встановлюємо значення за замовчуванням при помилці
    orders.value = [];
    ordersPagination.value = {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 1
    };
    totalOrders.value = 0;
  } finally {
    loadingOrders.value = false;
  }
};

const goBack = () => {
  router.push('/admin/users');
};

// Avatar methods
const openAvatarViewer = () => {
  if (user.value?.avatarUrl) {
    showAvatarModal.value = true;
  }
};

const closeAvatarViewer = () => {
  showAvatarModal.value = false;
};

const handleAvatarError = (event) => {
  // Hide broken image and show placeholder
  event.target.style.display = 'none';
  const placeholder = event.target.nextElementSibling;
  if (placeholder) {
    placeholder.style.display = 'block';
  }
};

const getRoleClass = (role) => {
  // Convert role from backend format (number) to role key
  const roleKey = getRoleKey(role);

  const roleClasses = {
    [ROLE_KEYS.ADMIN]: 'admin-badge-danger',
    [ROLE_KEYS.MODERATOR]: 'admin-badge-warning',
    [ROLE_KEYS.SELLER_OWNER]: 'admin-badge-info',
    [ROLE_KEYS.SELLER]: 'admin-badge-primary',
    [ROLE_KEYS.BUYER]: 'admin-badge-secondary'
  };
  return roleClasses[roleKey] || 'admin-badge-secondary';
};

const getRoleDisplayName = (role) => {
  // Convert role from backend format (number) to display name
  const roleKey = getRoleKey(role);
  return ROLE_DISPLAY_NAMES[roleKey] || role;
};

const getGenderDisplayName = (gender) => {
  const genderMap = {
    0: 'Male',
    1: 'Female',
    null: 'Not specified',
    undefined: 'Not specified'
  };
  return genderMap[gender] || 'Not specified';
};

const getLanguageDisplayName = (language) => {
  const languageMap = {
    0: 'Ukrainian',
    1: 'English',
    2: 'Russian',
    null: 'Not specified',
    undefined: 'Not specified'
  };
  return languageMap[language] || 'Not specified';
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Get order status class (matching backend OrderStatus enum)
const getOrderStatusClass = (status) => {
  const classMap = {
    // Numeric values (backend enum values)
    0: 'admin-badge-info',     // Processing
    1: 'admin-badge-warning',  // Pending
    2: 'admin-badge-primary',  // Shipped
    3: 'admin-badge-success',  // Delivered
    4: 'admin-badge-danger',   // Cancelled
    // String values (for backward compatibility)
    'Processing': 'admin-badge-info',
    'Pending': 'admin-badge-warning',
    'Shipped': 'admin-badge-primary',
    'Delivered': 'admin-badge-success',
    'Cancelled': 'admin-badge-danger'
  };
  return classMap[status] || 'admin-badge-secondary';
};

// Get order status text (matching backend OrderStatus enum)
const getOrderStatusText = (status) => {
  const statusMap = {
    // Numeric values (backend enum values)
    0: 'Processing',  // OrderStatus.Processing = 0
    1: 'Pending',     // OrderStatus.Pending = 1
    2: 'Shipped',     // OrderStatus.Shipped = 2
    3: 'Delivered',   // OrderStatus.Delivered = 3
    4: 'Cancelled',   // OrderStatus.Cancelled = 4
    // String values (for backward compatibility)
    'Processing': 'Processing',
    'Pending': 'Pending',
    'Shipped': 'Shipped',
    'Delivered': 'Delivered',
    'Cancelled': 'Cancelled'
  };
  return statusMap[status] || status || 'Unknown';
};

// Get payment status class (matching backend PaymentStatus enum)
const getPaymentStatusClass = (paymentStatus) => {
  const classMap = {
    // Numeric values (backend enum values)
    0: 'admin-badge-warning',  // Pending
    1: 'admin-badge-success',  // Completed
    2: 'admin-badge-info',     // Refunded
    3: 'admin-badge-danger',   // Failed
    // String values (for backward compatibility)
    'Pending': 'admin-badge-warning',
    'Completed': 'admin-badge-success',
    'Refunded': 'admin-badge-info',
    'Failed': 'admin-badge-danger'
  };
  return classMap[paymentStatus] || 'admin-badge-secondary';
};

// Get payment status text (matching backend PaymentStatus enum)
const getPaymentStatusText = (paymentStatus) => {
  const statusMap = {
    // Numeric values (backend enum values)
    0: 'Pending',     // PaymentStatus.Pending = 0
    1: 'Completed',   // PaymentStatus.Completed = 1
    2: 'Refunded',    // PaymentStatus.Refunded = 2
    3: 'Failed',      // PaymentStatus.Failed = 3
    // String values (for backward compatibility)
    'Pending': 'Pending',
    'Completed': 'Completed',
    'Refunded': 'Refunded',
    'Failed': 'Failed'
  };
  return statusMap[paymentStatus] || paymentStatus || 'Pending';
};

// Format order total from API response
const formatOrderTotal = (order) => {
  // API повертає TotalPriceAmount як основне поле
  const total = order.totalPriceAmount ||
                order.totalAmount ||
                order.total ||
                order.totalPrice?.amount ||
                order.totalPrice ||
                0;

  return Number(total).toFixed(2);
};

// Lifecycle
onMounted(() => {
  loadUser();
});
</script>

<style scoped>
.admin-user-detail-content {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.admin-user-detail-grid {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
}

.admin-detail-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-detail-section-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
  padding-bottom: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border);
}

.admin-detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--admin-space-xl);
}

.admin-detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-detail-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-detail-value {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  font-weight: var(--admin-font-medium);
}

.admin-detail-timestamp {
  display: block;
  font-size: var(--admin-text-xs);
  color: var(--admin-text-secondary);
  font-weight: var(--admin-font-normal);
  margin-top: var(--admin-space-xs);
}

@media (max-width: 768px) {
  .admin-detail-row {
    grid-template-columns: 1fr;
    gap: var(--admin-space-lg);
  }
}

/* Orders List Styles */
.admin-orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-order-item {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  transition: all var(--admin-transition-fast);
}

.admin-order-item:hover {
  box-shadow: var(--admin-shadow-md);
  transform: translateY(-1px);
}

.admin-order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-md);
  gap: var(--admin-space-md);
}

.admin-order-info {
  flex: 1;
}

.admin-order-number {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-xs) 0;
  cursor: help;
}

.admin-order-date {
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
}

.admin-order-statuses {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
  align-items: flex-end;
}

.admin-order-status,
.admin-payment-status {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-status-label {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
  font-weight: var(--admin-font-medium);
}

.admin-order-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--admin-space-md);
  border-top: 1px solid var(--admin-border-light);
}

.admin-order-amount {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-amount-label {
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
}

.admin-order-item {
  border: 1px solid var(--admin-border);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-lg);
  background: var(--admin-white);
  transition: box-shadow 0.2s ease;
}

.admin-order-item:hover {
  box-shadow: var(--admin-shadow-sm);
}

.admin-order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-md);
}

.admin-order-info {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-order-number {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-order-date {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
}

.admin-order-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-order-amount {
  font-size: var(--admin-text-lg);
  color: var(--admin-gray-900);
}

.admin-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--admin-space-md);
  margin-top: var(--admin-space-lg);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border);
}

.admin-pagination-info {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
}

@media (max-width: 768px) {
  .admin-order-header,
  .admin-order-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-sm);
  }

  .admin-order-actions {
    width: 100%;
  }

  .admin-pagination {
    flex-direction: column;
    gap: var(--admin-space-sm);
  }
}

/* New section-based layout styles */
.admin-detail-section {
  margin-bottom: var(--admin-space-xl);
  background: var(--admin-white);
  border: 1px solid var(--admin-gray-200);
  border-radius: var(--admin-radius-lg);
  overflow: hidden;
}

.admin-detail-section-title {
  background: var(--admin-gray-50);
  padding: var(--admin-space-md) var(--admin-space-lg);
  margin: 0;
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  border-bottom: 1px solid var(--admin-gray-200);
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-detail-section-title i {
  color: var(--admin-primary-500);
}

.admin-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--admin-space-lg);
  padding: var(--admin-space-lg);
}

.admin-detail-grid .admin-detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-detail-grid .admin-detail-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-detail-grid .admin-detail-value {
  font-size: var(--admin-text-base);
  color: var(--admin-gray-900);
  font-weight: var(--admin-font-medium);
}

/* Responsive adjustments for new layout */
@media (max-width: 768px) {
  .admin-detail-grid {
    grid-template-columns: 1fr;
    gap: var(--admin-space-md);
    padding: var(--admin-space-md);
  }

  .admin-detail-section-title {
    padding: var(--admin-space-sm) var(--admin-space-md);
    font-size: var(--admin-text-sm);
  }

  .admin-avatar-display {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .admin-user-avatar-large {
    width: 80px;
    height: 80px;
  }

  .avatar-placeholder-large {
    font-size: 32px;
  }
}

/* Avatar styles */
.admin-avatar-section {
  border-bottom: 1px solid var(--admin-border-light);
  padding-bottom: var(--admin-space-lg);
  margin-bottom: var(--admin-space-lg);
}

.admin-avatar-display {
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

.admin-user-avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 3px solid #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
}

.admin-user-avatar-large:hover {
  border-color: #007bff;
  transform: scale(1.02);
}

.admin-user-avatar-large.has-image {
  border-color: #28a745;
}

.avatar-image-large {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-placeholder-large {
  color: #6c757d;
  font-size: 48px;
}

.admin-user-avatar-large.has-image .avatar-placeholder-large {
  display: none;
}

.admin-avatar-info {
  flex: 1;
}

.admin-avatar-status {
  margin: 0 0 var(--admin-space-sm);
  color: var(--admin-text-muted);
  font-size: var(--admin-text-sm);
}

/* Modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.avatar-viewer {
  padding: 1rem;
}
</style>
