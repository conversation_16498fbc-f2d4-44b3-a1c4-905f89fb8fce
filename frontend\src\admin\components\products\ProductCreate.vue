<template>
  <ProductEdit
    :is-create="true"
    @save="handleSave"
    @cancel="handleCancel"
    @created="handleCreated"
  />
</template>

<script setup>
import { useRouter } from 'vue-router';
import ProductEdit from './ProductEdit.vue';

// Router
const router = useRouter();

// Emits
const emit = defineEmits(['save', 'cancel', 'created']);

// Methods
const handleSave = (productData) => {
  console.log('Product saved:', productData);
  emit('save', productData);
};

const handleCreated = (productData) => {
  console.log('Product created:', productData);
  emit('created', productData);

  // The ProductEdit component will handle the redirect to edit page
  // We don't need to redirect here as it's handled in ProductEdit
};

const handleCancel = () => {
  emit('cancel');

  // Go back to products list
  router.push('/admin/products');
};
</script>

<style scoped>
/* No additional styles needed - ProductEdit handles all styling */
</style>
