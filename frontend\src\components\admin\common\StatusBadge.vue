<template>
  <span class="status-badge" :class="statusClass">
    {{ statusText }}
  </span>
</template>

<script>
export default {
  name: 'StatusBadge',
  props: {
    status: {
      type: [String, Number],
      required: true
    }
  },
  computed: {
    statusClass() {
      const status = this.status.toString().toLowerCase();
      
      switch (status) {
        case 'approved':
        case 'active':
        case 'completed':
        case 'success':
        case '1':
          return 'status-success';
        case 'pending':
        case 'in progress':
        case 'processing':
        case '0':
          return 'status-pending';
        case 'rejected':
        case 'failed':
        case 'cancelled':
        case 'inactive':
        case '-1':
          return 'status-danger';
        default:
          return 'status-default';
      }
    },
    statusText() {
      const status = this.status.toString().toLowerCase();
      
      switch (status) {
        case '1':
          return 'Approved';
        case '0':
          return 'Pending';
        case '-1':
          return 'Rejected';
        default:
          return this.status.charAt(0).toUpperCase() + this.status.slice(1);
      }
    }
  }
};
</script>

<style scoped>
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: capitalize;
  border: 1px solid transparent;
  transition: all var(--admin-transition-fast);
}

.status-success {
  background-color: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border-color: var(--admin-success-light);
}

.status-pending {
  background-color: var(--admin-warning-bg);
  color: var(--admin-warning-dark);
  border-color: var(--admin-warning-light);
}

.status-danger {
  background-color: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border-color: var(--admin-danger-light);
}

.status-default {
  background-color: var(--admin-gray-100);
  color: var(--admin-gray-700);
  border-color: var(--admin-gray-300);
}
</style>
