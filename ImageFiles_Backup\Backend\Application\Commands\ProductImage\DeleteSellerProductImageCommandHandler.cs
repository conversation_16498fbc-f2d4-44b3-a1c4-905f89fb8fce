﻿﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class DeleteSellerProductImageCommandHandler : IRequestHandler<DeleteSellerProductImageCommand, bool>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly IFileService _fileService;

    public DeleteSellerProductImageCommandHandler(
        IProductImageRepository productImageRepository,
        IProductRepository productRepository,
        ICompanyUserRepository companyUserRepository,
        IFileService fileService)
    {
        _productImageRepository = productImageRepository;
        _productRepository = productRepository;
        _companyUserRepository = companyUserRepository;
        _fileService = fileService;
    }

    public async Task<bool> Handle(DeleteSellerProductImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо компанії, до яких належить продавець
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.SellerId,
            cancellationToken: cancellationToken
        );

        if (!companyUsers.Any())
            return false;

        // Отримуємо ID компаній продавця
        var companyIds = companyUsers.Select(cu => cu.CompanyId).ToList();

        // Отримуємо продукт
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null || !companyIds.Contains(product.CompanyId))
            return false;

        // Отримуємо зображення продукту
        var productImage = await _productImageRepository.GetByIdAsync(request.ImageId, cancellationToken);
        if (productImage == null || productImage.ProductId != request.ProductId)
            return false;

        // Видаляємо фізичний файл з диску
        await _fileService.DeleteFileAsync(
            productImage.Image!.Value,
            cancellationToken);

        // Видаляємо зображення продукту з бази даних
        await _productImageRepository.DeleteAsync(productImage.Id, cancellationToken);
        return true;
    }
}
