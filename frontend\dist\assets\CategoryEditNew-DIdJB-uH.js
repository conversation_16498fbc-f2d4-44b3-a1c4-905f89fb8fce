import{_ as F,g as m,C as A,h as H,f as j,H as G,i as J,c as g,a,t as c,d as o,z as K,b as n,w as p,r as Q,k as M,x as v,D as y,n as S,m as W,I as T,e as X,o as u}from"./index-L-hJxM_5.js";import{E as Y}from"./EnhancedCategorySelector-v7wVF3eo.js";import{E as Z}from"./EntityImageManager-DFnfDtsC.js";import{E as ee}from"./EntityMetaImageManager-oIAk4B-a.js";import{U as ae}from"./UploadProgress-DTyUnfEW.js";import"./products-Bpq90UOX.js";import"./image.service-DOD4lHqw.js";const se={class:"category-edit-new"},te={key:0,class:"loading-state"},le={key:1,class:"error-state"},oe={class:"error-message"},re={key:2,class:"not-found-state"},ie={key:3,class:"edit-content"},ne={class:"edit-header"},de={class:"header-content"},me={class:"breadcrumb"},ge={class:"header-actions"},ue={key:0,class:"message message-error"},ce={class:"message-content"},pe={key:1,class:"message message-success"},fe={class:"message-content"},ve={class:"form-card"},ye={class:"form-card-content"},be={class:"form-grid"},Ce={class:"form-field"},Ie={class:"form-field"},xe={class:"form-field"},we={class:"form-card"},ke={class:"form-card-content"},De={class:"form-field"},Ue={class:"form-card"},Ee={class:"form-card-content"},he={class:"form-field"},Me={class:"form-field"},Se={class:"form-card"},Te={class:"form-card-content"},Ve={class:"form-grid"},Oe={class:"form-field"},Pe={class:"form-help"},Ne={class:"form-field"},Re={class:"form-field"},Be={class:"form-help"},$e={class:"form-actions"},_e=["disabled"],qe={key:0,class:"fas fa-spinner fa-spin"},Le={key:1,class:"fas fa-save"},ze={key:0,class:"mt-3"},Fe={__name:"CategoryEditNew",setup(Ae){const I=j();X();const x=m(!1),f=m(!1),d=m(""),b=m(""),i=m(null),w=m(null),k=m(null),C=m([]),U=m(null),E=m(null),s=A({name:"",slug:"",description:"",parentId:null,image:"",metaImage:"",metaTitle:"",metaDescription:"",displayOrder:0}),h=H(()=>I.params.id),D=async t=>{x.value=!0,d.value="";try{const e=await T.getById(t);i.value=e,s.name=e.name||"",s.slug=e.slug||"",s.description=e.description||"",s.parentId=e.parentId||null,s.image=e.image||"",s.metaImage=e.metaImage||"",s.metaTitle=e.metaTitle||"",s.metaDescription=e.metaDescription||"",s.displayOrder=e.displayOrder||0}catch(e){console.error("Error fetching category:",e),e.name!=="CanceledError"&&e.code!=="ERR_CANCELED"&&(d.value="Failed to load category. Please try again.")}finally{x.value=!1}},V=()=>{s.name&&(s.slug=s.name.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"))},O=t=>{s.parentId=t},P=t=>{s.image=t.fileUrl,U.value=null,console.log("Category image uploaded:",t)},N=()=>{s.image="",U.value=null,console.log("Category image deleted")},R=t=>{U.value=t,console.log("Category image changed (local):",t)},B=t=>{s.metaImage=t.fileUrl,E.value=null,console.log("Category meta image uploaded:",t)},$=()=>{s.metaImage="",E.value=null,console.log("Category meta image deleted")},_=t=>{E.value=t,console.log("Category meta image changed (local):",t)},q=async t=>{const e=C.value.find(r=>r.id===t);if(e){e.status="uploading",e.progress=0,e.error=null;try{console.log("Retrying upload:",t)}catch(r){e.status="error",e.error=r.message}}},L=t=>{const e=C.value.findIndex(r=>r.id===t);e!==-1&&C.value.splice(e,1)},z=async()=>{f.value=!0,d.value="",b.value="";try{const t={name:s.name,slug:s.slug,description:s.description,parentId:s.parentId||null,image:s.image||null,metaImage:s.metaImage||null,metaTitle:s.metaTitle,metaDescription:s.metaDescription,displayOrder:s.displayOrder};await T.updateCategory(h.value,t),w.value&&w.value.uploadPendingImages&&await w.value.uploadPendingImages(h.value),k.value&&k.value.uploadPendingImage&&await k.value.uploadPendingImage(),b.value="Category updated successfully!",await D(h.value)}catch(t){console.error("Error updating category:",t),t.response&&t.response.data&&t.response.data.message?d.value=t.response.data.message:d.value="Failed to update category. Please try again."}finally{f.value=!1}};return G(()=>I.params.id,t=>{t&&D(t)},{immediate:!0}),J(()=>{const t=I.params.id;t&&D(t)}),(t,e)=>{const r=Q("router-link");return u(),g("div",se,[x.value?(u(),g("div",te,e[10]||(e[10]=[a("div",{class:"loading-spinner"},[a("i",{class:"fas fa-spinner fa-spin"})],-1),a("p",{class:"loading-text"},"Loading category...",-1)]))):d.value&&!i.value?(u(),g("div",le,[e[12]||(e[12]=a("div",{class:"error-icon"},[a("i",{class:"fas fa-exclamation-triangle"})],-1)),e[13]||(e[13]=a("h3",{class:"error-title"},"Error Loading Category",-1)),a("p",oe,c(d.value),1),a("button",{class:"retry-btn",onClick:e[0]||(e[0]=l=>D(K(I).params.id))},e[11]||(e[11]=[a("i",{class:"fas fa-redo"},null,-1),o(" Try Again ")]))])):!i.value&&!x.value?(u(),g("div",re,[e[15]||(e[15]=a("div",{class:"not-found-icon"},[a("i",{class:"fas fa-folder-open"})],-1)),e[16]||(e[16]=a("h3",{class:"not-found-title"},"Category Not Found",-1)),e[17]||(e[17]=a("p",{class:"not-found-message"},"The requested category does not exist or has been deleted.",-1)),n(r,{to:"/admin/categories",class:"back-btn"},{default:p(()=>e[14]||(e[14]=[a("i",{class:"fas fa-arrow-left"},null,-1),o(" Back to Categories ")])),_:1})])):(u(),g("div",ie,[a("div",ne,[a("div",de,[a("nav",me,[n(r,{to:"/admin",class:"breadcrumb-item"},{default:p(()=>e[18]||(e[18]=[o("Dashboard")])),_:1}),e[20]||(e[20]=a("span",{class:"breadcrumb-separator"},"/",-1)),n(r,{to:"/admin/categories",class:"breadcrumb-item"},{default:p(()=>e[19]||(e[19]=[o("Categories")])),_:1}),e[21]||(e[21]=a("span",{class:"breadcrumb-separator"},"/",-1)),n(r,{to:`/admin/categories/${i.value.id}`,class:"breadcrumb-item"},{default:p(()=>[o(c(i.value.name),1)]),_:1},8,["to"]),e[22]||(e[22]=a("span",{class:"breadcrumb-separator"},"/",-1)),e[23]||(e[23]=a("span",{class:"breadcrumb-item breadcrumb-current"},"Edit",-1))]),e[24]||(e[24]=a("h1",{class:"edit-title"},"Edit Category",-1)),e[25]||(e[25]=a("p",{class:"edit-subtitle"},"Update category information and settings",-1))]),a("div",ge,[n(r,{to:`/admin/categories/${i.value.id}`,class:"action-btn action-btn-secondary"},{default:p(()=>e[26]||(e[26]=[a("i",{class:"fas fa-eye"},null,-1),o(" View Category ")])),_:1},8,["to"]),n(r,{to:"/admin/categories",class:"action-btn action-btn-light"},{default:p(()=>e[27]||(e[27]=[a("i",{class:"fas fa-arrow-left"},null,-1),o(" Back to Categories ")])),_:1})])]),d.value?(u(),g("div",ue,[a("div",ce,[e[28]||(e[28]=a("i",{class:"fas fa-exclamation-circle"},null,-1)),a("span",null,c(d.value),1)]),a("button",{class:"message-close",onClick:e[1]||(e[1]=l=>d.value="")},e[29]||(e[29]=[a("i",{class:"fas fa-times"},null,-1)]))])):M("",!0),b.value?(u(),g("div",pe,[a("div",fe,[e[30]||(e[30]=a("i",{class:"fas fa-check-circle"},null,-1)),a("span",null,c(b.value),1)]),a("button",{class:"message-close",onClick:e[2]||(e[2]=l=>b.value="")},e[31]||(e[31]=[a("i",{class:"fas fa-times"},null,-1)]))])):M("",!0),a("form",{onSubmit:W(z,["prevent"]),class:"edit-form"},[a("div",ve,[e[36]||(e[36]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-info-circle"}),o(" Basic Information ")])],-1)),a("div",ye,[a("div",be,[a("div",Ce,[e[32]||(e[32]=a("label",{class:"form-label"},[o(" Name "),a("span",{class:"required"},"*")],-1)),v(a("input",{class:"form-input",type:"text",placeholder:"Category name","onUpdate:modelValue":e[3]||(e[3]=l=>s.name=l),onInput:V,required:""},null,544),[[y,s.name]])]),a("div",Ie,[e[33]||(e[33]=a("label",{class:"form-label"},[o(" Slug "),a("span",{class:"required"},"*")],-1)),v(a("input",{class:"form-input form-input-code",type:"text",placeholder:"category-slug","onUpdate:modelValue":e[4]||(e[4]=l=>s.slug=l),required:""},null,512),[[y,s.slug]]),e[34]||(e[34]=a("p",{class:"form-help"},"URL-friendly version of the name. Auto-generated but can be edited.",-1))])]),a("div",xe,[e[35]||(e[35]=a("label",{class:"form-label"},"Description",-1)),v(a("textarea",{class:"form-textarea",placeholder:"Category description","onUpdate:modelValue":e[5]||(e[5]=l=>s.description=l),rows:"3"},null,512),[[y,s.description]])])])]),a("div",we,[e[38]||(e[38]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-sitemap"}),o(" Category Hierarchy ")])],-1)),a("div",ke,[a("div",De,[e[37]||(e[37]=a("label",{class:"form-label"},"Parent Category",-1)),n(Y,{modelValue:s.parentId,"onUpdate:modelValue":e[6]||(e[6]=l=>s.parentId=l),"exclude-category-id":i.value.id,label:"",placeholder:"Search for parent category (leave empty for top level)...","help-text":"Select a parent category or leave empty to make this a root category",onChange:O},null,8,["modelValue","exclude-category-id"])])])]),a("div",Ue,[e[43]||(e[43]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-images"}),o(" Images ")])],-1)),a("div",Ee,[a("div",he,[e[39]||(e[39]=a("label",{class:"form-label"},"Category Image",-1)),n(Z,{ref_key:"categoryImageManager",ref:w,"entity-type":"category","entity-id":i.value.id,"current-image":s.image,"image-alt":"Category Image","local-mode":!1,onImageUploaded:P,onImageRemoved:N,onImageChanged:R},null,8,["entity-id","current-image"]),e[40]||(e[40]=a("p",{class:"form-help"},"Upload an image to represent this category. Recommended size: 300x300 pixels.",-1))]),a("div",Me,[e[41]||(e[41]=a("label",{class:"form-label"},"Meta Image (SEO)",-1)),n(ee,{ref_key:"metaImageManager",ref:k,"entity-type":"category","entity-id":i.value.id,"current-image":s.metaImage,"social-preview-title":s.name||"Category Name","social-preview-description":s.description||"Category Description","social-preview-url":`https://marketplace.com/categories/${s.slug||i.value.id}`,"local-mode":!1,onMetaImageUploaded:B,onMetaImageRemoved:$,onMetaImageChanged:_},null,8,["entity-id","current-image","social-preview-title","social-preview-description","social-preview-url"]),e[42]||(e[42]=a("p",{class:"form-help"},"Upload a meta image for SEO and social media sharing.",-1))])])]),a("div",Se,[e[48]||(e[48]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-search"}),o(" SEO Settings ")])],-1)),a("div",Te,[a("div",Ve,[a("div",Oe,[e[44]||(e[44]=a("label",{class:"form-label"},"Meta Title",-1)),v(a("input",{class:"form-input",type:"text",placeholder:"SEO title for search engines","onUpdate:modelValue":e[7]||(e[7]=l=>s.metaTitle=l),maxlength:"60"},null,512),[[y,s.metaTitle]]),a("p",Pe,[a("span",{class:S({"text-danger":s.metaTitle&&s.metaTitle.length>60})},c(s.metaTitle?s.metaTitle.length:0)+"/60 characters ",3)])]),a("div",Ne,[e[45]||(e[45]=a("label",{class:"form-label"},"Display Order",-1)),v(a("input",{class:"form-input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":e[8]||(e[8]=l=>s.displayOrder=l)},null,512),[[y,s.displayOrder,void 0,{number:!0}]]),e[46]||(e[46]=a("p",{class:"form-help"},"Categories with lower numbers will be displayed first.",-1))])]),a("div",Re,[e[47]||(e[47]=a("label",{class:"form-label"},"Meta Description",-1)),v(a("textarea",{class:"form-textarea",placeholder:"SEO description for search engines","onUpdate:modelValue":e[9]||(e[9]=l=>s.metaDescription=l),maxlength:"160",rows:"3"},null,512),[[y,s.metaDescription]]),a("p",Be,[a("span",{class:S({"text-danger":s.metaDescription&&s.metaDescription.length>160})},c(s.metaDescription?s.metaDescription.length:0)+"/160 characters ",3)])])])]),a("div",$e,[a("button",{type:"submit",class:S(["action-btn action-btn-primary action-btn-large",{"action-btn-loading":f.value}]),disabled:f.value},[f.value?(u(),g("i",qe)):(u(),g("i",Le)),o(" "+c(f.value?"Saving...":"Save Changes"),1)],10,_e),n(r,{to:`/admin/categories/${i.value.id}`,class:"action-btn action-btn-secondary action-btn-large"},{default:p(()=>e[49]||(e[49]=[a("i",{class:"fas fa-times"},null,-1),o(" Cancel ")])),_:1},8,["to"])]),C.value.length>0?(u(),g("div",ze,[n(ae,{uploads:C.value,onRetryUpload:q,onCancelUpload:L},null,8,["uploads"])])):M("",!0)],32)]))])}}},Xe=F(Fe,[["__scopeId","data-v-24593a59"]]);export{Xe as default};
