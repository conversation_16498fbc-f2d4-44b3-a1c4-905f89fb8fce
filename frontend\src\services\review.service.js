import apiClient from './api';

class ReviewService {
  // Get all reviews with optional filtering and pagination
  async getAll(params = {}) {
    return await apiClient.get('/reviews', { params });
  }

  // Get review by ID
  async getById(id) {
    return await apiClient.get(`/reviews/${id}`);
  }

  // Get reviews by product ID
  async getByProductId(productId, params = {}) {
    return await apiClient.get(`/products/${productId}/reviews`, { params });
  }

  // Create new review
  async create(reviewData) {
    return await apiClient.post('/reviews', reviewData);
  }

  // Update existing review
  async update(id, reviewData) {
    return await apiClient.put(`/reviews/${id}`, reviewData);
  }

  // Delete review
  async delete(id) {
    return await apiClient.delete(`/reviews/${id}`);
  }

  // Get category reviews
  getCategoryReviews(categorySlug, params = {}) {
    return apiClient.get(`/categories/${categorySlug}/reviews`, { params });
  }

  // Get user reviews
  async getUserReviews(params = {}) {
    try {
      const response = await apiClient.get('/users/me/reviews', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      throw error;
    }
  }

  // Get product review stats
  async getProductReviewStats(productId) {
    try {
      const response = await apiClient.get(`/products/${productId}/reviews/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product review stats:', error);
      throw error;
    }
  }

  // Check if user can review product
  async canUserReview(productId) {
    try {
      const response = await apiClient.get(`/products/${productId}/can-review`);
      return response.data;
    } catch (error) {
      console.error('Error checking if user can review:', error);
      throw error;
    }
  }
}

export default new ReviewService();