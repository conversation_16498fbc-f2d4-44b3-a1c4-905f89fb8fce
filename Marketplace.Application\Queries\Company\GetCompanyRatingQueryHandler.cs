using MediatR;
using Microsoft.EntityFrameworkCore;
using Marketplace.Infrastructure.Persistence;

namespace Marketplace.Application.Queries.Company;

public class GetCompanyRatingQueryHandler : IRequestHandler<GetCompanyRatingQuery, CompanyRatingResponse>
{
    private readonly MarketplaceDbContext _context;

    public GetCompanyRatingQueryHandler(MarketplaceDbContext context)
    {
        _context = context;
    }

    public async Task<CompanyRatingResponse> Handle(GetCompanyRatingQuery request, CancellationToken cancellationToken)
    {
        // Отримуємо всі рейтинги для продуктів компанії
        var ratings = await _context.Ratings
            .Include(r => r.Product)
            .Where(r => r.Product.CompanyId == request.CompanyId)
            .ToListAsync(cancellationToken);

        if (!ratings.Any())
        {
            return new CompanyRatingResponse
            {
                AverageRating = 0,
                TotalRatings = 0,
                RatingDistribution = new Dictionary<int, int>(),
                Breakdown = new CompanyRatingBreakdown
                {
                    ServiceRating = 0,
                    DeliveryTimeRating = 0,
                    AccuracyRating = 0
                }
            };
        }

        // Розраховуємо середній рейтинг для кожного рейтингу
        var averageRatings = ratings.Select(r => (r.Service + r.DeliveryTime + r.Accuracy) / 3.0).ToList();
        
        // Загальний середній рейтинг
        var overallAverage = averageRatings.Average();

        // Розподіл рейтингів (округлюємо до цілих чисел від 1 до 5)
        var distribution = new Dictionary<int, int>();
        for (int i = 1; i <= 5; i++)
        {
            distribution[i] = 0;
        }

        foreach (var avgRating in averageRatings)
        {
            var roundedRating = (int)Math.Round(avgRating);
            if (roundedRating >= 1 && roundedRating <= 5)
            {
                distribution[roundedRating]++;
            }
        }

        // Детальний розподіл по категоріях
        var breakdown = new CompanyRatingBreakdown
        {
            ServiceRating = ratings.Average(r => r.Service),
            DeliveryTimeRating = ratings.Average(r => r.DeliveryTime),
            AccuracyRating = ratings.Average(r => r.Accuracy)
        };

        return new CompanyRatingResponse
        {
            AverageRating = Math.Round(overallAverage, 2),
            TotalRatings = ratings.Count,
            RatingDistribution = distribution,
            Breakdown = breakdown
        };
    }
}

public class GetCompanyRatingBySlugQueryHandler : IRequestHandler<GetCompanyRatingBySlugQuery, CompanyRatingResponse>
{
    private readonly MarketplaceDbContext _context;
    private readonly IMediator _mediator;

    public GetCompanyRatingBySlugQueryHandler(MarketplaceDbContext context, IMediator mediator)
    {
        _context = context;
        _mediator = mediator;
    }

    public async Task<CompanyRatingResponse> Handle(GetCompanyRatingBySlugQuery request, CancellationToken cancellationToken)
    {
        // Спочатку знаходимо компанію за slug
        var company = await _context.Companies
            .FirstOrDefaultAsync(c => c.Slug.Value == request.Slug, cancellationToken);

        if (company == null)
        {
            return null;
        }

        // Використовуємо основний handler для отримання рейтингу
        var query = new GetCompanyRatingQuery(company.Id);
        return await _mediator.Send(query, cancellationToken);
    }
}
