<template>
  <div class="admin-page-container">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-header-content">
        <div class="admin-breadcrumb">
          <router-link to="/admin/addresses" class="admin-breadcrumb-item">
            <i class="fas fa-map-marker-alt"></i>
            Addresses
          </router-link>
          <span class="admin-breadcrumb-separator">/</span>
          <router-link
            :to="{ name: 'AdminAddressDetail', params: { id: $route.params.id } }"
            class="admin-breadcrumb-item">
            Address Details
          </router-link>
          <span class="admin-breadcrumb-separator">/</span>
          <span class="admin-breadcrumb-item admin-breadcrumb-current">Edit</span>
        </div>
        <h1 class="admin-page-title">Edit Address</h1>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-container" v-if="initialLoading">
      <div class="admin-loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="admin-loading-text">Loading address...</p>
    </div>

    <div v-else class="admin-content-grid">
      <!-- Main Content -->
      <div class="admin-content-main">
        <div class="admin-card">
          <div class="admin-card-header">
            <h2 class="admin-card-title">
              <i class="fas fa-edit"></i>
              Edit Address
            </h2>
          </div>
          <div class="admin-card-content">
            <!-- Error Display -->
            <div class="admin-alert admin-alert--danger" v-if="error">
              <div class="admin-alert-content">
                <i class="fas fa-exclamation-circle"></i>
                <span>{{ error }}</span>
              </div>
              <button class="admin-alert-close" @click="error = null">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <form @submit.prevent="updateAddress" class="admin-form">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  <i class="fas fa-user"></i>
                  User (Optional)
                </label>
                <div class="admin-form-control">
                  <select v-model="form.userId" class="admin-form-select">
                    <option value="">Select a user (optional)</option>
                    <option v-for="user in users" :key="user.id" :value="user.id">
                      {{ user.name }} ({{ user.email }})
                    </option>
                  </select>
                </div>
                <p class="admin-form-help">Leave empty to create an unassigned address</p>
              </div>

              <div class="admin-form-grid admin-form-grid--2-col">
                <div class="admin-form-group">
                  <label class="admin-form-label admin-form-label--required">
                    <i class="fas fa-map"></i>
                    Region
                  </label>
                  <div class="admin-form-control">
                    <input
                      class="admin-form-input"
                      type="text"
                      v-model="form.region"
                      placeholder="Enter region"
                      required>
                  </div>
                </div>

                <div class="admin-form-group">
                  <label class="admin-form-label admin-form-label--required">
                    <i class="fas fa-city"></i>
                    City
                  </label>
                  <div class="admin-form-control">
                    <input
                      class="admin-form-input"
                      type="text"
                      v-model="form.city"
                      placeholder="Enter city"
                      required>
                  </div>
                </div>
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label admin-form-label--required">
                  <i class="fas fa-road"></i>
                  Street
                </label>
                <div class="admin-form-control">
                  <input
                    class="admin-form-input"
                    type="text"
                    v-model="form.street"
                    placeholder="Enter street address"
                    required>
                </div>
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  <i class="fas fa-mail-bulk"></i>
                  Postal Code
                </label>
                <div class="admin-form-control">
                  <input
                    class="admin-form-input"
                    type="text"
                    v-model="form.postalCode"
                    placeholder="Enter postal code">
                </div>
              </div>

              <div class="admin-form-actions">
                <button
                  type="submit"
                  class="admin-btn admin-btn-warning"
                  :disabled="loading || !isFormValid || !hasChanges">
                  <i class="fas fa-save" :class="{ 'fa-spin': loading }"></i>
                  {{ loading ? 'Updating...' : 'Update Address' }}
                </button>
                <button
                  type="button"
                  class="admin-btn admin-btn-secondary"
                  @click="goBack"
                  :disabled="loading">
                  <i class="fas fa-arrow-left"></i>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="admin-content-sidebar">
        <!-- Address Preview Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-eye"></i>
              Address Preview
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-preview-section">
              <label class="admin-preview-label">Full Address:</label>
              <div class="admin-address-preview">
                <div class="admin-address-preview-content">
                  {{ formatPreviewAddress() }}
                </div>
              </div>
            </div>

            <div class="admin-preview-section">
              <label class="admin-preview-label">Assigned User:</label>
              <div class="admin-preview-value">
                <div v-if="selectedUser" class="admin-user-info">
                  <i class="fas fa-user"></i>
                  <span>{{ selectedUser.name }}</span>
                  <small>({{ selectedUser.email }})</small>
                </div>
                <span v-else class="admin-text-muted">
                  <i class="fas fa-user-slash"></i>
                  No user assigned
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Original Address Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-history"></i>
              Original Address
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-preview-section">
              <label class="admin-preview-label">Original:</label>
              <div class="admin-address-preview admin-address-preview--info">
                <div class="admin-address-preview-content">
                  {{ formatOriginalAddress() }}
                </div>
              </div>
            </div>

            <div class="admin-preview-section">
              <label class="admin-preview-label">Original User:</label>
              <div class="admin-preview-value">
                <div v-if="originalAddress.userName" class="admin-user-info">
                  <i class="fas fa-user"></i>
                  <span>{{ originalAddress.userName }}</span>
                </div>
                <span v-else class="admin-text-muted">
                  <i class="fas fa-user-slash"></i>
                  No user assigned
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Unsaved Changes Warning -->
        <div class="admin-card admin-card--warning" v-if="hasChanges">
          <div class="admin-card-header">
            <h3 class="admin-card-title admin-card-title--warning">
              <i class="fas fa-exclamation-triangle"></i>
              Unsaved Changes
            </h3>
          </div>
          <div class="admin-card-content">
            <p class="admin-warning-text">
              You have unsaved changes. Don't forget to save!
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { addressesService } from '@/admin/services/addresses';
import { usersService } from '@/admin/services/users';

const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const initialLoading = ref(true);
const error = ref(null);
const users = ref([]);
const originalAddress = ref({});

const form = ref({
  userId: '',
  region: '',
  city: '',
  street: '',
  postalCode: ''
});

// Computed
const isFormValid = computed(() => {
  return form.value.region.trim() && 
         form.value.city.trim() && 
         form.value.street.trim();
});

const selectedUser = computed(() => {
  if (!form.value.userId) return null;
  return users.value.find(user => user.id === form.value.userId);
});

const hasChanges = computed(() => {
  const original = originalAddress.value;
  const current = form.value;
  
  return (
    current.userId !== (original.userId || '') ||
    current.region !== (original.addressVO?.region || original.region || '') ||
    current.city !== (original.addressVO?.city || original.city || '') ||
    current.street !== (original.addressVO?.street || original.street || '') ||
    current.postalCode !== (original.addressVO?.postalCode || original.postalCode || '')
  );
});

// Methods
const loadAddress = async () => {
  try {
    const response = await addressesService.getAddressById(route.params.id);
    originalAddress.value = response;
    
    // Populate form with current data
    const addressData = response.addressVO || response;
    form.value = {
      userId: response.userId || '',
      region: addressData.region || '',
      city: addressData.city || '',
      street: addressData.street || '',
      postalCode: addressData.postalCode || ''
    };
  } catch (err) {
    error.value = err.message || 'Failed to load address';
  }
};

const loadUsers = async () => {
  try {
    const response = await usersService.getUsers({ pageSize: 1000 }); // Get all users
    users.value = response.data || [];
  } catch (err) {
    console.error('Failed to load users:', err);
    // Don't show error for users loading failure
  }
};

const updateAddress = async () => {
  loading.value = true;
  error.value = null;

  try {
    const addressData = {
      userId: form.value.userId || null,
      region: form.value.region.trim(),
      city: form.value.city.trim(),
      street: form.value.street.trim(),
      postalCode: form.value.postalCode.trim() || null
    };

    await addressesService.updateAddress(route.params.id, addressData);
    router.push({ name: 'AdminAddressDetail', params: { id: route.params.id } });
  } catch (err) {
    error.value = err.message || 'Failed to update address';
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push({ name: 'AdminAddressDetail', params: { id: route.params.id } });
};

const formatPreviewAddress = () => {
  const parts = [];
  if (form.value.street.trim()) parts.push(form.value.street.trim());
  if (form.value.city.trim()) parts.push(form.value.city.trim());
  if (form.value.region.trim()) parts.push(form.value.region.trim());
  if (form.value.postalCode.trim()) parts.push(form.value.postalCode.trim());
  
  return parts.length > 0 ? parts.join(', ') : 'Enter address details...';
};

const formatOriginalAddress = () => {
  const original = originalAddress.value;
  const addressData = original.addressVO || original;
  const parts = [];
  
  if (addressData.street) parts.push(addressData.street);
  if (addressData.city) parts.push(addressData.city);
  if (addressData.region) parts.push(addressData.region);
  if (addressData.postalCode) parts.push(addressData.postalCode);
  
  return parts.length > 0 ? parts.join(', ') : 'No address data';
};

// Lifecycle
onMounted(async () => {
  await Promise.all([loadAddress(), loadUsers()]);
  initialLoading.value = false;
});
</script>

<style scoped>
/* Import admin styles */
@import '@/assets/css/admin/admin.css';

/* Address preview component */
.admin-address-preview {
  background: var(--admin-gray-50);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  margin-top: var(--admin-space-sm);
  min-height: 3rem;
  display: flex;
  align-items: center;
}

.admin-address-preview--info {
  background: var(--admin-info-bg);
  border-color: var(--admin-info);
  color: var(--admin-info-dark);
}

.admin-address-preview--warning {
  background: var(--admin-warning-bg);
  border-color: var(--admin-warning);
  color: var(--admin-warning-dark);
}

.admin-address-preview-content {
  font-weight: var(--admin-font-medium);
  font-size: var(--admin-text-base);
  line-height: var(--admin-line-height-relaxed);
}

/* Preview sections */
.admin-preview-section {
  margin-bottom: var(--admin-space-lg);
}

.admin-preview-section:last-child {
  margin-bottom: 0;
}

.admin-preview-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin-bottom: var(--admin-space-xs);
  display: block;
}

.admin-preview-value {
  margin-top: var(--admin-space-sm);
}

/* User info display */
.admin-user-info {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  color: var(--admin-gray-900);
}

.admin-user-info i {
  color: var(--admin-primary);
  width: 16px;
}

.admin-user-info small {
  color: var(--admin-gray-600);
  font-size: var(--admin-text-xs);
}

/* Warning styles */
.admin-card--warning {
  border-color: var(--admin-warning);
  background: var(--admin-warning-bg);
}

.admin-card-title--warning {
  color: var(--admin-warning-dark);
}

.admin-warning-text {
  color: var(--admin-warning-dark);
  font-weight: var(--admin-font-medium);
}

/* Form improvements */
.admin-form-actions {
  display: flex;
  gap: var(--admin-space-md);
  margin-top: var(--admin-space-xl);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-content-grid {
    grid-template-columns: 1fr;
  }

  .admin-form-grid--2-col {
    grid-template-columns: 1fr;
  }

  .admin-form-actions {
    flex-direction: column;
  }

  .admin-user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-xs);
  }
}
</style>
