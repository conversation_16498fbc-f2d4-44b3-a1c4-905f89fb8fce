# Admin Panel Structure

This folder contains all the necessary files for the admin panel in the frontend application. The admin panel is organized into several sections, each with its own set of components and services.

## Directory Structure

```
ClientApp/
├── src/
│   ├── views/admin/             # Main admin view components
│   │   ├── Dashboard.vue        # Admin dashboard
│   │   ├── Products.vue         # Product management
│   │   ├── Categories.vue       # Category management
│   │   ├── Users.vue            # User management
│   │   ├── Orders.vue           # Order management
│   │   └── SellerRequests.vue   # Seller application management
│   │
│   ├── components/admin/        # Admin-specific components
│   │   ├── common/              # Shared components across admin sections
│   │   ├── dashboard/           # Dashboard-specific components
│   │   ├── products/            # Product management components
│   │   ├── categories/          # Category management components
│   │   ├── users/               # User management components
│   │   ├── orders/              # Order management components
│   │   └── seller-requests/     # Seller application components
│   │
│   ├── services/admin/          # Admin API services
│   │   ├── dashboard.js         # Dashboard data services
│   │   ├── products.js          # Product management services
│   │   ├── categories.js        # Category management services
│   │   ├── users.js             # User management services
│   │   ├── orders.js            # Order management services
│   │   └── sellerRequests.js    # Seller application services
│   │
│   └── layouts/
│       └── AdminLayout.vue      # Main admin layout with sidebar and header
```

## Features

### Dashboard
- Overview statistics (products, users, orders, revenue)
- Sales charts
- Recent orders
- Pending seller applications

### Products
- List all products with filtering and pagination
- Add, edit, and delete products
- Manage product images
- Set product status (active/inactive)

### Categories
- List all categories
- Add, edit, and delete categories
- View products in each category

### Users
- List all users with filtering and pagination
- Add, edit, and delete users
- Change user roles (Admin, Seller, Buyer)
- View user orders

### Orders
- List all orders with filtering and pagination
- View order details
- Update order status
- Add order notes
- Process refunds

### Seller Applications
- List all seller applications
- View application details
- Approve or reject applications
- Provide rejection reasons

## Common Components

- `AdminStatCard.vue` - Display statistics in card format
- `Pagination.vue` - Reusable pagination component
- `ConfirmDialog.vue` - Confirmation dialog for destructive actions
- `StatusBadge.vue` - Display status with appropriate color coding

## Usage

The admin panel is accessible at the `/admin` route and is protected by authentication and role-based authorization. Only users with the 'Admin' role can access these pages.

## API Integration

Each section has its own service file that handles API calls to the backend. These services are located in the `services/admin/` directory.
