using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.ProductImage;

public class UpdateProductImageAltTextCommandHandler : IRequestHandler<UpdateProductImageAltTextCommand, bool>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly ILogger<UpdateProductImageAltTextCommandHandler> _logger;

    public UpdateProductImageAltTextCommandHandler(
        IProductImageRepository productImageRepository,
        ILogger<UpdateProductImageAltTextCommandHandler> logger)
    {
        _productImageRepository = productImageRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateProductImageAltTextCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation($"Updating alt text for image {request.ImageId} to '{request.AltText}'");

            // Отримуємо зображення
            var productImage = await _productImageRepository.GetByIdAsync(request.ImageId, cancellationToken);
            if (productImage == null)
            {
                _logger.LogWarning($"Product image {request.ImageId} not found");
                return false;
            }

            // Перевіряємо, чи зображення належить до правильного продукту
            if (productImage.ProductId != request.ProductId)
            {
                _logger.LogWarning($"Product image {request.ImageId} does not belong to product {request.ProductId}");
                return false;
            }

            // Оновлюємо alt-текст
            productImage.Update(altText: request.AltText);
            await _productImageRepository.UpdateAsync(productImage, cancellationToken);

            _logger.LogInformation($"Successfully updated alt text for image {request.ImageId} to '{request.AltText}'");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating alt text for image {request.ImageId}");
            return false;
        }
    }
}
