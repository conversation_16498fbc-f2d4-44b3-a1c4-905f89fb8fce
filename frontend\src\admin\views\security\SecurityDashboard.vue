<template>
  <div class="security-dashboard">
    <!-- Header -->
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Security Dashboard</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button class="button is-primary" @click="refreshData" :class="{ 'is-loading': loading }">
              <span class="icon"><i class="fas fa-sync-alt"></i></span>
              <span>Refresh</span>
            </button>
            <div class="select">
              <select v-model="timeRange" @change="refreshData">
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- Loading State -->
    <div v-if="loading && isFirstLoad" class="has-text-centered">
      <div class="loader-wrapper">
        <div class="loader is-loading"></div>
        <p>Loading security dashboard...</p>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div v-else>
      <!-- Security Alerts -->
      <div v-if="alerts.length > 0" class="notification is-warning mb-5">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <span class="icon">
                <i class="fas fa-exclamation-triangle"></i>
              </span>
              <strong>Security Alerts ({{ alerts.length }})</strong>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button class="button is-small" @click="showAllAlerts = !showAllAlerts">
                {{ showAllAlerts ? 'Hide' : 'Show All' }}
              </button>
            </div>
          </div>
        </div>
        <div v-if="showAllAlerts">
          <div v-for="alert in alerts" :key="alert.id" class="box mb-3">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <span class="tag" :class="`is-${getSeverityColor(alert.severity)}`">
                    {{ alert.severity }}
                  </span>
                  <span class="ml-3">{{ alert.message }}</span>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <small class="has-text-grey">{{ formatTimestamp(alert.timestamp) }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="columns is-multiline mb-5">
        <div class="column is-3">
          <div class="card stats-card">
            <div class="card-content">
              <div class="level">
                <div class="level-left">
                  <div>
                    <p class="heading">Active Sessions</p>
                    <p class="title">{{ statistics.totalActiveSessions }}</p>
                  </div>
                </div>
                <div class="level-right">
                  <span class="icon is-large has-text-info">
                    <i class="fas fa-users fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="column is-3">
          <div class="card stats-card">
            <div class="card-content">
              <div class="level">
                <div class="level-left">
                  <div>
                    <p class="heading">Suspicious Activities</p>
                    <p class="title">{{ statistics.totalSuspiciousActivities }}</p>
                  </div>
                </div>
                <div class="level-right">
                  <span class="icon is-large has-text-warning">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="column is-3">
          <div class="card stats-card">
            <div class="card-content">
              <div class="level">
                <div class="level-left">
                  <div>
                    <p class="heading">Blocked IPs</p>
                    <p class="title">{{ statistics.totalBlockedIps }}</p>
                  </div>
                </div>
                <div class="level-right">
                  <span class="icon is-large has-text-danger">
                    <i class="fas fa-ban fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="column is-3">
          <div class="card stats-card">
            <div class="card-content">
              <div class="level">
                <div class="level-left">
                  <div>
                    <p class="heading">Failed Logins (24h)</p>
                    <p class="title">{{ statistics.failedLoginAttempts24h }}</p>
                  </div>
                </div>
                <div class="level-right">
                  <span class="icon is-large has-text-danger">
                    <i class="fas fa-times-circle fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts and Tables -->
      <div class="columns">
        <!-- Security Trends Chart -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Security Trends</p>
            </div>
            <div class="card-content">
              <div class="chart-container">
                <canvas ref="trendsChart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Top IP Addresses -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Top IP Addresses</p>
            </div>
            <div class="card-content">
              <div v-if="statistics.topIpAddresses.length === 0" class="has-text-centered has-text-grey">
                No data available
              </div>
              <div v-else>
                <div v-for="ip in statistics.topIpAddresses.slice(0, 5)" :key="ip.ipAddress" class="level mb-3">
                  <div class="level-left">
                    <div class="level-item">
                      <div>
                        <p class="has-text-weight-semibold">{{ ip.ipAddress }}</p>
                        <p class="is-size-7 has-text-grey">{{ ip.location }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="level-right">
                    <div class="level-item">
                      <div class="has-text-right">
                        <p class="has-text-weight-semibold">{{ ip.requestsCount }}</p>
                        <p class="is-size-7 has-text-grey">requests</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activities -->
      <div class="columns">
        <!-- Recent Suspicious Activities -->
        <div class="column is-6">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Recent Suspicious Activities</p>
            </div>
            <div class="card-content">
              <div v-if="recentSuspiciousActivities.length === 0" class="has-text-centered has-text-grey">
                No suspicious activities detected
              </div>
              <div v-else>
                <div v-for="activity in recentSuspiciousActivities" :key="activity.id" class="box mb-3">
                  <div class="level">
                    <div class="level-left">
                      <div class="level-item">
                        <span class="tag" :class="`is-${getSeverityColor(activity.severity)}`">
                          {{ activity.severity }}
                        </span>
                        <div class="ml-3">
                          <p class="has-text-weight-semibold">{{ activity.activityType }}</p>
                          <p class="is-size-7">{{ activity.description }}</p>
                        </div>
                      </div>
                    </div>
                    <div class="level-right">
                      <div class="level-item">
                        <small class="has-text-grey">{{ formatTimestamp(activity.timestamp) }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Top Active Users -->
        <div class="column is-6">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Top Active Users</p>
            </div>
            <div class="card-content">
              <div v-if="statistics.topActiveUsers.length === 0" class="has-text-centered has-text-grey">
                No user activity data
              </div>
              <div v-else>
                <div v-for="user in statistics.topActiveUsers.slice(0, 5)" :key="user.userId" class="level mb-3">
                  <div class="level-left">
                    <div class="level-item">
                      <div>
                        <p class="has-text-weight-semibold">{{ user.userName }}</p>
                        <p class="is-size-7 has-text-grey">{{ user.userEmail }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="level-right">
                    <div class="level-item">
                      <div class="has-text-right">
                        <p class="has-text-weight-semibold">{{ user.sessionsCount }}</p>
                        <p class="is-size-7 has-text-grey">sessions</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { securityService } from '@/admin/services/security';
import Chart from 'chart.js/auto';

// Reactive data
const loading = ref(false);
const error = ref(null);
const isFirstLoad = ref(true);
const timeRange = ref('24h');
const showAllAlerts = ref(false);

const statistics = ref({
  totalActiveSessions: 0,
  totalSuspiciousActivities: 0,
  totalBlockedIps: 0,
  failedLoginAttempts24h: 0,
  successfulLogins24h: 0,
  newUsers24h: 0,
  topIpAddresses: [],
  topActiveUsers: [],
  securityTrends: []
});

const alerts = ref([]);
const recentSuspiciousActivities = ref([]);
const trendsChart = ref(null);
let chartInstance = null;

// Methods
const refreshData = async () => {
  loading.value = true;
  error.value = null;

  try {
    await Promise.all([
      loadStatistics(),
      loadAlerts(),
      loadRecentActivities()
    ]);

    await nextTick();
    updateChart();
  } catch (err) {
    error.value = err.message || 'Failed to load security dashboard';
  } finally {
    loading.value = false;
    isFirstLoad.value = false;
  }
};

const loadStatistics = async () => {
  try {
    const fromDate = getFromDate();
    const toDate = new Date();

    const stats = await securityService.getSecurityStatistics(fromDate, toDate);
    statistics.value = {
      totalActiveSessions: stats.totalActiveSessions || 0,
      totalSuspiciousActivities: stats.totalSuspiciousActivities || 0,
      totalBlockedIps: stats.totalBlockedIps || 0,
      failedLoginAttempts24h: stats.failedLoginAttempts24h || 0,
      successfulLogins24h: stats.successfulLogins24h || 0,
      newUsers24h: stats.newUsers24h || 0,
      topIpAddresses: stats.topIpAddresses || [],
      topActiveUsers: stats.topActiveUsers || [],
      securityTrends: stats.securityTrends || []
    };
  } catch (err) {
    console.error('Error loading statistics:', err);
    // Use mock data for demo
    statistics.value = {
      totalActiveSessions: 45,
      totalSuspiciousActivities: 3,
      totalBlockedIps: 12,
      failedLoginAttempts24h: 28,
      successfulLogins24h: 156,
      newUsers24h: 8,
      topIpAddresses: [
        { ipAddress: '*************', requestsCount: 245, location: 'Ukraine' },
        { ipAddress: '***********', requestsCount: 89, location: 'Unknown' },
        { ipAddress: '********', requestsCount: 67, location: 'Local' }
      ],
      topActiveUsers: [
        { userId: '1', userName: 'admin', userEmail: '<EMAIL>', sessionsCount: 5 },
        { userId: '2', userName: 'moderator', userEmail: '<EMAIL>', sessionsCount: 3 }
      ],
      securityTrends: generateMockTrends()
    };
  }
};

const loadAlerts = async () => {
  try {
    // Mock alerts for demo
    alerts.value = [
      {
        id: 1,
        severity: 'HIGH',
        message: 'Multiple failed login attempts detected from IP *************',
        timestamp: new Date(Date.now() - 30 * 60 * 1000)
      },
      {
        id: 2,
        severity: 'MEDIUM',
        message: 'Unusual login location detected <NAME_EMAIL>',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
      }
    ];
  } catch (err) {
    console.error('Error loading alerts:', err);
  }
};

const loadRecentActivities = async () => {
  try {
    // Mock recent activities for demo
    recentSuspiciousActivities.value = [
      {
        id: 1,
        activityType: 'MULTIPLE_FAILED_LOGINS',
        description: 'Multiple failed login attempts from same IP',
        severity: 'HIGH',
        timestamp: new Date(Date.now() - 45 * 60 * 1000)
      },
      {
        id: 2,
        activityType: 'UNUSUAL_LOGIN_LOCATION',
        description: 'Login from unusual geographic location',
        severity: 'MEDIUM',
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000)
      }
    ];
  } catch (err) {
    console.error('Error loading recent activities:', err);
  }
};

const updateChart = () => {
  if (!trendsChart.value) return;

  if (chartInstance) {
    chartInstance.destroy();
  }

  const ctx = trendsChart.value.getContext('2d');
  const trends = statistics.value.securityTrends;

  chartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: trends.map(t => t.date.toLocaleDateString()),
      datasets: [
        {
          label: 'Login Attempts',
          data: trends.map(t => t.loginAttempts),
          borderColor: 'rgb(54, 162, 235)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          tension: 0.1
        },
        {
          label: 'Failed Logins',
          data: trends.map(t => t.failedLogins),
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.1)',
          tension: 0.1
        },
        {
          label: 'Suspicious Activities',
          data: trends.map(t => t.suspiciousActivities),
          borderColor: 'rgb(255, 205, 86)',
          backgroundColor: 'rgba(255, 205, 86, 0.1)',
          tension: 0.1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
};

// Utility methods
const getFromDate = () => {
  const now = new Date();
  switch (timeRange.value) {
    case '24h':
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }
};

const generateMockTrends = () => {
  const trends = [];
  const days = timeRange.value === '24h' ? 1 : timeRange.value === '7d' ? 7 : 30;

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    trends.push({
      date,
      loginAttempts: Math.floor(Math.random() * 100) + 50,
      failedLogins: Math.floor(Math.random() * 20) + 5,
      suspiciousActivities: Math.floor(Math.random() * 5),
      blockedIps: Math.floor(Math.random() * 3)
    });
  }

  return trends;
};

const getSeverityColor = (severity) => {
  switch (severity?.toUpperCase()) {
    case 'HIGH': return 'danger';
    case 'MEDIUM': return 'warning';
    case 'LOW': return 'info';
    default: return 'light';
  }
};

const formatTimestamp = (timestamp) => {
  return securityService.formatTimestamp(timestamp);
};

// Lifecycle
onMounted(() => {
  refreshData();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy();
  }
});
</script>

<style scoped>
.security-dashboard {
  padding: 1rem;
}

.stats-card {
  height: 100%;
}

.stats-card .card-content {
  padding: 1.5rem;
}

.chart-container {
  position: relative;
  height: 300px;
}

.loader-wrapper {
  padding: 3rem;
}

.box {
  border-left: 4px solid #dbdbdb;
}

.box.is-danger {
  border-left-color: #ff3860;
}

.box.is-warning {
  border-left-color: #ffdd57;
}

.box.is-info {
  border-left-color: #3273dc;
}
</style>
