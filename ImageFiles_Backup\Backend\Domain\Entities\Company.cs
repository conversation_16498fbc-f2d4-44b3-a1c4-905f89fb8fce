﻿using Marketplace.Domain.ValueObjects;

namespace Marketplace.Domain.Entities;

public class Company : IEntity
{
    private string _name;
    private Slug _slug;
    private string _description;
    private Url? _image;
    private Email _contactEmail;
    private Phone _contactPhone;
    private AddressVO _addressVO;
    private Meta _meta;
    public Guid Id { get; set; }
    public string Name
    {
        get => _name;
        set => UpdateName(value);
    }
    public Slug Slug
    {
        get => _slug;
        set => UpdateSlug(value);
    }
    public string Description
    {
        get => _description;
        set => UpdateDescription(value);
    }
    public Url? Image
    {
        get => _image;
        set => UpdateImage(value);
    }
    public Email ContactEmail
    {
        get => _contactEmail;
        set => UpdateContactEmail(value);
    }
    public Phone ContactPhone
    {
        get => _contactPhone;
        set => UpdateContactPhone(value);
    }
    public AddressVO Address
    {
        get => _addressVO;
        set => UpdateAddress(value);
    }
    public bool IsFeatured { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public Guid? ApprovedByUserId { get; set; }
    public User? ApprovedByUser { get; set; }
    public Meta Meta
    {
        get => _meta;
        set => UpdateMeta(value);
    }

    private Company() { } // Для EF Core

    public Company(
        string name,
        Slug slug,
        string description,
        Url? image,
        Email contactEmail,
        Phone contactPhone,
        AddressVO address,
        Meta meta)
    {
        ValidateCreation(name, slug, description, image, contactEmail, contactPhone, address, meta);

        Id = Guid.NewGuid();
        Name = name;
        Slug = slug;
        Description = description;
        Image = image;
        ContactEmail = contactEmail;
        ContactPhone = contactPhone;
        Address = address;
        Meta = meta;
        IsFeatured = false;
    }

    public void Update(
        string? name = null,
        Slug? slug = null,
        string? description = null,
        Url? image = null,
        Email? contactEmail = null,
        Phone? contactPhone = null,
        string? addressRegion = null,
        string? addressStreet = null,
        string? addressCity = null,
        string? addressPostalCode = null,
        string? metaTitle = null,
        string? metaDescription = null,
        Url? metaImage = null)
    {
        if (name != null) UpdateName(name);
        if (slug != null) UpdateSlug(slug);
        if (description != null) UpdateDescription(description);
        if (image != null) UpdateImage(image);
        if (contactEmail != null) UpdateContactEmail(contactEmail);
        if (contactPhone != null) UpdateContactPhone(contactPhone);

        if (addressRegion != null || addressStreet != null || addressCity != null || addressPostalCode != null)
        {
            UpdateAddress(new AddressVO(
                addressRegion ?? Address.Region,
                addressCity ?? Address.City,
                addressStreet ?? Address.Street,
                addressPostalCode ?? Address.PostalCode));
        }

        if (metaTitle != null || metaDescription != null || metaImage != null)
        {
            UpdateMeta(new Meta(
                metaTitle ?? Meta.Title,
                metaDescription ?? Meta.Description,
                metaImage ?? Meta.Image));
        }
    }

    public void MakeFeatured()
    {
        IsFeatured = true;
    }

    public void Approve(Guid approvedById)
    {
        ApprovedAt = DateTime.UtcNow;
        ApprovedByUserId = approvedById;
    }

    public void UpdateName(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
        if (name.Length > 100) throw new ArgumentException("Ім'я не має перевищувати 100 символів.", nameof(name));
        _name = name;
    }

    public void UpdateSlug(Slug slug)
    {
        _slug = slug ?? throw new ArgumentNullException(nameof(slug));
    }

    public void UpdateDescription(string description)
    {
        if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
        if (description.Length > 500) throw new ArgumentException("Опис не має перевищувати 500 символів.", nameof(description));
        _description = description;
    }

    public void UpdateImage(string image)
    {
        _image = new Url(image);
    }
    public void UpdateImage(Url? image)
    {
        _image = image;
    }

    public void UpdateContactEmail(Email contactEmail)
    {
        _contactEmail = contactEmail ?? throw new ArgumentNullException(nameof(contactEmail));
    }

    public void UpdateContactPhone(Phone contactPhone)
    {
        _contactPhone = contactPhone ?? throw new ArgumentNullException(nameof(contactPhone));
    }

    public void UpdateAddress(AddressVO address)
    {
        _addressVO = address ?? throw new ArgumentNullException(nameof(address));
    }

    public void UpdateMeta(Meta meta)
    {
        _meta = meta ?? throw new ArgumentNullException(nameof(meta));
    }

    public void ValidateCreation(
        string name,
        Slug slug,
        string description,
        Url? image,
        Email contactEmail,
        Phone contactPhone,
        AddressVO address,
        Meta meta)
    {
        if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
        if (name.Length > 100) throw new ArgumentException("Ім'я не має перевищувати 100 символів.", nameof(name));
        if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
        if (description.Length > 500) throw new ArgumentException("Опис не має перевищувати 500 символів.", nameof(description));
        if (slug == null) throw new ArgumentNullException(nameof(slug));
        // image може бути null
        if (contactEmail == null) throw new ArgumentNullException(nameof(contactEmail));
        if (contactPhone == null) throw new ArgumentNullException(nameof(contactPhone));
        if (address == null) throw new ArgumentNullException(nameof(address));
        if (meta == null) throw new ArgumentNullException(nameof(meta));
    }
}