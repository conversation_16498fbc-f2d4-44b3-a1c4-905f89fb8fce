using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Product;

public class GetRecommendedProductsQueryHandler : IRequestHandler<GetRecommendedProductsQuery, List<ProductResponse>>
{
    private readonly IProductRepository _productRepository;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;

    public GetRecommendedProductsQueryHandler(
        IProductRepository productRepository,
        IUserRepository userRepository,
        IMapper mapper)
    {
        _productRepository = productRepository;
        _userRepository = userRepository;
        _mapper = mapper;
    }

    public async Task<List<ProductResponse>> Handle(GetRecommendedProductsQuery request, CancellationToken cancellationToken)
    {
        // Якщо користувач не вказаний, повертаємо популярні товари
        if (!request.UserId.HasValue)
        {
            return await GetPopularProductsAsync(request, cancellationToken);
        }

        // Отримуємо користувача з його відвідуваннями категорій
        var user = await _userRepository.GetByIdAsync(request.UserId.Value, cancellationToken);
        if (user == null)
        {
            return await GetPopularProductsAsync(request, cancellationToken);
        }

        // Перевіряємо та скидаємо статистику, якщо потрібно
        user.CheckAndResetCategoryVisitsIfNeeded();

        // Отримуємо топ-3 найбільш відвідувані категорії
        var topCategories = user.GetTopVisitedCategories(3);

        if (!topCategories.Any())
        {
            // Якщо немає відвідувань, повертаємо популярні товари
            return await GetPopularProductsAsync(request, cancellationToken);
        }

        // Отримуємо рекомендовані товари з топ-3 категорій
        return await GetProductsFromTopCategoriesAsync(topCategories, request, cancellationToken);
    }

    private async Task<List<ProductResponse>> GetProductsFromTopCategoriesAsync(
        List<Guid> topCategories,
        GetRecommendedProductsQuery request,
        CancellationToken cancellationToken)
    {
        // Створюємо фільтр для топ-3 категорій
        Expression<Func<Domain.Entities.Product, bool>> filter = p => topCategories.Contains(p.CategoryId);

        // Додаємо фільтр за статусом, якщо вказано
        if (!string.IsNullOrEmpty(request.Status) && int.TryParse(request.Status, out var statusValue))
        {
            var originalFilter = filter;
            filter = p => originalFilter.Compile()(p) && (int)p.Status == statusValue;
        }

        // Отримуємо товари, відсортовані за популярністю (продажі + наявність)
        var products = await _productRepository.GetAllAsync(
            filter: filter,
            orderBy: "Sales", // Сортуємо за продажами для кращих рекомендацій
            descending: true,
            page: 1,
            pageSize: request.Limit ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Product, object>>[] { p => p.Category, p => p.Company }
        );

        return _mapper.Map<List<ProductResponse>>(products);
    }
    private async Task<List<ProductResponse>> GetPopularProductsAsync(
        GetRecommendedProductsQuery request,
        CancellationToken cancellationToken)
    {
        // Створюємо фільтр за статусом, якщо вказано
        Expression<Func<Domain.Entities.Product, bool>>? filter = null;
        if (!string.IsNullOrEmpty(request.Status) && int.TryParse(request.Status, out var statusValue))
        {
            filter = p => (int)p.Status == statusValue;
        }

        // Отримуємо найпопулярніші товари за продажами
        var products = await _productRepository.GetAllAsync(
            filter: filter,
            orderBy: "Sales",
            descending: true,
            page: 1,
            pageSize: request.Limit ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Product, object>>[] { p => p.Category, p => p.Company }
        );

        return _mapper.Map<List<ProductResponse>>(products);
    }
}
