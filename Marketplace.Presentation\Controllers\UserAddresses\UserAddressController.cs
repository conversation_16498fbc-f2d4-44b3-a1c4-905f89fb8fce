using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Marketplace.Application.Commands.Address;
using Marketplace.Application.Queries.Address;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Responses;

namespace Marketplace.Presentation.Controllers.UserAddresses;

[ApiController]
[Route("api/users/me/addresses")]
[Authorize]
public class UserAddressController : BasicApiController
{
    private readonly IMediator _mediator;

    public UserAddressController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Отримує всі адреси поточного користувача
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetUserAddresses(CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(ApiResponse<object>.Failure("Користувач не авторизований."));
        }

        try
        {
            var query = new GetUserAddressesQuery(userId.Value);
            var addresses = await _mediator.Send(query, cancellationToken);

            return Ok(ApiResponse<object>.SuccessWithData(
                addresses,
                "Адреси користувача отримано успішно."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.Failure($"Помилка отримання адрес: {ex.Message}"));
        }
    }

    /// <summary>
    /// Створює нову адресу для поточного користувача
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CreateAddress([FromBody] StoreAddressCommand command, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(ApiResponse<object>.Failure("Користувач не авторизований."));
        }

        try
        {
            var commandWithUserId = command with { UserId = userId.Value };
            var addressId = await _mediator.Send(commandWithUserId, cancellationToken);

            return Ok(ApiResponse<object>.SuccessWithData(
                new { addressId },
                "Адресу створено успішно."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.Failure($"Помилка створення адреси: {ex.Message}"));
        }
    }

    /// <summary>
    /// Оновлює існуючу адресу користувача
    /// </summary>
    [HttpPut("{addressId:guid}")]
    public async Task<IActionResult> UpdateAddress(Guid addressId, [FromBody] UpdateAddressCommand command, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(ApiResponse<object>.Failure("Користувач не авторизований."));
        }

        try
        {
            var commandWithIds = command with { Id = addressId, UserId = userId.Value };
            var result = await _mediator.Send(commandWithIds, cancellationToken);

            return Ok(ApiResponse<object>.SuccessWithData(result, "Адресу оновлено успішно."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.Failure($"Помилка оновлення адреси: {ex.Message}"));
        }
    }

    /// <summary>
    /// Видаляє адресу користувача
    /// </summary>
    [HttpDelete("{addressId:guid}")]
    public async Task<IActionResult> DeleteAddress(Guid addressId, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(ApiResponse<object>.Failure("Користувач не авторизований."));
        }

        try
        {
            var command = new DeleteAddressCommand(addressId, userId.Value);
            var result = await _mediator.Send(command, cancellationToken);

            if (result)
            {
                return Ok(ApiResponse<object>.SuccessWithMessage("Адресу видалено успішно."));
            }
            else
            {
                return NotFound(ApiResponse<object>.Failure("Адресу не знайдено."));
            }
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.Failure($"Помилка видалення адреси: {ex.Message}"));
        }
    }
}
