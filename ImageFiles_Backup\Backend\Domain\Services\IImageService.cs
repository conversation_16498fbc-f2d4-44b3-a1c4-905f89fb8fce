using Microsoft.AspNetCore.Http;

namespace Marketplace.Domain.Services;

/// <summary>
/// Універсальний сервіс для роботи з зображеннями різних типів сутностей
/// </summary>
public interface IImageService
{
    /// <summary>
    /// Завантажити одиночне зображення для сутності
    /// </summary>
    /// <param name="entityType">Тип сутності (product, category, company, user)</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="file">Файл зображення</param>
    /// <param name="imageType">Тип зображення (main, meta, avatar)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат завантаження файлу</returns>
    Task<FileUploadResult> UploadImageAsync(
        string entityType, 
        Guid entityId, 
        IFormFile file, 
        string imageType = "main",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Завантажити множинні зображення для сутності (наприклад, для ProductImage)
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="files">Колекція файлів</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Список результатів завантаження</returns>
    Task<List<FileUploadResult>> UploadMultipleImagesAsync(
        string entityType, 
        Guid entityId, 
        IFormFileCollection files,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Видалити зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageType">Тип зображення</param>
    /// <param name="imageId">ID зображення (для ProductImage)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат видалення</returns>
    Task<bool> DeleteImageAsync(
        string entityType, 
        Guid entityId, 
        string imageType = "main",
        Guid? imageId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Оновити зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="file">Новий файл зображення</param>
    /// <param name="imageType">Тип зображення</param>
    /// <param name="imageId">ID зображення (для ProductImage)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат оновлення</returns>
    Task<FileUploadResult> UpdateImageAsync(
        string entityType, 
        Guid entityId, 
        IFormFile file, 
        string imageType = "main",
        Guid? imageId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Отримати URL зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageType">Тип зображення</param>
    /// <param name="imageId">ID зображення (для ProductImage)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>URL зображення</returns>
    Task<string?> GetImageUrlAsync(
        string entityType, 
        Guid entityId, 
        string imageType = "main",
        Guid? imageId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Отримати всі зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Список зображень</returns>
    Task<List<object>> GetAllImagesAsync(
        string entityType, 
        Guid entityId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Встановити головне зображення (для ProductImage)
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageId">ID зображення</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат операції</returns>
    Task<bool> SetMainImageAsync(
        string entityType, 
        Guid entityId, 
        Guid imageId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Результат завантаження файлу
/// </summary>
public class FileUploadResult
{
    public Guid Id { get; set; }
    public string FileUrl { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
}
