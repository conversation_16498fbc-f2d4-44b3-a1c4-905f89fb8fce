﻿using Marketplace.Domain.Entities;
using System.Linq.Expressions;

namespace Marketplace.Domain.Repositories;

public interface ICompanyRepository : IRepository<Company>
{
    Task<Company?> GetBySlugAsync(string slug, CancellationToken cancellationToken = default, params Expression<Func<Company, object>>[] includes);

    Task<Guid?> GetIdBySlugAsync(string slug, CancellationToken cancellationToken = default);

    Task<IEnumerable<Company>> GetApprovedCompaniesAsync(
        string? filter = null,
        string? orderBy = null,
        bool descending = false,
        int? page = null,
        int? pageSize = null,
        CancellationToken cancellationToken = default,
        params Expression<Func<Company, object>>[] includes);

    Task<IEnumerable<Company>> GetPendingCompaniesAsync(
        string? filter = null,
        string? orderBy = null,
        bool descending = false,
        int? page = null,
        int? pageSize = null,
        CancellationToken cancellationToken = default,
        params Expression<Func<Company, object>>[] includes);

    Task ApproveAsync(Guid id, Guid userId, CancellationToken cancellationToken);

    Task BulkApproveAsync(List<Guid> ids, Guid userId, CancellationToken cancellationToken);

    Task RejectAsync(Guid id, CancellationToken cancellationToken);

    Task BulkRejectAsync(List<Guid> ids, CancellationToken cancellationToken);
}
