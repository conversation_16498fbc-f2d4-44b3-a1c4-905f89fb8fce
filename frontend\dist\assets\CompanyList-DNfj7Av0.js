import{_ as M,g as h,i as T,c as n,a,k as v,l as j,b,z as t,d as L,t as i,s as W,n as q,F as S,p as G,w as x,r as H,m as J,o as l}from"./index-L-hJxM_5.js";import{c as N}from"./companies-B97mkaMy.js";import{u as K}from"./useAdminSearch-BxXMp5oH.js";import{S as Q}from"./SearchAndFilters-B3kez0yT.js";import{P as X}from"./Pagination-DX2plTiq.js";const Y={class:"admin-companies"},Z={key:0,class:"admin-loading-state"},aa={key:1,class:"admin-alert admin-alert-danger"},ea={key:2,class:"admin-empty-state"},sa={key:3,class:"admin-company-table"},ta={class:"admin-table"},la={class:"admin-table-body"},na={class:"admin-table-td admin-logo-cell"},oa={class:"admin-company-logo-wrapper"},ia=["src","alt","onClick","onError","onLoad"],da={key:1,class:"admin-logo-placeholder"},ra={class:"admin-table-td"},ca={class:"admin-company-info"},ma={class:"admin-company-details"},ua={class:"admin-company-name"},ga={class:"admin-company-slug"},pa={class:"admin-table-td"},fa={class:"admin-company-contact"},ha={class:"admin-contact-email"},va={class:"admin-contact-phone"},ba={class:"admin-table-td"},_a={class:"admin-company-location"},ya={class:"admin-location-city"},Ca={class:"admin-location-street"},ka={class:"admin-table-td"},Fa={key:0,class:"admin-badge admin-badge-primary"},wa={key:1,class:"admin-badge admin-badge-secondary"},Aa={class:"admin-table-td"},La={class:"admin-table-actions"},Sa=["onClick","disabled"],xa={key:0,class:"table-loading-overlay"},Na={class:"modal-content"},Ea={class:"modal-header"},Pa={class:"modal-title"},Ba={class:"modal-body text-center"},Ia={key:0,class:"company-logo-viewer"},Va=["src","alt"],Oa={class:"mt-3"},Da={class:"text-muted"},$a={key:1,class:"no-logo-message"},za={__name:"CompanyList",setup(Ra){const g=h(!1),p=h(!1),d=h(null),E=[{key:"featured",label:"Featured",type:"select",columnClass:"is-2",options:[{value:"all",label:"All Companies"},{value:"true",label:"Featured Only"},{value:"false",label:"Regular Only"}]},{key:"hasImage",label:"Logo",type:"select",columnClass:"is-2",options:[{value:"all",label:"All"},{value:"true",label:"With Logo"},{value:"false",label:"No Logo"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",allOption:!1,options:[{value:"ApprovedAt",label:"Approved Date"},{value:"Name",label:"Name"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}],{items:_,loading:c,error:m,isFirstLoad:f,currentPage:P,totalPages:y,totalItems:B,filters:r,fetchData:C,handlePageChange:I}=K({fetchFunction:N.getCompanies,defaultFilters:{featured:"all",sortBy:"ApprovedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:15,clientSideSearch:!1}),V=o=>{r.search=o},O=(o,e)=>{r[o]=e},D=()=>{r.search="",r.featured="all",r.sortBy="ApprovedAt",r.sortOrder="desc"},$=async o=>{if(confirm("Are you sure you want to delete this company? This action cannot be undone.")){g.value=!0;try{await N.deleteCompany(o),await C()}catch(e){m.value=e.message||"Failed to delete company"}finally{g.value=!1}}},z=o=>{d.value=o,p.value=!0},k=()=>{p.value=!1,d.value=null},R=(o,e)=>{e.imageError=!0,console.warn(`Failed to load image for company ${e.name}:`,e.imageUrl)},U=o=>{o.imageError=!1};return T(()=>{C()}),(o,e)=>{var w,A;const F=H("router-link");return l(),n(S,null,[a("div",Y,[e[11]||(e[11]=j('<div class="admin-page-header" data-v-7f60996f><div class="admin-page-title-section" data-v-7f60996f><h1 class="admin-page-title" data-v-7f60996f><i class="fas fa-building admin-page-icon" data-v-7f60996f></i> Companies </h1><p class="admin-page-subtitle" data-v-7f60996f>Manage and monitor company profiles</p></div></div>',1)),b(Q,{filters:t(r),"filter-fields":E,"search-label":"Search Companies","search-placeholder":"Search by name or description...","search-column-class":"is-4","total-items":t(B),"item-name":"companies",loading:t(c),onSearchChanged:V,onFilterChanged:O,onResetFilters:D},null,8,["filters","total-items","loading"]),t(c)&&t(f)?(l(),n("div",Z,e[2]||(e[2]=[a("div",{class:"admin-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-loading-text"},"Loading companies...",-1)]))):t(m)?(l(),n("div",aa,[a("button",{class:"admin-alert-close",onClick:e[0]||(e[0]=s=>m.value=null)},e[3]||(e[3]=[a("i",{class:"fas fa-times"},null,-1)])),L(" "+i(t(m)),1)])):!t(c)&&!t(_).length?(l(),n("div",ea,e[4]||(e[4]=[a("div",{class:"admin-empty-icon"},[a("i",{class:"fas fa-building"})],-1),a("h3",{class:"admin-empty-title"},"No companies found",-1),a("p",{class:"admin-empty-text"},"Try adjusting your search criteria or filters",-1)]))):(l(),n("div",sa,[a("div",{class:q(["admin-table-container",{"admin-table-loading":t(c)&&!t(f)}])},[a("table",ta,[e[9]||(e[9]=a("thead",{class:"admin-table-header"},[a("tr",null,[a("th",{class:"admin-table-th"},"Logo"),a("th",{class:"admin-table-th"},"Name"),a("th",{class:"admin-table-th"},"Contact"),a("th",{class:"admin-table-th"},"Location"),a("th",{class:"admin-table-th"},"Featured"),a("th",{class:"admin-table-th"},"Actions")])],-1)),a("tbody",la,[(l(!0),n(S,null,G(t(_),s=>(l(),n("tr",{key:s.id,class:"admin-table-row"},[a("td",na,[a("div",oa,[s.imageUrl&&!s.imageError?(l(),n("img",{key:0,src:s.imageUrl,alt:s.name+" logo",class:"admin-company-logo clickable",loading:"lazy",onClick:u=>z(s),onError:u=>R(u,s),onLoad:u=>U(s),title:"Click to view full size"},null,40,ia)):(l(),n("div",da,e[5]||(e[5]=[a("i",{class:"fas fa-building"},null,-1)])))])]),a("td",ra,[a("div",ca,[a("div",ma,[a("h4",ua,i(s.name),1),a("p",ga,i(s.slug),1)])])]),a("td",pa,[a("div",fa,[a("p",ha,i(s.contactEmail),1),a("p",va,i(s.contactPhone),1)])]),a("td",ba,[a("div",_a,[a("p",ya,i(s.addressCity)+", "+i(s.addressRegion),1),a("p",Ca,i(s.addressStreet),1)])]),a("td",ka,[s.isFeatured?(l(),n("span",Fa,"Featured")):(l(),n("span",wa,"Regular"))]),a("td",Aa,[a("div",La,[b(F,{to:{name:"AdminCompanyDetail",params:{id:s.id}},class:"admin-btn admin-btn-secondary admin-btn-sm"},{default:x(()=>e[6]||(e[6]=[a("i",{class:"fas fa-eye"},null,-1),a("span",null,"View",-1)])),_:2},1032,["to"]),b(F,{to:{name:"AdminCompanyEdit",params:{id:s.id}},class:"admin-btn admin-btn-warning admin-btn-sm"},{default:x(()=>e[7]||(e[7]=[a("i",{class:"fas fa-edit"},null,-1),a("span",null,"Edit",-1)])),_:2},1032,["to"]),a("button",{class:"admin-btn admin-btn-danger admin-btn-sm",onClick:u=>$(s.id),disabled:g.value},e[8]||(e[8]=[a("i",{class:"fas fa-trash"},null,-1),a("span",null,"Delete",-1)]),8,Sa)])])]))),128))])]),t(c)&&!t(f)?(l(),n("div",xa,e[10]||(e[10]=[a("div",{class:"loading-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1)]))):v("",!0)],2),t(y)>1?(l(),W(X,{key:0,"current-page":t(P),"total-pages":t(y),onPageChanged:t(I)},null,8,["current-page","total-pages","onPageChanged"])):v("",!0)]))]),p.value?(l(),n("div",{key:0,class:"modal fade show d-block",onClick:k},[a("div",{class:"modal-dialog modal-lg",onClick:e[1]||(e[1]=J(()=>{},["stop"]))},[a("div",Na,[a("div",Ea,[a("h5",Pa,[e[12]||(e[12]=a("i",{class:"fas fa-building me-2"},null,-1)),L(" "+i((w=d.value)==null?void 0:w.name)+" - Company Logo ",1)]),a("button",{type:"button",class:"btn-close",onClick:k})]),a("div",Ba,[(A=d.value)!=null&&A.imageUrl?(l(),n("div",Ia,[a("img",{src:d.value.imageUrl,alt:`${d.value.name} logo`,class:"img-fluid rounded",style:{"max-height":"500px"}},null,8,Va),a("div",Oa,[a("h6",null,i(d.value.name),1),a("small",Da,i(d.value.contactEmail||"No email"),1)])])):(l(),n("div",$a,e[13]||(e[13]=[a("i",{class:"fas fa-building fa-3x text-muted mb-3"},null,-1),a("p",null,"No logo available for this company",-1)])))])])])])):v("",!0)],64)}}},qa=M(za,[["__scopeId","data-v-7f60996f"]]);export{qa as default};
