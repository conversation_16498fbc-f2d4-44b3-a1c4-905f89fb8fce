import{_,c as i,o as n,l as me,F as j,p as W,a as e,k as A,t as h,d as T,n as N,g as P,h as U,r as ae,L as te,s as le,H as G,i as he,b as R,w as be,x,y as O,D as Ce,z as se,I as B,e as $e}from"./index-L-hJxM_5.js";import{C as we}from"./ConfirmDialog-hd0r6dWx.js";import{P as ke}from"./Pagination-DX2plTiq.js";const Pe={class:"modern-table-container"},Ie={key:0,class:"table-body"},De={class:"table-cell",style:{width:"80px"}},Se={class:"category-image"},Ae=["src","alt"],Te={key:1,class:"category-img-placeholder"},Ve={class:"table-cell category-info",style:{flex:"2"}},Fe={class:"category-name"},Le={class:"category-slug"},Ee={key:0,class:"category-description"},ze={class:"table-cell",style:{width:"120px"}},He={key:0,class:"badge badge-primary"},xe={key:1,class:"badge badge-info"},Be={class:"table-cell",style:{width:"100px"}},Ne={class:"product-count-container"},Me=["onClick"],Re={class:"table-cell",style:{width:"80px"}},Oe={class:"seo-indicators"},Ue={key:0,class:"seo-indicator seo-indicator-success",title:"Has Meta Title"},qe={key:1,class:"seo-indicator seo-indicator-info",title:"Has Meta Description"},_e={key:2,class:"seo-indicator seo-indicator-warning",title:"Has Meta Image"},je={key:3,class:"seo-indicator seo-indicator-none",title:"No SEO data"},We={class:"table-cell",style:{width:"140px"}},Qe={class:"action-buttons"},Ge=["onClick"],Je=["onClick","disabled","title"],Ke={key:1,class:"table-loading"},Xe={key:2,class:"table-empty"},Ye={__name:"CategoryTable",props:{categories:{type:Array,required:!0},allCategories:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["edit","delete","view-products"],setup(r){const E=r,D=(d,l)=>d?d.length>l?d.substring(0,l)+"...":d:"",$=d=>!d||d===0?"badge-light":d<5?"badge-warning":d<20?"badge-info":"badge-success",y=d=>E.allCategories.some(l=>l.parentId===d),v=d=>{const l=(d.productCount||0)>0,s=y(d.id);return!l&&!s},b=d=>{const l=(d.productCount||0)>0,s=y(d.id);return l&&s?"Cannot delete: category has products and subcategories":l?"Cannot delete: category has products":s?"Cannot delete: category has subcategories":"Delete category"};return(d,l)=>(n(),i("div",Pe,[l[13]||(l[13]=me('<div class="table-header" data-v-b82d2bd2><div class="table-header-cell" style="width:80px;" data-v-b82d2bd2>Image</div><div class="table-header-cell" style="flex:2;" data-v-b82d2bd2>Category</div><div class="table-header-cell" style="width:120px;" data-v-b82d2bd2>Type</div><div class="table-header-cell" style="width:100px;" data-v-b82d2bd2>Products</div><div class="table-header-cell" style="width:80px;" data-v-b82d2bd2>SEO</div><div class="table-header-cell" style="width:140px;" data-v-b82d2bd2>Actions</div></div>',1)),!r.loading&&r.categories.length>0?(n(),i("div",Ie,[(n(!0),i(j,null,W(r.categories,s=>(n(),i("div",{key:s.id,class:"table-row"},[e("div",De,[e("div",Se,[s.image?(n(),i("img",{key:0,src:s.image,alt:s.name,class:"category-img"},null,8,Ae)):(n(),i("div",Te,l[0]||(l[0]=[e("i",{class:"fas fa-folder"},null,-1)])))])]),e("div",Ve,[e("div",Fe,h(s.name),1),e("div",Le,h(s.slug),1),s.description?(n(),i("div",Ee,h(D(s.description,80)),1)):A("",!0)]),e("div",ze,[s.parentId?(n(),i("span",xe,l[2]||(l[2]=[e("i",{class:"fas fa-sitemap"},null,-1),T(" Child ")]))):(n(),i("span",He,l[1]||(l[1]=[e("i",{class:"fas fa-layer-group"},null,-1),T(" Root ")])))]),e("div",Be,[e("div",Ne,[e("span",{class:N(["badge",$(s.productCount)])},[l[3]||(l[3]=e("i",{class:"fas fa-cube"},null,-1)),T(" "+h(s.productCount||0),1)],2),s.productCount>0?(n(),i("button",{key:0,class:"view-products-btn",onClick:a=>d.$emit("view-products",s),title:"View products"},l[4]||(l[4]=[e("i",{class:"fas fa-external-link-alt"},null,-1)]),8,Me)):A("",!0)])]),e("div",Re,[e("div",Oe,[s.metaTitle?(n(),i("span",Ue,l[5]||(l[5]=[e("i",{class:"fas fa-heading"},null,-1)]))):A("",!0),s.metaDescription?(n(),i("span",qe,l[6]||(l[6]=[e("i",{class:"fas fa-align-left"},null,-1)]))):A("",!0),s.metaImage?(n(),i("span",_e,l[7]||(l[7]=[e("i",{class:"fas fa-image"},null,-1)]))):A("",!0),!s.metaTitle&&!s.metaDescription&&!s.metaImage?(n(),i("span",je,l[8]||(l[8]=[e("i",{class:"fas fa-times"},null,-1)]))):A("",!0)])]),e("div",We,[e("div",Qe,[e("button",{class:"action-btn action-btn-edit",onClick:a=>d.$emit("edit",s),title:"Edit category"},l[9]||(l[9]=[e("i",{class:"fas fa-edit"},null,-1)]),8,Ge),e("button",{class:"action-btn action-btn-delete",onClick:a=>d.$emit("delete",s),disabled:!v(s),title:b(s)},l[10]||(l[10]=[e("i",{class:"fas fa-trash"},null,-1)]),8,Je)])])]))),128))])):r.loading?(n(),i("div",Ke,l[11]||(l[11]=[e("div",{class:"loading-spinner"},[e("i",{class:"fas fa-spinner fa-spin"}),e("span",null,"Loading categories...")],-1)]))):(n(),i("div",Xe,l[12]||(l[12]=[e("div",{class:"empty-state"},[e("i",{class:"fas fa-folder-open"}),e("h3",null,"No categories found"),e("p",null,"Try adjusting your search criteria or create a new category.")],-1)])))]))}},Ze=_(Ye,[["__scopeId","data-v-b82d2bd2"]]),et={class:"hierarchy-card-content"},tt={class:"hierarchy-left"},st={class:"hierarchy-image"},at=["src","alt"],lt={key:1,class:"category-img-placeholder"},ot={class:"hierarchy-info"},nt={class:"category-header"},it={class:"category-name"},rt={class:"category-badges"},dt={key:0,class:"badge badge-primary"},ct={key:1,class:"badge badge-info"},ut={key:2,class:"badge badge-success"},gt={class:"category-slug"},vt={class:"category-stats"},pt={key:0,class:"category-description"},ft={class:"hierarchy-actions"},yt=["disabled"],mt=["title","disabled"],ht={key:1,class:"children"},bt={__name:"CategoryHierarchyItem",props:{category:{type:Object,required:!0},level:{type:Number,default:0}},emits:["edit","delete","add-child","view-products"],setup(r){const E=r,D=P(!1),$=U(()=>E.category.children&&E.category.children.length>0),y=()=>{D.value=!D.value},v=s=>!s||s===0?"stat-item-light":s<5?"stat-item-warning":s<20?"stat-item-info":"stat-item-success",b=(s,a)=>s?s.length>a?s.substring(0,a)+"...":s:"",d=s=>{const a=(s.productCount||0)>0,c=$.value;return!a&&!c},l=s=>{const a=(s.productCount||0)>0,c=$.value;return a&&c?"Cannot delete: category has products and subcategories":a?"Cannot delete: category has products":c?"Cannot delete: category has subcategories":"Delete category"};return(s,a)=>{const c=ae("CategoryHierarchyItem",!0);return n(),i("div",{class:N(["hierarchy-item",{"has-children":$.value}])},[e("div",{class:"modern-hierarchy-card",style:te({marginLeft:`${r.level*2}rem`})},[e("div",et,[e("div",tt,[e("div",st,[r.category.image?(n(),i("img",{key:0,src:r.category.image,alt:r.category.name,class:"category-img"},null,8,at)):(n(),i("div",lt,a[8]||(a[8]=[e("i",{class:"fas fa-folder"},null,-1)])))]),e("div",ot,[e("div",nt,[e("h3",it,h(r.category.name),1),e("div",rt,[r.level===0?(n(),i("span",dt,a[9]||(a[9]=[e("i",{class:"fas fa-layer-group"},null,-1),T(" Root ")]))):(n(),i("span",ct,[a[10]||(a[10]=e("i",{class:"fas fa-sitemap"},null,-1)),T(" Level "+h(r.level+1),1)])),$.value?(n(),i("span",ut,[a[11]||(a[11]=e("i",{class:"fas fa-code-branch"},null,-1)),T(" "+h(r.category.children.length)+" children ",1)])):A("",!0)])]),e("div",gt,h(r.category.slug),1),e("div",vt,[e("span",{class:N(["stat-item",v(r.category.productCount)])},[a[12]||(a[12]=e("i",{class:"fas fa-cube"},null,-1)),T(" "+h(r.category.productCount||0)+" products ",1)],2)]),r.category.description?(n(),i("p",pt,h(b(r.category.description,100)),1)):A("",!0)])]),e("div",ft,[e("button",{class:"action-btn action-btn-edit",onClick:a[0]||(a[0]=m=>s.$emit("edit",r.category)),title:"Edit category"},a[13]||(a[13]=[e("i",{class:"fas fa-edit"},null,-1)])),e("button",{class:"action-btn action-btn-view",onClick:a[1]||(a[1]=m=>s.$emit("view-products",r.category)),title:"View products",disabled:!r.category.productCount},a[14]||(a[14]=[e("i",{class:"fas fa-box"},null,-1)]),8,yt),e("button",{class:"action-btn action-btn-add",onClick:a[2]||(a[2]=m=>s.$emit("add-child",r.category)),title:"Add subcategory"},a[15]||(a[15]=[e("i",{class:"fas fa-plus"},null,-1)])),e("button",{class:"action-btn action-btn-delete",onClick:a[3]||(a[3]=m=>s.$emit("delete",r.category)),title:l(r.category),disabled:!d(r.category)},a[16]||(a[16]=[e("i",{class:"fas fa-trash"},null,-1)]),8,mt)])])],4),$.value?(n(),i("div",{key:0,class:"expand-toggle",style:te({marginLeft:`${r.level*2}rem`})},[e("button",{class:"expand-btn",onClick:y},[e("i",{class:N(["fas",D.value?"fa-chevron-up":"fa-chevron-down"])},null,2),e("span",null,h(D.value?"Collapse":"Expand")+" ("+h(r.category.children.length)+")",1)])],4)):A("",!0),$.value&&D.value?(n(),i("div",ht,[(n(!0),i(j,null,W(r.category.children,m=>(n(),le(c,{key:m.id,category:m,level:r.level+1,onEdit:a[4]||(a[4]=C=>s.$emit("edit",C)),onDelete:a[5]||(a[5]=C=>s.$emit("delete",C)),onAddChild:a[6]||(a[6]=C=>s.$emit("add-child",C)),onViewProducts:a[7]||(a[7]=C=>s.$emit("view-products",C))},null,8,["category","level"]))),128))])):A("",!0)],2)}}},Ct=_(bt,[["__scopeId","data-v-14b8d7ed"]]),$t={class:"modern-hierarchy-container"},wt={key:0,class:"hierarchy-empty"},kt={key:1,class:"hierarchy-content"},Pt={__name:"CategoryHierarchy",props:{categories:{type:Array,default:()=>[]}},emits:["edit","delete","add-child","view-products"],setup(r){const E=r,D=(y,v=null)=>y.filter(b=>b.parentId===v).map(b=>({...b,children:D(y,b.id)})),$=U(()=>D(E.categories));return(y,v)=>(n(),i("div",$t,[r.categories.length===0?(n(),i("div",wt,v[4]||(v[4]=[e("div",{class:"empty-state"},[e("i",{class:"fas fa-folder-open"}),e("h3",null,"No categories found"),e("p",null,"Create your first category to get started with organizing your products.")],-1)]))):(n(),i("div",kt,[(n(!0),i(j,null,W($.value,b=>(n(),le(Ct,{key:b.id,category:b,level:0,onEdit:v[0]||(v[0]=d=>y.$emit("edit",d)),onDelete:v[1]||(v[1]=d=>y.$emit("delete",d)),onAddChild:v[2]||(v[2]=d=>y.$emit("add-child",d)),onViewProducts:v[3]||(v[3]=d=>y.$emit("view-products",d))},null,8,["category"]))),128))]))]))}},It=_(Pt,[["__scopeId","data-v-123a5b55"]]),Dt={class:"admin-page"},St={class:"admin-page-header"},At={class:"admin-page-actions"},Tt=["disabled"],Vt={class:"categories-stats-horizontal"},Ft={class:"stats-card stats-card-primary"},Lt={class:"stats-card-content"},Et={class:"stats-card-value"},zt={class:"stats-card stats-card-info"},Ht={class:"stats-card-content"},xt={class:"stats-card-value"},Bt={class:"stats-card stats-card-success"},Nt={class:"stats-card-content"},Mt={class:"stats-card-value"},Rt={class:"stats-card stats-card-warning"},Ot={class:"stats-card-content"},Ut={class:"stats-card-value"},qt={class:"categories-filters-horizontal"},_t={class:"filters-header"},jt={class:"filters-content"},Wt={class:"filter-group filter-group-search"},Qt={class:"filter-item"},Gt={class:"search-input-group"},Jt={class:"search-input-wrapper"},Kt=["placeholder"],Xt={class:"filter-item"},Yt=["value"],Zt={class:"filter-group filter-group-options"},es={class:"filter-item"},ts={class:"filter-item"},ss={class:"filter-item"},as={key:0,class:"has-text-centered py-6"},ls={key:1,class:"notification is-danger"},os={key:2},ns={key:0},is={key:1},rs={key:0,class:"pagination-wrapper"},ds={__name:"Categories",setup(r){const E=(o,t)=>{let u;return function(...p){const q=()=>{clearTimeout(u),o(...p)};clearTimeout(u),u=setTimeout(q,t)}},D=$e(),$=P(!1),y=P(""),v=P([]);P(null);const b=P(null),d=P(!1),l=P(!1),s=P(""),a=P("all"),c=P({parentId:"",productCount:""}),m=P("name"),C=P("asc"),I=P({currentPage:1,totalPages:1,totalItems:0,perPage:20}),F=P({total:0,rootCategories:0,withProducts:0,totalProducts:0}),oe=U(()=>z.value.filter(o=>!o.parentId)),ne=U(()=>s.value||c.value.parentId||c.value.productCount||m.value!=="name"||C.value!=="asc"),ie=U(()=>oe.value),J=E(()=>{I.value.currentPage=1,L()},300),z=P([]),K=async()=>{try{const o=await B.getAll({pageSize:1e4});z.value=o.data||[],console.log("📊 Fetched all categories:",z.value.length),console.log("📊 Category hierarchy structure:",z.value.map(t=>({id:t.id,name:t.name,parentId:t.parentId})))}catch(o){console.error("Error fetching all categories:",o)}},L=async()=>{$.value=!0,y.value="";try{await K();const o={page:I.value.currentPage,pageSize:I.value.perPage,orderBy:m.value==="productCount"?"name":m.value,descending:C.value==="desc"};s.value&&a.value==="all"&&(o.filter=s.value),c.value.parentId==="root"?o.parentId=null:c.value.parentId;const t=await B.getAll(o);let u=t.data||[];if(s.value&&a.value!=="all")try{u=(await B.getAll({pageSize:1e3,orderBy:m.value==="productCount"?"name":m.value,descending:C.value==="desc"})).data||[]}catch(g){console.warn("Failed to get all categories for search, using current page:",g),u=t.data||[]}if(c.value.parentId&&c.value.parentId!=="root"){const g=c.value.parentId,w=new Set;console.log("🔍 Filtering by root category:",g),console.log("📊 All categories available:",z.value.length),console.log("📊 Categories before filter:",u.length);const k=f=>{const V=z.value.filter(S=>S.parentId===f);console.log(`🔍 Found ${V.length} children for parent ${f}`),V.forEach(S=>{w.has(S.id)||(w.add(S.id),console.log(`➕ Added descendant: ${S.name} (${S.id})`),k(S.id))})};w.add(g),console.log(`➕ Added root category: ${g}`),k(g),console.log("📊 Total descendant IDs:",w.size),console.log("📊 Descendant IDs:",Array.from(w)),u=u.filter(f=>w.has(f.id)),console.log("📊 Categories after filter:",u.length),console.log("📊 Filtered categories:",u.map(f=>`${f.name} (${f.id})`))}if(c.value.productCount&&(u=u.filter(g=>{switch(c.value.productCount){case"empty":return(g.productCount||0)===0;case"hasProducts":return(g.productCount||0)>0;case"many":return(g.productCount||0)>10;default:return!0}})),s.value&&a.value!=="all"){const g=s.value.toLowerCase();u=u.filter(w=>{var k,f,V;switch(a.value){case"name":return(k=w.name)==null?void 0:k.toLowerCase().includes(g);case"slug":return(f=w.slug)==null?void 0:f.toLowerCase().includes(g);case"description":return(V=w.description)==null?void 0:V.toLowerCase().includes(g);default:return!0}})}if(s.value&&a.value==="all"&&u.length===0)try{const w=(await B.getAll({pageSize:1e3,orderBy:m.value==="productCount"?"name":m.value,descending:C.value==="desc"})).data||[],k=s.value.toLowerCase();u=w.filter(f=>{var V,S,M;return((V=f.name)==null?void 0:V.toLowerCase().includes(k))||((S=f.slug)==null?void 0:S.toLowerCase().includes(k))||((M=f.description)==null?void 0:M.toLowerCase().includes(k))})}catch(g){console.warn("Fallback search also failed:",g)}const H=c.value.productCount||s.value&&a.value!=="all"||c.value.parentId&&c.value.parentId!=="root"||s.value;(H||m.value==="productCount")&&u.length>0&&u.sort((g,w)=>{var V,S,M,ee;let k,f;switch(m.value){case"name":k=((V=g.name)==null?void 0:V.toLowerCase())||"",f=((S=w.name)==null?void 0:S.toLowerCase())||"";break;case"productCount":k=g.productCount||0,f=w.productCount||0;break;case"createdAt":k=new Date(g.createdAt||0),f=new Date(w.createdAt||0);break;default:k=((M=g.name)==null?void 0:M.toLowerCase())||"",f=((ee=w.name)==null?void 0:ee.toLowerCase())||""}return k<f?C.value==="asc"?-1:1:k>f?C.value==="asc"?1:-1:0}),v.value=u;const q=t.total||0,ye=u.length;I.value={currentPage:t.currentPage||1,totalPages:H?1:t.totalPages||Math.ceil(q/(t.pageSize||20)),totalItems:H?ye:q,perPage:t.pageSize||20},await re()}catch(o){console.error("Error fetching categories:",o),y.value="Failed to load categories. Please try again."}finally{$.value=!1}},re=async()=>{try{const o=await B.getStats();F.value={total:o.totalCategories||0,rootCategories:o.rootCategories||0,withProducts:o.categoriesWithProducts||0,totalProducts:o.totalProducts||0}}catch(o){console.warn("Failed to fetch stats from backend, using local calculation:",o),F.value.total=I.value.totalItems,F.value.rootCategories=v.value.filter(t=>!t.parentId).length,F.value.withProducts=v.value.filter(t=>t.productCount>0).length,F.value.totalProducts=v.value.reduce((t,u)=>t+(u.productCount||0),0)}},de=()=>{const o={all:"Search by name and slug (backend + frontend)...",name:"Search by category name (frontend only)...",slug:"Search by category slug (frontend only)...",description:"Search by description (frontend only)..."};return o[a.value]||o.all},ce=()=>{s.value="",a.value="all",I.value.currentPage=1,L()},ue=()=>{s.value="",a.value="all",c.value.parentId="",c.value.productCount="",m.value="name",C.value="asc",I.value.currentPage=1,L()},ge=async()=>{l.value=!l.value},ve=o=>{o>=1&&o<=I.value.totalPages&&(I.value.currentPage=o,L())},X=o=>{D.push(`/admin/categories/${o.id}/edit`)},pe=o=>{D.push({path:"/admin/categories/create",query:{parentId:o.id}})},Y=o=>{b.value=o,d.value=!0},Q=()=>{d.value=!1,b.value=null},fe=async()=>{if(b.value)try{console.log("Deleting category:",b.value),await B.delete(b.value.id),console.log("Category deleted successfully"),Q(),await L(),await K(),console.log("Categories refreshed after delete")}catch(o){console.error("Error deleting category:",o),Q(),o.response&&o.response.data&&o.response.data.message?y.value=o.response.data.message:y.value=`Failed to delete category: ${o.message||"Please try again."}`}},Z=o=>{D.push(`/admin/categories/${o.id}`)};return G([m,C],()=>{I.value.currentPage=1,L()}),G(()=>c.value.parentId,()=>{I.value.currentPage=1,L()}),G(()=>c.value.productCount,()=>{I.value.currentPage=1,L()}),he(()=>{L()}),(o,t)=>{var H;const u=ae("router-link");return n(),i("div",Dt,[e("div",St,[t[9]||(t[9]=e("div",{class:"admin-page-title-section"},[e("h1",{class:"admin-page-title"},[e("i",{class:"fas fa-tags admin-page-icon"}),T(" Categories Management ")]),e("p",{class:"admin-page-subtitle"},"Manage category hierarchy and product organization")],-1)),e("div",At,[R(u,{to:"/admin/categories/create",class:N(["admin-btn admin-btn-primary",{"admin-btn-disabled":$.value}])},{default:be(()=>t[8]||(t[8]=[e("i",{class:"fas fa-plus"},null,-1),T(" Add Category ")])),_:1},8,["class"]),e("button",{class:"admin-btn admin-btn-info",onClick:ge,disabled:$.value},[e("i",{class:N(l.value?"fas fa-table":"fas fa-sitemap")},null,2),T(" "+h(l.value?"Table View":"Hierarchy View"),1)],8,Tt)])]),e("div",Vt,[e("div",Ft,[t[11]||(t[11]=e("div",{class:"stats-card-icon"},[e("i",{class:"fas fa-folder"})],-1)),e("div",Lt,[e("div",Et,h(F.value.total),1),t[10]||(t[10]=e("div",{class:"stats-card-label"},"Total Categories",-1))])]),e("div",zt,[t[13]||(t[13]=e("div",{class:"stats-card-icon"},[e("i",{class:"fas fa-sitemap"})],-1)),e("div",Ht,[e("div",xt,h(F.value.rootCategories),1),t[12]||(t[12]=e("div",{class:"stats-card-label"},"Root Categories",-1))])]),e("div",Bt,[t[15]||(t[15]=e("div",{class:"stats-card-icon"},[e("i",{class:"fas fa-box"})],-1)),e("div",Nt,[e("div",Mt,h(F.value.withProducts),1),t[14]||(t[14]=e("div",{class:"stats-card-label"},"With Products",-1))])]),e("div",Rt,[t[17]||(t[17]=e("div",{class:"stats-card-icon"},[e("i",{class:"fas fa-shopping-bag"})],-1)),e("div",Ot,[e("div",Ut,h(F.value.totalProducts),1),t[16]||(t[16]=e("div",{class:"stats-card-label"},"Total Products",-1))])])]),e("div",qt,[e("div",_t,[t[19]||(t[19]=e("h3",{class:"filters-title"},[e("i",{class:"fas fa-search"}),T(" Search & Filters ")],-1)),ne.value?(n(),i("button",{key:0,class:"filters-clear-btn",onClick:ue,title:"Clear all filters"},t[18]||(t[18]=[e("i",{class:"fas fa-times"},null,-1),T(" Clear Filters ")]))):A("",!0)]),e("div",jt,[e("div",Wt,[e("div",Qt,[t[23]||(t[23]=e("label",{class:"filter-label"},"Search Categories",-1)),e("div",Gt,[x(e("select",{"onUpdate:modelValue":t[0]||(t[0]=p=>a.value=p),class:"search-field-select"},t[20]||(t[20]=[e("option",{value:"all"},"Name & Slug",-1),e("option",{value:"name"},"Name Only",-1),e("option",{value:"slug"},"Slug Only",-1),e("option",{value:"description"},"Description Only",-1)]),512),[[O,a.value]]),e("div",Jt,[x(e("input",{class:"search-input",type:"text",placeholder:de(),"onUpdate:modelValue":t[1]||(t[1]=p=>s.value=p),onInput:t[2]||(t[2]=(...p)=>se(J)&&se(J)(...p))},null,40,Kt),[[Ce,s.value]]),t[21]||(t[21]=e("div",{class:"search-input-icon"},[e("i",{class:"fas fa-search"})],-1))]),s.value?(n(),i("button",{key:0,class:"search-clear-btn",onClick:ce,title:"Clear search"},t[22]||(t[22]=[e("i",{class:"fas fa-times"},null,-1)]))):A("",!0)])]),e("div",Xt,[t[26]||(t[26]=e("label",{class:"filter-label"},"Parent Category",-1)),x(e("select",{"onUpdate:modelValue":t[3]||(t[3]=p=>c.value.parentId=p),class:"filter-select"},[t[24]||(t[24]=e("option",{value:""},"All Categories",-1)),t[25]||(t[25]=e("option",{value:"root"},"Root Categories Only",-1)),(n(!0),i(j,null,W(ie.value,p=>(n(),i("option",{key:p.id,value:p.id},h(p.name),9,Yt))),128))],512),[[O,c.value.parentId]])])]),e("div",Zt,[e("div",es,[t[28]||(t[28]=e("label",{class:"filter-label"},"Product Count",-1)),x(e("select",{"onUpdate:modelValue":t[4]||(t[4]=p=>c.value.productCount=p),class:"filter-select"},t[27]||(t[27]=[e("option",{value:""},"Any",-1),e("option",{value:"empty"},"Empty (0 products)",-1),e("option",{value:"hasProducts"},"Has Products (>0)",-1),e("option",{value:"many"},"Many Products (>10)",-1)]),512),[[O,c.value.productCount]])]),e("div",ts,[t[30]||(t[30]=e("label",{class:"filter-label"},"Sort By",-1)),x(e("select",{"onUpdate:modelValue":t[5]||(t[5]=p=>m.value=p),class:"filter-select"},t[29]||(t[29]=[e("option",{value:"name"},"Name",-1),e("option",{value:"productCount"},"Product Count",-1)]),512),[[O,m.value]])]),e("div",ss,[t[32]||(t[32]=e("label",{class:"filter-label"},"Order",-1)),x(e("select",{"onUpdate:modelValue":t[6]||(t[6]=p=>C.value=p),class:"filter-select"},t[31]||(t[31]=[e("option",{value:"asc"},"Ascending",-1),e("option",{value:"desc"},"Descending",-1)]),512),[[O,C.value]])])])])]),$.value?(n(),i("div",as,t[33]||(t[33]=[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"}),e("p",{class:"mt-3"},"Loading categories...")],-1)]))):y.value?(n(),i("div",ls,[e("button",{class:"delete",onClick:t[7]||(t[7]=p=>y.value="")}),T(" "+h(y.value),1)])):(n(),i("div",os,[l.value?(n(),i("div",ns,[R(It,{categories:z.value,onEdit:X,onDelete:Y,onAddChild:pe,onViewProducts:Z},null,8,["categories"])])):(n(),i("div",is,[R(Ze,{categories:v.value,"all-categories":z.value,loading:$.value,onEdit:X,onDelete:Y,onViewProducts:Z},null,8,["categories","all-categories","loading"]),I.value.totalPages>1?(n(),i("div",rs,[R(ke,{"current-page":I.value.currentPage,"total-pages":I.value.totalPages,onPageChanged:ve},null,8,["current-page","total-pages"])])):A("",!0)]))])),R(we,{"is-open":d.value,title:"Delete Category",message:`Are you sure you want to delete '${(H=b.value)==null?void 0:H.name}'? This action cannot be undone.`,"confirm-text":"Delete","confirm-button-class":"is-danger",onConfirm:fe,onCancel:Q},null,8,["is-open","message"])])}}},vs=_(ds,[["__scopeId","data-v-1a3261b3"]]);export{vs as default};
