using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.MetaImage;

/// <summary>
/// Обробник універсальної команди оновлення мета-зображення
/// </summary>
public class UpdateUniversalMetaImageCommandHandler : IRequestHandler<UpdateUniversalMetaImageCommand, FileUploadResult>
{
    private readonly IImageService _imageService;
    private readonly ILogger<UpdateUniversalMetaImageCommandHandler> _logger;

    public UpdateUniversalMetaImageCommandHandler(
        IImageService imageService,
        ILogger<UpdateUniversalMetaImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UpdateUniversalMetaImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Updating meta image for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.UpdateImageAsync(
                request.EntityType,
                request.EntityId,
                request.File,
                "meta",
                null,
                cancellationToken);

            _logger.LogInformation($"Successfully updated meta image for {request.EntityType} {request.EntityId}");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to update meta image for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
