<template>
  <div class="product-grid-container">
    <!-- Loading State -->
    <div v-if="displayLoading" class="loading-container">
      <div class="loader"></div>
      <p>Завантаження товарів...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="displayError" class="error-container">
      <h3>Помилка завантаження</h3>
      <p>{{ displayError }}</p>
      <button v-if="autoFetch" @click="fetchProducts" class="retry-btn">Спробувати знову</button>
    </div>

    <!-- Empty State -->
    <div v-else-if="!displayProducts || displayProducts.length === 0" class="empty-container">
      <h3>Товари не знайдено</h3>
      <p>{{ emptyMessage || 'На жаль, товарів у цій категорії поки немає' }}</p>
    </div>

    <!-- Products Grid -->
    <div v-if="displayProducts.length > 0 || showAddCard" class="products-grid" :class="gridClass">
      <!-- Add Product Card (for seller mode) -->
      <div
        v-if="showAddCard"
        class="product-card add-product-card"
        @click="$emit('add-product')"
      >
        <div class="add-product-content">
          <div class="add-product-icon">+</div>
          <div class="add-product-text">Додати товар</div>
        </div>
      </div>

      <!-- Product Cards -->
      <div
        v-for="product in displayProducts"
        :key="product.id"
        class="product-card"
        :class="{ 'seller-product-card': sellerMode }"
        @click="handleProductClick(product)"
      >
        <div class="product-badge" v-if="product.badge">{{ product.badge }}</div>
        
        <div class="product-image">
          <img 
            :src="product.image || product.mainImage || placeholderImage" 
            :alt="product.name || product.productName || 'Product Image'"
            @error="handleImageError"
          />
        </div>
        
        <div class="product-info">
          <h3 class="product-name">{{ product.name || product.productName }}</h3>

          <!-- Seller Mode: Product Status -->
          <div v-if="sellerMode && product.status" class="product-status" :class="'status-' + (product.status || 'pending').toLowerCase()">
            {{ getProductStatusText(product.status) }}
          </div>

          <!-- Regular Mode: Availability -->
          <div v-else-if="!sellerMode && (product.productStock > 0 || product.stock > 0 || product.inStock)" class="product-availability">
            <span class="availability-icon">✓</span>
            <span class="availability-text">В наявності</span>
          </div>

          <div v-else-if="!sellerMode" class="product-unavailability">
            <span class="availability-icon">✖</span>
            <span class="availability-text">Немає в наявності</span>
          </div>
          
          <div class="product-price">
            <span class="price">{{ formatPrice(product) }}</span>
          </div>
          
          <!-- Regular Mode: Cart/Wishlist Actions -->
          <div v-if="showActions && !sellerMode" class="product-actions">
            <button
              @click.stop="addToCart(product.productId || product.id)"
              :disabled="!(product.productStock > 0 || product.stock > 0 || product.inStock)"
              class="add-to-cart-btn"
            >
              <i class="fas fa-shopping-cart"></i>
            </button>

            <button
              v-if="product.productId"
              @click.stop="removeFromWishlist(product.id)"
              class="add-to-wishlist-btn"
            >
            <span class="availability-icon">✖</span>
            </button>
            <button
              v-else
              @click.stop="addToWishlist(product.id)"
              class="add-to-wishlist-btn"
            >
            <i class="fas fa-heart"></i>
            </button>
          </div>

          <!-- Seller Mode: Edit Button -->
          <button
            v-if="sellerMode"
            @click.stop="$emit('edit-product', product)"
            class="seller-edit-btn"
            title="Редагувати товар"
          >
            <i class="fas fa-edit"></i>
            Редагувати
          </button>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="showPagination && totalPages > 1" class="pagination-container">
      <Pagination
        :currentPage="displayCurrentPage"
        :total-items="displayTotalItems"
        :items-per-page="displayPageSize"
        :max-visible-pages="10"
        @page-changed="onPageChange"
      />
    </div>
  </div>
</template>

<script>
import cartService from '@/services/cart.service';
import wishlistService from '@/services/wishlist.service';
import Pagination from '@/components/catalog/Pagination.vue';
import { useToast } from '@/composables/useToast';
import CategoryService from '@/services/category.service';
import ProductService from '@/services/product.service';

export default {
  name: 'ProductGrid',
  components: {
    Pagination
  },
  setup() {
    const { showToast } = useToast();
    return { showToast };
  },
  props: {
    // Fetch parameters (for auto-fetch mode)
    fetchParams: {
      type: Object,
      default: () => ({})
    },
    // External data (for manual mode)
    products: {
      type: Array,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: null
    },
    // Pagination props (for manual mode)
    currentPage: {
      type: Number,
      default: 1
    },
    totalItems: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 12
    },
    // Display options
    emptyMessage: {
      type: String,
      default: 'Товари не знайдено'
    },
    showActions: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: false
    },
    gridColumns: {
      type: Number,
      default: 4
    },
    // Auto-fetch on mount
    autoFetch: {
      type: Boolean,
      default: true
    },
    // Seller mode
    sellerMode: {
      type: Boolean,
      default: false
    },
    // Show add product card
    showAddCard: {
      type: Boolean,
      default: false
    },
    // Custom click handler for seller mode
    onProductClick: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      placeholderImage: '/placeholder-product.svg',
      internalProducts: [],
      internalLoading: false,
      internalError: null,
      internalCurrentPage: 1,
      internalTotalItems: 0,
      internalPageSize: 12
    }
  },
  computed: {
    // Use external data if provided, otherwise use internal data
    displayProducts() {
      return this.products !== null ? this.products : this.internalProducts;
    },
    displayLoading() {
      return this.products !== null ? this.loading : this.internalLoading;
    },
    displayError() {
      return this.products !== null ? this.error : this.internalError;
    },
    displayCurrentPage() {
      return this.products !== null ? this.currentPage : this.internalCurrentPage;
    },
    displayTotalItems() {
      return this.products !== null ? this.totalItems : this.internalTotalItems;
    },
    displayPageSize() {
      return this.products !== null ? this.pageSize : this.internalPageSize;
    },
    totalPages() {
      return Math.ceil(this.displayTotalItems / this.displayPageSize);
    },
    gridClass() {
      return `grid-cols-${this.gridColumns}`;
    }
  },

  async mounted() {
    // Initialize pageSize from fetchParams if provided
    if (this.fetchParams.pageSize) {
      this.internalPageSize = this.fetchParams.pageSize;
    }

    if (this.autoFetch) {
      await this.fetchProducts();
    }
  },

  watch: {
    fetchParams: {
      handler(newParams) {
        // Update pageSize if it changed
        if (newParams.pageSize && newParams.pageSize !== this.internalPageSize) {
          this.internalPageSize = newParams.pageSize;
          this.internalCurrentPage = 1; // Reset to first page when pageSize changes
        }

        if (this.autoFetch) {
          this.fetchProducts();
        }
      },
      deep: true
    }
  },

  methods: {
    // Handle product click based on mode
    handleProductClick(product) {
      if (this.onProductClick) {
        // Use custom click handler if provided
        this.onProductClick(product);
      } else if (this.sellerMode) {
        // In seller mode, only open approved products
        if (product.status !== "Approved") return;
        this.goToProduct(product.slug || product.productSlug || product.id);
      } else {
        // Regular mode
        this.goToProduct(product.slug || product.productSlug || product.id);
      }
    },

    // Get product status text for seller mode
    getProductStatusText(status) {
      const statusTexts = {
        'Pending': 'На розгляді',
        'Approved': 'Схвалено',
        'Rejected': 'Відхилено',
        'Draft': 'Чернетка'
      };
      return statusTexts[status] || status || 'На розгляді';
    },

    async addToCart(productId) {
      try {
        await cartService.addToCart(productId);
        this.showToast('Товар додано до корзини', 'success');
        this.$emit('product-added-to-cart', productId);
      } catch (error) {
        console.error('Error adding to cart:', error);
        this.showToast('Помилка при додаванні до корзини', 'error');
      }
    },

    async addToWishlist(productId) {
      try {
        await wishlistService.addToWishlist(productId);
        this.showToast('Товар додано до обраних', 'success');
        this.$emit('product-added-to-wishlist', productId);
      } catch (error) {
        console.error('Error adding to wishlist:', error);
        this.showToast('Помилка при додаванні до обраних', 'error');
      }
    },

    async removeFromWishlist(productId) {
      try {
        await wishlistService.removeFromWishlist(productId);
        this.showToast('Товар видалено з обраних', 'success');
        this.$emit('product-removed-from-wishlist', productId);
      } catch (error) {
        console.error('Error removing from wishlist:', error);
        this.showToast('Помилка при видаленні з обраних', 'error');
      }
    },
    
    goToProduct(slug) {
      this.$router.push(`/product/${slug}`);
    },
    
    handleImageError(event) {
      event.target.src = this.placeholderImage;
    },
    
    async fetchProducts() {
      this.internalLoading = true;
      this.internalError = null;

      try {
        // Use pageSize from fetchParams if provided, otherwise use component's pageSize
        const effectivePageSize = this.fetchParams.pageSize || this.internalPageSize;

        const params = {
          pageSize: effectivePageSize,
          page: this.internalCurrentPage,
          ...this.fetchParams
        };

        let response;

        // Determine which service to use based on parameters
        if (params.categorySlug && params.random) {
          // Random products from category
          response = await CategoryService.getRandomProducts(params.categorySlug, params);
        } else if (params.categorySlug) {
          response = await CategoryService.getProducts(params.categorySlug, params);
        } else if (params.type === 'top' && params.categorySlug) {
          response = await ProductService.getTopProductsByCategory(params);
        } else if (params.type === 'top') {
          response = await ProductService.getTopProducts(params);
        } else if (params.type === 'recommended') {
          response = await ProductService.getRecommendedProducts(params);
        } else {
          response = await ProductService.getAll(params);
        }

        // Handle different response structures
        if (params.categorySlug && params.random) {
          // Random products return a simple array
          this.internalProducts = response.data || [];
          this.internalTotalItems = this.internalProducts.length;
        } else {
          // Regular paginated response
          this.internalProducts = response.data.data || response.data || [];
          this.internalTotalItems = response.data.total || response.total || this.internalProducts.length;
        }

        console.log('ProductGrid fetch result:', {
          products: this.internalProducts.length,
          totalItems: this.internalTotalItems,
          currentPage: this.internalCurrentPage,
          pageSize: effectivePageSize,
          totalPages: Math.ceil(this.internalTotalItems / effectivePageSize),
          response: response.data
        });

      } catch (error) {
        console.error('Error fetching products:', error);
        this.internalError = 'Помилка завантаження товарів. Спробуйте ще раз.';
        this.internalProducts = [];
        this.internalTotalItems = 0;
      } finally {
        this.internalLoading = false;
      }
    },

    onPageChange(page) {
      console.log('Page change requested:', page);

      if (this.products !== null) {
        // External mode - emit event for parent to handle
        this.$emit('page-changed', page);
      } else {
        // Internal mode - handle pagination internally
        this.internalCurrentPage = page;
        this.fetchProducts();
      }
    },



    formatPrice(product) {
      if (!product.priceAmount && !product.price) return 'Ціна не вказана';

      const price = product.priceAmount || product.price;
      if (price == 0) return 'Ціна не вказана';

      const roundedPrice = Math.round(price);
      const currency = product.priceCurrency || product.currency;

      if (!currency || currency === "UAH") return `${roundedPrice} ₴`;
      else if (currency === "USD") return `${roundedPrice} $`;
      else if (currency === "EUR") return `${roundedPrice} €`;

      return `${roundedPrice} ${currency}`;
    }
  }
}
</script>

<style scoped>
.product-grid-container {
  width: 100%;
}

/* Loading, Error, Empty States */
.loading-container, .error-container, .empty-container {
  text-align: center;
  padding: 60px 20px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #0056b3;
}

/* Products Grid */
.products-grid {
  display: grid;
  gap: 16px;
  margin-bottom: 20px;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Product Card */
.product-card {
  position: relative;
  background: white;
  border: solid #ABAAAA 2px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ff7a00;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
}

.product-image {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  overflow: hidden;
  border-radius: 4px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-availability, .product-unavailability {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.product-availability {
  color: #28a745;
}

.product-unavailability {
  color: #dc3545;
}

.availability-icon {
  font-weight: bold;
}

.product-price {
  margin-top: auto;
}

.price {
  font-size: 18px;
  font-weight: 700;
  color: #333;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}

.add-to-cart-btn, .add-to-wishlist-btn {
  flex: 40px 0 0;
  padding: 8px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.add-to-cart-btn {
  background-color: #007bff;
  color: white;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.add-to-cart-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.add-to-wishlist-btn {
  background-color: #f8f9fa;
  color: #dc3545;
  border: 1px solid #dc3545;
}

.add-to-wishlist-btn:hover {
  background-color: #dc3545;
  color: white;
}

.pagination-container {
  margin-top: 40px;
  display: flex;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .grid-cols-4 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-5 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-6 { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 768px) {
  .grid-cols-3, .grid-cols-4, .grid-cols-5, .grid-cols-6 { 
    grid-template-columns: repeat(2, 1fr); 
  }
  
  .product-image {
    height: 120px;
  }
  
  .product-name {
    font-size: 14px;
  }
  
  .price {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .product-actions {
    flex-direction: column;
  }
}

/* ===== SELLER MODE STYLES ===== */

/* Add Product Card */
.add-product-card {
  border: 2px dashed #bbb !important;
  background: #fafafa !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.add-product-card:hover {
  border-color: #007bff !important;
  background: #f0f8ff !important;
  transform: translateY(-2px) !important;
}

.add-product-content {
  text-align: center;
  color: #666;
}

.add-product-icon {
  font-size: 48px;
  margin-bottom: 8px;
  transition: color 0.2s;
}

.add-product-card:hover .add-product-icon {
  color: #007bff;
}

.add-product-text {
  font-size: 16px;
  font-weight: 500;
  transition: color 0.2s;
}

.add-product-card:hover .add-product-text {
  color: #007bff;
}

/* Product Status Badge */
.product-status {
  display: inline-block;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.status-pending {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-approved {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-rejected {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-draft {
  background: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

/* Seller Edit Button */
.seller-edit-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  gap: 4px;
}

.seller-edit-btn:hover,
.seller-edit-btn:focus {
  background: rgba(0, 95, 204, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  outline: none;
}

/* Seller Product Card Adjustments */
.seller-product-card {
  position: relative;
}

.seller-product-card .product-price {
  margin-top: auto;
}


</style>
