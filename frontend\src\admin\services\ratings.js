import api from '@/services/api';

const ratingsService = {
  async getRatings(params = {}) {
    try {
      // Очищаємо пусті параметри
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== null && value !== undefined && value !== '')
      );

      const response = await api.get('/api/admin/ratings', { params: cleanParams });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching ratings:', error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.errors?.[0] ||
                          'Failed to load ratings';
      throw new Error(errorMessage);
    }
  },

  async getRatingsWithFilters(filters = {}) {
    try {
      const params = {
        filter: filters.search,
        orderBy: filters.sortBy || 'CreatedAt',
        descending: filters.sortOrder === 'desc',
        page: filters.page,
        pageSize: filters.pageSize,
        minRating: filters.minRating,
        maxRating: filters.maxRating,
        minService: filters.minService,
        maxService: filters.maxService,
        minDeliveryTime: filters.minDeliveryTime,
        maxDeliveryTime: filters.maxDeliveryTime,
        minAccuracy: filters.minAccuracy,
        maxAccuracy: filters.maxAccuracy,
        productId: filters.productId,
        productSlug: filters.productSlug,
        userId: filters.userId,
        userEmail: filters.userEmail
      };

      return await ratingsService.getRatings(params);
    } catch (error) {
      console.error('Error fetching ratings with filters:', error);
      throw error;
    }
  },

  async getRatingById(id) {
    try {
      const response = await api.get(`/api/admin/ratings/${id}`);
      return response.data.data;
    } catch (error) {
      throw ratingsService.handleApiError(error, 'Failed to load rating details');
    }
  },

  async updateRating(id, data) {
    try {
      const response = await api.put(`/api/admin/ratings/${id}`, data);
      return response.data;
    } catch (error) {
      throw ratingsService.handleApiError(error, 'Failed to update rating');
    }
  },

  async deleteRating(id) {
    try {
      const response = await api.delete(`/api/admin/ratings/${id}`);
      return response.data;
    } catch (error) {
      throw ratingsService.handleApiError(error, 'Failed to delete rating');
    }
  },

  async bulkDeleteRatings(ids) {
    try {
      const response = await api.post('/api/admin/ratings/bulk-delete', { ids });
      return response.data;
    } catch (error) {
      throw ratingsService.handleApiError(error, 'Failed to delete ratings');
    }
  },

  async getRatingStats() {
    try {
      const response = await api.get('/api/admin/ratings/stats');
      return response.data.data;
    } catch (error) {
      throw ratingsService.handleApiError(error, 'Failed to load rating statistics');
    }
  },

  async getRatingStatistics(productId = null, productSlug = null, userId = null) {
    try {
      const params = {};
      if (productId) params.productId = productId;
      if (productSlug) params.productSlug = productSlug;
      if (userId) params.userId = userId;

      const response = await api.get('/api/admin/ratings/statistics', { params });
      return response.data.data;
    } catch (error) {
      throw ratingsService.handleApiError(error, 'Failed to load rating statistics');
    }
  },

  // Утилітарні методи для роботи з фільтрами
  buildFilterParams(filters) {
    const params = {};

    if (filters.search) params.filter = filters.search;
    if (filters.sortBy) params.orderBy = filters.sortBy;
    if (filters.sortOrder) params.descending = filters.sortOrder === 'desc';
    if (filters.page) params.page = filters.page;
    if (filters.pageSize) params.pageSize = filters.pageSize;

    // Rating filters
    if (filters.minRating !== null && filters.minRating !== undefined) params.minRating = filters.minRating;
    if (filters.maxRating !== null && filters.maxRating !== undefined) params.maxRating = filters.maxRating;

    // Individual rating filters
    if (filters.minService !== null && filters.minService !== undefined) params.minService = filters.minService;
    if (filters.maxService !== null && filters.maxService !== undefined) params.maxService = filters.maxService;
    if (filters.minDeliveryTime !== null && filters.minDeliveryTime !== undefined) params.minDeliveryTime = filters.minDeliveryTime;
    if (filters.maxDeliveryTime !== null && filters.maxDeliveryTime !== undefined) params.maxDeliveryTime = filters.maxDeliveryTime;
    if (filters.minAccuracy !== null && filters.minAccuracy !== undefined) params.minAccuracy = filters.minAccuracy;
    if (filters.maxAccuracy !== null && filters.maxAccuracy !== undefined) params.maxAccuracy = filters.maxAccuracy;

    // Entity filters
    if (filters.productId) params.productId = filters.productId;
    if (filters.productSlug) params.productSlug = filters.productSlug;
    if (filters.userId) params.userId = filters.userId;
    if (filters.userEmail) params.userEmail = filters.userEmail;

    return params;
  },

  // Покращена обробка помилок
  handleApiError(error, defaultMessage = 'Operation failed') {
    console.error('API Error:', error);

    if (error.response) {
      // Сервер відповів з кодом помилки
      const message = error.response.data?.message ||
                     error.response.data?.errors?.[0] ||
                     `Server error: ${error.response.status}`;
      return new Error(message);
    } else if (error.request) {
      // Запит був відправлений, але відповіді не було
      return new Error('Network error: No response from server');
    } else {
      // Щось інше пішло не так
      return new Error(error.message || defaultMessage);
    }
  },

  // Утилітарні методи для роботи з рейтингами
  calculateAverageRating(service, deliveryTime, accuracy) {
    return (service + deliveryTime + accuracy) / 3.0;
  },

  formatRating(rating) {
    return rating ? rating.toFixed(1) : '0.0';
  },

  getRatingColor(rating) {
    if (rating >= 4.5) return 'success';
    if (rating >= 3.5) return 'warning';
    if (rating >= 2.5) return 'info';
    return 'danger';
  },

  getRatingStars(rating) {
    return Math.round(rating || 0);
  }
};

export default ratingsService;
