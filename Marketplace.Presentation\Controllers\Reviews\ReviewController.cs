using Marketplace.Application.Queries.Review;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Reviews;

[ApiController]
[Route("api/reviews")]
public class ReviewController : BasicApiController
{
    private readonly IMediator _mediator;

    public ReviewController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Отримати останні відгуки (публічний доступ)
    /// </summary>
    [HttpGet("recent")]
    public async Task<IActionResult> GetRecentReviews(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetRecentReviewsQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ReviewResponse>>.SuccessWithData(response));
    }

    /// <summary>
    /// Отримати відгуки для конкретного товару (публічний доступ)
    /// </summary>
    [HttpGet("products/{productSlug}")]
    public async Task<IActionResult> GetProductReviews(
        [FromRoute] string productSlug,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetProductReviewsQuery(
            productSlug,
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ReviewResponse>>.SuccessWithData(response));
    }

    /// <summary>
    /// Отримати відгуки для товарів категорії (публічний доступ)
    /// </summary>
    [HttpGet("categories/{categorySlug}")]
    public async Task<IActionResult> GetCategoryReviews(
        [FromRoute] string categorySlug,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetCategoryReviewsQuery(
            categorySlug,
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ReviewResponse>>.SuccessWithData(response));
    }

    /// <summary>
    /// Отримати конкретний відгук за ID (публічний доступ)
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetReview([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetReviewQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null
            ? Ok(ApiResponse<ReviewResponse>.SuccessWithData(response))
            : NotFound(ApiResponse.Failure("Відгук не знайдено."));
    }
}

/// <summary>
/// Додатковий контролер для альтернативних маршрутів відгуків
/// </summary>
[ApiController]
public class AlternativeReviewController : BasicApiController
{
    private readonly IMediator _mediator;

    public AlternativeReviewController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Альтернативний маршрут для відгуків товару
    /// </summary>
    [HttpGet("api/products/{productSlug}/reviews")]
    public async Task<IActionResult> GetProductReviews(
        [FromRoute] string productSlug,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetProductReviewsQuery(
            productSlug,
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ReviewResponse>>.SuccessWithData(response));
    }

    /// <summary>
    /// Альтернативний маршрут для відгуків категорії
    /// </summary>
    [HttpGet("api/categories/{categorySlug}/reviews")]
    public async Task<IActionResult> GetCategoryReviews(
        [FromRoute] string categorySlug,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetCategoryReviewsQuery(
            categorySlug,
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ReviewResponse>>.SuccessWithData(response));
    }
}
