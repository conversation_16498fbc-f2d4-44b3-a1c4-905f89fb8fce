﻿﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.Product;

public class CreateSellerProductCommandHandler : IRequestHandler<CreateSellerProductCommand, Guid>
{
    private readonly IProductRepository _productRepository;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly ICategoryRepository _categoryRepository;

    public CreateSellerProductCommandHandler(
        IProductRepository productRepository,
        ICompanyUserRepository companyUserRepository,
        ICategoryRepository categoryRepository)
    {
        _productRepository = productRepository;
        _companyUserRepository = companyUserRepository;
        _categoryRepository = categoryRepository;
    }

    public async Task<Guid> Handle(CreateSellerProductCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо компанії, до яких належить продавець
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.SellerId,
            cancellationToken: cancellationToken
        );

        if (!companyUsers.Any())
            throw new InvalidOperationException("Ви не є продавцем жодної компанії.");

        // Перевіряємо, чи існує категорія
        var category = await _categoryRepository.GetByIdAsync(request.CategoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Категорія з ID {request.CategoryId} не знайдена.");

        // Створюємо новий продукт
        var product = new Domain.Entities.Product(
            companyUsers.First().CompanyId,
            request.SellerId,
            request.Name,
            new Slug(request.Slug),
            request.Description,
            new Money(request.PriceAmount, request.PriceCurrency),
            request.Stock,
            request.Sales,
            request.CategoryId,
            request.Attributes,
            new Domain.ValueObjects.Meta(
                request.MetaTitle,
                request.MetaDescription,
                request.MetaImage != null ? new Url(request.MetaImage) : null
            )
        );

        await _productRepository.AddAsync(product, cancellationToken);
        return product.Id;
    }
}
