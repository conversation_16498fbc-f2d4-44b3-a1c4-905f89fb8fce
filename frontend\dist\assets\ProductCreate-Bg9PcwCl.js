import{_ as n,s as d,e as m,o as i}from"./index-L-hJxM_5.js";import p from"./ProductEdit-CKOjAklg.js";import"./AdminProductHeader-CEj_aj3M.js";import"./EnhancedProductAttributesEditor-lcyAKYjd.js";import"./EnhancedCategorySelector-v7wVF3eo.js";import"./products-Bpq90UOX.js";import"./image.service-DOD4lHqw.js";import"./EntityMetaImageManager-oIAk4B-a.js";const l={__name:"ProductCreate",emits:["save","cancel","created"],setup(u,{emit:o}){const r=m(),t=o,c=e=>{console.log("Product saved:",e),t("save",e)},a=e=>{console.log("Product created:",e),t("created",e)},s=()=>{t("cancel"),r.push("/admin/products")};return(e,_)=>(i(),d(p,{"is-create":!0,onSave:c,onCancel:s,onCreated:a}))}},B=n(l,[["__scopeId","data-v-a5ded38f"]]);export{B as default};
