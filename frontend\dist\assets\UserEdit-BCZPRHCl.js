import{_ as j,g as y,C as q,h as R,f as K,i as G,c as n,a,d as c,t as m,n as p,k as f,m as Z,x as v,D as b,y as X,l as H,b as x,w as J,r as Q,B as W,e as Y,o as i}from"./index-L-hJxM_5.js";import{u as V}from"./users-D6yG63l9.js";import{E as ee}from"./EntityImageManager-DFnfDtsC.js";import{U as ae}from"./UploadProgress-DTyUnfEW.js";import{i as B}from"./image.service-DOD4lHqw.js";const se={class:"admin-page"},re={class:"admin-page-header"},te={class:"admin-page-title-section"},le={class:"admin-page-title"},oe={class:"admin-page-subtitle"},ne={class:"admin-page-actions"},ie=["disabled"],de={key:0,class:"admin-loading-state"},me={key:1,class:"admin-alert admin-alert-danger"},ue={key:2,class:"admin-user-edit-content"},pe={class:"admin-card"},fe={class:"admin-card-content"},ve={class:"admin-form-section"},ce={class:"admin-form-row"},ge={class:"admin-form-group"},ye={key:0,class:"admin-form-error"},be={class:"admin-form-group"},we={key:0,class:"admin-form-error"},Ue={class:"admin-form-row"},he={class:"admin-form-group"},_e={key:0,class:"admin-form-error"},ke={class:"admin-form-group"},Ne={key:0,class:"admin-form-error"},Ee={class:"admin-form-row"},Se={class:"admin-form-group"},Ce={key:0,class:"admin-form-error"},Ie={class:"admin-form-group"},Xe={key:0,class:"admin-form-error"},xe={class:"admin-form-row"},Ve={class:"admin-form-group"},Ae={key:0,class:"admin-form-error"},Pe={class:"admin-form-group"},qe={key:0,class:"admin-form-error"},Re={class:"admin-form-row"},Be={class:"admin-form-group"},Me={key:0,class:"admin-form-error"},De={class:"admin-form-section"},Le={class:"admin-form-row"},Fe={class:"admin-form-group admin-form-group-full"},Oe={class:"admin-form-section"},$e={class:"admin-form-group"},ze=["placeholder","required"],Te={key:0,class:"admin-form-hint"},je={key:1,class:"admin-form-error"},Ke={class:"admin-form-actions"},Ge=["disabled"],Ze={key:0},He={key:1},Je={key:0,class:"mt-3"},Qe={__name:"UserEdit",setup(We){const A=K(),U=Y(),k=y(!1),h=y(!1),N=y(!1),w=y(null),s=q({id:null,email:"",username:"",password:"",role:"",birthday:"",firstName:"",lastName:"",phone:"",gender:"",language:"",avatarUrl:null}),g=y([]),u=y(null);y({});const l=q({email:"",username:"",password:"",role:"",birthday:"",firstName:"",lastName:"",phone:"",gender:"",language:""}),d=R(()=>!!A.params.id),E=R(()=>A.params.id),S=()=>{Object.keys(l).forEach(r=>{l[r]=""})},M=()=>{S();let r=!0;return s.username.trim()?s.username.length<3&&(l.username="Username must be at least 3 characters",r=!1):(l.username="Username is required",r=!1),s.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s.email)||(l.email="Please enter a valid email address",r=!1):(l.email="Email is required",r=!1),!d.value&&!s.password.trim()?(l.password="Password is required",r=!1):s.password&&s.password.length<8?(l.password="Password must be at least 8 characters",r=!1):s.password&&!/[A-Z]/.test(s.password)?(l.password="Password must contain at least one uppercase letter",r=!1):s.password&&!/[0-9]/.test(s.password)?(l.password="Password must contain at least one number",r=!1):s.password&&!/[!@#$%^&*]/.test(s.password)&&(l.password="Password must contain at least one special character (!@#$%^&*)",r=!1),s.role||(l.role="Role is required",r=!1),r},C=async()=>{if(d.value){N.value=!0,w.value=null;try{const r=await V.getUserById(E.value);if(s.id=r.id,s.email=r.email||"",s.username=r.username||"",r.role!==void 0&&r.role!==null){const{getRoleKey:e}=await W(async()=>{const{getRoleKey:t}=await import("./roles-D6TbD4pL.js");return{getRoleKey:t}},[]);s.role=e(r.role)}else s.role="";s.birthday=r.birthday?r.birthday.split("T")[0]:"",s.firstName=r.firstName||"",s.lastName=r.lastName||"",s.phone=r.phone||"",s.gender=r.gender!==null&&r.gender!==void 0?r.gender.toString():"",s.language=r.language!==null&&r.language!==void 0?r.language.toString():"",s.password=""}catch(r){console.error("Error loading user:",r),w.value="Failed to load user data. Please try again."}finally{N.value=!1}}},P=async()=>{var r,e;if(M()){h.value=!0,w.value=null,S();try{const t={...s};d.value&&!t.password&&delete t.password,t.birthday?t.birthday=new Date(t.birthday).toISOString():delete t.birthday,t.gender===""?delete t.gender:t.gender=parseInt(t.gender),t.language===""?delete t.language:t.language=parseInt(t.language),t.firstName||delete t.firstName,t.lastName||delete t.lastName,t.phone||delete t.phone,delete t.avatarUrl;let o;d.value?(o=await V.updateUser(E.value,t),u.value&&await I()):(o=await V.createUser(t),o&&o.id&&(s.id=o.id,u.value&&await I())),d.value?U.push(`/admin/users/${E.value}`):o&&o.id?U.push(`/admin/users/${o.id}`):U.push("/admin/users")}catch(t){if(console.error("Error saving user:",t),t.response&&t.response.data&&t.response.data.errors){const o=t.response.data.errors;Object.keys(o).forEach(_=>{_.toLowerCase()in l&&(l[_.toLowerCase()]=o[_][0]||o[_])})}else w.value=((e=(r=t.response)==null?void 0:r.data)==null?void 0:e.message)||"Failed to save user. Please try again."}finally{h.value=!1}}},D=()=>{U.push("/admin/users")},L=()=>{S(),u.value=null,g.value=[],d.value?C():(s.id=null,s.email="",s.username="",s.password="",s.role="",s.birthday="",s.firstName="",s.lastName="",s.phone="",s.gender="",s.language="",s.avatarUrl=null)},F=r=>{s.avatarUrl=r.fileUrl,u.value=null,console.log("Avatar uploaded:",r)},O=async()=>{if(d.value&&s.avatarUrl)try{await B.deleteImage(s.id),s.avatarUrl=null,console.log("Avatar removed from server")}catch(r){console.error("Error removing avatar:",r)}else s.avatarUrl=null,u.value=null},$=r=>{u.value=r,console.log("Avatar changed (local):",r)},I=async()=>{if(!u.value||!s.id)return;const e={id:Date.now().toString(),filename:u.value.file.name,size:u.value.file.size,type:u.value.file.type,status:"uploading",progress:0,retryable:!0};g.value.push(e);try{const t=await B.uploadImage("user",s.id,u.value.file,o=>{e.progress=o});e.status="completed",s.avatarUrl=t.data.fileUrl,u.value=null,console.log("Avatar uploaded successfully:",t.data)}catch(t){throw e.status="error",e.error=t.message,console.error("Error uploading avatar:",t),t}},z=async r=>{const e=g.value.find(t=>t.id===r);if(e&&u.value){e.status="uploading",e.progress=0,e.error=null;try{await I()}catch{}}},T=r=>{const e=g.value.findIndex(t=>t.id===r);e!==-1&&g.value.splice(e,1)};return G(()=>{C()}),(r,e)=>{const t=Q("router-link");return i(),n("div",se,[a("div",re,[a("div",te,[a("button",{onClick:D,class:"admin-btn admin-btn-ghost admin-btn-sm"},e[10]||(e[10]=[a("i",{class:"fas fa-arrow-left"},null,-1),c(" Back to Users ")])),a("h1",le,[e[11]||(e[11]=a("i",{class:"fas fa-user-edit admin-page-icon"},null,-1)),c(" "+m(d.value?"Edit User":"Add User"),1)]),a("p",oe,m(d.value?"Update user information and settings":"Create a new user account"),1)]),a("div",ne,[a("button",{onClick:L,class:"admin-btn admin-btn-secondary"},e[12]||(e[12]=[a("i",{class:"fas fa-undo"},null,-1),c(" Reset ")])),a("button",{onClick:P,disabled:k.value,class:"admin-btn admin-btn-primary"},[a("i",{class:p(["fas fa-save",{"fa-spinner fa-pulse":k.value}])},null,2),c(" "+m(k.value?"Saving...":d.value?"Save Changes":"Create User"),1)],8,ie)])]),N.value?(i(),n("div",de,e[13]||(e[13]=[a("div",{class:"admin-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-loading-text"},"Loading user data...",-1)]))):w.value?(i(),n("div",me,[e[16]||(e[16]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),a("div",null,[e[14]||(e[14]=a("strong",null,"Error loading user data",-1)),a("p",null,m(w.value),1)]),a("button",{onClick:C,class:"admin-btn admin-btn-sm admin-btn-danger"},e[15]||(e[15]=[a("i",{class:"fas fa-retry"},null,-1),c(" Retry ")]))])):(i(),n("div",ue,[a("div",pe,[e[39]||(e[39]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-user-circle"}),c(" User Information ")])],-1)),a("div",fe,[a("form",{onSubmit:Z(P,["prevent"]),class:"admin-user-form"},[a("div",ve,[e[31]||(e[31]=a("h4",{class:"admin-form-section-title"},"Basic Information",-1)),a("div",ce,[a("div",ge,[e[17]||(e[17]=a("label",{class:"admin-form-label admin-required"}," Username * ",-1)),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.username}]),type:"text",placeholder:"Enter username","onUpdate:modelValue":e[0]||(e[0]=o=>s.username=o),required:""},null,2),[[b,s.username]]),l.username?(i(),n("div",ye,m(l.username),1)):f("",!0)]),a("div",be,[e[18]||(e[18]=a("label",{class:"admin-form-label admin-required"}," Email * ",-1)),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.email}]),type:"email",placeholder:"Enter email address","onUpdate:modelValue":e[1]||(e[1]=o=>s.email=o),required:""},null,2),[[b,s.email]]),l.email?(i(),n("div",we,m(l.email),1)):f("",!0)])]),a("div",Ue,[a("div",he,[e[20]||(e[20]=a("label",{class:"admin-form-label admin-required"}," Role * ",-1)),v(a("select",{class:p(["admin-form-select",{"admin-form-input-error":l.role}]),"onUpdate:modelValue":e[2]||(e[2]=o=>s.role=o),required:""},e[19]||(e[19]=[H('<option value="" data-v-e04ba27f>Select role</option><option value="Buyer" data-v-e04ba27f>Buyer</option><option value="Seller" data-v-e04ba27f>Seller</option><option value="SellerOwner" data-v-e04ba27f>Seller Owner</option><option value="Moderator" data-v-e04ba27f>Moderator</option><option value="Admin" data-v-e04ba27f>Admin</option>',6)]),2),[[X,s.role]]),l.role?(i(),n("div",_e,m(l.role),1)):f("",!0)]),a("div",ke,[e[21]||(e[21]=a("label",{class:"admin-form-label"}," Birthday ",-1)),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.birthday}]),type:"date","onUpdate:modelValue":e[3]||(e[3]=o=>s.birthday=o)},null,2),[[b,s.birthday]]),e[22]||(e[22]=a("div",{class:"admin-form-hint"},"Optional - Date of birth",-1)),l.birthday?(i(),n("div",Ne,m(l.birthday),1)):f("",!0)])]),a("div",Ee,[a("div",Se,[e[23]||(e[23]=a("label",{class:"admin-form-label"}," First Name ",-1)),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.firstName}]),type:"text",placeholder:"Enter first name","onUpdate:modelValue":e[4]||(e[4]=o=>s.firstName=o)},null,2),[[b,s.firstName]]),l.firstName?(i(),n("div",Ce,m(l.firstName),1)):f("",!0)]),a("div",Ie,[e[24]||(e[24]=a("label",{class:"admin-form-label"}," Last Name ",-1)),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.lastName}]),type:"text",placeholder:"Enter last name","onUpdate:modelValue":e[5]||(e[5]=o=>s.lastName=o)},null,2),[[b,s.lastName]]),l.lastName?(i(),n("div",Xe,m(l.lastName),1)):f("",!0)])]),a("div",xe,[a("div",Ve,[e[25]||(e[25]=a("label",{class:"admin-form-label"}," Phone ",-1)),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.phone}]),type:"tel",placeholder:"+380XXXXXXXXX","onUpdate:modelValue":e[6]||(e[6]=o=>s.phone=o)},null,2),[[b,s.phone]]),e[26]||(e[26]=a("div",{class:"admin-form-hint"},"Format: +380XXXXXXXXX",-1)),l.phone?(i(),n("div",Ae,m(l.phone),1)):f("",!0)]),a("div",Pe,[e[28]||(e[28]=a("label",{class:"admin-form-label"}," Gender ",-1)),v(a("select",{class:p(["admin-form-select",{"admin-form-input-error":l.gender}]),"onUpdate:modelValue":e[7]||(e[7]=o=>s.gender=o)},e[27]||(e[27]=[a("option",{value:""},"Select gender",-1),a("option",{value:"0"},"Male",-1),a("option",{value:"1"},"Female",-1)]),2),[[X,s.gender]]),l.gender?(i(),n("div",qe,m(l.gender),1)):f("",!0)])]),a("div",Re,[a("div",Be,[e[30]||(e[30]=a("label",{class:"admin-form-label"}," Language ",-1)),v(a("select",{class:p(["admin-form-select",{"admin-form-input-error":l.language}]),"onUpdate:modelValue":e[8]||(e[8]=o=>s.language=o)},e[29]||(e[29]=[a("option",{value:""},"Select language",-1),a("option",{value:"0"},"Ukrainian",-1),a("option",{value:"1"},"English",-1),a("option",{value:"2"},"Russian",-1)]),2),[[X,s.language]]),l.language?(i(),n("div",Me,m(l.language),1)):f("",!0)])])]),a("div",De,[e[33]||(e[33]=a("h4",{class:"admin-form-section-title"},[a("i",{class:"fas fa-user-circle me-2"}),c(" User Avatar ")],-1)),a("div",Le,[a("div",Fe,[x(ee,{"entity-type":"user","entity-id":s.id,"current-image":s.avatarUrl,"image-alt":"User Avatar","local-mode":!d.value,onImageUploaded:F,onImageRemoved:O,onImageChanged:$,ref:"avatarManager"},null,8,["entity-id","current-image","local-mode"]),e[32]||(e[32]=a("div",{class:"admin-form-hint"}," Upload a profile picture for this user. Recommended size: 200x200 pixels. ",-1))])])]),a("div",Oe,[e[34]||(e[34]=a("h4",{class:"admin-form-section-title"},"Password",-1)),a("div",$e,[a("label",{class:p(["admin-form-label",{"admin-required":!d.value}])}," Password "+m(d.value?"":"*"),3),v(a("input",{class:p(["admin-form-input",{"admin-form-input-error":l.password}]),type:"password",placeholder:d.value?"Leave empty to keep current password":"Enter password","onUpdate:modelValue":e[9]||(e[9]=o=>s.password=o),required:!d.value},null,10,ze),[[b,s.password]]),d.value?(i(),n("div",Te," Залиште порожнім, щоб не змінювати ")):f("",!0),l.password?(i(),n("div",je,m(l.password),1)):f("",!0)])]),e[38]||(e[38]=a("div",{class:"admin-form-legend"},[a("p",{class:"admin-form-legend-text"},[a("span",{class:"admin-required-indicator"},"*"),c(" - обов'язкові поля ")])],-1)),a("div",Ke,[a("button",{type:"submit",class:"admin-btn admin-btn-primary",disabled:h.value},[h.value?(i(),n("span",Ze,e[35]||(e[35]=[a("i",{class:"fas fa-spinner fa-spin"},null,-1),a("span",null,"Saving...",-1)]))):(i(),n("span",He,[e[36]||(e[36]=a("i",{class:"fas fa-save"},null,-1)),a("span",null,m(d.value?"Update User":"Create User"),1)]))],8,Ge),x(t,{to:"/admin/users",class:"admin-btn admin-btn-secondary"},{default:J(()=>e[37]||(e[37]=[a("i",{class:"fas fa-times"},null,-1),a("span",null,"Cancel",-1)])),_:1})])],32)])]),g.value.length>0?(i(),n("div",Je,[x(ae,{uploads:g.value,onRetryUpload:z,onCancelUpload:T},null,8,["uploads"])])):f("",!0)]))])}}},ta=j(Qe,[["__scopeId","data-v-e04ba27f"]]);export{ta as default};
