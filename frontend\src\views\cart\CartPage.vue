<template>
  <div class="cart-page">
    <div class="cart-header">
      <router-link to="/" class="back-link">
        <i class="fas fa-arrow-left"></i>
      </router-link>
      <h1 class="cart-title">Кошик <span class="cart-count">{{ totalItems }} товар (-и) </span><span v-if="discountToPercent != 0"  class="cart-count">(-{{discountToPercent}}%)</span></h1>
    </div>

    <div class="cart-content">
      <div class="cart-items">
        <button
          class="clear-cart-btn"
          @click="clearCart"
          :disabled="isClearing || !cartItems || cartItems.length === 0"
          :class="{ 'clearing': isClearing }"
        >
          <i :class="isClearing ? 'fas fa-spinner fa-spin' : 'fas fa-trash'"></i>
          {{ isClearing ? 'Очищення...' : `Видалити все (${totalItems})` }}
        </button>
        <div v-for="cartItem in cartItems" class="cart-item">
          <div class="item-details">
            <div class="remove-item-container">
              <button class="remove-item" @click="removeItem(cartItem.id)">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="item-image">
              <img
                :src="getProductImageUrlSafe(cartItem)"
                :alt="cartItem.productName || 'Product Image'"
                @error="handleImageError($event)"
                loading="lazy"
              />
            </div>
            <div class="item-info">
              <div v-if="cartItem.productStock > 0" class="item-availability">
              <span class="availability-icon">✓</span>
              <span class="availability-text">В наявності</span>
              </div>
              <h3 v-if="cartItem.productName" class="item-name">{{ cartItem.productName }}</h3>

              <!-- Відображення атрибутів товару -->
              <div v-if="cartItem.attributes" class="item-attributes">
                <div v-for="(value, key) in parseAttributes(cartItem.attributes)" :key="key" class="attribute-item">
                  <span class="attribute-label">{{ key }}:</span>
                  <span class="attribute-value">{{ value }}</span>
                </div>
              </div>

              <p v-if="cartItem.id" class="item-code">Код товару: {{ cartItem.id.toString().substring(0, 8) }}...</p>
              <div class="item-actions">
                <button
                  class="add-to-favorites"
                  :class="{ 'active': isInWishlist(cartItem.productId) }"
                  @click="toggleWishlist(cartItem.productId)"
                  :title="isInWishlist(cartItem.productId) ? 'Видалити з обраних' : 'Додати до обраних'"
                >
                  <span v-if="isInWishlist(cartItem.productId)" class="add-to-favorites-heart">
                    <i  class='fas fa-heart'></i>
                  </span>
                  <span v-if="!isInWishlist(cartItem.productId)" class="add-to-favorites-heart">
                    <i class='far fa-heart'></i>
                  </span>
                  <span class="add-to-favorites-text">
                    {{ isInWishlist(cartItem.productId) ? 'В обраних' : 'В обрані' }}
                  </span>
                </button>
              </div>
            </div>
            <div class="item-price-controls">
              <div class="price-container">
                <div v-if="cartItem.totalPrice" class="price-current">{{ Math.round(cartItem.totalPrice) }} ₴</div>
                <div v-if="cartItem.oldPrice" class="price-original">{{ cartItem.oldPrice}} ₴</div>
              </div>
              <div class="quantity-controls">
                <button class="quantity-btn minus"
                        @click="decreaseQuantity(cartItem)">
                  <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="quantity-input" :value="cartItem.quantity" min="1" />
                <button class="quantity-btn plus"
                        @click="increaseQuantity(cartItem)">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="cart-summary">
        <h2 class="summary-title">Разом</h2>
        <div class="summary-row">
          <span>{{ totalItems }} товари на суму</span>
          <span class="summary-value">{{ originalTotal }} ₴</span>
        </div>
        <div class="summary-row">
          <span>Знижка</span>
          <span class="summary-value discount">{{ discount }} ₴</span>
        </div>

        <!-- Знижка від купона -->
        <CouponSummary :coupon-discount="couponDiscount" />

        <!-- Сепаратор перед вартістю доставки, якщо є знижка від купона -->
        <div v-if="couponDiscount && couponDiscount.discountAmount > 0" class="summary-divider"></div>

        <!-- <div class="summary-row">
          <span>Вартість доставки</span>
          <span class="summary-delivery" :class="{ 'free': !shippingCost || shippingCost.cost === 0 }">
            {{ shippingCost ? (shippingCost.cost === 0 ? 'Безкоштовно' : `${Math.round(shippingCost.cost)} ₴`) : 'Не вибрано' }}
          </span>
        </div> -->

        <!-- Інформація про вибране відділення -->
        <div v-if="selectedDeliveryOffice" class="summary-row delivery-office-info">
          <span>Відділення</span>
          <span class="delivery-office-details">
            {{ formatDeliveryOfficeInfo() }}
          </span>
        </div>
        <div class="summary-divider"></div>
        <div class="summary-row total">
          <span>До оплати</span>
          <span class="summary-value">{{ finalTotal }} ₴</span>
        </div>
        <div class="summary-divider"></div>
        <button class="checkout-btn">Перейти до оформлення</button>

        <!-- Компонент для введення купона -->
        <div class="coupon-section">
          <CouponInput
            :applied-coupon="appliedCoupon"
            @coupon-applied="onCouponApplied"
            @coupon-removed="onCouponRemoved"
          />
        </div>
      </div>
    </div>

    <!-- Recommended Products -->
    <section class="recommended-section">
      <div class="container">
        <h2 class="section-title">Рекомендації на основі ваших переглядів</h2>
        <ProductGrid
          :fetch-params="{ type: 'recommended', status: 1, pageSize: 4 }"
          :grid-columns="4"
          empty-message="Рекомендовані товари поки що недоступні"
        />
      </div>
    </section>
  </div>
</template>

<script>
import wishlistService from '../../services/wishlist.service';
import CartService from '../../services/cart.service';
import cartService from '../../services/cart.service';
import ProductGrid from '@/components/common/ProductGrid.vue';
import { useWishlistSync } from '@/composables/useWishlistSync';
import { useToast } from '@/composables/useToast';
import CouponInput from '@/components/cart/CouponInput.vue';
import CouponSummary from '@/components/cart/CouponSummary.vue';
import ShippingSelector from '@/components/cart/ShippingSelector.vue';
import couponService from '@/services/coupon.service';

export default {
  name: 'Cart',
  components: {
    ProductGrid,
    CouponInput,
    CouponSummary,
    ShippingSelector
  },
  data() {
    const wishlistSync = useWishlistSync();
    const { showToast } = useToast();

    return {
      cartItems: [],
      error: null,
      isClearing: false, // Стан завантаження для кнопки очищення
      wishlistItems: [], // Список товарів в обраних
      wishlistLoaded: false, // Чи завантажений wishlist
      wishlistSync, // Додаємо wishlistSync до data
      showToast, // Додаємо showToast до data
      // Логіка зображень (скопійовано з ProductGrid)
      placeholderImage: '/placeholder-product.svg',
      imageCache: new Map(), // Кеш для URL зображень
      loadingImages: new Set(), // Множина ID товарів, для яких завантажуються зображення
      // Логіка купонів
      appliedCoupon: null, // Застосований купон
      couponDiscount: null, // Розрахунки знижки
      // Логіка доставки
      selectedShippingMethod: null, // Вибраний метод доставки
      selectedDeliveryOffice: null, // Вибране відділення доставки
      shippingCost: null, // Розрахунки вартості доставки
    };
  },
  computed: {
    totalItems() {
      return this.cartItems.reduce((total, item) => total + item.quantity, 0);
    },
    subtotal() {
      return  Number.isNaN(Number(this.cartItems.reduce((total, item) => total + item.total, 0))) ? this.originalTotal 
      : this.cartItems.reduce((total, item) => total + item.total, 0);
    },
    originalTotal() {
      return this.cartItems.reduce((total, item) => total + Math.round(item.totalPrice), 0);
    },
    discount() {
      return this.subtotal == this.originalTotal ? 0 : this.orginalTotal - this.subtotal;
    },
    discountToPercent() {
      return this.discount == 0 ? 0 : 100 * (this.orginalTotal - this.subtotal) / this.originalTotal;
    },
    cartItemsCount() {
      return this.cartItems.length;
    },
    // Фінальна сума з урахуванням купона та доставки
    finalTotal() {
      const baseTotal = this.couponDiscount ? this.couponDiscount.finalAmount : this.subtotal;
      const shippingCost = this.shippingCost ? this.shippingCost.cost : 0;
      return baseTotal + shippingCost;
    },
    // Загальна економія
    totalSavings() {
      const originalDiscount = this.originalTotal - this.subtotal;
      const couponDiscount = this.couponDiscount ? this.couponDiscount.discountAmount : 0;
      return originalDiscount + couponDiscount;
    }
  },
  async mounted() {
    await this.fetchCart();
    await this.fetchWishlist();

    // Завантажуємо зображення для товарів в корзині
    this.$nextTick(() => {
      this.preloadImages();
    });

    // Підписуємося на зміни wishlist з ProductGrid
    this.wishlistSync.onItemAdded(this.onWishlistItemAdded);
    this.wishlistSync.onItemRemoved(this.onWishlistItemRemoved);

    // Підписуємося на додавання товарів в корзину з ProductGrid
    this.wishlistSync.onCartItemAdded(this.onCartItemAdded);
  },

  beforeDestroy() {
    // Відписуємося від подій wishlist
    this.wishlistSync.offItemAdded(this.onWishlistItemAdded);
    this.wishlistSync.offItemRemoved(this.onWishlistItemRemoved);

    // Відписуємося від подій корзини
    this.wishlistSync.offCartItemAdded(this.onCartItemAdded);
  },

  watch: {
    // Завантажуємо зображення при зміні товарів в корзині
    cartItems: {
      handler(newCartItems) {
        if (newCartItems && newCartItems.length > 0) {
          // Завантажуємо зображення для нових товарів
          this.$nextTick(() => {
            this.preloadImages();
          });
        }

        // Перерахунковуємо знижку купона при зміні корзини
        if (this.appliedCoupon) {
          this.calculateCouponDiscount();
        }
      },
      deep: true
    },

    // Перерахунковуємо знижку при зміні subtotal
    subtotal() {
      if (this.appliedCoupon) {
        this.calculateCouponDiscount();
      }
    }
  },
  methods: {
    

    
    async fetchCart() {
      try {
        const response = await CartService.getCart();
        this.cartItems = response.data.data.items;
        this.error = null;
      } catch(error) {
        console.error('Error fetching cart:', error);
        this.error = 'Failed to load cart. Please try again.'
      }
    },

    async increaseQuantity(item) {
      if(item.quantity < item.productStock)
      {
        await cartService.changeItemCount(item.id, item.quantity+1);
      }
      await this.fetchCart();
    },
    async decreaseQuantity(item) {
      if (item.quantity > 1) {
        await cartService.changeItemCount(item.id, item.quantity-1);
      } else {
        await cartService.deleteItem(item.id);
      }
      await this.fetchCart();
    },
    async removeItem(itemId) {
      await cartService.deleteItem(itemId);
      await this.fetchCart();
    },
    async clearCart() {
      // Перевіряємо, чи є товари в корзині
      if (!this.cartItems || this.cartItems.length === 0) {
        return;
      }

      // Підтвердження від користувача
      const confirmed = confirm(`Ви впевнені, що хочете видалити всі товари з корзини? (${this.totalItems} товарів)`);

      if (!confirmed) {
        return;
      }

      this.isClearing = true; // Встановлюємо стан завантаження

      try {
        await cartService.deleteCart();

        // Оновлюємо корзину після очищення
        await this.fetchCart();

        // Показуємо повідомлення про успіх
        this.showSuccessMessage('Корзину успішно очищено');
      } catch (error) {
        console.error('Error clearing cart:', error);
        this.error = 'Не вдалося очистити корзину. Спробуйте ще раз.';
        this.showErrorMessage('Помилка при очищенні корзини');
      } finally {
        this.isClearing = false; // Знімаємо стан завантаження
      }
    },
    // Завантажує список ID товарів в обраних (скопійовано з ProductGrid)
    async fetchWishlist() {
      try {
        this.wishlistLoaded = false;
        const response = await wishlistService.getWishlist();

        // Обробляємо структуру відповіді
        let wishlistData = null;
        if (response.data.success) {
          wishlistData = response.data.data;
        } else if (response.data.data) {
          wishlistData = response.data.data;
        } else {
          wishlistData = response.data;
        }

        // Витягуємо productId з items
        if (wishlistData && wishlistData.items && Array.isArray(wishlistData.items)) {
          this.wishlistItems = wishlistData.items.map(item => item.productId);
        } else {
          this.wishlistItems = [];
        }

        this.wishlistLoaded = true;
      } catch (error) {
        console.error('Error loading wishlist:', error);
        // Якщо wishlist не існує (404), це нормально
        if (error.response?.status === 404) {
          this.wishlistItems = [];
        } else {
          this.wishlistItems = [];
        }
        this.wishlistLoaded = true;
      }
    },

    // Перевіряє чи товар в обраних (скопійовано з ProductGrid)
    isInWishlist(productId) {
      return this.wishlistItems.includes(productId);
    },



    // Перемикає стан товару в обраних (скопійовано з ProductGrid)
    async toggleWishlist(productId) {
      try {
        if (this.isInWishlist(productId)) {
          await this.removeFromWishlist(productId);
        } else {
          await this.addToWishlist(productId);
        }
      } catch (error) {
        console.error('Error in toggleWishlist:', error);
      }
    },

    // Додає товар до обраних (скопійовано з ProductGrid)
    async addToWishlist(productId) {
      try {
        await wishlistService.addToWishlist(productId);

        if (!this.wishlistItems.includes(productId)) {
          this.wishlistItems.push(productId);
        }

        this.showSuccessMessage('Товар додано до обраних');
        this.$emit('product-added-to-wishlist', productId);

        // Синхронізуємо з глобальним станом
        this.wishlistSync.addToGlobalWishlist(productId);
      } catch (error) {
        console.error('Error adding to wishlist:', error);
        this.showErrorMessage('Помилка при додаванні до обраних');
      }
    },

    // Видаляє товар з обраних (скопійовано з ProductGrid)
    async removeFromWishlist(productId) {
      try {
        await wishlistService.removeFromWishlist(productId);

        this.wishlistItems = this.wishlistItems.filter(id => id !== productId);

        this.showSuccessMessage('Товар видалено з обраних');
        this.$emit('product-removed-from-wishlist', productId);

        // Синхронізуємо з глобальним станом
        this.wishlistSync.removeFromGlobalWishlist(productId);
      } catch (error) {
        console.error('Error removing from wishlist:', error);
        this.showErrorMessage('Помилка при видаленні з обраних');
      }
    },

    applyPromoCode() {
      // Implementation for applying promo code
    },
    proceedToCheckout() {
      // Implementation for checkout process
    },

    showSuccessMessage(message) {
      this.showToast(message, 'success');
    },

    showErrorMessage(message) {
      this.showToast(message, 'error');
    },

    // Обробляє додавання товару до wishlist з ProductGrid
    onWishlistItemAdded(productId) {
      if (!this.wishlistItems.includes(productId)) {
        this.wishlistItems.push(productId);
      }
    },

    // Обробляє видалення товару з wishlist з ProductGrid
    onWishlistItemRemoved(productId) {
      this.wishlistItems = this.wishlistItems.filter(id => id !== productId);
    },

    // Обробляє додавання товару в корзину з ProductGrid
    async onCartItemAdded() {
      // Оновлюємо корзину після додавання товару
      try {
        await this.fetchCart();
        this.showToast('Корзина оновлена', 'info');

        // Завантажуємо зображення для нових товарів
        this.$nextTick(() => {
          this.preloadImages();
        });
      } catch (error) {
        console.error('Cart - Error refreshing cart after item added:', error);
      }
    },

    // ===== МЕТОДИ ДЛЯ РОБОТИ З ЗОБРАЖЕННЯМИ (скопійовано з ProductGrid) =====

    // Отримує ID товару з різних форматів
    getProductId(product) {
      const id = product.productId || product.id;
      return id;
    },

    // Безпечний метод для отримання URL зображення (без асинхронних викликів)
    getProductImageUrlSafe(product) {
      const productId = this.getProductId(product);

      // Якщо є пряме посилання на зображення, використовуємо його
      if (product.image || product.mainImage || product.imageUrl || product.productImage) {
        const directUrl = product.image || product.mainImage || product.imageUrl || product.productImage;
        return directUrl;
      }

      // Спробуємо створити URL на основі productId та стандартних шляхів
      if (productId) {
        // Перевіряємо кеш для стандартного URL
        if (this.imageCache.has(`standard_${productId}`)) {
          const cachedStandardUrl = this.imageCache.get(`standard_${productId}`);
          return cachedStandardUrl;
        }
      }

      // Перевіряємо кеш
      if (this.imageCache.has(productId)) {
        const cachedUrl = this.imageCache.get(productId);
        return cachedUrl;
      }

      // Запускаємо асинхронне завантаження
      if (productId) {
        this.$nextTick(() => {
          this.loadProductImage(productId);
        });
      }

      // Fallback на placeholder
      return this.placeholderImage;
    },

    // Отримує URL зображення товару (з асинхронним завантаженням)
    getProductImageUrl(product) {
      const productId = this.getProductId(product);

      // Якщо є пряме посилання на зображення, використовуємо його
      if (product.image || product.mainImage || product.imageUrl) {
        return product.image || product.mainImage || product.imageUrl;
      }

      // Перевіряємо кеш
      if (this.imageCache.has(productId)) {
        return this.imageCache.get(productId);
      }

      // Якщо зображення завантажується, повертаємо placeholder
      if (this.loadingImages.has(productId)) {
        return this.placeholderImage;
      }

      // Якщо є ID товару, завантажуємо URL асинхронно
      if (productId) {
        // Використовуємо nextTick для безпечного виклику
        this.$nextTick(() => {
          this.loadProductImage(productId);
        });
        return this.placeholderImage; // Тимчасово показуємо placeholder
      }

      // Fallback на placeholder
      return this.placeholderImage;
    },

    // Асинхронно завантажує URL зображення товару
    async loadProductImage(productId) {
      // Перевіряємо, чи компонент ще існує
      if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
        return;
      }

      if (this.loadingImages.has(productId) || this.imageCache.has(productId)) {
        return; // Вже завантажується або завантажено
      }

      this.loadingImages.add(productId);

      try {
        const apiUrl = import.meta.env.VUE_APP_API_URL || 'http://localhost:5296';

        // Спробуємо кілька варіантів API endpoints
        const imageEndpoint =  `${apiUrl}/api/universal/images/product/${productId}`;

        let response = await fetch(imageEndpoint);

        if (!response || !response.ok) {
          this.imageCache.set(productId, this.placeholderImage);
          return;
        }

        // Перевіряємо, чи компонент ще існує після асинхронної операції
        if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
          return;
        }

        if (response.ok) {
          const data = await response.json();

          // Перевіряємо, чи компонент ще існує після другої асинхронної операції
          if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
            return;
          }

          let imageUrl = null;

          // Перевіряємо різні формати відповіді API
          if (data.success && data.data) {
            data.data.forEach(element => {
              if(element.isMain == true)
              {
                imageUrl = element.url;
                return;
              }
            });
          } else if (data.data) {
            imageUrl = data.data;
          } else if (typeof data === 'string') {
            imageUrl = data;
          }


          if (imageUrl && imageUrl !== 'null' && imageUrl !== '') {
            this.imageCache.set(productId, imageUrl);
            // Примусово оновлюємо компонент для показу нового зображення
            this.$forceUpdate();
          } else {
            // Спробуємо використати fallback зображення
            const fallbackImageUrl = this.generateFallbackImageUrl(productId);
            if (fallbackImageUrl) {
              this.imageCache.set(productId, fallbackImageUrl);
              this.$forceUpdate();
            } else {
              // Якщо зображення не знайдено, кешуємо placeholder
              this.imageCache.set(productId, this.placeholderImage);
            }
          }
        } else {
          console.warn(`Cart - Image not found for product ${productId}: ${response.status}`);
          // Якщо помилка API, кешуємо placeholder
          this.imageCache.set(productId, this.placeholderImage);
        }
      } catch (error) {
        console.error('Cart - Error loading product image:', productId, error);
        // При помилці кешуємо placeholder
        this.imageCache.set(productId, this.placeholderImage);
      } finally {
        this.loadingImages.delete(productId);
      }
    },

    // Попередньо завантажує зображення для всіх товарів в корзині
    preloadImages() {
      if (this.cartItems && this.cartItems.length > 0) {
        this.cartItems.forEach(cartItem => {
          const productId = this.getProductId(cartItem);

          if (productId && !cartItem.image && !cartItem.mainImage && !cartItem.imageUrl && !cartItem.productImage) {
            // Завантажуємо зображення тільки якщо немає прямого URL
            // Використовуємо setTimeout для безпечного асинхронного виклику
            setTimeout(() => {
              this.loadProductImage(productId);
            }, 0);
          }
        });
      }
    },

    // Генерує fallback URL для зображення
    generateFallbackImageUrl(productId) {
      const apiUrl = import.meta.env.VUE_APP_API_URL || 'http://localhost:5296';

      // Спробуємо різні варіанти fallback зображень
      const fallbackUrls = [
        `${apiUrl}/uploads/images/product/${productId}/main.jpg`,
        `${apiUrl}/uploads/images/product/${productId}/main.png`,
        `${apiUrl}/uploads/images/product/${productId}/image.jpg`,
        `${apiUrl}/uploads/images/product/${productId}/image.png`,
        `${apiUrl}/images/products/${productId}.jpg`,
        `${apiUrl}/images/products/${productId}.png`
      ];

      // Повертаємо перший URL для спроби
      return fallbackUrls[0];
    },

    // Обробляє помилки завантаження зображень
    handleImageError(event) {
      if (event.target.src !== this.placeholderImage) {
        // Спробуємо інші fallback варіанти
        const currentSrc = event.target.src;

        if (currentSrc.includes('main.jpg')) {
          event.target.src = currentSrc.replace('main.jpg', 'main.png');
        } else if (currentSrc.includes('main.png')) {
          event.target.src = currentSrc.replace('main.png', 'image.jpg');
        } else if (currentSrc.includes('image.jpg')) {
          event.target.src = currentSrc.replace('image.jpg', 'image.png');
        } else {
          // Якщо всі варіанти не спрацювали, використовуємо placeholder
          event.target.src = this.placeholderImage;
        }
      }
    },

    // Обробляє атрибути товару
    parseAttributes(attributes) {
      if (!attributes) return {};

      // Якщо це вже об'єкт, повертаємо його
      if (typeof attributes === 'object' && !Array.isArray(attributes)) {
        return attributes;
      }

      // Якщо це JSON рядок, парсимо його (для зворотної сумісності)
      if (typeof attributes === 'string') {
        try {
          return JSON.parse(attributes);
        } catch (error) {
          console.error('Error parsing attributes JSON:', error);
          return {};
        }
      }

      return {};
    },

    // ===== МЕТОДИ ДЛЯ РОБОТИ З КУПОНАМИ =====

    // Обробляє застосування купона
    onCouponApplied(coupon) {
      this.appliedCoupon = coupon;
      this.calculateCouponDiscount();
    },

    // Обробляє видалення купона
    onCouponRemoved() {
      this.appliedCoupon = null;
      this.couponDiscount = null;
    },

    // Розраховує знижку від купона
    calculateCouponDiscount() {
      if (!this.appliedCoupon) {
        this.couponDiscount = null;
        return;
      }

      this.couponDiscount = couponService.calculateDiscount(
        this.appliedCoupon,
        this.subtotal
      );
    },
  }
};
</script>

<style scoped>

.add-to-favorites-heart {
  margin-right: 5px;
}

.product-availability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #00a046;
}

.availability-icon {
  margin-right: 4px;
}

.cart-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.cart-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-link {
  font-size: 18px;
  color: #333;
  margin-right: 15px;
  text-decoration: none;
}

.cart-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  flex-grow: 1;
}

.cart-count {
  font-size: 16px;
  font-weight: normal;
  color: #666;
}

.clear-cart-btn {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-top: 7px;
  margin-bottom: 7px;
  width: 100%;
  padding: 12px 16px;
  justify-content: center;
}

.clear-cart-btn:hover:not(:disabled) {
  background: #f5f5f5;
  color: #dc3545;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.clear-cart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.clear-cart-btn.clearing {
  background: #f8f9fa;
  color: #6c757d;
}

.clear-cart-btn i {
  margin-right: 8px;
}

/* Стилі ProductGrid компонента використовуються з самого компонента */

.cart-content {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.cart-items {
  flex: 1;
}

.cart-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  padding: 15px;
}

.item-availability {
  color: #4caf50;
  font-size: 14px;
  margin-bottom: 10px;
}

.item-availability i {
  margin-right: 5px;
}

.item-details {
  display: flex;
  position: relative;
}

.item-image {
  width: 120px;
  height: 120px;
  margin-right: 15px;
  margin-left: 15px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 5px 0;
}

/* Стилі для атрибутів товару */
.item-attributes {
  margin: 8px 0 12px 0;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.attribute-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 13px;
}

.attribute-item:last-child {
  margin-bottom: 0;
}

.attribute-label {
  font-weight: 500;
  color: #495057;
  margin-right: 8px;
  min-width: 60px;
}

.attribute-value {
  color: #007bff;
  font-weight: 600;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.item-code {
  color: #999;
  font-size: 12px;
  margin: 0 0 10px 0;
}

.item-actions {
  display: flex;
}

.add-to-favorites {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.add-to-favorites:hover {
  color: #dc3545;
}

.add-to-favorites.active {
  color: #dc3545;
}

.add-to-favorites.active .fas.fa-heart {
  color: #dc3545;
}

.add-to-favorites .far.fa-heart {
  color: #6c757d;
}

.add-to-favorites i {
  margin-right: 8px;
  transition: all 0.2s ease;
}

.add-to-favorites:hover i {
  transform: scale(1.1);
}

/* Анімація для серця при додаванні/видаленні */
.add-to-favorites.active i {
  animation: heartBeat 0.6s ease-in-out;
}

.add-to-favorites.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.add-to-favorites:disabled {
  pointer-events: none;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.item-price-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 20px;
  min-width: 120px;
}

.price-container {
  text-align: right;
  margin-bottom: 10px;
}

.price-current {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.price-original {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.quantity-btn {
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  font-size: 12px;
  padding: 5px 10px;
}

.quantity-input {
  width: 40px;
  border: none;
  text-align: center;
  font-size: 14px;
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.remove-item-container {
  height: 120px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-item {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 20px;
}

.cart-summary {
  width: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  align-self: flex-start;
}

.summary-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 15px;
  color: #666;
}

.summary-value {
  font-weight: 600;
  font-size: 18px;
  color: #333;
}

.summary-value.discount {
  color: #999;
}

.summary-value.free {
  color: #4caf50;
}

.summary-delivery {
  font-size: 14px;
  color: #666;
}

.summary-delivery.free {
  color: #4caf50;
}

.delivery-office-info {
  font-size: 14px;
  color: #666;
}

.delivery-office-details {
  font-size: 13px;
  color: #555;
  text-align: right;
  max-width: 200px;
  word-wrap: break-word;
}

.summary-divider {
  height: 1px;
  background-color: #eee;
  margin: 20px 0;
}

.summary-row.total {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.checkout-btn {
  justify-content: center;
  align-items: center;
  width: 100%;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 0;
  transition: all 0.3s ease;
}

.checkout-btn:hover {
  background-color: #1976d2;
  transform: translateY(-1px);
}

.coupon-section {
  margin-top: 20px;
}

.shipping-section {
  margin-top: 20px;
}

/* Старі стилі промокода видалено - тепер використовуємо CouponInput компонент */

.recommended-section {
  margin-top: 48px;
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
}

.product-carousel {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 20px;
}

.product-card {
  width: 250px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex-shrink: 0;
}

.product-image {
  height: 200px;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-options-btn,
.add-to-favorites-btn {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.product-options-btn {
  top: 10px;
  right: 10px;
}

.add-to-favorites-btn {
  top: 10px;
  right: 50px;
}

.product-discount {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: #ff5722;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 10px 0;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-availability {
  color: #4caf50;
  font-size: 12px;
  margin-bottom: 10px;
}

.product-availability i {
  margin-right: 5px;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  justify-content: flex-end;
}

.add-to-cart-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

@media (max-width: 768px) {
  .cart-content {
    flex-direction: column;
  }
  
  .cart-summary {
    width: 100%;
  }
  
  .item-details {
    flex-wrap: wrap;
  }
  
  .item-price-controls {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-left: 0;
    margin-top: 15px;
  }
}

.top-products-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #000;
  text-align: left;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.product-card {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ff7a00;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.product-image {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 8px;
  color: #333;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 54px;
}

.product-availability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #00a046;
}

.product-unavailability {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #a00000;
}

.availability-icon {
  margin-right: 4px;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.price-old {
  font-size: 12px;
  color: #888;
  text-decoration: line-through;
  margin-right: 8px;
}

.price-discount {
  font-size: 12px;
  color: white;
  background: #ff5252;
  padding: 2px 4px;
  border-radius: 4px;
}

.price-current {
  font-size: 18px;
  font-weight: 700;
  color: #000;
  margin-bottom: 12px;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.wishlist-btn, .cart-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.wishlist-btn:hover {
  color: #ff5252;
  border-color: #ff5252;
}

.cart-btn {
  background: #0066cc;
  color: white;
  border: none;
}

.cart-btn:hover {
  background: #0055aa;
}

.cart-btn svg, .wishlist-btn svg {
  width: 18px;
  height: 18px;
}

</style>
