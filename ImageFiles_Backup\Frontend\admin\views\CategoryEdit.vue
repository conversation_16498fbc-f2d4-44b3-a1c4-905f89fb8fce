<template>
  <div class="category-edit">
    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader-wrapper">
        <div class="loader is-loading"></div>
        <p class="mt-3">Loading category...</p>
      </div>
    </div>

    <!-- Category Not Found -->
    <div v-else-if="!category && !loading" class="has-text-centered py-6">
      <span class="icon is-large has-text-grey-light">
        <i class="fas fa-folder-open fa-3x"></i>
      </span>
      <p class="title is-5 has-text-grey">Category not found</p>
      <p class="subtitle is-6 has-text-grey">The requested category does not exist</p>
      <router-link to="/admin/categories" class="button is-primary">
        <span class="icon">
          <i class="fas fa-arrow-left"></i>
        </span>
        <span>Back to Categories</span>
      </router-link>
    </div>

    <!-- Edit Form -->
    <div v-else>
      <!-- Header -->
      <div class="level mb-5">
        <div class="level-left">
          <div class="level-item">
            <div>
              <nav class="breadcrumb" aria-label="breadcrumbs">
                <ul>
                  <li><router-link to="/admin">Dashboard</router-link></li>
                  <li><router-link to="/admin/categories">Categories</router-link></li>
                  <li><router-link :to="`/admin/categories/${category.id}`">{{ category.name }}</router-link></li>
                  <li class="is-active"><a href="#" aria-current="page">Edit</a></li>
                </ul>
              </nav>
              <h1 class="title is-4">Edit Category</h1>
              <p class="subtitle is-6">Update category information</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons">
              <router-link :to="`/admin/categories/${category.id}`" class="button is-light">
                <span class="icon">
                  <i class="fas fa-eye"></i>
                </span>
                <span>View Category</span>
              </router-link>
              <router-link to="/admin/categories" class="button is-light">
                <span class="icon">
                  <i class="fas fa-arrow-left"></i>
                </span>
                <span>Back to Categories</span>
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="notification is-danger">
        <button class="delete" @click="error = ''"></button>
        {{ error }}
      </div>

      <!-- Success Message -->
      <div v-if="success" class="notification is-success">
        <button class="delete" @click="success = ''"></button>
        {{ success }}
      </div>

      <!-- Form Card -->
      <div class="card">
        <div class="card-header">
          <p class="card-header-title">
            <span class="icon">
              <i class="fas fa-edit"></i>
            </span>
            <span>Category Information</span>
          </p>
        </div>
        <div class="card-content">
          <form @submit.prevent="submitForm">
            <!-- Basic Info -->
            <div class="field">
              <label class="label">Name <span class="has-text-danger">*</span></label>
              <div class="control">
                <input 
                  class="input" 
                  type="text" 
                  placeholder="Category name" 
                  v-model="form.name"
                  @input="generateSlug"
                  required>
              </div>
            </div>
            
            <div class="field">
              <label class="label">Slug <span class="has-text-danger">*</span></label>
              <div class="control">
                <input 
                  class="input" 
                  type="text" 
                  placeholder="category-slug" 
                  v-model="form.slug"
                  required>
              </div>
              <p class="help">URL-friendly version of the name. Auto-generated but can be edited.</p>
            </div>
            
            <div class="field">
              <label class="label">Description</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  placeholder="Category description" 
                  v-model="form.description"
                  rows="3"></textarea>
              </div>
            </div>
            
            <!-- Parent Category -->
            <div class="field">
              <label class="label">Parent Category</label>
              <div class="control">
                <div class="dropdown" :class="{ 'is-active': isParentDropdownOpen }">
                  <div class="dropdown-trigger">
                    <div class="field has-addons">
                      <div class="control is-expanded">
                        <input
                          class="input"
                          type="text"
                          placeholder="Search for parent category (leave empty for top level)..."
                          v-model="parentSearchQuery"
                          @input="onParentSearchInput"
                          @focus="openParentDropdown"
                          @blur="onParentBlur"
                        />
                      </div>
                      <div class="control">
                        <button
                          class="button"
                          type="button"
                          @click="toggleParentDropdown"
                        >
                          <span class="icon">
                            <i class="fas fa-chevron-down" :class="{ 'fa-rotate-180': isParentDropdownOpen }"></i>
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="dropdown-menu" role="menu">
                    <div class="dropdown-content">
                      <a
                        class="dropdown-item"
                        :class="{ 'is-active': !form.parentId }"
                        @mousedown.prevent="selectParentCategory(null)"
                      >
                        <div class="category-item">
                          <div class="category-name">None (Top Level)</div>
                          <div class="category-slug has-text-grey is-size-7">Root category</div>
                        </div>
                      </a>
                      <a
                        v-for="cat in filteredParentCategories"
                        :key="cat.id"
                        class="dropdown-item"
                        :class="{ 'is-active': form.parentId === cat.id }"
                        @mousedown.prevent="selectParentCategory(cat)"
                      >
                        <div class="category-item">
                          <div class="category-name">{{ cat.name }}</div>
                          <div class="category-slug has-text-grey is-size-7">{{ cat.slug }}</div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <p v-if="selectedParentCategory" class="help">
                Selected: {{ selectedParentCategory.name }}
              </p>
            </div>

            <!-- Image Upload -->
            <div class="field">
              <label class="label">Category Image</label>
              <EntityImageManager
                entity-type="category"
                :entity-id="category.id"
                :current-image="form.imageUrl"
                image-alt="Category Image"
                :local-mode="false"
                @image-uploaded="handleImageUploaded"
                @image-removed="handleImageRemoved"
                @image-changed="handleImageChanged"
                ref="imageManager"
              />
            </div>

            <!-- Display Order -->
            <div class="field">
              <label class="label">Display Order</label>
              <div class="control">
                <input 
                  class="input" 
                  type="number" 
                  min="0" 
                  placeholder="0" 
                  v-model.number="form.displayOrder">
              </div>
              <p class="help">Categories with lower numbers will be displayed first.</p>
            </div>
            
            <!-- SEO Section -->
            <div class="field">
              <label class="label">
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-search"></i>
                  </span>
                  <span>SEO Settings</span>
                </span>
              </label>
            </div>
            
            <div class="field">
              <label class="label">Meta Title</label>
              <div class="control">
                <input 
                  class="input" 
                  type="text" 
                  placeholder="SEO title for search engines" 
                  v-model="form.metaTitle"
                  maxlength="60">
              </div>
              <p class="help">
                <span :class="{ 'has-text-danger': form.metaTitle && form.metaTitle.length > 60 }">
                  {{ form.metaTitle ? form.metaTitle.length : 0 }}/60 characters
                </span>
              </p>
            </div>
            
            <div class="field">
              <label class="label">Meta Description</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  placeholder="SEO description for search engines" 
                  v-model="form.metaDescription"
                  maxlength="160"
                  rows="3"></textarea>
              </div>
              <p class="help">
                <span :class="{ 'has-text-danger': form.metaDescription && form.metaDescription.length > 160 }">
                  {{ form.metaDescription ? form.metaDescription.length : 0 }}/160 characters
                </span>
              </p>
            </div>
          </form>
        </div>
        <footer class="card-footer">
          <div class="card-footer-item">
            <div class="buttons is-fullwidth">
              <button 
                class="button is-primary is-fullwidth" 
                @click="submitForm" 
                :disabled="isSubmitting"
                :class="{ 'is-loading': isSubmitting }">
                <span class="icon">
                  <i class="fas fa-save"></i>
                </span>
                <span>Update Category</span>
              </button>
              <router-link :to="`/admin/categories/${category.id}`" class="button is-light is-fullwidth">
                <span class="icon">
                  <i class="fas fa-times"></i>
                </span>
                <span>Cancel</span>
              </router-link>
            </div>
          </div>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoriesService } from '@/admin/services/categories';
import EntityImageManager from '@/admin/components/common/EntityImageManager.vue';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const error = ref('');
const success = ref('');
const isSubmitting = ref(false);
const category = ref(null);
const categories = ref([]);

// Form state
const form = reactive({
  name: '',
  slug: '',
  description: '',
  parentId: '',
  imageUrl: '',
  displayOrder: 0,
  metaTitle: '',
  metaDescription: ''
});

// Image handling
const imageManager = ref(null);

// Parent category dropdown state
const isParentDropdownOpen = ref(false);
const parentSearchQuery = ref('');
const selectedParentCategory = ref(null);

// Computed properties
const filteredParentCategories = computed(() => {
  // Exclude self to prevent circular references
  const available = categories.value.filter(cat => cat.id !== category.value?.id);
  
  if (!parentSearchQuery.value.trim()) {
    return available;
  }
  
  const query = parentSearchQuery.value.toLowerCase().trim();
  return available.filter(cat => {
    return (
      cat.name.toLowerCase().includes(query) ||
      cat.slug.toLowerCase().includes(query)
    );
  });
});

// Methods
const fetchCategory = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    const categoryData = await categoriesService.getById(id);
    category.value = categoryData;
    
    // Populate form with category data
    Object.keys(form).forEach(key => {
      if (key in categoryData) {
        form[key] = categoryData[key] || '';
      }
    });
    
    // Set parent category if exists
    if (categoryData.parentId && categories.value.length > 0) {
      const parentCategory = categories.value.find(cat => cat.id === categoryData.parentId);
      if (parentCategory) {
        selectedParentCategory.value = parentCategory;
        parentSearchQuery.value = parentCategory.name;
      }
    }

  } catch (err) {
    console.error('Error fetching category:', err);
    error.value = 'Failed to load category. Please try again.';
  } finally {
    loading.value = false;
  }
};

const fetchCategories = async () => {
  try {
    const response = await categoriesService.getAll({ pageSize: 1000 });
    categories.value = response.data || [];
  } catch (err) {
    console.error('Error fetching categories:', err);
  }
};

// Generate slug from name
const generateSlug = () => {
  if (!form.name) return;
  
  // Only auto-generate if user hasn't manually edited the slug
  if (!form.slug || form.slug === slugify(category.value?.name || '')) {
    form.slug = slugify(form.name);
  }
};

// Slugify text
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')    // Remove all non-word characters
    .replace(/\-\-+/g, '-')      // Replace multiple - with single -
    .replace(/^-+/, '')          // Trim - from start of text
    .replace(/-+$/, '');         // Trim - from end of text
};

// Handle image events
const handleImageUploaded = (data) => {
  console.log('Image uploaded:', data);
  form.imageUrl = data.imageUrl || data.url;
};

const handleImageRemoved = () => {
  console.log('Image removed');
  form.imageUrl = '';
};

const handleImageChanged = (data) => {
  if (data && data.isLocal) {
    form.imageUrl = data.previewUrl;
  } else if (!data) {
    form.imageUrl = '';
  }
};

// Parent category dropdown methods
const selectParentCategory = (cat) => {
  if (cat) {
    selectedParentCategory.value = cat;
    form.parentId = cat.id;
    parentSearchQuery.value = cat.name;
  } else {
    selectedParentCategory.value = null;
    form.parentId = '';
    parentSearchQuery.value = '';
  }
  isParentDropdownOpen.value = false;
};

const openParentDropdown = () => {
  isParentDropdownOpen.value = true;
};

const toggleParentDropdown = () => {
  if (!isParentDropdownOpen.value) {
    if (selectedParentCategory.value && parentSearchQuery.value === selectedParentCategory.value.name) {
      parentSearchQuery.value = '';
    }
    openParentDropdown();
  } else {
    isParentDropdownOpen.value = false;
  }
};

const onParentSearchInput = () => {
  if (!isParentDropdownOpen.value) {
    isParentDropdownOpen.value = true;
  }
};

const onParentBlur = () => {
  setTimeout(() => {
    isParentDropdownOpen.value = false;
    
    if (!selectedParentCategory.value) {
      parentSearchQuery.value = '';
    } else {
      parentSearchQuery.value = selectedParentCategory.value.name;
    }
  }, 200);
};

// Submit form
const submitForm = async () => {
  error.value = '';
  success.value = '';
  isSubmitting.value = true;
  
  try {
    // Create a clean form object
    const categoryData = { ...form };
    
    // Convert string numbers to actual numbers
    categoryData.displayOrder = parseInt(categoryData.displayOrder) || 0;
    
    // If parentId is empty string, set to null
    if (categoryData.parentId === '') {
      categoryData.parentId = null;
    }
    
    await categoriesService.updateCategory(category.value.id, categoryData);
    
    success.value = 'Category updated successfully!';
    
    // Redirect to category view after a short delay
    setTimeout(() => {
      router.push(`/admin/categories/${category.value.id}`);
    }, 1500);
    
  } catch (err) {
    console.error('Error updating category:', err);
    error.value = 'Failed to update category. Please try again.';
  } finally {
    isSubmitting.value = false;
  }
};

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId && categories.value.length > 0) {
    fetchCategory(newId);
  }
});

// Lifecycle
onMounted(async () => {
  await fetchCategories();
  const categoryId = route.params.id;
  if (categoryId) {
    await fetchCategory(categoryId);
  }
});
</script>

<style scoped>
.loader-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dropdown {
  width: 100%;
}

.dropdown-menu {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.category-item {
  padding: 0.25rem 0;
}

.category-name {
  font-weight: 500;
}

.category-slug {
  margin-top: 0.125rem;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item.is-active {
  background-color: #3273dc;
  color: white;
}

.dropdown-item.is-active .category-slug {
  color: #e8e8e8;
}

.fa-rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}
</style>
