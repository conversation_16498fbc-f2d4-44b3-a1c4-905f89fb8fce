<template>
  <div class="category-form">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">{{ isEditMode ? 'Edit Category' : 'Add Category' }}</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/categories" class="button is-light">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Categories</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered py-6">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading category data...</p>
    </div>
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>
    <div v-else>
      <form @submit.prevent="saveCategory">
        <div class="columns">
          <!-- Main Category Information -->
          <div class="column is-8">
            <div class="card">
              <div class="card-header">
                <p class="card-header-title">Category Information</p>
              </div>
              <div class="card-content">
                <div class="field">
                  <label class="label">Category Name*</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="category.name" 
                      required
                      placeholder="Enter category name">
                  </div>
                </div>

                <div class="field">
                  <label class="label">Description</label>
                  <div class="control">
                    <textarea 
                      class="textarea" 
                      v-model="category.description" 
                      placeholder="Enter category description"
                      rows="4"></textarea>
                  </div>
                </div>

                <div class="field">
                  <label class="label">Slug</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="category.slug" 
                      placeholder="Enter category slug">
                  </div>
                  <p class="help">Leave empty to auto-generate from name</p>
                </div>

                <div class="field">
                  <label class="label">Parent Category</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="category.parentId">
                        <option :value="null">None (Root Category)</option>
                        <option 
                          v-for="parent in availableParents" 
                          :key="parent.id" 
                          :value="parent.id">
                          {{ parent.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Category Image -->
            <div class="card mt-4">
              <div class="card-header">
                <p class="card-header-title">Category Image</p>
              </div>
              <div class="card-content">
                <div class="columns">
                  <div class="column is-4">
                    <div class="image-preview">
                      <img 
                        :src="imagePreview || 'https://via.placeholder.com/200?text=No+Image'" 
                        alt="Category image preview"
                        @error="handleImageError">
                    </div>
                  </div>
                  <div class="column is-8">
                    <div class="file is-boxed">
                      <label class="file-label">
                        <input 
                          class="file-input" 
                          type="file" 
                          accept="image/*" 
                          @change="handleImageUpload">
                        <span class="file-cta">
                          <span class="file-icon">
                            <i class="fas fa-upload"></i>
                          </span>
                          <span class="file-label">
                            Choose an image...
                          </span>
                        </span>
                      </label>
                    </div>
                    <p class="help mt-2">Recommended size: 800x600 pixels</p>
                    
                    <div v-if="uploadingImage" class="mt-4">
                      <progress class="progress is-primary" max="100"></progress>
                      <p class="help">Uploading image...</p>
                    </div>
                    
                    <div v-if="category.image" class="mt-4">
                      <button 
                        type="button" 
                        class="button is-danger is-small"
                        @click="removeImage">
                        <span class="icon is-small">
                          <i class="fas fa-trash"></i>
                        </span>
                        <span>Remove Image</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- SEO Information -->
            <div class="card mt-4">
              <div class="card-header">
                <p class="card-header-title">SEO Information</p>
              </div>
              <div class="card-content">
                <div class="field">
                  <label class="label">Meta Title</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="category.metaTitle" 
                      placeholder="Enter meta title">
                  </div>
                  <p class="help">Leave empty to use category name</p>
                </div>

                <div class="field">
                  <label class="label">Meta Description</label>
                  <div class="control">
                    <textarea 
                      class="textarea" 
                      v-model="category.metaDescription" 
                      placeholder="Enter meta description"
                      rows="3"></textarea>
                  </div>
                </div>

                <div class="field">
                  <label class="label">Meta Keywords</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="category.metaKeywords" 
                      placeholder="Enter meta keywords (comma separated)">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="column is-4">
            <!-- Status and Visibility -->
            <div class="card">
              <div class="card-header">
                <p class="card-header-title">Status & Visibility</p>
              </div>
              <div class="card-content">
                <div class="field">
                  <label class="checkbox">
                    <input type="checkbox" v-model="category.isActive">
                    Active
                  </label>
                </div>

                <div class="field">
                  <label class="checkbox">
                    <input type="checkbox" v-model="category.isVisible">
                    Visible in navigation
                  </label>
                </div>

                <div class="field">
                  <label class="checkbox">
                    <input type="checkbox" v-model="category.isFeatured">
                    Featured category
                  </label>
                </div>

                <div class="field">
                  <label class="label">Display Order</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="number" 
                      v-model="category.displayOrder" 
                      min="0" 
                      step="1">
                  </div>
                  <p class="help">Lower numbers appear first</p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
              <div class="card-content">
                <div class="field is-grouped">
                  <div class="control is-expanded">
                    <button 
                      type="submit" 
                      class="button is-primary is-fullwidth"
                      :class="{ 'is-loading': saving }">
                      {{ isEditMode ? 'Update Category' : 'Create Category' }}
                    </button>
                  </div>
                  <div class="control">
                    <router-link 
                      to="/admin/categories" 
                      class="button is-light">
                      Cancel
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoriesService } from '@/admin/services/categories';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const allCategories = ref([]);
const uploadingImage = ref(false);
const imagePreview = ref(null);
const imageFile = ref(null);

// Category data
const category = reactive({
  name: '',
  description: '',
  slug: '',
  parentId: null,
  image: '',
  isActive: true,
  isVisible: true,
  isFeatured: false,
  displayOrder: 0,
  metaTitle: '',
  metaDescription: '',
  metaKeywords: ''
});

// Computed properties
const isEditMode = computed(() => !!route.params.id);
const categoryId = computed(() => route.params.id);

const availableParents = computed(() => {
  // Filter out the current category and its children to prevent circular references
  if (!isEditMode.value) {
    return allCategories.value;
  }
  
  // Get all descendant IDs to exclude them as potential parents
  const descendantIds = getDescendantIds(categoryId.value);
  
  return allCategories.value.filter(c => 
    c.id !== categoryId.value && !descendantIds.includes(c.id)
  );
});

// Get all descendant category IDs
const getDescendantIds = (parentId) => {
  const descendants = [];
  
  const findDescendants = (id) => {
    const children = allCategories.value.filter(c => c.parentId === id);
    
    children.forEach(child => {
      descendants.push(child.id);
      findDescendants(child.id);
    });
  };
  
  findDescendants(parentId);
  return descendants;
};

// Fetch category data if in edit mode
const fetchCategory = async () => {
  if (!isEditMode.value) return;
  
  loading.value = true;
  
  try {
    const data = await categoriesService.getCategoryById(categoryId.value);
    
    // Update category data
    Object.keys(category).forEach(key => {
      if (data[key] !== undefined) {
        category[key] = data[key];
      }
    });
    
    // Set image preview
    if (category.image) {
      imagePreview.value = category.image;
    }
    
  } catch (err) {
    console.error('Error fetching category:', err);
    error.value = 'Failed to load category data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Fetch all categories
const fetchAllCategories = async () => {
  try {
    const response = await categoriesService.getCategories();
    if (response.categories) {
      allCategories.value = response.categories;
    }
  } catch (err) {
    console.error('Error fetching categories:', err);
  }
};

// Handle image upload
const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  if (!file.type.startsWith('image/')) {
    error.value = 'Please select an image file.';
    return;
  }
  
  // Store file for later upload
  imageFile.value = file;
  
  // Create preview
  imagePreview.value = URL.createObjectURL(file);
  
  // If in edit mode, upload immediately
  if (isEditMode.value) {
    uploadImage();
  }
};

// Upload image to server
const uploadImage = async () => {
  if (!imageFile.value || !isEditMode.value) return;
  
  uploadingImage.value = true;
  
  try {
    const response = await categoriesService.uploadCategoryImage(categoryId.value, imageFile.value);
    
    if (response.imageUrl) {
      category.image = response.imageUrl;
    }
  } catch (err) {
    console.error('Error uploading image:', err);
    error.value = 'Failed to upload image. Please try again.';
  } finally {
    uploadingImage.value = false;
  }
};

// Remove image
const removeImage = () => {
  category.image = '';
  imagePreview.value = null;
  imageFile.value = null;
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/200?text=No+Image';
};

// Save category
const saveCategory = async () => {
  saving.value = true;
  error.value = null;
  
  try {
    let savedCategory;
    
    if (isEditMode.value) {
      // Update existing category
      savedCategory = await categoriesService.updateCategory(categoryId.value, category);
    } else {
      // Create new category
      savedCategory = await categoriesService.createCategory(category);
      
      // Upload image if any
      if (imageFile.value && savedCategory.category && savedCategory.category.id) {
        await categoriesService.uploadCategoryImage(savedCategory.category.id, imageFile.value);
      }
    }
    
    // Redirect to category list
    router.push('/admin/categories');
  } catch (err) {
    console.error('Error saving category:', err);
    error.value = 'Failed to save category. Please check your input and try again.';
  } finally {
    saving.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchAllCategories();
  fetchCategory();
});
</script>

<style scoped>
.category-form {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.image-preview {
  width: 100%;
  height: 200px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #dbdbdb;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.progress.is-primary::-webkit-progress-value {
  background-color: #ff7700;
}

.progress.is-primary::-moz-progress-bar {
  background-color: #ff7700;
}

.progress.is-primary::-ms-fill {
  background-color: #ff7700;
}
</style>
