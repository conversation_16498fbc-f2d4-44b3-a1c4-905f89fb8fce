/* ===== ADMIN BUTTONS SYSTEM ===== */
/* Based on Reports page button patterns */

/* ===== BASE BUTTON ===== */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-sm);
  padding: var(--admin-btn-padding-md);
  border: 1px solid transparent;
  border-radius: var(--admin-btn-radius);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  user-select: none;
  white-space: nowrap;
}

.admin-btn:focus {
  outline: none;
  box-shadow: var(--admin-shadow-focus);
}

.admin-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== BUTTON SIZES ===== */
.admin-btn-sm {
  padding: var(--admin-btn-padding-sm);
  font-size: var(--admin-text-xs);
}

.admin-btn-lg {
  padding: var(--admin-btn-padding-lg);
  font-size: var(--admin-text-base);
}

/* ===== BUTTON VARIANTS ===== */

/* Primary Button */
.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

.admin-btn-primary:hover:not(:disabled) {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
}

/* Secondary Button */
.admin-btn-secondary {
  background: var(--admin-gray-100);
  color: var(--admin-gray-700);
  border-color: var(--admin-gray-300);
}

.admin-btn-secondary:hover:not(:disabled) {
  background: var(--admin-gray-200);
  border-color: var(--admin-gray-400);
}

/* Success Button */
.admin-btn-success {
  background: var(--admin-success);
  color: white;
  border-color: var(--admin-success);
}

.admin-btn-success:hover:not(:disabled) {
  background: var(--admin-success-dark);
  border-color: var(--admin-success-dark);
}

/* Warning Button */
.admin-btn-warning {
  background: var(--admin-warning);
  color: white;
  border-color: var(--admin-warning);
}

.admin-btn-warning:hover:not(:disabled) {
  background: var(--admin-warning-dark);
  border-color: var(--admin-warning-dark);
}

/* Danger Button */
.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.admin-btn-danger:hover:not(:disabled) {
  background: var(--admin-danger-dark);
  border-color: var(--admin-danger-dark);
}

/* Info Button */
.admin-btn-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.admin-btn-info:hover:not(:disabled) {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
}

/* ===== OUTLINE BUTTONS ===== */
.admin-btn-outline-primary {
  background: transparent;
  color: var(--admin-primary);
  border-color: var(--admin-primary);
}

.admin-btn-outline-primary:hover:not(:disabled) {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-outline-secondary {
  background: transparent;
  color: var(--admin-gray-600);
  border-color: var(--admin-gray-300);
}

.admin-btn-outline-secondary:hover:not(:disabled) {
  background: var(--admin-gray-100);
  color: var(--admin-gray-700);
}

.admin-btn-outline-success {
  background: transparent;
  color: var(--admin-success);
  border-color: var(--admin-success);
}

.admin-btn-outline-success:hover:not(:disabled) {
  background: var(--admin-success);
  color: white;
}

.admin-btn-outline-warning {
  background: transparent;
  color: var(--admin-warning);
  border-color: var(--admin-warning);
}

.admin-btn-outline-warning:hover:not(:disabled) {
  background: var(--admin-warning);
  color: white;
}

.admin-btn-outline-danger {
  background: transparent;
  color: var(--admin-danger);
  border-color: var(--admin-danger);
}

.admin-btn-outline-danger:hover:not(:disabled) {
  background: var(--admin-danger);
  color: white;
}

/* ===== GHOST BUTTONS ===== */
.admin-btn-ghost {
  background: transparent;
  color: var(--admin-gray-600);
  border: none;
  padding: var(--admin-space-sm) var(--admin-space-md);
}

.admin-btn-ghost:hover:not(:disabled) {
  background: var(--admin-gray-100);
  color: var(--admin-gray-700);
}

/* ===== LINK BUTTONS ===== */
.admin-btn-link {
  background: transparent;
  color: var(--admin-primary);
  border: none;
  padding: 0;
  text-decoration: underline;
}

.admin-btn-link:hover:not(:disabled) {
  color: var(--admin-primary-dark);
}

/* ===== SPECIAL BUTTONS ===== */

/* Refresh Button */
.admin-refresh-btn {
  background: var(--admin-gray-100);
  border: 1px solid var(--admin-gray-300);
  color: var(--admin-gray-700);
  padding: var(--admin-space-sm) var(--admin-space-lg);
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  transition: all var(--admin-transition-normal);
}

.admin-refresh-btn:hover:not(:disabled) {
  background: var(--admin-gray-200);
}

.admin-refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Reset Button */
.admin-reset-btn {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  padding: var(--admin-space-sm) var(--admin-space-lg);
  background: var(--admin-gray-100);
  border: 1px solid var(--admin-gray-300);
  border-radius: var(--admin-radius-md);
  color: var(--admin-gray-600);
  font-size: var(--admin-text-sm);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
}

.admin-reset-btn:hover {
  background: var(--admin-gray-200);
  color: var(--admin-gray-700);
}

/* Apply Button */
.admin-apply-btn {
  background: var(--admin-primary);
  color: white;
  border: none;
  padding: var(--admin-space-md) var(--admin-space-2xl);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-semibold);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  transition: all var(--admin-transition-normal);
}

.admin-apply-btn:hover:not(:disabled) {
  background: var(--admin-primary-dark);
}

/* Retry Button */
.admin-retry-btn {
  background: var(--admin-danger);
  color: white;
  border: none;
  padding: var(--admin-space-md) var(--admin-space-2xl);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-semibold);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-sm);
  transition: all var(--admin-transition-normal);
}

.admin-retry-btn:hover {
  background: var(--admin-danger-dark);
}

/* Action Button */
.admin-action-btn {
  background: var(--admin-success);
  color: white;
  border: none;
  padding: var(--admin-space-sm) var(--admin-space-lg);
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
}

.admin-action-btn:hover {
  background: var(--admin-success-dark);
}

/* ===== BUTTON GROUPS ===== */
.admin-btn-group {
  display: inline-flex;
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  box-shadow: var(--admin-shadow-sm);
}

.admin-btn-group .admin-btn {
  border-radius: 0;
  border-right-width: 0;
}

.admin-btn-group .admin-btn:first-child {
  border-top-left-radius: var(--admin-radius-md);
  border-bottom-left-radius: var(--admin-radius-md);
}

.admin-btn-group .admin-btn:last-child {
  border-top-right-radius: var(--admin-radius-md);
  border-bottom-right-radius: var(--admin-radius-md);
  border-right-width: 1px;
}

/* ===== BUTTON UTILITIES ===== */
.admin-btn-block {
  width: 100%;
  justify-content: center;
}

.admin-btn-icon-only {
  padding: var(--admin-space-sm);
  width: auto;
  aspect-ratio: 1;
}

.admin-btn-loading {
  position: relative;
  color: transparent;
}

.admin-btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
