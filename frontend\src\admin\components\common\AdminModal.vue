<template>
  <Teleport to="body">
    <Transition name="admin-modal" appear>
      <div v-if="modelValue" class="admin-modal-overlay" @click="handleOverlayClick">
        <div class="admin-modal" :class="modalClasses" @click.stop>
          <!-- Header -->
          <div v-if="title || $slots.header" class="admin-modal-header">
            <slot name="header">
              <h3 class="admin-modal-title">{{ title }}</h3>
            </slot>
            <button 
              v-if="closable"
              class="admin-modal-close"
              @click="handleClose"
              type="button"
              aria-label="Close modal"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <!-- Body -->
          <div class="admin-modal-body" :class="bodyClasses">
            <slot></slot>
          </div>
          
          <!-- Footer -->
          <div v-if="$slots.footer" class="admin-modal-footer">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['sm', 'default', 'lg', 'xl', 'full'].includes(value)
  },
  closable: {
    type: Boolean,
    default: true
  },
  closeOnOverlay: {
    type: Boolean,
    default: true
  },
  closeOnEscape: {
    type: Boolean,
    default: true
  },
  scrollable: {
    type: Boolean,
    default: true
  },
  centered: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'opened', 'closed']);

// Computed
const modalClasses = computed(() => ({
  [`admin-modal--${props.size}`]: props.size !== 'default',
  'admin-modal--centered': props.centered,
  'admin-modal--loading': props.loading
}));

const bodyClasses = computed(() => ({
  'admin-modal-body--scrollable': props.scrollable
}));

// Methods
const handleClose = () => {
  emit('update:modelValue', false);
  emit('close');
};

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose();
  }
};

const handleEscapeKey = (event) => {
  if (event.key === 'Escape' && props.closeOnEscape && props.modelValue) {
    handleClose();
  }
};

// Lifecycle
onMounted(() => {
  if (props.closeOnEscape) {
    document.addEventListener('keydown', handleEscapeKey);
  }
  
  // Prevent body scroll when modal is open
  if (props.modelValue) {
    document.body.style.overflow = 'hidden';
    emit('opened');
  }
});

onUnmounted(() => {
  if (props.closeOnEscape) {
    document.removeEventListener('keydown', handleEscapeKey);
  }
  
  // Restore body scroll
  document.body.style.overflow = '';
  
  if (props.modelValue) {
    emit('closed');
  }
});

// Watch for modelValue changes
import { watch } from 'vue';

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
    emit('opened');
  } else {
    document.body.style.overflow = '';
    emit('closed');
  }
});
</script>

<style scoped>
.admin-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--admin-space-lg);
}

.admin-modal {
  background: var(--admin-bg-primary);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-xl);
  max-height: 90vh;
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Size variants */
.admin-modal--sm {
  max-width: 400px;
}

.admin-modal--lg {
  max-width: 800px;
}

.admin-modal--xl {
  max-width: 1200px;
}

.admin-modal--full {
  max-width: 95vw;
  max-height: 95vh;
}

.admin-modal--centered {
  margin: auto;
}

.admin-modal--loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Header */
.admin-modal-header {
  padding: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--admin-space-md);
  flex-shrink: 0;
}

.admin-modal-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0;
}

.admin-modal-close {
  background: none;
  border: none;
  color: var(--admin-text-muted);
  font-size: var(--admin-text-lg);
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: all var(--admin-transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.admin-modal-close:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-primary);
}

/* Body */
.admin-modal-body {
  padding: var(--admin-space-lg);
  flex: 1;
  min-height: 0;
}

.admin-modal-body--scrollable {
  overflow-y: auto;
}

/* Footer */
.admin-modal-footer {
  padding: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-light);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--admin-space-md);
  flex-shrink: 0;
}

/* Transitions */
.admin-modal-enter-active,
.admin-modal-leave-active {
  transition: opacity 0.3s ease;
}

.admin-modal-enter-from,
.admin-modal-leave-to {
  opacity: 0;
}

.admin-modal-enter-active .admin-modal,
.admin-modal-leave-active .admin-modal {
  transition: transform 0.3s ease;
}

.admin-modal-enter-from .admin-modal,
.admin-modal-leave-to .admin-modal {
  transform: scale(0.9) translateY(-20px);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-modal-overlay {
    padding: var(--admin-space-md);
  }
  
  .admin-modal {
    max-height: 95vh;
    max-width: 100%;
  }
  
  .admin-modal-header,
  .admin-modal-body,
  .admin-modal-footer {
    padding: var(--admin-space-md);
  }
  
  .admin-modal-footer {
    flex-direction: column-reverse;
    gap: var(--admin-space-sm);
  }
  
  .admin-modal-footer .admin-btn {
    width: 100%;
  }
}
</style>
