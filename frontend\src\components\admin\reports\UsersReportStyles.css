/* Demographics Section */
.demographics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.demographics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.demographic-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.demographic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.demographic-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.demographic-header i {
  font-size: 1.25rem;
  color: #667eea;
}

/* Age Groups */
.age-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.age-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.group-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.group-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.group-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.group-count {
  color: #374151;
  font-weight: 500;
}

.group-percentage {
  color: #6b7280;
}

/* Locations */
.locations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.location-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.location-info {
  flex: 1;
}

.location-name {
  font-weight: 500;
  color: #111827;
  margin-bottom: 2px;
}

.location-region {
  font-size: 0.875rem;
  color: #6b7280;
}

.location-stats {
  text-align: right;
}

.location-count {
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.location-percentage {
  font-size: 0.875rem;
  color: #6b7280;
}

/* User Types */
.user-types {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.user-type {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.2s;
}

.user-type.buyer {
  background: rgba(16, 185, 129, 0.1);
}

.user-type.seller {
  background: rgba(245, 158, 11, 0.1);
}

.user-type.admin {
  background: rgba(239, 68, 68, 0.1);
}

.type-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.buyer .type-icon {
  background: #10b981;
  color: white;
}

.seller .type-icon {
  background: #f59e0b;
  color: white;
}

.admin .type-icon {
  background: #ef4444;
  color: white;
}

.type-info {
  flex: 1;
}

.type-label {
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.type-count {
  font-size: 0.875rem;
  color: #6b7280;
}

.type-percentage {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Engagement Section */
.engagement-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.engagement-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.engagement-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.engagement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.engagement-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.engagement-header i {
  font-size: 1.25rem;
  color: #667eea;
}

/* Session Stats */
.session-stats {
  text-align: center;
}

.session-duration {
  margin-bottom: 20px;
}

.duration-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  display: block;
}

.duration-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.session-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
}

.breakdown-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.breakdown-value {
  font-weight: 500;
  color: #111827;
}

/* Page View Stats */
.pageview-stats {
  text-align: center;
}

.pageview-average {
  margin-bottom: 20px;
}

.pageview-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  display: block;
}

.pageview-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.pageview-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pageview-range {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-label {
  font-size: 0.875rem;
  color: #374151;
  min-width: 80px;
  text-align: left;
}

.range-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.range-fill {
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

.range-percentage {
  font-size: 0.875rem;
  color: #6b7280;
  min-width: 40px;
  text-align: right;
}

/* Return Stats */
.return-stats {
  text-align: center;
}

.return-rate {
  margin-bottom: 20px;
}

.rate-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  display: block;
}

.rate-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.return-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.return-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
}

.return-period {
  font-size: 0.875rem;
  color: #6b7280;
}

.return-percentage {
  font-weight: 500;
  color: #111827;
}

@media (max-width: 768px) {
  .demographics-grid,
  .engagement-grid {
    grid-template-columns: 1fr;
  }
  
  .pageview-range {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .range-label,
  .range-percentage {
    text-align: center;
    min-width: auto;
  }
  
  .duration-value,
  .pageview-value,
  .rate-value {
    font-size: 2rem;
  }
}
