/* ===== ADMIN DASHBOARD STYLES ===== */
/* Based on Reports page design patterns */

/* ===== DASHBOARD LAYOUT ===== */
.admin-dashboard {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

/* ===== DASHBOARD HEADER ===== */
.admin-dashboard__header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-dashboard__header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--admin-space-xl);
}

.admin-dashboard__title-section {
  flex: 1;
}

.admin-dashboard__title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0 0 var(--admin-space-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

.admin-dashboard__title-icon {
  font-size: var(--admin-text-3xl);
}

.admin-dashboard__subtitle {
  font-size: var(--admin-text-lg);
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: var(--admin-font-normal);
}

.admin-dashboard__actions {
  display: flex;
  gap: var(--admin-space-md);
}

.admin-dashboard__refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: var(--admin-space-md) var(--admin-space-lg);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  cursor: pointer;
}

.admin-dashboard__refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.admin-dashboard__refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.admin-dashboard__refresh-btn--loading {
  pointer-events: none;
}

/* ===== ALERT STYLES ===== */
.admin-dashboard__alert {
  display: flex;
  align-items: flex-start;
  gap: var(--admin-space-lg);
  padding: var(--admin-space-lg);
  border-radius: var(--admin-radius-lg);
  margin-bottom: var(--admin-space-xl);
  position: relative;
}

.admin-dashboard__alert--error {
  background: var(--admin-danger-bg);
  border: 1px solid var(--admin-danger-light);
  color: var(--admin-danger-dark);
}

.admin-dashboard__alert-icon {
  font-size: var(--admin-text-xl);
  flex-shrink: 0;
}

.admin-dashboard__alert-content {
  flex: 1;
}

.admin-dashboard__alert-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-dashboard__alert-message {
  margin: 0;
  font-size: var(--admin-text-base);
}

.admin-dashboard__alert-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: var(--admin-space-xs);
  border-radius: var(--admin-radius-sm);
  transition: background-color var(--admin-transition-normal);
}

.admin-dashboard__alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* ===== LOADING STATES ===== */
.admin-dashboard__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  margin-bottom: var(--admin-space-xl);
  box-shadow: var(--admin-shadow-md);
}

.admin-dashboard__loading-content {
  text-align: center;
  max-width: 400px;
  padding: var(--admin-space-2xl);
}

.admin-dashboard__loading-spinner {
  font-size: 3rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
}

.admin-dashboard__loading-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-dashboard__loading-text {
  font-size: var(--admin-text-base);
  color: var(--admin-gray-600);
  margin: 0 0 var(--admin-space-xl) 0;
}

.admin-dashboard__loading-progress {
  width: 100%;
  height: 4px;
  background: var(--admin-gray-200);
  border-radius: var(--admin-radius-full);
  overflow: hidden;
  margin-bottom: var(--admin-space-lg);
}

.admin-dashboard__loading-bar {
  height: 100%;
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-full);
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.admin-dashboard__loading-retry {
  margin-top: var(--admin-space-lg);
}

.admin-dashboard__retry-btn {
  background: var(--admin-warning);
  color: white;
  border: none;
  padding: var(--admin-space-md) var(--admin-space-lg);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  margin: 0 auto;
}

.admin-dashboard__retry-btn:hover {
  background: var(--admin-warning-dark);
  transform: translateY(-1px);
}

/* ===== EMPTY STATES ===== */
.admin-dashboard__empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  margin-bottom: var(--admin-space-xl);
  box-shadow: var(--admin-shadow-md);
}

.admin-dashboard__empty-content {
  text-align: center;
  max-width: 500px;
  padding: var(--admin-space-2xl);
}

.admin-dashboard__empty-icon {
  font-size: 4rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-xl);
}

.admin-dashboard__empty-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-dashboard__empty-text {
  font-size: var(--admin-text-lg);
  color: var(--admin-gray-600);
  margin: 0 0 var(--admin-space-xl) 0;
  line-height: var(--admin-leading-relaxed);
}

.admin-dashboard__empty-reasons {
  background: var(--admin-gray-50);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  margin-bottom: var(--admin-space-xl);
  text-align: left;
}

.admin-dashboard__empty-reasons h4 {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-dashboard__empty-reasons ul {
  margin: 0;
  padding-left: var(--admin-space-lg);
  color: var(--admin-gray-600);
}

.admin-dashboard__empty-reasons li {
  margin-bottom: var(--admin-space-sm);
}

.admin-dashboard__empty-retry {
  background: var(--admin-primary);
  color: white;
  border: none;
  padding: var(--admin-space-lg) var(--admin-space-2xl);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  margin: 0 auto;
}

.admin-dashboard__empty-retry:hover {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.admin-dashboard .admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

/* ===== MAIN DASHBOARD CONTENT ===== */
.admin-dashboard__main {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-2xl);
}

/* ===== SECTION HEADERS ===== */
.admin-dashboard__section-header {
  margin-bottom: var(--admin-space-xl);
}

.admin-dashboard__section-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-dashboard__section-icon {
  font-size: var(--admin-text-xl);
  color: var(--admin-primary);
}

.admin-dashboard__section-subtitle {
  font-size: var(--admin-text-base);
  color: var(--admin-gray-600);
  margin: 0;
  font-weight: var(--admin-font-normal);
}

/* ===== KPI SECTION ===== */
.admin-dashboard__kpi-section {
  margin-bottom: var(--admin-space-2xl);
}

.admin-dashboard__kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--admin-space-lg);
}

.admin-dashboard__kpi-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  transition: all var(--admin-transition-normal);
  position: relative;
  overflow: hidden;
}

.admin-dashboard__kpi-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

.admin-dashboard__kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-lg);
}

.admin-dashboard__kpi-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--admin-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  flex-shrink: 0;
}

.admin-dashboard__kpi-card--users .admin-dashboard__kpi-icon {
  background: var(--admin-info-bg);
  color: var(--admin-info);
}

.admin-dashboard__kpi-card--orders .admin-dashboard__kpi-icon {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-dashboard__kpi-card--products .admin-dashboard__kpi-icon {
  background: var(--admin-warning-bg);
  color: var(--admin-warning);
}

.admin-dashboard__kpi-card--revenue .admin-dashboard__kpi-icon {
  background: var(--admin-primary-bg);
  color: var(--admin-primary);
}

.admin-dashboard__kpi-card--categories .admin-dashboard__kpi-icon {
  background: var(--admin-secondary-bg);
  color: var(--admin-secondary);
}

.admin-dashboard__kpi-card--profit .admin-dashboard__kpi-icon {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-dashboard__kpi-trend {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
}

.admin-dashboard__kpi-trend--positive {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-dashboard__kpi-trend--negative {
  background: var(--admin-danger-bg);
  color: var(--admin-danger);
}

.admin-dashboard__kpi-trend--neutral {
  background: var(--admin-gray-100);
  color: var(--admin-gray-600);
}

.admin-dashboard__kpi-content {
  flex: 1;
}

.admin-dashboard__kpi-label {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--admin-font-medium);
}

.admin-dashboard__kpi-value {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  line-height: var(--admin-leading-tight);
  margin-bottom: var(--admin-space-sm);
}

.admin-dashboard__kpi-description {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-500);
  margin: 0;
  line-height: var(--admin-leading-relaxed);
}

.admin-dashboard .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== STATS CARDS GRID ===== */
.admin-dashboard .columns.is-multiline {
  margin-bottom: var(--admin-space-2xl);
}

.admin-dashboard .column.is-one-fifth {
  padding: var(--admin-space-md);
}

/* ===== STAT CARD STYLING ===== */
.admin-stat-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  transition: all var(--admin-transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-stat-card:hover {
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

.admin-stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-lg);
}

.admin-stat-card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--admin-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.admin-stat-card-icon.is-info {
  background: var(--admin-info-bg);
  color: var(--admin-info);
}

.admin-stat-card-icon.is-success {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-stat-card-icon.is-warning {
  background: var(--admin-warning-bg);
  color: var(--admin-warning);
}

.admin-stat-card-icon.is-primary {
  background: var(--admin-primary-bg);
  color: var(--admin-primary);
}

.admin-stat-card-icon.is-link {
  background: var(--admin-secondary-bg);
  color: var(--admin-secondary);
}

.admin-stat-card-content {
  flex: 1;
}

.admin-stat-card-title {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--admin-font-medium);
}

.admin-stat-card-value {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  line-height: var(--admin-leading-tight);
}

/* ===== CHARTS SECTION ===== */
.admin-dashboard__charts-section {
  margin-bottom: var(--admin-space-2xl);
}

.admin-dashboard__charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto;
  gap: var(--admin-space-lg);
}

.admin-dashboard__chart-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  transition: all var(--admin-transition-normal);
}

.admin-dashboard__chart-card:hover {
  box-shadow: var(--admin-shadow-lg);
}

.admin-dashboard__chart-card--main {
  grid-column: 1;
  grid-row: 1;
}

.admin-dashboard__chart-card--side {
  grid-column: 2;
  grid-row: 1;
}

.admin-dashboard__chart-card--full {
  grid-column: 1 / -1;
  grid-row: 2;
}

.admin-dashboard__chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-dashboard__chart-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-dashboard__chart-icon {
  font-size: var(--admin-text-lg);
  color: var(--admin-primary);
}

.admin-dashboard__chart-controls {
  display: flex;
  gap: var(--admin-space-sm);
}

.admin-dashboard__period-select {
  background: var(--admin-white);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-sm) var(--admin-space-md);
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
}

.admin-dashboard__period-select:hover {
  border-color: var(--admin-primary);
}

.admin-dashboard__period-select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-bg);
}

.admin-dashboard__chart-content {
  position: relative;
  min-height: 300px;
  margin-bottom: var(--admin-space-lg);
}

.admin-dashboard__chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--admin-gray-500);
  gap: var(--admin-space-md);
}

.admin-dashboard__chart-loading i {
  font-size: 2rem;
  color: var(--admin-primary);
}

.admin-dashboard__chart-footer {
  border-top: 1px solid var(--admin-border-color);
  padding-top: var(--admin-space-lg);
}

.admin-dashboard__chart-stats {
  display: flex;
  justify-content: space-around;
  gap: var(--admin-space-lg);
}

.admin-dashboard__stat-item {
  text-align: center;
  flex: 1;
}

.admin-dashboard__stat-label {
  display: block;
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-xs);
  font-weight: var(--admin-font-medium);
}

.admin-dashboard__stat-value {
  display: block;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
}

.admin-dashboard__stat-value--positive {
  color: var(--admin-success);
}

.admin-dashboard__stat-value--negative {
  color: var(--admin-danger);
}

.admin-dashboard__stat-value--neutral {
  color: var(--admin-gray-600);
}

/* ===== ORDER STATUS LEGEND ===== */
.admin-dashboard__status-legend {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-dashboard__legend-item {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  transition: background-color var(--admin-transition-normal);
}

.admin-dashboard__legend-item:hover {
  background: var(--admin-gray-50);
}

.admin-dashboard__legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--admin-radius-sm);
  flex-shrink: 0;
}

.admin-dashboard__legend-label {
  flex: 1;
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-700);
  font-weight: var(--admin-font-medium);
}

.admin-dashboard__legend-count {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
  background: var(--admin-gray-100);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  min-width: 24px;
  text-align: center;
}

/* ===== PROFIT BREAKDOWN ===== */
.admin-dashboard__profit-breakdown {
  display: flex;
  justify-content: space-around;
  gap: var(--admin-space-lg);
}

.admin-dashboard__breakdown-item {
  text-align: center;
  flex: 1;
}

.admin-dashboard__breakdown-label {
  display: block;
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-xs);
  font-weight: var(--admin-font-medium);
}

.admin-dashboard__breakdown-value {
  display: block;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
}

.admin-dashboard .columns {
  margin-bottom: var(--admin-space-2xl);
}

.admin-chart-container {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  height: 100%;
}

.admin-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-chart-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

.admin-chart-controls {
  display: flex;
  gap: var(--admin-space-sm);
}

.admin-chart-body {
  position: relative;
  height: 300px;
}

/* ===== QUICK ACTIONS SECTION ===== */
.admin-dashboard__quick-actions-section {
  margin-bottom: var(--admin-space-2xl);
}

.admin-dashboard__quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--admin-space-lg);
}

.admin-dashboard__quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-md);
  padding: var(--admin-space-2xl);
  text-decoration: none;
  border-radius: var(--admin-radius-xl);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
  min-height: 140px;
  position: relative;
  overflow: hidden;
  background: var(--admin-white);
  border: 2px solid transparent;
  box-shadow: var(--admin-shadow-md);
}

.admin-dashboard__quick-action-btn:hover {
  transform: translateY(-4px);
  box-shadow: var(--admin-shadow-xl);
}

.admin-dashboard__quick-action-btn--primary {
  border-color: var(--admin-primary);
  background: linear-gradient(135deg, var(--admin-primary-bg) 0%, var(--admin-white) 100%);
}

.admin-dashboard__quick-action-btn--primary:hover {
  background: linear-gradient(135deg, var(--admin-primary-light) 0%, var(--admin-primary-bg) 100%);
}

.admin-dashboard__quick-action-btn--info {
  border-color: var(--admin-info);
  background: linear-gradient(135deg, var(--admin-info-bg) 0%, var(--admin-white) 100%);
}

.admin-dashboard__quick-action-btn--info:hover {
  background: linear-gradient(135deg, var(--admin-info-light) 0%, var(--admin-info-bg) 100%);
}

.admin-dashboard__quick-action-btn--success {
  border-color: var(--admin-success);
  background: linear-gradient(135deg, var(--admin-success-bg) 0%, var(--admin-white) 100%);
}

.admin-dashboard__quick-action-btn--success:hover {
  background: linear-gradient(135deg, var(--admin-success-light) 0%, var(--admin-success-bg) 100%);
}

.admin-dashboard__quick-action-btn--warning {
  border-color: var(--admin-warning);
  background: linear-gradient(135deg, var(--admin-warning-bg) 0%, var(--admin-white) 100%);
}

.admin-dashboard__quick-action-btn--warning:hover {
  background: linear-gradient(135deg, var(--admin-warning-light) 0%, var(--admin-warning-bg) 100%);
}

.admin-dashboard__quick-action-btn--secondary {
  border-color: var(--admin-secondary);
  background: linear-gradient(135deg, var(--admin-secondary-bg) 0%, var(--admin-white) 100%);
}

.admin-dashboard__quick-action-btn--secondary:hover {
  background: linear-gradient(135deg, var(--admin-secondary-light) 0%, var(--admin-secondary-bg) 100%);
}

.admin-dashboard__quick-action-btn--dark {
  border-color: var(--admin-gray-700);
  background: linear-gradient(135deg, var(--admin-gray-100) 0%, var(--admin-white) 100%);
}

.admin-dashboard__quick-action-btn--dark:hover {
  background: linear-gradient(135deg, var(--admin-gray-200) 0%, var(--admin-gray-100) 100%);
}

.admin-dashboard__quick-action-icon {
  font-size: 2.5rem;
  margin-bottom: var(--admin-space-sm);
}

.admin-dashboard__quick-action-btn--primary .admin-dashboard__quick-action-icon {
  color: var(--admin-primary);
}

.admin-dashboard__quick-action-btn--info .admin-dashboard__quick-action-icon {
  color: var(--admin-info);
}

.admin-dashboard__quick-action-btn--success .admin-dashboard__quick-action-icon {
  color: var(--admin-success);
}

.admin-dashboard__quick-action-btn--warning .admin-dashboard__quick-action-icon {
  color: var(--admin-warning);
}

.admin-dashboard__quick-action-btn--secondary .admin-dashboard__quick-action-icon {
  color: var(--admin-secondary);
}

.admin-dashboard__quick-action-btn--dark .admin-dashboard__quick-action-icon {
  color: var(--admin-gray-700);
}

.admin-dashboard__quick-action-text {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  text-align: center;
  margin-bottom: var(--admin-space-xs);
}

.admin-dashboard__quick-action-description {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  text-align: center;
  line-height: var(--admin-leading-relaxed);
}

.admin-dashboard__quick-action-badge {
  position: absolute;
  top: var(--admin-space-md);
  right: var(--admin-space-md);
  background: var(--admin-danger);
  color: white;
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-bold);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  min-width: 20px;
  text-align: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* ===== LEGACY QUICK ACTIONS SUPPORT ===== */
.admin-quick-actions {
  margin-bottom: var(--admin-space-xl);
}

.admin-quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-space-lg);
  padding: var(--admin-space-md);
}

.admin-quick-action {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-md);
  padding: var(--admin-space-lg) var(--admin-space-xl);
  text-decoration: none;
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
  min-height: 60px;
  position: relative;
  overflow: hidden;
}

.admin-quick-action:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
}

.admin-quick-action i {
  font-size: var(--admin-text-lg);
}

.admin-quick-action span {
  font-size: var(--admin-text-base);
}

/* Legacy Quick Actions Support */
.quick-actions-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  overflow: hidden;
}

.quick-actions-card .card-header {
  background: var(--admin-gray-50);
  border-bottom: 1px solid var(--admin-border-color);
  padding: var(--admin-space-lg) var(--admin-space-2xl);
}

.quick-actions-card .card-header-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
}

.quick-actions-card .card-content {
  padding: var(--admin-space-2xl);
}

.quick-actions-card .buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-lg);
  justify-content: center;
}

.quick-actions-card .button {
  padding: var(--admin-space-lg) var(--admin-space-2xl);
  border-radius: var(--admin-radius-lg);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
  border: 1px solid transparent;
}

.quick-actions-card .button.is-info {
  background: var(--admin-info);
  color: white;
  border-color: var(--admin-info);
}

.quick-actions-card .button.is-info:hover {
  background: var(--admin-info-dark);
  border-color: var(--admin-info-dark);
  transform: translateY(-1px);
}

.quick-actions-card .button.is-success {
  background: var(--admin-success);
  color: white;
  border-color: var(--admin-success);
}

.quick-actions-card .button.is-success:hover {
  background: var(--admin-success-dark);
  border-color: var(--admin-success-dark);
  transform: translateY(-1px);
}

.quick-actions-card .button.is-warning {
  background: var(--admin-warning);
  color: white;
  border-color: var(--admin-warning);
}

.quick-actions-card .button.is-warning:hover {
  background: var(--admin-warning-dark);
  border-color: var(--admin-warning-dark);
  transform: translateY(-1px);
}

.quick-actions-card .button.is-primary {
  background: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

.quick-actions-card .button.is-primary:hover {
  background: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  transform: translateY(-1px);
}

/* ===== ACTIVITY SECTION ===== */
.admin-dashboard__activity-section {
  margin-bottom: var(--admin-space-2xl);
}

.admin-dashboard__activity-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--admin-space-lg);
}

.admin-dashboard__activity-card {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  transition: all var(--admin-transition-normal);
}

.admin-dashboard__activity-card:hover {
  box-shadow: var(--admin-shadow-lg);
}

.admin-dashboard__activity-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--admin-gray-500);
  gap: var(--admin-space-md);
}

.admin-dashboard__activity-loading i {
  font-size: 2rem;
  color: var(--admin-primary);
}

/* ===== RECENT ORDERS ===== */
.admin-recent-orders {
  height: 100%;
}

.admin-recent-orders__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-recent-orders__title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-recent-orders__icon {
  font-size: var(--admin-text-lg);
  color: var(--admin-primary);
}

.admin-recent-orders__view-all {
  background: var(--admin-primary);
  color: white;
  text-decoration: none;
  padding: var(--admin-space-sm) var(--admin-space-lg);
  border-radius: var(--admin-radius-lg);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  transition: all var(--admin-transition-normal);
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-recent-orders__view-all:hover {
  background: var(--admin-primary-dark);
  transform: translateY(-1px);
}

.admin-recent-orders__list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-recent-orders__item {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
  padding: var(--admin-space-lg);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-lg);
  transition: all var(--admin-transition-normal);
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  user-select: none;
}

.admin-recent-orders__item:hover {
  border-color: var(--admin-primary);
  background: var(--admin-primary-bg);
  transform: translateX(4px);
}

.admin-recent-orders__avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--admin-radius-full);
  background: var(--admin-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--admin-font-bold);
  font-size: var(--admin-text-sm);
  flex-shrink: 0;
}

.admin-recent-orders__content {
  flex: 1;
  min-width: 0;
}

.admin-recent-orders__order-id {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  margin-bottom: var(--admin-space-xs);
  font-family: var(--admin-font-mono);
}

.admin-recent-orders__customer {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-900);
  margin-bottom: var(--admin-space-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-recent-orders__date {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
}

.admin-recent-orders__meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--admin-space-xs);
  flex-shrink: 0;
}

.admin-recent-orders__total {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-bold);
  color: var(--admin-gray-900);
}

.admin-recent-orders__status {
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-recent-orders__status--delivered {
  background: var(--admin-success-bg);
  color: var(--admin-success);
}

.admin-recent-orders__status--processing {
  background: var(--admin-info-bg);
  color: var(--admin-info);
}

.admin-recent-orders__status--pending {
  background: var(--admin-warning-bg);
  color: var(--admin-warning);
}

.admin-recent-orders__status--cancelled {
  background: var(--admin-danger-bg);
  color: var(--admin-danger);
}

.admin-recent-orders__status--shipped {
  background: var(--admin-primary-bg);
  color: var(--admin-primary);
}

.admin-recent-orders__empty {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-recent-orders__empty i {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-lg);
}

.admin-recent-orders__empty-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-700);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-recent-orders__empty-text {
  font-size: var(--admin-text-base);
  color: var(--admin-gray-500);
  margin: 0;
}

/* ===== LEGACY RECENT ACTIVITY SUPPORT ===== */
.admin-recent-activity {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  height: 100%;
}

.admin-recent-activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-recent-activity-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  margin: 0;
}

/* ===== LOADING STATES ===== */
.admin-dashboard .admin-loading {
  text-align: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-dashboard .admin-loading i {
  font-size: 3rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

.admin-dashboard .admin-loading-text {
  font-size: var(--admin-text-lg);
  margin-bottom: var(--admin-space-sm);
}

.admin-dashboard .admin-loading-subtext {
  color: var(--admin-gray-400);
  font-size: var(--admin-text-sm);
}

/* ===== ERROR STATES ===== */
.admin-dashboard .admin-error {
  background: var(--admin-danger-bg);
  border: 1px solid var(--admin-danger-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  color: var(--admin-danger-dark);
  margin-bottom: var(--admin-space-xl);
}

.admin-dashboard .admin-warning {
  background: var(--admin-warning-bg);
  border: 1px solid var(--admin-warning-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
  color: var(--admin-warning-dark);
  margin-bottom: var(--admin-space-xl);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .admin-dashboard__charts-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .admin-dashboard__chart-card--main,
  .admin-dashboard__chart-card--side,
  .admin-dashboard__chart-card--full {
    grid-column: 1;
  }

  .admin-dashboard__chart-card--main {
    grid-row: 1;
  }

  .admin-dashboard__chart-card--side {
    grid-row: 2;
  }

  .admin-dashboard__chart-card--full {
    grid-row: 3;
  }
}

@media (min-width: 1400px) {
  .admin-dashboard__activity-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 1024px) {
  .admin-dashboard__kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .admin-dashboard__quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .admin-dashboard__activity-grid {
    grid-template-columns: 1fr;
  }

  .admin-dashboard .column.is-one-fifth {
    flex: none;
    width: 50%;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    padding: var(--admin-space-lg);
  }

  .admin-dashboard__header {
    padding: var(--admin-space-2xl);
  }

  .admin-dashboard__header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-lg);
  }

  .admin-dashboard__title {
    font-size: var(--admin-text-2xl);
  }

  .admin-dashboard__kpi-grid {
    grid-template-columns: 1fr;
  }

  .admin-dashboard__quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .admin-dashboard__quick-action-btn {
    min-height: 120px;
    padding: var(--admin-space-lg);
  }

  .admin-dashboard__quick-action-icon {
    font-size: 2rem;
  }

  .admin-dashboard__chart-content {
    min-height: 250px;
  }

  .admin-dashboard__chart-stats,
  .admin-dashboard__profit-breakdown {
    flex-direction: column;
    gap: var(--admin-space-md);
  }

  .admin-dashboard__stat-item,
  .admin-dashboard__breakdown-item {
    text-align: left;
    padding: var(--admin-space-md);
    background: var(--admin-gray-50);
    border-radius: var(--admin-radius-md);
  }

  .admin-dashboard .admin-page-header {
    padding: var(--admin-space-2xl);
  }

  .admin-dashboard .admin-page-title {
    font-size: var(--admin-text-2xl);
  }

  .admin-dashboard .column.is-one-fifth {
    width: 100%;
  }

  .admin-chart-body {
    height: 250px;
  }

  .admin-quick-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--admin-space-md);
  }

  .admin-quick-action {
    padding: var(--admin-space-md) var(--admin-space-lg);
    min-height: 50px;
  }

  .quick-actions-card .buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .quick-actions-card .button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .admin-dashboard {
    padding: var(--admin-space-md);
  }

  .admin-dashboard__header {
    padding: var(--admin-space-lg);
  }

  .admin-dashboard__title {
    font-size: var(--admin-text-xl);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-sm);
  }

  .admin-dashboard__kpi-card,
  .admin-dashboard__chart-card,
  .admin-dashboard__activity-card {
    padding: var(--admin-space-lg);
  }

  .admin-dashboard__kpi-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-md);
  }

  .admin-dashboard__chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-md);
  }

  .admin-recent-orders__item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-sm);
  }

  .admin-recent-orders__meta {
    align-items: flex-start;
    width: 100%;
  }
}
