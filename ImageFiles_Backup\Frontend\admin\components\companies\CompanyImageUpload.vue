<template>
  <div class="company-image-upload">
    <div class="image-preview-section">
      <label class="admin-form-label">{{ label }}</label>

      <!-- Image Preview -->
      <div class="image-preview-container">
        <div v-if="displayImageUrl" class="image-preview">
          <img
            :src="displayImageUrl"
            :alt="label"
            @error="handleImageError"
            class="preview-image"
          />
          <div class="image-overlay">
            <button
              type="button"
              class="admin-btn admin-btn-sm admin-btn-danger"
              @click="removeImage"
              title="Remove image">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Upload Area -->
        <div v-else class="upload-area" @click="triggerFileInput">
          <div class="upload-content">
            <i class="fas fa-cloud-upload-alt upload-icon"></i>
            <p class="upload-text">Click to upload {{ label.toLowerCase() }}</p>
            <p class="upload-hint">JPG, PNG, GIF up to 5MB</p>
          </div>
        </div>
      </div>

      <!-- File Input -->
      <input
        ref="fileInput"
        type="file"
        accept="image/*,.jfif"
        @change="handleFileSelect"
        style="display: none;"
      />

      <!-- Upload Button -->
      <div v-if="!displayImageUrl" class="upload-button-section">
        <button
          type="button"
          class="admin-btn admin-btn-secondary admin-btn-sm"
          @click="triggerFileInput"
          :disabled="uploading">
          <i class="fas fa-upload" :class="{ 'fa-spin': uploading }"></i>
          <span>{{ uploading ? 'Uploading...' : 'Choose Image' }}</span>
        </button>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="admin-alert admin-alert-danger">
        <i class="fas fa-exclamation-circle"></i>
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: 'Image'
  },
  companyId: {
    type: String,
    default: null
  },
  imageType: {
    type: String,
    default: 'image', // 'image' or 'meta-image'
    validator: (value) => ['image', 'meta-image'].includes(value)
  }
});

const emit = defineEmits(['update:modelValue', 'image-uploaded', 'image-removed']);

// Reactive data
const fileInput = ref(null);
const uploading = ref(false);
const error = ref(null);
const pendingFile = ref(null);

// Computed
const displayImageUrl = computed(() => {
  // Don't show images from example.com (fake data)
  if (props.modelValue && !props.modelValue.includes('example.com')) {
    return props.modelValue;
  }
  return null;
});

// Watch for changes in modelValue
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    pendingFile.value = null;
  }
});

// Methods
const triggerFileInput = () => {
  if (fileInput.value && !uploading.value) {
    fileInput.value.click();
  }
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  error.value = null;

  // Validate file type
  if (!file.type.startsWith('image/')) {
    error.value = 'Please select a valid image file';
    return;
  }

  // Validate file size (5MB)
  if (file.size > 5 * 1024 * 1024) {
    error.value = 'Image size must be less than 5MB';
    return;
  }

  // Store the file for later upload
  pendingFile.value = file;

  // Create preview URL
  const reader = new FileReader();
  reader.onload = (e) => {
    emit('update:modelValue', e.target.result);
    emit('image-uploaded', {
      file: file,
      preview: e.target.result,
      isPending: true
    });
  };
  reader.readAsDataURL(file);

  // Clear the input
  event.target.value = '';
};

const removeImage = async () => {
  try {
    uploading.value = true;
    error.value = null;

    // Emit removal event with current image URL for deletion
    emit('image-removed', {
      imageUrl: props.modelValue,
      imageType: props.imageType
    });

    // Clear the display
    emit('update:modelValue', '');
    pendingFile.value = null;
  } catch (err) {
    error.value = err.message || 'Failed to remove image';
  } finally {
    uploading.value = false;
  }
};

const handleImageError = (event) => {
  // Hide broken images
  event.target.style.display = 'none';
  error.value = 'Failed to load image';
};

// Expose methods for parent component
defineExpose({
  getPendingFile: () => pendingFile.value,
  clearPendingFile: () => {
    pendingFile.value = null;
  }
});
</script>

<style scoped>
.company-image-upload {
  margin-bottom: 1rem;
}

.image-preview-container {
  position: relative;
  width: 200px;
  height: 150px;
  border: 2px dashed var(--admin-border-light);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  background: var(--admin-bg-tertiary);
}

.upload-area:hover {
  background: var(--admin-bg-secondary);
}

.upload-content {
  text-align: center;
  color: var(--admin-text-muted);
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.upload-text {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  color: var(--admin-text-secondary);
}

.upload-hint {
  font-size: 0.75rem;
  color: var(--admin-text-light);
}

.upload-button-section {
  margin-top: 0.5rem;
}

.admin-alert {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-alert-danger {
  background: rgba(241, 70, 104, 0.1);
  color: var(--admin-danger);
  border: 1px solid rgba(241, 70, 104, 0.2);
}
</style>
