<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Layout Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Include our admin layout fix styles */
        /* ===== ADMIN LAYOUT OVERRIDE ===== */
        .admin-layout {
            min-height: 100vh !important;
            background-color: #f8f9fa !important;
        }

        .admin-layout .columns {
            display: flex !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
            flex-wrap: nowrap !important;
        }

        .admin-layout .column {
            padding: 0 !important;
            margin: 0 !important;
        }

        .admin-layout .column.is-one-quarter {
            flex: none !important;
            width: 320px !important;
            min-width: 320px !important;
            max-width: 320px !important;
            position: relative !important;
        }

        .admin-layout .column.is-three-quarters {
            flex: 1 !important;
            width: calc(100% - 320px) !important;
            min-height: 100vh !important;
            display: flex !important;
            flex-direction: column !important;
        }

        /* Sidebar */
        .sidebar-column {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%) !important;
            position: relative !important;
            z-index: 1000 !important;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
        }

        .sidebar-container {
            height: 100vh !important;
            min-height: 100vh !important;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%) !important;
            padding: 1rem !important;
            position: relative !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
            overflow-y: auto !important;
        }

        .sidebar-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-bottom: 2rem !important;
            padding-bottom: 1rem !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .logo-container {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        .logo-container .title {
            color: white !important;
            margin: 0 !important;
            font-size: 1.25rem !important;
            font-weight: 600 !important;
        }

        .sidebar .panel-block {
            background: transparent !important;
            border: none !important;
            color: #ecf0f1 !important;
            padding: 0.75rem 1rem !important;
            margin-bottom: 0.25rem !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
            text-decoration: none !important;
            cursor: pointer !important;
            border-left: 3px solid transparent !important;
        }

        .sidebar .panel-block:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            color: white !important;
            transform: translateX(5px) !important;
            border-left-color: rgba(255, 255, 255, 0.3) !important;
        }

        .sidebar .panel-block.is-active {
            background: #3498db !important;
            color: white !important;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3) !important;
            border-left-color: #2980b9 !important;
        }

        .sidebar .panel-icon {
            margin-right: 0.75rem !important;
            width: 20px !important;
            text-align: center !important;
            font-size: 1rem !important;
        }

        .sidebar .menu-label {
            color: #bdc3c7 !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            font-size: 0.75rem !important;
            letter-spacing: 0.1em !important;
            margin-top: 1.5rem !important;
            margin-bottom: 0.5rem !important;
            padding-left: 1rem !important;
        }

        .sidebar .menu-label:first-of-type {
            margin-top: 0 !important;
        }

        /* Main Content */
        .admin-app .main-content,
        .admin-layout .main-content {
            display: flex !important;
            flex-direction: column !important;
            background-color: #f8f9fa !important;
            min-height: 100vh !important;
            position: relative !important;
            width: 100% !important;
        }

        /* Header */
        .admin-header {
            background: white !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            padding: 0 !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 999 !important;
            width: 100% !important;
            flex-shrink: 0 !important;
        }

        .admin-header .navbar {
            padding: 0.5rem 1.5rem !important;
            min-height: 60px !important;
            margin: 0 !important;
            background: transparent !important;
            box-shadow: none !important;
        }

        .admin-header .navbar-brand .title {
            color: #2c3e50 !important;
            margin: 0 !important;
            font-size: 1.5rem !important;
            font-weight: 600 !important;
        }

        /* Page Container */
        .admin-page-container {
            padding: 2rem !important;
            max-width: none !important;
            flex: 1 !important;
            background: #f8f9fa !important;
            width: 100% !important;
        }

        /* Test content */
        .test-content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="columns is-gapless">
            <!-- Sidebar -->
            <div class="column is-one-quarter sidebar-column">
                <div class="sidebar-container">
                    <div class="sidebar-header">
                        <div class="logo-container">
                            <h2 class="title is-5">Klondike Admin</h2>
                        </div>
                    </div>

                    <nav class="panel sidebar">
                        <p class="menu-label">General</p>
                        <div class="menu-list">
                            <a class="panel-block is-active">
                                <span class="panel-icon"><i class="fas fa-tachometer-alt"></i></span> Dashboard
                            </a>
                        </div>

                        <p class="menu-label">Users</p>
                        <div class="menu-list">
                            <a class="panel-block">
                                <span class="panel-icon"><i class="fas fa-users"></i></span> Users
                            </a>
                            <a class="panel-block">
                                <span class="panel-icon"><i class="fas fa-user-plus"></i></span> Seller Applications
                            </a>
                            <a class="panel-block">
                                <span class="panel-icon"><i class="fas fa-building"></i></span> Companies
                            </a>
                        </div>

                        <p class="menu-label">Catalog</p>
                        <div class="menu-list">
                            <a class="panel-block">
                                <span class="panel-icon"><i class="fas fa-box"></i></span> Products
                            </a>
                            <a class="panel-block">
                                <span class="panel-icon"><i class="fas fa-tags"></i></span> Categories
                            </a>
                        </div>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="column is-three-quarters main-content">
                <header class="admin-header">
                    <nav class="navbar" role="navigation">
                        <div class="navbar-brand">
                            <h1 class="navbar-item title is-4">Klondike Admin</h1>
                        </div>
                    </nav>
                </header>

                <section class="section">
                    <div class="container is-fluid admin-page-container">
                        <div class="test-content">
                            <h1>Dashboard Test</h1>
                            <p>Це тестова сторінка для перевірки макету адмін-панелі.</p>
                            <p>Сайдбар повинен бути зліва з фіксованою шириною 250px, а основний контент повинен займати решту простору.</p>
                            
                            <h2>Тестові метрики</h2>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                                <div style="background: white; padding: 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #3498db;">
                                    <h3 style="margin: 0 0 0.5rem 0; color: #2c3e50; font-size: 0.875rem;">Total Users</h3>
                                    <div style="font-size: 2rem; font-weight: 700; color: #3498db;">1,234</div>
                                </div>
                                <div style="background: white; padding: 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #27ae60;">
                                    <h3 style="margin: 0 0 0.5rem 0; color: #2c3e50; font-size: 0.875rem;">Total Orders</h3>
                                    <div style="font-size: 2rem; font-weight: 700; color: #27ae60;">567</div>
                                </div>
                                <div style="background: white; padding: 1rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #e74c3c;">
                                    <h3 style="margin: 0 0 0.5rem 0; color: #2c3e50; font-size: 0.875rem;">Total Products</h3>
                                    <div style="font-size: 2rem; font-weight: 700; color: #e74c3c;">89</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</body>
</html>
