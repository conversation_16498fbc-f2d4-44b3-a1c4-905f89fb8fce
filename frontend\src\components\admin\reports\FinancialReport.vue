<template>
  <div class="financial-report">
    <!-- Financial Metrics Grid -->
    <div class="metrics-section">
      <h3 class="section-title">
        <i class="fas fa-chart-line"></i>
        Financial Performance
      </h3>

      <div class="metrics-grid">
        <div
          v-for="metric in financialMetrics"
          :key="metric.key"
          class="metric-card"
          :class="metric.trend"
        >
          <div class="metric-header">
            <div class="metric-icon">
              <i :class="metric.icon"></i>
            </div>
            <div class="metric-change" v-if="metric.changePercentage">
              <i :class="getChangeIcon(metric.changePercentage)"></i>
              <span>{{ Math.abs(metric.changePercentage).toFixed(1) }}%</span>
            </div>
          </div>

          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-comparison" v-if="metric.previousValue">
              vs {{ formatMetricValue(metric.previousValue, metric.type) }} last period
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Revenue Trend Chart -->
    <div class="chart-section">
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="chart-title">Revenue Trend</h3>
          <div class="chart-controls">
            <select v-model="chartPeriod" @change="updateChart" class="chart-select">
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>

        <div class="chart-content">
          <canvas ref="revenueChartCanvas" id="revenue-chart"></canvas>
        </div>
      </div>
    </div>

    <!-- Section Divider -->
    <div class="section-divider"></div>

    <!-- Financial Insights -->
    <div class="insights-section">
      <h3 class="section-title">
        <i class="fas fa-lightbulb"></i>
        Financial Insights
      </h3>

      <div class="insights-grid">
        <div
          v-for="insight in financialInsights"
          :key="insight.id"
          class="insight-card"
          :class="insight.type"
        >
          <div class="insight-icon">
            <i :class="insight.icon"></i>
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
          </div>
          <div class="insight-priority">
            <div class="priority-indicator" :class="`priority-${insight.priority}`">
              {{ insight.priority }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Commission Breakdown -->
    <div class="commission-section">
      <h3 class="section-title">
        <i class="fas fa-percentage"></i>
        Commission Breakdown
      </h3>

      <div class="commission-grid">
        <div class="commission-card">
          <div class="commission-header">
            <h4>Platform Commission (15%)</h4>
          </div>
          <div class="commission-content">
            <div class="commission-amount">
              {{ formatCurrency(totalCommission) }}
            </div>
            <div class="commission-details">
              <div class="detail-item">
                <span class="detail-label">From Sales:</span>
                <span class="detail-value">{{ formatCurrency(salesCommission) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Processing Fees:</span>
                <span class="detail-value">{{ formatCurrency(processingFees) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="commission-card">
          <div class="commission-header">
            <h4>Seller Earnings</h4>
          </div>
          <div class="commission-content">
            <div class="commission-amount">
              {{ formatCurrency(sellerEarnings) }}
            </div>
            <div class="commission-details">
              <div class="detail-item">
                <span class="detail-label">Net Revenue:</span>
                <span class="detail-value">{{ formatCurrency(netRevenue) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">After Commission:</span>
                <span class="detail-value">{{ formatCurrency(sellerEarnings) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction Summary Table -->
    <div class="transactions-section">
      <h3 class="section-title">
        <i class="fas fa-list"></i>
        Recent Transactions
      </h3>

      <div class="transactions-table">
        <div class="table-controls">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              v-model="transactionSearch"
              type="text"
              placeholder="Search transactions..."
              class="search-input"
            />
          </div>

          <div class="filter-controls">
            <select v-model="statusFilter" class="filter-select">
              <option value="">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="refunded">Refunded</option>
            </select>

            <select v-model="paymentMethodFilter" class="filter-select">
              <option value="">All Payment Methods</option>
              <option value="card">Credit Card</option>
              <option value="paypal">PayPal</option>
              <option value="bank">Bank Transfer</option>
            </select>
          </div>
        </div>

        <div class="table-container">
          <table class="transactions-table-element">
            <thead>
              <tr>
                <th @click="sortBy('date')" class="sortable">
                  Date
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('orderNumber')" class="sortable">
                  Order #
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('amount')" class="sortable">
                  Amount
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th @click="sortBy('commission')" class="sortable">
                  Commission
                  <i class="fas fa-sort sort-icon"></i>
                </th>
                <th>Payment Method</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="transaction in paginatedTransactions" :key="transaction.id" class="transaction-row">
                <td>{{ formatDate(transaction.date) }}</td>
                <td>
                  <span class="order-number">{{ transaction.orderNumber }}</span>
                </td>
                <td>
                  <span class="amount">{{ formatCurrency(transaction.amount) }}</span>
                </td>
                <td>
                  <span class="commission">{{ formatCurrency(transaction.commission) }}</span>
                </td>
                <td>
                  <span class="payment-method">{{ transaction.paymentMethod }}</span>
                </td>
                <td>
                  <span class="status" :class="`status-${transaction.status}`">
                    {{ transaction.status }}
                  </span>
                </td>
                <td>
                  <div class="action-buttons">
                    <button @click="viewTransaction(transaction)" class="action-btn view-btn">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button @click="downloadReceipt(transaction)" class="action-btn download-btn">
                      <i class="fas fa-download"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="pagination" v-if="totalPages > 1">
          <button
            @click="currentPage = 1"
            :disabled="currentPage === 1"
            class="pagination-btn"
          >
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button
            @click="currentPage--"
            :disabled="currentPage === 1"
            class="pagination-btn"
          >
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>

          <button
            @click="currentPage++"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
          >
            <i class="fas fa-angle-right"></i>
          </button>
          <button
            @click="currentPage = totalPages"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
          >
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'FinancialReport',
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // Refs
    const revenueChartCanvas = ref(null)
    const chartPeriod = ref('daily')
    const transactionSearch = ref('')
    const statusFilter = ref('')
    const paymentMethodFilter = ref('')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const sortField = ref('date')
    const sortDirection = ref('desc')

    // Chart instances
    let revenueChart = null

    // Computed properties
    const financialMetrics = computed(() => {
      return props.data?.metrics?.items || []
    })

    const financialInsights = computed(() => {
      return props.data?.insights || []
    })

    const totalRevenue = computed(() => {
      const metric = financialMetrics.value.find(m => m.key === 'totalRevenue')
      return metric ? metric.value : 0
    })

    const totalCommission = computed(() => {
      const metric = financialMetrics.value.find(m => m.key === 'totalCommission')
      return metric ? metric.value : totalRevenue.value * 0.15
    })

    const salesCommission = computed(() => {
      return totalCommission.value * 0.9 // 90% from sales
    })

    const processingFees = computed(() => {
      return totalCommission.value * 0.1 // 10% processing fees
    })

    const netRevenue = computed(() => {
      return totalRevenue.value - (totalRevenue.value * 0.05) // Minus refunds/chargebacks
    })

    const sellerEarnings = computed(() => {
      return netRevenue.value - totalCommission.value
    })



    const transactions = computed(() => {
      return props.data?.table?.data || []
    })

    const filteredTransactions = computed(() => {
      let filtered = transactions.value

      // Apply search filter
      if (transactionSearch.value) {
        const query = transactionSearch.value.toLowerCase()
        filtered = filtered.filter(t =>
          t.orderNumber?.toLowerCase().includes(query) ||
          t.paymentMethod?.toLowerCase().includes(query)
        )
      }

      // Apply status filter
      if (statusFilter.value) {
        filtered = filtered.filter(t => t.status === statusFilter.value)
      }

      // Apply payment method filter
      if (paymentMethodFilter.value) {
        filtered = filtered.filter(t => t.paymentMethod === paymentMethodFilter.value)
      }

      // Apply sorting
      filtered.sort((a, b) => {
        const aVal = a[sortField.value]
        const bVal = b[sortField.value]

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return sortDirection.value === 'asc' ? aVal - bVal : bVal - aVal
        }

        const aStr = String(aVal).toLowerCase()
        const bStr = String(bVal).toLowerCase()

        if (sortDirection.value === 'asc') {
          return aStr.localeCompare(bStr)
        } else {
          return bStr.localeCompare(aStr)
        }
      })

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredTransactions.value.length / itemsPerPage.value)
    })

    const paginatedTransactions = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage.value
      const end = start + itemsPerPage.value
      return filteredTransactions.value.slice(start, end)
    })

    // Methods
    const formatMetricValue = (value, type) => {
      switch (type) {
        case 'currency':
          return reportsService.formatCurrency(value)
        case 'percentage':
          return reportsService.formatPercentage(value)
        case 'number':
          return reportsService.formatNumber(value)
        default:
          return String(value)
      }
    }

    const formatCurrency = (value) => {
      return reportsService.formatCurrency(value)
    }

    const formatDate = (value) => {
      return new Date(value).toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const getChangeIcon = (changePercentage) => {
      if (changePercentage > 0) return 'fas fa-arrow-up'
      if (changePercentage < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }



    const sortBy = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const viewTransaction = (transaction) => {
      // TODO: Implement transaction details modal
      console.log('View transaction:', transaction)
    }

    const downloadReceipt = (transaction) => {
      // TODO: Implement receipt download
      console.log('Download receipt for:', transaction)
    }

    const updateChart = () => {
      createRevenueChart()
    }

    const createRevenueChart = async () => {
      await nextTick()

      if (revenueChart) {
        revenueChart.destroy()
      }

      if (!revenueChartCanvas.value || !props.data?.charts?.primary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = revenueChartCanvas.value.getContext('2d')

        revenueChart = new Chart(ctx, {
          type: 'line',
          data: props.data.charts.primary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + formatCurrency(context.parsed.y)
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: 'Amount (UAH)'
                },
                ticks: {
                  callback: function(value) {
                    return formatCurrency(value)
                  }
                }
              }
            },
            interaction: {
              mode: 'nearest',
              axis: 'x',
              intersect: false
            }
          }
        })
      } catch (error) {
        console.error('Error creating revenue chart:', error)
      }
    }



    // Watchers
    watch(() => props.data, () => {
      nextTick(() => {
        createRevenueChart()
      })
    }, { deep: true })

    watch([transactionSearch, statusFilter, paymentMethodFilter], () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      nextTick(() => {
        createRevenueChart()
      })
    })

    onUnmounted(() => {
      if (revenueChart) {
        revenueChart.destroy()
      }
    })

    return {
      revenueChartCanvas,
      chartPeriod,
      transactionSearch,
      statusFilter,
      paymentMethodFilter,
      currentPage,
      itemsPerPage,
      financialMetrics,
      financialInsights,
      totalRevenue,
      totalCommission,
      salesCommission,
      processingFees,
      netRevenue,
      sellerEarnings,
      filteredTransactions,
      totalPages,
      paginatedTransactions,
      formatMetricValue,
      formatCurrency,
      formatDate,
      getChangeIcon,
      sortBy,
      viewTransaction,
      downloadReceipt,
      updateChart
    }
  }
}
</script>

<style scoped>
.financial-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Metrics Section */
.metrics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.metric-card.positive {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #f8fafc 100%);
}

.metric-card.negative {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #f8fafc 100%);
}

.metric-card.neutral {
  border-left: 4px solid #6b7280;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.positive .metric-change {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.negative .metric-change {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.neutral .metric-change {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-comparison {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Chart Section */
.chart-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #f9fafb;
  position: relative;
  z-index: 2;
}

/* Section Divider */
.section-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #d1d5db 50%, #e5e7eb 80%, transparent 100%);
  margin: 24px 0;
  border-radius: 1px;
}

.chart-container {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-title::before {
  content: "📊";
  font-size: 1.25rem;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.chart-content {
  height: 400px;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.chart-content canvas {
  max-width: 100%;
  max-height: 100%;
  display: block;
}

/* Distribution Section */
.distribution-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.distribution-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
  align-items: center;
}

.chart-wrapper {
  height: 300px;
  position: relative;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.legend-value {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Insights Section */
.insights-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-top: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-top: 3px solid #f3f4f6;
  clear: both;
  position: relative;
  z-index: 1;
}

.insights-section .section-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.insights-section .section-title i {
  color: #f59e0b;
  font-size: 1.25rem;
  margin-right: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-divider {
    margin: 16px 0;
  }

  .chart-section {
    margin-bottom: 12px;
    padding: 16px;
  }

  .insights-section {
    margin-top: 12px;
    padding: 16px;
  }

  .chart-title {
    font-size: 1.25rem;
  }

  .insights-section .section-title {
    font-size: 1.25rem;
    margin-bottom: 16px;
    padding-bottom: 8px;
  }
}

.insights-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #e5e7eb;
  background: #f9fafb;
  transition: all 0.2s;
  position: relative;
  z-index: 1;
}

.insight-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.insight-card.positive {
  background: linear-gradient(135deg, #f0fdf4 0%, #f9fafb 100%);
  border-left-color: #10b981;
}

.insight-card.negative {
  background: linear-gradient(135deg, #fef2f2 0%, #f9fafb 100%);
  border-left-color: #ef4444;
}

.insight-card.warning {
  background: linear-gradient(135deg, #fffbeb 0%, #f9fafb 100%);
  border-left-color: #f59e0b;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.positive .insight-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.negative .insight-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.warning .insight-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.insight-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.insight-priority {
  display: flex;
  align-items: center;
}

.priority-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.priority-1 { background: #9ca3af; }
.priority-2 { background: #6b7280; }
.priority-3 { background: #f59e0b; }
.priority-4 { background: #ef4444; }
.priority-5 { background: #dc2626; }

/* Commission Section */
.commission-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.commission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.commission-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 24px;
  transition: all 0.2s;
}

.commission-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.commission-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.commission-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16px;
  line-height: 1;
}

.commission-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.detail-value {
  font-weight: 600;
  color: #374151;
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .distribution-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .commission-grid {
    grid-template-columns: 1fr;
  }

  .chart-content {
    height: 300px;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  .commission-amount {
    font-size: 2rem;
  }
}

/* Transactions Section */
.transactions-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 300px;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.transactions-table-element {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.transactions-table-element th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  white-space: nowrap;
}

.transactions-table-element th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.transactions-table-element th.sortable:hover {
  background: #f3f4f6;
}

.sort-icon {
  margin-left: 8px;
  opacity: 0.5;
  font-size: 0.75rem;
}

.transactions-table-element td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.transaction-row:hover {
  background: #f9fafb;
}

.order-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #667eea;
  font-weight: 600;
}

.amount {
  font-weight: 600;
  color: #111827;
}

.commission {
  font-weight: 500;
  color: #10b981;
}

.payment-method {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-completed {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-pending {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-refunded {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.view-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.view-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.download-btn {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.download-btn:hover {
  background: rgba(16, 185, 129, 0.2);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 20px;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 16px;
}

@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
    min-width: auto;
  }

  .transactions-table-element {
    font-size: 0.875rem;
  }

  .transactions-table-element th,
  .transactions-table-element td {
    padding: 8px 12px;
  }
}
</style>

