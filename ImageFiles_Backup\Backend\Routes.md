# Публічні маршрути (не потребують автентифікації)
## Пошук
- GET /api/search?q={keyword}                     - Пошук продуктів, категорій, компаній [Усі]

## Категорії
- GET /api/categories                             - Список усіх кореневих категорій [Усі]
- GET /api/categories/{slug}                      - Отримати категорію за slug [Усі]
- GET /api/categories/{slug}/subcategories        - Отримати підкатегорії категорії [Усі]
- GET /api/categories/{slug}/products             - Отримати продукти в категорії [Усі]

## Компанії
- GET /api/companies                              - Список усіх активних схвалених компаній [Усі]
- GET /api/companies/{slug}                       - Отримати компанію за slug [Усі]
- GET /api/companies/{slug}/products              - Отримати продукти компанії [Усі]
- GET /api/companies/{slug}/schedule              - Отримати розклад компанії [Усі]

## Продукти
- GET /api/products                               - Список усіх схвалених продуктів [Усі]
- GET /api/products/{slug}                        - Отримати продукт за slug [Усі]
- GET /api/products/{slug}/images                 - Отримати зображення продукту [Усі]
- GET /api/products/{slug}/reviews                - Отримати відгуки продукту [Усі]
- GET /api/products/{slug}/ratings                - Отримати рейтинги продукту [Усі]
- GET /api/products/{slug}/attributes             - Отримати атрибути продукту [Усі]

## Способи доставки
- GET /api/shipping-methods                       - Список усіх способів доставки [Усі]

## Користувачі
- GET /api/users/{id}/public                      - Отримати публічний профіль користувача (наприклад, дані продавця) [Усі]

# Маршрути користувача (автентифікований, за замовчуванням Buyer)
## Профіль користувача
- GET /api/users/me                               - Отримати профіль користувача [Buyer, Seller, SellerOwner]
- PUT /api/users/me                               - Оновити профіль користувача [Buyer, Seller, SellerOwner]
- PATCH /api/users/me                             - Частково оновити профіль [Buyer, Seller, SellerOwner]
- POST /api/users/me/confirm-email                - Підтвердити email [Buyer, Seller, SellerOwner]
- POST /api/users/me/reset-password               - Запит на скидання пароля [Buyer, Seller, SellerOwner]
- PUT /api/users/me/password                      - Оновити пароль [Buyer, Seller, SellerOwner]

## Адреси
- GET /api/users/me/addresses                     - Список адрес користувача [Buyer, Seller, SellerOwner]
- GET /api/users/me/addresses/{id}                - Отримати конкретну адресу [Buyer, Seller, SellerOwner]
- POST /api/users/me/addresses                    - Створити нову адресу [Buyer, Seller, SellerOwner]
- PUT /api/users/me/addresses/{id}                - Оновити адресу [Buyer, Seller, SellerOwner]
- DELETE /api/users/me/addresses/{id}             - Видалити адресу [Buyer, Seller, SellerOwner]

## Обрані адреси
- GET /api/users/me/favorites                     - Список обраних адрес [Buyer, Seller, SellerOwner]
- GET /api/users/me/favorites/{id}                - Отримати конкретну обрану адресу [Buyer, Seller, SellerOwner]
- POST /api/users/me/favorites                    - Додати обрану адресу [Buyer, Seller, SellerOwner]
- PUT /api/users/me/favorites/{id}                - Оновити обрану адресу [Buyer, Seller, SellerOwner]
- DELETE /api/users/me/favorites/{id}             - Видалити обрану адресу [Buyer, Seller, SellerOwner]

## Списки бажань
- GET /api/users/me/wishlists                     - Список списків бажань [Buyer, Seller, SellerOwner]
- GET /api/users/me/wishlists/{id}                - Отримати конкретний список бажань [Buyer, Seller, SellerOwner]
- POST /api/users/me/wishlists                    - Створити новий список бажань [Buyer, Seller, SellerOwner]
- PUT /api/users/me/wishlists/{id}                - Оновити список бажань [Buyer, Seller, SellerOwner]
- DELETE /api/users/me/wishlists/{id}             - Видалити список бажань [Buyer, Seller, SellerOwner]
- GET /api/users/me/wishlists/{id}/items          - Список елементів списку бажань [Buyer, Seller, SellerOwner]
- POST /api/users/me/wishlists/{id}/items         - Додати елемент до списку бажань [Buyer, Seller, SellerOwner]
- DELETE /api/users/me/wishlists/{id}/items/{itemId} - Видалити елемент зі списку бажань [Buyer, Seller, SellerOwner]

## Кошик
- GET /api/users/me/cart                          - Отримати поточний кошик [Buyer]
- POST /api/users/me/cart/items                   - Додати елемент до кошика [Buyer]
- PUT /api/users/me/cart/items/{itemId}           - Оновити елемент кошика (наприклад, кількість) [Buyer]
- DELETE /api/users/me/cart/items/{itemId}        - Видалити елемент з кошика [Buyer]
- DELETE /api/users/me/cart                       - Очистити кошик [Buyer]
- POST /api/users/me/cart/checkout                - Перетворити кошик на замовлення [Buyer]

## Замовлення
- GET /api/users/me/orders                        - Список замовлень користувача [Buyer, Seller, SellerOwner]
- GET /api/users/me/orders/{id}                   - Отримати конкретне замовлення [Buyer, Seller, SellerOwner]
- POST /api/users/me/orders                       - Створити нове замовлення [Buyer]
- PUT /api/users/me/orders/{id}/cancel            - Скасувати замовлення [Buyer]
- GET /api/users/me/orders/{id}/items             - Список елементів замовлення [Buyer, Seller, SellerOwner]
- GET /api/users/me/orders/{id}/coupons           - Список застосованих купонів [Buyer, Seller, SellerOwner]

## Платежі
- GET /api/users/me/payments                      - Список платежів користувача [Buyer, Seller, SellerOwner]
- GET /api/users/me/payments/{id}                 - Отримати конкретний платіж [Buyer, Seller, SellerOwner]
- POST /api/users/me/payments                     - Ініціювати платіж для замовлення [Buyer]

## Сповіщення
- GET /api/users/me/notifications                 - Список сповіщень користувача [Buyer, Seller, SellerOwner]
- GET /api/users/me/notifications/{id}            - Отримати конкретне сповіщення [Buyer, Seller, SellerOwner]
- PUT /api/users/me/notifications/{id}/read       - Позначити сповіщення як прочитане [Buyer, Seller, SellerOwner]
- PUT /api/users/me/notifications/read-all        - Позначити всі сповіщення як прочитані [Buyer, Seller, SellerOwner]

## Чати
- GET /api/users/me/chats                         - Список чатів користувача [Buyer, Seller, SellerOwner]
- GET /api/users/me/chats/{id}                    - Отримати конкретний чат [Buyer, Seller, SellerOwner]
- POST /api/users/me/chats                        - Створити новий чат [Buyer, Seller, SellerOwner]
- GET /api/users/me/chats/{id}/messages           - Список повідомлень чату [Buyer, Seller, SellerOwner]
- POST /api/users/me/chats/{id}/messages          - Надіслати повідомлення в чат [Buyer, Seller, SellerOwner]
- PUT /api/users/me/chats/{id}/messages/{messageId}/read - Позначити повідомлення як прочитане [Buyer, Seller, SellerOwner]

## Запити на роль продавця
- POST /api/users/me/seller-requests              - Подати запит на роль продавця [Buyer]
- GET /api/users/me/seller-requests               - Список запитів на роль продавця [Buyer, Seller, SellerOwner]
- GET /api/users/me/seller-requests/{id}          - Отримати конкретний запит [Buyer, Seller, SellerOwner]

## Відгуки та рейтинги
- GET /api/users/me/reviews                       - Отримати відгуки користувача [Buyer, Seller, SellerOwner]
- POST /api/users/me/reviews                      - Створити відгук для продукту [Buyer, Seller, SellerOwner]
- PUT /api/users/me/reviews/{id}                  - Оновити відгук [Buyer, Seller, SellerOwner]
- DELETE /api/users/me/reviews/{id}               - Видалити відгук [Buyer, Seller, SellerOwner]
- POST /api/users/me/ratings                      - Створити рейтинг для продукту [Buyer, Seller, SellerOwner]
- PUT /api/users/me/ratings/{id}                  - Оновити рейтинг [Buyer, Seller, SellerOwner]
- DELETE /api/users/me/ratings/{id}               - Видалити рейтинг [Buyer, Seller, SellerOwner]

## Купони
- POST /api/users/me/coupons/apply                - Застосувати купон до замовлення [Buyer]
- GET /api/users/me/coupons/validate?code={code}  - Перевірити валідність купона [Buyer]

# Маршрути продавця (автентифікований, Seller або SellerOwner)
## Управління компанією
- GET /api/sellers/me/company                     - Отримати дані компанії продавця [Seller, SellerOwner]
- PUT /api/sellers/me/company                     - Оновити ключові дані компанії (наприклад, назва, логотип) [SellerOwner] // Обмежено для Seller
- PATCH /api/sellers/me/company                   - Частково оновити дані компанії [SellerOwner] // Обмежено для Seller
- POST /api/sellers/me/company/deactivate         - Деактивувати компанію (зміна статусу на неактивний) [SellerOwner] // Змінено
- POST /api/sellers/me/company/activate           - Активувати компанію (відновлення статусу) [SellerOwner] // Новий
- GET /api/sellers/me/company/settings            - Отримати налаштування компанії (наприклад, контактні дані) [SellerOwner]
- PUT /api/sellers/me/company/settings            - Оновити налаштування компанії [SellerOwner]
- GET /api/sellers/me/company/schedule            - Отримати розклад компанії [Seller, SellerOwner]
- POST /api/sellers/me/company/schedule           - Створити запис у розкладі [SellerOwner] // Обмежено для Seller
- PUT /api/sellers/me/company/schedule/{scheduleId} - Оновити запис у розкладі [SellerOwner] // Обмежено для Seller
- DELETE /api/sellers/me/company/schedule/{scheduleId} - Видалити запис у розкладі [SellerOwner] // Обмежено для Seller
- POST /api/sellers/me/company/image              - Завантажити зображення компанії [SellerOwner] // Обмежено для Seller
- POST /api/sellers/me/company/meta-image         - Завантажити мета-зображення компанії [SellerOwner] // Обмежено для Seller

## Користувачі компанії
- GET /api/sellers/me/company/users               - Список користувачів компанії [SellerOwner]
- POST /api/sellers/me/company/users              - Додати користувача до компанії [SellerOwner]
- PUT /api/sellers/me/company/users/{userId}      - Оновити статус користувача (наприклад, _isOwner) [SellerOwner]
- DELETE /api/sellers/me/company/users/{userId}   - Видалити користувача з компанії [SellerOwner]

## Фінансові налаштування
- GET /api/sellers/me/company/finance             - Отримати фінансові налаштування компанії (наприклад, рахунок) [SellerOwner]
- PUT /api/sellers/me/company/finance             - Оновити фінансові налаштування [SellerOwner]

## Продукти
- GET /api/sellers/me/products                    - Список продуктів продавця [Seller, SellerOwner]
- POST /api/sellers/me/products                   - Створити новий продукт [Seller, SellerOwner]
- GET /api/sellers/me/products/{id}               - Отримати конкретний продукт [Seller, SellerOwner]
- PUT /api/sellers/me/products/{id}               - Оновити продукт [Seller, SellerOwner]
- PATCH /api/sellers/me/products/{id}             - Частково оновити продукт [Seller, SellerOwner]
- DELETE /api/sellers/me/products/{id}            - Видалити продукт [Seller, SellerOwner]
- GET /api/sellers/me/products/{id}/images        - Список зображень продукту [Seller, SellerOwner]
- POST /api/sellers/me/products/{id}/images       - Додати зображення продукту [Seller, SellerOwner]
- POST /api/sellers/me/products/{id}/images/upload - Завантажити зображення продукту [Seller, SellerOwner]
- PUT /api/sellers/me/products/{id}/images/{imageId} - Оновити зображення продукту [Seller, SellerOwner]
- DELETE /api/sellers/me/products/{id}/images/{imageId} - Видалити зображення продукту [Seller, SellerOwner]
- POST /api/sellers/me/products/{id}/meta-image   - Завантажити мета-зображення продукту [Seller, SellerOwner]
- GET /api/sellers/me/products/{id}/attributes    - Отримати атрибути продукту [Seller, SellerOwner]
- POST /api/sellers/me/products/{id}/attributes   - Додати атрибут продукту [Seller, SellerOwner]
- PUT /api/sellers/me/products/{id}/attributes/{key} - Оновити атрибут продукту [Seller, SellerOwner]
- DELETE /api/sellers/me/products/{id}/attributes/{key} - Видалити атрибут продукту [Seller, SellerOwner]

## Замовлення
- GET /api/sellers/me/orders                      - Список замовлень для продуктів продавця [Seller, SellerOwner]
- GET /api/sellers/me/orders/{id}                 - Отримати конкретне замовлення [Seller, SellerOwner]
- GET /api/sellers/me/orders/{id}/items           - Список елементів замовлення [Seller, SellerOwner]

## Аналітика
- GET /api/sellers/me/analytics/products          - Отримати статистику продуктів продавця [Seller, SellerOwner]
- GET /api/sellers/me/analytics/orders            - Отримати статистику замовлень продавця [Seller, SellerOwner]
- GET /api/sellers/me/analytics/company           - Отримати розширену статистику компанії (наприклад, прибуток) [SellerOwner]

# Маршрути модератора (автентифікований, Moderator)
## Продукти
- GET /api/moderators/products                    - Список усіх продуктів для модерації [Moderator]
- GET /api/moderators/products/{id}               - Отримати конкретний продукт [Moderator]
- POST /api/moderators/products/{id}/approve      - Схвалити продукт [Moderator]
- POST /api/moderators/products/{id}/reject       - Відхилити продукт [Moderator]
- POST /api/moderators/products/bulk-approve      - Масово схвалити продукти [Moderator]
- POST /api/moderators/products/bulk-reject       - Масово відхилити продукти [Moderator]

## Компанії
- GET /api/moderators/companies                   - Список усіх компаній для модерації [Moderator]
- GET /api/moderators/companies/{id}              - Отримати конкретну компанію [Moderator]
- POST /api/moderators/companies/{id}/approve     - Схвалити компанію [Moderator]
- POST /api/moderators/companies/{id}/reject      - Відхилити компанію [Moderator]
- POST /api/moderators/companies/bulk-approve     - Масово схвалити компанії [Moderator]
- POST /api/moderators/companies/bulk-reject      - Масово відхилити компанії [Moderator]

## Запити на роль продавця
- GET /api/moderators/seller-requests             - Список усіх запитів на роль продавця [Moderator]
- GET /api/moderators/seller-requests/{id}        - Отримати конкретний запит [Moderator]
- POST /api/moderators/seller-requests/{id}/approve - Схвалити запит [Moderator]
- POST /api/moderators/seller-requests/{id}/reject - Відхилити запит [Moderator]
- POST /api/moderators/seller-requests/bulk-approve - Масово схвалити запити [Moderator]
- POST /api/moderators/seller-requests/bulk-reject - Масово відхилити запити [Moderator]

## Відгуки
- GET /api/moderators/reviews                     - Список усіх відгуків для модерації [Moderator]
- GET /api/moderators/reviews/{id}                - Отримати конкретний відгук [Moderator]
- PUT /api/moderators/reviews/{id}                - Оновити відгук (наприклад, модерація) [Moderator]
- DELETE /api/moderators/reviews/{id}             - Видалити відгук [Moderator]
- POST /api/moderators/reviews/bulk-delete        - Масово видалити відгуки [Moderator]

## Рейтинги
- GET /api/moderators/ratings                     - Список усіх рейтингів для модерації [Moderator]
- GET /api/moderators/ratings/{id}                - Отримати конкретний рейтинг [Moderator]
- PUT /api/moderators/ratings/{id}                - Оновити рейтинг [Moderator]
- DELETE /api/moderators/ratings/{id}             - Видалити рейтинг [Moderator]
- POST /api/moderators/ratings/bulk-delete        - Масово видалити рейтинги [Moderator]

## Чати
- GET /api/moderators/chats                       - Список усіх чатів для модерації [Moderator]
- GET /api/moderators/chats/{id}                  - Отримати конкретний чат [Moderator]
- GET /api/moderators/chats/{id}/messages         - Список повідомлень чату [Moderator]
- PUT /api/moderators/chats/{id}/messages/{messageId} - Оновити повідомлення (наприклад, позначити) [Moderator]
- DELETE /api/moderators/chats/{id}/messages/{messageId} - Видалити повідомлення [Moderator]

## Користувачі
- GET /api/moderators/users                       - Список користувачів для модерації [Moderator]
- GET /api/moderators/users/{id}                  - Отримати конкретного користувача [Moderator]

# Маршрути адміністратора (автентифікований, Admin)
## Користувачі
- GET /api/admin/users                            - Список усіх користувачів [Admin]
- GET /api/admin/users/{id}                       - Отримати конкретного користувача [Admin]
- PUT /api/admin/users/{id}                       - Оновити користувача (наприклад, роль, схвалення) [Admin]
- PATCH /api/admin/users/{id}                     - Частково оновити користувача [Admin]
- DELETE /api/admin/users/{id}                    - Видалити користувача [Admin]
- POST /api/admin/users/{id}/approve              - Схвалити користувача [Admin]
- POST /api/admin/users/{id}/send-email-confirmation - Надіслати посилання для підтвердження email [Admin]
- POST /api/admin/users/bulk-approve              - Масово схвалити користувачів [Admin]
- POST /api/admin/users/bulk-delete               - Масово видалити користувачів [Admin]

## Категорії
- GET /api/admin/categories                       - Список усіх категорій [Admin]
- POST /api/admin/categories                      - Створити нову категорію [Admin]
- PUT /api/admin/categories/{id}                  - Оновити категорію [Admin]
- PATCH /api/admin/categories/{id}                - Частково оновити категорію [Admin]
- DELETE /api/admin/categories/{id}               - Видалити категорію [Admin]
- POST /api/admin/categories/{id}/image           - Завантажити зображення категорії [Admin]
- POST /api/admin/categories/{id}/meta-image      - Завантажити мета-зображення категорії [Admin]
- POST /api/admin/categories/bulk-delete          - Масово видалити категорії [Admin]

## Компанії
- GET /api/admin/companies                        - Список усіх компаній (включаючи несхвалені) [Admin]
- GET /api/admin/companies/{id}                   - Отримати конкретну компанію [Admin]
- POST /api/admin/companies                       - Створити нову компанію [Admin]
- PUT /api/admin/companies/{id}                   - Оновити компанію [Admin]
- PATCH /api/admin/companies/{id}                 - Частково оновити компанію [Admin]
- DELETE /api/admin/companies/{id}                - Видалити компанію [Admin]
- POST /api/admin/companies/{id}/approve          - Схвалити компанію [Admin]
- POST /api/admin/companies/{id}/feature          - Позначити компанію як обрану [Admin]
- POST /api/admin/companies/{id}/unfeature        - Зняти статус обраної компанії [Admin]
- POST /api/admin/companies/{id}/image            - Завантажити зображення компанії [Admin]
- POST /api/admin/companies/{id}/meta-image       - Завантажити мета-зображення компанії [Admin]
- GET /api/admin/companies/{id}/users             - Список користувачів компанії [Admin]
- POST /api/admin/companies/{id}/users            - Додати користувача до компанії [Admin]
- PUT /api/admin/companies/{id}/users/{userId}    - Оновити користувача компанії (наприклад, статус власника) [Admin]
- DELETE /api/admin/companies/{id}/users/{userId} - Видалити користувача з компанії [Admin]
- GET /api/admin/companies/{id}/schedule          - Отримати розклад компанії [Admin]
- POST /api/admin/companies/{id}/schedule         - Створити запис у розкладі [Admin]
- PUT /api/admin/companies/{id}/schedule/{scheduleId} - Оновити запис у розкладі [Admin]
- DELETE /api/admin/companies/{id}/schedule/{scheduleId} - Видалити запис у розкладі [Admin]
- POST /api/admin/companies/bulk-approve          - Масово схвалити компанії [Admin]
- POST /api/admin/companies/bulk-delete           - Масово видалити компанії [Admin]

## Продукти
- GET /api/admin/products                         - Список усіх продуктів (включаючи несхвалені) [Admin]
- GET /api/admin/products/{id}                    - Отримати конкретний продукт [Admin]
- POST /api/admin/products                        - Створити новий продукт [Admin]
- PUT /api/admin/products/{id}                    - Оновити продукт [Admin]
- PATCH /api/admin/products/{id}                  - Частково оновити продукт [Admin]
- DELETE /api/admin/products/{id}                 - Видалити продукт [Admin]
- POST /api/admin/products/{id}/approve           - Схвалити продукт [Admin]
- GET /api/admin/products/{id}/images             - Список зображень продукту [Admin]
- POST /api/admin/products/{id}/images            - Додати зображення продукту [Admin]
- POST /api/admin/products/{id}/images/upload     - Завантажити зображення продукту [Admin]
- PUT /api/admin/products/{id}/images/{imageId}   - Оновити зображення продукту [Admin]
- DELETE /api/admin/products/{id}/images/{imageId} - Видалити зображення продукту [Admin]
- POST /api/admin/products/{id}/meta-image        - Завантажити мета-зображення продукту [Admin]
- GET /api/admin/products/{id}/attributes         - Отримати атрибути продукту [Admin]
- POST /api/admin/products/{id}/attributes        - Додати атрибут продукту [Admin]
- PUT /api/admin/products/{id}/attributes/{key}   - Оновити атрибут продукту [Admin]
- DELETE /api/admin/products/{id}/attributes/{key} - Видалити атрибут продукту [Admin]
- POST /api/admin/products/bulk-approve           - Масово схвалити продукти [Admin]
- POST /api/admin/products/bulk-delete            - Масово видалити продукти [Admin]

## Замовлення
- GET /api/admin/orders                           - Список усіх замовлень [Admin]
- GET /api/admin/orders/{id}                      - Отримати конкретне замовлення [Admin]
- PUT /api/admin/orders/{id}                      - Оновити замовлення (наприклад, статус) [Admin]
- PATCH /api/admin/orders/{id}                    - Частково оновити замовлення [Admin]
- GET /api/admin/orders/{id}/items                - Список елементів замовлення [Admin]
- POST /api/admin/orders/{id}/items               - Додати елемент до замовлення [Admin]
- PUT /api/admin/orders/{id}/items/{itemId}       - Оновити елемент замовлення [Admin]
- DELETE /api/admin/orders/{id}/items/{itemId}    - Видалити елемент замовлення [Admin]
- GET /api/admin/orders/{id}/coupons              - Список застосованих купонів [Admin]
- POST /api/admin/orders/{id}/coupons             - Застосувати купон до замовлення [Admin]
- DELETE /api/admin/orders/{id}/coupons/{couponId} - Видалити купон із замовлення [Admin]

## Платежі
- GET /api/admin/payments                         - Список усіх платежів [Admin]
- GET /api/admin/payments/{id}                    - Отримати конкретний платіж [Admin]
- PUT /api/admin/payments/{id}                    - Оновити платіж (наприклад, статус) [Admin]
- PATCH /api/admin/payments/{id}                  - Частково оновити платіж [Admin]

## Купони
- GET /api/admin/coupons                          - Список усіх купонів [Admin]
- GET /api/admin/coupons/{id}                     - Отримати конкретний купон [Admin]
- POST /api/admin/coupons                         - Створити новий купон [Admin]
- PUT /api/admin/coupons/{id}                     - Оновити купон [Admin]
- PATCH /api/admin/coupons/{id}                   - Частково оновити купон [Admin]
- DELETE /api/admin/coupons/{id}                  - Видалити купон [Admin]
- POST /api/admin/coupons/bulk-delete             - Масово видалити купони [Admin]

## Запити на роль продавця
- GET /api/admin/seller-requests                  - Список усіх запитів на роль продавця [Admin]
- GET /api/admin/seller-requests/{id}             - Отримати конкретний запит [Admin]
- POST /api/admin/seller-requests/{id}/approve    - Схвалити запит [Admin]
- POST /api/admin/seller-requests/{id}/reject     - Відхилити запит [Admin]
- PUT /api/admin/seller-requests/{id}             - Оновити запит (наприклад, додаткова інформація) [Admin]
- PATCH /api/admin/seller-requests/{id}           - Частково оновити запит [Admin]
- POST /api/admin/seller-requests/bulk-approve    - Масово схвалити запити [Admin]
- POST /api/admin/seller-requests/bulk-reject     - Масово відхилити запити [Admin]

## Способи доставки
- GET /api/admin/shipping-methods                 - Список усіх способів доставки [Admin]
- GET /api/admin/shipping-methods/{id}            - Отримати конкретний спосіб доставки [Admin]
- POST /api/admin/shipping-methods                - Створити новий спосіб доставки [Admin]
- PUT /api/admin/shipping-methods/{id}            - Оновити спосіб доставки [Admin]
- PATCH /api/admin/shipping-methods/{id}          - Частково оновити спосіб доставки [Admin]
- DELETE /api/admin/shipping-methods/{id}         - Видалити спосіб доставки [Admin]
- POST /api/admin/shipping-methods/bulk-delete    - Масово видалити способи доставки [Admin]

## Сповіщення
- GET /api/admin/notifications                    - Список усіх сповіщень [Admin]
- GET /api/admin/notifications/{id}               - Отримати конкретне сповіщення [Admin]
- POST /api/admin/notifications                   - Створити нове сповіщення [Admin]
- PUT /api/admin/notifications/{id}               - Оновити сповіщення [Admin]
- PATCH /api/admin/notifications/{id}             - Частково оновити сповіщення [Admin]
- DELETE /api/admin/notifications/{id}            - Видалити сповіщення [Admin]
- POST /api/admin/notifications/bulk-send         - Надіслати сповіщення кільком користувачам [Admin]

## Чати
- GET /api/admin/chats                            - Список усіх чатів [Admin]
- GET /api/admin/chats/{id}                       - Отримати конкретний чат [Admin]
- GET /api/admin/chats/{id}/messages              - Список повідомлень чату [Admin]
- PUT /api/admin/chats/{id}/messages/{messageId}  - Оновити повідомлення (наприклад, модерація) [Admin]
- DELETE /api/admin/chats/{id}/messages/{messageId} - Видалити повідомлення [Admin]

## Відгуки та рейтинги
- GET /api/admin/reviews                          - Список усіх відгуків [Admin]
- GET /api/admin/reviews/{id}                     - Отримати конкретний відгук [Admin]
- PUT /api/admin/reviews/{id}                     - Оновити відгук (наприклад, модерація) [Admin]
- PATCH /api/admin/reviews/{id}                   - Частково оновити відгук [Admin]
- DELETE /api/admin/reviews/{id}                  - Видалити відгук [Admin]
- GET /api/admin/ratings                          - Список усіх рейтингів [Admin]
- GET /api/admin/ratings/{id}                     - Отримати конкретний рейтинг [Admin]
- PUT /api/admin/ratings/{id}                     - Оновити рейтинг [Admin]
- PATCH /api/admin/ratings/{id}                   - Частково оновити рейтинг [Admin]
- DELETE /api/admin/ratings/{id}                  - Видалити рейтинг [Admin]
- POST /api/admin/reviews/bulk-delete             - Масово видалити відгуки [Admin]
- POST /api/admin/ratings/bulk-delete             - Масово видалити рейтинги [Admin]

## Аналітика
- GET /api/admin/analytics/orders                 - Отримати статистику замовлень [Admin]
- GET /api/admin/analytics/products               - Отримати статистику продуктів [Admin]
- GET /api/admin/analytics/users                  - Отримати статистику користувачів [Admin]

## Журнал аудиту
- GET /api/admin/audit-logs                       - Список журналів аудиту [Admin]

## Вебхуки
- POST /api/admin/webhooks                        - Створити вебхук [Admin]
- GET /api/admin/webhooks                         - Список вебхуків [Admin]
- GET /api/admin/webhooks/{id}                    - Отримати конкретний вебхук [Admin]
- PUT /api/admin/webhooks/{id}                    - Оновити вебхук [Admin]
- DELETE /api/admin/webhooks/{id}                 - Видалити вебхук [Admin]

## М'яке видалення
- GET /api/admin/trash/{entityType}               - Список м'яко видалених об'єктів (наприклад, продукти, користувачі) [Admin]
- POST /api/admin/trash/{entityType}/{id}/restore - Відновити м'яко видалений об'єкт [Admin]
- DELETE /api/admin/trash/{entityType}/{id}/permanent - Остаточно видалити об'єкт [Admin]

# Маршрути для роботи з файлами
## Middleware для статичних файлів
- GET /uploads/**                                 - Отримати статичний файл за шляхом [Усі]

## Зображення для сутностей
### Категорії
- POST /api/categories/{slug}/image               - Завантажити зображення категорії [Admin, SellerOwner]
- POST /api/categories/{slug}/meta-image          - Завантажити мета-зображення категорії [Admin, SellerOwner]

### Компанії
- POST /api/companies/{slug}/image                - Завантажити зображення компанії [Admin, SellerOwner]
- POST /api/companies/{slug}/meta-image           - Завантажити мета-зображення компанії [Admin, SellerOwner]

### Продукти
- POST /api/products/{id}/images/upload           - Завантажити зображення продукту [Admin, Seller, SellerOwner]
- POST /api/products/{id}/meta-image              - Завантажити мета-зображення продукту [Admin, Seller, SellerOwner]