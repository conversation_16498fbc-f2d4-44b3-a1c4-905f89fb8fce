import apiClient from './api.service';

/**
 * Універсальний сервіс для роботи з зображеннями
 */
class ImageService {
  constructor() {
    this.baseURL = '/universal';
  }

  /**
   * Нормалізує URL зображення для валідації на бекенді
   * @param {string} url - URL для нормалізації
   * @returns {string} Нормалізований URL
   */
  normalizeImageUrl(url) {
    if (!url) return url;

    // Якщо URL вже абсолютний, повертаємо як є
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Для розробки використовуємо localhost:5296 (бекенд)
    // В продакшені це буде замінено на реальний домен
    const backendUrl = import.meta.env.DEV ? 'http://localhost:5296' : window.location.origin;

    // Якщо URL відносний, додаємо базовий домен бекенду
    if (url.startsWith('/')) {
      return `${backendUrl}${url}`;
    }

    // Якщо URL не починається з /, додаємо / та базовий домен
    return `${backendUrl}/${url}`;
  }

  /**
   * Завантаження зображення для сутності
   * @param {string} entityType - Тип сутності (user, product, category, company, seller-request)
   * @param {string} entityId - ID сутності
   * @param {File} file - Файл зображення
   * @param {Function} onProgress - Callback для відстеження прогресу
   * @returns {Promise<Object>} Відповідь сервера
   */
  async uploadImage(entityType, entityId, file, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    };

    try {
      // Спеціальний endpoint для seller-request
      if (entityType === 'seller-request') {
        const response = await apiClient.post(
          `/api/users/me/seller-requests/${entityId}/images/logo`,
          formData,
          config
        );
        return response.data;
      }

      // Стандартний endpoint для інших типів
      const response = await apiClient.post(
        `${this.baseURL}/images/${entityType}/${entityId}`,
        formData,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Завантаження мета-зображення для сутності
   * @param {string} entityType - Тип сутності (product, category, company, seller-request)
   * @param {string} entityId - ID сутності
   * @param {File} file - Файл зображення
   * @param {Function} onProgress - Callback для відстеження прогресу
   * @returns {Promise<Object>} Відповідь сервера
   */
  async uploadMetaImage(entityType, entityId, file, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    };

    try {
      // Спеціальний endpoint для seller-request
      if (entityType === 'seller-request') {
        const response = await apiClient.post(
          `/api/users/me/seller-requests/${entityId}/images/meta`,
          formData,
          config
        );
        return response.data;
      }

      // Стандартний endpoint для інших типів
      const response = await apiClient.post(
        `/universal/meta-images/${entityType}/${entityId}`,
        formData,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Отримання всіх зображень для сутності (тільки для продуктів)
   * @param {string} entityType - Тип сутності
   * @param {string} entityId - ID сутності
   * @returns {Promise<Object>} Список зображень
   */
  async getImages(entityType, entityId) {
    try {
      const response = await apiClient.get(
        `${this.baseURL}/images/${entityType}/${entityId}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Видалення зображення за ID
   * @param {string} imageId - ID зображення
   * @returns {Promise<Object>} Відповідь сервера
   */
  async deleteImage(imageId) {
    try {
      const response = await apiClient.delete(`${this.baseURL}/images/${imageId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Видалення зображення сутності (універсальний метод)
   * @param {string} entityType - Тип сутності (company, product, category, user)
   * @param {string} entityId - ID сутності
   * @param {string} imageType - Тип зображення (main, meta)
   * @returns {Promise<Object>} Відповідь сервера
   */
  async deleteEntityImage(entityType, entityId, imageType = 'main') {
    try {
      const response = await apiClient.delete(
        `${this.baseURL}/images/${entityType}/${entityId}?imageType=${imageType}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async deleteProductImage(imageId, productId = null) {
    try {
      // If productId is provided, use the correct endpoint structure
      const endpoint = productId
        ? `/admin/products/${productId}/images/${imageId}`
        : `/admin/products/images/${imageId}`;

      const response = await apiClient.delete(endpoint);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateProductImageOrder(productId, imageId, order) {
    try {
      const response = await apiClient.put(
        `/admin/products/${productId}/images/${imageId}`,
        { order }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateProductImageAltText(productId, imageId, altText) {
    try {
      const response = await apiClient.patch(
        `/admin/products/${productId}/images/${imageId}/alt-text`,
        { altText }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async setMainProductImage(productId, imageId) {
    try {
      const response = await apiClient.patch(
        `/admin/products/${productId}/images/${imageId}/main`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Видалення поточного зображення компанії
   * @param {string} companyId - ID компанії
   * @returns {Promise<Object>} Відповідь сервера
   */
  async deleteCompanyImage(companyId) {
    try {
      console.log('Deleting company image for company:', companyId);
      console.log('Token in localStorage:', localStorage.getItem('token') ? 'Present' : 'Missing');

      const response = await apiClient.delete(
        `/admin/companies/${companyId}/images/current`
      );
      console.log('Delete company image response:', response);
      return response.data;
    } catch (error) {
      console.error('Delete company image error:', error);
      console.error('Error response:', error.response);
      throw this.handleError(error);
    }
  }

  /**
   * Видалення мета-зображення компанії
   * @param {string} companyId - ID компанії
   * @returns {Promise<Object>} Відповідь сервера
   */
  async deleteCompanyMetaImage(companyId) {
    try {
      console.log('Deleting company meta image for company:', companyId);
      console.log('Token in localStorage:', localStorage.getItem('token') ? 'Present' : 'Missing');

      const response = await apiClient.delete(
        `/admin/companies/${companyId}/meta-image`
      );
      console.log('Delete company meta image response:', response);
      return response.data;
    } catch (error) {
      console.error('Delete company meta image error:', error);
      console.error('Error response:', error.response);
      throw this.handleError(error);
    }
  }

  /**
   * Оновлення порядку зображення продукту
   * @param {string} productId - ID продукту
   * @param {string} imageId - ID зображення
   * @param {number} order - Новий порядок
   * @returns {Promise<Object>} Відповідь сервера
   */
  async updateProductImageOrder(productId, imageId, order) {
    try {
      const response = await apiClient.put(
        `/admin/products/${productId}/images/${imageId}`,
        { order }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Видалення мета-зображення
   * @param {string} entityType - Тип сутності
   * @param {string} entityId - ID сутності
   * @returns {Promise<Object>} Відповідь сервера
   */
  async deleteMetaImage(entityType, entityId) {
    try {
      const response = await apiClient.delete(
        `/universal/meta-images/${entityType}/${entityId}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Встановлення головного зображення (тільки для продуктів)
   * @param {string} entityType - Тип сутності
   * @param {string} entityId - ID сутності
   * @param {string} imageId - ID зображення
   * @returns {Promise<Object>} Відповідь сервера
   */
  async setMainImage(entityType, entityId, imageId) {
    try {
      const response = await apiClient.patch(
        `${this.baseURL}/images/${entityType}/${entityId}/main/${imageId}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Валідація файлу зображення
   * @param {File} file - Файл для валідації
   * @param {Object} options - Опції валідації
   * @returns {Object} Результат валідації
   */
  validateImageFile(file, options = {}) {
    const {
      maxSize = 5 * 1024 * 1024, // 5MB
      allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      maxWidth = 2048,
      maxHeight = 2048,
    } = options;

    const errors = [];

    // Перевірка типу файлу
    if (!allowedTypes.includes(file.type)) {
      errors.push(`Дозволені тільки файли типів: ${allowedTypes.join(', ')}`);
    }

    // Перевірка розміру файлу
    if (file.size > maxSize) {
      errors.push(`Розмір файлу не повинен перевищувати ${maxSize / 1024 / 1024}MB`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Отримання URL зображення через UniversalImage контролер
   * @param {string} entityType - Тип сутності (product, category, company, user)
   * @param {string} entityId - ID сутності
   * @param {string} imageType - Тип зображення (main, meta, avatar, collection)
   * @param {string} imageId - ID конкретного зображення (для ProductImage)
   * @returns {string} URL зображення
   */
  getImageUrl(entityType, entityId, imageType = 'main', imageId = null) {
    let url = `${this.baseURL}/images/${entityType}/${entityId}/url?imageType=${imageType}`;
    if (imageId) {
      url += `&imageId=${imageId}`;
    }
    return url;
  }

  /**
   * Отримання URL головного зображення товару
   * @param {string} productId - ID товару
   * @returns {string} URL головного зображення
   */
  getProductMainImageUrl(productId) {
    return this.getImageUrl('product', productId, 'main');
  }

  /**
   * Отримання URL конкретного зображення товару
   * @param {string} productId - ID товару
   * @param {string} imageId - ID зображення
   * @returns {string} URL зображення
   */
  getProductImageUrl(productId, imageId) {
    return this.getImageUrl('product', productId, 'collection', imageId);
  }

  /**
   * Створення превью зображення
   * @param {File} file - Файл зображення
   * @returns {Promise<string>} Data URL превью
   */
  createImagePreview(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Обробка помилок API
   * @param {Error} error - Помилка
   * @returns {Object} Оброблена помилка
   */
  handleError(error) {
    if (error.response) {
      // Помилка від сервера
      return {
        message: error.response.data?.message || 'Помилка сервера',
        status: error.response.status,
        errors: error.response.data?.errors || [],
      };
    } else if (error.request) {
      // Помилка мережі
      return {
        message: 'Помилка мережі. Перевірте підключення до інтернету.',
        status: 0,
        errors: [],
      };
    } else {
      // Інша помилка
      return {
        message: error.message || 'Невідома помилка',
        status: 0,
        errors: [],
      };
    }
  }

  /**
   * Розширена валідація зображення з перевіркою розмірів
   * @param {File} file - Файл для валідації
   * @param {Object} options - Опції валідації
   * @returns {Promise<Object>} Результат валідації
   */
  async validateImageAdvanced(file, options = {}) {
    const {
      maxSize = 5 * 1024 * 1024, // 5MB
      allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      minWidth = 0,
      minHeight = 0,
      maxWidth = 4096,
      maxHeight = 4096
    } = options;

    const errors = [];

    // Базова валідація
    if (!allowedTypes.includes(file.type)) {
      errors.push(`Непідтримуваний тип файлу. Дозволені: ${allowedTypes.join(', ')}`);
    }

    if (file.size > maxSize) {
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(1);
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      errors.push(`Розмір файлу ${fileSizeMB}MB перевищує максимальний ${maxSizeMB}MB`);
    }

    // Валідація розмірів
    if (minWidth > 0 || minHeight > 0 || maxWidth < 4096 || maxHeight < 4096) {
      try {
        const dimensions = await this.getImageDimensions(file);

        if (dimensions.width < minWidth) {
          errors.push(`Ширина зображення ${dimensions.width}px менша за мінімальну ${minWidth}px`);
        }
        if (dimensions.height < minHeight) {
          errors.push(`Висота зображення ${dimensions.height}px менша за мінімальну ${minHeight}px`);
        }
        if (dimensions.width > maxWidth) {
          errors.push(`Ширина зображення ${dimensions.width}px перевищує максимальну ${maxWidth}px`);
        }
        if (dimensions.height > maxHeight) {
          errors.push(`Висота зображення ${dimensions.height}px перевищує максимальну ${maxHeight}px`);
        }
      } catch (error) {
        errors.push('Неможливо визначити розміри зображення');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      fileSize: file.size,
      fileName: file.name,
      fileType: file.type
    };
  }

  /**
   * Отримання розмірів зображення
   * @param {File} file - Файл зображення
   * @returns {Promise<Object>} Розміри зображення
   */
  getImageDimensions(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height
        });
      };
      img.onerror = () => {
        reject(new Error('Неможливо завантажити зображення'));
      };
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Стиснення зображення
   * @param {File} file - Оригінальний файл
   * @param {Object} options - Опції стиснення
   * @returns {Promise<File>} Стиснений файл
   */
  compressImage(file, options = {}) {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      outputFormat = 'image/jpeg'
    } = options;

    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Розрахунок нових розмірів
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Малювання та стиснення
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob((blob) => {
          const compressedFile = new File([blob], file.name, {
            type: outputFormat,
            lastModified: Date.now()
          });
          resolve(compressedFile);
        }, outputFormat, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Створення превью зображення з можливістю обрізки
   * @param {File} file - Файл зображення
   * @param {Object} cropData - Дані для обрізки (x, y, width, height)
   * @returns {Promise<string>} URL превью
   */
  async createCroppedPreview(file, cropData = null) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        if (cropData) {
          // Обрізка зображення
          canvas.width = cropData.width;
          canvas.height = cropData.height;

          ctx.drawImage(
            img,
            cropData.x, cropData.y, cropData.width, cropData.height,
            0, 0, cropData.width, cropData.height
          );
        } else {
          // Без обрізки
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
        }

        resolve(canvas.toDataURL());
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Форматування розміру файлу для відображення
   * @param {number} bytes - Розмір в байтах
   * @returns {string} Відформатований розмір
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Перевірка чи потрібно стискати зображення
   * @param {File} file - Файл для перевірки
   * @param {number} maxUncompressedSize - Максимальний розмір без стиснення
   * @returns {boolean} Чи потрібно стискати
   */
  shouldCompressImage(file, maxUncompressedSize = 1024 * 1024) {
    return file.size > maxUncompressedSize || file.type === 'image/png';
  }
}

export default new ImageService();
