<template>
  <div class="admin-page-content">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title">
        <h1 class="admin-page-heading">
          <i class="fas fa-comment-dots"></i>
          Review Details
        </h1>
        <nav class="admin-breadcrumb">
          <router-link to="/admin" class="admin-breadcrumb-item">Dashboard</router-link>
          <span class="admin-breadcrumb-separator">/</span>
          <router-link to="/admin/reviews" class="admin-breadcrumb-item">Reviews</router-link>
          <span class="admin-breadcrumb-separator">/</span>
          <span class="admin-breadcrumb-item admin-breadcrumb-current">
            {{ review.id ? `Review #${review.id.substring(0, 8)}...` : 'Review Details' }}
          </span>
        </nav>
      </div>
      <div class="admin-page-actions">
        <button
          v-if="review.productSlug"
          class="admin-btn admin-btn-secondary admin-btn-sm"
          @click="viewProduct"
          :disabled="actionLoading">
          <i class="fas fa-external-link-alt"></i>
          View Product
        </button>
        <button
          class="admin-btn admin-btn-secondary admin-btn-sm"
          @click="fetchReview"
          :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-container" v-if="loading && !review.id">
      <div class="admin-loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="admin-loading-text">Loading review details...</p>
    </div>

    <!-- Error State -->
    <div class="admin-alert admin-alert--danger" v-else-if="error">
      <div class="admin-alert-content">
        <i class="fas fa-exclamation-circle"></i>
        <span>{{ error }}</span>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Review Details -->
    <div v-else-if="review.id">
      <div class="columns">
        <!-- Main Info -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Review Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Product</label>
                    <div class="info-value">
                      <div class="product-info">
                        <router-link
                          :to="{ name: 'AdminProductDetail', params: { id: review.productId } }"
                          class="product-link">
                          {{ review.productName }}
                        </router-link>
                        <div v-if="review.productSlug" class="product-slug">
                          <small class="has-text-grey">Slug: {{ review.productSlug }}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">User</label>
                    <div class="info-value">
                      <div class="user-info">
                        <router-link
                          :to="{ name: 'AdminUserDetail', params: { id: review.userId } }"
                          class="user-link">
                          {{ review.userName }}
                        </router-link>
                        <div v-if="review.userEmail" class="user-email">
                          <small class="has-text-grey">{{ review.userEmail }}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="field">
                    <label class="label">Overall Rating</label>
                    <div class="info-value">
                      <div class="rating-display">
                        <div class="stars">
                          <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= Math.round(review.averageRating || 0) }">
                            ★
                          </span>
                          <span class="ml-2 rating-text">
                            {{ review.averageRating ? review.averageRating.toFixed(1) : '0.0' }}/5
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Review ID</label>
                    <p class="info-value">
                      <code class="review-id">{{ review.id }}</code>
                    </p>
                  </div>
                  <div class="field">
                    <label class="label">Created At</label>
                    <p class="info-value">{{ formatDateTime(review.createdAt) }}</p>
                  </div>
                  <div class="field" v-if="review.updatedAt && review.updatedAt !== review.createdAt">
                    <label class="label">Updated At</label>
                    <p class="info-value">{{ formatDateTime(review.updatedAt) }}</p>
                  </div>
                  <div class="field" v-if="review.parentId">
                    <label class="label">Parent Review</label>
                    <p class="info-value">
                      <router-link
                        :to="{ name: 'AdminReviewDetail', params: { id: review.parentId } }"
                        class="parent-link">
                        View Parent Review
                      </router-link>
                    </p>
                  </div>
                </div>
              </div>
              
              <div class="field" v-if="review.comment">
                <label class="label">Comment</label>
                <div class="content">
                  <p>{{ review.comment }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Rating Breakdown -->
          <div class="card mt-4" v-if="review.serviceRating || review.deliveryTimeRating || review.accuracyRating">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon"><i class="fas fa-star"></i></span>
                <span>Detailed Rating Breakdown</span>
              </p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-4">
                  <div class="rating-category">
                    <label class="label">Service Quality</label>
                    <div class="rating-display">
                      <div class="stars">
                        <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= (review.serviceRating || 0) }">
                          ★
                        </span>
                      </div>
                      <div class="rating-value">{{ review.serviceRating || 'N/A' }}/5</div>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="rating-category">
                    <label class="label">Delivery Time</label>
                    <div class="rating-display">
                      <div class="stars">
                        <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= (review.deliveryTimeRating || 0) }">
                          ★
                        </span>
                      </div>
                      <div class="rating-value">{{ review.deliveryTimeRating || 'N/A' }}/5</div>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="rating-category">
                    <label class="label">Product Accuracy</label>
                    <div class="rating-display">
                      <div class="stars">
                        <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= (review.accuracyRating || 0) }">
                          ★
                        </span>
                      </div>
                      <div class="rating-value">{{ review.accuracyRating || 'N/A' }}/5</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Average calculation display -->
              <div class="has-text-centered mt-4 pt-4" style="border-top: 1px solid var(--border-color);">
                <div class="average-calculation">
                  <span class="has-text-grey">Average: </span>
                  <span class="has-text-weight-bold">
                    ({{ review.serviceRating || 0 }} + {{ review.deliveryTimeRating || 0 }} + {{ review.accuracyRating || 0 }}) ÷ 3 =
                    {{ review.averageRating ? review.averageRating.toFixed(1) : '0.0' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">
                <span class="icon"><i class="fas fa-cogs"></i></span>
                <span>Actions</span>
              </p>
            </div>
            <div class="card-content">
              <div class="buttons is-fullwidth">
                <button
                  v-if="review.productSlug"
                  class="button is-link is-fullwidth"
                  @click="viewProduct"
                  :disabled="actionLoading">
                  <span class="icon"><i class="fas fa-external-link-alt"></i></span>
                  <span>View Product Page</span>
                </button>
                <router-link
                  v-if="review.productId"
                  :to="{ name: 'AdminProductDetail', params: { id: review.productId } }"
                  class="button is-info is-fullwidth">
                  <span class="icon"><i class="fas fa-box"></i></span>
                  <span>Product Admin</span>
                </router-link>
                <router-link
                  v-if="review.userId"
                  :to="{ name: 'AdminUserDetail', params: { id: review.userId } }"
                  class="button is-warning is-fullwidth">
                  <span class="icon"><i class="fas fa-user"></i></span>
                  <span>User Profile</span>
                </router-link>
                <button
                  class="button is-primary is-fullwidth"
                  @click="showEditModal = true"
                  :disabled="actionLoading">
                  <span class="icon"><i class="fas fa-edit"></i></span>
                  <span>Edit Comment</span>
                </button>
                <button
                  class="button is-danger is-fullwidth"
                  @click="showDeleteModal = true"
                  :disabled="actionLoading">
                  <span class="icon"><i class="fas fa-trash"></i></span>
                  <span>Delete Review</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Review Stats -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Statistics</p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Helpful Votes</label>
                <p class="info-value">{{ review.helpfulVotes || 0 }}</p>
              </div>
              <div class="field">
                <label class="label">Total Votes</label>
                <p class="info-value">{{ review.totalVotes || 0 }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Comment Modal -->
    <div class="modal" :class="{ 'is-active': showEditModal }">
      <div class="modal-background" @click="closeEditModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Edit Review Comment</p>
          <button class="delete" @click="closeEditModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Comment</label>
            <div class="control">
              <textarea
                class="textarea"
                v-model="editedComment"
                placeholder="Enter review comment..."
                rows="4"
                maxlength="1000">
              </textarea>
            </div>
            <p class="help">{{ editedComment.length }}/1000 characters</p>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-primary"
            @click="updateReview"
            :class="{ 'is-loading': actionLoading }"
            :disabled="!editedComment.trim() || editedComment.length > 1000">
            Save Changes
          </button>
          <button class="button" @click="closeEditModal">Cancel</button>
        </footer>
      </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="showDeleteModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Delete Review</p>
          <button class="delete" @click="showDeleteModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete this review? This action cannot be undone.</p>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-danger"
            @click="deleteReview"
            :class="{ 'is-loading': actionLoading }">
            Delete Review
          </button>
          <button class="button" @click="showDeleteModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import reviewsService from '@/admin/services/reviews';

const route = useRoute();
const router = useRouter();

// Reactive data
const review = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showDeleteModal = ref(false);
const showEditModal = ref(false);
const editedComment = ref('');

// Methods
const fetchReview = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const response = await reviewsService.getReviewById(route.params.id);
    review.value = response;
  } catch (err) {
    error.value = err.message || 'Failed to load review details';
  } finally {
    loading.value = false;
  }
};

const updateReview = async () => {
  actionLoading.value = true;
  try {
    await reviewsService.updateReview(review.value.id, { comment: editedComment.value });
    // Refresh review data
    await fetchReview();
    closeEditModal();
  } catch (err) {
    error.value = err.message || 'Failed to update review';
  } finally {
    actionLoading.value = false;
  }
};

const deleteReview = async () => {
  actionLoading.value = true;
  try {
    await reviewsService.deleteReview(review.value.id);
    router.push({ name: 'AdminReviews' });
  } catch (err) {
    error.value = err.message || 'Failed to delete review';
  } finally {
    actionLoading.value = false;
    showDeleteModal.value = false;
  }
};

const closeEditModal = () => {
  showEditModal.value = false;
  editedComment.value = review.value.comment || '';
};

const viewProduct = () => {
  if (review.value.productSlug) {
    // Відкриваємо сторінку продукту в новій вкладці
    window.open(`/products/${review.value.productSlug}`, '_blank');
  }
};

// Utility methods
const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString('uk-UA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Lifecycle
onMounted(() => {
  fetchReview();
});

// Watch for review changes to update editedComment
watch(() => review.value.comment, (newComment) => {
  if (newComment && !showEditModal.value) {
    editedComment.value = newComment;
  }
}, { immediate: true });
</script>

<style scoped>
.review-detail {
  padding: 1rem;
}

/* Breadcrumbs */
.breadcrumb {
  margin-bottom: 1.5rem;
}

.breadcrumb a {
  color: var(--link-color);
}

.breadcrumb li.is-active a {
  color: var(--text-primary);
}

/* Info values */
.info-value {
  font-weight: 500;
  color: var(--text-primary);
}

.product-info, .user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-link, .user-link {
  color: var(--link-color);
  font-weight: 600;
  text-decoration: none;
}

.product-link:hover, .user-link:hover {
  color: var(--link-color-dark);
  text-decoration: underline;
}

.product-slug, .user-email {
  font-size: 0.9em;
}

.review-id {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  word-break: break-all;
}

.parent-link {
  color: var(--info-color);
  text-decoration: none;
}

.parent-link:hover {
  text-decoration: underline;
}

/* Rating displays */
.rating-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1.2em;
  transition: color 0.2s ease;
}

.star.is-filled {
  color: #ffd700;
}

.rating-text, .rating-value {
  font-weight: 600;
  color: var(--text-primary);
}

.rating-category {
  text-align: center;
  padding: 1rem;
  background-color: var(--darker-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.rating-category .label {
  margin-bottom: 0.5rem;
  font-size: 0.9em;
  color: var(--text-secondary);
}

.average-calculation {
  font-size: 1.1em;
  padding: 0.5rem;
  background-color: var(--darker-bg);
  border-radius: 6px;
  display: inline-block;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.card-header {
  background-color: var(--darker-bg);
  border-bottom: 1px solid var(--border-color);
}

.card-header-title {
  color: var(--text-primary);
}

/* Buttons */
.button.is-link {
  background-color: var(--link-color);
  border-color: var(--link-color);
  color: white;
}

.button.is-link:hover {
  background-color: var(--link-color-dark);
}

.button.is-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.button.is-warning:hover {
  background-color: var(--warning-color-dark);
}

/* Content */
.content p {
  white-space: pre-wrap;
  line-height: 1.6;
  color: var(--text-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .review-detail {
    padding: 0.5rem;
  }

  .level {
    display: block;
  }

  .level-right {
    margin-top: 1rem;
  }

  .buttons {
    flex-wrap: wrap;
  }

  .rating-category {
    padding: 0.5rem;
  }

  .star {
    font-size: 1em;
  }
}
</style>

<style scoped>
/* Import admin styles */
@import '@/assets/css/admin/admin.css';
</style>
