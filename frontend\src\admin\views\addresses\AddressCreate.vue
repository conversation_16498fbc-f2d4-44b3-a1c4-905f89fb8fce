<template>
  <div class="address-create">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <nav class="breadcrumb">
            <ul>
              <li><router-link to="/admin/addresses">Addresses</router-link></li>
              <li class="is-active"><a>Create Address</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <div class="columns">
      <div class="column is-8">
        <div class="card">
          <div class="card-header">
            <p class="card-header-title">Create New Address</p>
          </div>
          <div class="card-content">
            <!-- Error Display -->
            <div class="notification is-danger" v-if="error">
              <button class="delete" @click="error = null"></button>
              {{ error }}
            </div>

            <form @submit.prevent="createAddress">
              <div class="field">
                <label class="label">User (Optional)</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.userId">
                      <option value="">Select a user (optional)</option>
                      <option v-for="user in users" :key="user.id" :value="user.id">
                        {{ user.name }} ({{ user.email }})
                      </option>
                    </select>
                  </div>
                </div>
                <p class="help">Leave empty to create an unassigned address</p>
              </div>

              <div class="field">
                <label class="label">Region *</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    v-model="form.region" 
                    placeholder="Enter region"
                    required>
                </div>
              </div>

              <div class="field">
                <label class="label">City *</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    v-model="form.city" 
                    placeholder="Enter city"
                    required>
                </div>
              </div>

              <div class="field">
                <label class="label">Street *</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    v-model="form.street" 
                    placeholder="Enter street address"
                    required>
                </div>
              </div>

              <div class="field">
                <label class="label">Postal Code</label>
                <div class="control">
                  <input 
                    class="input" 
                    type="text" 
                    v-model="form.postalCode" 
                    placeholder="Enter postal code">
                </div>
              </div>

              <div class="field is-grouped">
                <div class="control">
                  <button 
                    type="submit" 
                    class="button is-primary"
                    :class="{ 'is-loading': loading }"
                    :disabled="loading || !isFormValid">
                    <span class="icon"><i class="fas fa-save"></i></span>
                    <span>Create Address</span>
                  </button>
                </div>
                <div class="control">
                  <button 
                    type="button" 
                    class="button"
                    @click="goBack"
                    :disabled="loading">
                    <span class="icon"><i class="fas fa-arrow-left"></i></span>
                    <span>Cancel</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="column is-4">
        <div class="card">
          <div class="card-header">
            <p class="card-header-title">Address Preview</p>
          </div>
          <div class="card-content">
            <div class="content">
              <p><strong>Full Address:</strong></p>
              <div class="box has-background-light">
                <p>{{ formatPreviewAddress() }}</p>
              </div>
              
              <p><strong>Assigned User:</strong></p>
              <p v-if="selectedUser">
                {{ selectedUser.name }} ({{ selectedUser.email }})
              </p>
              <p v-else class="has-text-grey">No user assigned</p>
            </div>
          </div>
        </div>

        <div class="card mt-4">
          <div class="card-header">
            <p class="card-header-title">Instructions</p>
          </div>
          <div class="card-content">
            <div class="content">
              <ul>
                <li>Fields marked with * are required</li>
                <li>You can create an address without assigning it to a user</li>
                <li>Postal code is optional but recommended</li>
                <li>Make sure the address information is accurate</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { addressesService } from '@/admin/services/addresses';
import { usersService } from '@/admin/services/users';

const router = useRouter();

// Reactive data
const loading = ref(false);
const error = ref(null);
const users = ref([]);

const form = ref({
  userId: '',
  region: '',
  city: '',
  street: '',
  postalCode: ''
});

// Computed
const isFormValid = computed(() => {
  return form.value.region.trim() && 
         form.value.city.trim() && 
         form.value.street.trim();
});

const selectedUser = computed(() => {
  if (!form.value.userId) return null;
  return users.value.find(user => user.id === form.value.userId);
});

// Methods
const loadUsers = async () => {
  try {
    const response = await usersService.getUsers({ pageSize: 1000 }); // Get all users
    users.value = response.data || [];
  } catch (err) {
    console.error('Failed to load users:', err);
    // Don't show error for users loading failure
  }
};

const createAddress = async () => {
  loading.value = true;
  error.value = null;

  try {
    const addressData = {
      userId: form.value.userId || null,
      region: form.value.region.trim(),
      city: form.value.city.trim(),
      street: form.value.street.trim(),
      postalCode: form.value.postalCode.trim() || null
    };

    await addressesService.createAddress(addressData);
    router.push({ name: 'AdminAddresses' });
  } catch (err) {
    error.value = err.message || 'Failed to create address';
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push({ name: 'AdminAddresses' });
};

const formatPreviewAddress = () => {
  const parts = [];
  if (form.value.street.trim()) parts.push(form.value.street.trim());
  if (form.value.city.trim()) parts.push(form.value.city.trim());
  if (form.value.region.trim()) parts.push(form.value.region.trim());
  if (form.value.postalCode.trim()) parts.push(form.value.postalCode.trim());
  
  return parts.length > 0 ? parts.join(', ') : 'Enter address details...';
};

// Lifecycle
onMounted(() => {
  loadUsers();
});
</script>

<style scoped>
.address-create {
  padding: 1rem;
}

.box.has-background-light {
  background-color: #f5f5f5 !important;
  min-height: 3rem;
  display: flex;
  align-items: center;
}

.has-text-grey {
  color: #7a7a7a !important;
}

.notification.is-danger {
  margin-bottom: 1rem;
}

.field .help {
  color: #7a7a7a;
  font-size: 0.875rem;
}
</style>
