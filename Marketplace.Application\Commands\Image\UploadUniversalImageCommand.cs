using Marketplace.Domain.Services;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Універсальна команда для завантаження зображення для будь-якої сутності
/// </summary>
public record UploadUniversalImageCommand(
    string EntityType,
    Guid EntityId,
    IFormFile File,
    string ImageType = "main"
) : IRequest<FileUploadResult>;
