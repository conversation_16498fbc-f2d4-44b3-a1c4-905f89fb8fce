/* ===== ADMIN CATEGORIES PAGE STYLES ===== */
/* Based on Reports page design patterns */

/* ===== CATEGORIES PAGE LAYOUT ===== */
.admin-categories {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-categories .admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
}

.admin-categories .admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-lg);
}

/* ===== CATEGORY FILTERS ===== */
.admin-category-filters {
  margin-bottom: var(--admin-space-2xl);
}

/* ===== CATEGORY TABLE ===== */
.admin-category-table {
  background: var(--admin-white);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-2xl);
  box-shadow: var(--admin-shadow-md);
  border: 1px solid var(--admin-border-color);
  margin-bottom: var(--admin-space-2xl);
}

/* ===== CATEGORY STATUS ===== */
.admin-category-status {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-full);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-category-status.active {
  background: var(--admin-success-bg);
  color: var(--admin-success-dark);
  border: 1px solid var(--admin-success-light);
}

.admin-category-status.inactive {
  background: var(--admin-danger-bg);
  color: var(--admin-danger-dark);
  border: 1px solid var(--admin-danger-light);
}

/* ===== CATEGORY ACTION BUTTONS ===== */
.admin-category-actions {
  display: flex;
  gap: var(--admin-space-xs);
  align-items: center;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .admin-categories {
    padding: var(--admin-space-lg);
  }
  
  .admin-categories .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-categories .admin-page-title {
    font-size: var(--admin-text-2xl);
  }
  
  .admin-category-filters {
    padding: var(--admin-space-lg);
  }
  
  .admin-category-table {
    padding: var(--admin-space-lg);
    overflow-x: auto;
  }
}
