using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.ProductImage;

public class UpdateProductImageOrderCommandHandler : IRequestHandler<UpdateProductImageOrderCommand, bool>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly ILogger<UpdateProductImageOrderCommandHandler> _logger;

    public UpdateProductImageOrderCommandHandler(
        IProductImageRepository productImageRepository,
        ILogger<UpdateProductImageOrderCommandHandler> logger)
    {
        _productImageRepository = productImageRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateProductImageOrderCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation($"Updating order for image {request.ImageId} to {request.Order}");

            // Отримуємо зображення
            var productImage = await _productImageRepository.GetByIdAsync(request.ImageId, cancellationToken);
            if (productImage == null)
            {
                _logger.LogWarning($"Product image {request.ImageId} not found");
                return false;
            }

            // Перевіряємо, чи зображення належить до правильного продукту
            if (productImage.ProductId != request.ProductId)
            {
                _logger.LogWarning($"Product image {request.ImageId} does not belong to product {request.ProductId}");
                return false;
            }

            // Оновлюємо порядок
            productImage.Update(order: request.Order);
            await _productImageRepository.UpdateAsync(productImage, cancellationToken);

            _logger.LogInformation($"Successfully updated order for image {request.ImageId} to {request.Order}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating order for image {request.ImageId}");
            return false;
        }
    }
}
