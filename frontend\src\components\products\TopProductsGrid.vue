<template>
  <div class="top-products-container">
    <div class="section-header">
      <h2 class="section-title">
        {{ title }}
      </h2>
      <div class="section-controls" v-if="showControls">
        <select v-model="selectedCategory" @change="loadTopProducts" class="category-select">
          <option value="">Всі категорії</option>
          <option v-for="category in categories" :key="category.slug" :value="category.slug">
            {{ category.name }}
          </option>
        </select>
        <select v-model="sortBy" @change="loadTopProducts" class="sort-select">
          <option value="Sales">За продажами</option>
          <option value="Stock">За наявністю</option>
          <option value="PriceAmount">За ціною</option>
        </select>
        <select v-model="limit" @change="loadTopProducts" class="limit-select">
          <option value="5">5 товарів</option>
          <option value="10">10 товарів</option>
          <option value="15">15 товарів</option>
          <option value="20">20 товарів</option>
        </select>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <p>Завантаження топ товарів...</p>
    </div>

    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="loadTopProducts" class="retry-btn">Спробувати знову</button>
    </div>

    <div v-else-if="products.length === 0" class="empty">
      <p>Товари не знайдено</p>
    </div>

    <div v-else class="products-grid">
      <div 
        v-for="(product, index) in products" 
        :key="product.id" 
        class="product-card"
        @click="openProduct(product)"
      >
        <div class="rank-badge">
          #{{ index + 1 }}
        </div>
        
        <div class="product-image">
          <img
            :src="product.imageUrl || '/placeholder-product.jpg'"
            :alt="product.name"
            @error="handleImageError"
          />
        </div>
        
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <p class="product-category">{{ product.categoryName }}</p>
          
          <div class="product-stats">
            <div class="stat">
              <span class="stat-label">Продано:</span>
              <span class="stat-value sales">{{ product.sales }}</span>
            </div>
            <div class="stat">
              <span class="stat-label">В наявності:</span>
              <span class="stat-value stock">{{ product.stock }}</span>
            </div>
          </div>
          
          <div class="product-price">
            <span class="price">{{ formatPrice(product.priceAmount) }} {{ product.priceCurrency }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showViewAll && !showControls" class="view-all-container">
      <router-link :to="viewAllLink" class="view-all-btn">
        Переглянути всі топ товари
      </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import imageService from '@/services/image.service';

const props = defineProps({
  title: {
    type: String,
    default: 'Топ товари за продажами'
  },
  categorySlug: {
    type: String,
    default: null
  },
  limit: {
    type: Number,
    default: 10
  },
  orderBy: {
    type: String,
    default: 'Sales'
  },
  showControls: {
    type: Boolean,
    default: false
  },
  showViewAll: {
    type: Boolean,
    default: true
  },
  autoLoad: {
    type: Boolean,
    default: true
  }
});

const router = useRouter();

// Reactive data
const products = ref([]);
const categories = ref([]);
const loading = ref(false);
const error = ref(null);

// Controls
const selectedCategory = ref(props.categorySlug || '');
const sortBy = ref(props.orderBy);
const limit = ref(props.limit);

// Computed
const viewAllLink = computed(() => {
  const params = new URLSearchParams();
  if (selectedCategory.value) params.append('category', selectedCategory.value);
  params.append('sortBy', sortBy.value);
  return `/products/top?${params.toString()}`;
});

// Methods
const loadTopProducts = async () => {
  try {
    loading.value = true;
    error.value = null;

    const params = new URLSearchParams();
    if (selectedCategory.value) params.append('categorySlug', selectedCategory.value);
    params.append('limit', limit.value.toString());
    params.append('orderBy', sortBy.value);
    params.append('descending', 'true');

    const response = await fetch(`/api/products/top?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.success && data.data) {
      products.value = data.data;
    } else {
      products.value = data; // Fallback for direct array response
    }
  } catch (err) {
    console.error('Error loading top products:', err);
    error.value = 'Помилка завантаження топ товарів';
  } finally {
    loading.value = false;
  }
};

const loadCategories = async () => {
  try {
    const response = await fetch('/api/categories');
    const data = await response.json();
    
    if (data.success && data.data) {
      categories.value = data.data;
    } else {
      categories.value = data;
    }
  } catch (err) {
    console.error('Error loading categories:', err);
  }
};

const openProduct = (product) => {
  router.push(`/products/${product.slug}`);
};

const handleImageError = (event) => {
  event.target.src = '/placeholder-product.jpg';
};

const formatPrice = (price) => {
  return new Intl.NumberFormat('uk-UA').format(price);
};

// Watchers
watch(() => props.categorySlug, (newValue) => {
  selectedCategory.value = newValue || '';
  if (props.autoLoad) {
    loadTopProducts();
  }
});

// Lifecycle
onMounted(async () => {
  if (props.showControls) {
    await loadCategories();
  }
  
  if (props.autoLoad) {
    await loadTopProducts();
  }
});

// Expose methods for parent components
defineExpose({
  loadTopProducts,
  refresh: loadTopProducts
});
</script>

<style scoped>
.top-products-container {
  margin: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.section-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.category-select,
.sort-select,
.limit-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  min-width: 120px;
}

.loading {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff7700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 40px;
  color: #dc3545;
}

.retry-btn {
  background: #ff7700;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.product-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.rank-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #ff7700;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1;
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #333;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category {
  font-size: 12px;
  color: #666;
  margin: 0 0 10px 0;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #666;
  margin-bottom: 2px;
}

.stat-value {
  font-weight: 600;
  font-size: 14px;
}

.stat-value.sales {
  color: #28a745;
}

.stat-value.stock {
  color: #007bff;
}

.product-price {
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: #ff7700;
}

.view-all-container {
  text-align: center;
  margin-top: 30px;
}

.view-all-btn {
  display: inline-block;
  background: #ff7700;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.view-all-btn:hover {
  background: #e66600;
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
  }

  .section-controls {
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
}
</style>
