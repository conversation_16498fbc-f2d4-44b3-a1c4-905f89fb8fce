<template>
  <div class="modern-table-container">
    <!-- Table Header -->
    <div class="table-header">
      <div class="table-header-cell" style="width: 80px;">Image</div>
      <div class="table-header-cell" style="flex: 2;">Category</div>
      <div class="table-header-cell" style="width: 120px;">Type</div>
      <div class="table-header-cell" style="width: 100px;">Products</div>
      <div class="table-header-cell" style="width: 80px;">SEO</div>
      <div class="table-header-cell" style="width: 140px;">Actions</div>
    </div>

    <!-- Table Body -->
    <div class="table-body" v-if="!loading && categories.length > 0">
      <div v-for="category in categories" :key="category.id" class="table-row">
        <!-- Image -->
        <div class="table-cell" style="width: 80px;">
          <div class="category-image">
            <img v-if="category.image" :src="category.image" :alt="category.name" class="category-img">
            <div v-else class="category-img-placeholder">
              <i class="fas fa-folder"></i>
            </div>
          </div>
        </div>

        <!-- Category Info -->
        <div class="table-cell category-info" style="flex: 2;">
          <div class="category-name">{{ category.name }}</div>
          <div class="category-slug">{{ category.slug }}</div>
          <div v-if="category.description" class="category-description">
            {{ truncateText(category.description, 80) }}
          </div>
        </div>

        <!-- Type -->
        <div class="table-cell" style="width: 120px;">
          <span v-if="!category.parentId" class="badge badge-primary">
            <i class="fas fa-layer-group"></i>
            Root
          </span>
          <span v-else class="badge badge-info">
            <i class="fas fa-sitemap"></i>
            Child
          </span>
        </div>

        <!-- Products -->
        <div class="table-cell" style="width: 100px;">
          <div class="product-count-container">
            <span class="badge" :class="getProductCountBadgeClass(category.productCount)">
              <i class="fas fa-cube"></i>
              {{ category.productCount || 0 }}
            </span>
            <button
              v-if="category.productCount > 0"
              class="view-products-btn"
              @click="$emit('view-products', category)"
              title="View products">
              <i class="fas fa-external-link-alt"></i>
            </button>
          </div>
        </div>

        <!-- SEO -->
        <div class="table-cell" style="width: 80px;">
          <div class="seo-indicators">
            <span v-if="category.metaTitle" class="seo-indicator seo-indicator-success" title="Has Meta Title">
              <i class="fas fa-heading"></i>
            </span>
            <span v-if="category.metaDescription" class="seo-indicator seo-indicator-info" title="Has Meta Description">
              <i class="fas fa-align-left"></i>
            </span>
            <span v-if="category.metaImage" class="seo-indicator seo-indicator-warning" title="Has Meta Image">
              <i class="fas fa-image"></i>
            </span>
            <span v-if="!category.metaTitle && !category.metaDescription && !category.metaImage"
                  class="seo-indicator seo-indicator-none" title="No SEO data">
              <i class="fas fa-times"></i>
            </span>
          </div>
        </div>

        <!-- Actions -->
        <div class="table-cell" style="width: 140px;">
          <div class="action-buttons">
            <button
              class="action-btn action-btn-edit"
              @click="$emit('edit', category)"
              title="Edit category">
              <i class="fas fa-edit"></i>
            </button>
            <button
              class="action-btn action-btn-delete"
              @click="$emit('delete', category)"
              :disabled="!canDeleteCategory(category)"
              :title="getDeleteTooltip(category)">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else-if="loading" class="table-loading">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Loading categories...</span>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="table-empty">
      <div class="empty-state">
        <i class="fas fa-folder-open"></i>
        <h3>No categories found</h3>
        <p>Try adjusting your search criteria or create a new category.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  allCategories: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['edit', 'delete', 'view-products']);

// Helper functions
const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const getProductCountBadgeClass = (count) => {
  if (!count || count === 0) return 'badge-light';
  if (count < 5) return 'badge-warning';
  if (count < 20) return 'badge-info';
  return 'badge-success';
};

// Check if category has children
const hasChildren = (categoryId) => {
  return props.allCategories.some(cat => cat.parentId === categoryId);
};

// Check if category can be deleted
const canDeleteCategory = (category) => {
  const hasProducts = (category.productCount || 0) > 0;
  const hasChildCategories = hasChildren(category.id);
  return !hasProducts && !hasChildCategories;
};

// Get tooltip text for delete button
const getDeleteTooltip = (category) => {
  const hasProducts = (category.productCount || 0) > 0;
  const hasChildCategories = hasChildren(category.id);

  if (hasProducts && hasChildCategories) {
    return 'Cannot delete: category has products and subcategories';
  } else if (hasProducts) {
    return 'Cannot delete: category has products';
  } else if (hasChildCategories) {
    return 'Cannot delete: category has subcategories';
  }
  return 'Delete category';
};


</script>

<style scoped>
/* Modern Table Container */
.modern-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Table Header */
.table-header {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-header-cell {
  padding: 1rem;
  display: flex;
  align-items: center;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.table-header-cell:last-child {
  border-right: none;
}

/* Table Body */
.table-body {
  /* Remove height restriction to display full height */
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  background: white;
}

.table-row:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table-cell {
  padding: 1rem;
  display: flex;
  align-items: center;
  border-right: 1px solid #e5e7eb;
}

.table-cell:last-child {
  border-right: none;
}

/* Category Image */
.category-image {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-img-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1.25rem;
}

/* Category Info */
.category-info {
  flex-direction: column;
  align-items: flex-start !important;
}

.category-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}

.category-slug {
  color: #6b7280;
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
  background: #f3f4f6;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  margin-bottom: 0.25rem;
}

.category-description {
  color: #9ca3af;
  font-size: 0.75rem;
  line-height: 1.4;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.badge-info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
}

.badge-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.badge-light {
  background: #f3f4f6;
  color: #6b7280;
}

/* Product Count */
.product-count-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-products-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.view-products-btn:hover {
  color: #3b82f6;
  background: #eff6ff;
}

/* SEO Indicators */
.seo-indicators {
  display: flex;
  gap: 0.25rem;
}

.seo-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.seo-indicator-success {
  background: #10b981;
  color: white;
}

.seo-indicator-info {
  background: #06b6d4;
  color: white;
}

.seo-indicator-warning {
  background: #f59e0b;
  color: white;
}

.seo-indicator-none {
  background: #e5e7eb;
  color: #9ca3af;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn-edit {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.action-btn-edit:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.action-btn-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.action-btn-delete:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Loading State */
.table-loading {
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3b82f6;
}

/* Empty State */
.table-empty {
  padding: 4rem 2rem;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #6b7280;
}

.empty-state i {
  font-size: 3rem;
  color: #d1d5db;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.empty-state p {
  margin: 0;
  color: #9ca3af;
}

/* Responsive */
@media (max-width: 768px) {
  .table-header,
  .table-row {
    font-size: 0.875rem;
  }

  .table-header-cell,
  .table-cell {
    padding: 0.75rem 0.5rem;
  }

  .category-image {
    width: 40px;
    height: 40px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}
</style>
