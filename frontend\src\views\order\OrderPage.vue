<template>
  <div class="order-page">
    <h2 class="page-title">
      Оформлення замовлення
    </h2>
    <div class="page-container">
      <div class="left-section">
        <div class="order-form">
          <!-- Recipient Section -->
          <div class="form-section">
            <h3 class="section-title">Одержувач</h3>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">Прізвище</label>
                <input 
                  type="text" 
                  v-model="recipient.firstName" 
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label class="form-label">Ім'я</label>
                <input 
                  type="text" 
                  v-model="recipient.lastName" 
                  class="form-input"
                />
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">По батькові</label>
                <input 
                  type="text" 
                  v-model="recipient.phone" 
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label class="form-label">Електронна пошта</label>
                <input 
                  type="email" 
                  v-model="recipient.email" 
                  class="form-input"
                />
              </div>
            </div>
          </div>

          <!-- Delivery Section -->
          <div class="form-section">
            <h3 class="section-title">Доставка</h3>
            
            <div class="delivery-option">
              <label class="radio-label">
                <input 
                  type="radio" 
                  v-model="delivery.method" 
                  value="pickup"
                  class="radio-input"
                />
                <span class="radio-custom"></span>
                <div class="option-content">
                  <div class="option-title">Самовивіз з магазину</div>
                  <div class="option-subtitle">Сьогодні</div>
                </div>
                <div class="option-price">Безкоштовно</div>
              </label>
            </div>

            <div class="delivery-option">
              <label class="radio-label">
                <input 
                  type="radio" 
                  v-model="delivery.method" 
                  value="nova-poshta-branch"
                  class="radio-input"
                />
                <span class="radio-custom"></span>
                <div class="option-content">
                  <div class="option-title">До відділення Нової Пошти</div>
                  <div class="option-subtitle">Сьогодні</div>
                </div>
                <div class="option-price">Безкоштовно</div>
              </label>
            </div>

            <div class="delivery-option">
              <label class="radio-label">
                <input 
                  type="radio" 
                  v-model="delivery.method" 
                  value="ukrposhta"
                  class="radio-input"
                />
                <span class="radio-custom"></span>
                <div class="option-content">
                  <div class="option-title">До відділення Укрпошти</div>
                  <div class="option-subtitle">Сьогодні</div>
                </div>
                <div class="option-price">Безкоштовно</div>
              </label>
            </div>

            <div class="delivery-option">
              <label class="radio-label">
                <input 
                  type="radio" 
                  v-model="delivery.method" 
                  value="nova-poshta-courier"
                  class="radio-input"
                />
                <span class="radio-custom"></span>
                <div class="option-content">
                  <div class="option-title">Кур'єр Нової Пошти</div>
                </div>
                <div class="option-price">За тарифами перевізника</div>
              </label>
            </div>
          </div>

          <!-- Payment Section -->
          <div class="form-section">
            <h3 class="section-title">Оплата</h3>
            
            <div class="payment-option">
              <label class="radio-label">
                <input 
                  type="radio" 
                  v-model="payment.method" 
                  value="on-delivery"
                  class="radio-input"
                />
                <span class="radio-custom"></span>
                <div class="option-content">
                  <div class="option-title">Оплата під час отримання товару</div>
                </div>
              </label>
            </div>

            <div class="payment-option">
              <label class="radio-label">
                <input 
                  type="radio" 
                  v-model="payment.method" 
                  value="prepaid"
                  class="radio-input"
                />
                <span class="radio-custom"></span>
                <div class="option-content">
                  <div class="option-title">Оплата зараз</div>
                </div>
              </label>
            </div>
          </div>

          <!-- Submit Button -->
          <button class="submit-btn" @click="proceedToPayment">
            Перейти до оплати
          </button>
        </div>
      </div>

      <div class="right-section">
        <div class="cart-info">
          <div class="cart-header">
            <h2 class="summary-title">Кошик<span class="cart-count">{{ totalItems }} товар (-и) </span><span v-if="discountToPercent != 0"  class="cart-count">(-{{discountToPercent}}%)</span></h2>
            <button class="cart-edit-btn" @click="editCart">
              Редагувати
            </button>
          </div>
          <div class="cart-summary">
            <div class="summary-row">
              <span>{{ totalItems }} товари на суму</span>
              <span class="summary-value">{{ originalTotal }} ₴</span>
            </div>
            <div class="summary-row">
              <span>Знижка</span>
              <span class="summary-value discount">{{ discount }} ₴</span>
            </div>

            <!-- Знижка від купона -->
            <CouponSummary :coupon-discount="couponDiscount" />

            <!-- Сепаратор перед вартістю доставки, якщо є знижка від купона -->
            <div v-if="couponDiscount && couponDiscount.discountAmount > 0" class="summary-divider"></div>

            <!-- <div class="summary-row">
              <span>Вартість доставки</span>
              <span class="summary-delivery" :class="{ 'free': !shippingCost || shippingCost.cost === 0 }">
                {{ shippingCost ? (shippingCost.cost === 0 ? 'Безкоштовно' : `${Math.round(shippingCost.cost)} ₴`) : 'Не вибрано' }}
              </span>
            </div> -->

            <!-- Інформація про вибране відділення -->
            <div v-if="selectedDeliveryOffice" class="summary-row delivery-office-info">
              <span>Відділення</span>
              <span class="delivery-office-details">
                {{ formatDeliveryOfficeInfo() }}
              </span>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-row total">
              <span>До оплати</span>
              <span class="summary-value">{{ finalTotal }} ₴</span>
            </div>
            <div class="summary-divider"></div>
            <button class="checkout-btn">Перейти до оформлення</button>

            <!-- Компонент для введення купона -->
            <div class="coupon-section">
              <CouponInput
                :applied-coupon="appliedCoupon"
                @coupon-applied="onCouponApplied"
                @coupon-removed="onCouponRemoved"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const recipient = ref({
  firstName: '',
  lastName: '',
  phone: '',
  email: ''
});

const delivery = ref({
  method: 'pickup'
});

const payment = ref({
  method: 'on-delivery'
});

function proceedToPayment() {
  console.log('Proceeding to payment...', {
    recipient: recipient.value,
    delivery: delivery.value,
    payment: payment.value
  });
}

function editCart() {
  // Перенаправляємо користувача до сторінки корзини для редагування
  window.location.href = '/cart';
}
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&display=swap');

.order-page {
  background-color: #ffffff;
  min-height: 100vh;
  font-family: 'Rubik', sans-serif;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 20px;
  display: flex;
  gap: 40px;
}

.left-section {
  flex: 1;
  max-width: 600px;
}

.right-section {
  flex: 1;
  max-width: 400px;
}

.page-title {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 10px;
  margin-left: 26%;
  padding-top: 20px;
  color: #1a1a1a;
  font-family: 'Rubik', sans-serif;
}

.back-arrow {
  font-size: 22px;
  color: #505050;
  cursor: pointer;
  margin-right: 7px;
}

.back-arrow:hover {
  color: #000000;
}

.order-form {
  background: white;
  padding: 20px;
}

.form-section {
  margin-bottom: 17px;
}

.form-section:last-of-type {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #1a1a1a;
  font-family: 'Rubik', sans-serif;
}

.form-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.form-group {
  flex: 1;
}

.form-input {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid #868686;
  border-radius: 20px;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #4285f4;
}

.form-input::placeholder {
  color: #9e9e9e;
  font-family: 'Rubik', sans-serif;
}

.delivery-option,
.payment-option {
  margin-bottom: 10px;
}

.radio-label {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #868686;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  font-family: 'Rubik', sans-serif;
}

.radio-label:hover {
  border-color: #dadce0;
}

.radio-input:checked + .radio-custom + .option-content,
.radio-input:checked + .radio-custom + .option-content .option-title {
  color: #1a1a1a;
}

.radio-input {
  display: none;
}

.radio-custom {
  width: 16px;
  height: 16px;
  border: 2px solid #dadce0;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.radio-input:checked + .radio-custom {
  border-color: #4285f4;
}

.radio-input:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background-color: #4285f4;
  border-radius: 50%;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 17px;
  font-weight: 400;
  color: #3c4043;
  margin-bottom: 2px;
  font-family: 'Rubik', sans-serif;
}

.option-subtitle {
  font-size: 12px;
  color: #5f6368;
  font-family: 'Rubik', sans-serif;
}

.option-price {
  font-size: 14px;
  color: #3c4043;
  font-weight: 400;
  font-family: 'Rubik', sans-serif;
  margin-left: 8px;
}

.submit-btn {
  width: 100%;
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 50px;
  font-size: 22px;
  font-weight: 400;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'Rubik', sans-serif;
}

.submit-btn:hover {
  background-color: #3367d6;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 400;
  color: #505050;
  font-family: 'Rubik', sans-serif;
}

@media (max-width: 768px) {
  .page-container {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .order-form {
    padding: 24px;
  }
  
  .page-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .cart-info {
    max-width: 100%;
  }
}

.cart-info {
  width: 100%;
  max-width: 400px;
  background-color: #fff;
  border: 1px solid #ABAAAA;
  border-radius: 10px;
  align-self: flex-start;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  margin-bottom: 20px;
}

.cart-summary {
  padding: 0 20px 20px 20px;
}

.summary-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 15px;
  color: #666;
}

.summary-value {
  font-weight: 600;
  font-size: 18px;
  color: #333;
}

.summary-value.discount {
  color: #999;
}

.summary-value.free {
  color: #4caf50;
}

.summary-delivery {
  font-size: 14px;
  color: #666;
}

.summary-delivery.free {
  color: #4caf50;
}

.delivery-office-info {
  font-size: 14px;
  color: #666;
}

.delivery-office-details {
  font-size: 13px;
  color: #555;
  text-align: right;
  max-width: 200px;
  word-wrap: break-word;
}

.summary-divider {
  height: 1px;
  background-color: #eee;
  margin: 20px 0;
}

.summary-row.total {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.cart-count {
  font-size: 16px;
  font-weight: normal;
  color: #666;
}

.cart-edit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  padding: 8px 16px;
  font-size: 20px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Rubik', sans-serif;
}

.cart-edit-btn:hover {
  text-decoration: underline;
}

.cart-edit-btn i {
  font-size: 12px;
}
</style>