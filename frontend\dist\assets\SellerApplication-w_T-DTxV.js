import{_ as C,c as d,a as s,k as g,d as i,n as R,t as f,m as U,x as n,D as r,g as b,C as N,i as A,U as x,V as D,o as m}from"./index-L-hJxM_5.js";const I={name:"SellerApplication",setup(){const{showToast:p}=D(),o=b(null),u=b(!1),t=b(!1),a=N({companyName:"",companySlug:"",companyDescription:"",contactEmail:"",contactPhone:"",addressRegion:"",addressCity:"",addressStreet:"",addressPostalCode:"",bankAccount:"",bankName:"",bankCode:"",taxId:"",additionalInfo:""}),y=()=>{a.companyName&&!a.companySlug&&(a.companySlug=a.companyName.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim())},e=l=>({Pending:"status-pending",Approved:"status-approved",Rejected:"status-rejected"})[l]||"status-pending",k=l=>({Pending:"На розгляді",Approved:"Схвалено",Rejected:"Відхилено"})[l]||l,S=l=>new Date(l).toLocaleDateString("uk-UA",{year:"numeric",month:"long",day:"numeric"}),v=async()=>{try{const l=await x.getUserSellerRequests({pageSize:1});if(l.requests&&l.requests.length>0){const c=l.requests[0];(c.status==="Pending"||c.status==="Approved")&&(o.value=c)}}catch(l){console.error("Error checking existing request:",l)}},q=async()=>{t.value=!0;try{const l={companyName:a.companyName,companySlug:a.companySlug,companyDescription:a.companyDescription,companyImageUrl:"",contactEmail:a.contactEmail,contactPhone:a.contactPhone,addressRegion:a.addressRegion,addressCity:a.addressCity,addressStreet:a.addressStreet,addressPostalCode:a.addressPostalCode,metaTitle:a.companyName,metaDescription:a.companyDescription,metaImageUrl:"",bankAccount:a.bankAccount,bankName:a.bankName,bankCode:a.bankCode,taxId:a.taxId,paymentDetails:`${a.bankName} - ${a.bankAccount}`,daySchedules:[],additionalInfo:a.additionalInfo};await x.createSellerRequest(l),p("Заявку успішно подано! Очікуйте на розгляд адміністратором.","success"),await v(),u.value=!1}catch(l){console.error("Error submitting application:",l),p("Помилка при подачі заявки. Спробуйте ще раз.","error")}finally{t.value=!1}};return A(()=>{v()}),{existingRequest:o,showForm:u,submitting:t,form:a,generateSlug:y,getStatusClass:e,getStatusText:k,formatDate:S,submitApplication:q}}},V={class:"seller-application"},P={class:"container"},w={key:0,class:"existing-request-notice"},E={class:"notice-content"},T={class:"notice-text"},X={key:1,class:"application-form"},j={class:"form-section"},L={class:"form-grid"},z={class:"form-group"},B={class:"form-group"},F={class:"form-hint"},M={class:"form-group full-width"},G={class:"form-group"},H={class:"form-group"},J={class:"form-section"},K={class:"form-grid"},O={class:"form-group"},Q={class:"form-group"},W={class:"form-group"},Y={class:"form-group"},Z={class:"form-section"},h={class:"form-grid"},_={class:"form-group"},$={class:"form-group"},oo={class:"form-group"},so={class:"form-group"},to={class:"form-section"},eo={class:"form-group"},ao={class:"form-actions"},lo=["disabled"],no={key:0,class:"fas fa-spinner fa-spin"},ro={key:1,class:"fas fa-paper-plane"};function io(p,o,u,t,a,y){return m(),d("div",V,[s("div",P,[o[38]||(o[38]=s("div",{class:"application-header"},[s("h1",{class:"application-title"},[s("i",{class:"fas fa-store"}),i(" Заявка на становлення продавцем ")]),s("p",{class:"application-subtitle"}," Заповніть форму нижче, щоб подати заявку на отримання статусу продавця на нашій платформі ")],-1)),t.existingRequest?(m(),d("div",w,[s("div",E,[o[19]||(o[19]=s("i",{class:"fas fa-info-circle"},null,-1)),s("div",T,[o[18]||(o[18]=s("h3",null,"У вас вже є активна заявка",-1)),s("p",null,[o[17]||(o[17]=i("Статус: ")),s("span",{class:R(t.getStatusClass(t.existingRequest.status))},f(t.getStatusText(t.existingRequest.status)),3)]),s("p",null,"Подано: "+f(t.formatDate(t.existingRequest.createdAt)),1)])]),t.existingRequest.status==="Rejected"?(m(),d("button",{key:0,onClick:o[0]||(o[0]=e=>t.showForm=!0),class:"btn btn-primary"}," Подати нову заявку ")):g("",!0)])):g("",!0),!t.existingRequest||t.showForm?(m(),d("div",X,[s("form",{onSubmit:o[16]||(o[16]=U((...e)=>t.submitApplication&&t.submitApplication(...e),["prevent"]))},[s("div",j,[o[25]||(o[25]=s("h2",{class:"section-title"},[s("i",{class:"fas fa-building"}),i(" Інформація про компанію ")],-1)),s("div",L,[s("div",z,[o[20]||(o[20]=s("label",{for:"companyName",class:"form-label"},"Назва компанії *",-1)),n(s("input",{id:"companyName","onUpdate:modelValue":o[1]||(o[1]=e=>t.form.companyName=e),type:"text",class:"form-input",required:"",placeholder:"Введіть назву вашої компанії"},null,512),[[r,t.form.companyName]])]),s("div",B,[o[21]||(o[21]=s("label",{for:"companySlug",class:"form-label"},"URL компанії *",-1)),n(s("input",{id:"companySlug","onUpdate:modelValue":o[2]||(o[2]=e=>t.form.companySlug=e),type:"text",class:"form-input",required:"",placeholder:"company-url",onInput:o[3]||(o[3]=(...e)=>t.generateSlug&&t.generateSlug(...e))},null,544),[[r,t.form.companySlug]]),s("small",F,"Буде використовуватися в URL: /companies/"+f(t.form.companySlug),1)]),s("div",M,[o[22]||(o[22]=s("label",{for:"companyDescription",class:"form-label"},"Опис компанії *",-1)),n(s("textarea",{id:"companyDescription","onUpdate:modelValue":o[4]||(o[4]=e=>t.form.companyDescription=e),class:"form-textarea",rows:"4",required:"",placeholder:"Розкажіть про вашу компанію, її діяльність та цілі"},null,512),[[r,t.form.companyDescription]])]),s("div",G,[o[23]||(o[23]=s("label",{for:"contactEmail",class:"form-label"},"Контактний email *",-1)),n(s("input",{id:"contactEmail","onUpdate:modelValue":o[5]||(o[5]=e=>t.form.contactEmail=e),type:"email",class:"form-input",required:"",placeholder:"<EMAIL>"},null,512),[[r,t.form.contactEmail]])]),s("div",H,[o[24]||(o[24]=s("label",{for:"contactPhone",class:"form-label"},"Контактний телефон *",-1)),n(s("input",{id:"contactPhone","onUpdate:modelValue":o[6]||(o[6]=e=>t.form.contactPhone=e),type:"tel",class:"form-input",required:"",placeholder:"+380XXXXXXXXX"},null,512),[[r,t.form.contactPhone]])])])]),s("div",J,[o[30]||(o[30]=s("h2",{class:"section-title"},[s("i",{class:"fas fa-map-marker-alt"}),i(" Адреса компанії ")],-1)),s("div",K,[s("div",O,[o[26]||(o[26]=s("label",{for:"addressRegion",class:"form-label"},"Область *",-1)),n(s("input",{id:"addressRegion","onUpdate:modelValue":o[7]||(o[7]=e=>t.form.addressRegion=e),type:"text",class:"form-input",required:"",placeholder:"Київська область"},null,512),[[r,t.form.addressRegion]])]),s("div",Q,[o[27]||(o[27]=s("label",{for:"addressCity",class:"form-label"},"Місто *",-1)),n(s("input",{id:"addressCity","onUpdate:modelValue":o[8]||(o[8]=e=>t.form.addressCity=e),type:"text",class:"form-input",required:"",placeholder:"Київ"},null,512),[[r,t.form.addressCity]])]),s("div",W,[o[28]||(o[28]=s("label",{for:"addressStreet",class:"form-label"},"Вулиця та номер будинку *",-1)),n(s("input",{id:"addressStreet","onUpdate:modelValue":o[9]||(o[9]=e=>t.form.addressStreet=e),type:"text",class:"form-input",required:"",placeholder:"вул. Хрещатик, 1"},null,512),[[r,t.form.addressStreet]])]),s("div",Y,[o[29]||(o[29]=s("label",{for:"addressPostalCode",class:"form-label"},"Поштовий індекс *",-1)),n(s("input",{id:"addressPostalCode","onUpdate:modelValue":o[10]||(o[10]=e=>t.form.addressPostalCode=e),type:"text",class:"form-input",required:"",placeholder:"01001"},null,512),[[r,t.form.addressPostalCode]])])])]),s("div",Z,[o[35]||(o[35]=s("h2",{class:"section-title"},[s("i",{class:"fas fa-credit-card"}),i(" Фінансова інформація ")],-1)),s("div",h,[s("div",_,[o[31]||(o[31]=s("label",{for:"bankAccount",class:"form-label"},"Номер банківського рахунку *",-1)),n(s("input",{id:"bankAccount","onUpdate:modelValue":o[11]||(o[11]=e=>t.form.bankAccount=e),type:"text",class:"form-input",required:"",placeholder:"*****************************"},null,512),[[r,t.form.bankAccount]])]),s("div",$,[o[32]||(o[32]=s("label",{for:"bankName",class:"form-label"},"Назва банку *",-1)),n(s("input",{id:"bankName","onUpdate:modelValue":o[12]||(o[12]=e=>t.form.bankName=e),type:"text",class:"form-input",required:"",placeholder:"ПриватБанк"},null,512),[[r,t.form.bankName]])]),s("div",oo,[o[33]||(o[33]=s("label",{for:"bankCode",class:"form-label"},"МФО банку *",-1)),n(s("input",{id:"bankCode","onUpdate:modelValue":o[13]||(o[13]=e=>t.form.bankCode=e),type:"text",class:"form-input",required:"",placeholder:"305299"},null,512),[[r,t.form.bankCode]])]),s("div",so,[o[34]||(o[34]=s("label",{for:"taxId",class:"form-label"},"Податковий номер *",-1)),n(s("input",{id:"taxId","onUpdate:modelValue":o[14]||(o[14]=e=>t.form.taxId=e),type:"text",class:"form-input",required:"",placeholder:"**********"},null,512),[[r,t.form.taxId]])])])]),s("div",to,[o[37]||(o[37]=s("h2",{class:"section-title"},[s("i",{class:"fas fa-info-circle"}),i(" Додаткова інформація ")],-1)),s("div",eo,[o[36]||(o[36]=s("label",{for:"additionalInfo",class:"form-label"},"Додаткова інформація",-1)),n(s("textarea",{id:"additionalInfo","onUpdate:modelValue":o[15]||(o[15]=e=>t.form.additionalInfo=e),class:"form-textarea",rows:"4",placeholder:"Розкажіть про ваш досвід, плани розвитку бізнесу або інші важливі деталі"},null,512),[[r,t.form.additionalInfo]])])]),s("div",ao,[s("button",{type:"submit",class:"btn btn-primary btn-large",disabled:t.submitting},[t.submitting?(m(),d("i",no)):(m(),d("i",ro)),i(" "+f(t.submitting?"Подача заявки...":"Подати заявку"),1)],8,lo)])],32)])):g("",!0)])])}const co=C(I,[["render",io],["__scopeId","data-v-e2051f53"]]);export{co as default};
