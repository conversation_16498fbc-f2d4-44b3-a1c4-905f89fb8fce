using Marketplace.Application.Responses;
using MediatR;

namespace Marketplace.Application.Queries.Security;

public record GetLogsQuery(
    string? Filter = null,
    string? OrderBy = null,
    bool Descending = false,
    int? Page = null,
    int? PageSize = null,
    string? Level = null,
    string? Category = null,
    Guid? UserId = null,
    string? UserEmail = null,
    Guid? EntityId = null,
    string? EntityType = null,
    string? IpAddress = null,
    DateTime? FromDate = null,
    DateTime? ToDate = null
    ) : IRequest<PagedResponse<LogResponse>>;
