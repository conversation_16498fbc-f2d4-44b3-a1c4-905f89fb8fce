/* Admin Components
   This file contains styles for specific admin components like tables, filters, cards, etc.
   Consolidated from admin-filter-fixes.css, admin-filter-element-fixes.css,
   admin-table-text.css, and parts of admin-layout-fixes.css
*/

/* ===== Table Styles ===== */

/* Table container */
.table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: var(--space-6, 1.5rem);
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: transparent;
  margin-bottom: var(--space-6, 1.5rem);
  color: var(--text-dark);
}

/* Table headers */
.table th {
  font-size: var(--font-sm, 0.875rem);
  font-weight: var(--font-semibold, 600);
  color: #1a1a1a !important;
  border-bottom: 2px solid var(--border-color, #374151);
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  text-align: left;
  position: sticky;
  top: 0;
  background-color: var(--card-bg, #1e293b);
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  vertical-align: middle;
}

/* Table cells */
.table td {
  padding: var(--space-4, 1rem);
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color, #374151);
  color: #1a1a1a !important;
  font-size: var(--font-base, 1rem);
  line-height: var(--line-normal, 1.5);
  font-weight: 500;
}

/* Table row hover */
.table tbody tr:hover {
  background-color: rgba(50, 115, 220, 0.1);
}

/* Table row spacing */
.table tbody tr {
  transition: background-color 0.2s ease;
}

/* Product image cell */
.image-cell {
  width: 72px;
  padding: var(--space-2, 0.5rem) !important;
  text-align: center;
}

.image-cell .image {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Product thumbnail */
.product-thumbnail,
.image.is-48x48 img {
  object-fit: cover;
  width: 48px;
  height: 48px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Product name and SKU */
.product-name {
  color: #222222 !important;
  font-weight: var(--font-medium, 500);
  margin-bottom: var(--space-1, 0.25rem);
  line-height: 1.3;
}

.product-sku,
.secondary-text {
  font-size: var(--font-xs, 0.75rem);
  color: #555555 !important;
  line-height: 1.2;
}

/* Action buttons in tables */
.table .buttons {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-2, 0.5rem);
  margin: 0;
}

.table .buttons .button {
  margin: 0;
  height: 2rem;
  width: 2rem;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Order ID and important identifiers */
.order-id,
.user-id,
.important-id {
  color: #1a1a1a !important;
  font-weight: 600;
}

/* Price and monetary values */
.price-value,
td:has(.price-value) {
  color: #1a1a1a !important;
  font-weight: 600;
}

/* Date values */
.date-value {
  color: #1a1a1a !important;
  font-weight: 500;
}

/* Empty state and loading text */
.table .empty-text,
.table .loading-text {
  color: #333333 !important;
  font-weight: 500;
}

/* ===== Filter Box Styles ===== */

/* Filter box container */
.filter-box,
.filter-section {
  padding: var(--space-5, 1.25rem);
  margin-bottom: var(--space-5, 1.25rem);
  background-color: var(--card-bg, #1e293b);
  border-radius: 8px;
  border: 1px solid var(--border-color, #374151);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Filter field container */
.filter-box .field,
.filter-section .field {
  margin-bottom: var(--space-4, 1rem);
}

/* Filter labels */
.filter-box .label,
.filter-section .label,
.filter-box .form-label,
.filter-section .form-label {
  display: block;
  margin-bottom: var(--space-2, 0.5rem);
  font-size: var(--font-sm, 0.875rem);
  font-weight: var(--font-medium, 500);
  color: var(--text-primary, #f3f4f6);
  line-height: 1.4;
}

/* Filter row */
.filter-section .columns,
.filter-box .columns,
.filter-section .row,
.filter-box .row {
  display: flex;
  flex-wrap: wrap;
  margin: -8px;
}

/* Filter column */
.filter-section .column,
.filter-box .column,
.filter-section .col,
.filter-box .col {
  padding: 8px;
  flex: 1 0 0%;
  min-width: 200px;
}

/* ===== Input Field Styles ===== */

/* All input fields in filters */
.filter-box .input,
.filter-section .input {
  height: 40px;
  padding: 8px 16px;
  font-size: 14px;
  line-height: 24px;
  color: var(--text-primary, #f3f4f6);
  background-color: var(--darker-bg, #0f172a);
  border: 1px solid var(--border-color, #374151);
  border-radius: 4px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  width: 100%;
  display: block;
}

/* Input focus state */
.filter-box .input:focus,
.filter-section .input:focus {
  border-color: var(--accent-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  outline: none;
}

/* Input placeholder */
.filter-box .input::placeholder,
.filter-section .input::placeholder {
  color: var(--text-muted, #6c757d);
  opacity: 0.8;
}

/* Search input container */
.filter-section .search-container,
.filter-box .search-container,
.filter-section .control.has-icons-left,
.filter-box .control.has-icons-left {
  position: relative;
  width: 100%;
  margin-bottom: 0;
}

/* Search input field */
.filter-section input[type="text"],
.filter-section input[type="search"],
.filter-box input[type="text"],
.filter-box input[type="search"],
.filter-section .search-input,
.filter-box .search-input {
  padding-left: 36px;
}

/* Search icon */
.filter-section .icon.is-left,
.filter-box .icon.is-left,
.filter-section .search-icon,
.filter-box .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  height: 16px;
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Date input field */
.filter-section input[type="date"],
.filter-box input[type="date"] {
  padding: 8px 16px;
}

/* ===== Select Dropdown Styles ===== */

/* Select container */
.filter-box .select,
.filter-section .select,
.filter-box .dropdown,
.filter-section .dropdown {
  display: block;
  width: 100%;
  position: relative;
}

/* Select element */
.filter-box .select select,
.filter-section .select select {
  height: 40px;
  padding: 8px 36px 8px 16px;
  font-size: 14px;
  line-height: 24px;
  color: var(--text-primary, #f3f4f6);
  background-color: var(--darker-bg, #0f172a);
  border: 1px solid var(--border-color, #374151);
  border-radius: 4px;
  width: 100%;
  display: block;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

/* Select focus state */
.filter-box .select select:focus,
.filter-section .select select:focus {
  border-color: var(--accent-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  outline: none;
}

/* Select option styling */
.filter-box .select select option,
.filter-section .select select option {
  padding: 8px;
  background-color: var(--darker-bg, #0f172a);
  color: var(--text-primary, #f3f4f6);
}

/* ===== Range Input Styles ===== */

/* Price range container */
.price-range-inputs,
.date-range-inputs {
  display: flex;
  align-items: center;
  gap: var(--space-2, 0.5rem);
}

/* Range inputs */
.price-range-inputs .input,
.date-range-inputs .input {
  text-align: center;
  padding: 8px;
  flex: 1;
}

/* Range separator */
.price-range-separator,
.date-range-separator {
  color: var(--text-muted, #6c757d);
  margin: 0 var(--space-1, 0.25rem);
  font-weight: 500;
  flex-shrink: 0;
}

/* ===== Filter Buttons ===== */

/* Filter buttons container */
.filter-buttons {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3, 0.75rem);
  margin-top: var(--space-4, 1rem);
  padding-top: var(--space-4, 1rem);
  border-top: 1px solid var(--border-color, #374151);
}

/* Filter buttons */
.filter-buttons .button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 4px;
  transition: all 0.2s ease;
  height: 40px;
}

/* Reset button */
.filter-section .button.is-light,
.filter-box .button.is-light,
.filter-section .reset-button,
.filter-box .reset-button {
  color: #f3f4f6;
  background-color: #1e293b;
  border: 1px solid #374151;
}

/* Apply button */
.filter-section .button.is-primary,
.filter-box .button.is-primary,
.filter-section .apply-button,
.filter-box .apply-button {
  color: #fff;
  background-color: #ff7700;
  border: none;
}

/* ===== Status Badge Styles ===== */

/* Status badge base */
.status-badge,
.tag,
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 12px;
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
  border-radius: 9999px;
  text-transform: capitalize;
}

/* Status colors */
.tag.is-success,
.status-badge.is-success,
.badge-success,
.status-active,
.status-paid,
.status-delivered,
.status-shipped,
.status-approved {
  background-color: var(--status-success-bg, #0d6e32);
  color: var(--status-success-text, #ffffff);
}

.tag.is-warning,
.status-badge.is-warning,
.badge-warning,
.status-pending {
  background-color: var(--status-warning-bg, #cc8a00);
  color: var(--status-warning-text, #ffffff);
}

.tag.is-danger,
.status-badge.is-danger,
.badge-danger,
.status-cancelled,
.status-rejected,
.status-refunded {
  background-color: var(--status-danger-bg, #b71c1c);
  color: var(--status-danger-text, #ffffff);
}

.tag.is-info,
.status-badge.is-info,
.badge-info,
.status-processing {
  background-color: var(--status-info-bg, #0a58ca);
  color: var(--status-info-text, #ffffff);
}

.tag.is-inactive,
.status-badge.is-inactive,
.badge-inactive,
.status-inactive {
  background-color: rgba(75, 85, 99, 0.3);
  color: #ffffff;
}

/* Light variants with better contrast */
.tag.is-success.is-light {
  background-color: var(--status-success-light-bg, #d1e7dd);
  color: var(--status-success-light-text, #0d6e32) !important;
  font-weight: 700;
}

.tag.is-warning.is-light {
  background-color: var(--status-warning-light-bg, #fff3cd);
  color: var(--status-warning-light-text, #664400) !important;
  font-weight: 700;
}

.tag.is-danger.is-light {
  background-color: var(--status-danger-light-bg, #f8d7da);
  color: var(--status-danger-light-text, #842029) !important;
  font-weight: 700;
}

.tag.is-info.is-light {
  background-color: var(--status-info-light-bg, #cfe2ff);
  color: var(--status-info-light-text, #0a3977) !important;
  font-weight: 700;
}

.tag.is-primary.is-light {
  background-color: var(--status-primary-light-bg, #cfe2ff);
  color: var(--status-primary-light-text, #084298) !important;
}

/* ===== Card Styles ===== */

/* Card container */
.card {
  background-color: var(--card-bg, #1e293b);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin-bottom: var(--space-6, 1.5rem);
  overflow: hidden;
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.card-header {
  padding: var(--space-4, 1rem) var(--space-5, 1.25rem);
  border-bottom: 1px solid var(--border-color, #374151);
  display: flex;
  align-items: center;
}

.card-header-title {
  color: #ffffff;
  font-weight: var(--font-bold, 700);
  font-size: var(--font-lg, 1.25rem);
  line-height: var(--line-tight, 1.25);
  letter-spacing: -0.01em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Custom title styles for dashboard components */
.sales-title {
  color: #4dabf7 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-title {
  color: #ffd43b !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.requests-title {
  color: #69db7c !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.orders-title {
  color: #ff922b !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-content {
  padding: var(--space-5, 1.25rem);
  color: var(--text-primary, #f3f4f6);
  font-size: var(--font-base, 1rem);
  line-height: var(--line-normal, 1.5);
}

.card-footer {
  border-top: 1px solid var(--border-color, #374151);
  padding: var(--space-4, 1rem) var(--space-5, 1.25rem);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* ===== Modal Styles ===== */

.modal-card {
  background-color: var(--card-bg, #1e293b);
  border-radius: 8px;
  overflow: hidden;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

.modal-card-head {
  background-color: var(--card-bg, #1e293b);
  border-bottom: 1px solid var(--border-color, #374151);
  padding: var(--space-4, 1rem) var(--space-5, 1.25rem);
}

.modal-card-title {
  color: var(--text-primary, #f3f4f6);
  font-size: var(--font-lg, 1.25rem);
  font-weight: var(--font-semibold, 600);
  line-height: var(--line-tight, 1.25);
}

.modal-card-body {
  background-color: var(--card-bg, #1e293b);
  color: var(--text-primary, #f3f4f6);
  padding: var(--space-5, 1.25rem);
  font-size: var(--font-base, 1rem);
  line-height: var(--line-normal, 1.5);
}

.modal-card-foot {
  background-color: var(--card-bg, #1e293b);
  border-top: 1px solid var(--border-color, #374151);
  padding: var(--space-4, 1rem) var(--space-5, 1.25rem);
  justify-content: flex-end;
}

/* Ensure modals are hidden by default - override any conflicting styles */
.modal {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1000 !important;
}

.modal.is-active {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Fix pagination display issues */
.pagination {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
  gap: 0.5rem !important;
}

.pagination-list {
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.pagination-list li {
  display: inline-block !important;
}

/* ===== Additional Pagination Styles ===== */

.pagination-link {
  background-color: var(--card-bg, #1e293b);
  color: var(--text-primary, #f3f4f6);
  border-color: var(--border-color, #374151);
  font-size: var(--font-sm, 0.875rem);
  font-weight: var(--font-medium, 500);
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  min-width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.pagination-link:hover {
  background-color: var(--darker-bg, #0f172a);
  border-color: var(--accent-color, #3b82f6);
}

.pagination-link.is-current {
  background-color: var(--status-primary-bg, #0d6efd);
  border-color: var(--status-primary-bg, #0d6efd);
  color: white;
  font-weight: var(--font-semibold, 600);
}

.pagination-ellipsis {
  color: var(--text-secondary, #9ca3af);
  padding: var(--space-2, 0.5rem);
}

/* ===== Report Generator Styles ===== */

.report-generator,
.report-section {
  background-color: var(--card-bg, #1e293b);
  border-radius: 8px;
  padding: var(--space-5, 1.25rem);
  margin-bottom: var(--space-5, 1.25rem);
}

.report-generator .columns,
.report-section .columns {
  display: flex;
  flex-wrap: wrap;
  margin: -8px;
  align-items: flex-end;
}

.report-generator .column,
.report-section .column {
  padding: 8px;
  flex: 1 0 0%;
  min-width: 200px;
}

.generate-report-button,
.button.is-info {
  height: 40px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background-color: var(--status-info-bg, #0a58ca);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
