using Marketplace.Application.Commands.Image;
using Marketplace.Domain.Services;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Universal;

/// <summary>
/// Універсальний контролер для роботи з зображеннями різних типів сутностей
/// </summary>
[ApiController]
[Route("api/universal/images")]
[Authorize(Roles = "Admin,Moderator")]
public class UniversalImageController : BasicApiController
{
    private readonly IMediator _mediator;
    private readonly IImageService _imageService;

    public UniversalImageController(IMediator mediator, IImageService imageService)
    {
        _mediator = mediator;
        _imageService = imageService;
    }

    /// <summary>
    /// Завантажити зображення для сутності
    /// </summary>
    /// <param name="entityType">Тип сутності (product, category, company, user)</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageType">Тип зображення (main, meta, avatar, collection)</param>
    /// <param name="file">Файл зображення</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат завантаження</returns>
    [HttpPost("{entityType}/{entityId}")]
    public async Task<IActionResult> UploadImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromQuery] string imageType = "main",
        [FromForm] IFormFile file = null,
        CancellationToken cancellationToken = default)
    {
        if (file == null)
        {
            return BadRequest(ApiResponse.Failure("Не вибрано файл для завантаження."));
        }

        try
        {
            var command = new UploadUniversalImageCommand(entityType, entityId, file, imageType);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(ApiResponse<FileUploadResult>.SuccessWithData(result, "Зображення успішно завантажено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка завантаження зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Завантажити множинні зображення для сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="files">Колекція файлів</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Список результатів завантаження</returns>
    [HttpPost("{entityType}/{entityId}/multiple")]
    public async Task<IActionResult> UploadMultipleImages(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromForm] IFormFileCollection files,
        CancellationToken cancellationToken = default)
    {
        if (files == null || files.Count == 0)
        {
            return BadRequest(ApiResponse.Failure("Не вибрано файлів для завантаження."));
        }

        try
        {
            var command = new UploadMultipleUniversalImagesCommand(entityType, entityId, files);
            var results = await _mediator.Send(command, cancellationToken);

            return Ok(ApiResponse<List<FileUploadResult>>.SuccessWithData(results, $"Успішно завантажено {results.Count} зображень."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка завантаження зображень: {ex.Message}"));
        }
    }

    /// <summary>
    /// Видалити зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageType">Тип зображення</param>
    /// <param name="imageId">ID зображення (для ProductImage)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат видалення</returns>
    [HttpDelete("{entityType}/{entityId}")]
    public async Task<IActionResult> DeleteImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromQuery] string imageType = "main",
        [FromQuery] Guid? imageId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new DeleteUniversalImageCommand(entityType, entityId, imageType, imageId);
            var result = await _mediator.Send(command, cancellationToken);

            return result
                ? Ok(ApiResponse.SuccessResponse("Зображення успішно видалено."))
                : NotFound(ApiResponse.Failure("Зображення не знайдено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка видалення зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Оновити зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageType">Тип зображення</param>
    /// <param name="imageId">ID зображення (для ProductImage)</param>
    /// <param name="file">Новий файл зображення</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат оновлення</returns>
    [HttpPut("{entityType}/{entityId}")]
    public async Task<IActionResult> UpdateImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromQuery] string imageType = "main",
        [FromQuery] Guid? imageId = null,
        [FromForm] IFormFile file = null,
        CancellationToken cancellationToken = default)
    {
        if (file == null)
        {
            return BadRequest(ApiResponse.Failure("Не вибрано файл для оновлення."));
        }

        try
        {
            var command = new UpdateUniversalImageCommand(entityType, entityId, file, imageType, imageId);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(ApiResponse<FileUploadResult>.SuccessWithData(result, "Зображення успішно оновлено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка оновлення зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Встановити головне зображення (для ProductImage)
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageId">ID зображення</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат операції</returns>
    [HttpPatch("{entityType}/{entityId}/main/{imageId}")]
    public async Task<IActionResult> SetMainImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromRoute] Guid imageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new SetMainUniversalImageCommand(entityType, entityId, imageId);
            var result = await _mediator.Send(command, cancellationToken);

            return result
                ? Ok(ApiResponse.SuccessResponse("Головне зображення успішно встановлено."))
                : BadRequest(ApiResponse.Failure("Не вдалося встановити головне зображення."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка встановлення головного зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Отримати всі зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Список зображень</returns>
    [HttpGet("{entityType}/{entityId}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAllImages(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var images = await _imageService.GetAllImagesAsync(entityType, entityId, cancellationToken);
            return Ok(ApiResponse<List<object>>.SuccessWithData(images));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка отримання зображень: {ex.Message}"));
        }
    }

    /// <summary>
    /// Отримати URL конкретного зображення
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="imageType">Тип зображення</param>
    /// <param name="imageId">ID зображення (для ProductImage)</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>URL зображення</returns>
    [HttpGet("{entityType}/{entityId}/url")]
    [AllowAnonymous]
    public async Task<IActionResult> GetImageUrl(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromQuery] string imageType = "main",
        [FromQuery] Guid? imageId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var imageUrl = await _imageService.GetImageUrlAsync(entityType, entityId, imageType, imageId, cancellationToken);
            
            return imageUrl != null 
                ? Ok(ApiResponse<string>.SuccessWithData(imageUrl))
                : NotFound(ApiResponse.Failure("Зображення не знайдено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка отримання URL зображення: {ex.Message}"));
        }
    }
}
