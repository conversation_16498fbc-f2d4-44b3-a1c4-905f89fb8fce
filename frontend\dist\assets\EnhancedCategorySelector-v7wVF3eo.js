import{_ as K,P as U,g as y,h as $,H as F,N as H,c as h,o as v,k,a as m,n as V,t as x,x as z,D as J,F as O,p as P,J as A,d as E,M as Q,i as j,s as G,w as q}from"./index-L-hJxM_5.js";import{p as R}from"./products-Bpq90UOX.js";const W={class:"enhanced-selector"},X={class:"admin-dropdown-wrapper"},Y=["placeholder","disabled"],Z={key:0,class:"admin-selector-loading"},_={key:1,class:"admin-dropdown"},ee=["onMousedown","onMouseenter"],te={class:"item-info"},ae={class:"item-name"},le={key:0,class:"item-subtext"},ne={key:2,class:"admin-dropdown"},re={class:"admin-dropdown-empty"},oe={key:1,class:"admin-form-error"},se={key:2,class:"admin-form-help"},ie={__name:"EnhancedSelector",props:{modelValue:{type:[String,Number],default:null},label:{type:String,default:""},placeholder:{type:String,default:"Search..."},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},errorMessage:{type:String,default:""},helpText:{type:String,default:""},items:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},itemKey:{type:[String,Function],default:"id"},itemName:{type:[String,Function],default:"name"},itemSubtext:{type:[String,Function],default:null},filterFunction:{type:Function,default:null},maxHeight:{type:String,default:"300px"},showOnFocus:{type:Boolean,default:!0},minSearchLength:{type:Number,default:0}},emits:["update:modelValue","change","select","search"],setup(r,{emit:L}){U(e=>({"48088b7e":r.maxHeight}));const t=r,C=L,i=y(""),d=y(!1),s=y(-1),b=y(null),f=y(null),I=y(null),M=$(()=>!!t.errorMessage);$(()=>!t.modelValue||!t.items.length?null:t.items.find(e=>p(e)===t.modelValue));const w=$(()=>{if(!d.value)return[];if(!i.value||i.value.length<t.minSearchLength)return t.items;const e=t.items.find(u=>p(u)===t.modelValue);if(e&&g(e)===i.value)return t.items;if(t.filterFunction)return t.filterFunction(t.items,i.value);const o=i.value.toLowerCase();return t.items.filter(u=>{const N=g(u).toLowerCase(),D=l(u);return N.includes(o)||D&&D.toLowerCase().includes(o)})}),p=e=>typeof t.itemKey=="function"?t.itemKey(e):e[t.itemKey],g=e=>typeof t.itemName=="function"?t.itemName(e):e[t.itemName],l=e=>t.itemSubtext?typeof t.itemSubtext=="function"?t.itemSubtext(e):e[t.itemSubtext]:null,a=e=>{const o=p(e);C("update:modelValue",o),C("change",o),C("select",e),i.value=g(e),d.value=!1,s.value=-1},n=()=>{d.value=!0;const e=t.items.find(o=>p(o)===t.modelValue);e&&(g(e),i.value)},c=()=>{setTimeout(()=>{d.value=!1,s.value=-1},200)},S=()=>{d.value=!0,s.value=-1,C("search",i.value)},B=e=>{if(d.value)switch(e.key){case"ArrowDown":e.preventDefault(),s.value=Math.min(s.value+1,w.value.length-1),T();break;case"ArrowUp":e.preventDefault(),s.value=Math.max(s.value-1,-1),T();break;case"Enter":e.preventDefault(),s.value>=0&&w.value[s.value]&&a(w.value[s.value]);break;case"Escape":d.value=!1,s.value=-1;break}},T=()=>{Q(()=>{if(f.value&&s.value>=0){const e=f.value.children;e[s.value]&&e[s.value].scrollIntoView({block:"nearest"})}})};return F(()=>t.modelValue,async e=>{if(e&&t.items.length>0){const o=t.items.find(u=>p(u)===e);o&&(i.value=g(o))}else i.value=""},{immediate:!0}),F(()=>t.items,e=>{if(t.modelValue&&e.length>0){const o=e.find(u=>p(u)===t.modelValue);o&&(i.value=g(o))}},{immediate:!0}),F(i,()=>{s.value=-1}),H(()=>{I.value&&I.value.abort()}),(e,o)=>(v(),h("div",W,[r.label?(v(),h("label",{key:0,class:V(["admin-form-label",{"admin-form-label--required":r.required}])},x(r.label),3)):k("",!0),m("div",{class:V(["admin-selector-container",{"admin-selector-container--error":M.value}])},[m("div",X,[z(m("input",{ref_key:"searchInput",ref:b,"onUpdate:modelValue":o[0]||(o[0]=u=>i.value=u),type:"text",class:V(["admin-form-input",{"admin-form-input--error":M.value}]),placeholder:r.placeholder||"Search...",disabled:r.disabled||r.loading,onFocus:n,onClick:n,onBlur:c,onKeydown:B,onInput:S,autocomplete:"off"},null,42,Y),[[J,i.value]]),r.loading?(v(),h("div",Z,o[1]||(o[1]=[m("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):k("",!0),d.value&&w.value.length>0?(v(),h("div",_,[m("div",{class:"admin-dropdown-list",ref_key:"dropdownList",ref:f},[(v(!0),h(O,null,P(w.value,(u,N)=>(v(),h("div",{key:p(u),class:V(["admin-dropdown-item",{"admin-dropdown-item--highlighted":N===s.value}]),onMousedown:D=>a(u),onMouseenter:D=>s.value=N},[A(e.$slots,"item",{item:u,index:N},()=>[m("div",te,[m("span",ae,x(g(u)),1),l(u)?(v(),h("span",le,x(l(u)),1)):k("",!0)])])],42,ee))),128))],512)])):d.value&&i.value&&w.value.length===0?(v(),h("div",ne,[m("div",re,[A(e.$slots,"empty",{},()=>[o[2]||(o[2]=E(" No items found "))])])])):k("",!0)])],2),r.errorMessage?(v(),h("div",oe,[o[3]||(o[3]=m("i",{class:"fas fa-exclamation-triangle"},null,-1)),E(" "+x(r.errorMessage),1)])):k("",!0),r.helpText?(v(),h("div",se,x(r.helpText),1)):k("",!0)]))}},ue=K(ie,[["__scopeId","data-v-346841b6"]]),de={class:"category-info"},ce={class:"category-name"},me={__name:"EnhancedCategorySelector",props:{modelValue:{type:[String,Number],default:null},label:{type:String,default:"Category"},placeholder:{type:String,default:"Search categories..."},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},errorMessage:{type:String,default:""},helpText:{type:String,default:""}},emits:["update:modelValue","change","select"],setup(r,{expose:L,emit:t}){const C=r,i=t,d=y([]),s=y(!1),b=y(null),f={data:null,timestamp:null,ttl:5*60*1e3},I=l=>l.parentName?`${l.parentName} → ${l.name}`:l.name,M=l=>l.parentId?"Subcategory":"Main",w=(l,a)=>{if(!a)return l;const n=a.toLowerCase();return l.filter(c=>c.name.toLowerCase().includes(n)||c.slug&&c.slug.toLowerCase().includes(n)||c.parentName&&c.parentName.toLowerCase().includes(n))},p=async(l=!1)=>{if(!l&&f.data&&f.timestamp&&Date.now()-f.timestamp<f.ttl){d.value=f.data;return}try{s.value=!0,b.value&&b.value.abort(),b.value=new AbortController;const a=await R.getCategories({pageSize:1e3,signal:b.value.signal});let n=[];Array.isArray(a)?n=a:a&&Array.isArray(a.data)?n=a.data:n=[],n.sort((c,S)=>{if(!c.parentId&&S.parentId)return-1;if(c.parentId&&!S.parentId)return 1;if(c.parentId&&S.parentId){const B=(c.parentName||"").localeCompare(S.parentName||"");if(B!==0)return B}return c.name.localeCompare(S.name)}),d.value=n,f.data=n,f.timestamp=Date.now()}catch(a){a.name!=="AbortError"&&(console.error("Error loading categories:",a),d.value=[])}finally{s.value=!1}},g=l=>{i("search",l)};return F(()=>C.modelValue,async l=>{l&&d.value.length===0&&await p()},{immediate:!0}),j(async()=>{await p()}),H(()=>{b.value&&b.value.abort()}),L({loadCategories:p,categories:d}),(l,a)=>(v(),G(ue,{"model-value":r.modelValue,"onUpdate:modelValue":a[0]||(a[0]=n=>l.$emit("update:modelValue",n)),onChange:a[1]||(a[1]=n=>l.$emit("change",n)),onSelect:a[2]||(a[2]=n=>l.$emit("select",n)),onSearch:g,label:r.label,placeholder:r.placeholder||"Search categories...",required:r.required,disabled:r.disabled,"error-message":r.errorMessage,"help-text":r.helpText,items:d.value,loading:s.value,"item-key":"id","item-name":I,"item-subtext":M,"filter-function":w,"max-height":"300px","show-on-focus":!0,"min-search-length":0},{item:q(({item:n})=>[m("div",de,[m("span",ce,x(I(n)),1),m("span",{class:V(["category-type",n.parentId?"subcategory":"main"])},x(n.parentId?"Subcategory":"Main"),3)])]),empty:q(()=>a[3]||(a[3]=[E(" No categories found ")])),_:1},8,["model-value","label","placeholder","required","disabled","error-message","help-text","items","loading"]))}},ve=K(me,[["__scopeId","data-v-554aeb7f"]]);export{ve as E,ue as a};
