using MediatR;
using Microsoft.Extensions.Logging;
using Marketplace.Infrastructure.Services.Interfaces;
using Marketplace.Domain.Repositories;
using Microsoft.Extensions.Configuration;

namespace Marketplace.Application.Commands.SellerRequest;

public class UploadSellerRequestImageCommandHandler : IRequestHandler<UploadSellerRequestImageCommand, string>
{
    private readonly ISellerRequestRepository _sellerRequestRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<UploadSellerRequestImageCommandHandler> _logger;
    private readonly string _baseUrl;

    public UploadSellerRequestImageCommandHandler(
        ISellerRequestRepository sellerRequestRepository,
        IFileStorageService fileStorageService,
        ILogger<UploadSellerRequestImageCommandHandler> logger,
        IConfiguration configuration)
    {
        _sellerRequestRepository = sellerRequestRepository;
        _fileStorageService = fileStorageService;
        _logger = logger;
        _baseUrl = configuration["BaseUrl"] ?? "https://localhost:7000";
    }

    public async Task<string> Handle(UploadSellerRequestImageCommand request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи існує заявка
        var sellerRequest = await _sellerRequestRepository.GetByIdAsync(request.SellerRequestId, cancellationToken);
        if (sellerRequest == null)
        {
            throw new InvalidOperationException($"Заявку з ID {request.SellerRequestId} не знайдено.");
        }

        // Перевіряємо, чи файл існує
        if (request.File == null || request.File.Length == 0)
            throw new InvalidOperationException("Файл не вибрано або він порожній.");

        // Перевіряємо тип файлу
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
        var extension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(extension))
            throw new InvalidOperationException("Недопустимий тип файлу. Дозволені типи: .jpg, .jpeg, .png, .gif, .webp");

        // Перевіряємо розмір файлу (5MB)
        if (request.File.Length > 5 * 1024 * 1024)
            throw new InvalidOperationException("Розмір файлу не повинен перевищувати 5MB.");

        // Визначаємо папку для збереження
        var folderPath = request.ImageType.ToLower() switch
        {
            "logo" => $"temp/seller-requests/{request.SellerRequestId}/logo",
            "meta" => $"temp/seller-requests/{request.SellerRequestId}/meta",
            _ => throw new InvalidOperationException($"Невідомий тип зображення: {request.ImageType}")
        };

        // Генеруємо ім'я файлу
        var fileName = $"{request.ImageType}_{DateTime.UtcNow:yyyyMMdd_HHmmss}{extension}";

        try
        {
            // Зберігаємо файл
            var relativePath = await _fileStorageService.SaveFileAsync(
                request.File,
                folderPath,
                fileName,
                cancellationToken);

            var fullUrl = $"{_baseUrl}/uploads/{relativePath}";

            _logger.LogInformation("Successfully uploaded {ImageType} image for seller request {RequestId}: {Url}",
                request.ImageType, request.SellerRequestId, fullUrl);

            return fullUrl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading {ImageType} image for seller request {RequestId}",
                request.ImageType, request.SellerRequestId);
            throw;
        }
    }
}
