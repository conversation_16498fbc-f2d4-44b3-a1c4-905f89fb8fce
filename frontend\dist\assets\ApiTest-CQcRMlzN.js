import{p as g}from"./products-Bpq90UOX.js";import{_ as p,g as i,c as l,a as s,k as u,t as c,d as f,I as P,o as n}from"./index-L-hJxM_5.js";const _={class:"api-test"},m={key:0,class:"notification is-info"},y={key:1,class:"content"},A={key:2,class:"notification is-danger"},I={__name:"ApiTest",setup(k){const r=i(!1),t=i(null),o=i(null),d=async()=>{r.value=!0,o.value=null,t.value=null;try{const e=await g.getProducts({page:1,pageSize:5});t.value=e,console.log("Products API test result:",e)}catch(e){o.value=e.message,console.error("Products API test error:",e)}finally{r.value=!1}},v=async()=>{r.value=!0,o.value=null,t.value=null;try{const e=await P.getCategories();t.value=e,console.log("Categories API test result:",e)}catch(e){o.value=e.message,console.error("Categories API test error:",e)}finally{r.value=!1}};return(e,a)=>(n(),l("div",_,[a[2]||(a[2]=s("h1",{class:"title"},"API Test Page",-1)),s("div",{class:"buttons"},[s("button",{class:"button is-primary",onClick:d},"Test Products API"),s("button",{class:"button is-info",onClick:v},"Test Categories API")]),r.value?(n(),l("div",m," Loading... ")):u("",!0),t.value?(n(),l("div",y,[a[0]||(a[0]=s("h3",null,"API Response:",-1)),s("pre",null,c(JSON.stringify(t.value,null,2)),1)])):u("",!0),o.value?(n(),l("div",A,[a[1]||(a[1]=s("strong",null,"Error:",-1)),f(" "+c(o.value),1)])):u("",!0)]))}},T=p(I,[["__scopeId","data-v-52b580ed"]]);export{T as default};
