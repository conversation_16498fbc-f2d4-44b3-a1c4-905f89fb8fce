import{_ as z,g as i,H as de,c as g,o as u,a as e,x as y,D as A,n as N,t as c,m as re,h as B,i as ue,b as R,s as T,k as q,F as ce,p as ve,W as pe,w as me,d as ge,r as fe,e as he}from"./index-L-hJxM_5.js";import{a as x}from"./addresses-DL07ASCS.js";import{S as be}from"./SearchAndFilters-B3kez0yT.js";import{P as ye}from"./Pagination-DX2plTiq.js";const Ce={class:"field"},_e={class:"control"},ke={class:"field"},Ae={class:"control"},xe={class:"field"},Se={class:"control"},Ue={class:"field"},$e={class:"control"},Ve={class:"field"},Fe={class:"control"},Ie={class:"field is-grouped"},we={class:"control"},De=["disabled"],Ne={class:"control"},Oe=["disabled"],Ee={__name:"AddressForm",props:{address:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["submit","cancel"],setup(h,{emit:C}){const w=h,v=C,o=i({userId:"",region:"",city:"",street:"",postalCode:""});de(()=>w.address,a=>{var t,d,r,b;a?o.value={userId:a.userId||"",region:((t=a.addressVO)==null?void 0:t.region)||a.region||"",city:((d=a.addressVO)==null?void 0:d.city)||a.city||"",street:((r=a.addressVO)==null?void 0:r.street)||a.street||"",postalCode:((b=a.addressVO)==null?void 0:b.postalCode)||a.postalCode||""}:o.value={userId:"",region:"",city:"",street:"",postalCode:""}},{immediate:!0});const p=()=>{const a={...o.value,addressVO:{region:o.value.region,city:o.value.city,street:o.value.street,postalCode:o.value.postalCode}};a.userId||delete a.userId,v("submit",a)};return(a,t)=>(u(),g("form",{onSubmit:re(p,["prevent"])},[e("div",Ce,[t[6]||(t[6]=e("label",{class:"label"},"User ID (Optional)",-1)),e("div",_e,[y(e("input",{type:"text",class:"input","onUpdate:modelValue":t[0]||(t[0]=d=>o.value.userId=d),placeholder:"Enter user ID or leave empty"},null,512),[[A,o.value.userId]])])]),e("div",ke,[t[7]||(t[7]=e("label",{class:"label"},"Region *",-1)),e("div",Ae,[y(e("input",{type:"text",class:"input","onUpdate:modelValue":t[1]||(t[1]=d=>o.value.region=d),placeholder:"Enter region",required:""},null,512),[[A,o.value.region]])])]),e("div",xe,[t[8]||(t[8]=e("label",{class:"label"},"City *",-1)),e("div",Se,[y(e("input",{type:"text",class:"input","onUpdate:modelValue":t[2]||(t[2]=d=>o.value.city=d),placeholder:"Enter city",required:""},null,512),[[A,o.value.city]])])]),e("div",Ue,[t[9]||(t[9]=e("label",{class:"label"},"Street *",-1)),e("div",$e,[y(e("input",{type:"text",class:"input","onUpdate:modelValue":t[3]||(t[3]=d=>o.value.street=d),placeholder:"Enter street address",required:""},null,512),[[A,o.value.street]])])]),e("div",Ve,[t[10]||(t[10]=e("label",{class:"label"},"Postal Code *",-1)),e("div",Fe,[y(e("input",{type:"text",class:"input","onUpdate:modelValue":t[4]||(t[4]=d=>o.value.postalCode=d),placeholder:"Enter postal code",required:""},null,512),[[A,o.value.postalCode]])])]),e("div",Ie,[e("div",we,[e("button",{type:"submit",class:N(["button is-primary",{"is-loading":h.loading}]),disabled:h.loading},c(h.address?"Update":"Create")+" Address ",11,De)]),e("div",Ne,[e("button",{type:"button",class:"button is-light",onClick:t[5]||(t[5]=d=>a.$emit("cancel")),disabled:h.loading}," Cancel ",8,Oe)])])],32))}},Pe=z(Ee,[["__scopeId","data-v-d0577040"]]),Le={class:"address-list"},Me={class:"level"},Be={class:"level-right"},Re={class:"level-item"},Te={class:"buttons"},qe=["disabled"],ze={key:0,class:"has-text-centered py-6"},He={key:1,class:"notification is-danger"},je={key:2,class:"card"},Qe={class:"card-content"},We={class:"table is-fullwidth is-hoverable"},Ge={class:"checkbox"},Je=["checked","indeterminate"],Ke={class:"checkbox"},Xe=["value"],Ye={key:1,class:"has-text-grey"},Ze={class:"buttons"},es=["onClick","disabled"],ss=["onClick","disabled"],ts=["onClick","disabled"],ls={class:"modal-card"},as={class:"modal-card-head"},os={class:"modal-card-title"},ns={class:"modal-card-body"},is={__name:"AddressList",setup(h){const C=he(),w=(l,s)=>{let k;return function(...V){const F=()=>{clearTimeout(k),l(...V)};clearTimeout(k),k=setTimeout(F,s)}},v=i([]),o=i(!1),p=i(null),a=i(!1),t=i(!1),d=i(""),r=i([]),b=i(!0),O=i(!1),S=i(!1),U=i(null),_=i({region:"",hasUser:""}),H=i({region:{type:"text",label:"Region",placeholder:"Filter by region..."},hasUser:{type:"select",label:"User Status",options:[{value:"true",label:"Has User"},{value:"false",label:"No User"}]}}),j=i("createdAt"),Q=i("desc"),f=i(1),D=i(1),E=i(0),W=i(15),P=B(()=>v.value.length>0&&r.value.length===v.value.length),G=B(()=>r.value.length>0&&r.value.length<v.value.length),m=async()=>{o.value=!0,p.value=null;try{const l={filter:d.value,region:_.value.region,hasUser:_.value.hasUser,orderBy:j.value,descending:Q.value==="desc",page:f.value,pageSize:W.value},s=await x.getAddresses(l);v.value=s.data||[],f.value=s.currentPage||1,D.value=s.totalPages||1,E.value=s.totalItems||0,r.value=[]}catch(l){p.value=l.message||"Failed to load addresses",v.value=[]}finally{o.value=!1,b.value=!1}},J=l=>{d.value=l,f.value=1,Z()},K=()=>{f.value=1,m()},X=()=>{d.value="",_.value.region="",_.value.hasUser="",f.value=1,m()},Y=l=>{f.value=l,m()},Z=w(m,300),ee=()=>{P.value?r.value=[]:r.value=v.value.map(l=>l.id)},se=()=>{C.push({name:"AdminAddressCreate"})},te=l=>{C.push({name:"AdminAddressDetail",params:{id:l}})},le=l=>{C.push({name:"AdminAddressEdit",params:{id:l}})},$=()=>{O.value=!1,S.value=!1,U.value=null},ae=async l=>{t.value=!0;try{S.value&&U.value?await x.updateAddress(U.value.id,l):await x.createAddress(l),$(),await m()}catch(s){p.value=s.message||"Failed to save address"}finally{t.value=!1}},oe=async l=>{if(confirm("Are you sure you want to delete this address?")){a.value=!0;try{await x.deleteAddress(l),await m()}catch(s){p.value=s.message||"Failed to delete address"}finally{a.value=!1}}},ne=async()=>{if(confirm(`Are you sure you want to delete ${r.value.length} selected addresses?`)){a.value=!0;try{for(const l of r.value)await x.deleteAddress(l);await m()}catch(l){p.value=l.message||"Failed to delete addresses"}finally{a.value=!1}}},ie=l=>l?new Date(l).toLocaleDateString():"N/A";return ue(()=>{m()}),(l,s)=>{const k=fe("router-link");return u(),g("div",Le,[e("div",Me,[s[4]||(s[4]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Address Management")])],-1)),e("div",Be,[e("div",Re,[e("div",Te,[e("button",{class:"button is-primary",onClick:se},s[2]||(s[2]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add Address",-1)])),r.value.length>0?(u(),g("button",{key:0,class:"button is-danger",onClick:ne,disabled:r.value.length===0||a.value},[s[3]||(s[3]=e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)),e("span",null,"Delete Selected ("+c(r.value.length)+")",1)],8,qe)):q("",!0)])])])]),R(be,{search:d.value,"onUpdate:search":s[0]||(s[0]=n=>d.value=n),filters:_.value,"filter-fields":H.value,"search-label":"Search Addresses","search-placeholder":"Search by region, city, street, or user name...","search-column-class":"is-4","total-items":E.value,"item-name":"addresses",loading:o.value,onSearchChanged:J,onFilterChanged:K,onResetFilters:X},null,8,["search","filters","filter-fields","total-items","loading"]),o.value&&b.value?(u(),g("div",ze,s[5]||(s[5]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading addresses...",-1)]))):p.value?(u(),g("div",He,[e("p",null,c(p.value),1),e("button",{class:"button is-light mt-2",onClick:m},s[6]||(s[6]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(u(),g("div",je,[e("div",Qe,[e("div",{class:N(["table-container",{"is-loading":o.value&&!b.value}])},[e("table",We,[e("thead",null,[e("tr",null,[e("th",null,[e("label",Ge,[e("input",{type:"checkbox",onChange:ee,checked:P.value,indeterminate:G.value},null,40,Je)])]),s[7]||(s[7]=e("th",null,"User",-1)),s[8]||(s[8]=e("th",null,"Region",-1)),s[9]||(s[9]=e("th",null,"City",-1)),s[10]||(s[10]=e("th",null,"Street",-1)),s[11]||(s[11]=e("th",null,"Postal Code",-1)),s[12]||(s[12]=e("th",null,"Created",-1)),s[13]||(s[13]=e("th",null,"Actions",-1))])]),e("tbody",null,[(u(!0),g(ce,null,ve(v.value,n=>{var V,F,L,M;return u(),g("tr",{key:n.id},[e("td",null,[e("label",Ke,[y(e("input",{type:"checkbox",value:n.id,"onUpdate:modelValue":s[1]||(s[1]=I=>r.value=I)},null,8,Xe),[[pe,r.value]])])]),e("td",null,[n.userId?(u(),T(k,{key:0,to:{name:"AdminUserDetail",params:{id:n.userId}},class:"has-text-link"},{default:me(()=>[ge(c(n.userName||"Unknown User"),1)]),_:2},1032,["to"])):(u(),g("span",Ye,"No User"))]),e("td",null,c(((V=n.addressVO)==null?void 0:V.region)||n.region||"N/A"),1),e("td",null,c(((F=n.addressVO)==null?void 0:F.city)||n.city||"N/A"),1),e("td",null,c(((L=n.addressVO)==null?void 0:L.street)||n.street||"N/A"),1),e("td",null,c(((M=n.addressVO)==null?void 0:M.postalCode)||n.postalCode||"N/A"),1),e("td",null,c(ie(n.createdAt)),1),e("td",null,[e("div",Ze,[e("button",{class:"button is-small is-primary",onClick:I=>te(n.id),disabled:a.value},s[14]||(s[14]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)]),8,es),e("button",{class:"button is-small is-info",onClick:I=>le(n.id),disabled:a.value},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-edit"})],-1),e("span",null,"Edit",-1)]),8,ss),e("button",{class:"button is-small is-danger",onClick:I=>oe(n.id),disabled:a.value},s[16]||(s[16]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]),8,ts)])])])}),128))])])],2)])])),D.value>1?(u(),T(ye,{key:3,"current-page":f.value,"total-pages":D.value,onPageChanged:Y},null,8,["current-page","total-pages"])):q("",!0),e("div",{class:N(["modal",{"is-active":O.value||S.value}])},[e("div",{class:"modal-background",onClick:$}),e("div",ls,[e("header",as,[e("p",os,c(S.value?"Edit Address":"Create New Address"),1),e("button",{class:"delete",onClick:$})]),e("section",ns,[R(Pe,{address:U.value,loading:t.value,onSubmit:ae,onCancel:$},null,8,["address","loading"])])])],2)])}}},vs=z(is,[["__scopeId","data-v-3841a761"]]);export{vs as default};
