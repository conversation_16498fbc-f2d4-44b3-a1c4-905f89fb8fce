using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Обробник універсальної команди завантаження зображення
/// </summary>
public class UploadUniversalImageCommandHandler : IRequestHandler<UploadUniversalImageCommand, FileUploadResult>
{
    private readonly IImageService _imageService;
    private readonly ILogger<UploadUniversalImageCommandHandler> _logger;

    public UploadUniversalImageCommandHandler(
        IImageService imageService,
        ILogger<UploadUniversalImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadUniversalImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Uploading {request.ImageType} image for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.UploadImageAsync(
                request.EntityType,
                request.EntityId,
                request.File,
                request.ImageType,
                cancellationToken);

            _logger.LogInformation($"Successfully uploaded {request.ImageType} image for {request.EntityType} {request.EntityId}");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to upload {request.ImageType} image for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
