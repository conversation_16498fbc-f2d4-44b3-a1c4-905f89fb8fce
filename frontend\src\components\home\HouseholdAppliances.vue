<template>
  <section class="household-appliances-section">
    <div class="section-header">
      <h2 class="section-title">Побутова техніка</h2>
      <a href="/catalog/category-2" class="view-all">Більше товарів з категорії</a>
    </div>
    <ProductGrid
      :products="products"
      :loading="loading"
      :error="error"
      :show-actions="showActions"
      :grid-columns="4"
      :page-size="pageSize"
      :category-slug="categorySlug"
      :empty-message="'Побутова техніка поки що недоступна'"
      @product-added-to-cart="$emit('product-added-to-cart', $event)"
      @product-added-to-wishlist="$emit('product-added-to-wishlist', $event)"
      @retry="$emit('retry')"
    />
  </section>
</template>

<script>
import ProductGrid from '@/components/common/ProductGrid.vue';

export default {
  name: 'HouseholdAppliances',
  components: {
    ProductGrid
  },
  props: {
    products: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: null
    },
    showActions: {
      type: Boolean,
      default: true
    },
    pageSize: {
      type: Number,
      default: 8
    },
    categorySlug: {
      type: String,
      default: 'household-appliances'
    }
  }
}
</script>

<style scoped>
.household-appliances-section {
  margin-bottom: 48px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #000;
}

.view-all {
  color: #0066cc;
  text-decoration: none;
  font-size: 14px;
}

.view-all:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .section-title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .view-all {
    margin-top: 8px;
  }
}
</style>