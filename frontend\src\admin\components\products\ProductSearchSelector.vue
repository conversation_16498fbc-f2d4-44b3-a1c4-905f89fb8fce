<template>
  <div class="product-search-selector">
    <div class="search-header">
      <h4 class="search-title">Search Products</h4>
      <p class="search-description">Search and select a product to add to the order</p>
    </div>

    <!-- Search Input -->
    <div class="search-input-wrapper">
      <div class="search-input-group">
        <input 
          class="search-input" 
          type="text" 
          placeholder="Search products by name, SKU, or description..."
          v-model="searchQuery"
          @input="debouncedSearch"
          @focus="showResults = true">
        <div class="search-input-icon">
          <i class="fas fa-search"></i>
        </div>
      </div>
      <button 
        v-if="searchQuery"
        class="search-clear-btn"
        @click="clearSearch"
        title="Clear search">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Search Results -->
    <div v-if="showResults && searchQuery" class="search-results">
      <!-- Loading -->
      <div v-if="searching" class="search-loading">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p class="loading-text">Searching products...</p>
      </div>

      <!-- No Results -->
      <div v-else-if="searchResults.length === 0" class="no-results">
        <div class="no-results-icon">
          <i class="fas fa-search"></i>
        </div>
        <h4 class="no-results-title">No Products Found</h4>
        <p class="no-results-message">Try adjusting your search terms</p>
      </div>

      <!-- Results List -->
      <div v-else class="results-list">
        <div 
          v-for="product in searchResults" 
          :key="product.id"
          class="result-item"
          :class="{ 'result-item-selected': selectedProduct?.id === product.id }"
          @click="selectProduct(product)">
          
          <div class="product-image" v-if="product.image">
            <img :src="product.image" :alt="product.name" @error="handleImageError">
          </div>
          <div class="product-placeholder" v-else>
            <i class="fas fa-box"></i>
          </div>
          
          <div class="product-info">
            <h5 class="product-name">{{ product.name }}</h5>
            <p class="product-sku" v-if="product.sku">SKU: {{ product.sku }}</p>
            <p class="product-price">{{ formatCurrency(product.price) }}</p>
            <p class="product-stock" :class="getStockClass(product.stock)">
              Stock: {{ product.stock || 0 }}
            </p>
          </div>
          
          <div class="product-actions">
            <button 
              class="select-btn"
              :class="{ 'select-btn-selected': selectedProduct?.id === product.id }">
              <i class="fas fa-check" v-if="selectedProduct?.id === product.id"></i>
              <i class="fas fa-plus" v-else></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="searchResults.length > 0 && totalPages > 1" class="search-pagination">
        <button 
          class="pagination-btn"
          :disabled="currentPage <= 1"
          @click="goToPage(currentPage - 1)">
          <i class="fas fa-chevron-left"></i>
          Previous
        </button>
        
        <span class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        
        <button 
          class="pagination-btn"
          :disabled="currentPage >= totalPages"
          @click="goToPage(currentPage + 1)">
          Next
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Selected Product -->
    <div v-if="selectedProduct" class="selected-product">
      <h4 class="selected-title">Selected Product</h4>
      <div class="selected-item">
        <div class="product-image" v-if="selectedProduct.image">
          <img :src="selectedProduct.image" :alt="selectedProduct.name">
        </div>
        <div class="product-placeholder" v-else>
          <i class="fas fa-box"></i>
        </div>
        
        <div class="product-info">
          <h5 class="product-name">{{ selectedProduct.name }}</h5>
          <p class="product-sku" v-if="selectedProduct.sku">SKU: {{ selectedProduct.sku }}</p>
          <p class="product-price">{{ formatCurrency(selectedProduct.price) }}</p>
        </div>
        
        <button class="remove-btn" @click="clearSelection" title="Remove selection">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { productsService } from '@/admin/services/products';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'product-selected']);

// Reactive data
const searchQuery = ref('');
const searching = ref(false);
const showResults = ref(false);
const searchResults = ref([]);
const selectedProduct = ref(props.modelValue);
const currentPage = ref(1);
const totalPages = ref(1);
const pageSize = 10;

// Debounced search
let searchTimeout = null;

// Computed properties
const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    if (searchQuery.value.trim()) {
      performSearch();
    } else {
      searchResults.value = [];
    }
  }, 300);
};

// Methods
const performSearch = async (page = 1) => {
  if (!searchQuery.value.trim()) return;

  searching.value = true;
  try {
    const response = await productsService.searchProducts({
      query: searchQuery.value.trim(),
      page: page,
      pageSize: pageSize
    });

    searchResults.value = response.data || [];
    currentPage.value = response.currentPage || 1;
    totalPages.value = response.totalPages || 1;

  } catch (error) {
    console.error('Error searching products:', error);
    searchResults.value = [];
  } finally {
    searching.value = false;
  }
};

const selectProduct = (product) => {
  selectedProduct.value = product;
  emit('update:modelValue', product);
  emit('product-selected', product);
  showResults.value = false;
};

const clearSelection = () => {
  selectedProduct.value = null;
  emit('update:modelValue', null);
  emit('product-selected', null);
};

const clearSearch = () => {
  searchQuery.value = '';
  searchResults.value = [];
  showResults.value = false;
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    performSearch(page);
  }
};

// Utility methods
const formatCurrency = (amount) => {
  if (amount == null) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(amount);
};

const getStockClass = (stock) => {
  if (stock === 0) return 'stock-danger';
  if (stock < 10) return 'stock-warning';
  return 'stock-success';
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  selectedProduct.value = newValue;
});

// Close results when clicking outside
const handleClickOutside = (event) => {
  if (!event.target.closest('.product-search-selector')) {
    showResults.value = false;
  }
};

// Add event listener for clicking outside
if (typeof window !== 'undefined') {
  document.addEventListener('click', handleClickOutside);
}
</script>

<style scoped>
/* Base Styles */
.product-search-selector {
  width: 100%;
}

.search-header {
  margin-bottom: 1rem;
}

.search-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.search-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Search Input */
.search-input-wrapper {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.search-input-group {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
}

.search-clear-btn {
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-clear-btn:hover {
  background: #4b5563;
}

/* Search Results */
.search-results {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-spinner {
  font-size: 1.5rem;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.loading-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.no-results-icon {
  font-size: 2rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.no-results-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.no-results-message {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Results List */
.results-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.result-item:hover {
  background: #f8fafc;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item-selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.product-sku {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0 0 0.25rem 0;
}

.product-price {
  font-size: 0.875rem;
  font-weight: 500;
  color: #059669;
  margin: 0 0 0.25rem 0;
}

.product-stock {
  font-size: 0.75rem;
  margin: 0;
}

.stock-success {
  color: #059669;
}

.stock-warning {
  color: #d97706;
}

.stock-danger {
  color: #dc2626;
}

.product-actions {
  flex-shrink: 0;
}

.select-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-btn:hover {
  background: #2563eb;
}

.select-btn-selected {
  background: #10b981;
}

/* Pagination */
.search-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.pagination-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.pagination-btn:hover {
  background: #2563eb;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Selected Product */
.selected-product {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1rem;
}

.selected-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-btn:hover {
  background: #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
  .result-item {
    padding: 0.75rem;
  }
  
  .product-image,
  .product-placeholder {
    width: 40px;
    height: 40px;
  }
  
  .search-pagination {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .pagination-info {
    order: -1;
  }
}
</style>
