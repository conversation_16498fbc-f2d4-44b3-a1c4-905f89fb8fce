<template>
  <div class="admin-settings">
    <h1 class="title">Settings</h1>

    <!-- Loading indicator -->
    <div v-if="loading" class="has-text-centered" style="padding: 2rem;">
      <div class="is-size-4">
        <i class="fas fa-spinner fa-spin"></i>
        Loading settings...
      </div>
    </div>

    <div v-else>
      <!-- Header with actions -->
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <h1 class="title">System Settings</h1>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons">
              <button class="button is-info" @click="validateAllSettings" :disabled="validating">
                <span class="icon"><i class="fas fa-check-circle"></i></span>
                <span>Validate All</span>
              </button>
              <button class="button is-warning" @click="downloadBackup" :disabled="loading">
                <span class="icon"><i class="fas fa-download"></i></span>
                <span>Backup</span>
              </button>
              <button class="button is-success" @click="saveAllSettings" :disabled="saving">
                <span class="icon"><i class="fas fa-save"></i></span>
                <span>Save All</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Success/Error Messages -->
      <div v-if="successMessage" class="notification is-success">
        <button class="delete" @click="successMessage = null"></button>
        {{ successMessage }}
      </div>
      <div v-if="errorMessage" class="notification is-danger">
        <button class="delete" @click="errorMessage = null"></button>
        {{ errorMessage }}
      </div>

      <div class="tabs is-boxed">
        <ul>
          <li v-for="category in categories" :key="category.key" :class="{ 'is-active': activeTab === category.key }">
            <a @click="activeTab = category.key">
              <span class="icon is-small"><i :class="category.icon"></i></span>
              <span>{{ category.name }}</span>
            </a>
          </li>
        </ul>
      </div>
    
    <!-- General Settings -->
    <div v-if="activeTab === 'general'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">General Settings</h2>
        
        <div class="field">
          <label class="label">Site Name</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.general.site_name">
          </div>
        </div>

        <div class="field">
          <label class="label">Site Description</label>
          <div class="control">
            <textarea class="textarea" v-model="settings.general.site_description"></textarea>
          </div>
        </div>

        <div class="field">
          <label class="label">Site Email</label>
          <div class="control">
            <input class="input" type="email" v-model="settings.general.site_email">
          </div>
        </div>

        <div class="field">
          <label class="label">Site Phone</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.general.site_phone">
          </div>
        </div>

        <div class="field">
          <label class="label">Currency</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="settings.general.currency">
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="GBP">British Pound (GBP)</option>
                <option value="JPY">Japanese Yen (JPY)</option>
                <option value="UAH">Ukrainian Hryvnia (UAH)</option>
                <option value="CAD">Canadian Dollar (CAD)</option>
              </select>
            </div>
          </div>
        </div>

        <div class="field">
          <label class="label">Currency Symbol</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.general.currency_symbol">
          </div>
        </div>

        <div class="field">
          <label class="label">Timezone</label>
          <div class="control">
            <div class="select is-fullwidth">
              <select v-model="settings.general.timezone">
                <option value="Europe/Kiev">Europe/Kiev</option>
                <option value="Europe/London">Europe/London</option>
                <option value="America/New_York">America/New_York</option>
                <option value="America/Los_Angeles">America/Los_Angeles</option>
                <option value="Asia/Tokyo">Asia/Tokyo</option>
              </select>
            </div>
          </div>
        </div>

        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="maintenanceMode">
              Enable Maintenance Mode
            </label>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Payment Settings -->
    <div v-if="activeTab === 'payment'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">Payment Settings</h2>
        
        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="stripeEnabled">
              Enable Stripe
            </label>
          </div>
        </div>

        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="paypalEnabled">
              Enable PayPal
            </label>
          </div>
        </div>

        <div class="field">
          <label class="label">Commission Rate (%)</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.payment.commission_rate" step="0.01">
          </div>
        </div>
      </div>
    </div>
    
    <!-- Shipping Settings -->
    <div v-if="activeTab === 'shipping'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">Shipping Settings</h2>
        
        <div class="field">
          <label class="label">Free Shipping Minimum Order Amount</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.shipping.free_shipping_minimum" step="0.01">
          </div>
        </div>

        <div class="field">
          <label class="label">Standard Shipping Rate</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.shipping.standard_shipping_rate" step="0.01">
          </div>
        </div>

        <div class="field">
          <label class="label">Express Shipping Rate</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.shipping.express_shipping_rate" step="0.01">
          </div>
        </div>
      </div>
    </div>
    
    <!-- Email Settings -->
    <div v-if="activeTab === 'email'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">Email Settings</h2>
        
        <div class="field">
          <label class="label">SMTP Host</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.email.smtp_host">
          </div>
        </div>

        <div class="field">
          <label class="label">SMTP Port</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.email.smtp_port">
          </div>
        </div>

        <div class="field">
          <label class="label">SMTP Username</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.email.smtp_username">
          </div>
        </div>

        <div class="field">
          <label class="label">From Email</label>
          <div class="control">
            <input class="input" type="email" v-model="settings.email.from_email">
          </div>
        </div>

        <div class="field">
          <label class="label">From Name</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.email.from_name">
          </div>
        </div>
      </div>
    </div>
    
    <!-- API Settings -->
    <div v-if="activeTab === 'api'" class="settings-content">
      <div class="box">
        <h2 class="subtitle">API Settings</h2>
        
        <div class="field">
          <label class="label">API Key</label>
          <div class="control">
            <input class="input" type="text" v-model="settings.api.apiKey" readonly>
          </div>
          <p class="help">This is your API key. Keep it secure.</p>
        </div>
        
        <div class="field">
          <div class="control">
            <button class="button is-info" @click="regenerateApiKey">
              Regenerate API Key
            </button>
          </div>
        </div>
        
        <div class="field">
          <label class="label">API Rate Limit (requests per minute)</label>
          <div class="control">
            <input class="input" type="number" v-model="settings.api.api_rate_limit">
          </div>
        </div>

        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" v-model="webhooksEnabled">
              Enable Webhooks
            </label>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Save Button -->
    <div class="field is-grouped is-grouped-right mt-4">
      <div class="control" v-if="activeTab === 'email'">
        <button class="button is-info" @click="testEmailSettings" :disabled="loading || saving">
          Test Email Settings
        </button>
      </div>
      <div class="control">
        <button
          class="button is-primary"
          @click="saveSettings"
          :class="{ 'is-loading': saving }"
          :disabled="loading || saving"
        >
          Save Settings
        </button>
      </div>
    </div>
    </div> <!-- Close v-else div -->
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { settingsService } from '@/admin/services/settings';

// Active tab
const activeTab = ref('general');

// Loading states
const loading = ref(false);
const saving = ref(false);
const validating = ref(false);

// Messages
const successMessage = ref(null);
const errorMessage = ref(null);

// Categories
const categories = ref([]);

// Settings data
const settings = ref({
  general: {},
  payment: {},
  shipping: {},
  email: {},
  api: {}
});

// Computed properties for boolean settings
const maintenanceMode = computed({
  get: () => settings.value.general.maintenance_mode === 'true',
  set: (value) => settings.value.general.maintenance_mode = value.toString()
});

const stripeEnabled = computed({
  get: () => settings.value.payment.stripe_enabled === 'true',
  set: (value) => settings.value.payment.stripe_enabled = value.toString()
});

const paypalEnabled = computed({
  get: () => settings.value.payment.paypal_enabled === 'true',
  set: (value) => settings.value.payment.paypal_enabled = value.toString()
});

const webhooksEnabled = computed({
  get: () => settings.value.api.webhooks_enabled === 'true',
  set: (value) => settings.value.api.webhooks_enabled = value.toString()
});

// Methods
const loadSettings = async () => {
  loading.value = true;
  try {
    const [general, payment, shipping, email, api] = await Promise.all([
      settingsService.getGeneralSettings(),
      settingsService.getPaymentSettings(),
      settingsService.getShippingSettings(),
      settingsService.getEmailSettings(),
      settingsService.getApiSettings()
    ]);

    settings.value = {
      general: general.data || {},
      payment: payment.data || {},
      shipping: shipping.data || {},
      email: email.data || {},
      api: api.data || {}
    };
  } catch (error) {
    console.error('Error loading settings:', error);
    alert('Error loading settings. Please try again.');
  } finally {
    loading.value = false;
  }
};

const saveSettings = async () => {
  saving.value = true;
  try {
    const currentTab = activeTab.value;
    let result;

    switch (currentTab) {
      case 'general':
        result = await settingsService.updateGeneralSettings(settings.value.general);
        break;
      case 'payment':
        result = await settingsService.updatePaymentSettings(settings.value.payment);
        break;
      case 'shipping':
        result = await settingsService.updateShippingSettings(settings.value.shipping);
        break;
      case 'email':
        result = await settingsService.updateEmailSettings(settings.value.email);
        break;
      case 'api':
        result = await settingsService.updateApiSettings(settings.value.api);
        break;
      default:
        throw new Error('Unknown settings tab');
    }

    if (result.success !== false) {
      alert('Settings saved successfully!');
    } else {
      throw new Error('Failed to save settings');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    alert('Error saving settings. Please try again.');
  } finally {
    saving.value = false;
  }
};

const testEmailSettings = async () => {
  try {
    await settingsService.testEmailSettings(settings.value.email);
    successMessage.value = 'Test email sent successfully!';
  } catch (error) {
    console.error('Error testing email settings:', error);
    errorMessage.value = 'Failed to send test email. Please check your settings.';
  }
};

const validateAllSettings = async () => {
  validating.value = true;
  errorMessage.value = null;

  try {
    const allSettings = {};
    Object.keys(settings.value).forEach(category => {
      Object.assign(allSettings, settings.value[category]);
    });

    const validation = await settingsService.validateSettings(allSettings);

    const hasErrors = validation.validationResults.some(result => !result.isValid);
    if (hasErrors) {
      errorMessage.value = 'Some settings have validation errors. Please check and fix them.';
    } else {
      successMessage.value = 'All settings are valid!';
    }
  } catch (error) {
    errorMessage.value = error.message || 'Failed to validate settings';
  } finally {
    validating.value = false;
  }
};

const saveAllSettings = async () => {
  saving.value = true;
  errorMessage.value = null;

  try {
    const promises = Object.keys(settings.value).map(category => {
      return settingsService.updateSettingsByCategory ?
        settingsService.updateSettingsByCategory(category, settings.value[category]) :
        settingsService[`update${category.charAt(0).toUpperCase() + category.slice(1)}Settings`](settings.value[category]);
    });

    await Promise.all(promises);
    successMessage.value = 'All settings saved successfully!';
  } catch (error) {
    errorMessage.value = error.message || 'Failed to save settings';
  } finally {
    saving.value = false;
  }
};

const downloadBackup = async () => {
  try {
    await settingsService.downloadBackup();
    successMessage.value = 'Settings backup downloaded successfully!';
  } catch (error) {
    errorMessage.value = error.message || 'Failed to download backup';
  }
};

const loadCategories = async () => {
  try {
    categories.value = await settingsService.getCategories();
  } catch (error) {
    console.error('Error loading categories:', error);
    // Use default categories
    categories.value = [
      { key: 'general', name: 'General', icon: 'fas fa-cog' },
      { key: 'payment', name: 'Payment', icon: 'fas fa-credit-card' },
      { key: 'email', name: 'Email', icon: 'fas fa-envelope' },
      { key: 'api', name: 'API', icon: 'fas fa-code' }
    ];
  }
};

// Initialize
onMounted(async () => {
  await loadCategories();
  await loadSettings();
});
</script>

<style scoped>
.admin-settings {
  padding: 1rem;
}

.tabs a {
  color: #9ca3af;
}

.tabs li.is-active a {
  color: #f3f4f6;
  border-bottom-color: #3B82F6;
}

.settings-content {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}
</style>
