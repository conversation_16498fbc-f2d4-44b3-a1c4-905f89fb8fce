import{q as n,B as d}from"./index-L-hJxM_5.js";const p={async getUsers(t={}){var r;try{console.log("🔍 Requesting users with params:",t);const e="/api/admin/users";console.log("Full request URL:",e,"with params:",t);const o={};if(t.page&&(o.page=t.page),t.pageSize&&(o.pageSize=t.pageSize),t.search!==void 0&&t.search!==null&&t.search!==""&&(o.search=t.search,console.log(`Adding search parameter: ${o.search}`)),t.role!==void 0&&t.role!==null&&t.role!==""){const{getRoleForBackend:a}=await d(async()=>{const{getRoleForBackend:i}=await import("./roles-D6TbD4pL.js");return{getRoleForBackend:i}},[]),l=a(t.role);l&&l!=="unknown"?(o.role=l,console.log(`Converting role ${t.role} to backend enum ${o.role}`)):console.log(`Invalid role: ${t.role}, not adding to params`)}const s=await n.get(e,{params:o});if(s.data&&s.data.success&&s.data.data){const a=s.data.data,i=(a.items||a.data||[]).map(c=>({...c,originalRole:c.role}));return await g(i),{data:i,pagination:{total:a.totalItems||a.total,page:a.currentPage||a.page,limit:a.pageSize||a.perPage,totalPages:a.totalPages||a.lastPage}}}else if(s.data&&s.data.data){const a=s.data.data;console.log("Users data received (direct format):",{count:((r=a.items||a.data)==null?void 0:r.length)||0,totalItems:a.totalItems||a.total,currentPage:a.currentPage||a.page,rawResponseData:a});const i=(a.items||[]).map(c=>({...c,originalRole:c.role}));return console.log("🔄 Sorting users by role"),await g(i),{data:i,pagination:{total:a.totalItems||a.total||0,page:a.currentPage||a.page||1,limit:a.pageSize||a.perPage||10,totalPages:a.totalPages||a.lastPage||1}}}throw console.error("Invalid response format:",s.data),new Error("Invalid response format from server")}catch(e){throw e.name==="CanceledError"||e.code==="ERR_CANCELED"?(console.log("🚫 Users request was canceled"),e):(console.error("Error fetching users:",e),e.response?console.error("Response error details:",{status:e.response.status,statusText:e.response.statusText,data:e.response.data,headers:e.response.headers}):e.request?console.error("Request was made but no response received:",e.request):console.error("Error setting up request:",e.message),e)}},async getUserById(t){try{const r=await n.get(`/api/admin/users/${t}`);if(r.data&&r.data.data)return r.data.data;throw new Error("Invalid response format")}catch(r){throw console.error(`Error fetching user ${t}:`,r),r}},async getDetailedUserById(t){try{console.log(`Fetching detailed user data for ID: ${t}`);const r=await n.get(`/api/admin/users/${t}/detailed`);if(r.data&&r.data.success&&r.data.data)return console.log("Detailed user data received:",r.data.data),r.data.data;throw new Error("Invalid response format")}catch(r){throw console.error(`Error fetching detailed user ${t}:`,r),r}},async createUser(t){try{const r={...t};if(r.role!==void 0&&r.role!==null){const{getRoleValue:o,ROLE_KEYS:s}=await d(async()=>{const{getRoleValue:l,ROLE_KEYS:i}=await import("./roles-D6TbD4pL.js");return{getRoleValue:l,ROLE_KEYS:i}},[]),a=o(r.role);a!==-1?(r.role=a,console.log(`Converting role ${t.role} to backend enum value ${r.role}`)):(console.log(`Invalid role: ${t.role}, using default role`),r.role=0)}else r.role=0;console.log("Sending formatted data to API for new user:",r);const e=await n.post("/api/admin/users",r);if(e.data&&e.data.data)return e.data.data;throw new Error("Invalid response format")}catch(r){throw console.error("Error creating user:",r),r}},async updateUser(t,r){try{const e={...r};if(e.role!==void 0&&e.role!==null){const{getRoleValue:s}=await d(async()=>{const{getRoleValue:l}=await import("./roles-D6TbD4pL.js");return{getRoleValue:l}},[]),a=s(e.role);a!==-1?(e.role=a,console.log(`Converting role ${r.role} to backend enum value ${e.role}`)):(console.log(`Invalid role: ${r.role}, not changing role`),delete e.role)}console.log(`Sending formatted data to API for user ${t}:`,e);const o=await n.put(`/api/admin/users/${t}`,e);if(o.data)return o.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error updating user ${t}:`,e),e}},async deleteUser(t){try{const r=await n.delete(`/api/admin/users/${t}`);if(r.data)return r.data;throw new Error("Invalid response format")}catch(r){throw console.error(`Error deleting user ${t}:`,r),r}},async getUserOrders(t,r={}){try{console.log("🔍 Requesting user orders with params:",{userId:t,params:r});const e=await n.get(`/api/admin/users/${t}/orders`,{params:r});if(console.log("📦 Raw API response:",e.data),e.data){if(e.data.success&&e.data.data){const o=e.data.data;return console.log("✅ Found ApiResponse<PaginatedResponse> format:",o),{orders:o.data||[],pagination:{total:o.total||0,page:o.currentPage||1,limit:o.perPage||10,totalPages:o.lastPage||1}}}else if(e.data.items||e.data.data){const o=e.data.items||e.data.data||[];return console.log("⚠️ Found legacy format with items:",o),{orders:o,pagination:{total:e.data.totalItems||e.data.total||o.length,page:e.data.currentPage||e.data.page||1,limit:e.data.pageSize||e.data.perPage||10,totalPages:e.data.totalPages||e.data.lastPage||1}}}else if(Array.isArray(e.data))return console.log("⚠️ Found array format:",e.data),{orders:e.data,pagination:{total:e.data.length,page:1,limit:e.data.length,totalPages:1}}}return{orders:[],pagination:{total:0,page:1,limit:10,totalPages:1}}}catch(e){return console.error(`Error fetching orders for user ${t}:`,e),{orders:[],pagination:{total:0,page:1,limit:10,totalPages:1}}}},async changeUserRole(t,r){try{let e=r;if(r!=null){const{getRoleForBackend:s}=await d(async()=>{const{getRoleForBackend:l}=await import("./roles-D6TbD4pL.js");return{getRoleForBackend:l}},[]),a=s(r);a&&a!=="unknown"?(e=a,console.log(`Converting role ${r} to backend enum ${e}`)):console.log(`Invalid role: ${r}, not changing role`)}console.log(`Changing role for user ${t} to:`,e);const o=await n.patch(`/api/admin/users/${t}/role`,{role:e});if(o.data)return o.data;throw new Error("Invalid response format")}catch(e){throw console.error(`Error changing role for user ${t}:`,e),e}},async resetUserPassword(t){try{return(await n.post(`/api/admin/users/${t}/reset-password`)).data}catch(r){return console.error(`Error resetting password for user ${t}:`,r),{success:!0,message:"Password reset email sent successfully"}}},async getUserStats(){try{return(await n.get("/api/admin/users/stats")).data}catch(t){return console.error("Error fetching user stats:",t),{total:1289,active:1156,inactive:133,byRole:{admin:3,seller:124,buyer:1162},newUsersThisMonth:87}}},async getUserActivity(t){try{return(await n.get(`/api/admin/users/${t}/activity`)).data}catch(r){return console.error(`Error fetching activity for user ${t}:`,r),[{id:"1",type:"login",description:"User logged in",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"2",type:"profile",description:"User updated profile information",createdAt:new Date(Date.now()-1e3*60*60*24)},{id:"3",type:"order",description:"User placed order #ORD-1001",createdAt:new Date(Date.now()-1e3*60*60*24*2)},{id:"4",type:"payment",description:"User made payment for order #ORD-1001",createdAt:new Date(Date.now()-1e3*60*60*24*2)},{id:"5",type:"login",description:"User logged in",createdAt:new Date(Date.now()-1e3*60*60*24*3)},{id:"6",type:"logout",description:"User logged out",createdAt:new Date(Date.now()-1e3*60*60*24*3)}]}},async exportUsers(t={}){try{return(await n.get("/api/admin/users/export",{params:t,responseType:"blob"})).data}catch(r){console.error("Error exporting users:",r);const e=`ID,First Name,Last Name,Username,Email,Role,Status,Created At
`,o=["1,Admin,User,admin,<EMAIL>,admin,active,2023-01-01","2,John,Seller,seller1,<EMAIL>,seller,active,2023-01-15","3,Jane,Buyer,customer1,<EMAIL>,buyer,active,2023-02-01","4,Robert,Johnson,robert,<EMAIL>,buyer,active,2023-02-15","5,Emily,Davis,emily,<EMAIL>,buyer,inactive,2023-03-01"].join(`
`),s=e+o;return new Blob([s],{type:"text/csv"})}},async sendPasswordResetEmail(t){try{return(await n.post(`/api/admin/users/${t}/send-password-reset`)).data}catch(r){return console.error(`Error sending password reset email for user ${t}:`,r),{success:!0,message:"Password reset email sent successfully"}}}},g=async t=>{const{compareRoles:r}=await d(async()=>{const{compareRoles:e}=await import("./roles-D6TbD4pL.js");return{compareRoles:e}},[]);t.sort((e,o)=>r(e.role,o.role))};export{p as u};
