import{i as f}from"./image.service-DOD4lHqw.js";import{_ as v,c as o,o as d,a as l,k as h,m as p,n as y,t as u,L as I,d as c}from"./index-L-hJxM_5.js";const b={name:"EntityImageManager",props:{entityType:{type:String,required:!0,validator:t=>["user","product","category","company"].includes(t)},entityId:{type:String,default:null},currentImage:{type:String,default:null},imageAlt:{type:String,default:"Зображення"},maxSize:{type:Number,default:5*1024*1024},allowedTypes:{type:Array,default:()=>["image/jpeg","image/png","image/gif","image/webp"]},localMode:{type:Boolean,default:!1},minWidth:{type:Number,default:0},minHeight:{type:Number,default:0},maxWidth:{type:Number,default:4096},maxHeight:{type:Number,default:4096},enableCompression:{type:Boolean,default:!0},compressionQuality:{type:Number,default:.8}},emits:["image-uploaded","image-removed","image-changed"],data(){return{isDragOver:!1,uploading:!1,uploadProgress:0,error:null,showImageViewer:!1,localImageUrl:null,pendingFile:null,previewImage:null,previewFile:null}},computed:{currentImageUrl(){return this.localImageUrl||this.currentImage},uploadHint(){const t=Math.round(this.maxSize/1024/1024),e=this.allowedTypes.map(a=>a.split("/")[1].toUpperCase()).join(", ");let i=`Максимальний розмір: ${t}MB. Формати: ${e}`;return(this.minWidth>0||this.minHeight>0)&&(i+=`. Мінімальні розміри: ${this.minWidth}x${this.minHeight}px`),(this.maxWidth<4096||this.maxHeight<4096)&&(i+=`. Максимальні розміри: ${this.maxWidth}x${this.maxHeight}px`),this.enableCompression&&(i+=". Автоматичне стиснення увімкнено"),i}},methods:{triggerFileInput(){this.$refs.fileInput.click()},handleFileSelect(t){const e=t.target.files[0];e&&this.processFile(e)},handleDrop(t){t.preventDefault(),this.isDragOver=!1;const e=t.dataTransfer.files;e.length>0&&this.processFile(e[0])},async processFile(t){this.error=null;const e=this.validateBasicFile(t);if(!e.isValid){this.error=e.errors.join(", ");return}try{const a=await this.validateImageDimensions(t);if(!a.isValid){this.error=a.errors.join(", ");return}}catch{this.error="Помилка перевірки зображення";return}let i=t;if(this.enableCompression&&this.shouldCompressImage(t))try{i=await this.compressImage(t)}catch(a){console.warn("Compression failed, using original file:",a)}this.localMode?await this.handleLocalMode(i):await this.uploadFile(i)},async handleLocalMode(t){try{this.previewImage=URL.createObjectURL(t),this.previewFile=t}catch{this.error="Помилка створення превью зображення"}},async uploadFile(t){if(!this.entityId){this.error="ID сутності не вказано";return}this.uploading=!0,this.uploadProgress=0;try{const e=await f.uploadImage(this.entityType,this.entityId,t,i=>{this.uploadProgress=i});this.localImageUrl=null,this.pendingFile=null,this.$emit("image-uploaded",e.data)}catch(e){this.error=e.message}finally{this.uploading=!1,this.uploadProgress=0}},removeImage(){this.localMode?(this.localImageUrl=null,this.pendingFile=null,this.$emit("image-changed",null)):this.$emit("image-removed")},openImageViewer(){this.currentImageUrl&&(this.showImageViewer=!0)},closeImageViewer(){this.showImageViewer=!1},validateBasicFile(t){const e=[];if(this.allowedTypes.includes(t.type)||e.push(`Непідтримуваний тип файлу. Дозволені: ${this.allowedTypes.join(", ")}`),t.size>this.maxSize){const i=(this.maxSize/1048576).toFixed(1),a=(t.size/(1024*1024)).toFixed(1);e.push(`Розмір файлу ${a}MB перевищує максимальний ${i}MB`)}return{isValid:e.length===0,errors:e}},validateImageDimensions(t){return new Promise(e=>{const i=new Image;i.onload=()=>{const a=[];i.width<this.minWidth&&a.push(`Ширина зображення ${i.width}px менша за мінімальну ${this.minWidth}px`),i.height<this.minHeight&&a.push(`Висота зображення ${i.height}px менша за мінімальну ${this.minHeight}px`),i.width>this.maxWidth&&a.push(`Ширина зображення ${i.width}px перевищує максимальну ${this.maxWidth}px`),i.height>this.maxHeight&&a.push(`Висота зображення ${i.height}px перевищує максимальну ${this.maxHeight}px`),e({isValid:a.length===0,errors:a,dimensions:{width:i.width,height:i.height}})},i.onerror=()=>{e({isValid:!1,errors:["Неможливо завантажити зображення"]})},i.src=URL.createObjectURL(t)})},shouldCompressImage(t){return t.size>1048576||t.type==="image/png"},compressImage(t){return new Promise(e=>{const i=document.createElement("canvas"),a=i.getContext("2d"),n=new Image;n.onload=()=>{let{width:s,height:r}=n;const m=Math.max(this.maxWidth,this.maxHeight);if(s>m||r>m){const g=Math.min(m/s,m/r);s*=g,r*=g}i.width=s,i.height=r,a.drawImage(n,0,0,s,r),i.toBlob(g=>{const w=new File([g],t.name,{type:"image/jpeg",lastModified:Date.now()});e(w)},"image/jpeg",this.compressionQuality)},n.src=URL.createObjectURL(t)})},clearPreview(){this.previewImage&&URL.revokeObjectURL(this.previewImage),this.previewImage=null,this.previewFile=null,this.error=null},async confirmUpload(){if(this.previewFile)try{this.localImageUrl=this.previewImage,this.pendingFile=this.previewFile,this.$emit("image-changed",{file:this.previewFile,previewUrl:this.previewImage,isLocal:!0}),this.previewImage=null,this.previewFile=null}catch{this.error="Помилка підтвердження зображення"}},async uploadPendingFile(){this.pendingFile&&this.entityId&&await this.uploadFile(this.pendingFile)},resetLocalChanges(){this.localImageUrl=null,this.pendingFile=null,this.error=null}}},x={class:"entity-image-manager"},F={class:"image-container"},U={key:0,class:"current-image"},V=["src","alt"],k={class:"image-overlay"},C=["disabled"],S={class:"upload-content"},D={class:"upload-hint"},M={key:2,class:"upload-progress"},z={class:"progress"},_={class:"progress-text"},B={key:3,class:"alert alert-danger mt-2"},P={key:4,class:"preview-container"},H={class:"preview-header"},L={class:"preview-image-wrapper"},j=["src","alt"],W={class:"preview-actions"},N=["disabled"],O=["disabled"],R={class:"modal-content"},T={class:"modal-header"},A={class:"modal-title"},E={class:"modal-body text-center"},Q=["src","alt"];function q(t,e,i,a,n,s){return d(),o("div",x,[l("div",F,[s.currentImageUrl?(d(),o("div",U,[l("img",{src:s.currentImageUrl,alt:i.imageAlt,class:"image-preview",onClick:e[0]||(e[0]=(...r)=>s.openImageViewer&&s.openImageViewer(...r))},null,8,V),l("div",k,[l("button",{type:"button",class:"btn btn-sm btn-danger",onClick:e[1]||(e[1]=(...r)=>s.removeImage&&s.removeImage(...r)),disabled:n.uploading},e[14]||(e[14]=[l("i",{class:"fas fa-trash"},null,-1)]),8,C),l("button",{type:"button",class:"btn btn-sm btn-primary",onClick:e[2]||(e[2]=(...r)=>s.openImageViewer&&s.openImageViewer(...r))},e[15]||(e[15]=[l("i",{class:"fas fa-eye"},null,-1)]))])])):(d(),o("div",{key:1,class:y(["upload-zone",{"drag-over":n.isDragOver}]),onDrop:e[3]||(e[3]=(...r)=>s.handleDrop&&s.handleDrop(...r)),onDragover:e[4]||(e[4]=p(r=>n.isDragOver=!0,["prevent"])),onDragleave:e[5]||(e[5]=r=>n.isDragOver=!1),onClick:e[6]||(e[6]=(...r)=>s.triggerFileInput&&s.triggerFileInput(...r))},[l("div",S,[e[16]||(e[16]=l("i",{class:"fas fa-cloud-upload-alt upload-icon"},null,-1)),e[17]||(e[17]=l("p",{class:"upload-text"}," Перетягніть зображення сюди або натисніть для вибору ",-1)),l("p",D,u(s.uploadHint),1)])],34)),n.uploading?(d(),o("div",M,[l("div",z,[l("div",{class:"progress-bar",style:I({width:n.uploadProgress+"%"})},null,4)]),l("p",_,"Завантаження: "+u(n.uploadProgress)+"%",1)])):h("",!0),n.error?(d(),o("div",B,u(n.error),1)):h("",!0),n.previewImage&&!s.currentImageUrl?(d(),o("div",P,[l("div",H,[e[19]||(e[19]=l("h6",null,"Попередній перегляд",-1)),l("button",{type:"button",class:"btn btn-sm btn-outline-secondary",onClick:e[7]||(e[7]=(...r)=>s.clearPreview&&s.clearPreview(...r))},e[18]||(e[18]=[l("i",{class:"fas fa-times"},null,-1)]))]),l("div",L,[l("img",{src:n.previewImage,alt:i.imageAlt+" preview",class:"preview-image"},null,8,j)]),l("div",W,[l("button",{type:"button",class:"btn btn-sm btn-primary",onClick:e[8]||(e[8]=(...r)=>s.confirmUpload&&s.confirmUpload(...r)),disabled:n.uploading},e[20]||(e[20]=[l("i",{class:"fas fa-check"},null,-1),c(" Підтвердити ")]),8,N),l("button",{type:"button",class:"btn btn-sm btn-secondary",onClick:e[9]||(e[9]=(...r)=>s.clearPreview&&s.clearPreview(...r)),disabled:n.uploading},e[21]||(e[21]=[l("i",{class:"fas fa-times"},null,-1),c(" Скасувати ")]),8,O)])])):h("",!0)]),l("input",{ref:"fileInput",type:"file",accept:"image/*",style:{display:"none"},onChange:e[10]||(e[10]=(...r)=>s.handleFileSelect&&s.handleFileSelect(...r))},null,544),n.showImageViewer?(d(),o("div",{key:0,class:"modal fade show d-block",onClick:e[13]||(e[13]=(...r)=>s.closeImageViewer&&s.closeImageViewer(...r))},[l("div",{class:"modal-dialog modal-lg",onClick:e[12]||(e[12]=p(()=>{},["stop"]))},[l("div",R,[l("div",T,[l("h5",A,u(i.imageAlt),1),l("button",{type:"button",class:"btn-close",onClick:e[11]||(e[11]=(...r)=>s.closeImageViewer&&s.closeImageViewer(...r))})]),l("div",E,[l("img",{src:s.currentImageUrl,alt:i.imageAlt,class:"img-fluid"},null,8,Q)])])])])):h("",!0)])}const K=v(b,[["render",q],["__scopeId","data-v-3bb95c5e"]]);export{K as E};
