using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.MetaImage;

/// <summary>
/// Обробник універсальної команди видалення мета-зображення
/// </summary>
public class DeleteUniversalMetaImageCommandHandler : IRequestHandler<DeleteUniversalMetaImageCommand, bool>
{
    private readonly IImageService _imageService;
    private readonly ILogger<DeleteUniversalMetaImageCommandHandler> _logger;

    public DeleteUniversalMetaImageCommandHandler(
        IImageService imageService,
        ILogger<DeleteUniversalMetaImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteUniversalMetaImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Deleting meta image for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.DeleteImageAsync(
                request.EntityType,
                request.EntityId,
                "meta",
                null,
                cancellationToken);

            if (result)
            {
                _logger.LogInformation($"Successfully deleted meta image for {request.EntityType} {request.EntityId}");
            }
            else
            {
                _logger.LogWarning($"Meta image not found for deletion: {request.EntityType} {request.EntityId}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to delete meta image for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
