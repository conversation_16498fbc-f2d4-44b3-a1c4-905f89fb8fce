<template>
  <div class="user-form">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-user me-2"></i>
          {{ isEditing ? 'Редагування користувача' : 'Створення користувача' }}
        </h5>
      </div>
      
      <div class="card-body">
        <form @submit.prevent="handleSubmit">
          <div class="row">
            <!-- Основна інформація -->
            <div class="col-md-8">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="firstName" class="form-label">Ім'я *</label>
                    <input
                      id="firstName"
                      v-model="form.firstName"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.firstName }"
                      required
                    />
                    <div v-if="errors.firstName" class="invalid-feedback">
                      {{ errors.firstName }}
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="lastName" class="form-label">Прізвище *</label>
                    <input
                      id="lastName"
                      v-model="form.lastName"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.lastName }"
                      required
                    />
                    <div v-if="errors.lastName" class="invalid-feedback">
                      {{ errors.lastName }}
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="mb-3">
                <label for="email" class="form-label">Email *</label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="form-control"
                  :class="{ 'is-invalid': errors.email }"
                  required
                />
                <div v-if="errors.email" class="invalid-feedback">
                  {{ errors.email }}
                </div>
              </div>
              
              <div class="mb-3">
                <label for="phone" class="form-label">Телефон</label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  class="form-control"
                  :class="{ 'is-invalid': errors.phone }"
                />
                <div v-if="errors.phone" class="invalid-feedback">
                  {{ errors.phone }}
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="role" class="form-label">Роль *</label>
                    <select
                      id="role"
                      v-model="form.role"
                      class="form-select"
                      :class="{ 'is-invalid': errors.role }"
                      required
                    >
                      <option value="">Оберіть роль</option>
                      <option value="User">Користувач</option>
                      <option value="Seller">Продавець</option>
                      <option value="Admin">Адміністратор</option>
                    </select>
                    <div v-if="errors.role" class="invalid-feedback">
                      {{ errors.role }}
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="language" class="form-label">Мова</label>
                    <select
                      id="language"
                      v-model="form.language"
                      class="form-select"
                    >
                      <option value="Ukrainian">Українська</option>
                      <option value="English">English</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Аватар -->
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Аватар користувача</label>
                <EntityImageManager
                  entity-type="user"
                  :entity-id="form.id"
                  :current-image="form.avatarUrl"
                  image-alt="Аватар користувача"
                  :local-mode="!isEditing"
                  @image-uploaded="handleAvatarUploaded"
                  @image-removed="handleAvatarRemoved"
                  @image-changed="handleAvatarChanged"
                  ref="avatarManager"
                />
              </div>
            </div>
          </div>
          
          <!-- Додаткова інформація -->
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="birthday" class="form-label">Дата народження</label>
                <input
                  id="birthday"
                  v-model="form.birthday"
                  type="date"
                  class="form-control"
                />
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="mb-3">
                <label for="gender" class="form-label">Стать</label>
                <select
                  id="gender"
                  v-model="form.gender"
                  class="form-select"
                >
                  <option value="">Не вказано</option>
                  <option value="Male">Чоловіча</option>
                  <option value="Female">Жіноча</option>
                  <option value="Other">Інша</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Статус -->
          <div class="mb-3">
            <div class="form-check">
              <input
                id="isApproved"
                v-model="form.isApproved"
                type="checkbox"
                class="form-check-input"
              />
              <label for="isApproved" class="form-check-label">
                Користувач підтверджений
              </label>
            </div>
          </div>
          
          <!-- Кнопки дій -->
          <div class="d-flex justify-content-between">
            <button
              type="button"
              class="btn btn-secondary"
              @click="handleCancel"
            >
              <i class="fas fa-times me-2"></i>
              Скасувати
            </button>
            
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="saving || !isFormValid"
            >
              <i v-if="saving" class="fas fa-spinner fa-spin me-2"></i>
              <i v-else class="fas fa-save me-2"></i>
              {{ saving ? 'Збереження...' : 'Зберегти' }}
            </button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- Прогрес завантаження -->
    <div v-if="pendingUploads.length > 0" class="mt-3">
      <UploadProgress
        :uploads="pendingUploads"
        @retry-upload="retryUpload"
        @cancel-upload="cancelUpload"
      />
    </div>
  </div>
</template>

<script>
import EntityImageManager from '@/admin/components/common/EntityImageManager.vue';
import UploadProgress from '@/admin/components/common/UploadProgress.vue';
import userService from '@/services/user.service';
import imageService from '@/services/image.service';

export default {
  name: 'UserForm',
  components: {
    EntityImageManager,
    UploadProgress
  },
  props: {
    userId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      form: {
        id: null,
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        role: '',
        language: 'Ukrainian',
        birthday: null,
        gender: '',
        isApproved: false,
        avatarUrl: null
      },
      originalForm: {},
      errors: {},
      saving: false,
      loading: false,
      pendingUploads: [],
      pendingAvatarChange: null
    };
  },
  computed: {
    isEditing() {
      return !!this.userId;
    },
    
    isFormValid() {
      return this.form.firstName && 
             this.form.lastName && 
             this.form.email && 
             this.form.role &&
             Object.keys(this.errors).length === 0;
    },
    
    hasChanges() {
      return JSON.stringify(this.form) !== JSON.stringify(this.originalForm) ||
             this.pendingAvatarChange !== null;
    }
  },
  async created() {
    if (this.isEditing) {
      await this.loadUser();
    }
  },
  methods: {
    async loadUser() {
      this.loading = true;
      try {
        const response = await userService.getUser(this.userId);
        this.form = { ...response.data };
        this.originalForm = { ...response.data };
      } catch (error) {
        this.$toast.error('Помилка завантаження користувача: ' + error.message);
      } finally {
        this.loading = false;
      }
    },
    
    async handleSubmit() {
      if (!this.isFormValid) return;
      
      this.saving = true;
      this.errors = {};
      
      try {
        let user;
        
        if (this.isEditing) {
          user = await userService.updateUser(this.userId, this.form);
        } else {
          user = await userService.createUser(this.form);
          this.form.id = user.data.id;
        }
        
        // Завантажуємо аватар, якщо є зміни
        if (this.pendingAvatarChange) {
          await this.uploadPendingAvatar();
        }
        
        this.$toast.success(
          this.isEditing ? 'Користувача оновлено' : 'Користувача створено'
        );
        
        this.$router.push('/admin/users');
        
      } catch (error) {
        if (error.status === 422 && error.errors) {
          this.errors = error.errors;
        } else {
          this.$toast.error('Помилка збереження: ' + error.message);
        }
      } finally {
        this.saving = false;
      }
    },
    
    async uploadPendingAvatar() {
      if (!this.pendingAvatarChange || !this.form.id) return;
      
      const uploadId = Date.now().toString();
      const upload = {
        id: uploadId,
        filename: this.pendingAvatarChange.file.name,
        size: this.pendingAvatarChange.file.size,
        type: this.pendingAvatarChange.file.type,
        status: 'uploading',
        progress: 0,
        retryable: true
      };
      
      this.pendingUploads.push(upload);
      
      try {
        const response = await imageService.uploadImage(
          'user',
          this.form.id,
          this.pendingAvatarChange.file,
          (progress) => {
            upload.progress = progress;
          }
        );
        
        upload.status = 'completed';
        this.form.avatarUrl = response.data.fileUrl;
        this.pendingAvatarChange = null;
        
        // Очищуємо локальні зміни в компоненті
        this.$refs.avatarManager?.resetLocalChanges();
        
      } catch (error) {
        upload.status = 'error';
        upload.error = error.message;
        throw error;
      }
    },
    
    handleAvatarUploaded(data) {
      this.form.avatarUrl = data.fileUrl;
      this.$toast.success('Аватар завантажено');
    },
    
    handleAvatarRemoved() {
      this.form.avatarUrl = null;
      this.$toast.success('Аватар видалено');
    },
    
    handleAvatarChanged(change) {
      this.pendingAvatarChange = change;
    },
    
    async retryUpload(uploadId) {
      const upload = this.pendingUploads.find(u => u.id === uploadId);
      if (upload && this.pendingAvatarChange) {
        upload.status = 'uploading';
        upload.progress = 0;
        upload.error = null;
        
        try {
          await this.uploadPendingAvatar();
        } catch (error) {
          // Помилка вже оброблена в uploadPendingAvatar
        }
      }
    },
    
    cancelUpload(uploadId) {
      const index = this.pendingUploads.findIndex(u => u.id === uploadId);
      if (index !== -1) {
        this.pendingUploads.splice(index, 1);
      }
    },
    
    handleCancel() {
      if (this.hasChanges) {
        if (confirm('У вас є незбережені зміни. Ви впевнені, що хочете вийти?')) {
          this.$router.push('/admin/users');
        }
      } else {
        this.$router.push('/admin/users');
      }
    }
  },
  
  beforeRouteLeave(to, from, next) {
    if (this.hasChanges) {
      if (confirm('У вас є незбережені зміни. Ви впевнені, що хочете вийти?')) {
        next();
      } else {
        next(false);
      }
    } else {
      next();
    }
  }
};
</script>

<style scoped>
.user-form {
  max-width: 1000px;
  margin: 0 auto;
}

.form-label {
  font-weight: 500;
}

.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}
</style>
