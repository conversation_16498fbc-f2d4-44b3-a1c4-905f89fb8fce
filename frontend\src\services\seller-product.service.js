import apiService from './api';

// Enum mappings to match backend
const CURRENCY_MAP = {
  'UAH': 0,
  'USD': 1,
  'EUR': 2
};

const PRODUCT_STATUS_MAP = {
  'Pending': 0,
  'Approved': 1,
  'Rejected': 2,
  'Draft': 0  // Map Draft to Pending
};

class SellerProductService {
  /**
   * Отримує компанію поточного продавця
   * @returns {Promise<Object>} - Об'єкт з даними компанії
   */
  async getSellerCompany() {
    try {
      const response = await apiService.get('/api/sellers/me/company');
      console.log('Seller company response:', response);

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      throw new Error('Не вдалося отримати дані компанії');
    } catch (error) {
      console.error('Error getting seller company:', error);
      throw error;
    }
  }

  /**
   * Отримує товари поточного продавця
   * @param {Object} params - Параметри запиту
   * @returns {Promise<Object>} - Об'єкт з товарами та інформацією про пагінацію
   */
  async getSellerProducts(params = {}) {
    try {
      const response = await apiService.get('/api/sellers/me/products', { params });
      console.log("seller response", response);

      if (response.data && response.data.success && response.data.data) {
        const products = this._convertProductsFromApi(response.data.data.data || []);
        return {
          products: products,
          pagination: {
            total: response.data.data.total || 0,
            page: response.data.data.currentPage || 1,
            limit: response.data.data.pageSize || 10,
            totalPages: response.data.data.totalPages || 1
          }
        };
      }

      console.error('Invalid response format:', response.data);
      return {
        products: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      };
    } catch (error) {
      console.error('Error fetching seller products:', error);
      
      // Якщо помилка 403 (Forbidden), повертаємо порожній результат
      if (error.response?.status === 403) {
        return {
          products: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        };
      }
      
      throw error;
    }
  }

  /**
   * Отримує конкретний товар продавця
   * @param {string} productId - ID товару
   * @returns {Promise<Object>} - Об'єкт з даними товару
   */
  async getSellerProduct(productId) {
    try {
      const response = await apiService.get(`/api/sellers/me/products/${productId}`);

      if (response.data && response.data.success && response.data.data) {
        return this._convertFromApiFormat(response.data.data);
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching seller product:', error);
      throw error;
    }
  }

  /**
   * Створює новий товар
   * @param {Object} productData - Дані товару
   * @returns {Promise<Object>} - Об'єкт з даними створеного товару
   */
  async createProduct(productData) {
    try {
      console.log('Original product data:', productData);

      // Спочатку отримуємо дані компанії поточного продавця
      const companyData = await this.getSellerCompany();
      console.log('Company data:', companyData);

      // Додаємо CompanyId до даних товару
      const productDataWithCompany = {
        ...productData,
        companyId: companyData.id
      };

      // Конвертуємо дані для backend API
      const apiData = this._convertToApiFormat(productDataWithCompany);
      console.log('Converted API data:', apiData);

      const response = await apiService.post('/api/sellers/me/products', apiData);
      console.log('API response:', response);

      if (response.data && response.data.success && response.data.data) {
        // Для createProduct response.data.data містить ID створеного товару
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error creating product:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      throw error;
    }
  }

  /**
   * Оновлює товар
   * @param {string} productId - ID товару
   * @param {Object} productData - Дані товару
   * @returns {Promise<boolean>} - Результат операції
   */
  async updateProduct(productId, productData) {
    try {
      console.log('Updating product with ID:', productId);
      console.log('Original product data:', productData);

      // Перевіряємо токен аутентифікації
      const token = localStorage.getItem('token');
      console.log('Auth token exists:', !!token);

      if (!token) {
        throw new Error('Токен аутентифікації відсутній. Будь ласка, увійдіть в систему.');
      }

      // Отримуємо дані компанії для діагностики
      try {
        const companyData = await this.getSellerCompany();
        console.log('Current user company:', companyData);
        console.log('User company ID:', companyData.id);
      } catch (companyError) {
        console.error('Error getting company data:', companyError);
      }

      // Конвертуємо дані для backend API (режим оновлення)
      const apiData = this._convertToApiFormat(productData, true);
      console.log('Converted API data for update:', JSON.stringify(apiData, null, 2));

      console.log('Final API data being sent:', JSON.stringify(apiData, null, 2));
      console.log('API endpoint:', `/api/sellers/me/products/${productId}`);

      const response = await apiService.put(`/api/sellers/me/products/${productId}`, apiData);
      console.log('Update response:', response);

      return response.data && response.data.success;
    } catch (error) {
      console.error('Error updating product:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error message:', error.message);

      if (error.response?.status === 400) {
        const errorMessage = error.response?.data?.message || error.response?.data?.errors || 'Неправильний формат даних';
        console.error('400 Bad Request details:', errorMessage);
        throw new Error(`Помилка валідації даних: ${JSON.stringify(errorMessage)}`);
      } else if (error.response?.status === 401) {
        throw new Error('Помилка аутентифікації. Будь ласка, увійдіть в систему знову.');
      } else if (error.response?.status === 403) {
        throw new Error('У вас немає прав для редагування цього товару.');
      } else if (error.response?.status === 404) {
        throw new Error('Товар не знайдено.');
      }

      throw error;
    }
  }

  /**
   * Видаляє товар
   * @param {string} productId - ID товару
   * @returns {Promise<boolean>} - Результат операції
   */
  async deleteProduct(productId) {
    try {
      const response = await apiService.delete(`/api/sellers/me/products/${productId}`);

      return response.data && response.data.success;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  /**
   * Завантажує зображення для товару
   * @param {string} productId - ID товару
   * @param {File} imageFile - Файл зображення
   * @returns {Promise<Object>} - Об'єкт з даними завантаженого зображення
   */
  async uploadProductImage(productId, imageFile) {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await apiService.post(`/api/sellers/me/products/${productId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error uploading product image:', error);
      throw error;
    }
  }

  /**
   * Конвертує дані з frontend формату в API формат
   * @param {Object} productData - Дані товару з frontend
   * @param {boolean} isUpdate - Чи це оновлення товару (для UpdateProductCommand)
   * @returns {Object} - Дані товару для API
   * @private
   */
  _convertToApiFormat(productData, isUpdate = false) {
    const apiData = { ...productData };

    console.log('Converting product data to API format:', productData);

    // Конвертуємо валюту з рядка в enum
    if (apiData.priceCurrency && typeof apiData.priceCurrency === 'string') {
      const originalCurrency = apiData.priceCurrency;
      apiData.priceCurrency = CURRENCY_MAP[apiData.priceCurrency] ?? CURRENCY_MAP['UAH'];
      console.log(`Currency converted: ${originalCurrency} -> ${apiData.priceCurrency}`);
    } else {
      // Встановлюємо UAH за замовчуванням якщо валюта не вказана
      apiData.priceCurrency = CURRENCY_MAP['UAH'];
      console.log('Currency set to default UAH (0)');
    }

    // Конвертуємо статус з рядка в enum
    if (apiData.status !== undefined && apiData.status !== null) {
      if (typeof apiData.status === 'string') {
        const originalStatus = apiData.status;
        apiData.status = PRODUCT_STATUS_MAP[apiData.status] ?? PRODUCT_STATUS_MAP['Pending'];
        console.log(`Status converted: ${originalStatus} -> ${apiData.status}`);
      }
    } else {
      // Встановлюємо Pending за замовчуванням
      apiData.status = PRODUCT_STATUS_MAP['Pending'];
      console.log('Status set to default Pending (0)');
    }

    // Переконуємося, що числові поля мають правильний тип та не null
    if (apiData.priceAmount !== undefined && apiData.priceAmount !== null) {
      apiData.priceAmount = parseFloat(apiData.priceAmount) || 0;
    } else {
      apiData.priceAmount = 0;
    }

    if (apiData.stock !== undefined && apiData.stock !== null) {
      apiData.stock = parseInt(apiData.stock) || 1;
    } else {
      apiData.stock = 1;
    }

    if (apiData.sales !== undefined && apiData.sales !== null) {
      apiData.sales = parseInt(apiData.sales) || 0;
    } else {
      apiData.sales = 0;
    }

    // Переконуємося, що рядкові поля не null (обов'язкові тільки для створення)
    if (!isUpdate) {
      if (!apiData.name || apiData.name.trim() === '') {
        throw new Error('Product name is required');
      }

      if (!apiData.description || apiData.description.trim() === '') {
        throw new Error('Product description is required');
      }
    } else {
      // Для оновлення перевіряємо тільки якщо поля передані
      if (apiData.name !== undefined && (!apiData.name || apiData.name.trim() === '')) {
        throw new Error('Product name cannot be empty');
      }

      if (apiData.description !== undefined && (!apiData.description || apiData.description.trim() === '')) {
        throw new Error('Product description cannot be empty');
      }
    }

    // CategoryId обов'язковий тільки для створення товару
    if (!isUpdate && (!apiData.categoryId || apiData.categoryId.trim() === '')) {
      throw new Error('Category ID is required');
    }

    // Переконуємося, що attributes не null
    if (!apiData.attributes) {
      apiData.attributes = {};
    }

    // Переконуємося, що categoryId є валідним GUID
    if (apiData.categoryId && typeof apiData.categoryId === 'string') {
      // Валідація GUID формату
      const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!guidRegex.test(apiData.categoryId)) {
        throw new Error('Invalid categoryId format. Must be a valid GUID.');
      }
    }

    // Для оновлення товару companyId не потрібен (backend сам визначить)
    if (isUpdate) {
      delete apiData.companyId;

      // UpdateSellerProductCommand використовує string для PriceCurrency
      console.log('Original priceCurrency:', apiData.priceCurrency, typeof apiData.priceCurrency);

      if (apiData.priceCurrency !== undefined && apiData.priceCurrency !== null) {
        // Переконуємося, що це правильна назва enum
        if (typeof apiData.priceCurrency === 'number') {
          // Конвертуємо enum в string
          const currencyMap = { 0: 'UAH', 1: 'USD', 2: 'EUR' };
          apiData.priceCurrency = currencyMap[apiData.priceCurrency] || 'UAH';
        } else if (typeof apiData.priceCurrency === 'string') {
          // Переконуємося, що рядок є валідним enum значенням
          const validCurrencies = ['UAH', 'USD', 'EUR'];
          if (!validCurrencies.includes(apiData.priceCurrency.toUpperCase())) {
            console.warn('Invalid currency:', apiData.priceCurrency, 'defaulting to UAH');
            apiData.priceCurrency = 'UAH';
          } else {
            apiData.priceCurrency = apiData.priceCurrency.toUpperCase();
          }
        } else {
          console.warn('Unexpected priceCurrency type:', typeof apiData.priceCurrency, 'value:', apiData.priceCurrency);
          apiData.priceCurrency = 'UAH';
        }
        console.log('Final priceCurrency for update:', apiData.priceCurrency, typeof apiData.priceCurrency);
      } else {
        console.log('PriceCurrency is null/undefined, removing from request');
        delete apiData.priceCurrency;
      }

      // Видаляємо порожні URL поля для оновлення
      if (apiData.metaImage === '' || apiData.metaImage === null || apiData.metaImage === undefined) {
        console.log('Removing empty metaImage field');
        delete apiData.metaImage;
      }
    } else {
      // Для створення товару переконуємося, що companyId є валідним GUID
      if (!apiData.companyId) {
        throw new Error('Company ID is required');
      }

      const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!guidRegex.test(apiData.companyId)) {
        throw new Error('Invalid companyId format. Must be a valid GUID.');
      }
    }

    console.log('Final API data before sending:', JSON.stringify(apiData, null, 2));
    return apiData;
  }

  /**
   * Конвертує дані з API формату в frontend формат
   * @param {Object} apiData - Дані товару з API
   * @returns {Object} - Дані товару для frontend
   * @private
   */
  _convertFromApiFormat(apiData) {
    const frontendData = { ...apiData };

    // Конвертуємо валюту з enum в рядок
    if (typeof frontendData.priceCurrency === 'number') {
      const currencyMap = { 0: 'UAH', 1: 'USD', 2: 'EUR' };
      frontendData.priceCurrency = currencyMap[frontendData.priceCurrency] || 'UAH';
    }

    // Конвертуємо статус з enum в рядок
    if (typeof frontendData.status === 'number') {
      const statusMap = { 0: 'Pending', 1: 'Approved', 2: 'Rejected' };
      frontendData.status = statusMap[frontendData.status] || 'Pending';
    }

    return frontendData;
  }

  /**
   * Конвертує масив товарів з API формату
   * @param {Array} products - Масив товарів з API
   * @returns {Array} - Масив товарів для frontend
   * @private
   */
  _convertProductsFromApi(products) {
    if (!Array.isArray(products)) return [];

    return products.map(product => this._convertFromApiFormat(product));
  }
}

export default new SellerProductService();
