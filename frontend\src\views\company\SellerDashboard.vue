<template>
  <div class="seller-dashboard">
    <div class="container">
      <!-- Header -->
      <div class="dashboard-header">
        <h1 class="dashboard-title">
          <i class="fas fa-store"></i>
          Кабінет продавця
        </h1>
      </div>

      <!-- Buyer Notice -->
      <div v-if="userRole === 'Buyer'" class="buyer-notice">
        <div class="notice-content">
          <div class="notice-icon">
            <i class="fas fa-info-circle"></i>
          </div>
          <div class="notice-text">
            <h2>Станьте продавцем на нашій платформі!</h2>
            <p>
              Ви можете подати заявку на отримання статусу продавця та почати продавати свої товари. 
              Заповніть форму заявки, і наші адміністратори розглянуть її найближчим часом.
            </p>
          </div>
        </div>
        
        <div class="notice-actions">
          <router-link to="/seller/application" class="btn btn-primary btn-large">
            <i class="fas fa-paper-plane"></i>
            Подати заявку на становлення продавцем
          </router-link>
        </div>

        <!-- Existing Requests -->
        <div v-if="sellerRequests.length > 0" class="existing-requests">
          <h3>Ваші заявки:</h3>
          <div class="requests-list">
            <div 
              v-for="request in sellerRequests" 
              :key="request.id" 
              class="request-card"
            >
              <div class="request-info">
                <div class="request-company">{{ request.companyRequestData?.companyName || 'Без назви' }}</div>
                <div class="request-date">Подано: {{ formatDate(request.createdAt) }}</div>
              </div>
              <div class="request-status">
                <span :class="getStatusClass(request.status)">
                  {{ getStatusText(request.status) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Seller Dashboard -->
      <div v-else-if="userRole === 'Seller' || userRole === 'SellerOwner'" class="seller-content">
        <div class="dashboard-grid">
          <!-- Quick Stats -->
          <div class="stats-section">
            <h2 class="section-title">
              <i class="fas fa-chart-bar"></i>
              Статистика
            </h2>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-box"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">0</div>
                  <div class="stat-label">Товарів</div>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">0</div>
                  <div class="stat-label">Замовлень</div>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">₴0</div>
                  <div class="stat-label">Дохід</div>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">0</div>
                  <div class="stat-label">Відгуків</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="actions-section">
            <h2 class="section-title">
              <i class="fas fa-bolt"></i>
              Швидкі дії
            </h2>
            <div class="actions-grid">
              <button class="action-card" @click="navigateToProducts">
                <i class="fas fa-plus"></i>
                <span>Додати товар</span>
              </button>
              
              <button class="action-card" @click="navigateToOrders">
                <i class="fas fa-list"></i>
                <span>Переглянути замовлення</span>
              </button>
              
              <button class="action-card" @click="navigateToAnalytics">
                <i class="fas fa-chart-line"></i>
                <span>Аналітика</span>
              </button>
              
              <button class="action-card" @click="navigateToSettings">
                <i class="fas fa-cog"></i>
                <span>Налаштування</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Access Denied -->
      <div v-else class="access-denied">
        <div class="denied-content">
          <i class="fas fa-lock"></i>
          <h2>Доступ заборонено</h2>
          <p>У вас немає прав для доступу до кабінету продавця.</p>
          <router-link to="/dashboard" class="btn btn-primary">
            Повернутися до головної
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useToast } from '@/composables/useToast';
import sellerRequestService from '@/services/seller-request.service';

export default {
  name: 'SellerDashboard',
  setup() {
    const store = useStore();
    const router = useRouter();
    const { showToast } = useToast();
    
    const sellerRequests = ref([]);
    const loading = ref(false);
    
    const user = computed(() => store.getters['auth/user']);
    const userRole = computed(() => user.value?.role);
    
    const getStatusClass = (status) => {
      const statusClasses = {
        'Pending': 'status-pending',
        'Approved': 'status-approved',
        'Rejected': 'status-rejected'
      };
      return statusClasses[status] || 'status-pending';
    };

    const getStatusText = (status) => {
      const statusTexts = {
        'Pending': 'На розгляді',
        'Approved': 'Схвалено',
        'Rejected': 'Відхилено'
      };
      return statusTexts[status] || status;
    };

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const loadSellerRequests = async () => {
      if (userRole.value !== 'Buyer') return;
      
      loading.value = true;
      try {
        const response = await sellerRequestService.getUserSellerRequests();
        sellerRequests.value = response.requests || [];
      } catch (error) {
        console.error('Error loading seller requests:', error);
        showToast('Помилка завантаження заявок', 'error');
      } finally {
        loading.value = false;
      }
    };

    // Navigation methods
    const navigateToProducts = () => {
      showToast('Функціонал управління товарами буде додано незабаром', 'info');
    };

    const navigateToOrders = () => {
      showToast('Функціонал управління замовленнями буде додано незабаром', 'info');
    };

    const navigateToAnalytics = () => {
      showToast('Функціонал аналітики буде додано незабаром', 'info');
    };

    const navigateToSettings = () => {
      showToast('Функціонал налаштувань буде додано незабаром', 'info');
    };

    onMounted(() => {
      loadSellerRequests();
    });

    return {
      userRole,
      sellerRequests,
      loading,
      getStatusClass,
      getStatusText,
      formatDate,
      navigateToProducts,
      navigateToOrders,
      navigateToAnalytics,
      navigateToSettings
    };
  }
};
</script>

<style scoped>
.seller-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
}

.dashboard-title {
  font-size: 2.5rem;
  color: #333;
  font-weight: 700;
}

.dashboard-title i {
  color: #007bff;
  margin-right: 12px;
}

/* Buyer Notice */
.buyer-notice {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.notice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 32px;
}

.notice-icon {
  font-size: 3rem;
  color: #007bff;
}

.notice-text {
  text-align: left;
  max-width: 600px;
}

.notice-text h2 {
  color: #333;
  margin-bottom: 12px;
  font-size: 1.8rem;
}

.notice-text p {
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
}

.notice-actions {
  margin-bottom: 32px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-large {
  padding: 16px 32px;
  font-size: 1.1rem;
}

/* Existing Requests */
.existing-requests {
  border-top: 2px solid #e9ecef;
  padding-top: 24px;
  text-align: left;
}

.existing-requests h3 {
  color: #333;
  margin-bottom: 16px;
  font-size: 1.3rem;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.request-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.request-info {
  flex: 1;
}

.request-company {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.request-date {
  color: #666;
  font-size: 0.9rem;
}

.request-status {
  font-weight: 600;
}

.status-pending {
  color: #ff9800;
}

.status-approved {
  color: #4caf50;
}

.status-rejected {
  color: #f44336;
}

/* Seller Content */
.seller-content {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-grid {
  display: grid;
  gap: 40px;
}

.section-title {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title i {
  color: #007bff;
}

/* Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

.stat-icon {
  font-size: 2rem;
  color: #007bff;
  width: 60px;
  height: 60px;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* Actions */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.action-card:hover {
  border-color: #007bff;
  background: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.action-card i {
  font-size: 2rem;
}

/* Access Denied */
.access-denied {
  background: #fff;
  border-radius: 12px;
  padding: 60px 32px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.denied-content i {
  font-size: 4rem;
  color: #f44336;
  margin-bottom: 24px;
}

.denied-content h2 {
  color: #333;
  margin-bottom: 16px;
  font-size: 1.8rem;
}

.denied-content p {
  color: #666;
  margin-bottom: 32px;
  font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-title {
    font-size: 2rem;
  }

  .notice-content {
    flex-direction: column;
    text-align: center;
  }

  .notice-text {
    text-align: center;
  }

  .stats-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .request-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .container {
    padding: 0 15px;
  }
}
</style>
