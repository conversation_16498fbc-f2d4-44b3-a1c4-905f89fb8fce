using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Infrastructure.Repositories;

public class LogRepository : Repository<Log>, ILogRepository
{
    private readonly MarketplaceDbContext _marketplaceContext;

    public LogRepository(MarketplaceDbContext context) : base(context)
    {
        _marketplaceContext = context;
    }

    public async Task<List<Log>> GetByLevelAsync(LogLevel level, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Logs
            .Where(l => l.Level == level)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Log>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Logs
            .Where(l => l.Category == category)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Log>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Logs
            .Where(l => l.UserId == userId)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Log>> GetByDateRangeAsync(DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Logs
            .Where(l => l.Timestamp >= from && l.Timestamp <= to)
            .OrderByDescending(l => l.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Log>> GetFilteredLogsAsync(
        LogLevel? level = null,
        string? category = null,
        Guid? userId = null,
        DateTime? from = null,
        DateTime? to = null,
        int? page = null,
        int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = _marketplaceContext.Logs
            .Include(l => l.User)
            .AsQueryable();

        if (level.HasValue)
            query = query.Where(l => l.Level == level.Value);

        if (!string.IsNullOrEmpty(category))
            query = query.Where(l => l.Category == category);

        if (userId.HasValue)
            query = query.Where(l => l.UserId == userId.Value);

        if (from.HasValue)
            query = query.Where(l => l.Timestamp >= from.Value);

        if (to.HasValue)
            query = query.Where(l => l.Timestamp <= to.Value);

        query = query.OrderByDescending(l => l.Timestamp);

        if (page.HasValue && pageSize.HasValue)
        {
            var skip = (page.Value - 1) * pageSize.Value;
            query = query.Skip(skip).Take(pageSize.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<int> GetLogCountAsync(
        LogLevel? level = null,
        string? category = null,
        Guid? userId = null,
        DateTime? from = null,
        DateTime? to = null,
        CancellationToken cancellationToken = default)
    {
        var query = _marketplaceContext.Logs.AsQueryable();

        if (level.HasValue)
            query = query.Where(l => l.Level == level.Value);

        if (!string.IsNullOrEmpty(category))
            query = query.Where(l => l.Category == category);

        if (userId.HasValue)
            query = query.Where(l => l.UserId == userId.Value);

        if (from.HasValue)
            query = query.Where(l => l.Timestamp >= from.Value);

        if (to.HasValue)
            query = query.Where(l => l.Timestamp <= to.Value);

        return await query.CountAsync(cancellationToken);
    }

    public async Task CleanupOldLogsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var oldLogs = await _marketplaceContext.Logs
            .Where(l => l.Timestamp < olderThan)
            .ToListAsync(cancellationToken);

        _marketplaceContext.Logs.RemoveRange(oldLogs);
        await _marketplaceContext.SaveChangesAsync(cancellationToken);
    }
}
