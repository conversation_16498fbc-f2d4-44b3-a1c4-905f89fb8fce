/* ===== ADMIN LAYOUT SYSTEM ===== */
/* Based on Reports page layout patterns */

/* ===== BASE LAYOUT ===== */
.admin-layout {
  min-height: 100vh;
  background-color: var(--admin-bg-primary);
  font-family: var(--admin-font-family);
}

/* ===== PAGE STRUCTURE ===== */
.admin-page {
  padding: var(--admin-space-2xl);
  background-color: var(--admin-bg-primary);
  min-height: 100vh;
}

.admin-page-content {
  max-width: var(--admin-container-max-width);
  margin: 0 auto;
}

/* ===== PAGE HEADER ===== */
.admin-page-header {
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-xl);
  padding: var(--admin-space-3xl);
  margin-bottom: var(--admin-space-2xl);
  color: white;
  box-shadow: var(--admin-shadow-lg);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.admin-page-header-content {
  max-width: 800px;
}

.admin-page-title-section {
  flex: 1;
}

.admin-page-title {
  font-size: var(--admin-text-4xl);
  font-weight: var(--admin-font-bold);
  color: white;
  margin: 0 0 var(--admin-space-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.admin-page-icon {
  font-size: var(--admin-text-3xl);
  opacity: 0.9;
}

.admin-page-subtitle {
  font-size: var(--admin-text-lg);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: var(--admin-font-normal);
}

.admin-page-actions {
  display: flex;
  gap: var(--admin-space-md);
  align-items: flex-start;
}

.admin-page-header-actions {
  display: flex;
  gap: var(--admin-space-md);
  margin-top: var(--admin-space-lg);
}

/* ===== SECTIONS ===== */
.admin-section {
  background: var(--admin-card-bg);
  border-radius: var(--admin-card-radius);
  padding: var(--admin-card-padding);
  margin-bottom: var(--admin-space-2xl);
  box-shadow: var(--admin-card-shadow);
  border: 1px solid var(--admin-card-border);
}

.admin-section:last-child {
  margin-bottom: 0;
}

.admin-section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-xl);
  padding-bottom: var(--admin-space-lg);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-section-title-area {
  flex: 1;
}

.admin-section-actions {
  display: flex;
  gap: var(--admin-space-md);
}

.admin-section-content {
  /* Content area - no default styles */
}

/* ===== GRID SYSTEMS ===== */

/* Metrics Grid */
.admin-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--admin-space-xl);
}

.admin-metrics-grid-sm {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--admin-space-lg);
}

.admin-metrics-grid-lg {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--admin-space-2xl);
}

/* Charts Grid */
.admin-charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--admin-space-2xl);
}

.admin-charts-grid-equal {
  grid-template-columns: 1fr 1fr;
}

.admin-charts-grid-single {
  grid-template-columns: 1fr;
}

/* Content Grid */
.admin-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--admin-space-xl);
}

.admin-content-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.admin-content-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.admin-content-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* ===== FLEXBOX UTILITIES ===== */
.admin-flex {
  display: flex;
}

.admin-flex-col {
  display: flex;
  flex-direction: column;
}

.admin-flex-wrap {
  flex-wrap: wrap;
}

.admin-flex-nowrap {
  flex-wrap: nowrap;
}

.admin-flex-1 {
  flex: 1;
}

.admin-flex-auto {
  flex: auto;
}

.admin-flex-none {
  flex: none;
}

/* Justify Content */
.admin-justify-start {
  justify-content: flex-start;
}

.admin-justify-center {
  justify-content: center;
}

.admin-justify-end {
  justify-content: flex-end;
}

.admin-justify-between {
  justify-content: space-between;
}

.admin-justify-around {
  justify-content: space-around;
}

.admin-justify-evenly {
  justify-content: space-evenly;
}

/* Align Items */
.admin-items-start {
  align-items: flex-start;
}

.admin-items-center {
  align-items: center;
}

.admin-items-end {
  align-items: flex-end;
}

.admin-items-stretch {
  align-items: stretch;
}

.admin-items-baseline {
  align-items: baseline;
}

/* ===== SPACING UTILITIES ===== */

/* Gaps */
.admin-gap-xs { gap: var(--admin-space-xs); }
.admin-gap-sm { gap: var(--admin-space-sm); }
.admin-gap-md { gap: var(--admin-space-md); }
.admin-gap-lg { gap: var(--admin-space-lg); }
.admin-gap-xl { gap: var(--admin-space-xl); }
.admin-gap-2xl { gap: var(--admin-space-2xl); }
.admin-gap-3xl { gap: var(--admin-space-3xl); }

/* Margins */
.admin-m-0 { margin: 0; }
.admin-m-auto { margin: auto; }
.admin-mt-0 { margin-top: 0; }
.admin-mb-0 { margin-bottom: 0; }
.admin-ml-0 { margin-left: 0; }
.admin-mr-0 { margin-right: 0; }

.admin-mt-xs { margin-top: var(--admin-space-xs); }
.admin-mt-sm { margin-top: var(--admin-space-sm); }
.admin-mt-md { margin-top: var(--admin-space-md); }
.admin-mt-lg { margin-top: var(--admin-space-lg); }
.admin-mt-xl { margin-top: var(--admin-space-xl); }
.admin-mt-2xl { margin-top: var(--admin-space-2xl); }
.admin-mt-3xl { margin-top: var(--admin-space-3xl); }

.admin-mb-xs { margin-bottom: var(--admin-space-xs); }
.admin-mb-sm { margin-bottom: var(--admin-space-sm); }
.admin-mb-md { margin-bottom: var(--admin-space-md); }
.admin-mb-lg { margin-bottom: var(--admin-space-lg); }
.admin-mb-xl { margin-bottom: var(--admin-space-xl); }
.admin-mb-2xl { margin-bottom: var(--admin-space-2xl); }
.admin-mb-3xl { margin-bottom: var(--admin-space-3xl); }

/* Padding */
.admin-p-0 { padding: 0; }
.admin-pt-0 { padding-top: 0; }
.admin-pb-0 { padding-bottom: 0; }
.admin-pl-0 { padding-left: 0; }
.admin-pr-0 { padding-right: 0; }

.admin-p-xs { padding: var(--admin-space-xs); }
.admin-p-sm { padding: var(--admin-space-sm); }
.admin-p-md { padding: var(--admin-space-md); }
.admin-p-lg { padding: var(--admin-space-lg); }
.admin-p-xl { padding: var(--admin-space-xl); }
.admin-p-2xl { padding: var(--admin-space-2xl); }
.admin-p-3xl { padding: var(--admin-space-3xl); }

/* ===== CONTAINERS ===== */
.admin-container {
  max-width: var(--admin-container-max-width);
  margin: 0 auto;
  padding: 0 var(--admin-space-lg);
}

.admin-container-fluid {
  width: 100%;
  padding: 0 var(--admin-space-lg);
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .admin-page {
    padding: var(--admin-space-lg);
  }
  
  .admin-page-header {
    padding: var(--admin-space-2xl);
  }
  
  .admin-metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--admin-space-lg);
  }
  
  .admin-charts-grid {
    grid-template-columns: 1fr;
    gap: var(--admin-space-lg);
  }
  
  .admin-content-grid-2,
  .admin-content-grid-3,
  .admin-content-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .admin-section {
    padding: var(--admin-space-lg);
  }
}

@media (max-width: 480px) {
  .admin-page {
    padding: var(--admin-space-md);
  }
  
  .admin-page-header {
    padding: var(--admin-space-lg);
  }
  
  .admin-section {
    padding: var(--admin-space-md);
  }
}

/* ===== LOADING STATES ===== */
.admin-loading-container,
.admin-error-container,
.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--admin-space-4xl);
  min-height: 400px;
}

.admin-loading-spinner i {
  font-size: 3rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-lg);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.admin-error-message i {
  font-size: 3rem;
  color: var(--admin-danger);
  margin-bottom: var(--admin-space-lg);
}

.admin-empty-message i {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-lg);
}

/* ===== VISIBILITY UTILITIES ===== */
.admin-hidden {
  display: none;
}

.admin-visible {
  display: block;
}

.admin-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
