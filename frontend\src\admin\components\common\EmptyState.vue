<template>
  <div class="empty-state">
    <div class="empty-state-icon">
      <span class="icon is-large">
        <i :class="`fas fa-${icon} fa-3x`"></i>
      </span>
    </div>
    <h3 class="empty-state-title">{{ title }}</h3>
    <p class="empty-state-message">{{ message }}</p>
    <div v-if="$slots.action" class="empty-state-action">
      <slot name="action"></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'inbox'
  }
});
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 1rem 0;
}

.empty-state-icon {
  margin-bottom: 1.5rem;
  color: #b5b5b5;
}

.empty-state-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #363636;
}

.empty-state-message {
  font-size: 1rem;
  color: #7a7a7a;
  max-width: 500px;
  margin-bottom: 1.5rem;
}

.empty-state-action {
  margin-top: 0.5rem;
}
</style>
