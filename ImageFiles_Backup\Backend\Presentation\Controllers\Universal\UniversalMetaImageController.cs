using Marketplace.Application.Commands.MetaImage;
using Marketplace.Domain.Services;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Universal;

/// <summary>
/// Універсальний контролер для роботи з мета-зображеннями різних типів сутностей
/// </summary>
[ApiController]
[Route("api/universal/meta-images")]
[Authorize(Roles = "Admin,Moderator")]
public class UniversalMetaImageController : BasicApiController
{
    private readonly IMediator _mediator;
    private readonly IImageService _imageService;

    public UniversalMetaImageController(IMediator mediator, IImageService imageService)
    {
        _mediator = mediator;
        _imageService = imageService;
    }

    /// <summary>
    /// Завантажити мета-зображення для сутності
    /// </summary>
    /// <param name="entityType">Тип сутності (product, category, company)</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="file">Файл зображення</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат завантаження</returns>
    [HttpPost("{entityType}/{entityId}")]
    public async Task<IActionResult> UploadMetaImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromForm] IFormFile file,
        CancellationToken cancellationToken = default)
    {
        if (file == null)
        {
            return BadRequest(ApiResponse.Failure("Не вибрано файл для завантаження."));
        }

        // Перевіряємо, чи підтримується мета-зображення для цього типу сутності
        var supportedTypes = new[] { "product", "category", "company" };
        if (!supportedTypes.Contains(entityType.ToLower()))
        {
            return BadRequest(ApiResponse.Failure($"Мета-зображення не підтримується для типу сутності: {entityType}"));
        }

        try
        {
            var command = new UploadUniversalMetaImageCommand(entityType, entityId, file);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(ApiResponse<FileUploadResult>.SuccessWithData(result, "Мета-зображення успішно завантажено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка завантаження мета-зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Видалити мета-зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат видалення</returns>
    [HttpDelete("{entityType}/{entityId}")]
    public async Task<IActionResult> DeleteMetaImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        CancellationToken cancellationToken = default)
    {
        // Перевіряємо, чи підтримується мета-зображення для цього типу сутності
        var supportedTypes = new[] { "product", "category", "company" };
        if (!supportedTypes.Contains(entityType.ToLower()))
        {
            return BadRequest(ApiResponse.Failure($"Мета-зображення не підтримується для типу сутності: {entityType}"));
        }

        try
        {
            var command = new DeleteUniversalMetaImageCommand(entityType, entityId);
            var result = await _mediator.Send(command, cancellationToken);

            return result
                ? Ok(ApiResponse.SuccessResponse("Мета-зображення успішно видалено."))
                : NotFound(ApiResponse.Failure("Мета-зображення не знайдено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка видалення мета-зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Оновити мета-зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="file">Новий файл зображення</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>Результат оновлення</returns>
    [HttpPut("{entityType}/{entityId}")]
    public async Task<IActionResult> UpdateMetaImage(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        [FromForm] IFormFile file,
        CancellationToken cancellationToken = default)
    {
        if (file == null)
        {
            return BadRequest(ApiResponse.Failure("Не вибрано файл для оновлення."));
        }

        // Перевіряємо, чи підтримується мета-зображення для цього типу сутності
        var supportedTypes = new[] { "product", "category", "company" };
        if (!supportedTypes.Contains(entityType.ToLower()))
        {
            return BadRequest(ApiResponse.Failure($"Мета-зображення не підтримується для типу сутності: {entityType}"));
        }

        try
        {
            var command = new UpdateUniversalMetaImageCommand(entityType, entityId, file);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(ApiResponse<FileUploadResult>.SuccessWithData(result, "Мета-зображення успішно оновлено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка оновлення мета-зображення: {ex.Message}"));
        }
    }

    /// <summary>
    /// Отримати URL мета-зображення сутності
    /// </summary>
    /// <param name="entityType">Тип сутності</param>
    /// <param name="entityId">ID сутності</param>
    /// <param name="cancellationToken">Токен скасування</param>
    /// <returns>URL мета-зображення</returns>
    [HttpGet("{entityType}/{entityId}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetMetaImageUrl(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId,
        CancellationToken cancellationToken = default)
    {
        // Перевіряємо, чи підтримується мета-зображення для цього типу сутності
        var supportedTypes = new[] { "product", "category", "company" };
        if (!supportedTypes.Contains(entityType.ToLower()))
        {
            return BadRequest(ApiResponse.Failure($"Мета-зображення не підтримується для типу сутності: {entityType}"));
        }

        try
        {
            var imageUrl = await _imageService.GetImageUrlAsync(entityType, entityId, "meta", null, cancellationToken);
            
            return imageUrl != null 
                ? Ok(ApiResponse<string>.SuccessWithData(imageUrl))
                : NotFound(ApiResponse.Failure("Мета-зображення не знайдено."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse.Failure($"Помилка отримання URL мета-зображення: {ex.Message}"));
        }
    }
}
