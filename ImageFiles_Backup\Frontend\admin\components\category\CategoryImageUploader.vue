<template>
  <div class="category-image-uploader">
    <label v-if="label" class="form-label">{{ label }}</label>
    
    <div class="upload-container">
      <!-- Current Image Display -->
      <div v-if="(currentImageUrl || previewUrl) && !pendingRemoval" class="current-image">
        <img 
          :src="previewUrl || currentImageUrl" 
          :alt="imageType === 'logo' ? 'Category Image' : 'Category Meta Image'"
          class="image-preview"
          :class="{ 'meta-image': imageType === 'meta' }"
        />
        <div class="image-overlay">
          <button 
            type="button" 
            class="btn-remove" 
            @click="removeImage"
            :disabled="uploading"
          >
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>

      <!-- Upload Area -->
      <div v-else class="upload-area" @click="triggerFileInput">
        <div class="upload-content">
          <i class="fas fa-cloud-upload-alt upload-icon"></i>
          <p class="upload-text">
            {{ pendingRemoval ? 'Image will be removed - Click to upload new' : (uploadText || 'Upload Image') }}
          </p>
          <p class="upload-hint">
            {{ imageType === 'meta' ? 'Recommended: 1200x630px for social media' : 'Recommended: 300x300px' }}
          </p>
        </div>
      </div>

      <!-- File Input -->
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="handleFileSelect"
        style="display: none"
      />

      <!-- Upload Progress -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
        </div>
        <p class="progress-text">Uploading... {{ uploadProgress }}%</p>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        {{ error }}
      </div>
    </div>

    <!-- Social Preview for Meta Images -->
    <div v-if="imageType === 'meta' && (previewUrl || currentImageUrl) && !pendingRemoval" class="social-preview">
      <h4>Social Media Preview</h4>
      <div class="preview-card">
        <img 
          :src="previewUrl || currentImageUrl" 
          alt="Social preview"
          class="preview-image"
        />
        <div class="preview-content">
          <h5>{{ socialTitle || 'Category Name' }}</h5>
          <p>{{ socialDescription || 'Category description for social media sharing.' }}</p>
          <span class="preview-url">marketplace.com</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import imageService from '@/services/image.service'

export default {
  name: 'CategoryImageUploader',
  props: {
    label: {
      type: String,
      default: ''
    },
    uploadText: {
      type: String,
      default: 'Upload Image'
    },
    currentImageUrl: {
      type: String,
      default: null
    },
    entityId: {
      type: String,
      required: true
    },
    imageType: {
      type: String,
      required: true,
      validator: value => ['logo', 'meta'].includes(value)
    },
    socialTitle: {
      type: String,
      default: ''
    },
    socialDescription: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      previewUrl: null,
      pendingFile: null,
      pendingRemoval: false,
      uploading: false,
      uploadProgress: 0,
      error: null
    }
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click()
    },

    async handleFileSelect(event) {
      const file = event.target.files[0]
      if (!file) return

      this.error = null

      // Validate file
      const validation = imageService.validateImageFile(file, {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      })

      if (!validation.isValid) {
        this.error = validation.errors.join(', ')
        return
      }

      try {
        // Create preview
        this.previewUrl = await imageService.createImagePreview(file)
        this.pendingFile = file
        this.pendingRemoval = false

        // Emit change event
        this.$emit('image-changed', {
          type: 'upload',
          file,
          previewUrl: this.previewUrl,
          imageType: this.imageType
        })

        // Clear file input
        event.target.value = ''
      } catch (error) {
        console.error('Error creating preview:', error)
        this.error = 'Failed to create image preview'
      }
    },

    removeImage() {
      this.previewUrl = null
      this.pendingFile = null
      this.pendingRemoval = true
      this.error = null

      this.$emit('image-changed', {
        type: 'removal',
        imageType: this.imageType,
        originalImage: this.currentImageUrl
      })
    },

    hasChanges() {
      return !!(this.pendingFile || this.pendingRemoval)
    },

    async processChanges() {
      if (!this.hasChanges()) {
        return { success: true, url: this.currentImageUrl }
      }

      try {
        if (this.pendingRemoval) {
          // Remove image
          if (this.currentImageUrl) {
            await this.deleteImage()
          }
          return { success: true, url: null }
        }

        if (this.pendingFile) {
          // Upload new image
          const result = await this.uploadImage(this.pendingFile)
          return { success: true, url: result.fileUrl }
        }

        return { success: true, url: this.currentImageUrl }
      } catch (error) {
        console.error('Error processing changes:', error)
        return { success: false, error: error.message }
      }
    },

    async uploadImage(file) {
      this.uploading = true
      this.uploadProgress = 0
      this.error = null

      try {
        let response
        if (this.imageType === 'meta') {
          response = await imageService.uploadMetaImage(
            'category',
            this.entityId,
            file,
            (progress) => {
              this.uploadProgress = progress
            }
          )
        } else {
          response = await imageService.uploadImage(
            'category',
            this.entityId,
            file,
            (progress) => {
              this.uploadProgress = progress
            }
          )
        }

        // Clear pending state
        this.pendingFile = null
        this.previewUrl = null

        return response.data
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.uploading = false
        this.uploadProgress = 0
      }
    },

    async deleteImage() {
      try {
        if (this.imageType === 'meta') {
          await imageService.deleteMetaImage('category', this.entityId)
        } else {
          await imageService.deleteEntityImage('category', this.entityId, 'main')
        }
      } catch (error) {
        // Log error but don't throw - image might already be deleted
        console.warn('Error deleting image:', error)
      }
    },

    reset() {
      this.previewUrl = null
      this.pendingFile = null
      this.pendingRemoval = false
      this.uploading = false
      this.uploadProgress = 0
      this.error = null
    },

    cancelRemoval() {
      this.pendingRemoval = false
      this.$emit('image-changed', {
        type: 'cancel-removal',
        imageType: this.imageType
      })
    }
  },

  beforeUnmount() {
    // Clear any pending operations
    try {
      this.pendingFile = null
      this.pendingRemoval = false
      this.error = null
      this.uploadProgress = 0

      // Revoke blob URLs to prevent memory leaks
      if (this.previewUrl && this.previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.previewUrl)
        this.previewUrl = null
      }
    } catch (error) {
      console.error('Error in CategoryImageUploader beforeUnmount:', error)
    }
  },

  unmounted() {
    // Final cleanup
    try {
      this.pendingFile = null
      this.pendingRemoval = false
    } catch (error) {
      console.error('Error in CategoryImageUploader unmounted:', error)
    }
  }
}
</script>

<style scoped>
.category-image-uploader {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.upload-container {
  position: relative;
}

.current-image {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.image-preview {
  width: 200px;
  height: 200px;
  object-fit: cover;
  display: block;
}

.image-preview.meta-image {
  width: 300px;
  height: 157px; /* 1200x630 aspect ratio */
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.current-image:hover .image-overlay {
  opacity: 1;
}

.btn-remove {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-remove:hover {
  background: #dc2626;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  background: #f9fafb;
}

.upload-area:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.upload-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.upload-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.error-message {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.social-preview {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.social-preview h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.preview-card {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  max-width: 500px;
}

.preview-image {
  width: 120px;
  height: 63px;
  object-fit: cover;
  flex-shrink: 0;
}

.preview-content {
  padding: 0.75rem;
  flex: 1;
}

.preview-content h5 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.preview-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.3;
}

.preview-url {
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
}
</style>
