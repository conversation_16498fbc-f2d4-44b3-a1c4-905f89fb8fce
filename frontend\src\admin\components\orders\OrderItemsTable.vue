<template>
  <div class="order-items-table">
    <div class="card">
      <div class="card-header">
        <p class="card-header-title">
          <span class="icon">
            <i class="fas fa-shopping-cart"></i>
          </span>
          Order Items
        </p>
      </div>
      <div class="card-content">
        <div class="table-container">
          <table class="table is-fullwidth is-striped is-hoverable">
            <thead>
              <tr>
                <th>Product</th>
                <th class="has-text-right">Price per Unit</th>
                <th class="has-text-centered">Quantity</th>
                <th class="has-text-right">Total Price</th>
                <th v-if="editable" class="has-text-centered">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in items" :key="item.id || index">
                <!-- Product Column -->
                <td>
                  <div class="media">
                    <div class="media-left" v-if="item.productImage">
                      <figure class="image is-48x48">
                        <img 
                          :src="item.productImage" 
                          :alt="item.productName"
                          class="is-rounded"
                          @error="handleImageError"
                        >
                      </figure>
                    </div>
                    <div class="media-left" v-else>
                      <figure class="image is-48x48">
                        <div class="placeholder-image">
                          <i class="fas fa-image"></i>
                        </div>
                      </figure>
                    </div>
                    <div class="media-content">
                      <p class="title is-6">{{ item.productName || 'Unknown Product' }}</p>
                      <p class="subtitle is-7 has-text-grey" v-if="item.productId">
                        ID: {{ item.productId }}
                      </p>
                    </div>
                  </div>
                </td>

                <!-- Price per Unit Column -->
                <td class="has-text-right">
                  <span class="tag is-light">
                    {{ formatCurrency(item.price || item.unitPrice) }}
                  </span>
                </td>

                <!-- Quantity Column -->
                <td class="has-text-centered">
                  <div v-if="editable" class="field has-addons has-addons-centered">
                    <div class="control">
                      <button 
                        class="button is-small"
                        @click="decreaseQuantity(index)"
                        :disabled="item.quantity <= 1"
                      >
                        <span class="icon is-small">
                          <i class="fas fa-minus"></i>
                        </span>
                      </button>
                    </div>
                    <div class="control">
                      <input 
                        class="input is-small has-text-centered" 
                        type="number" 
                        v-model.number="item.quantity"
                        @change="updateQuantity(index, item.quantity)"
                        min="0"
                        style="width: 60px;"
                      >
                    </div>
                    <div class="control">
                      <button 
                        class="button is-small"
                        @click="increaseQuantity(index)"
                      >
                        <span class="icon is-small">
                          <i class="fas fa-plus"></i>
                        </span>
                      </button>
                    </div>
                  </div>
                  <span v-else class="tag is-primary">
                    {{ item.quantity }}
                  </span>
                </td>

                <!-- Total Price Column -->
                <td class="has-text-right">
                  <strong class="has-text-primary">
                    {{ formatCurrency(item.total || (item.price * item.quantity)) }}
                  </strong>
                </td>

                <!-- Actions Column (only in edit mode) -->
                <td v-if="editable" class="has-text-centered">
                  <button 
                    class="button is-small is-danger is-outlined"
                    @click="removeItem(index)"
                    title="Remove item"
                  >
                    <span class="icon is-small">
                      <i class="fas fa-trash"></i>
                    </span>
                  </button>
                </td>
              </tr>

              <!-- Empty state -->
              <tr v-if="!items || items.length === 0">
                <td :colspan="editable ? 5 : 4" class="has-text-centered has-text-grey">
                  <div class="py-4">
                    <span class="icon is-large">
                      <i class="fas fa-shopping-cart fa-2x"></i>
                    </span>
                    <p class="mt-2">No items in this order</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Add Item Button (only in edit mode) -->
        <div v-if="editable" class="has-text-centered mt-4">
          <button 
            class="button is-primary is-outlined"
            @click="$emit('add-item')"
          >
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>Add Item</span>
          </button>
        </div>

        <!-- Order Summary -->
        <div class="box mt-4 has-background-light">
          <div class="columns">
            <div class="column is-8">
              <p class="title is-6">Order Summary</p>
            </div>
            <div class="column is-4 has-text-right">
              <div class="field is-grouped is-grouped-multiline is-justify-content-flex-end">
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag is-dark">Subtotal</span>
                    <span class="tag is-light">{{ formatCurrency(subtotal) }}</span>
                  </div>
                </div>

                <div class="control" v-if="shipping > 0">
                  <div class="tags has-addons">
                    <span class="tag is-dark">Shipping</span>
                    <span class="tag is-light">{{ formatCurrency(shipping) }}</span>
                  </div>
                </div>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag is-primary">Total</span>
                    <span class="tag is-primary is-light">{{ formatCurrency(total) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  editable: {
    type: Boolean,
    default: false
  },
  tax: {
    type: Number,
    default: 0
  },
  shipping: {
    type: Number,
    default: 0
  },
  discount: {
    type: Number,
    default: 0
  }
});

// Emits
const emit = defineEmits([
  'update-quantity',
  'remove-item',
  'add-item'
]);

// Computed properties
const subtotal = computed(() => {
  return props.items.reduce((sum, item) => {
    return sum + ((item.price || item.unitPrice || 0) * (item.quantity || 0));
  }, 0);
});

const total = computed(() => {
  return subtotal.value + props.shipping - props.discount;
});

// Methods
const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const handleImageError = (event) => {
  event.target.style.display = 'none';
  event.target.parentElement.innerHTML = '<div class="placeholder-image"><i class="fas fa-image"></i></div>';
};

const increaseQuantity = (index) => {
  const newQuantity = props.items[index].quantity + 1;
  updateQuantity(index, newQuantity);
};

const decreaseQuantity = (index) => {
  const newQuantity = Math.max(1, props.items[index].quantity - 1);
  updateQuantity(index, newQuantity);
};

const updateQuantity = (index, quantity) => {
  if (quantity <= 0) {
    removeItem(index);
  } else {
    emit('update-quantity', index, quantity);
  }
};

const removeItem = (index) => {
  emit('remove-item', index);
};
</script>

<style scoped>
.order-items-table {
  margin-bottom: 1rem;
}

.placeholder-image {
  width: 48px;
  height: 48px;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dbdbdb;
}

.media-content {
  overflow: hidden;
}

.title.is-6 {
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.subtitle.is-7 {
  margin-bottom: 0;
  line-height: 1.1;
}

.field.has-addons {
  margin-bottom: 0;
}

.tags.has-addons {
  margin-bottom: 0.5rem;
}

.box {
  border-radius: 6px;
}

.table td {
  vertical-align: middle;
}

.table thead th {
  border-bottom: 2px solid #dbdbdb;
  font-weight: 600;
}
</style>
