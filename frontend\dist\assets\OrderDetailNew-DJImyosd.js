import{_ as ls,g as c,h as is,H as ds,i as rs,f as cs,c as l,a as s,t as o,d,z as _,b as C,w as S,r as us,k as I,m as vs,n as y,F as j,p as G,x as U,D as fs,y as J,l as ps,e as ms,o as i}from"./index-L-hJxM_5.js";import{a as x,b as K,c as N,o as A}from"./orders-DGdj4xZm.js";const bs={class:"order-detail-new"},_s={key:0,class:"loading-state"},ys={key:1,class:"error-state"},gs={class:"error-message"},hs={key:2,class:"not-found-state"},ws={key:3,class:"order-content"},ks={class:"order-header"},Cs={class:"header-content"},Ss={class:"breadcrumb"},Ns={class:"breadcrumb-item breadcrumb-current"},As={class:"order-title"},Os={class:"order-subtitle"},Ps={class:"header-actions"},Is={class:"info-section"},xs={class:"info-grid"},Ds={class:"info-card info-card-main"},Es={class:"info-card-content"},Ls={class:"info-row"},Us={class:"info-item"},Ts={class:"info-value"},Ms={class:"info-item"},Fs={class:"info-row"},Vs={class:"info-item"},$s={class:"info-value info-value-price"},Rs={class:"info-item"},qs={class:"info-row"},Bs={class:"info-item"},zs={class:"info-item"},Hs={class:"info-value"},Qs={class:"info-card"},js={class:"info-card-content"},Gs={class:"info-row"},Js={class:"info-item"},Ks={class:"info-value"},Ws={class:"info-item"},Xs={class:"info-value"},Ys={class:"info-row"},Zs={class:"info-item"},st={class:"info-value"},tt={class:"info-item"},et={class:"info-value"},at={class:"info-card"},ot={class:"info-card-content"},nt={class:"info-row"},lt={class:"info-item"},it={class:"info-item"},dt={class:"info-value"},rt={class:"info-card"},ct={class:"info-card-content"},ut={class:"info-row"},vt={class:"info-item"},ft={class:"info-value"},pt={class:"info-item"},mt={class:"items-section"},bt={class:"section-header"},_t={class:"section-title"},yt={class:"section-actions"},gt=["disabled"],ht={key:0,class:"items-loading"},wt={key:1,class:"no-items"},kt={key:2,class:"items-table-wrapper"},Ct={class:"table-header"},St={class:"table-info"},Nt={class:"table-count"},At={class:"table-total"},Ot={class:"items-table"},Pt={class:"table"},It={class:"product-cell"},xt={class:"product-info"},Dt={key:0,class:"product-image"},Et=["src","alt"],Lt={class:"product-details"},Ut={class:"product-name"},Tt={class:"product-id"},Mt={class:"price-cell"},Ft={class:"price-tag"},Vt={class:"quantity-cell"},$t={class:"quantity-badge"},Rt={class:"total-cell"},qt={class:"total-price"},Bt={class:"actions-cell"},zt={class:"action-buttons"},Ht={class:"order-total-section"},Qt={class:"order-total-card"},jt={class:"total-row"},Gt={class:"total-amount"},Jt={key:0,class:"notes-section"},Kt={class:"section-header"},Wt={class:"section-actions"},Xt={key:0,class:"add-note-form"},Yt={class:"form-field"},Zt={class:"form-actions"},se=["disabled"],te={key:0,class:"fas fa-spinner fa-spin"},ee={key:1,class:"fas fa-save"},ae={key:1,class:"notes-list"},oe={class:"note-header"},ne={class:"note-author"},le={class:"note-date"},ie={class:"note-content"},de={key:2,class:"no-notes"},re={class:"modal-content"},ce={class:"modal-body"},ue={class:"form-field"},ve={class:"form-field"},fe={class:"modal-footer"},pe=["disabled"],me={key:0,class:"fas fa-spinner fa-spin"},be={key:1,class:"fas fa-save"},_e={__name:"OrderDetailNew",setup(ye){const D=cs(),W=ms(),E=c(!1),g=c(!1),T=c(!1),m=c([]),v=c(!1),f=c(""),h=c(!1),w=c(""),a=c(null),p=c([]),O=c(!1),b=c(!1),r=c({status:"",paymentStatus:""}),M=is(()=>{var e,t,u;return((e=a.value)==null?void 0:e.customerName)||((u=(t=a.value)==null?void 0:t.customer)==null?void 0:u.name)||"Unknown Customer"}),L=async e=>{E.value=!0,w.value="";try{const t=await A.getById(e);a.value=t,r.value.status=x(t.status).toLowerCase()||"pending",r.value.paymentStatus=N(t.paymentStatus).toLowerCase()||"pending",await Promise.all([F(t.id),V(t.id)])}catch(t){console.error("Error fetching order:",t),t.name!=="CanceledError"&&t.code!=="ERR_CANCELED"&&(w.value="Failed to load order. Please try again.")}finally{E.value=!1}},F=async e=>{g.value=!0;try{const t=await A.getOrderItems(e);p.value=t.data||t||[]}catch(t){console.error("Error fetching order items:",t),p.value=[]}finally{g.value=!1}},X=()=>{var e;(e=a.value)!=null&&e.id&&F(a.value.id)},V=async e=>{if(e){T.value=!0;try{const t=await A.getOrderNotes(e);t&&t.data?m.value=Array.isArray(t.data)?t.data:[]:m.value=[]}catch(t){console.error("Error fetching order notes:",t),m.value=[]}finally{T.value=!1}}},Y=async()=>{var e;if(!(!f.value.trim()||!((e=a.value)!=null&&e.id))){h.value=!0;try{await A.addOrderNote(a.value.id,f.value.trim()),f.value="",v.value=!1,await V(a.value.id)}catch(t){console.error("Error adding note:",t),w.value="Failed to add note: "+(t.message||"Unknown error")}finally{h.value=!1}}},k=e=>e==null?"N/A":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(e),Z=e=>e?new Date(e).toLocaleDateString("uk-UA"):"N/A",$=e=>e?new Date(e).toLocaleString("uk-UA"):"N/A",ss=()=>p.value.reduce((e,t)=>e+(t.totalPrice||0),0),ts=e=>{if(!e||typeof e!="string")return"status-badge-light";switch(e.toLowerCase()){case"delivered":case"completed":return"status-badge-success";case"cancelled":case"failed":return"status-badge-danger";case"processing":case"shipped":return"status-badge-info";case"pending":case"confirmed":return"status-badge-warning";default:return"status-badge-light"}},es=e=>{e.target.style.display="none"},R=e=>{e&&W.push(`/admin/customers/${e}`)},as=()=>{window.print()},os=()=>{var e,t;console.log("Opening status modal..."),r.value.status=x((e=a.value)==null?void 0:e.status).toLowerCase()||"processing",r.value.paymentStatus=N((t=a.value)==null?void 0:t.paymentStatus).toLowerCase()||"pending",O.value=!0,console.log("showStatusModal.value:",O.value),console.log("statusForm.value:",r.value)},P=()=>{var e,t;O.value=!1,r.value.status=x((e=a.value)==null?void 0:e.status).toLowerCase()||"processing",r.value.paymentStatus=N((t=a.value)==null?void 0:t.paymentStatus).toLowerCase()||"pending"},ns=async()=>{var e;if((e=a.value)!=null&&e.id){b.value=!0;try{await A.updateOrderStatus(a.value.id,{status:r.value.status,paymentStatus:r.value.paymentStatus}),a.value.status=r.value.status,a.value.paymentStatus=r.value.paymentStatus,P(),alert("Order status updated successfully!")}catch(t){console.error("Error updating order status:",t),alert("Failed to update order status. Please try again.")}finally{b.value=!1}}};return ds(()=>D.params.id,e=>{e&&L(e)},{immediate:!0}),rs(()=>{const e=D.params.id;e&&L(e)}),(e,t)=>{var q,B,z,H,Q;const u=us("router-link");return i(),l("div",bs,[E.value?(i(),l("div",_s,t[8]||(t[8]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Loading order details...",-1)]))):w.value?(i(),l("div",ys,[t[10]||(t[10]=s("div",{class:"error-icon"},[s("i",{class:"fas fa-exclamation-triangle"})],-1)),t[11]||(t[11]=s("h3",{class:"error-title"},"Error Loading Order",-1)),s("p",gs,o(w.value),1),s("button",{class:"retry-btn",onClick:t[0]||(t[0]=n=>L(_(D).params.id))},t[9]||(t[9]=[s("i",{class:"fas fa-redo"},null,-1),d(" Try Again ")]))])):a.value?(i(),l("div",ws,[s("div",ks,[s("div",Cs,[s("nav",Ss,[C(u,{to:"/admin",class:"breadcrumb-item"},{default:S(()=>t[16]||(t[16]=[d("Dashboard")])),_:1}),t[18]||(t[18]=s("span",{class:"breadcrumb-separator"},"/",-1)),C(u,{to:"/admin/orders",class:"breadcrumb-item"},{default:S(()=>t[17]||(t[17]=[d("Orders")])),_:1}),t[19]||(t[19]=s("span",{class:"breadcrumb-separator"},"/",-1)),s("span",Ns,"Order #"+o(a.value.id),1)]),s("h1",As,"Order #"+o(a.value.id),1),s("p",Os,o(Z(a.value.createdAt)),1)]),s("div",Ps,[s("button",{class:"action-btn action-btn-secondary",onClick:as},t[20]||(t[20]=[s("i",{class:"fas fa-print"},null,-1),d(" Print ")])),s("button",{class:"action-btn action-btn-info",onClick:vs(os,["prevent"])},t[21]||(t[21]=[s("i",{class:"fas fa-cog"},null,-1),d(" Update Status ")])),C(u,{to:`/admin/orders/${a.value.id}/edit`,class:"action-btn action-btn-primary"},{default:S(()=>t[22]||(t[22]=[s("i",{class:"fas fa-edit"},null,-1),d(" Edit Order ")])),_:1},8,["to"])])]),s("div",Is,[t[41]||(t[41]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},[s("i",{class:"fas fa-info-circle"}),d(" Order Information ")])],-1)),s("div",xs,[s("div",Ds,[t[29]||(t[29]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},"Order Details")],-1)),s("div",Es,[s("div",Ls,[s("div",Us,[t[23]||(t[23]=s("label",{class:"info-label"},"Order ID",-1)),s("span",Ts,o(a.value.id),1)]),s("div",Ms,[t[24]||(t[24]=s("label",{class:"info-label"},"Customer",-1)),s("span",{class:"info-value info-value-link",onClick:t[1]||(t[1]=n=>R(a.value.customerId))},o(M.value),1)])]),s("div",Fs,[s("div",Vs,[t[25]||(t[25]=s("label",{class:"info-label"},"Total Price",-1)),s("span",$s,o(k(a.value.totalPriceAmount||((q=a.value.totalPrice)==null?void 0:q.amount)||0)),1)]),s("div",Rs,[t[26]||(t[26]=s("label",{class:"info-label"},"Status",-1)),s("span",{class:y(["status-badge",ts(a.value.status)])},o(_(x)(a.value.status)),3)])]),s("div",qs,[s("div",Bs,[t[27]||(t[27]=s("label",{class:"info-label"},"Payment Status",-1)),s("span",{class:y(["status-badge",_(K)(a.value.paymentStatus)])},o(_(N)(a.value.paymentStatus)||"Pending"),3)]),s("div",zs,[t[28]||(t[28]=s("label",{class:"info-label"},"Created At",-1)),s("span",Hs,o($(a.value.createdAt)),1)])])])]),s("div",Qs,[t[34]||(t[34]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},[s("i",{class:"fas fa-shipping-fast"}),d(" Shipping Information ")])],-1)),s("div",js,[s("div",Gs,[s("div",Js,[t[30]||(t[30]=s("label",{class:"info-label"},"Address Line",-1)),s("span",Ks,o(((B=a.value.shippingAddress)==null?void 0:B.address1)||a.value.shippingAddressLine||"N/A"),1)]),s("div",Ws,[t[31]||(t[31]=s("label",{class:"info-label"},"Country",-1)),s("span",Xs,o(((z=a.value.shippingAddress)==null?void 0:z.country)||a.value.shippingCountry||"N/A"),1)])]),s("div",Ys,[s("div",Zs,[t[32]||(t[32]=s("label",{class:"info-label"},"City",-1)),s("span",st,o(((H=a.value.shippingAddress)==null?void 0:H.city)||a.value.shippingCity||"N/A"),1)]),s("div",tt,[t[33]||(t[33]=s("label",{class:"info-label"},"Shipping Method",-1)),s("span",et,o(a.value.shippingMethodName||"N/A"),1)])])])]),s("div",at,[t[37]||(t[37]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},[s("i",{class:"fas fa-user"}),d(" Customer Information ")])],-1)),s("div",ot,[s("div",nt,[s("div",lt,[t[35]||(t[35]=s("label",{class:"info-label"},"Customer Name",-1)),s("span",{class:"info-value info-value-link",onClick:t[2]||(t[2]=n=>R(a.value.customerId))},o(M.value),1)]),s("div",it,[t[36]||(t[36]=s("label",{class:"info-label"},"Email",-1)),s("span",dt,o(a.value.customerEmail||"N/A"),1)])])])]),s("div",rt,[t[40]||(t[40]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},[s("i",{class:"fas fa-credit-card"}),d(" Payment Information ")])],-1)),s("div",ct,[s("div",ut,[s("div",vt,[t[38]||(t[38]=s("label",{class:"info-label"},"Payment Method",-1)),s("span",ft,o(a.value.paymentMethodText||"N/A"),1)]),s("div",pt,[t[39]||(t[39]=s("label",{class:"info-label"},"Payment Status",-1)),s("span",{class:y(["status-badge",_(K)(a.value.paymentStatus)])},o(_(N)(a.value.paymentStatus)),3)])])])])])]),s("div",mt,[s("div",bt,[s("h2",_t,[t[42]||(t[42]=s("i",{class:"fas fa-box"},null,-1)),d(" Order Items ("+o(p.value.length)+") ",1)]),s("div",yt,[s("button",{class:"action-btn action-btn-secondary",onClick:X,disabled:g.value},[s("i",{class:y(["fas fa-sync-alt",{"fa-spin":g.value}])},null,2),t[43]||(t[43]=d(" Refresh "))],8,gt)])]),g.value?(i(),l("div",ht,t[44]||(t[44]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Loading order items...",-1)]))):p.value.length===0?(i(),l("div",wt,t[45]||(t[45]=[s("div",{class:"no-items-icon"},[s("i",{class:"fas fa-box-open"})],-1),s("h3",{class:"no-items-title"},"No Items Found",-1),s("p",{class:"no-items-message"},"This order doesn't have any items.",-1)]))):(i(),l("div",kt,[s("div",Ct,[s("div",St,[s("span",Nt,o(p.value.length)+" items in this order",1),s("span",At,"Total: "+o(k(ss())),1)])]),s("div",Ot,[s("table",Pt,[t[47]||(t[47]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Unit Price"),s("th",null,"Quantity"),s("th",null,"Total Price"),s("th",null,"Actions")])],-1)),s("tbody",null,[(i(!0),l(j,null,G(p.value,n=>(i(),l("tr",{key:n.id,class:"table-row"},[s("td",It,[s("div",xt,[n.productImage?(i(),l("div",Dt,[s("img",{src:n.productImage,alt:n.productName,onError:es},null,40,Et)])):I("",!0),s("div",Lt,[s("h4",Ut,o(n.productName||"Unknown Product"),1),s("p",Tt,"Product ID: "+o(n.productId),1)])])]),s("td",Mt,[s("span",Ft,o(k(n.price||n.priceAmount||0)),1)]),s("td",Vt,[s("span",$t,o(n.quantity),1)]),s("td",Rt,[s("span",qt,o(k(n.total||n.price*n.quantity||0)),1)]),s("td",Bt,[s("div",zt,[C(u,{to:`/admin/products/${n.productId}/view`,class:"action-btn action-btn-sm action-btn-info",title:"View Product"},{default:S(()=>t[46]||(t[46]=[s("i",{class:"fas fa-eye"},null,-1)])),_:2},1032,["to"])])])]))),128))])])]),s("div",Ht,[s("div",Qt,[s("div",jt,[t[48]||(t[48]=s("span",{class:"total-label"},"Order Total:",-1)),s("span",Gt,o(k(a.value.totalPriceAmount||((Q=a.value.totalPrice)==null?void 0:Q.amount)||0)),1)])])])]))]),m.value.length>0||v.value?(i(),l("div",Jt,[s("div",Kt,[t[50]||(t[50]=s("h2",{class:"section-title"},[s("i",{class:"fas fa-sticky-note"}),d(" Order Notes ")],-1)),s("div",Wt,[s("button",{class:"action-btn action-btn-primary",onClick:t[3]||(t[3]=n=>v.value=!v.value)},t[49]||(t[49]=[s("i",{class:"fas fa-plus"},null,-1),d(" Add Note ")]))])]),v.value?(i(),l("div",Xt,[s("div",Yt,[t[51]||(t[51]=s("label",{class:"form-label"},"Add Note",-1)),U(s("textarea",{"onUpdate:modelValue":t[4]||(t[4]=n=>f.value=n),class:"form-textarea",placeholder:"Enter your note here...",rows:"3"},"            ",512),[[fs,f.value]])]),s("div",Zt,[s("button",{class:"action-btn action-btn-primary",onClick:Y,disabled:!f.value.trim()||h.value},[h.value?(i(),l("i",te)):(i(),l("i",ee)),d(" "+o(h.value?"Adding...":"Add Note"),1)],8,se),s("button",{class:"action-btn action-btn-secondary",onClick:t[5]||(t[5]=n=>{v.value=!1,f.value=""})}," Cancel ")])])):I("",!0),m.value.length>0?(i(),l("div",ae,[(i(!0),l(j,null,G(m.value,n=>(i(),l("div",{key:n.id,class:"note-item"},[s("div",oe,[s("span",ne,o(n.authorName||"Admin"),1),s("span",le,o($(n.createdAt)),1)]),s("div",ie,o(n.content||n.note),1)]))),128))])):v.value?I("",!0):(i(),l("div",de,t[52]||(t[52]=[s("div",{class:"no-notes-icon"},[s("i",{class:"fas fa-sticky-note"})],-1),s("p",{class:"no-notes-text"},"No notes available for this order.",-1)])))])):I("",!0)])):(i(),l("div",hs,[t[13]||(t[13]=s("div",{class:"not-found-icon"},[s("i",{class:"fas fa-shopping-cart"})],-1)),t[14]||(t[14]=s("h3",{class:"not-found-title"},"Order Not Found",-1)),t[15]||(t[15]=s("p",{class:"not-found-message"},"The requested order does not exist or has been deleted.",-1)),C(u,{to:"/admin/orders",class:"back-btn"},{default:S(()=>t[12]||(t[12]=[s("i",{class:"fas fa-arrow-left"},null,-1),d(" Back to Orders ")])),_:1})])),s("div",{class:y(["modal",{"modal-active":O.value}])},[s("div",{class:"modal-backdrop",onClick:P}),s("div",re,[s("div",{class:"modal-header"},[t[54]||(t[54]=s("h3",{class:"modal-title"},"Update Order Status",-1)),s("button",{class:"modal-close",onClick:P},t[53]||(t[53]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",ce,[s("div",ue,[t[56]||(t[56]=s("label",{class:"form-label"},"Order Status",-1)),U(s("select",{"onUpdate:modelValue":t[6]||(t[6]=n=>r.value.status=n),class:"form-select"},t[55]||(t[55]=[ps('<option value="processing" data-v-97c4fcd9>Processing</option><option value="pending" data-v-97c4fcd9>Pending</option><option value="shipped" data-v-97c4fcd9>Shipped</option><option value="delivered" data-v-97c4fcd9>Delivered</option><option value="cancelled" data-v-97c4fcd9>Cancelled</option>',5)]),512),[[J,r.value.status]])]),s("div",ve,[t[58]||(t[58]=s("label",{class:"form-label"},"Payment Status",-1)),U(s("select",{"onUpdate:modelValue":t[7]||(t[7]=n=>r.value.paymentStatus=n),class:"form-select"},t[57]||(t[57]=[s("option",{value:"pending"},"Pending",-1),s("option",{value:"paid"},"Paid",-1),s("option",{value:"failed"},"Failed",-1),s("option",{value:"refunded"},"Refunded",-1)]),512),[[J,r.value.paymentStatus]])])]),s("div",fe,[s("button",{class:y(["action-btn action-btn-primary",{"action-btn-loading":b.value}]),onClick:ns,disabled:b.value},[b.value?(i(),l("i",me)):(i(),l("i",be)),d(" "+o(b.value?"Updating...":"Update Status"),1)],10,pe),s("button",{class:"action-btn action-btn-secondary",onClick:P},"Cancel")])])],2)])}}},we=ls(_e,[["__scopeId","data-v-97c4fcd9"]]);export{we as default};
