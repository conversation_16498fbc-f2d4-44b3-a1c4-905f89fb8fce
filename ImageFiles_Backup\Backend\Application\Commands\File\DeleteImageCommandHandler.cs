﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.File;

/// <summary>
/// Обробник команди для видалення зображення для Category, Company
/// </summary>
public class DeleteImageCommandHandler : IRequestHandler<DeleteImageCommand, bool>
{
    private readonly ICategoryRepository _categoryRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<DeleteImageCommandHandler> _logger;

    public DeleteImageCommandHandler(
        ICategoryRepository categoryRepository,
        ICompanyRepository companyRepository,
        IFileService fileService,
        ILogger<DeleteImageCommandHandler> logger)
    {
        _categoryRepository = categoryRepository;
        _companyRepository = companyRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteImageCommand request, CancellationToken cancellationToken)
    {
        // Оновлюємо в залежності від типу сутності
        switch (request.EntityType.ToLower())
        {
            case "category":
                await DeleteCategoryImage(request.Id, cancellationToken);
                break;
            case "company":
                await DeleteCompanyImage(request.Id, cancellationToken);
                break;
            default:
                throw new InvalidOperationException($"Невідомий тип сутності: {request.EntityType}");
        }

        // Повертаємо результат
        return true;
    }

    private async Task DeleteCategoryImage(Guid categoryId, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Категорію з ID {categoryId} не знайдено.");

        // Створюємо нову  з оновленим зображенням

        await _fileService.DeleteFileAsync(
            category.Image!.Value,
            cancellationToken);

        // Оновлюємо 
        category.UpdateImage(null);

        // Зберігаємо зміни
        await _categoryRepository.UpdateAsync(category, cancellationToken);
    }

    private async Task DeleteCompanyImage(Guid companyId, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {companyId} не знайдено.");

        // Створюємо нову  з оновленим зображенням
        await _fileService.DeleteFileAsync(
            company.Image!.Value,
            cancellationToken);

        // Оновлюємо 
        company.UpdateImage(null);

        // Зберігаємо зміни
        await _companyRepository.UpdateAsync(company, cancellationToken);
    }
}
