import{q as c}from"./index-L-hJxM_5.js";const m={PROCESSING:0,PENDING:1,SHIPPED:2,DELIVERED:3,CANCELLED:4},h={PENDING:0,COMPLETED:1,REFUNDED:2,FAILED:3},O={UAH:0,USD:1,EUR:2},$={[m.PROCESSING]:"Processing",[m.PENDING]:"Pending",[m.SHIPPED]:"Shipped",[m.DELIVERED]:"Delivered",[m.CANCELLED]:"Cancelled",Processing:"Processing",Pending:"Pending",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled"},b={[h.PENDING]:"Pending",[h.COMPLETED]:"Completed",[h.REFUNDED]:"Refunded",[h.FAILED]:"Failed",Pending:"Pending",Completed:"Completed",Refunded:"Refunded",Failed:"Failed"},F={[m.PROCESSING]:"is-info",[m.PENDING]:"is-warning",[m.SHIPPED]:"is-primary",[m.DELIVERED]:"is-success",[m.CANCELLED]:"is-danger",Processing:"is-info",Pending:"is-warning",Shipped:"is-primary",Delivered:"is-success",Cancelled:"is-danger"},H={[h.PENDING]:"is-warning",[h.COMPLETED]:"is-success",[h.REFUNDED]:"is-info",[h.FAILED]:"is-danger",Pending:"is-warning",Completed:"is-success",Refunded:"is-info",Failed:"is-danger"},P={[O.UAH]:{locale:"uk-UA",currency:"UAH"},[O.USD]:{locale:"en-US",currency:"USD"},[O.EUR]:{locale:"en-EU",currency:"EUR"},UAH:{locale:"uk-UA",currency:"UAH"},USD:{locale:"en-US",currency:"USD"},EUR:{locale:"en-EU",currency:"EUR"}},x={[h.PENDING]:"Pending",[h.COMPLETED]:"Completed",[h.REFUNDED]:"Refunded",[h.FAILED]:"Failed"},V=e=>$[e]||e||"Unknown",z=e=>b[e]||e||"Unknown",K=e=>F[e]||"is-light",J=e=>H[e]||"is-light",W=e=>P[e]||P[O.UAH],k=e=>x[e]||e,l={NETWORK:"NETWORK",VALIDATION:"VALIDATION",AUTHENTICATION:"AUTHENTICATION",AUTHORIZATION:"AUTHORIZATION",NOT_FOUND:"NOT_FOUND",SERVER:"SERVER",UNKNOWN:"UNKNOWN"},I={[l.NETWORK]:"Network error. Please check your connection and try again.",[l.VALIDATION]:"Please check your input and try again.",[l.AUTHENTICATION]:"Authentication failed. Please log in again.",[l.AUTHORIZATION]:"You do not have permission to perform this action.",[l.NOT_FOUND]:"The requested resource was not found.",[l.SERVER]:"Server error. Please try again later.",[l.UNKNOWN]:"An unexpected error occurred. Please try again."},C={ORDER_NOT_FOUND:"Order not found. It may have been deleted or you may not have access to it.",ORDER_UPDATE_FAILED:"Failed to update order. Please check your input and try again.",ORDER_STATUS_UPDATE_FAILED:"Failed to update order status. Please try again.",PAYMENT_STATUS_UPDATE_FAILED:"Failed to update payment status. Please try again.",ORDER_ITEMS_LOAD_FAILED:"Failed to load order items. Please refresh the page.",ORDER_LIST_LOAD_FAILED:"Failed to load orders list. Please refresh the page.",ORDER_DELETE_FAILED:"Failed to delete order. Please try again.",ORDER_NOTE_ADD_FAILED:"Failed to add order note. Please try again."},v=e=>{if(!e)return l.UNKNOWN;if(!e.response)return l.NETWORK;switch(e.response.status){case 400:return l.VALIDATION;case 401:return l.AUTHENTICATION;case 403:return l.AUTHORIZATION;case 404:return l.NOT_FOUND;case 422:return l.VALIDATION;case 500:case 502:case 503:case 504:return l.SERVER;default:return l.UNKNOWN}},A=(e,t=null)=>{var r,o,n,i;const s=v(e);if((o=(r=e.response)==null?void 0:r.data)!=null&&o.message)return e.response.data.message;if((i=(n=e.response)==null?void 0:n.data)!=null&&i.errors){if(Array.isArray(e.response.data.errors))return e.response.data.errors.join(", ");if(typeof e.response.data.errors=="object")return Object.values(e.response.data.errors).flat().join(", ")}return t&&C[t]?C[t]:I[s]||I[l.UNKNOWN]},S=(e,t="Unknown",s={})=>{const r=v(e);console.group(`🚨 ${r} Error in ${t}`),console.error("Error message:",e.message),console.error("Error type:",r),e.response&&(console.error("Response status:",e.response.status),console.error("Response statusText:",e.response.statusText),console.error("Response data:",e.response.data),console.error("Response headers:",e.response.headers)),e.request&&console.error("Request:",e.request),e.config&&console.error("Request config:",{url:e.config.url,method:e.config.method,data:e.config.data,params:e.config.params}),Object.keys(s).length>0&&console.error("Additional data:",s),console.error("Stack trace:",e.stack),console.groupEnd()};class q{constructor(){this.events={},this.eventHistory=[],this.maxHistorySize=100,this.debugMode=!1}on(t,s,r={}){this.events[t]||(this.events[t]=[]);const o={callback:s,id:this._generateId(),once:r.once||!1,priority:r.priority||0,component:r.component||"unknown"};return this.events[t].push(o),this.events[t].sort((n,i)=>i.priority-n.priority),this.debugMode&&console.log(`📡 EventBus: Subscribed to '${t}' (component: ${o.component}, id: ${o.id})`),()=>{this.events[t]=this.events[t].filter(n=>n.id!==o.id),this.debugMode&&console.log(`📡 EventBus: Unsubscribed from '${t}' (id: ${o.id})`)}}emit(t,s={}){const r={event:t,data:s,timestamp:Date.now(),id:this._generateId()};if(this._addToHistory(r),this.debugMode&&console.log(`📡 EventBus: Emitting '${t}'`,r),this.events[t]){const o=[];this.events[t].forEach(n=>{try{n.callback(r),n.once&&o.push(n.id)}catch(i){console.error(`📡 EventBus: Error in listener for '${t}':`,i)}}),o.length>0&&(this.events[t]=this.events[t].filter(n=>!o.includes(n.id)))}return r.id}off(t){this.debugMode&&this.events[t]&&console.log(`📡 EventBus: Removing all listeners for '${t}' (${this.events[t].length} listeners)`),delete this.events[t]}getHistory(t=null){return t?this.eventHistory.filter(s=>s.event===t):[...this.eventHistory]}clearHistory(){this.eventHistory=[],this.debugMode&&console.log("📡 EventBus: Event history cleared")}getListenersCount(t=null){var s;return t?((s=this.events[t])==null?void 0:s.length)||0:Object.values(this.events).reduce((r,o)=>r+o.length,0)}getDebugInfo(){return{events:Object.keys(this.events),totalListeners:this.getListenersCount(),historySize:this.eventHistory.length,eventDetails:Object.entries(this.events).map(([t,s])=>({event:t,listenerCount:s.length,listeners:s.map(r=>({id:r.id,component:r.component,priority:r.priority,once:r.once}))}))}}_generateId(){return Math.random().toString(36).substr(2,9)}_addToHistory(t){this.eventHistory.push(t),this.eventHistory.length>this.maxHistorySize&&(this.eventHistory=this.eventHistory.slice(-this.maxHistorySize))}}const f=new q,E={ORDER_UPDATED:"order:updated",ORDER_STATUS_CHANGED:"order:status-changed",ORDER_PAYMENT_STATUS_CHANGED:"order:payment-status-changed",ORDER_DELETED:"order:deleted",ORDER_CREATED:"order:created",ORDER_ITEM_ADDED:"order:item-added",ORDER_ITEM_UPDATED:"order:item-updated",ORDER_ITEM_REMOVED:"order:item-removed",ORDER_NOTE_ADDED:"order:note-added",ORDER_REFUNDED:"order:refunded",ORDER_CANCELLED:"order:cancelled",PRODUCT_UPDATED:"product:updated",PRODUCT_CREATED:"product:created",PRODUCT_DELETED:"product:deleted",PRODUCT_STOCK_CHANGED:"product:stock-changed",CATEGORY_UPDATED:"category:updated",CATEGORY_CREATED:"category:created",CATEGORY_DELETED:"category:deleted",COMPANY_UPDATED:"company:updated",COMPANY_CREATED:"company:created",COMPANY_DELETED:"company:deleted",COMPANY_STATUS_CHANGED:"company:status-changed",USER_UPDATED:"user:updated",USER_CREATED:"user:created",USER_DELETED:"user:deleted",USER_ROLE_CHANGED:"user:role-changed",CACHE_INVALIDATED:"system:cache-invalidated",DATA_REFRESH_NEEDED:"system:data-refresh-needed",ERROR_OCCURRED:"system:error-occurred",NOTIFICATION_SHOWN:"system:notification-shown"},R=(e,t,s={})=>({type:e,orderId:t,timestamp:Date.now(),...s}),T={markOrdersForRefresh(){localStorage.setItem("orders_need_refresh","true"),localStorage.setItem("orders_refresh_timestamp",Date.now().toString())},shouldRefreshOrders(){return localStorage.getItem("orders_need_refresh")==="true"},clearOrdersRefreshFlag(){localStorage.removeItem("orders_need_refresh"),localStorage.removeItem("orders_refresh_timestamp")},getRefreshTimestamp(){const e=localStorage.getItem("orders_refresh_timestamp");return e?parseInt(e):null}},N=e=>{if(!e)return"Processing";switch(e.toLowerCase()){case"processing":return"Processing";case"pending":return"Pending";case"shipped":return"Shipped";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return"Processing"}},_=e=>{if(!e)return"Pending";switch(e.toLowerCase()){case"pending":return"Pending";case"completed":return"Completed";case"refunded":return"Refunded";case"failed":return"Failed";default:return"Pending"}},B={maxRetries:3,baseDelay:1e3,maxDelay:1e4,retryableStatuses:[408,429,500,502,503,504]},U=async(e,t="API call",s=B)=>{var o,n,i;let r;for(let a=0;a<=s.maxRetries;a++)try{const d=await e();return a>0&&console.log(`✅ ${t} succeeded on attempt ${a+1}`),d}catch(d){r=d;const u=s.retryableStatuses.includes((o=d.response)==null?void 0:o.status)||d.code==="NETWORK_ERROR"||((n=d.message)==null?void 0:n.includes("timeout"))||((i=d.message)==null?void 0:i.includes("Network Error"));if(a===s.maxRetries||!u){console.error(`❌ ${t} failed after ${a+1} attempts:`,d);break}const D=Math.min(s.baseDelay*Math.pow(2,a),s.maxDelay);console.warn(`⚠️ ${t} failed on attempt ${a+1}, retrying in ${D}ms...`,d.message),await new Promise(g=>setTimeout(g,D))}throw r};class G{constructor(){this.cache=new Map,this.version=1,this.CACHE_DURATION=3e4,this.MAX_CACHE_SIZE=100}generateKey(t){return`orders_v${this.version}_${JSON.stringify(t)}`}get(t){const s=this.generateKey(t),r=this.cache.get(s);return r&&Date.now()-r.timestamp<this.CACHE_DURATION?(console.log("📦 Cache hit for:",s),r.data):(r&&(console.log("⏰ Cache expired for:",s),this.cache.delete(s)),null)}set(t,s){const r=this.generateKey(t);if(this.cache.size>=this.MAX_CACHE_SIZE){const o=this.cache.keys().next().value;this.cache.delete(o),console.log("🧹 Removed oldest cache entry:",o)}this.cache.set(r,{data:s,timestamp:Date.now()}),console.log("💾 Cached data for:",r)}invalidate(){this.version++,this.cache.clear(),console.log("🔄 Cache invalidated, new version:",this.version)}clear(){this.cache.clear(),console.log("🧹 Cache cleared")}invalidatePattern(t){const s=[];for(const r of this.cache.keys())r.includes(t)&&s.push(r);s.forEach(r=>{this.cache.delete(r),console.log("🗑️ Invalidated cache entry:",r)})}getStats(){return{size:this.cache.size,version:this.version,maxSize:this.MAX_CACHE_SIZE,duration:this.CACHE_DURATION}}}const p=new G;class Y{constructor(){this.logs=[],this.maxLogs=1e3,this.sessionId=Math.random().toString(36).substr(2,9),this.startTime=Date.now()}log(t,s,r={},o=null){const n={id:Math.random().toString(36).substr(2,9),timestamp:Date.now(),sessionId:this.sessionId,level:t,operation:s,data:JSON.parse(JSON.stringify(r)),error:o?{message:o.message,stack:o.stack,name:o.name}:null,userAgent:navigator.userAgent,url:window.location.href};this.logs.push(n),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs));const i=`[Orders ${t.toUpperCase()}]`,a=`${s}${r.orderId?` (Order: ${r.orderId})`:""}`;switch(t){case"error":console.error(i,a,r,o);break;case"warn":console.warn(i,a,r);break;case"info":console.info(i,a,r);break;case"debug":console.log(i,a,r);break}return n}error(t,s,r){return this.log("error",t,s,r)}warn(t,s){return this.log("warn",t,s)}info(t,s){return this.log("info",t,s)}debug(t,s){return this.log("debug",t,s)}getLogs(t={}){let s=[...this.logs];return t.level&&(s=s.filter(r=>r.level===t.level)),t.operation&&(s=s.filter(r=>r.operation.toLowerCase().includes(t.operation.toLowerCase()))),t.orderId&&(s=s.filter(r=>r.data.orderId===t.orderId)),t.since&&(s=s.filter(r=>r.timestamp>=t.since)),s.sort((r,o)=>o.timestamp-r.timestamp)}exportLogs(){const t={sessionId:this.sessionId,startTime:this.startTime,exportTime:Date.now(),userAgent:navigator.userAgent,url:window.location.href,cacheStats:p.getStats(),logs:this.logs},s=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),r=URL.createObjectURL(s),o=document.createElement("a");o.href=r,o.download=`orders-logs-${this.sessionId}-${Date.now()}.json`,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(r),this.info("exportLogs",{exportedCount:this.logs.length})}clear(){const t=this.logs.length;this.logs=[],this.info("clearLogs",{clearedCount:t})}getDiagnostics(){const t=Date.now(),s=t-this.startTime,r=this.logs.reduce((n,i)=>(n[i.level]=(n[i.level]||0)+1,n),{}),o=this.getLogs({level:"error",since:t-3e5});return{sessionId:this.sessionId,sessionDuration:s,totalLogs:this.logs.length,logsByLevel:r,recentErrors:o.length,cacheStats:p.getStats(),lastActivity:this.logs.length>0?this.logs[this.logs.length-1].timestamp:this.startTime}}}const y=new Y,w={forceRefresh(){p.invalidate(),y.info("forceRefresh",{cacheStats:p.getStats()})},getCacheStats(){return p.getStats()},getLogs(e){return y.getLogs(e)},exportLogs(){return y.exportLogs()},getDiagnostics(){return y.getDiagnostics()},clearLogs(){return y.clear()},async getAll(e={}){return w.getOrders(e)},async getAllAdmin(e={}){return w.getOrders(e)},async getOrders(e={}){var s;const t=Date.now();y.info("getOrders:start",{params:e});try{console.log("Requesting orders with params:",e);const r=e.status||e.paymentStatus||e.search||e.filter;if(!r){const a=p.get(e);if(a)return a}const o={};if(e.page&&(o.page=e.page),e.pageSize&&(o.pageSize=e.pageSize),e.orderBy&&(o.orderBy=e.orderBy),e.sortBy&&(o.orderBy=e.sortBy),e.descending!==void 0&&(o.descending=e.descending),e.sortOrder&&(o.descending=e.sortOrder==="desc",console.log("🔄 Setting sort order:",e.sortOrder,"-> descending:",o.descending)),e.search&&e.search.trim()!==""?o.filter=e.search.trim():e.filter&&e.filter.trim()!==""&&(o.filter=e.filter.trim()),e.status!==void 0&&e.status!==null&&e.status!==""){const a=typeof e.status=="string"?parseInt(e.status,10):e.status;isNaN(a)||(o.status=a,console.log("🔄 Setting order status filter:",a))}if(e.paymentStatus!==void 0&&e.paymentStatus!==null&&e.paymentStatus!==""){const a=typeof e.paymentStatus=="string"?parseInt(e.paymentStatus,10):e.paymentStatus;isNaN(a)||(o.paymentStatus=a,console.log("🔄 Setting payment status filter:",a))}e.dateFrom&&(o.dateFrom=e.dateFrom),e.dateTo&&(o.dateTo=e.dateTo),console.log("📊 Final API params being sent:",o),console.log("📋 Original params received:",e),r&&(console.log("🧹 Clearing cache due to filters"),p.clear());const n=await c.get("/api/admin/orders",{params:o});if(console.log("Orders API response:",n),n.data&&n.data.success&&n.data.data){const a=n.data.data;console.log("Processing ApiResponse format:",a);const d={success:!0,data:a.data||a.items||[],totalItems:a.total||a.totalItems||0,currentPage:a.currentPage||a.page||e.page||1,pageSize:a.perPage||a.pageSize||e.pageSize||10,totalPages:a.lastPage||a.totalPages||1};return console.log("Processed result:",d),r||p.set(e,d),d}if(n.data&&n.data.items){const a={success:!0,data:n.data.items||[],totalItems:n.data.totalItems||n.data.total||0,currentPage:n.data.currentPage||n.data.page||e.page||1,pageSize:n.data.pageSize||n.data.perPage||e.pageSize||10,totalPages:n.data.totalPages||n.data.lastPage||1};return r||p.set(e,a),a}const i={success:!0,data:n.data||[],totalItems:0,currentPage:1,pageSize:10,totalPages:1};return r||p.set(e,i),i}catch(r){const o=Date.now()-t;y.error("getOrders:error",{params:e,duration:o,errorType:r.name,statusCode:(s=r.response)==null?void 0:s.status},r),S(r,"getOrders",{params:e});const n=A(r,"ORDER_LIST_LOAD_FAILED"),i=new Error(n);throw i.originalError=r,i.context="getOrders",i}},async getById(e){return this.getOrderById(e)},async update(e,t){var s,r;try{console.log("=== ORDER UPDATE DEBUG ==="),console.log("Order ID:",e),console.log("Original frontend data:",JSON.stringify(t,null,2)),console.log("Data analysis:"),console.log("- status type:",typeof t.status,"value:",t.status),console.log("- paymentStatus type:",typeof t.paymentStatus,"value:",t.paymentStatus),console.log("- shippingAddress type:",typeof t.shippingAddress,"value:",t.shippingAddress),console.log("- items type:",typeof t.items,"length:",(s=t.items)==null?void 0:s.length),console.log("- trackingNumber type:",typeof t.trackingNumber,"value:",t.trackingNumber),console.log("- shippingMethod type:",typeof t.shippingMethod,"value:",t.shippingMethod),console.log("- notes type:",typeof t.notes,"value:",t.notes);const o=N(t.status),n=_(t.paymentStatus);console.log("Status transformation:",t.status,"->",o),console.log("Payment status transformation:",t.paymentStatus,"->",n);let i=[];t.items&&Array.isArray(t.items)&&(i=t.items.filter(g=>g&&g.id).map(g=>{console.log("Transforming item:",g);const L=Math.max(1,parseInt(g.quantity)||1),M=parseFloat(g.price||g.priceAmount||0);return{id:g.id,quantity:L,priceAmount:M,priceCurrency:g.priceCurrency||"UAH"}}));let a=t.notes||t.comment||"";Array.isArray(a)&&(a=a.join("; ")),typeof a!="string"&&(a=String(a||""));const d={status:o,paymentStatus:n,shippingAddress:((r=t.shippingAddress)==null?void 0:r.address1)||t.shippingAddress,trackingNumber:t.trackingNumber||null,shippingMethod:t.shippingMethod||t.shippingMethodName||null,notes:a,items:i},u={};Object.keys(d).forEach(g=>{d[g]!==null&&d[g]!==void 0&&(u[g]=d[g])}),console.log("Final request data (before cleaning):",JSON.stringify(d,null,2)),console.log("Final request data (after cleaning):",JSON.stringify(u,null,2)),console.log("Request data validation:"),console.log("- All required fields present:",{status:!!u.status,paymentStatus:!!u.paymentStatus,items:Array.isArray(u.items)}),console.log("- Data types check:",{status:typeof u.status,paymentStatus:typeof u.paymentStatus,shippingAddress:typeof u.shippingAddress,trackingNumber:typeof u.trackingNumber,shippingMethod:typeof u.shippingMethod,notes:typeof u.notes,items:Array.isArray(u.items)?"array":typeof u.items});const D=await c.put(`/api/admin/orders/${e}`,u);return console.log("✅ Update order response:",D),console.log("=== ORDER UPDATE SUCCESS ==="),p.invalidate(),T.markOrdersForRefresh(),f.emit(E.ORDER_UPDATED,{orderId:e,data:D.data}),D.data}catch(o){S(o,"updateOrder",{orderId:e,requestData:t});const n=A(o,"ORDER_UPDATE_FAILED"),i=new Error(n);throw i.originalError=o,i.context="updateOrder",i.orderId=e,i}},async getOrderById(e){try{console.log(`Fetching order ${e} from API...`);const t=await c.get(`/api/admin/orders/${e}`);if(console.log("Order API response:",t),t.data&&t.data.success&&t.data.data){const s=t.data.data;console.log("Order data extracted:",s);const r={id:s.id,customerId:s.customerId,customerName:s.customerName,customerEmail:s.customerEmail,total:s.totalPriceAmount,totalPriceAmount:s.totalPriceAmount,subtotal:s.totalPriceAmount,tax:0,shipping:0,discount:0,status:s.status!==void 0?s.status:0,paymentStatus:s.paymentStatus!==void 0?s.paymentStatus:0,paymentStatusText:s.paymentStatusText||"Pending",paymentMethod:s.paymentMethodText||"Card",paymentMethodText:s.paymentMethodText||"Card",shippingMethod:s.shippingMethodName||"Standard",shippingMethodName:s.shippingMethodName||"Standard",shippingAddress:{address1:s.shippingAddressLine||"N/A",city:s.shippingCity||"N/A",country:s.shippingCountry||"N/A"},items:[],notes:"",createdAt:s.createdAt,updatedAt:s.createdAt};try{const o=await this.getOrderItems(s.id);o&&o.success&&o.data&&Array.isArray(o.data)?r.items=o.data:r.items=[]}catch(o){console.warn("Could not fetch order items:",o),r.items=[]}return r}if(t.data)return t.data;throw new Error("Invalid response format")}catch(t){return console.error(`Error fetching order ${e}:`,t),t.response&&t.response.status===404?null:{id:e,customerId:"1",customerName:"John Doe",customerEmail:"<EMAIL>",total:156.78,totalPriceAmount:156.78,subtotal:149.99,tax:0,shipping:0,discount:0,status:0,paymentStatus:1,paymentStatusText:"Paid",paymentMethod:"Credit Card",paymentMethodText:"Credit Card",shippingMethod:"Standard",shippingMethodName:"Standard",shippingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},billingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},items:[{id:"1",productId:"1",productName:"Smartphone X",quantity:1,price:99.99,total:99.99},{id:"2",productId:"2",productName:"Wireless Headphones",quantity:1,price:49.99,total:49.99}],notes:"",createdAt:new Date(Date.now()-1e3*60*60*2),updatedAt:new Date(Date.now()-1e3*60*30)}}},async updateOrderStatus(e,t){try{console.log("=== ORDER STATUS UPDATE DEBUG ==="),console.log("Order ID:",e),console.log("Input status:",t,"type:",typeof t);let s,r;typeof t=="object"&&t!==null?(s=N(t.status),r=_(t.paymentStatus)):s=N(t),console.log("Converted status string:",s),console.log("Converted payment status string:",r);const o={status:s,paymentStatus:r,timestamp:Date.now(),requestId:Math.random().toString(36).substr(2,9)};console.log("Request body:",JSON.stringify(o,null,2));const n=await U(()=>c.patch(`/api/admin/orders/${e}/status`,o,{headers:{"X-Request-ID":o.requestId,"X-Idempotency-Key":`order-status-${e}-${s}-${o.timestamp}`}}),`Order ${e} status update to ${s}`);return console.log("✅ Order status update successful:",n),console.log("=== ORDER STATUS UPDATE SUCCESS ==="),p.invalidate(),T.markOrdersForRefresh(),f.emit(E.ORDER_STATUS_CHANGED,R(E.ORDER_STATUS_CHANGED,e,{oldStatus:t,newStatus:s,data:n.data,requestId:o.requestId})),n.data}catch(s){S(s,"updateOrderStatus",{orderId:e,status:t}),f.emit(E.ERROR_OCCURRED,R(E.ERROR_OCCURRED,e,{operation:"updateOrderStatus",error:s.message,status:t}));const r=A(s,"ORDER_STATUS_UPDATE_FAILED"),o=new Error(r);throw o.originalError=s,o.context="updateOrderStatus",o.orderId=e,o}},async updatePaymentStatus(e,t){try{console.log("=== PAYMENT STATUS UPDATE DEBUG ==="),console.log("Order ID:",e),console.log("Input payment status:",t,"type:",typeof t);const s=k(t)||t;console.log("Converted payment status:",s);const r=["Pending","Completed","Refunded","Failed"];if(!r.includes(s))throw new Error(`Invalid payment status: ${s}. Valid statuses: ${r.join(", ")}`);const o={paymentStatus:s,timestamp:Date.now(),requestId:Math.random().toString(36).substr(2,9)};console.log("Request body:",JSON.stringify(o,null,2));const n=await U(()=>c.patch(`/api/admin/orders/${e}/payment-status`,o,{headers:{"X-Request-ID":o.requestId,"X-Idempotency-Key":`payment-status-${e}-${s}-${o.timestamp}`}}),`Order ${e} payment status update to ${s}`);return console.log("✅ Payment status update successful:",n),console.log("=== PAYMENT STATUS UPDATE SUCCESS ==="),p.invalidate(),T.markOrdersForRefresh(),f.emit(E.ORDER_PAYMENT_STATUS_CHANGED,R(E.ORDER_PAYMENT_STATUS_CHANGED,e,{oldPaymentStatus:t,newPaymentStatus:s,data:n.data,requestId:o.requestId})),n.data}catch(s){S(s,"updatePaymentStatus",{orderId:e,paymentStatus:t}),f.emit(E.ERROR_OCCURRED,R(E.ERROR_OCCURRED,e,{operation:"updatePaymentStatus",error:s.message,paymentStatus:t}));const r=A(s,"PAYMENT_STATUS_UPDATE_FAILED"),o=new Error(r);throw o.originalError=s,o.context="updatePaymentStatus",o.orderId=e,o}},async addOrderNote(e,t){try{return(await c.post(`/api/admin/orders/${e}/notes`,{note:t})).data}catch(s){return console.error(`Error adding note to order ${e}:`,s),{success:!0,note:{id:Math.floor(Math.random()*1e3).toString(),content:t,createdAt:new Date,createdBy:"Admin"}}}},async getOrderNotes(e){try{return(await c.get(`/api/admin/orders/${e}/notes`)).data}catch(t){return console.error(`Error fetching notes for order ${e}:`,t),[{id:"1",content:"Order received and processing",createdAt:new Date(Date.now()-1e3*60*60*2),createdBy:"System"},{id:"2",content:"Payment confirmed",createdAt:new Date(Date.now()-1e3*60*60),createdBy:"System"},{id:"3",content:"Customer requested expedited shipping",createdAt:new Date(Date.now()-1e3*60*30),createdBy:"Admin"}]}},async refundOrder(e,t,s){try{return(await c.post(`/api/admin/orders/${e}/refund`,{amount:t,reason:s})).data}catch(r){return console.error(`Error processing refund for order ${e}:`,r),{success:!0,refund:{id:Math.floor(Math.random()*1e3).toString(),amount:t,reason:s,createdAt:new Date}}}},async exportOrders(e={}){try{return(await c.get("/api/admin/orders/export",{params:e,responseType:"blob"})).data}catch(t){console.error("Error exporting orders:",t);const s=`Order ID,Customer,Email,Date,Total,Status,Payment Status
`,r=["ORD-1001,John Doe,<EMAIL>,2023-01-01,156.78,Processing,Paid","ORD-1002,Jane Smith,<EMAIL>,2023-01-02,89.99,Pending,Pending","ORD-1003,Robert Johnson,<EMAIL>,2023-01-03,245.50,Shipped,Paid","ORD-1004,Emily Davis,<EMAIL>,2023-01-04,78.25,Delivered,Paid","ORD-1005,Michael Wilson,<EMAIL>,2023-01-05,189.99,Cancelled,Refunded"].join(`
`),o=s+r;return new Blob([o],{type:"text/csv"})}},async getOrderStats(e="month"){try{return(await c.get(`/api/admin/orders/stats?period=${e}`)).data}catch(t){return console.error("Error fetching order stats:",t),{total:356,totalRevenue:28456.78,averageOrderValue:79.93,byStatus:{Pending:45,Processing:32,Shipped:18,Delivered:256,Cancelled:5},byPeriod:[{date:"2023-01-01",count:12,revenue:956.78},{date:"2023-01-02",count:15,revenue:1245.5},{date:"2023-01-03",count:8,revenue:678.25},{date:"2023-01-04",count:20,revenue:1789.99},{date:"2023-01-05",count:18,revenue:1456.78}]}}},async getOrdersByCustomer(e,t={}){try{const s=await c.get(`/api/admin/users/${e}/orders`,{params:t});return s.data&&s.data.data?{orders:s.data.data.items||[],pagination:{total:s.data.data.totalItems||0,page:s.data.data.currentPage||1,limit:s.data.data.pageSize||10,totalPages:s.data.data.totalPages||1}}:s.data}catch(s){return console.error(`Error fetching orders for user ${e}:`,s),{orders:[{id:"ORD-1001",total:156.78,status:"Processing",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"ORD-1002",total:89.99,status:"Delivered",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*24*7)}],pagination:{total:2,page:1,limit:10,totalPages:1}}}},async cancelOrder(e,t){try{return(await c.post(`/api/admin/orders/${e}/cancel`,{reason:t})).data}catch(s){return console.error(`Error cancelling order ${e}:`,s),{success:!0,order:{id:e,status:"Cancelled",updatedAt:new Date}}}},async resendOrderConfirmation(e){try{return(await c.post(`/api/admin/orders/${e}/resend-confirmation`)).data}catch(t){return console.error(`Error resending confirmation for order ${e}:`,t),{success:!0,message:"Order confirmation email has been resent."}}},async getOrderItems(e,t={}){try{console.log(`Fetching items for order ${e}...`);const s=await c.get(`/api/admin/orders/${e}/items`,{params:t});if(console.log("Order items API response:",s),s.data&&s.data.success&&s.data.data){const r=s.data.data;return console.log("Order items data extracted:",r),{success:!0,data:(r.data||r.items||[]).map(i=>({id:i.id,productId:i.productId,productName:i.productName||"Unknown Product",productImage:i.productImage||null,quantity:i.quantity,price:parseFloat(i.priceAmount||0),priceAmount:parseFloat(i.priceAmount||0),total:parseFloat(i.priceAmount||0)*(i.quantity||0)}))}}return s.data&&Array.isArray(s.data)?{success:!0,data:s.data}:{success:!0,data:[]}}catch(s){return console.error(`Error fetching items for order ${e}:`,s),{success:!0,data:[]}}},async updateOrderItem(e,t,s){try{return(await c.put(`/api/admin/orders/${e}/items/${t}`,s)).data}catch(r){return console.error(`Error updating item ${t} in order ${e}:`,r),{success:!0,item:{id:t,...s,updatedAt:new Date}}}},async addOrderItem(e,t){try{return(await c.post(`/api/admin/orders/${e}/items`,t)).data}catch(s){return console.error(`Error adding item to order ${e}:`,s),{success:!0,item:{id:Math.floor(Math.random()*1e3).toString(),...t,createdAt:new Date}}}},async removeOrderItem(e,t){try{return(await c.delete(`/api/admin/orders/${e}/items/${t}`)).data}catch(s){return console.error(`Error removing item ${t} from order ${e}:`,s),{success:!0,message:"Item removed successfully"}}}};export{E,V as a,J as b,z as c,W as d,f as e,K as g,w as o,T as u};
