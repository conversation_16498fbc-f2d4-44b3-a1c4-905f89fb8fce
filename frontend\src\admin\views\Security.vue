<template>
  <div class="admin-security">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Security & Logs</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="refreshData" :class="{ 'is-loading': loading }">
            <span class="icon"><i class="fas fa-sync-alt"></i></span>
            <span>Refresh</span>
          </button>
        </div>
      </div>
    </div>

    <div class="tabs is-boxed">
      <ul>
        <li :class="{ 'is-active': activeTab === 'logs' }">
          <a @click="activeTab = 'logs'">
            <span class="icon is-small"><i class="fas fa-list"></i></span>
            <span>System Logs</span>
          </a>
        </li>
        <li :class="{ 'is-active': activeTab === 'stats' }">
          <a @click="activeTab = 'stats'">
            <span class="icon is-small"><i class="fas fa-chart-bar"></i></span>
            <span>Statistics</span>
          </a>
        </li>
        <li :class="{ 'is-active': activeTab === 'settings' }">
          <a @click="activeTab = 'settings'">
            <span class="icon is-small"><i class="fas fa-shield-alt"></i></span>
            <span>Security Settings</span>
          </a>
        </li>
      </ul>
    </div>

    <!-- System Logs -->
    <div v-if="activeTab === 'logs'" class="security-content">
      <div class="card">
        <div class="card-header">
          <p class="card-header-title">System Logs</p>
        </div>
        <div class="card-content">
          <!-- Filters -->
          <div class="columns is-multiline">
            <div class="column is-2">
              <div class="field">
                <label class="label">Level</label>
                <div class="select is-fullwidth">
                  <select v-model="logFilters.level" @change="loadLogs">
                    <option value="">All Levels</option>
                    <option v-for="level in logLevels" :key="level" :value="level">{{ level }}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">Category</label>
                <div class="select is-fullwidth">
                  <select v-model="logFilters.category" @change="loadLogs">
                    <option value="">All Categories</option>
                    <option v-for="category in logCategories" :key="category" :value="category">{{ category }}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">User Email</label>
                <input
                  class="input"
                  type="text"
                  v-model="logFilters.userEmail"
                  @input="debouncedLoadLogs"
                  placeholder="Filter by user email...">
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">IP Address</label>
                <input
                  class="input"
                  type="text"
                  v-model="logFilters.ipAddress"
                  @input="debouncedLoadLogs"
                  placeholder="Filter by IP...">
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">From Date</label>
                <input
                  class="input"
                  type="date"
                  v-model="logFilters.fromDate"
                  @change="loadLogs">
              </div>
            </div>
            <div class="column is-2">
              <div class="field">
                <label class="label">To Date</label>
                <input
                  class="input"
                  type="date"
                  v-model="logFilters.toDate"
                  @change="loadLogs">
              </div>
            </div>
          </div>

          <div class="field is-grouped">
            <div class="control">
              <input
                class="input"
                type="text"
                v-model="logFilters.search"
                @input="debouncedLoadLogs"
                placeholder="Search in messages...">
            </div>
            <div class="control">
              <button class="button is-info" @click="clearFilters">
                <span class="icon"><i class="fas fa-times"></i></span>
                <span>Clear Filters</span>
              </button>
            </div>
            <div class="control">
              <button class="button is-success" @click="exportLogs">
                <span class="icon"><i class="fas fa-download"></i></span>
                <span>Export CSV</span>
              </button>
            </div>
          </div>

          <!-- Loading -->
          <div class="has-text-centered" v-if="loading">
            <span class="icon is-large">
              <i class="fas fa-spinner fa-pulse fa-3x"></i>
            </span>
            <p class="mt-3">Loading logs...</p>
          </div>

          <!-- Logs Table -->
          <div v-else class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>Level</th>
                  <th>Category</th>
                  <th>Message</th>
                  <th>User</th>
                  <th>IP Address</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="log in logs.data" :key="log.id">
                  <td>{{ formatDateTime(log.timestamp) }}</td>
                  <td>
                    <span class="tag" :class="getLogLevelClass(log.level)">
                      {{ log.level }}
                    </span>
                  </td>
                  <td>{{ log.category }}</td>
                  <td class="log-message">{{ log.message }}</td>
                  <td>{{ log.userName || 'System' }}</td>
                  <td>{{ log.ipAddress || 'N/A' }}</td>
                  <td>
                    <button class="button is-small is-info" @click="viewLogDetails(log)">
                      <span class="icon"><i class="fas fa-eye"></i></span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Pagination -->
            <nav class="pagination is-centered" v-if="logs.totalPages > 1">
              <button
                class="pagination-previous"
                @click="changePage(logs.page - 1)"
                :disabled="logs.page <= 1">
                Previous
              </button>
              <button
                class="pagination-next"
                @click="changePage(logs.page + 1)"
                :disabled="logs.page >= logs.totalPages">
                Next
              </button>
              <ul class="pagination-list">
                <li v-for="page in visiblePages" :key="page">
                  <button
                    class="pagination-link"
                    :class="{ 'is-current': page === logs.page }"
                    @click="changePage(page)">
                    {{ page }}
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Statistics -->
    <div v-if="activeTab === 'stats'" class="security-content">
      <div class="columns">
        <div class="column is-6">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Log Statistics</p>
            </div>
            <div class="card-content">
              <div v-if="statsLoading" class="has-text-centered">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
                <p>Loading statistics...</p>
              </div>
              <div v-else>
                <div class="field">
                  <label class="label">Total Logs</label>
                  <p class="title is-4">{{ stats.totalLogs || 0 }}</p>
                </div>
                <div class="field">
                  <label class="label">Date Range</label>
                  <p class="subtitle is-6">{{ formatDateRange() }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="column is-6">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Logs by Level</p>
            </div>
            <div class="card-content">
              <div v-if="statsLoading" class="has-text-centered">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
              </div>
              <div v-else>
                <div v-for="(count, level) in stats.logsByLevel" :key="level" class="field">
                  <div class="level">
                    <div class="level-left">
                      <div class="level-item">
                        <span class="tag" :class="getLogLevelClass(level)">{{ level }}</span>
                      </div>
                    </div>
                    <div class="level-right">
                      <div class="level-item">
                        <span class="title is-5">{{ count }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="columns">
        <div class="column is-12">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Logs by Category</p>
            </div>
            <div class="card-content">
              <div v-if="statsLoading" class="has-text-centered">
                <span class="icon is-large">
                  <i class="fas fa-spinner fa-pulse fa-2x"></i>
                </span>
              </div>
              <div v-else class="columns is-multiline">
                <div v-for="(count, category) in stats.logsByCategory" :key="category" class="column is-3">
                  <div class="box has-text-centered">
                    <p class="title is-4">{{ count }}</p>
                    <p class="subtitle is-6">{{ category }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    
    <!-- Security Settings -->
    <div v-if="activeTab === 'settings'" class="security-content">
      <div class="card">
        <div class="card-header">
          <p class="card-header-title">Security Settings</p>
        </div>
        <div class="card-content">
          <div class="field">
            <label class="label">Log Retention Period (days)</label>
            <div class="control">
              <input class="input" type="number" v-model="securitySettings.logRetentionDays" min="1" max="365">
            </div>
            <p class="help">Logs older than this will be automatically deleted</p>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input type="checkbox" v-model="securitySettings.enableAuditLogging">
                Enable detailed audit logging
              </label>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <label class="checkbox">
                <input type="checkbox" v-model="securitySettings.enableSecurityAlerts">
                Enable security alerts
              </label>
            </div>
          </div>

          <div class="field">
            <div class="control">
              <button class="button is-primary" @click="saveSecuritySettings" :class="{ 'is-loading': savingSettings }">
                <span class="icon"><i class="fas fa-save"></i></span>
                <span>Save Settings</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Log Details Modal -->
    <div class="modal" :class="{ 'is-active': showLogModal }">
      <div class="modal-background" @click="showLogModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Log Details</p>
          <button class="delete" @click="showLogModal = false"></button>
        </header>
        <section class="modal-card-body" v-if="selectedLog">
          <div class="field">
            <label class="label">Timestamp</label>
            <p>{{ formatDateTime(selectedLog.timestamp) }}</p>
          </div>
          <div class="field">
            <label class="label">Level</label>
            <span class="tag" :class="getLogLevelClass(selectedLog.level)">{{ selectedLog.level }}</span>
          </div>
          <div class="field">
            <label class="label">Category</label>
            <p>{{ selectedLog.category }}</p>
          </div>
          <div class="field">
            <label class="label">Message</label>
            <p>{{ selectedLog.message }}</p>
          </div>
          <div class="field" v-if="selectedLog.userName">
            <label class="label">User</label>
            <p>{{ selectedLog.userName }} ({{ selectedLog.userEmail }})</p>
          </div>
          <div class="field" v-if="selectedLog.entityId">
            <label class="label">Entity</label>
            <p>{{ selectedLog.entityType }}: {{ selectedLog.entityId }}</p>
          </div>
          <div class="field" v-if="selectedLog.ipAddress">
            <label class="label">IP Address</label>
            <p>{{ selectedLog.ipAddress }}</p>
          </div>
          <div class="field" v-if="selectedLog.userAgent">
            <label class="label">User Agent</label>
            <p class="is-size-7">{{ selectedLog.userAgent }}</p>
          </div>
          <div class="field" v-if="selectedLog.additionalData">
            <label class="label">Additional Data</label>
            <pre class="has-background-light p-3">{{ formatAdditionalData(selectedLog.additionalData) }}</pre>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button class="button" @click="showLogModal = false">Close</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { securityService } from '@/admin/services/security';
import { debounce } from 'lodash';

// Active tab
const activeTab = ref('logs');

// Loading states
const loading = ref(false);
const statsLoading = ref(false);
const savingSettings = ref(false);

// Data
const logs = ref({
  data: [],
  totalCount: 0,
  page: 1,
  pageSize: 20,
  totalPages: 1
});
const stats = ref({});
const logLevels = ref([]);
const logCategories = ref([]);
const selectedLog = ref(null);
const showLogModal = ref(false);

const securitySettings = ref({
  logRetentionDays: 30,
  enableAuditLogging: true,
  enableSecurityAlerts: true
});

// Filters
const logFilters = ref({
  level: '',
  category: '',
  search: '',
  userEmail: '',
  ipAddress: '',
  fromDate: '',
  toDate: ''
});

// Computed
const visiblePages = computed(() => {
  const pages = [];
  const start = Math.max(1, logs.value.page - 2);
  const end = Math.min(logs.value.totalPages, logs.value.page + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
});

// Methods
const loadLogs = async () => {
  loading.value = true;
  try {
    const filterParams = {
      page: logs.value.page,
      pageSize: logs.value.pageSize,
      sortBy: 'Timestamp',
      sortOrder: 'desc',
      ...logFilters.value
    };

    const response = await securityService.getLogsWithFilters(filterParams);
    logs.value.data = response.items || response.data || [];
    logs.value.totalCount = response.totalCount || response.totalItems || 0;
    logs.value.totalPages = response.totalPages || Math.ceil(logs.value.totalCount / logs.value.pageSize);
  } catch (error) {
    console.error('Error loading logs:', error);
    logs.value.data = [];
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  statsLoading.value = true;
  try {
    const response = await logsService.getLogStats();
    stats.value = response.data;
  } catch (error) {
    console.error('Error loading stats:', error);
  } finally {
    statsLoading.value = false;
  }
};

const loadLogLevels = async () => {
  try {
    const response = await logsService.getLogLevels();
    logLevels.value = response.data;
  } catch (error) {
    console.error('Error loading log levels:', error);
  }
};

const loadLogCategories = async () => {
  try {
    const response = await logsService.getLogCategories();
    logCategories.value = response.data;
  } catch (error) {
    console.error('Error loading log categories:', error);
  }
};

const refreshData = async () => {
  await Promise.all([
    loadLogs(),
    activeTab.value === 'stats' ? loadStats() : Promise.resolve()
  ]);
};

const changePage = (page) => {
  logs.value.page = page;
  loadLogs();
};

const clearFilters = () => {
  logFilters.value = {
    level: '',
    category: '',
    search: '',
    userEmail: '',
    ipAddress: '',
    fromDate: '',
    toDate: ''
  };
  logs.value.page = 1;
  loadLogs();
};

// Debounced search
const debouncedLoadLogs = debounce(loadLogs, 300);

// Export logs
const exportLogs = async () => {
  try {
    await securityService.exportLogs(logFilters.value, 'csv');
  } catch (error) {
    console.error('Error exporting logs:', error);
  }
};

const viewLogDetails = (log) => {
  selectedLog.value = log;
  showLogModal.value = true;
};

const saveSecuritySettings = async () => {
  savingSettings.value = true;
  try {
    // This would be implemented when we have a proper security settings API
    console.log('Saving security settings:', securitySettings.value);
    alert('Security settings saved successfully!');
  } catch (error) {
    console.error('Error saving security settings:', error);
    alert('Error saving security settings. Please try again.');
  } finally {
    savingSettings.value = false;
  }
};

const formatDateTime = (date) => {
  return new Date(date).toLocaleString();
};

const formatDateRange = () => {
  if (!stats.value.dateRange) return 'N/A';
  const from = new Date(stats.value.dateRange.from).toLocaleDateString();
  const to = new Date(stats.value.dateRange.to).toLocaleDateString();
  return `${from} - ${to}`;
};

const formatAdditionalData = (data) => {
  try {
    return JSON.stringify(JSON.parse(data), null, 2);
  } catch {
    return data;
  }
};

const getLogLevelClass = (level) => {
  switch (level?.toUpperCase()) {
    case 'CRITICAL': return 'is-danger';
    case 'ERROR': return 'is-danger';
    case 'WARNING': return 'is-warning';
    case 'INFO': return 'is-info';
    default: return 'is-light';
  }
};

onMounted(async () => {
  await Promise.all([
    loadLogLevels(),
    loadLogCategories(),
    loadLogs()
  ]);
});
</script>

<style scoped>
.admin-security {
  padding: 1rem;
}

.security-content {
  margin-top: 1.5rem;
}

.table-container {
  max-height: 500px;
  overflow-y: auto;
}

.log-message {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination {
  margin-top: 1rem;
}

.modal-card {
  max-width: 800px;
}

.modal-card-body {
  max-height: 70vh;
  overflow-y: auto;
}

pre {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.875rem;
}
</style>
