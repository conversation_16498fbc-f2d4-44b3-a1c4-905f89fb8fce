<template>
  <div class="company-select">
    <label class="label">{{ label }} <span v-if="required" class="has-text-danger">*</span></label>
    <div class="control">
      <div class="dropdown" :class="{ 'is-active': isDropdownOpen }">
        <div class="dropdown-trigger">
          <div class="field has-addons">
            <div class="control is-expanded">
              <input
                class="input"
                type="text"
                :placeholder="placeholder"
                v-model="searchQuery"
                @input="onSearchInput"
                @focus="openDropdown"
                @blur="onBlur"
                :readonly="readonly"
              />
            </div>
            <div class="control">
              <button
                class="button"
                type="button"
                @click="toggleDropdown"
                :disabled="readonly"
              >
                <span class="icon">
                  <i class="fas fa-chevron-down" :class="{ 'fa-rotate-180': isDropdownOpen }"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
        <div class="dropdown-menu" role="menu">
          <div class="dropdown-content">
            <div v-if="loading" class="dropdown-item">
              <div class="has-text-centered">
                <span class="icon">
                  <i class="fas fa-spinner fa-spin"></i>
                </span>
                Loading companies...
              </div>
            </div>
            <div v-else-if="filteredCompanies.length === 0" class="dropdown-item">
              <div class="has-text-grey has-text-centered">
                No companies found
              </div>
            </div>
            <template v-else>
              <a
                v-for="company in filteredCompanies"
                :key="company.id"
                class="dropdown-item"
                :class="{ 'is-active': selectedCompany?.id === company.id }"
                @mousedown.prevent="selectCompany(company)"
              >
                <div class="company-item">
                  <div class="company-name">{{ company.name }}</div>
                  <div class="company-details has-text-grey is-size-7">
                    {{ company.contactEmail }} • {{ company.addressCity }}
                  </div>
                </div>
              </a>
            </template>
          </div>
        </div>
      </div>
    </div>
    <p v-if="selectedCompany" class="help">
      Selected: {{ selectedCompany.name }}
    </p>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { companiesService } from '@/admin/services/companies';

// Props
const props = defineProps({
  modelValue: {
    type: [String, null],
    default: null
  },
  label: {
    type: String,
    default: 'Company'
  },
  placeholder: {
    type: String,
    default: 'Search and select company...'
  },
  required: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change']);

// Reactive data - Initialize with empty array to ensure it's always an array
const companies = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const isDropdownOpen = ref(false);
const selectedCompany = ref(null);

// Computed
const filteredCompanies = computed(() => {
  // Defensive programming: ensure companies.value is always an array
  const companiesArray = Array.isArray(companies.value) ? companies.value : [];

  console.log('🔍 Filtering companies:', {
    totalCompanies: companiesArray.length,
    searchQuery: searchQuery.value,
    isDropdownOpen: isDropdownOpen.value
  });

  // If no search query or dropdown just opened, show all companies
  if (!searchQuery.value.trim() || searchQuery.value === (selectedCompany.value?.name || '')) {
    console.log('📋 Showing all companies:', companiesArray.length);
    return companiesArray.slice(0, 50); // Limit to 50 items when no search
  }

  const query = searchQuery.value.toLowerCase().trim();
  const filtered = companiesArray.filter(company => {
    // Additional safety check for company object
    if (!company || typeof company !== 'object') return false;

    return (
      (company.name && company.name.toLowerCase().includes(query)) ||
      (company.contactEmail && company.contactEmail.toLowerCase().includes(query)) ||
      (company.addressCity && company.addressCity.toLowerCase().includes(query)) ||
      (company.slug && company.slug.toLowerCase().includes(query))
    );
  }).slice(0, 50); // Limit to 50 items

  console.log('🎯 Filtered companies:', filtered.length);
  return filtered;
});

// Methods
const fetchCompanies = async () => {
  try {
    loading.value = true;
    console.log('🏢 Fetching companies...');

    const response = await companiesService.getCompanies({ pageSize: 1000 });
    console.log('📦 Companies API response:', response);

    // Defensive programming: ensure we always get an array
    let companiesData = [];
    if (response && response.data && Array.isArray(response.data)) {
      companiesData = response.data;
    } else if (response && response.companies && Array.isArray(response.companies)) {
      companiesData = response.companies;
    } else if (Array.isArray(response)) {
      companiesData = response;
    } else {
      console.warn('⚠️ Unexpected API response structure:', response);
      companiesData = [];
    }

    // Ensure companies.value is always an array
    companies.value = companiesData;
    console.log('✅ Companies loaded:', companies.value.length);

    // If we have a modelValue, find and set the selected company
    if (props.modelValue && Array.isArray(companies.value)) {
      const found = companies.value.find(comp => comp && comp.id === props.modelValue);
      if (found) {
        selectedCompany.value = found;
        searchQuery.value = found.name || '';
        console.log('🎯 Selected company found:', found.name);
      } else {
        console.log('❌ Company not found for ID:', props.modelValue);
      }
    }
  } catch (error) {
    console.error('❌ Error fetching companies:', error);
    // Ensure companies.value is always an array even on error
    companies.value = [];
  } finally {
    loading.value = false;
  }
};

const selectCompany = (company) => {
  // Defensive programming: ensure company object is valid
  if (!company || typeof company !== 'object' || !company.id) {
    console.error('❌ Invalid company object:', company);
    return;
  }

  selectedCompany.value = company;
  searchQuery.value = company.name || '';
  isDropdownOpen.value = false;

  console.log('✅ Company selected:', company.name);
  emit('update:modelValue', company.id);
  emit('change', company);
};

const openDropdown = () => {
  if (!props.readonly) {
    isDropdownOpen.value = true;
    console.log('📂 Dropdown opened');
  }
};

const closeDropdown = () => {
  isDropdownOpen.value = false;
  console.log('📁 Dropdown closed');
};

const toggleDropdown = () => {
  if (props.readonly) return;

  if (!isDropdownOpen.value) {
    // When opening dropdown, clear search if it matches selected company
    if (selectedCompany.value && searchQuery.value === selectedCompany.value.name) {
      searchQuery.value = '';
    }
    openDropdown();
  } else {
    closeDropdown();
  }
};

const onSearchInput = () => {
  if (!isDropdownOpen.value) {
    isDropdownOpen.value = true;
    console.log('📂 Dropdown opened via search input');
  }
};

const onBlur = () => {
  // Delay closing to allow click on dropdown items
  setTimeout(() => {
    closeDropdown();

    // If no company is selected, clear the search
    if (!selectedCompany.value) {
      searchQuery.value = '';
    } else {
      // Restore the selected company name with defensive programming
      searchQuery.value = selectedCompany.value.name || '';
    }
  }, 200);
};

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  // Defensive programming: ensure companies.value is an array
  const companiesArray = Array.isArray(companies.value) ? companies.value : [];

  if (newValue && companiesArray.length > 0) {
    const found = companiesArray.find(comp => comp && comp.id === newValue);
    if (found) {
      selectedCompany.value = found;
      searchQuery.value = found.name || '';
      console.log('🔄 Company updated via watch:', found.name);
    } else {
      console.log('🔍 Company not found in watch for ID:', newValue);
    }
  } else if (!newValue) {
    selectedCompany.value = null;
    searchQuery.value = '';
    console.log('🧹 Cleared company selection');
  }
});

// Lifecycle
onMounted(() => {
  fetchCompanies();
});
</script>

<style scoped>
.company-select {
  position: relative;
}

.dropdown {
  width: 100%;
}

.dropdown-menu {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.company-item {
  padding: 0.25rem 0;
}

.company-name {
  font-weight: 500;
}

.company-details {
  margin-top: 0.125rem;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item.is-active {
  background-color: #3273dc;
  color: white;
}

.dropdown-item.is-active .company-details {
  color: #e8e8e8;
}

.fa-rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}
</style>
