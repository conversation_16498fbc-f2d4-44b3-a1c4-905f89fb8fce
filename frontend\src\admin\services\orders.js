import api from '@/services/api';
import {
  transformOrderStatusForAPI,
  transformPaymentStatusForAPI
} from '@/admin/utils/orderConstants';
import { logError, getErrorMessage } from '@/admin/utils/errorHandler';
import { eventBus, EVENTS, updateFlags, createOrderEvent } from '@/admin/utils/eventBus';

// Status conversion functions
const convertStatusStringToNumber = (status) => {
  if (typeof status === 'number') return status;
  if (!status) return 0; // Default to Processing

  const statusLower = status.toLowerCase();
  switch (statusLower) {
    case 'processing': return 0;
    case 'pending': return 1;
    case 'shipped': return 2;
    case 'delivered': return 3;
    case 'cancelled': return 4;
    default: return 0; // Default to Processing
  }
};

const convertPaymentStatusStringToNumber = (status) => {
  if (typeof status === 'number') return status;
  if (!status) return 0; // Default to Pending

  const statusLower = status.toLowerCase();
  switch (statusLower) {
    case 'pending': return 0;
    case 'completed': return 1;
    case 'refunded': return 2;
    case 'failed': return 3;
    default: return 0; // Default to Pending
  }
};

// API format conversion functions (for string-based API)
const convertStatusStringToAPIFormat = (status) => {
  if (!status) return 'Processing'; // Default

  const statusLower = status.toLowerCase();
  switch (statusLower) {
    case 'processing': return 'Processing';
    case 'pending': return 'Pending';
    case 'shipped': return 'Shipped';
    case 'delivered': return 'Delivered';
    case 'cancelled': return 'Cancelled';
    default: return 'Processing'; // Default
  }
};

const convertPaymentStatusStringToAPIFormat = (status) => {
  if (!status) return 'Pending'; // Default

  const statusLower = status.toLowerCase();
  switch (statusLower) {
    case 'pending': return 'Pending';
    case 'completed': return 'Completed';
    case 'refunded': return 'Refunded';
    case 'failed': return 'Failed';
    default: return 'Pending'; // Default
  }
};

// API retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  retryableStatuses: [408, 429, 500, 502, 503, 504]
};

// Utility function for API calls with retry logic
const apiWithRetry = async (apiCall, context = 'API call', retryConfig = RETRY_CONFIG) => {
  let lastError;

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      const result = await apiCall();

      // Log successful retry
      if (attempt > 0) {
        console.log(`✅ ${context} succeeded on attempt ${attempt + 1}`);
      }

      return result;
    } catch (error) {
      lastError = error;

      // Check if error is retryable
      const isRetryable = retryConfig.retryableStatuses.includes(error.response?.status) ||
                         error.code === 'NETWORK_ERROR' ||
                         error.message?.includes('timeout') ||
                         error.message?.includes('Network Error');

      // Don't retry on last attempt or non-retryable errors
      if (attempt === retryConfig.maxRetries || !isRetryable) {
        console.error(`❌ ${context} failed after ${attempt + 1} attempts:`, error);
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        retryConfig.baseDelay * Math.pow(2, attempt),
        retryConfig.maxDelay
      );

      console.warn(`⚠️ ${context} failed on attempt ${attempt + 1}, retrying in ${delay}ms...`, error.message);

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

// Покращена система кешування
class OrdersCache {
  constructor() {
    this.cache = new Map();
    this.version = 1;
    this.CACHE_DURATION = 30000; // 30 секунд
    this.MAX_CACHE_SIZE = 100; // Максимальна кількість записів
  }

  // Генерація ключа кешу з версією
  generateKey(params) {
    return `orders_v${this.version}_${JSON.stringify(params)}`;
  }

  // Отримання з кешу
  get(params) {
    const key = this.generateKey(params);
    const cached = this.cache.get(key);

    if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
      console.log('📦 Cache hit for:', key);
      return cached.data;
    }

    if (cached) {
      console.log('⏰ Cache expired for:', key);
      this.cache.delete(key);
    }

    return null;
  }

  // Збереження в кеш
  set(params, data) {
    const key = this.generateKey(params);

    // Очищення старих записів якщо кеш переповнений
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
      console.log('🧹 Removed oldest cache entry:', oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });

    console.log('💾 Cached data for:', key);
  }

  // Інвалідація кешу
  invalidate() {
    this.version++;
    this.cache.clear();
    console.log('🔄 Cache invalidated, new version:', this.version);
  }

  // Примусове очищення
  clear() {
    this.cache.clear();
    console.log('🧹 Cache cleared');
  }

  // Часткова інвалідація по паттерну
  invalidatePattern(pattern) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      console.log('🗑️ Invalidated cache entry:', key);
    });
  }

  // Отримання статистики кешу
  getStats() {
    return {
      size: this.cache.size,
      version: this.version,
      maxSize: this.MAX_CACHE_SIZE,
      duration: this.CACHE_DURATION
    };
  }
}

const ordersCache = new OrdersCache();

// Enhanced logging system
class OrdersLogger {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000;
    this.sessionId = Math.random().toString(36).substr(2, 9);
    this.startTime = Date.now();
  }

  log(level, operation, data = {}, error = null) {
    const logEntry = {
      id: Math.random().toString(36).substr(2, 9),
      timestamp: Date.now(),
      sessionId: this.sessionId,
      level,
      operation,
      data: JSON.parse(JSON.stringify(data)), // Deep clone
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : null,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.logs.push(logEntry);

    // Keep logs manageable
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with formatting
    const prefix = `[Orders ${level.toUpperCase()}]`;
    const message = `${operation}${data.orderId ? ` (Order: ${data.orderId})` : ''}`;

    switch (level) {
      case 'error':
        console.error(prefix, message, data, error);
        break;
      case 'warn':
        console.warn(prefix, message, data);
        break;
      case 'info':
        console.info(prefix, message, data);
        break;
      case 'debug':
        console.log(prefix, message, data);
        break;
    }

    return logEntry;
  }

  error(operation, data, error) {
    return this.log('error', operation, data, error);
  }

  warn(operation, data) {
    return this.log('warn', operation, data);
  }

  info(operation, data) {
    return this.log('info', operation, data);
  }

  debug(operation, data) {
    return this.log('debug', operation, data);
  }

  // Get logs by criteria
  getLogs(criteria = {}) {
    let filteredLogs = [...this.logs];

    if (criteria.level) {
      filteredLogs = filteredLogs.filter(log => log.level === criteria.level);
    }

    if (criteria.operation) {
      filteredLogs = filteredLogs.filter(log =>
        log.operation.toLowerCase().includes(criteria.operation.toLowerCase())
      );
    }

    if (criteria.orderId) {
      filteredLogs = filteredLogs.filter(log =>
        log.data.orderId === criteria.orderId
      );
    }

    if (criteria.since) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= criteria.since);
    }

    return filteredLogs.sort((a, b) => b.timestamp - a.timestamp);
  }

  // Export logs for analysis
  exportLogs() {
    const exportData = {
      sessionId: this.sessionId,
      startTime: this.startTime,
      exportTime: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      cacheStats: ordersCache.getStats(),
      logs: this.logs
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `orders-logs-${this.sessionId}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.info('exportLogs', { exportedCount: this.logs.length });
  }

  // Clear logs
  clear() {
    const clearedCount = this.logs.length;
    this.logs = [];
    this.info('clearLogs', { clearedCount });
  }

  // Get diagnostic info
  getDiagnostics() {
    const now = Date.now();
    const sessionDuration = now - this.startTime;

    const logsByLevel = this.logs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {});

    const recentErrors = this.getLogs({ level: 'error', since: now - 300000 }); // Last 5 minutes

    return {
      sessionId: this.sessionId,
      sessionDuration,
      totalLogs: this.logs.length,
      logsByLevel,
      recentErrors: recentErrors.length,
      cacheStats: ordersCache.getStats(),
      lastActivity: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : this.startTime
    };
  }
}

const ordersLogger = new OrdersLogger();



export const ordersService = {
  // Функція примусового оновлення
  forceRefresh() {
    ordersCache.invalidate();
    ordersLogger.info('forceRefresh', { cacheStats: ordersCache.getStats() });
  },

  // Отримання статистики кешу
  getCacheStats() {
    return ordersCache.getStats();
  },

  // Отримання логів
  getLogs(criteria) {
    return ordersLogger.getLogs(criteria);
  },

  // Експорт логів
  exportLogs() {
    return ordersLogger.exportLogs();
  },

  // Діагностична інформація
  getDiagnostics() {
    return ordersLogger.getDiagnostics();
  },

  // Очищення логів
  clearLogs() {
    return ordersLogger.clear();
  },

  async getAll(params = {}) {
    return ordersService.getOrders(params);
  },

  async getAllAdmin(params = {}) {
    return ordersService.getOrders(params);
  },

  async getOrders(params = {}) {
    const startTime = Date.now();
    ordersLogger.info('getOrders:start', { params });

    try {
      console.log('Requesting orders with params:', params);

      // Check cache first (but skip cache for filtered requests to ensure fresh data)
      const hasFilters = params.status || params.paymentStatus || params.search || params.filter;
      if (!hasFilters) {
        const cached = ordersCache.get(params);
        if (cached) {
          return cached;
        }
      }

      // Стандартизуємо параметри для відповідності API (як у Companies)
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;

      // Параметри сортування
      if (params.orderBy) apiParams.orderBy = params.orderBy;
      if (params.sortBy) apiParams.orderBy = params.sortBy; // Конвертуємо sortBy в orderBy
      if (params.descending !== undefined) apiParams.descending = params.descending;
      if (params.sortOrder) {
        apiParams.descending = params.sortOrder === 'desc';
        console.log('🔄 Setting sort order:', params.sortOrder, '-> descending:', apiParams.descending);
      }

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      // Фільтри статусу - переконуємося, що передаємо числові значення
      if (params.status !== undefined && params.status !== null && params.status !== '') {
        // Конвертуємо в число, якщо це рядок
        const statusValue = typeof params.status === 'string' ? parseInt(params.status, 10) : params.status;
        if (!isNaN(statusValue)) {
          apiParams.status = statusValue;
          console.log('🔄 Setting order status filter:', statusValue);
        }
      }

      if (params.paymentStatus !== undefined && params.paymentStatus !== null && params.paymentStatus !== '') {
        // Конвертуємо в число, якщо це рядок
        const paymentStatusValue = typeof params.paymentStatus === 'string' ? parseInt(params.paymentStatus, 10) : params.paymentStatus;
        if (!isNaN(paymentStatusValue)) {
          apiParams.paymentStatus = paymentStatusValue;
          console.log('🔄 Setting payment status filter:', paymentStatusValue);
        }
      }

      // Фільтри дат
      if (params.dateFrom) {
        apiParams.dateFrom = params.dateFrom;
      }

      if (params.dateTo) {
        apiParams.dateTo = params.dateTo;
      }

      console.log('📊 Final API params being sent:', apiParams);
      console.log('📋 Original params received:', params);

      // Clear cache when filters are applied to ensure fresh data
      if (hasFilters) {
        console.log('🧹 Clearing cache due to filters');
        ordersCache.clear();
      }

      const response = await api.get('/api/admin/orders', { params: apiParams });

      console.log('Orders API response:', response);

      // Handle ApiResponse<PaginatedResponse<OrderResponse>> format
      if (response.data && response.data.success && response.data.data) {
        const responseData = response.data.data;
        console.log('Processing ApiResponse format:', responseData);

        const result = {
          success: true,
          data: responseData.data || responseData.items || [],
          totalItems: responseData.total || responseData.totalItems || 0,
          currentPage: responseData.currentPage || responseData.page || params.page || 1,
          pageSize: responseData.perPage || responseData.pageSize || params.pageSize || 10,
          totalPages: responseData.lastPage || responseData.totalPages || 1
        };

        console.log('Processed result:', result);

        // Кешуємо результат
        if (!hasFilters) {
          ordersCache.set(params, result);
        }

        return result;
      }

      // Handle direct response format
      if (response.data && response.data.items) {
        const result = {
          success: true,
          data: response.data.items || [],
          totalItems: response.data.totalItems || response.data.total || 0,
          currentPage: response.data.currentPage || response.data.page || params.page || 1,
          pageSize: response.data.pageSize || response.data.perPage || params.pageSize || 10,
          totalPages: response.data.totalPages || response.data.lastPage || 1
        };

        // Кешуємо результат
        if (!hasFilters) {
          ordersCache.set(params, result);
        }

        return result;
      }

      // Fallback to direct data
      const result = {
        success: true,
        data: response.data || [],
        totalItems: 0,
        currentPage: 1,
        pageSize: 10,
        totalPages: 1
      };

      // Кешуємо результат
      if (!hasFilters) {
        ordersCache.set(params, result);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      ordersLogger.error('getOrders:error', {
        params,
        duration,
        errorType: error.name,
        statusCode: error.response?.status
      }, error);

      logError(error, 'getOrders', { params });

      // Create user-friendly error message
      const errorMessage = getErrorMessage(error, 'ORDER_LIST_LOAD_FAILED');

      // Re-throw with enhanced error information
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.context = 'getOrders';
      throw enhancedError;
    }
  },

  // Get order by ID (alias for getOrderById)
  async getById(id) {
    return this.getOrderById(id);
  },

  // Update order
  async update(id, data) {
    try {
      console.log('=== ORDER UPDATE DEBUG ===');
      console.log('Order ID:', id);
      console.log('Original frontend data:', JSON.stringify(data, null, 2));

      // Analyze input data structure
      console.log('Data analysis:');
      console.log('- status type:', typeof data.status, 'value:', data.status);
      console.log('- paymentStatus type:', typeof data.paymentStatus, 'value:', data.paymentStatus);
      console.log('- shippingAddress type:', typeof data.shippingAddress, 'value:', data.shippingAddress);
      console.log('- items type:', typeof data.items, 'length:', data.items?.length);
      console.log('- trackingNumber type:', typeof data.trackingNumber, 'value:', data.trackingNumber);
      console.log('- shippingMethod type:', typeof data.shippingMethod, 'value:', data.shippingMethod);
      console.log('- notes type:', typeof data.notes, 'value:', data.notes);

      // Transform frontend data to API format - convert to proper string format
      const transformedStatus = convertStatusStringToAPIFormat(data.status);
      const transformedPaymentStatus = convertPaymentStatusStringToAPIFormat(data.paymentStatus);

      console.log('Status transformation:', data.status, '->', transformedStatus);
      console.log('Payment status transformation:', data.paymentStatus, '->', transformedPaymentStatus);

      // Validate and transform items
      let transformedItems = [];
      if (data.items && Array.isArray(data.items)) {
        transformedItems = data.items
          .filter(item => item && item.id) // Only include items with valid IDs
          .map(item => {
            console.log('Transforming item:', item);

            // Ensure quantity is a positive integer
            const quantity = Math.max(1, parseInt(item.quantity) || 1);

            // Ensure price is a valid decimal
            const priceAmount = parseFloat(item.price || item.priceAmount || 0);

            return {
              id: item.id,
              quantity: quantity,
              priceAmount: priceAmount,
              priceCurrency: item.priceCurrency || 'UAH'
            };
          });
      }

      // Ensure notes is always a string, not an array
      let notesValue = data.notes || data.comment || '';
      if (Array.isArray(notesValue)) {
        notesValue = notesValue.join('; '); // Convert array to string
      }
      if (typeof notesValue !== 'string') {
        notesValue = String(notesValue || '');
      }

      const requestData = {
        status: transformedStatus,
        paymentStatus: transformedPaymentStatus,
        shippingAddress: data.shippingAddress?.address1 || data.shippingAddress,
        trackingNumber: data.trackingNumber || null,
        shippingMethod: data.shippingMethod || data.shippingMethodName || null,
        notes: notesValue,
        items: transformedItems
      };

      // Remove null/undefined fields to avoid backend issues
      const cleanedRequestData = {};
      Object.keys(requestData).forEach(key => {
        if (requestData[key] !== null && requestData[key] !== undefined) {
          cleanedRequestData[key] = requestData[key];
        }
      });

      console.log('Final request data (before cleaning):', JSON.stringify(requestData, null, 2));
      console.log('Final request data (after cleaning):', JSON.stringify(cleanedRequestData, null, 2));
      console.log('Request data validation:');
      console.log('- All required fields present:', {
        status: !!cleanedRequestData.status,
        paymentStatus: !!cleanedRequestData.paymentStatus,
        items: Array.isArray(cleanedRequestData.items)
      });
      console.log('- Data types check:', {
        status: typeof cleanedRequestData.status,
        paymentStatus: typeof cleanedRequestData.paymentStatus,
        shippingAddress: typeof cleanedRequestData.shippingAddress,
        trackingNumber: typeof cleanedRequestData.trackingNumber,
        shippingMethod: typeof cleanedRequestData.shippingMethod,
        notes: typeof cleanedRequestData.notes,
        items: Array.isArray(cleanedRequestData.items) ? 'array' : typeof cleanedRequestData.items
      });

      const response = await api.put(`/api/admin/orders/${id}`, cleanedRequestData);
      console.log('✅ Update order response:', response);
      console.log('=== ORDER UPDATE SUCCESS ===');

      // Clear cache to ensure fresh data
      ordersCache.invalidate();

      // Mark orders for refresh and emit events
      updateFlags.markOrdersForRefresh();
      eventBus.emit(EVENTS.ORDER_UPDATED, { orderId: id, data: response.data });

      return response.data;
    } catch (error) {
      logError(error, 'updateOrder', { orderId: id, requestData: data });

      // Create user-friendly error message
      const errorMessage = getErrorMessage(error, 'ORDER_UPDATE_FAILED');

      // Re-throw with enhanced error information
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.context = 'updateOrder';
      enhancedError.orderId = id;
      throw enhancedError;
    }
  },

  async getOrderById(id) {
    try {
      console.log(`Fetching order ${id} from API...`);
      const response = await api.get(`/api/admin/orders/${id}`);
      console.log('Order API response:', response);

      // Handle ApiResponse<OrderResponse> format
      if (response.data && response.data.success && response.data.data) {
        const orderData = response.data.data;
        console.log('Order data extracted:', orderData);

        // Transform the data to match frontend expectations
        const transformedOrder = {
          id: orderData.id,
          customerId: orderData.customerId,
          customerName: orderData.customerName,
          customerEmail: orderData.customerEmail,
          total: orderData.totalPriceAmount,
          totalPriceAmount: orderData.totalPriceAmount,
          subtotal: orderData.totalPriceAmount, // Remove tax calculation
          tax: 0, // Remove tax from calculations
          shipping: 0,
          discount: 0,
          status: orderData.status !== undefined ? orderData.status : 0,
          paymentStatus: orderData.paymentStatus !== undefined ? orderData.paymentStatus : 0,
          paymentStatusText: orderData.paymentStatusText || 'Pending',
          paymentMethod: orderData.paymentMethodText || 'Card',
          paymentMethodText: orderData.paymentMethodText || 'Card',
          shippingMethod: orderData.shippingMethodName || 'Standard',
          shippingMethodName: orderData.shippingMethodName || 'Standard',
          shippingAddress: {
            address1: orderData.shippingAddressLine || 'N/A',
            city: orderData.shippingCity || 'N/A',
            country: orderData.shippingCountry || 'N/A'
          },
          items: [], // Will be populated by separate API call
          notes: '', // Changed from array to string
          createdAt: orderData.createdAt,
          updatedAt: orderData.createdAt
        };

        // Fetch order items separately
        try {
          const itemsResponse = await this.getOrderItems(orderData.id);
          if (itemsResponse && itemsResponse.success && itemsResponse.data && Array.isArray(itemsResponse.data)) {
            transformedOrder.items = itemsResponse.data;
          } else {
            transformedOrder.items = [];
          }
        } catch (itemsError) {
          console.warn('Could not fetch order items:', itemsError);
          transformedOrder.items = [];
        }

        return transformedOrder;
      }

      // Handle direct response format
      if (response.data) {
        return response.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`Error fetching order ${id}:`, error);

      // Check if it's a 404 error
      if (error.response && error.response.status === 404) {
        return null; // Return null for not found
      }

      // Return mock data for other errors
      return {
        id,
        customerId: '1',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        total: 156.78,
        totalPriceAmount: 156.78,
        subtotal: 149.99,
        tax: 0,
        shipping: 0,
        discount: 0,
        status: 0, // Processing
        paymentStatus: 1, // Completed
        paymentStatusText: 'Paid',
        paymentMethod: 'Credit Card',
        paymentMethodText: 'Credit Card',
        shippingMethod: 'Standard',
        shippingMethodName: 'Standard',
        shippingAddress: {
          firstName: 'John',
          lastName: 'Doe',
          address1: '123 Main St',
          address2: 'Apt 4B',
          city: 'New York',
          state: 'NY',
          postalCode: '10001',
          country: 'USA',
          phone: '************'
        },
        billingAddress: {
          firstName: 'John',
          lastName: 'Doe',
          address1: '123 Main St',
          address2: 'Apt 4B',
          city: 'New York',
          state: 'NY',
          postalCode: '10001',
          country: 'USA',
          phone: '************'
        },
        items: [
          {
            id: '1',
            productId: '1',
            productName: 'Smartphone X',
            quantity: 1,
            price: 99.99,
            total: 99.99
          },
          {
            id: '2',
            productId: '2',
            productName: 'Wireless Headphones',
            quantity: 1,
            price: 49.99,
            total: 49.99
          }
        ],
        notes: '', // Changed from array to string
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
        updatedAt: new Date(Date.now() - 1000 * 60 * 30)
      };
    }
  },

  async updateOrderStatus(id, status) {
    try {
      console.log('=== ORDER STATUS UPDATE DEBUG ===');
      console.log('Order ID:', id);
      console.log('Input status:', status, 'type:', typeof status);

      // Convert status to API format (strings)
      let statusString, paymentStatusString;

      if (typeof status === 'object' && status !== null) {
        // Handle object with status and paymentStatus
        statusString = convertStatusStringToAPIFormat(status.status);
        paymentStatusString = convertPaymentStatusStringToAPIFormat(status.paymentStatus);
      } else {
        // Handle single status value
        statusString = convertStatusStringToAPIFormat(status);
      }

      console.log('Converted status string:', statusString);
      console.log('Converted payment status string:', paymentStatusString);

      const requestBody = {
        status: statusString,
        paymentStatus: paymentStatusString,
        timestamp: Date.now(),
        requestId: Math.random().toString(36).substr(2, 9)
      };
      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      // Use retry mechanism for status update
      const response = await apiWithRetry(
        () => api.patch(`/api/admin/orders/${id}/status`, requestBody, {
          headers: {
            'X-Request-ID': requestBody.requestId,
            'X-Idempotency-Key': `order-status-${id}-${statusString}-${requestBody.timestamp}`
          }
        }),
        `Order ${id} status update to ${statusString}`
      );

      console.log('✅ Order status update successful:', response);
      console.log('=== ORDER STATUS UPDATE SUCCESS ===');

      // Clear cache to ensure fresh data
      ordersCache.invalidate();

      // Mark orders for refresh and emit events with enhanced data
      updateFlags.markOrdersForRefresh();
      eventBus.emit(EVENTS.ORDER_STATUS_CHANGED, createOrderEvent(
        EVENTS.ORDER_STATUS_CHANGED,
        id,
        {
          oldStatus: status,
          newStatus: statusString,
          data: response.data,
          requestId: requestBody.requestId
        }
      ));

      return response.data;
    } catch (error) {
      logError(error, 'updateOrderStatus', { orderId: id, status });

      // Emit error event
      eventBus.emit(EVENTS.ERROR_OCCURRED, createOrderEvent(
        EVENTS.ERROR_OCCURRED,
        id,
        {
          operation: 'updateOrderStatus',
          error: error.message,
          status
        }
      ));

      // Create user-friendly error message
      const errorMessage = getErrorMessage(error, 'ORDER_STATUS_UPDATE_FAILED');

      // Re-throw with enhanced error information
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.context = 'updateOrderStatus';
      enhancedError.orderId = id;
      throw enhancedError;
    }
  },

  async updatePaymentStatus(id, paymentStatus) {
    try {
      console.log('=== PAYMENT STATUS UPDATE DEBUG ===');
      console.log('Order ID:', id);
      console.log('Input payment status:', paymentStatus, 'type:', typeof paymentStatus);

      // Use standardized transformation
      const statusString = transformPaymentStatusForAPI(paymentStatus) || paymentStatus;
      console.log('Converted payment status:', statusString);

      // Validate status
      const validStatuses = ['Pending', 'Completed', 'Refunded', 'Failed'];
      if (!validStatuses.includes(statusString)) {
        throw new Error(`Invalid payment status: ${statusString}. Valid statuses: ${validStatuses.join(', ')}`);
      }

      const requestBody = {
        paymentStatus: statusString,
        timestamp: Date.now(),
        requestId: Math.random().toString(36).substr(2, 9)
      };
      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      // Use retry mechanism for payment status update
      const response = await apiWithRetry(
        () => api.patch(`/api/admin/orders/${id}/payment-status`, requestBody, {
          headers: {
            'X-Request-ID': requestBody.requestId,
            'X-Idempotency-Key': `payment-status-${id}-${statusString}-${requestBody.timestamp}`
          }
        }),
        `Order ${id} payment status update to ${statusString}`
      );

      console.log('✅ Payment status update successful:', response);
      console.log('=== PAYMENT STATUS UPDATE SUCCESS ===');

      // Clear cache to ensure fresh data
      ordersCache.invalidate();

      // Mark orders for refresh and emit events
      updateFlags.markOrdersForRefresh();
      eventBus.emit(EVENTS.ORDER_PAYMENT_STATUS_CHANGED, createOrderEvent(
        EVENTS.ORDER_PAYMENT_STATUS_CHANGED,
        id,
        {
          oldPaymentStatus: paymentStatus,
          newPaymentStatus: statusString,
          data: response.data,
          requestId: requestBody.requestId
        }
      ));

      return response.data;
    } catch (error) {
      logError(error, 'updatePaymentStatus', { orderId: id, paymentStatus });

      // Emit error event
      eventBus.emit(EVENTS.ERROR_OCCURRED, createOrderEvent(
        EVENTS.ERROR_OCCURRED,
        id,
        {
          operation: 'updatePaymentStatus',
          error: error.message,
          paymentStatus
        }
      ));

      // Create user-friendly error message
      const errorMessage = getErrorMessage(error, 'PAYMENT_STATUS_UPDATE_FAILED');

      // Re-throw with enhanced error information
      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.context = 'updatePaymentStatus';
      enhancedError.orderId = id;
      throw enhancedError;
    }
  },

  async addOrderNote(id, note) {
    try {
      const response = await api.post(`/api/admin/orders/${id}/notes`, { note });
      return response.data;
    } catch (error) {
      console.error(`Error adding note to order ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        note: {
          id: Math.floor(Math.random() * 1000).toString(),
          content: note,
          createdAt: new Date(),
          createdBy: 'Admin'
        }
      };
    }
  },

  async getOrderNotes(id) {
    try {
      const response = await api.get(`/api/admin/orders/${id}/notes`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching notes for order ${id}:`, error);
      // Return mock data
      return [
        {
          id: '1',
          content: 'Order received and processing',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
          createdBy: 'System'
        },
        {
          id: '2',
          content: 'Payment confirmed',
          createdAt: new Date(Date.now() - 1000 * 60 * 60),
          createdBy: 'System'
        },
        {
          id: '3',
          content: 'Customer requested expedited shipping',
          createdAt: new Date(Date.now() - 1000 * 60 * 30),
          createdBy: 'Admin'
        }
      ];
    }
  },

  async refundOrder(id, amount, reason) {
    try {
      const response = await api.post(`/api/admin/orders/${id}/refund`, { amount, reason });
      return response.data;
    } catch (error) {
      console.error(`Error processing refund for order ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        refund: {
          id: Math.floor(Math.random() * 1000).toString(),
          amount,
          reason,
          createdAt: new Date()
        }
      };
    }
  },

  async exportOrders(params = {}) {
    try {
      const response = await api.get('/api/admin/orders/export', {
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting orders:', error);
      // Create a mock CSV file for demo purposes
      const headers = 'Order ID,Customer,Email,Date,Total,Status,Payment Status\n';
      const rows = [
        'ORD-1001,John Doe,<EMAIL>,2023-01-01,156.78,Processing,Paid',
        'ORD-1002,Jane Smith,<EMAIL>,2023-01-02,89.99,Pending,Pending',
        'ORD-1003,Robert Johnson,<EMAIL>,2023-01-03,245.50,Shipped,Paid',
        'ORD-1004,Emily Davis,<EMAIL>,2023-01-04,78.25,Delivered,Paid',
        'ORD-1005,Michael Wilson,<EMAIL>,2023-01-05,189.99,Cancelled,Refunded'
      ].join('\n');

      const csvContent = headers + rows;
      return new Blob([csvContent], { type: 'text/csv' });
    }
  },

  async getOrderStats(period = 'month') {
    try {
      const response = await api.get(`/api/admin/orders/stats?period=${period}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order stats:', error);
      // Return mock data
      return {
        total: 356,
        totalRevenue: 28456.78,
        averageOrderValue: 79.93,
        byStatus: {
          'Pending': 45,
          'Processing': 32,
          'Shipped': 18,
          'Delivered': 256,
          'Cancelled': 5
        },
        byPeriod: [
          { date: '2023-01-01', count: 12, revenue: 956.78 },
          { date: '2023-01-02', count: 15, revenue: 1245.50 },
          { date: '2023-01-03', count: 8, revenue: 678.25 },
          { date: '2023-01-04', count: 20, revenue: 1789.99 },
          { date: '2023-01-05', count: 18, revenue: 1456.78 }
        ]
      };
    }
  },

  async getOrdersByCustomer(customerId, params = {}) {
    try {
      // Змінено шлях з customers на users для відповідності API
      const response = await api.get(`/api/admin/users/${customerId}/orders`, { params });

      // Перевірка формату відповіді
      if (response.data && response.data.data) {
        return {
          orders: response.data.data.items || [],
          pagination: {
            total: response.data.data.totalItems || 0,
            page: response.data.data.currentPage || 1,
            limit: response.data.data.pageSize || 10,
            totalPages: response.data.data.totalPages || 1
          }
        };
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching orders for user ${customerId}:`, error);
      // Return mock data
      return {
        orders: [
          {
            id: 'ORD-1001',
            total: 156.78,
            status: 'Processing',
            paymentStatus: 'Paid',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2)
          },
          {
            id: 'ORD-1002',
            total: 89.99,
            status: 'Delivered',
            paymentStatus: 'Paid',
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)
          }
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      };
    }
  },

  async cancelOrder(id, reason) {
    try {
      const response = await api.post(`/api/admin/orders/${id}/cancel`, { reason });
      return response.data;
    } catch (error) {
      console.error(`Error cancelling order ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        order: {
          id,
          status: 'Cancelled',
          updatedAt: new Date()
        }
      };
    }
  },

  async resendOrderConfirmation(id) {
    try {
      const response = await api.post(`/api/admin/orders/${id}/resend-confirmation`);
      return response.data;
    } catch (error) {
      console.error(`Error resending confirmation for order ${id}:`, error);
      // Return mock success response
      return {
        success: true,
        message: 'Order confirmation email has been resent.'
      };
    }
  },

  // Order Items Management
  async getOrderItems(orderId, params = {}) {
    try {
      console.log(`Fetching items for order ${orderId}...`);
      const response = await api.get(`/api/admin/orders/${orderId}/items`, { params });
      console.log('Order items API response:', response);

      // Handle ApiResponse<PaginatedResponse<OrderItemResponse>> format
      if (response.data && response.data.success && response.data.data) {
        const itemsData = response.data.data;
        console.log('Order items data extracted:', itemsData);

        // Extract items from paginated response
        const items = itemsData.data || itemsData.items || [];

        // Transform OrderItemResponse to frontend format
        const transformedItems = items.map(item => ({
          id: item.id,
          productId: item.productId,
          productName: item.productName || 'Unknown Product',
          productImage: item.productImage || null,
          quantity: item.quantity,
          price: parseFloat(item.priceAmount || 0),
          priceAmount: parseFloat(item.priceAmount || 0),
          total: parseFloat(item.priceAmount || 0) * (item.quantity || 0)
        }));

        return {
          success: true,
          data: transformedItems
        };
      }

      // Handle direct response format
      if (response.data && Array.isArray(response.data)) {
        return {
          success: true,
          data: response.data
        };
      }

      // No items found
      return {
        success: true,
        data: []
      };
    } catch (error) {
      console.error(`Error fetching items for order ${orderId}:`, error);

      // Return empty array instead of mock data for real orders
      return {
        success: true,
        data: []
      };
    }
  },

  async updateOrderItem(orderId, itemId, itemData) {
    try {
      const response = await api.put(`/api/admin/orders/${orderId}/items/${itemId}`, itemData);
      return response.data;
    } catch (error) {
      console.error(`Error updating item ${itemId} in order ${orderId}:`, error);
      // Return mock success response
      return {
        success: true,
        item: {
          id: itemId,
          ...itemData,
          updatedAt: new Date()
        }
      };
    }
  },

  async addOrderItem(orderId, itemData) {
    try {
      const response = await api.post(`/api/admin/orders/${orderId}/items`, itemData);
      return response.data;
    } catch (error) {
      console.error(`Error adding item to order ${orderId}:`, error);
      // Return mock success response
      return {
        success: true,
        item: {
          id: Math.floor(Math.random() * 1000).toString(),
          ...itemData,
          createdAt: new Date()
        }
      };
    }
  },

  async removeOrderItem(orderId, itemId) {
    try {
      const response = await api.delete(`/api/admin/orders/${orderId}/items/${itemId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing item ${itemId} from order ${orderId}:`, error);
      // Return mock success response
      return {
        success: true,
        message: 'Item removed successfully'
      };
    }
  }
};
