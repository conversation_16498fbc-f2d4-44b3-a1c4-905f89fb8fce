import apiClient from './api.service';

class DashboardService {
  // Get dashboard statistics
  async getStats() {
    try {
      const response = await apiClient.get('/admin/dashboard');
      console.log('Dashboard stats API response:', response);
      return {
        data: response.data.data.stats
      };
    } catch (error) {
      console.warn('Dashboard stats API not available, using mock data', error);
      // Return mock data if API fails
      return {
        data: {
          products: 150,
          categories: 50,
          orders: 30,
          users: 125,
          revenue: 19210.50
        }
      };
    }
  }

  // Get recent users
  async getRecentUsers(limit = 5) {
    try {
      return await apiClient.get('/dashboard/recent-users', { params: { limit } });
    } catch (error) {
      console.warn('Recent users API not available, using mock data');
      // Return mock data if API fails
      return {
        data: [
          { id: 'a1b2c3d4', username: 'johndoe', email: '<EMAIL>', role: 'Admin', createdAt: new Date() },
          { id: 'b2c3d4e5', username: 'jane<PERSON>', email: '<EMAIL>', role: 'Seller', createdAt: new Date(Date.now() - 86400000) },
          { id: 'c3d4e5f6', username: 'mikejohnson', email: '<EMAIL>', role: 'Buyer', createdAt: new Date(Date.now() - 172800000) },
          { id: 'd4e5f6g7', username: 'sarahwilliams', email: '<EMAIL>', role: 'Moderator', createdAt: new Date(Date.now() - 259200000) },
          { id: 'e5f6g7h8', username: 'davidbrown', email: '<EMAIL>', role: 'Buyer', createdAt: new Date(Date.now() - 345600000) }
        ]
      };
    }
  }

  // Get recent orders
  async getRecentOrders(limit = 5) {
    try {
      const response = await apiClient.get('/admin/dashboard/recent-orders', { params: { limit } });
      console.log('Recent orders API response:', response);
      return {
        data: response.data.data
      };
    } catch (error) {
      console.warn('Recent orders API not available, using mock data', error);
      // Return mock data if API fails
      return {
        data: [
          { id: 'ORD-1234', customerName: 'John Doe', total: 125.99, status: 'Completed', createdAt: new Date() },
          { id: 'ORD-1235', customerName: 'Jane Smith', total: 89.50, status: 'Processing', createdAt: new Date(Date.now() - 86400000) },
          { id: 'ORD-1236', customerName: 'Robert Johnson', total: 245.75, status: 'Pending', createdAt: new Date(Date.now() - 172800000) },
          { id: 'ORD-1237', customerName: 'Sarah Williams', total: 55.25, status: 'Cancelled', createdAt: new Date(Date.now() - 259200000) },
          { id: 'ORD-1238', customerName: 'David Brown', total: 175.00, status: 'Completed', createdAt: new Date(Date.now() - 345600000) }
        ]
      };
    }
  }

  // Get recent activities
  async getRecentActivities(limit = 10) {
    try {
      return await apiClient.get('/dashboard/recent-activities', { params: { limit } });
    } catch (error) {
      console.warn('Recent activities API not available, using mock data');
      // Return mock data if API fails
      return {
        data: [
          { date: new Date(2023, 10, 15, 14, 30), user: 'John Doe', action: 'Added new product' },
          { date: new Date(2023, 10, 15, 13, 45), user: 'Jane Smith', action: 'Updated category' },
          { date: new Date(2023, 10, 15, 12, 20), user: 'Mike Johnson', action: 'Processed order #1234' },
          { date: new Date(2023, 10, 15, 11, 10), user: 'Sarah Williams', action: 'Approved seller application' },
          { date: new Date(2023, 10, 15, 10, 5), user: 'David Brown', action: 'Added new user' }
        ]
      };
    }
  }

  // Get sales statistics
  async getSalesStats(period = 'month') {
    try {
      return await apiClient.get('/dashboard/sales-stats', { params: { period } });
    } catch (error) {
      console.warn('Sales stats API not available, using mock data');
      // Return mock data if API fails
      return {
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          datasets: [
            {
              label: 'Sales',
              data: [65, 59, 80, 81, 56, 55, 40, 45, 60, 70, 85, 90]
            }
          ]
        }
      };
    }
  }
}

export default new DashboardService();
