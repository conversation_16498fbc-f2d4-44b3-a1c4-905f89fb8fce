import{q as s}from"./index-L-hJxM_5.js";const d={async getReviews(t={}){var a,r;try{return(await s.get("/api/admin/reviews",{params:t})).data.data}catch(e){throw console.error("Error fetching reviews:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to load reviews")}},async getReviewById(t){var a,r;try{return(await s.get(`/api/admin/reviews/${t}`)).data.data}catch(e){throw console.error("Error fetching review:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to load review details")}},async updateReview(t,a){var r,e;try{const o={comment:a.comment};return(await s.put(`/api/admin/reviews/${t}`,o)).data}catch(o){throw console.error("Error updating review:",o),new Error(((e=(r=o.response)==null?void 0:r.data)==null?void 0:e.message)||"Failed to update review")}},async deleteReview(t){var a,r;try{return(await s.delete(`/api/admin/reviews/${t}`)).data}catch(e){throw console.error("Error deleting review:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to delete review")}},async bulkDeleteReviews(t){var a,r;try{const e={reviewIds:t};return(await s.post("/api/admin/reviews/bulk-delete",e)).data}catch(e){throw console.error("Error bulk deleting reviews:",e),new Error(((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||"Failed to delete reviews")}},async getReviewStats(){var t,a;try{return(await s.get("/api/admin/reviews/stats")).data.data}catch(r){throw console.error("Error fetching review stats:",r),new Error(((a=(t=r.response)==null?void 0:t.data)==null?void 0:a.message)||"Failed to load review statistics")}}};export{d as r};
