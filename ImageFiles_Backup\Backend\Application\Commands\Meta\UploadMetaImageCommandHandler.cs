﻿using Marketplace.Domain.Services;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Meta;

/// <summary>
/// Обробник команди для завантаження зображення для Meta
/// </summary>
public class UploadMetaImageCommandHandler : IRequestHandler<UploadMetaImageCommand, FileUploadResult>
{
    private readonly string _baseUrl;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<UploadMetaImageCommandHandler> _logger;

    public UploadMetaImageCommandHandler(
        IConfiguration configuration,
        ICategoryRepository categoryRepository,
        IProductRepository productRepository,
        ICompanyRepository companyRepository,
        IFileService fileService,
        ILogger<UploadMetaImageCommandHandler> logger)
    {
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
        _categoryRepository = categoryRepository;
        _productRepository = productRepository;
        _companyRepository = companyRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadMetaImageCommand request, CancellationToken cancellationToken)
    {
        // Завантажуємо файл
        var file = await _fileService.SaveFileAsync(
            request.File.OpenReadStream(),
            request.File.FileName,
            request.File.ContentType,
            $"meta/{request.EntityType.ToLower()}/{request.EntityId}",
            cancellationToken);

        var fileUrl = $"{_baseUrl}/uploads/{file}";
        // Оновлюємо Meta в залежності від типу сутності
        switch (request.EntityType.ToLower())
        {
            case "category":
                await UpdateCategoryMetaImage(request.EntityId, fileUrl, cancellationToken);
                break;
            case "product":
                await UpdateProductMetaImage(request.EntityId, fileUrl, cancellationToken);
                break;
            case "company":
                await UpdateCompanyMetaImage(request.EntityId, fileUrl, cancellationToken);
                break;
            default:
                throw new InvalidOperationException($"Невідомий тип сутності: {request.EntityType}");
        }

        // Повертаємо результат
        return new FileUploadResult
        {
            FileUrl = fileUrl,
            FileName = request.File.FileName,
            ContentType = request.File.ContentType,
            Size = request.File.Length
        };
    }

    private async Task UpdateCategoryMetaImage(Guid categoryId, string imageUrl, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Категорію з ID {categoryId} не знайдено.");

        // Створюємо нову Meta з оновленим зображенням
        var updatedMeta = new Domain.ValueObjects.Meta(
            category.Meta.Title,
            category.Meta.Description,
            new Url(imageUrl));

        // Оновлюємо Meta
        category.UpdateMeta(updatedMeta);

        // Зберігаємо зміни
        await _categoryRepository.UpdateAsync(category, cancellationToken);
    }

    private async Task UpdateProductMetaImage(Guid productId, string imageUrl, CancellationToken cancellationToken)
    {
        var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
        if (product == null)
            throw new InvalidOperationException($"Продукт з ID {productId} не знайдено.");

        // Створюємо нову Meta з оновленим зображенням
        var updatedMeta = new Domain.ValueObjects.Meta(
            product.Meta.Title,
            product.Meta.Description,
            new Url(imageUrl));

        // Оновлюємо Meta
        product.UpdateMeta(updatedMeta);

        // Зберігаємо зміни
        await _productRepository.UpdateAsync(product, cancellationToken);
    }

    private async Task UpdateCompanyMetaImage(Guid companyId, string imageUrl, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {companyId} не знайдено.");

        // Створюємо нову Meta з оновленим зображенням
        var updatedMeta = new Domain.ValueObjects.Meta(
            company.Meta.Title,
            company.Meta.Description,
            new Url(imageUrl));

        // Оновлюємо Meta
        company.UpdateMeta(updatedMeta);

        // Зберігаємо зміни
        await _companyRepository.UpdateAsync(company, cancellationToken);
    }
}
