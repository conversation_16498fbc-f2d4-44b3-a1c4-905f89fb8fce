﻿﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class AddSellerProductImageCommandHandler : IRequestHandler<AddSellerProductImageCommand, Guid>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyUserRepository _companyUserRepository;

    public AddSellerProductImageCommandHandler(
        IProductImageRepository productImageRepository,
        IProductRepository productRepository,
        ICompanyUserRepository companyUserRepository)
    {
        _productImageRepository = productImageRepository;
        _productRepository = productRepository;
        _companyUserRepository = companyUserRepository;
    }

    public async Task<Guid> Handle(AddSellerProductImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо компанії, до яких належить продавець
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.SellerId,
            cancellationToken: cancellationToken
        );

        if (!companyUsers.Any())
            throw new InvalidOperationException("Ви не є продавцем жодної компанії.");

        // Отримуємо ID компаній продавця
        var companyIds = companyUsers.Select(cu => cu.CompanyId).ToList();

        // Отримуємо продукт
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
            throw new InvalidOperationException($"Продукт з ID {request.ProductId} не знайдено.");

        // Перевіряємо, чи продукт належить компанії продавця
        if (!companyIds.Contains(product.CompanyId))
            throw new InvalidOperationException("Ви не маєте доступу до цього продукту.");

        // Створюємо нове зображення продукту
        var productImage = new Domain.Entities.ProductImage(
            request.ProductId,
            new Url(request.Image)
        );

        // Встановлюємо порядок
        productImage.Update(order: request.Order);

        await _productImageRepository.AddAsync(productImage, cancellationToken);
        return productImage.Id;
    }
}
