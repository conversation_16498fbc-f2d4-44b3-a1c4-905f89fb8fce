# Product Components

## Виправлені компоненти:

### 1. SimpleCompanySelector.vue
- **Виправлено**: Тепер правильно показує поточну компанію при редагуванні продукту
- **Функціональність**: Пошук з випадаючим списком, навігація клавіатурою
- **Watchers**: Додано watchers для синхронізації з modelValue та завантаженими даними

### 2. SimpleCategorySelector.vue  
- **Виправлено**: Тепер правильно показує поточну категорію при редагуванні продукту
- **Функціональність**: Пошук з випадаючим списком, навігація клавіатурою
- **Watchers**: Додано watchers для синхронізації з modelValue та завантаженими даними

### 3. SimpleProductAttributesEditor.vue
- **Повністю переписано**: Простий підхід без автоматичного пошуку
- **Функціональність**: 
  - Додавання атрибутів тільки після натискання кнопки "Add"
  - Підтримка різних форматів даних з бази (рядки, масиви, JSON)
  - Видалення окремих значень або цілих атрибутів
  - Переміщення атрибутів вгору/вниз
  - Автоматичне видалення атрибута при видаленні останнього значення

## Як працює:

### Селектори (Company/Category):
1. При завантаженні компонента завантажуються дані
2. При встановленні modelValue знаходиться відповідний елемент
3. Поле пошуку заповнюється назвою знайденого елемента
4. При введенні тексту фільтруються результати
5. При виборі елемента оновлюється modelValue

### Атрибути:
1. Користувач вводить назву атрибута та значення
2. Натискає кнопку "Add" 
3. Якщо атрибут існує - додається нове значення
4. Якщо атрибут новий - створюється новий атрибут
5. Значення відображаються як теги з можливістю видалення

## Тестування:
- `AttributesTest.vue` - тестування різних форматів атрибутів
- `SelectorTest.vue` - тестування селекторів

## Формати даних:

### Вхідні дані з бази:
```javascript
{
  "Колір": "к",           // Рядок -> ["к"]
  "Розміри": "S,M,L",     // CSV -> ["S", "M", "L"] 
  "Вага": 4               // Число -> ["4"]
}
```

### Внутрішній формат:
```javascript
{
  "Колір": ["к"],
  "Розміри": ["S", "M", "L"],
  "Вага": ["4"]
}
```

### Вихідні дані:
```javascript
{
  "Колір": ["к"],
  "Розміри": ["S", "M", "L"],
  "Вага": ["4"]
}
```
