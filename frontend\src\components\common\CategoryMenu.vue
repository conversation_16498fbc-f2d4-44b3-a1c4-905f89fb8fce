<template>
  <div class="category-menu-overlay" v-if="isOpen" @click="closeMenu">
    <div class="category-menu-container" @click.stop>
      <div class="category-menu-content">
        <!-- Left side - Main categories -->
        <div class="category-menu-sidebar">
          <ul class="category-list">
            <li 
              v-for="category in mainCategories" 
              :key="category.id" 
              class="category-item"
              :class="{ 'active': selectedCategory === category }"
              @mouseenter="selectCategory(category)"
            >
              <div class="category-icon">
                <i :class="getCategoryIcon(category)"></i>
              </div>
              <a :href="`/catalog/${category.slug} `" class="category-label">{{ category.name }}
                <i class="fas fa-chevron-right category-arrow"></i>
              </a>
            </li>
          </ul>
        </div>

        <!-- Right side - Subcategories -->
        <div class="category-menu-subcategories">
          <button class="close-button" @click="closeMenu">
            <i class="fas fa-times"></i>
          </button>

          <div v-if="selectedCategory" class="subcategories-container">
            <div v-for="(group, groupName) in groupedSubcategories" :key="groupName" class="subcategory-group">
              <h3 class="subcategory-group-title">{{ groupName }}</h3>
              <ul class="subcategory-list">
                <li v-for="subcategory in group" :key="subcategory.id" class="subcategory-item">
                  <a :href="`/catalog/${subcategory.slug}`" class="subcategory-link">
                    {{ subcategory.name }}
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import categoryService from '@/services/category.service';
import mockCategories from '@/mock/categories';

export default {
  name: 'CategoryMenu',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const categories = ref([]);
    const selectedCategory = ref(null);
    const isLoading = ref(false);
    const error = ref(null);

    // Fetch all categories
    const fetchCategories = async () => {
      isLoading.value = true;
      error.value = null;
      
      try 
      {
        const params = { pageSize: 152};
        const response = await categoryService.getAll(params);
        categories.value = response.data.data || [];
        // console.log("category menu");
        // console.log(response);
        
        // Select the first category by default if available
        if (categories.value.length > 0 && !selectedCategory.value) {
          selectedCategory.value = categories.value[0];
        }
      } catch (err) {
        // Use mock data as fallback if API call fails
        console.warn('Using mock category data as fallback:', err);
        categories.value = mockCategories;
        
        // Select the first category by default
        if (categories.value.length > 0 && !selectedCategory.value) {
          selectedCategory.value = categories.value[0];
        }
        
        error.value = null; // Clear error since we're using fallback data
      } finally {
        isLoading.value = false;
      }
    };

    // Get only main/parent categories
    const mainCategories = computed(() => {
      return categories.value.filter(category => !category.parentId);
    });

    // Get subcategories for the selected category
    const subcategories = computed(() => {
      if (!selectedCategory.value) return [];
      
      return categories.value.filter(category => 
        category.parentId === selectedCategory.value.id
      );
    });

    // Group subcategories by type or other criteria
    const groupedSubcategories = computed(() => {
      if (!selectedCategory.value) return {};
      
      const groups = {};
      
      // Get direct children of the selected category
      const directChildren = categories.value.filter(category => 
        category.parentId === selectedCategory.value.id
      );
      
      // For each direct child, find its children (grandchildren of selected category)
      directChildren.forEach(child => {
        const childItems = categories.value.filter(category => 
          category.parentId === child.id
        );
        
        // If the child has children, create a group with those children
        if (childItems.length > 0) {
          groups[child.name] = childItems;
        }
      });
      
      return groups;
    });

    // Select a category
    const selectCategory = (category) => {
      selectedCategory.value = category;
    };

    // Close the menu
    const closeMenu = () => {
      emit('close');
    };

    // Get icon class for a category
    const getCategoryIcon = (category) => {
      // Map category names to Font Awesome icons
      const iconMap = {
        'Ноутбуки та комп\'ютери': 'fas fa-laptop',
        'Смартфони, ТВ і електроніка': 'fas fa-mobile-alt',
        'Побутова техніка': 'fas fa-blender',
        'Товари для дому': 'fas fa-home',
        'Дача, сад і город': 'fas fa-seedling',
        'Спорт і хобі': 'fas fa-running',
        'Спорт': 'fas fa-running',
        'Краса та здоров\'я': 'fas fa-heartbeat',
        'Одяг, взуття та прикраси': 'fas fa-tshirt',
        'Одяг': 'fas fa-tshirt'
      };
      
      return iconMap[category.name] || 'fas fa-folder';
    };

    // Fetch categories when component is mounted
    onMounted(fetchCategories);

    return {
      categories,
      mainCategories,
      selectedCategory,
      subcategories,
      groupedSubcategories,
      isLoading,
      error,
      selectCategory,
      closeMenu,
      getCategoryIcon
    };
  }
};
</script>

<style scoped>

.category-item.active .category-arrow {
  display: none;
}

.category-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.category-menu-container {
  background-color: white;
  width: 100%;
  max-width: 1200px;
  margin-top: 80px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.category-menu-content {
  display: flex;
  height: 450px;
}

.category-menu-sidebar {
  width: 300px;
  overflow-y: auto;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 15px 0 15px 15px;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 6px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.category-item:hover,
.category-item.active {
  font-size: 20px;
}

.category-icon {
  width: 24px;
  margin-right: 12px;
  color: #555;
}

.category-label {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.category-arrow {
  font-size: 12px;
  color: #999;
}

.category-menu-subcategories {
  flex: 1;
  padding: 20px 30px;
  overflow-y: auto;
  position: relative;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  z-index: 10;
}

.close-button:hover {
  color: #333;
}

.subcategories-container {
  display: flex;
  flex-wrap: wrap;
}

.subcategory-group {
  width: 33.33%;
  padding: 0 15px 20px 0;
}

.subcategory-group-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

.subcategory-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.subcategory-item {
  margin-bottom: 3px;
}

.subcategory-link {
  color: #555;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.subcategory-link:hover {
  color: #ff7700;
}

@media (max-width: 992px) {
  .subcategory-group {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .category-menu-content {
    flex-direction: column;
    height: auto;
    max-height: 80vh;
  }

  .category-menu-sidebar {
    width: 100%;
    max-height: 300px;
  }

  .subcategory-group {
    width: 100%;
  }
}
</style>
