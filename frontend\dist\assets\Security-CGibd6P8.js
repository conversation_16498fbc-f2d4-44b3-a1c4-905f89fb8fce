import{q as w,_ as j,g,h as H,i as K,c as a,a as s,k as f,n as m,x as k,y as $,F as L,p as S,t as n,D as P,d as U,W as T,o}from"./index-L-hJxM_5.js";const x={async getLogs(u={}){var i,p;try{return(await w.get("/api/admin/logs",{params:u})).data}catch(c){throw console.error("Error fetching logs:",c),new Error(((p=(i=c.response)==null?void 0:i.data)==null?void 0:p.message)||"Failed to fetch logs")}},async getLogById(u){var i,p;try{return(await w.get(`/api/admin/logs/${u}`)).data}catch(c){throw console.error("Error fetching log:",c),new Error(((p=(i=c.response)==null?void 0:i.data)==null?void 0:p.message)||"Failed to fetch log")}},async getLogLevels(){try{return(await w.get("/api/admin/logs/levels")).data}catch(u){return console.error("Error fetching log levels:",u),{data:["Info","Warning","Error","Critical"]}}},async getLogCategories(){try{return(await w.get("/api/admin/logs/categories")).data}catch(u){return console.error("Error fetching log categories:",u),{data:["User","Product","Order","Payment","Authentication","Authorization","System","Database","Api","Security"]}}},async getLogStats(u={}){try{return(await w.get("/api/admin/logs/stats",{params:u})).data}catch(i){return console.error("Error fetching log stats:",i),{data:{totalLogs:0,logsByLevel:{},logsByCategory:{},logsByDay:{},dateRange:{from:new Date(Date.now()-7*24*60*60*1e3).toISOString(),to:new Date().toISOString()}}}}}},Q={class:"admin-security"},X={class:"level"},Y={class:"level-right"},Z={class:"level-item"},ss={class:"tabs is-boxed"},es={key:0,class:"security-content"},ts={class:"card"},ls={class:"card-content"},as={class:"field is-grouped is-grouped-multiline"},os={class:"control"},ns={class:"select"},is=["value"],rs={class:"control"},ds={class:"select"},cs=["value"],us={class:"control"},vs={class:"control"},gs={key:0,class:"has-text-centered"},ps={key:1,class:"table-container"},ys={class:"table is-fullwidth is-striped is-hoverable"},fs={class:"log-message"},ms=["onClick"],_s={key:0,class:"pagination is-centered"},bs=["disabled"],hs=["disabled"],ks={class:"pagination-list"},Ls=["onClick"],Ss={key:1,class:"security-content"},Cs={class:"columns"},ws={class:"column is-6"},xs={class:"card"},As={class:"card-content"},Ds={key:0,class:"has-text-centered"},Es={key:1},Ps={class:"field"},Rs={class:"title is-4"},Ns={class:"field"},Is={class:"subtitle is-6"},$s={class:"column is-6"},Us={class:"card"},Ts={class:"card-content"},Vs={key:0,class:"has-text-centered"},Bs={key:1},Ms={class:"level"},Fs={class:"level-left"},Os={class:"level-item"},zs={class:"level-right"},Ws={class:"level-item"},Js={class:"title is-5"},qs={class:"columns"},Gs={class:"column is-12"},js={class:"card"},Hs={class:"card-content"},Ks={key:0,class:"has-text-centered"},Qs={key:1,class:"columns is-multiline"},Xs={class:"box has-text-centered"},Ys={class:"title is-4"},Zs={class:"subtitle is-6"},se={key:2,class:"security-content"},ee={class:"card"},te={class:"card-content"},le={class:"field"},ae={class:"control"},oe={class:"field"},ne={class:"control"},ie={class:"checkbox"},re={class:"field"},de={class:"control"},ce={class:"checkbox"},ue={class:"field"},ve={class:"control"},ge={class:"modal-card"},pe={class:"modal-card-head"},ye={key:0,class:"modal-card-body"},fe={class:"field"},me={class:"field"},_e={class:"field"},be={class:"field"},he={key:0,class:"field"},ke={key:1,class:"field"},Le={key:2,class:"field"},Se={key:3,class:"field"},Ce={class:"is-size-7"},we={key:4,class:"field"},xe={class:"has-background-light p-3"},Ae={class:"modal-card-foot"},De={__name:"Security",setup(u){const i=g("logs"),p=g(!1),c=g(!1),A=g(!1),d=g({data:[],totalCount:0,page:1,pageSize:20,totalPages:1}),b=g({}),R=g([]),N=g([]),r=g(null),C=g(!1),h=g({logRetentionDays:30,enableAuditLogging:!0,enableSecurityAlerts:!0}),y=g({level:"",category:"",from:"",to:""}),V=H(()=>{const l=[],e=Math.max(1,d.value.page-2),t=Math.min(d.value.totalPages,d.value.page+2);for(let v=e;v<=t;v++)l.push(v);return l}),_=async()=>{p.value=!0;try{const l={page:d.value.page,pageSize:d.value.pageSize,...y.value},e=await x.getLogs(l);d.value=e.data}catch(l){console.error("Error loading logs:",l)}finally{p.value=!1}},B=async()=>{c.value=!0;try{const l=await x.getLogStats();b.value=l.data}catch(l){console.error("Error loading stats:",l)}finally{c.value=!1}},M=async()=>{try{const l=await x.getLogLevels();R.value=l.data}catch(l){console.error("Error loading log levels:",l)}},F=async()=>{try{const l=await x.getLogCategories();N.value=l.data}catch(l){console.error("Error loading log categories:",l)}},O=async()=>{await Promise.all([_(),i.value==="stats"?B():Promise.resolve()])},D=l=>{d.value.page=l,_()},z=()=>{y.value={level:"",category:"",from:"",to:""},d.value.page=1,_()},W=l=>{r.value=l,C.value=!0},J=async()=>{A.value=!0;try{console.log("Saving security settings:",h.value),alert("Security settings saved successfully!")}catch(l){console.error("Error saving security settings:",l),alert("Error saving security settings. Please try again.")}finally{A.value=!1}},I=l=>new Date(l).toLocaleString(),q=()=>{if(!b.value.dateRange)return"N/A";const l=new Date(b.value.dateRange.from).toLocaleDateString(),e=new Date(b.value.dateRange.to).toLocaleDateString();return`${l} - ${e}`},G=l=>{try{return JSON.stringify(JSON.parse(l),null,2)}catch{return l}},E=l=>{switch(l==null?void 0:l.toUpperCase()){case"CRITICAL":return"is-danger";case"ERROR":return"is-danger";case"WARNING":return"is-warning";case"INFO":return"is-info";default:return"is-light"}};return K(async()=>{await Promise.all([M(),F(),_()])}),(l,e)=>(o(),a("div",Q,[s("div",X,[e[16]||(e[16]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Security & Logs")])],-1)),s("div",Y,[s("div",Z,[s("button",{class:m(["button is-primary",{"is-loading":p.value}]),onClick:O},e[15]||(e[15]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Refresh",-1)]),2)])])]),s("div",ss,[s("ul",null,[s("li",{class:m({"is-active":i.value==="logs"})},[s("a",{onClick:e[0]||(e[0]=t=>i.value="logs")},e[17]||(e[17]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-list"})],-1),s("span",null,"System Logs",-1)]))],2),s("li",{class:m({"is-active":i.value==="stats"})},[s("a",{onClick:e[1]||(e[1]=t=>i.value="stats")},e[18]||(e[18]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-chart-bar"})],-1),s("span",null,"Statistics",-1)]))],2),s("li",{class:m({"is-active":i.value==="settings"})},[s("a",{onClick:e[2]||(e[2]=t=>i.value="settings")},e[19]||(e[19]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-shield-alt"})],-1),s("span",null,"Security Settings",-1)]))],2)])]),i.value==="logs"?(o(),a("div",es,[s("div",ts,[e[26]||(e[26]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"System Logs")],-1)),s("div",ls,[s("div",as,[s("div",os,[s("div",ns,[k(s("select",{"onUpdate:modelValue":e[3]||(e[3]=t=>y.value.level=t),onChange:_},[e[20]||(e[20]=s("option",{value:""},"All Levels",-1)),(o(!0),a(L,null,S(R.value,t=>(o(),a("option",{key:t,value:t},n(t),9,is))),128))],544),[[$,y.value.level]])])]),s("div",rs,[s("div",ds,[k(s("select",{"onUpdate:modelValue":e[4]||(e[4]=t=>y.value.category=t),onChange:_},[e[21]||(e[21]=s("option",{value:""},"All Categories",-1)),(o(!0),a(L,null,S(N.value,t=>(o(),a("option",{key:t,value:t},n(t),9,cs))),128))],544),[[$,y.value.category]])])]),s("div",us,[k(s("input",{class:"input",type:"date","onUpdate:modelValue":e[5]||(e[5]=t=>y.value.from=t),onChange:_,placeholder:"From date"},null,544),[[P,y.value.from]])]),s("div",vs,[k(s("input",{class:"input",type:"date","onUpdate:modelValue":e[6]||(e[6]=t=>y.value.to=t),onChange:_,placeholder:"To date"},null,544),[[P,y.value.to]])]),s("div",{class:"control"},[s("button",{class:"button is-info",onClick:z},e[22]||(e[22]=[s("span",{class:"icon"},[s("i",{class:"fas fa-times"})],-1),s("span",null,"Clear",-1)]))])]),p.value?(o(),a("div",gs,e[23]||(e[23]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading logs...",-1)]))):(o(),a("div",ps,[s("table",ys,[e[25]||(e[25]=s("thead",null,[s("tr",null,[s("th",null,"Timestamp"),s("th",null,"Level"),s("th",null,"Category"),s("th",null,"Message"),s("th",null,"User"),s("th",null,"IP Address"),s("th",null,"Actions")])],-1)),s("tbody",null,[(o(!0),a(L,null,S(d.value.data,t=>(o(),a("tr",{key:t.id},[s("td",null,n(I(t.timestamp)),1),s("td",null,[s("span",{class:m(["tag",E(t.level)])},n(t.level),3)]),s("td",null,n(t.category),1),s("td",fs,n(t.message),1),s("td",null,n(t.userName||"System"),1),s("td",null,n(t.ipAddress||"N/A"),1),s("td",null,[s("button",{class:"button is-small is-info",onClick:v=>W(t)},e[24]||(e[24]=[s("span",{class:"icon"},[s("i",{class:"fas fa-eye"})],-1)]),8,ms)])]))),128))])]),d.value.totalPages>1?(o(),a("nav",_s,[s("button",{class:"pagination-previous",onClick:e[7]||(e[7]=t=>D(d.value.page-1)),disabled:d.value.page<=1}," Previous ",8,bs),s("button",{class:"pagination-next",onClick:e[8]||(e[8]=t=>D(d.value.page+1)),disabled:d.value.page>=d.value.totalPages}," Next ",8,hs),s("ul",ks,[(o(!0),a(L,null,S(V.value,t=>(o(),a("li",{key:t},[s("button",{class:m(["pagination-link",{"is-current":t===d.value.page}]),onClick:v=>D(t)},n(t),11,Ls)]))),128))])])):f("",!0)]))])])])):f("",!0),i.value==="stats"?(o(),a("div",Ss,[s("div",Cs,[s("div",ws,[s("div",xs,[e[30]||(e[30]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Log Statistics")],-1)),s("div",As,[c.value?(o(),a("div",Ds,e[27]||(e[27]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",null,"Loading statistics...",-1)]))):(o(),a("div",Es,[s("div",Ps,[e[28]||(e[28]=s("label",{class:"label"},"Total Logs",-1)),s("p",Rs,n(b.value.totalLogs||0),1)]),s("div",Ns,[e[29]||(e[29]=s("label",{class:"label"},"Date Range",-1)),s("p",Is,n(q()),1)])]))])])]),s("div",$s,[s("div",Us,[e[32]||(e[32]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Logs by Level")],-1)),s("div",Ts,[c.value?(o(),a("div",Vs,e[31]||(e[31]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1)]))):(o(),a("div",Bs,[(o(!0),a(L,null,S(b.value.logsByLevel,(t,v)=>(o(),a("div",{key:v,class:"field"},[s("div",Ms,[s("div",Fs,[s("div",Os,[s("span",{class:m(["tag",E(v)])},n(v),3)])]),s("div",zs,[s("div",Ws,[s("span",Js,n(t),1)])])])]))),128))]))])])])]),s("div",qs,[s("div",Gs,[s("div",js,[e[34]||(e[34]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Logs by Category")],-1)),s("div",Hs,[c.value?(o(),a("div",Ks,e[33]||(e[33]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1)]))):(o(),a("div",Qs,[(o(!0),a(L,null,S(b.value.logsByCategory,(t,v)=>(o(),a("div",{key:v,class:"column is-3"},[s("div",Xs,[s("p",Ys,n(t),1),s("p",Zs,n(v),1)])]))),128))]))])])])])])):f("",!0),i.value==="settings"?(o(),a("div",se,[s("div",ee,[e[40]||(e[40]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Security Settings")],-1)),s("div",te,[s("div",le,[e[35]||(e[35]=s("label",{class:"label"},"Log Retention Period (days)",-1)),s("div",ae,[k(s("input",{class:"input",type:"number","onUpdate:modelValue":e[9]||(e[9]=t=>h.value.logRetentionDays=t),min:"1",max:"365"},null,512),[[P,h.value.logRetentionDays]])]),e[36]||(e[36]=s("p",{class:"help"},"Logs older than this will be automatically deleted",-1))]),s("div",oe,[s("div",ne,[s("label",ie,[k(s("input",{type:"checkbox","onUpdate:modelValue":e[10]||(e[10]=t=>h.value.enableAuditLogging=t)},null,512),[[T,h.value.enableAuditLogging]]),e[37]||(e[37]=U(" Enable detailed audit logging "))])])]),s("div",re,[s("div",de,[s("label",ce,[k(s("input",{type:"checkbox","onUpdate:modelValue":e[11]||(e[11]=t=>h.value.enableSecurityAlerts=t)},null,512),[[T,h.value.enableSecurityAlerts]]),e[38]||(e[38]=U(" Enable security alerts "))])])]),s("div",ue,[s("div",ve,[s("button",{class:m(["button is-primary",{"is-loading":A.value}]),onClick:J},e[39]||(e[39]=[s("span",{class:"icon"},[s("i",{class:"fas fa-save"})],-1),s("span",null,"Save Settings",-1)]),2)])])])])])):f("",!0),s("div",{class:m(["modal",{"is-active":C.value}])},[s("div",{class:"modal-background",onClick:e[12]||(e[12]=t=>C.value=!1)}),s("div",ge,[s("header",pe,[e[41]||(e[41]=s("p",{class:"modal-card-title"},"Log Details",-1)),s("button",{class:"delete",onClick:e[13]||(e[13]=t=>C.value=!1)})]),r.value?(o(),a("section",ye,[s("div",fe,[e[42]||(e[42]=s("label",{class:"label"},"Timestamp",-1)),s("p",null,n(I(r.value.timestamp)),1)]),s("div",me,[e[43]||(e[43]=s("label",{class:"label"},"Level",-1)),s("span",{class:m(["tag",E(r.value.level)])},n(r.value.level),3)]),s("div",_e,[e[44]||(e[44]=s("label",{class:"label"},"Category",-1)),s("p",null,n(r.value.category),1)]),s("div",be,[e[45]||(e[45]=s("label",{class:"label"},"Message",-1)),s("p",null,n(r.value.message),1)]),r.value.userName?(o(),a("div",he,[e[46]||(e[46]=s("label",{class:"label"},"User",-1)),s("p",null,n(r.value.userName)+" ("+n(r.value.userEmail)+")",1)])):f("",!0),r.value.entityId?(o(),a("div",ke,[e[47]||(e[47]=s("label",{class:"label"},"Entity",-1)),s("p",null,n(r.value.entityType)+": "+n(r.value.entityId),1)])):f("",!0),r.value.ipAddress?(o(),a("div",Le,[e[48]||(e[48]=s("label",{class:"label"},"IP Address",-1)),s("p",null,n(r.value.ipAddress),1)])):f("",!0),r.value.userAgent?(o(),a("div",Se,[e[49]||(e[49]=s("label",{class:"label"},"User Agent",-1)),s("p",Ce,n(r.value.userAgent),1)])):f("",!0),r.value.additionalData?(o(),a("div",we,[e[50]||(e[50]=s("label",{class:"label"},"Additional Data",-1)),s("pre",xe,n(G(r.value.additionalData)),1)])):f("",!0)])):f("",!0),s("footer",Ae,[s("button",{class:"button",onClick:e[14]||(e[14]=t=>C.value=!1)},"Close")])])],2)]))}},Pe=j(De,[["__scopeId","data-v-58767715"]]);export{Pe as default};
