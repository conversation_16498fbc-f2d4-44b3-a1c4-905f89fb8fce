﻿using Bogus;
using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Persistence;
using Marketplace.Infrastructure.Services.Auth;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace Marketplace.Infrastructure.DatabaseSeeder;

public class DatabaseSeeder
{
    private readonly MarketplaceDbContext _context;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ILogger<DatabaseSeeder> _logger;
    private readonly Random _random = new Random();

    public DatabaseSeeder(MarketplaceDbContext context, IPasswordHasher passwordHasher, ILogger<DatabaseSeeder> logger)
    {
        _context = context;
        _passwordHasher = passwordHasher;
        _logger = logger;
    }

    public void Seed(int count = 30, int productCount = 300)
    {
        // Глобальна перевірка: якщо база вже заповнена, не виконуємо seeding
        if (IsDatabaseAlreadySeeded())
        {
            Console.WriteLine("Database is already seeded. Skipping seeding process.");
            return;
        }

        Console.WriteLine("Starting database seeding process...");

        // 1. UserSeeder - Create admin user and regular users
        var adminUser = CreateAdminUserIfNotExists();
        var moderatorUser = CreateModeratorUserIfNotExists();
        var users = CreateUsersIfNotExist(500); // Збільшуємо до 500 користувачів

        // Додаємо адміністратора та модератора до списку користувачів
        var allUsers = new List<User>(users);
        if (adminUser != null)
        {
            allUsers.Add(adminUser);
        }
        if (moderatorUser != null)
        {
            allUsers.Add(moderatorUser);
        }

        // 2. SellerRequestSeeder - Create seller requests for buyer users (not seller owners)
        CreateSellerRequestsIfNotExist(allUsers, count);

        // 3. CompanySeeder - Create companies only for approved seller requests
        var companies = CreateCompaniesFromApprovedRequests(allUsers, count);

        // 4. CompanyUserSeeder - Create company-user relationships
        CreateCompanyUsersIfNotExist(companies, allUsers);

        // 5. CompanyFinanceSeeder - Create financial data for companies
        CreateCompanyFinancesIfNotExist(companies);

        // 6. CompanyScheduleSeeder - Create schedules for companies
        CreateCompanySchedulesIfNotExist(companies);

        // 7. CategorySeeder - Create categories with 3-level hierarchy (8 + 24 + 32 = 64 categories)
        var categories = CreateCategoriesIfNotExist();

        // 8. ProductSeeder - Create products distributed across categories
        var products = CreateProductsIfNotExist(companies, categories, allUsers, productCount);

        // 9. AddressSeeder - Create addresses for users
        var addresses = CreateAddressesIfNotExist(allUsers, count * 2); // Більше адрес

        // 10. ShippingMethodSeeder - Create standard shipping methods
        var shippingMethods = CreateShippingMethodsIfNotExist();

        // 11. OrderSeeder - Create orders for buyer users (збільшуємо до ~1000)
        // Замовлення використовують існуючі стандартні методи доставки
        var orders = CreateOrdersIfNotExist(allUsers, addresses, shippingMethods, Math.Max(1000, count * 7));

        // 12. OrderItemSeeder - Create order items for ALL orders
        CreateOrderItemsForAllOrders(orders, products);

        // 12.1. Update order total prices based on order items (only for new orders)
        UpdateOrderTotalPricesForNewOrders(orders);

        // 13. PaymentSeeder - Create payments for orders
        CreatePaymentsIfNotExist(orders);

        // 14. CartSeeder - Create carts for all users
        var carts = CreateCartsIfNotExist(allUsers, count * 2);
        CreateCartItemsIfNotExist(carts, products, count * 6); // Більше товарів у кошиках

        // 15. WishlistSeeder - Create wishlists for all users
        var wishlists = CreateWishlistsIfNotExist(allUsers, count * 2);
        CreateWishlistItemsIfNotExist(wishlists, products, count * 8); // Більше товарів у списках бажань

        // 16. RatingSeeder - Create ratings for ordered products
        var ratings = CreateRatingsIfNotExist(products, allUsers, count * 5); // Більше рейтингів

        // 17. ReviewSeeder - Create reviews for some ratings
        CreateReviewsIfNotExist(products, allUsers, ratings, count * 3); // Більше відгуків

        // Additional entities (not in main plan but needed)
        var coupons = CreateCouponsIfNotExist(count * 2); // Більше купонів
        CreateOrderCouponsIfNotExist(orders, coupons);
        var chats = CreateChatsIfNotExist(allUsers, companies, count * 2); // Більше чатів
        CreateMessagesIfNotExist(chats, allUsers, count * 10); // Більше повідомлень
        CreateNotificationsIfNotExist(allUsers, count * 5); // Більше сповіщень
        CreateFavoritesIfNotExist(allUsers, products, count * 4); // Більше вподобань

        Console.WriteLine("Database seeding completed successfully.");
    }

    /// <summary>
    /// Перевіряє чи база даних вже заповнена даними
    /// </summary>
    /// <returns>true якщо база вже заповнена, false якщо потрібно заповнити</returns>
    private bool IsDatabaseAlreadySeeded()
    {
        try
        {
            // Перевіряємо наявність адміністратора як індикатор того що база була заповнена
            var hasAdmin = _context.Users.Any(u => u.Role == Role.Admin);

            if (hasAdmin)
            {
                // Якщо є адмін, показуємо статистику та пропускаємо seeding
                var userCount = _context.Users.Count();
                var categoryCount = _context.Categories.Count();
                var productCount = _context.Products.Count();
                var orderCount = _context.Orders.Count();
                var companyCount = _context.Companies.Count();
                var sellerRequestCount = _context.SellerRequests.Count();

                Console.WriteLine($"Database already contains seeded data:");
                Console.WriteLine($"- Users: {userCount}");
                Console.WriteLine($"- Categories: {categoryCount}");
                Console.WriteLine($"- Products: {productCount}");
                Console.WriteLine($"- Orders: {orderCount}");
                Console.WriteLine($"- Companies: {companyCount}");
                Console.WriteLine($"- Seller Requests: {sellerRequestCount}");
                Console.WriteLine("Skipping seeding to preserve existing data integrity.");

                return true;
            }

            // Якщо немає адміна, база точно порожня і потрібно заповнити
            Console.WriteLine("Database is empty. Starting full seeding process...");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if database is already seeded");
            // У випадку помилки, краще не ризикувати і не заповнювати
            Console.WriteLine("Error checking database state. Skipping seeding to be safe.");
            return true;
        }
    }

    private User? CreateAdminUserIfNotExists()
    {
        // Check if admin user exists
        var adminUser = _context.Users.FirstOrDefault(u => u.Role == Role.Admin);

        if (adminUser == null)
        {
            // Hash the password
            string hashedPassword = _passwordHasher.HashPassword("Admin123!");

            // Create default admin user
            adminUser = new User(
                "admin",
                new Email("<EMAIL>"),
                new Password(hashedPassword),
                Role.Admin
            );

            // Встановлюємо додаткові властивості
            adminUser.Update(
                null, null, null, null, null, null, null, null, null, null, null,
                DateTime.UtcNow, null, null, DateTime.UtcNow
            );
            adminUser.Approve(adminUser.Id);

            _context.Users.Add(adminUser);
            _context.SaveChanges();

            Console.WriteLine("Default admin user created:");
            Console.WriteLine($"Username: {adminUser.Username}");
            Console.WriteLine($"Password: Admin123!");
            Console.WriteLine($"Email: {adminUser.Email}");

            return adminUser;
        }
        else
        {
            Console.WriteLine("Admin user already exists.");
            return adminUser;
        }
    }

    private User? CreateModeratorUserIfNotExists()
    {
        // Check if moderator user exists - спочатку отримуємо всіх модераторів, потім фільтруємо на клієнті
        var moderatorUser = _context.Users
            .Where(u => u.Role == Role.Moderator)
            .AsEnumerable()
            .FirstOrDefault(u => u.Email.Value == "<EMAIL>");

        if (moderatorUser == null)
        {
            // Hash the password
            string hashedPassword = _passwordHasher.HashPassword("Moder123!");

            // Create default moderator user
            moderatorUser = new User(
                "moderator",
                new Email("<EMAIL>"),
                new Password(hashedPassword),
                Role.Moderator
            );

            // Встановлюємо додаткові властивості
            moderatorUser.Update(
                null, null, null, null, null, null, null, null, null, null, null,
                DateTime.UtcNow, null, null, DateTime.UtcNow
            );
            moderatorUser.Approve(moderatorUser.Id);

            _context.Users.Add(moderatorUser);
            _context.SaveChanges();

            Console.WriteLine("Default moderator user created:");
            Console.WriteLine($"Username: {moderatorUser.Username}");
            Console.WriteLine($"Password: Moder123!");
            Console.WriteLine($"Email: {moderatorUser.Email}");

            return moderatorUser;
        }
        else
        {
            Console.WriteLine("Moderator user already exists.");
            return moderatorUser;
        }
    }

    private List<User> CreateUsersIfNotExist(int count)
    {
        // Перевіряємо, чи є вже користувачі (не адміністратори та не модератори)
        var existingUserCount = _context.Users.Count(u => u.Role != Role.Admin && u.Role != Role.Moderator);

        if (existingUserCount >= count)
        {
            Console.WriteLine($"Already have {existingUserCount} regular users.");
            return _context.Users.Where(u => u.Role != Role.Admin && u.Role != Role.Moderator).ToList();
        }

        // Створюємо додаткових користувачів, якщо потрібно
        var usersToCreate = count - existingUserCount;
        Console.WriteLine($"Creating {usersToCreate} additional regular users.");

        // Hash the password once for all users
        string hashedPassword = _passwordHasher.HashPassword("Password123!");

        // Розподіл ролей: 20% Seller, 80% Buyer (30 з Buyer стануть SellerOwner після схвалення заявки)
        var sellersCount = (int)(usersToCreate * 0.20); // 20% Seller
        var buyersCount = usersToCreate - sellersCount; // 80% Buyer (включаючи майбутніх SellerOwner)

        var newUsers = new List<User>();

        // Створюємо Seller користувачів
        var sellerFaker = new Faker<User>()
            .CustomInstantiator(f => new User(
                f.Internet.UserName(),
                new Email(f.Internet.Email()),
                new Password(hashedPassword),
                Role.Seller
            ))
            .RuleFor(u => u.Id, f => Guid.NewGuid())
            .RuleFor(u => u.EmailConfirmed, f => true)
            .RuleFor(u => u.EmailConfirmedAt, f => DateTime.UtcNow)
            .RuleFor(u => u.LastSeenAt, f => DateTime.UtcNow);

        var sellers = sellerFaker.Generate(sellersCount);
        newUsers.AddRange(sellers);

        // Створюємо Buyer користувачів
        var buyerFaker = new Faker<User>()
            .CustomInstantiator(f => new User(
                f.Internet.UserName(),
                new Email(f.Internet.Email()),
                new Password(hashedPassword),
                Role.Buyer
            ))
            .RuleFor(u => u.Id, f => Guid.NewGuid())
            .RuleFor(u => u.EmailConfirmed, f => true)
            .RuleFor(u => u.EmailConfirmedAt, f => DateTime.UtcNow)
            .RuleFor(u => u.LastSeenAt, f => DateTime.UtcNow);

        var buyers = buyerFaker.Generate(buyersCount);
        newUsers.AddRange(buyers);

        // Затверджуємо всіх користувачів
        var adminUser = _context.Users.FirstOrDefault(u => u.Role == Role.Admin);
        if (adminUser != null)
        {
            foreach (var user in newUsers)
            {
                user.Approve(adminUser.Id);
            }
        }

        // Додаємо нових користувачів до бази даних
        _context.Users.AddRange(newUsers);
        _context.SaveChanges();

        Console.WriteLine($"Created {sellersCount} sellers and {buyersCount} buyers (30 buyers will become seller owners after request approval).");

        // Повертаємо всіх користувачів (крім адміністратора та модератора)
        return _context.Users.Where(u => u.Role != Role.Admin && u.Role != Role.Moderator).ToList();
    }

    private List<Address> CreateAddressesIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже адреси
        var existingCount = _context.Addresses.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} addresses.");
            return _context.Addresses.ToList();
        }

        // Створюємо додаткові адреси, якщо потрібно
        var addressesToCreate = count - existingCount;
        Console.WriteLine($"Creating {addressesToCreate} additional addresses.");

        // Налаштування генератора фейкових даних для Address
        var addressFaker = new Faker<Address>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача для прив'язки адреси
                var randomUser = f.PickRandom(users);

                return new Address(
                    new AddressVO(
                        region: f.Address.State(),           // Регіон
                        city: f.Address.City(),              // Місто
                        street: f.Address.StreetName(),      // Вулиця
                        postalCode: f.Address.ZipCode("#####")      // Поштовий код
                    ),
                    userId: randomUser.Id
                );
            })
            .RuleFor(a => a.Id, f => Guid.NewGuid()); // Унікальний ID

        // Генерація даних
        var newAddresses = addressFaker.Generate(addressesToCreate);

        // Додавання до бази
        _context.Addresses.AddRange(newAddresses);
        _context.SaveChanges();

        Console.WriteLine($"Created {addressesToCreate} addresses.");

        // Повертаємо всі адреси
        return _context.Addresses.ToList();
    }

    private List<Category> CreateCategoriesIfNotExist()
    {
        // Перевірка, чи є вже категорії
        var existingCount = _context.Categories.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} categories.");
            return _context.Categories.ToList();
        }

        Console.WriteLine("Creating category hierarchy with 8 top-level categories...");

        // Точна структура категорій згідно з планом
        var categoryNames = new Dictionary<string, Dictionary<string, List<string>>>
        {
            {
                "Ноутбуки та комп'ютери", new Dictionary<string, List<string>>
                {
                    { "Ноутбуки",
                        new List<string> {
                            "Геймерські ноутбуки",
                            "Бізнес-ноутбуки" } },
                    { "Стаціонарні комп'ютери",
                        new List<string> {
                            "Системні блоки",
                            "Монітори" } },
                    { "Комплектуючі",
                        new List<string> {
                            "Материнські плати",
                            "Процесори" } }
                }
            },
            {
                "Смартфони, ТВ і електроніка", new Dictionary<string, List<string>>
                {
                    { "Смартфони",
                        new List<string> {
                            "Флагманські смартфони",
                            "Бюджетні смартфони" } },
                    { "Планшети",
                        new List<string> {
                            "Android планшети",
                            "iPad" } },
                    { "Телевізори",
                        new List<string> {
                            "Smart TV",
                            "4K телевізори" } }
                }
            },
            {
                "Побутова техніка", new Dictionary<string, List<string>>
                {
                    { "Холодильники",
                        new List<string> {
                            "Смарт-холодильники",
                            "Холодильники з морозильною камерою" } },
                    { "Пральні машини",
                        new List<string> {
                            "Пральні машини з сушкою",
                            "Автоматичні пральні машини" } }
                }
            },
            {
                "Товари для дому", new Dictionary<string, List<string>>
                {
                    { "Меблі для вітальні",
                        new List<string> {
                            "Дивани",
                            "Крісла",
                            "Стільці" } },
                    { "Меблі для спальні",
                        new List<string> {
                            "Ліжка",
                            "Шафи",
                            "Комоди" } },
                    { "Кухонні меблі",
                        new List<string> {
                            "Кухонні столи",
                            "Кухонні шафи" } },
                    { "Офісні меблі",
                        new List<string> {
                            "Офісні столи",
                            "Офісні стільці" } },
                    { "Аксесуари",
                        new List<string> {
                            "Декор для дому",
                            "Освітлення" }
                    }
                }
            },
            {
                "Дача, сад і город", new Dictionary<string, List<string>>
                {
                    { "Іграшки",
                        new List<string> {
                            "Конструктори",
                            "Плюшеві іграшки" } },
                    { "Книги",
                        new List<string> {
                            "Художня література",
                            "Навчальна література" } },
                    { "Спортивний інвентар",
                        new List<string> {
                            "Футбольні м'ячі",
                            "Баскетбольні м'ячі" } },
                    { "Садовий інвентар",
                        new List<string> {
                            "Садові інструменти",
                            "Грядки та горщики" } },
                    { "Аксесуари",
                        new List<string> {
                            "Садові меблі",
                            "Освітлення для саду" }
                    }
                }
            },
            {
                "Спорт і хобі", new Dictionary<string, List<string>>
                {
                    { "Спортивний одяг",
                        new List<string> {
                            "Футболки",
                            "Шорти",
                            "Спортивні костюми" } },
                    { "Спортивне взуття",
                        new List<string> {
                            "Спортивні кросівки",
                            "Бутси" } },
                    { "Спортивні аксесуари",
                        new List<string> {
                            "Рюкзаки",
                            "Гамаші" } },
                    { "Хобі та творчість",
                        new List<string> {
                            "Набори для малювання",
                            "Книги з рукоділля" } },
                    { "Аксесуари",
                        new List<string> {
                            "Спортивні сумки",
                            "Годинники" }
                    }
                }
            },
            {
                "Краса та здоров'я", new Dictionary<string, List<string>>
                {
                    { "Косметика",
                        new List<string> {
                            "Макіяж",
                            "Догляд за шкірою" } },
                    { "Парфуми",
                        new List<string> {
                            "Чоловічі парфуми",
                            "Жіночі парфуми" } },
                    { "Засоби гігієни",
                        new List<string> {
                            "Зубні пасти",
                            "Шампуні" } },
                    { "Вітаміни та добавки",
                        new List<string> {
                            "Вітамінні комплекси",
                            "БАДи" } },
                    { "Аксесуари",
                        new List<string> {
                            "Косметички",
                            "Гребінці" }
                    }
                }
            },
            {
                "Одяг, взуття та прикраси", new Dictionary<string, List<string>>
                {
                    { "Жіночий одяг",
                        new List<string> {
                            "Сукні",
                            "Блузи",
                            "Спідниці" } },
                    { "Чоловічий одяг",
                        new List<string> {
                            "Сорочки",
                            "Штани",
                            "Куртки" } },
                    { "Дитячий одяг",
                        new List<string> {
                            "Дитячі сукні",
                            "Дитячі штани" } },
                    { "Взуття та прикраси",
                        new List<string> {
                            "Черевики",
                            "Кільця",
                            "Браслети" } }
                }
            }
        };

        // Створюємо всі категорії згідно з планом
        var newCategories = new List<Category>();

        // Створюємо всі 8 категорій верхнього рівня
        foreach (var topLevelCategory in categoryNames)
        {
            var categoryName = topLevelCategory.Key;
            var subCategories = topLevelCategory.Value;
            // Створюємо слаг, який відповідає регулярному виразу ^[a-z0-9-]+$
            var slugValue = Regex.Replace(categoryName.ToLower(), @"[^a-z0-9-]", "-");
            // Замінюємо множинні дефіси на один
            slugValue = Regex.Replace(slugValue, @"-+", "-");
            // Видаляємо дефіси на початку та в кінці
            slugValue = slugValue.Trim('-');
            // Перевіряємо, чи не порожній слаг
            if (string.IsNullOrEmpty(slugValue))
            {
                slugValue = "category-" + Guid.NewGuid().ToString("N")[..8];
            }
            var slug = new Slug(slugValue);
            // Створюємо категорію без parentId
            var category = new Category(
                name: categoryName,
                slug: slug,
                description: $"Категорія для товарів типу {categoryName.ToLower()}",
                meta: new Meta(
                    title: $"{categoryName} - Найкращі товари",
                    description: $"Широкий вибір товарів у категорії {categoryName.ToLower()}"
                )
            );
            newCategories.Add(category);
            // Додаємо підкатегорії, якщо є
            foreach (var subCategory in subCategories)
            {
                // Створюємо слаг для підкатегорії
                var subSlugValue = Regex.Replace(subCategory.Key.ToLower(), @"[^a-z0-9-]", "-");
                subSlugValue = Regex.Replace(subSlugValue, @"-+", "-").Trim('-');
                if (string.IsNullOrEmpty(subSlugValue))
                {
                    subSlugValue = "subcategory-" + Guid.NewGuid().ToString("N")[..8];
                }
                var subSlug = new Slug(subSlugValue);

                // Створюємо підкатегорію
                var subCategoryEntity = new Category(
                    name: subCategory.Key,
                    slug: subSlug,
                    description: $"Підкатегорія для товарів типу {subCategory.Key.ToLower()}",
                    parentId: category.Id,
                    meta: new Meta(
                        title: $"{subCategory} - Найкращі товари",
                        description: $"Широкий вибір товар, у підкатегорії {subCategory.Key.ToLower()}"
                        // Не встановлюємо image
                    ));
                newCategories.Add(subCategoryEntity);
                // Додаємо підпідкатегорії, якщо є
                if (subCategory.Value != null && subCategory.Value.Count > 0)
                {
                    foreach (var subsubCategoryName in subCategory.Value)
                    {
                        // Створюємо слаг для підпідкатегорії
                        var subsubSlugValue = Regex.Replace(subsubCategoryName.ToLower(), @"[^a-z0-9-]", "-");
                        subsubSlugValue = Regex.Replace(subsubSlugValue, @"-+", "-").Trim('-');
                        if (string.IsNullOrEmpty(subsubSlugValue))
                        {
                            subsubSlugValue = "subsubcategory-" + Guid.NewGuid().ToString("N")[..8];
                        }
                        var subsubSlug = new Slug(subsubSlugValue);
                        // Створюємо підпідкатегорію
                        var subsubCategory = new Category(
                            name: subsubCategoryName,
                            slug: subsubSlug,
                            description: $"Підпідкатегорія для товарів типу {subsubCategoryName.ToLower()}",
                            parentId: subCategoryEntity.Id,
                            meta: new Meta(
                                title: $"{subsubCategoryName} - Найкращі товари",
                                description: $"Широкий вибір товарів у підпідкатегорії {subsubCategoryName.ToLower()}"
                            )
                        );
                        newCategories.Add(subsubCategory);
                    }
                }

            }
        }

        // Додавання до бази
        _context.Categories.AddRange(newCategories);
        _context.SaveChanges();

        Console.WriteLine($"Created {newCategories.Count} categories (8 top-level + subcategories + sub-subcategories).");

        // Повертаємо всі категорії
        return _context.Categories.ToList();
    }

    private List<Company> CreateCompaniesFromApprovedRequests(List<User> users, int count)
    {
        // Отримуємо схвалені SellerRequest
        var approvedRequests = _context.SellerRequests
            .Where(sr => sr.Status == RequestStatus.Approved)
            .ToList();

        if (!approvedRequests.Any())
        {
            Console.WriteLine("No approved seller requests found. Skipping company creation.");
            return new List<Company>();
        }

        // Перевірка, чи є вже компанії
        var existingCount = _context.Companies.Count();

        // Створюємо компанії для всіх схвалених заявок
        var companiesToCreate = approvedRequests.Count - existingCount;

        if (companiesToCreate <= 0)
        {
            Console.WriteLine($"Already have {existingCount} companies for {approvedRequests.Count} approved requests.");
            return _context.Companies.ToList();
        }

        Console.WriteLine($"Creating {companiesToCreate} companies from approved seller requests.");

        var newCompanies = new List<Company>();
        var admin = users.FirstOrDefault(u => u.Role == Role.Admin);

        for (int i = 0; i < companiesToCreate && i < approvedRequests.Count; i++)
        {
            var request = approvedRequests[i];
            var companyData = request.CompanyRequestData;

            // Створюємо правильний слаг з назви компанії
            var slugValue = Regex.Replace(companyData.Name.ToLower(), @"[^a-z0-9-]", "-");
            slugValue = Regex.Replace(slugValue, @"-+", "-").Trim('-');
            if (string.IsNullOrEmpty(slugValue))
            {
                slugValue = "company-" + Guid.NewGuid().ToString("N")[..8];
            }

            // Створюємо компанію на основі даних з SellerRequest
            var company = new Company(
                name: companyData.Name,
                slug: new Slug(slugValue),
                description: companyData.Description,
                image: null, // Не встановлюємо для тестування функціональності завантаження
                contactEmail: new Email(companyData.ContactEmail),
                contactPhone: new Phone(companyData.ContactPhone),
                address: new AddressVO(
                    region: companyData.AddressRegion,
                    city: companyData.AddressCity,
                    street: companyData.AddressStreet,
                    postalCode: companyData.AddressPostalCode
                ),
                meta: new Meta(
                    title: companyData.MetaTitle,
                    description: companyData.MetaDescription
                    // Не встановлюємо meta image
                )
            );

            // Встановлюємо затвердження компанії
            if (admin != null && request.ApprovedByUser.HasValue)
            {
                company.ApprovedAt = request.ApprovedAt;
                company.ApprovedByUserId = request.ApprovedByUser.Value;
            }

            newCompanies.Add(company);
        }

        // Додавання до бази
        _context.Companies.AddRange(newCompanies);
        _context.SaveChanges();

        Console.WriteLine($"Created {newCompanies.Count} companies.");

        // Повертаємо всі компанії
        return _context.Companies.ToList();
    }

    private List<Company> CreateCompaniesIfNotExist(List<Address> addresses, List<User> users, int count)
    {
        // Перевірка, чи є вже компанії
        var existingCount = _context.Companies.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} companies.");
            return _context.Companies.ToList();
        }

        // Створюємо додаткові компанії, якщо потрібно
        var companiesToCreate = count - existingCount;
        Console.WriteLine($"Creating {companiesToCreate} additional companies.");

        // Налаштування генератора фейкових даних для Company
        var companyFaker = new Faker<Company>()
            .CustomInstantiator(f =>
            {
                var name = f.Company.CompanyName();
                // Створюємо слаг, який відповідає регулярному виразу ^[a-z0-9-]+$
                var slugValue = Regex.Replace(name.ToLower(), @"[^a-z0-9-]", "-");
                // Замінюємо множинні дефіси на один
                slugValue = Regex.Replace(slugValue, @"-+", "-");
                // Видаляємо дефіси на початку та в кінці
                slugValue = slugValue.Trim('-');
                // Перевіряємо, чи не порожній слаг
                if (string.IsNullOrEmpty(slugValue))
                {
                    slugValue = "company-" + f.Random.AlphaNumeric(5).ToLower();
                }
                var slug = new Slug(slugValue);

                // Створюємо нову адресу для компанії
                var addressVO = new AddressVO(
                    region: f.Address.State(),
                    city: f.Address.City(),
                    street: f.Address.StreetName(),
                    postalCode: f.Address.ZipCode("#####")
                );

                return new Company(
                    name: name,
                    slug: slug,
                    description: f.Lorem.Paragraph(),
                    image: null, // Не встановлюємо зображення для тестування функціональності завантаження
                    contactEmail: new Email(f.Internet.Email(name)),
                    contactPhone: new Phone("+380" + f.Random.Number(10, 99).ToString() + f.Random.Number(1000000, 9999999).ToString()),
                    address: addressVO,
                    meta: new Meta(
                        title: $"{name} - Офіційний магазин",
                        description: $"Купуйте товари від {name} за найкращими цінами"
                        // Не встановлюємо metaImage для тестування функціональності завантаження
                    )
                );
            });

        // Генерація даних
        var newCompanies = companyFaker.Generate(companiesToCreate);

        // Знаходимо адміністратора для затвердження
        var admin = users.FirstOrDefault(u => u.Role == Role.Admin);

        // Встановлюємо всі компанії як затверджені (за сценарієм незатверджених компаній не може бути)
        foreach (var company in newCompanies)
        {
            // Всі компанії затверджені за замовчуванням
            if (admin != null)
            {
                company.ApprovedAt = DateTime.UtcNow;
                company.ApprovedByUserId = admin.Id;
            }

            if (new Random().Next(100) < 30) // 30% компаній рекомендовані
            {
                company.IsFeatured = true;
            }
        }

        // Додавання до бази
        _context.Companies.AddRange(newCompanies);
        _context.SaveChanges();

        Console.WriteLine($"Created {companiesToCreate} companies.");

        // Повертаємо всі компанії
        return _context.Companies.ToList();
    }

    private List<Product> CreateProductsIfNotExist(List<Company> companies, List<Category> categories, List<User> users, int count)
    {
        // Перевірка, чи є вже продукти
        var existingCount = _context.Products.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} products.");
            return _context.Products.ToList();
        }

        // Створюємо додаткові продукти, якщо потрібно
        var productsToCreate = count - existingCount;
        Console.WriteLine($"Creating {productsToCreate} additional products.");

        // Отримуємо категорії найнижчого рівня (підпідкатегорії)
        var leafCategories = categories.Where(c => !categories.Any(child => child.ParentId == c.Id)).ToList();

        // Список продуктів для кожної підпідкатегорії
        var productsByCategory = new Dictionary<string, List<string>>
        {
            // Ноутбуки та комп'ютери
            { "Геймерські ноутбуки", new List<string> { "Ігровий ноутбук Acer Predator Helios 300", "ASUS ROG Strix G15", "MSI Gaming Laptop", "HP Omen Gaming" } },
            { "Бізнес-ноутбуки", new List<string> { "Dell Latitude 7420", "HP EliteBook 840 G8", "Lenovo ThinkPad X1", "ASUS ExpertBook" } },
            { "Системні блоки", new List<string> { "Ігровий ПК Cobra Advanced", "Офісний комп'ютер HP ProDesk", "Dell OptiPlex", "Lenovo ThinkCentre" } },
            { "Монітори", new List<string> { "Samsung Odyssey G7", "LG UltraGear 27GN950", "ASUS ProArt Display", "Dell UltraSharp" } },
            { "Материнські плати", new List<string> { "ASUS ROG Strix Z690-A", "MSI MPG B550 GAMING PLUS", "Gigabyte AORUS", "ASRock B450M" } },
            { "Процесори", new List<string> { "Intel Core i9-12900K", "AMD Ryzen 9 5950X", "Intel Core i7-11700K", "AMD Ryzen 7 5800X" } },

            // Смартфони, ТВ і електроніка
            { "Флагманські смартфони", new List<string> { "iPhone 15 Pro Max", "Samsung Galaxy S23 Ultra", "Google Pixel 8 Pro", "OnePlus 11" } },
            { "Бюджетні смартфони", new List<string> { "Xiaomi Redmi Note 12", "Samsung Galaxy A54", "Realme 10", "POCO X5" } },
            { "Android планшети", new List<string> { "Samsung Galaxy Tab S9", "Lenovo Tab P12 Pro", "Xiaomi Pad 6", "Huawei MatePad" } },
            { "iPad", new List<string> { "Apple iPad Pro 12.9", "Apple iPad Air 5", "Apple iPad 10.9", "Apple iPad mini 6" } },
            { "Smart TV", new List<string> { "Samsung Neo QLED QN90C", "LG OLED C3", "Sony Bravia XR", "TCL QLED C735" } },
            { "4K телевізори", new List<string> { "Sony Bravia XR A80L", "Philips OLED 808", "Samsung QN85C", "LG NANO86" } },

            // Побутова техніка
            { "Смарт-холодильники", new List<string> { "Samsung Family Hub", "LG InstaView", "Bosch Serie 8", "Electrolux CombiCool" } },
            { "Холодильники з морозильною камерою", new List<string> { "Bosch Serie 4", "Electrolux ENN2853COW", "Samsung RB37", "LG GBB92" } },
            { "Пральні машини з сушкою", new List<string> { "Samsung WD90T534DBW", "Bosch WDU28590", "LG F4V9", "Electrolux EW9W" } },
            { "Автоматичні пральні машини", new List<string> { "LG F4V5VG0W", "Electrolux EW6F4R28WU", "Samsung WW90", "Bosch WAU28" } },

            // Товари для дому
            { "Дивани", new List<string> { "Кутовий диван 'Мілан'", "Розкладний диван 'Барселона'", "Диван 'Честер'", "Модульний диван 'Токіо'" } },
            { "Крісла", new List<string> { "Крісло-гойдалка 'Релакс'", "Крісло 'Лондон'", "Офісне крісло 'Ергономік'", "Крісло 'Скандинавія'" } },
            { "Стільці", new List<string> { "Обідній стілець 'Комфорт'", "Барний стілець 'Модерн'", "Дитячий стілець 'Веселка'", "Складний стілець 'Практик'" } },
            { "Ліжка", new List<string> { "Двоспальне ліжко 'Венеція'", "Ліжко з підйомним механізмом 'Прага'", "Односпальне ліжко 'Мінімал'", "Ліжко 'Класик'" } },
            { "Шафи", new List<string> { "Шафа-купе 'Токіо'", "Гардеробна система 'Нью-Йорк'", "Шафа 'Прованс'", "Модульна шафа 'Лофт'" } },
            { "Комоди", new List<string> { "Комод 'Класик'", "Комод з дзеркалом 'Елеганс'", "Дитячий комод 'Казка'", "Комод 'Мінімал'" } },
            { "Кухонні столи", new List<string> { "Обідній стіл 'Родинний'", "Барний стіл 'Лофт'", "Розкладний стіл 'Практик'", "Круглий стіл 'Класик'" } },
            { "Кухонні шафи", new List<string> { "Кухонна шафа 'Модерн'", "Навісна шафа 'Компакт'", "Шафа під мийку 'Практик'", "Кутова шафа 'Оптимал'" } },
            { "Офісні столи", new List<string> { "Робочий стіл 'Професіонал'", "Комп'ютерний стіл 'Геймер'", "Письмовий стіл 'Класик'", "Стіл-трансформер 'Універсал'" } },
            { "Офісні стільці", new List<string> { "Офісне крісло 'Ергономік'", "Комп'ютерне крісло 'Комфорт'", "Керівницьке крісло 'Престиж'", "Операторське крісло 'Практик'" } },
            { "Декор для дому", new List<string> { "Картина 'Абстракція'", "Ваза 'Елеганс'", "Свічка ароматична 'Релакс'", "Статуетка 'Мистецтво'" } },
            { "Освітлення", new List<string> { "Люстра 'Кришталь'", "Настільна лампа 'Класик'", "Торшер 'Модерн'", "LED-стрічка 'Декор'" } },

            // Дача, сад і город
            { "Конструктори", new List<string> { "LEGO Classic", "Конструктор 'Замок'", "Дерев'яний конструктор", "Магнітний конструктор" } },
            { "Плюшеві іграшки", new List<string> { "Ведмедик 'Тедді'", "Зайчик 'Пухнастик'", "Собачка 'Друг'", "Котик 'М'якуш'" } },
            { "Художня література", new List<string> { "Роман 'Класика'", "Детектив 'Таємниця'", "Фантастика 'Майбутнє'", "Поезія 'Душа'" } },
            { "Навчальна література", new List<string> { "Підручник з математики", "Словник англійської мови", "Енциклопедія 'Світ'", "Атлас географії" } },
            { "Футбольні м'ячі", new List<string> { "М'яч FIFA", "Тренувальний м'яч", "Дитячий м'яч", "Професійний м'яч" } },
            { "Баскетбольні м'ячі", new List<string> { "М'яч NBA", "Вуличний м'яч", "Тренувальний м'яч", "Дитячий м'яч" } },
            { "Садові інструменти", new List<string> { "Набір садових інструментів Fiskars", "Секатор Gardena", "Лопата 'Садівник'", "Грабли 'Професіонал'" } },
            { "Грядки та горщики", new List<string> { "Збірна грядка 'Городник'", "Горщик для рослин 'Флора'", "Кашпо 'Декор'", "Вазон 'Класик'" } },
            { "Садові меблі", new List<string> { "Набір садових меблів 'Прованс'", "Гамак 'Відпочинок'", "Стіл садовий 'Комфорт'", "Лавка 'Релакс'" } },
            { "Освітлення для саду", new List<string> { "Сонячні ліхтарі 'Еко'", "Гірлянда садова 'Фея'", "Прожектор 'Світло'", "Ліхтар 'Класик'" } },

            // Спорт і хобі
            { "Футболки", new List<string> { "Спортивна футболка Nike Dri-FIT", "Футболка Adidas Performance", "Under Armour HeatGear", "Puma DryCELL" } },
            { "Шорти", new List<string> { "Шорти для бігу Puma", "Тренувальні шорти Under Armour", "Nike Flex Shorts", "Adidas 3-Stripes" } },
            { "Спортивні костюми", new List<string> { "Костюм Nike Sportswear", "Костюм Adidas Essentials", "Костюм Puma Classics", "Костюм Under Armour Rival" } },
            { "Спортивні кросівки", new List<string> { "Бігові кросівки Nike Air Zoom", "Тренувальні кросівки Reebok Nano", "Adidas Ultraboost", "New Balance Fresh Foam" } },
            { "Бутси", new List<string> { "Футбольні бутси Adidas Predator", "Бутси Nike Mercurial", "Puma Future Z", "Under Armour Magnetico" } },
            { "Рюкзаки", new List<string> { "Спортивний рюкзак Nike", "Туристичний рюкзак Deuter", "Міський рюкзак Adidas", "Рюкзак для фітнесу Under Armour" } },
            { "Гамаші", new List<string> { "Компресійні гамаші Nike", "Бігові гамаші Adidas", "Фітнес гамаші Under Armour", "Йога гамаші Puma" } },
            { "Набори для малювання", new List<string> { "Набір акварелі 'Художник'", "Набір олівців 'Майстер'", "Набір фарб 'Творець'", "Набір пензлів 'Професіонал'" } },
            { "Книги з рукоділля", new List<string> { "Книга 'В'язання спицями'", "Книга 'Вишивка хрестиком'", "Книга 'Декупаж'", "Книга 'Скрапбукінг'" } },
            { "Спортивні сумки", new List<string> { "Сумка для спортзалу Nike", "Сумка для тенісу Wilson", "Сумка для плавання Speedo", "Сумка для йоги Adidas" } },
            { "Годинники", new List<string> { "Спортивні годинники Garmin", "Фітнес-браслет Fitbit", "Смарт-годинник Apple Watch", "Годинники для бігу Polar" } },

            // Краса та здоров'я
            { "Макіяж", new List<string> { "Тональний крем Estée Lauder", "Палетка тіней Urban Decay", "Помада MAC", "Тушь для вій Maybelline" } },
            { "Догляд за шкірою", new List<string> { "Сироватка з вітаміном С The Ordinary", "Крем для обличчя La Roche-Posay", "Очищувальний гель CeraVe", "Зволожувач Neutrogena" } },
            { "Чоловічі парфуми", new List<string> { "Dior Sauvage", "Chanel Bleu de Chanel", "Tom Ford Oud Wood", "Versace Eros" } },
            { "Жіночі парфуми", new List<string> { "Chanel Coco Mademoiselle", "Yves Saint Laurent Black Opium", "Dior J'adore", "Lancôme La Vie Est Belle" } },
            { "Зубні пасти", new List<string> { "Зубна паста Colgate Total", "Зубна паста Sensodyne", "Зубна паста Oral-B", "Зубна паста Lacalut" } },
            { "Шампуні", new List<string> { "Шампунь L'Oréal Elvive", "Шампунь Head & Shoulders", "Шампунь Pantene Pro-V", "Шампунь Garnier Fructis" } },
            { "Вітамінні комплекси", new List<string> { "Вітаміни Centrum", "Вітаміни Vitrum", "Вітаміни Complivit", "Вітаміни Solgar" } },
            { "БАДи", new List<string> { "Омега-3 Now Foods", "Магній B6", "Вітамін D3", "Коензим Q10" } },
            { "Косметички", new List<string> { "Косметичка 'Елеганс'", "Косметичка 'Подорож'", "Косметичка 'Компакт'", "Косметичка 'Професіонал'" } },
            { "Гребінці", new List<string> { "Гребінець дерев'яний", "Гребінець масажний", "Гребінець для укладки", "Гребінець антистатичний" } },

            // Одяг, взуття та прикраси
            { "Сукні", new List<string> { "Вечірня сукня 'Елеганс'", "Повсякденна сукня 'Комфорт'", "Коктейльна сукня 'Шарм'", "Літня сукня 'Легкість'" } },
            { "Блузи", new List<string> { "Шовкова блуза 'Париж'", "Офісна блуза 'Бізнес'", "Повсякденна блуза 'Кежуал'", "Святкова блуза 'Глем'" } },
            { "Спідниці", new List<string> { "Класична спідниця 'Офіс'", "Джинсова спідниця 'Кежуал'", "Плісирована спідниця 'Романтик'", "Міні-спідниця 'Молодіжна'" } },
            { "Сорочки", new List<string> { "Класична сорочка 'Брістоль'", "Повсякденна сорочка 'Кежуал'", "Офісна сорочка 'Професіонал'", "Святкова сорочка 'Елегант'" } },
            { "Штани", new List<string> { "Джинси 'Урбан'", "Класичні брюки 'Престиж'", "Спортивні штани 'Комфорт'", "Літні штани 'Легкість'" } },
            { "Куртки", new List<string> { "Зимова куртка 'Тепло'", "Демісезонна куртка 'Стиль'", "Шкіряна куртка 'Рок'", "Спортивна куртка 'Актив'" } },
            { "Дитячі сукні", new List<string> { "Святкова сукня 'Принцеса'", "Повсякденна сукня 'Веселка'", "Шкільна сукня 'Акуратна'", "Літня сукня 'Сонечко'" } },
            { "Дитячі штани", new List<string> { "Дитячі джинси 'Міцні'", "Спортивні штани 'Активні'", "Класичні штани 'Школа'", "Літні шорти 'Гра'" } },
            { "Черевики", new List<string> { "Зимові черевики 'Тепло'", "Демісезонні черевики 'Стиль'", "Робочі черевики 'Міцність'", "Модні черевики 'Тренд'" } },
            { "Кільця", new List<string> { "Обручка 'Вічність'", "Кільце з діамантом 'Блиск'", "Срібне кільце 'Класик'", "Золоте кільце 'Розкіш'" } },
            { "Браслети", new List<string> { "Золотий браслет 'Елеганс'", "Срібний браслет 'Мінімал'", "Браслет з камінням 'Шарм'", "Спортивний браслет 'Актив'" } }
        };

        // Забезпечуємо заповнення всіх категорій
        var productsPerCategory = Math.Max(1, productsToCreate / leafCategories.Count);
        var remainingProducts = productsToCreate;
        var newProducts = new List<Product>();

        // Створюємо продукти для кожної категорії
        foreach (var category in leafCategories)
        {
            var productsForThisCategory = Math.Min(productsPerCategory, remainingProducts);
            if (productsForThisCategory <= 0) break;

            // Налаштування генератора фейкових даних для Product для конкретної категорії
            var productFaker = new Faker<Product>()
                .CustomInstantiator(f =>
                {
                    // Вибираємо випадкову компанію
                    var company = f.PickRandom(companies);

                    // Вибираємо випадкового користувача як продавця
                    var seller = f.PickRandom(users);

                    // Визначаємо назву продукту на основі категорії
                    string productName;
                    if (productsByCategory.ContainsKey(category.Name) && productsByCategory[category.Name].Any())
                    {
                        var productType = f.PickRandom(productsByCategory[category.Name]);
                        productName = productType;
                    }
                    else
                    {
                        productName = f.Commerce.ProductName();
                    }

                    // Створюємо слаг, який відповідає регулярному виразу ^[a-z0-9-]+$
                    var slugValue = Regex.Replace(productName.ToLower(), @"[^a-z0-9-]", "-");
                    // Замінюємо множинні дефіси на один
                    slugValue = Regex.Replace(slugValue, @"-+", "-");
                    // Видаляємо дефіси на початку та в кінці
                    slugValue = slugValue.Trim('-');
                    // Перевіряємо, чи не порожній слаг
                    if (string.IsNullOrEmpty(slugValue))
                    {
                        slugValue = "product-" + f.Random.AlphaNumeric(5).ToLower();
                    }
                    var slug = new Slug(slugValue);

                    // Створюємо атрибути продукту
                    var attributes = new Dictionary<string, string>
                    {
                        { "Колір", f.Commerce.Color() },
                        { "Матеріал", f.Commerce.ProductMaterial() },
                        { "Вага", $"{f.Random.Number(1, 10)} кг" },
                        { "Розміри", $"{f.Random.Number(10, 100)}x{f.Random.Number(10, 100)}x{f.Random.Number(10, 100)} см" }
                    };

                    return new Product(
                        companyId: company.Id,
                        sellerId: seller.Id,
                        name: productName,
                        slug: slug,
                        description: f.Commerce.ProductDescription(),
                        price: new Money(f.Random.Decimal(100, 10000), Currency.UAH),
                        stock: (uint)f.Random.Number(1, 100),
                        sales: (uint)f.Random.Number(0, 50), // Випадкова кількість продажів за останній місяць
                        categoryId: category.Id,
                        attributes: attributes,
                        meta: new Meta(
                            title: $"{productName} - Купити в інтернет-магазині",
                            description: $"Купити {productName.ToLower()} за найкращою ціною. Доставка по всій Україні."
                            // Не встановлюємо image для meta
                        )
                    );
                });

            // Генерація продуктів для цієї категорії
            var categoryProducts = productFaker.Generate(productsForThisCategory);
            newProducts.AddRange(categoryProducts);
            remainingProducts -= productsForThisCategory;
        }

        // Знаходимо адміністратора для затвердження
        var admin = users.FirstOrDefault(u => u.Role == Role.Admin);

        // Встановлюємо статус та затвердження для продуктів
        foreach (var product in newProducts)
        {
            // Встановлюємо статус продукту
            var statusRandom = new Random().Next(100);
            if (statusRandom < 80) // 80% продуктів затверджені
            {
                product.Status = ProductStatus.Approved;
            }
            else if (statusRandom < 90) // 10% продуктів очікують
            {
                product.Status = ProductStatus.Pending;
            }
            else // 10% продуктів відхилені
            {
                product.Status = ProductStatus.Rejected;
            }

            // Всі продукти повинні мати ApprovedByUserId, навіть якщо вони не затверджені
            if (admin != null)
            {
                product.ApprovedByUserId = admin.Id;

                // Встановлюємо затвердження продукту
                if (new Random().Next(100) < 70) // 70% продуктів затверджені
                {
                    product.IsApproved = true;
                    product.ApprovedAt = DateTime.UtcNow;
                }
            }

            // Встановлюємо дати створення та оновлення (півтори року = 540 днів)
            product.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 540));
            if (new Random().Next(100) < 50) // 50% продуктів оновлені
            {
                product.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 90)); // Оновлення за останні 3 місяці
            }
        }

        // Додавання до бази
        _context.Products.AddRange(newProducts);
        _context.SaveChanges();

        Console.WriteLine($"Created {productsToCreate} products.");

        // Повертаємо всі продукти
        return _context.Products.ToList();
    }



    private List<Rating> CreateRatingsIfNotExist(List<Product> products, List<User> users, int count)
    {
        // Перевірка, чи є вже рейтинги
        var existingCount = _context.Ratings.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} ratings.");
            return _context.Ratings.ToList();
        }

        // Створюємо додаткові рейтинги, якщо потрібно
        var ratingsToCreate = count - existingCount;
        Console.WriteLine($"Creating {ratingsToCreate} additional ratings.");

        // Налаштування генератора фейкових даних для Rating
        var ratingFaker = new Faker<Rating>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий продукт та користувача
                var product = f.PickRandom(products);
                var user = f.PickRandom(users);

                // Генеруємо оцінки з більшою ймовірністю для 4-5 балів
                var service = f.Random.WeightedRandom(new[] { 1, 2, 3, 4, 5 }, new[] { 0.05f, 0.1f, 0.15f, 0.35f, 0.35f });
                var deliveryTime = f.Random.WeightedRandom(new[] { 1, 2, 3, 4, 5 }, new[] { 0.05f, 0.1f, 0.15f, 0.35f, 0.35f });
                var accuracy = f.Random.WeightedRandom(new[] { 1, 2, 3, 4, 5 }, new[] { 0.05f, 0.1f, 0.15f, 0.35f, 0.35f });

                // Генеруємо коментар (всі рейтинги мають коментар)
                string comment = f.Lorem.Sentence();

                return new Rating(
                    userId: user.Id,
                    productId: product.Id,
                    service: service,
                    deliveryTime: deliveryTime,
                    accuracy: accuracy,
                    comment: comment
                );
            });

        // Генерація даних
        var newRatings = ratingFaker.Generate(ratingsToCreate);

        // Встановлюємо дати створення та оновлення (5 місяців)
        foreach (var rating in newRatings)
        {
            rating.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 150));
            if (new Random().Next(100) < 20) // 20% рейтингів оновлені
            {
                rating.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            }
        }

        // Додавання до бази
        _context.Ratings.AddRange(newRatings);
        _context.SaveChanges();

        Console.WriteLine($"Created {ratingsToCreate} ratings.");

        // Повертаємо всі рейтинги
        return _context.Ratings.ToList();
    }

    private void CreateReviewsIfNotExist(List<Product> products, List<User> users, List<Rating> ratings, int count)
    {
        // Перевірка, чи є вже відгуки
        var existingCount = _context.Reviews.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} reviews.");
            return;
        }

        // Створюємо додаткові відгуки, якщо потрібно
        var reviewsToCreate = count - existingCount;
        Console.WriteLine($"Creating {reviewsToCreate} additional reviews.");

        // Налаштування генератора фейкових даних для Review
        var reviewFaker = new Faker<Review>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий рейтинг
                var rating = f.PickRandom(ratings);

                // Використовуємо той самий продукт та користувача, що і в рейтингу
                var productId = rating.ProductId;
                var userId = rating.UserId;

                // Генеруємо коментар (не більше 500 символів)
                var paragraph = f.Lorem.Paragraph();
                var comment = paragraph.Length <= 500 ? paragraph : paragraph.Substring(0, 500);

                // Створюємо відгук
                return new Review(
                    userId: userId,
                    productId: productId,
                    rating: rating,
                    parentId: Guid.NewGuid(), // Використовуємо новий GUID як parentId
                    comment: comment
                );
            });

        // Генерація даних
        var newReviews = reviewFaker.Generate(reviewsToCreate);

        // Встановлюємо дати створення та оновлення (5 місяців)
        foreach (var review in newReviews)
        {
            review.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 150));
            if (new Random().Next(100) < 20) // 20% відгуків оновлені
            {
                review.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            }
        }

        // Додавання до бази
        _context.Reviews.AddRange(newReviews);
        _context.SaveChanges();

        Console.WriteLine($"Created {reviewsToCreate} reviews.");
    }

    private List<ShippingMethod> CreateShippingMethodsIfNotExist()
    {
        // Перевірка, чи є вже стандартні методи доставки
        var standardMethods = _context.ShippingMethods
            .Where(sm => sm.Name == Name.NovaPoshta || sm.Name == Name.UkrPoshta ||
                        sm.Name == Name.Courier || sm.Name == Name.SelfPickup)
            .ToList();

        if (standardMethods.Count >= 4)
        {
            Console.WriteLine($"Already have {standardMethods.Count} standard shipping methods.");
            return standardMethods;
        }

        Console.WriteLine("Creating standard shipping methods.");

        // Створюємо стандартні методи доставки
        var shippingMethods = new List<ShippingMethod>
        {
            new ShippingMethod(
                price: new Money(50, Currency.UAH),
                estimatedDays: 2,
                name: Name.NovaPoshta
            ),
            new ShippingMethod(
                price: new Money(40, Currency.UAH),
                estimatedDays: 5,
                name: Name.UkrPoshta
            ),
            new ShippingMethod(
                price: new Money(0m, Currency.UAH),
                estimatedDays: 0,
                name: Name.SelfPickup
            ),
            new ShippingMethod(
                price: new Money(100, Currency.UAH),
                estimatedDays: 1,
                name: Name.Courier
            )
        };

        // Додавання до бази
        _context.ShippingMethods.AddRange(shippingMethods);
        _context.SaveChanges();

        Console.WriteLine($"Created {shippingMethods.Count} standard shipping methods.");

        // Повертаємо всі стандартні методи доставки
        return shippingMethods;
    }

    private List<Coupon> CreateCouponsIfNotExist(int count)
    {
        // Перевірка, чи є вже купони
        var existingCount = _context.Coupons.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} coupons.");
            return _context.Coupons.ToList();
        }

        // Створюємо додаткові купони, якщо потрібно
        var couponsToCreate = count - existingCount;
        Console.WriteLine($"Creating {couponsToCreate} additional coupons.");

        // Налаштування генератора фейкових даних для Coupon
        var couponFaker = new Faker<Coupon>()
            .CustomInstantiator(f =>
            {
                // Генеруємо код купона
                var code = f.Random.AlphaNumeric(8).ToUpper();

                // Визначаємо тип знижки
                var discountType = f.Random.Bool() ? DiscountType.Percentage : DiscountType.Fixed;

                // Генеруємо розмір знижки в залежності від типу
                double discount;
                if (discountType == DiscountType.Percentage)
                {
                    discount = f.Random.Double(5, 50); // Від 5% до 50%
                }
                else
                {
                    discount = f.Random.Double(50, 500); // Від 50 до 500 грн
                }

                // Генеруємо ліміт використань (не для всіх купонів)
                uint? usageLimit = null;
                if (f.Random.Bool(0.7f)) // 70% купонів мають ліміт
                {
                    usageLimit = (uint)f.Random.Number(1, 100);
                }

                // Генеруємо дату закінчення (не для всіх купонів)
                DateTime? expiresAt = null;
                if (f.Random.Bool(0.8f)) // 80% купонів мають дату закінчення
                {
                    expiresAt = DateTime.UtcNow.AddDays(f.Random.Number(7, 90));
                }

                return new Coupon(
                    code: code,
                    discount: discount,
                    discountType: discountType,
                    usageLimit: usageLimit,
                    expiresAt: expiresAt
                );
            });

        // Генерація даних
        var newCoupons = couponFaker.Generate(couponsToCreate);

        // Встановлюємо дати створення та оновлення (5 місяців)
        foreach (var coupon in newCoupons)
        {
            coupon.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 150));
            if (new Random().Next(100) < 20) // 20% купонів оновлені
            {
                coupon.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            }
        }

        // Додавання до бази
        _context.Coupons.AddRange(newCoupons);
        _context.SaveChanges();

        Console.WriteLine($"Created {couponsToCreate} coupons.");

        // Повертаємо всі купони
        return _context.Coupons.ToList();
    }

    private List<Order> CreateOrdersIfNotExist(List<User> users, List<Address> addresses, List<ShippingMethod> shippingMethods, int count)
    {
        // Перевірка, чи є вже замовлення
        var existingCount = _context.Orders.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} orders.");
            return _context.Orders.ToList();
        }

        // Створюємо додаткові замовлення, якщо потрібно
        var ordersToCreate = count - existingCount;
        Console.WriteLine($"Creating {ordersToCreate} additional orders.");

        // Отримуємо тільки Buyer користувачів
        var buyerUsers = users.Where(u => u.Role == Role.Buyer).ToList();

        if (!buyerUsers.Any())
        {
            Console.WriteLine("No buyer users found. Skipping order creation.");
            return new List<Order>();
        }

        // Налаштування генератора фейкових даних для Order
        var orderFaker = new Faker<Order>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового Buyer користувача та адресу
                var user = f.PickRandom(buyerUsers);

                // Вибираємо адресу, яка належить цьому користувачу, або будь-яку іншу
                var userAddresses = addresses.Where(a => a.UserId == user.Id).ToList();
                var address = userAddresses.Any() ? f.PickRandom(userAddresses) : f.PickRandom(addresses);

                // Вибираємо випадковий існуючий метод доставки
                var shippingMethod = f.PickRandom(shippingMethods);

                // Створюємо тимчасову ціну, яка буде оновлена після створення OrderItems
                var totalPrice = new Money(1.00m, Currency.UAH);

                // Генеруємо випадковий статус замовлення з більшою ймовірністю для активних статусів
                var orderStatus = f.Random.WeightedRandom(
                    new[] { OrderStatus.Processing, OrderStatus.Pending, OrderStatus.Shipped, OrderStatus.Delivered, OrderStatus.Cancelled },
                    new[] { 0.15f, 0.25f, 0.25f, 0.2f, 0.15f }  // 15% замовлень скасовані
                );

                var order = new Order(
                    customerId: user.Id,
                    totalPrice: totalPrice,
                    shippingAddressId: address.Id,
                    shippingMethodId: shippingMethod.Id
                );

                // Встановлюємо статус замовлення
                order.Status = orderStatus;

                // Встановлюємо дату створення з діапазоном півтори року (540 днів назад)
                order.CreatedAt = DateTime.UtcNow.AddDays(-f.Random.Int(1, 540));

                return order;
            });

        // Генерація даних
        var newOrders = orderFaker.Generate(ordersToCreate);

        // Зберігаємо замовлення
        _context.Orders.AddRange(newOrders);
        _context.SaveChanges();

        Console.WriteLine($"Created {ordersToCreate} orders using existing shipping methods.");

        // Повертаємо всі замовлення
        return _context.Orders.ToList();
    }

    private void CreateOrderItemsForAllOrders(List<Order> orders, List<Product> products)
    {
        // Перевіряємо, чи є вже OrderItems
        var existingOrderItems = _context.OrderItems.ToList();
        var ordersWithItems = existingOrderItems.Select(oi => oi.OrderId).Distinct().ToList();
        var ordersWithoutItems = orders.Where(o => !ordersWithItems.Contains(o.Id)).ToList();

        if (!ordersWithoutItems.Any())
        {
            Console.WriteLine($"All {orders.Count} orders already have order items.");
            return;
        }

        Console.WriteLine($"Creating order items for {ordersWithoutItems.Count} orders without items.");

        var faker = new Faker();
        var newOrderItems = new List<OrderItem>();

        foreach (var order in ordersWithoutItems)
        {
            // Кожне замовлення має від 1 до 5 товарів
            var itemsCount = faker.Random.Number(1, 5);
            var selectedProducts = faker.PickRandom(products, itemsCount).ToList();

            foreach (var product in selectedProducts)
            {
                // Генеруємо кількість
                var quantity = (uint)faker.Random.Number(1, 3);

                // Використовуємо ТОЧНУ ціну продукту без варіацій
                // Створюємо новий об'єкт Money, щоб уникнути проблем з Entity Framework tracking
                var itemPrice = new Money(product.Price.Amount, product.Price.Currency);

                var orderItem = new OrderItem(
                    orderId: order.Id,
                    productId: product.Id,
                    quantity: quantity,
                    price: itemPrice
                );

                // Встановлюємо дату створення близько до дати замовлення
                orderItem.CreatedAt = order.CreatedAt.AddMinutes(faker.Random.Number(1, 60));

                newOrderItems.Add(orderItem);
            }
        }

        // Додавання до бази
        _context.OrderItems.AddRange(newOrderItems);
        _context.SaveChanges();

        Console.WriteLine($"Created {newOrderItems.Count} order items for {ordersWithoutItems.Count} orders.");
    }

    private void UpdateOrderTotalPricesForNewOrders(List<Order> orders)
    {
        Console.WriteLine("Updating order total prices for newly created orders...");

        // Отримуємо всі OrderItems
        var orderItems = _context.OrderItems.ToList();
        var ordersToUpdate = new List<Order>();

        // Отримуємо ID замовлень, які вже мають правильно розраховані ціни
        // (припускаємо, що замовлення з ціною > 1 UAH вже мають правильну ціну)
        var existingOrdersWithCorrectPrices = _context.Orders
            .Where(o => o.TotalPrice.Amount > 1.00m)
            .Select(o => o.Id)
            .ToList();

        foreach (var order in orders)
        {
            // Пропускаємо замовлення, які вже мають правильно розраховані ціни
            if (existingOrdersWithCorrectPrices.Contains(order.Id))
            {
                Console.WriteLine($"Order {order.Id} already has correct price: {order.TotalPrice.Amount:F2} UAH");
                continue;
            }

            var orderItemsForOrder = orderItems.Where(oi => oi.OrderId == order.Id).ToList();

            if (orderItemsForOrder.Any())
            {
                // Розраховуємо правильну загальну суму: сума всіх (ціна × кількість)
                var totalAmount = orderItemsForOrder.Sum(oi => oi.Price.Amount * oi.Quantity);
                var newTotalPrice = new Money(totalAmount, Currency.UAH);

                // Оновлюємо ціну тільки якщо вона відрізняється
                if (order.TotalPrice.Amount != totalAmount)
                {
                    order.TotalPrice = newTotalPrice;
                    ordersToUpdate.Add(order);
                    Console.WriteLine($"Order {order.Id}: {orderItemsForOrder.Count} items, Updated Total: {totalAmount:F2} UAH");
                }
            }
            else
            {
                Console.WriteLine($"Warning: Order {order.Id} has no order items!");
            }
        }

        if (ordersToUpdate.Any())
        {
            _context.Orders.UpdateRange(ordersToUpdate);
            _context.SaveChanges();
            Console.WriteLine($"Updated total prices for {ordersToUpdate.Count} orders.");
        }
        else
        {
            Console.WriteLine("No orders to update.");
        }
    }

    private void CreateOrderCouponsIfNotExist(List<Order> orders, List<Coupon> coupons)
    {
        // Перевірка, чи є вже зв'язки замовлень з купонами
        var existingCount = _context.OrderCoupons.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} order coupons.");
            return;
        }

        Console.WriteLine("Creating order coupons.");

        // Створюємо зв'язки для 30% замовлень
        var ordersWithCoupons = orders.OrderBy(o => Guid.NewGuid()).Take(orders.Count * 3 / 10).ToList();

        var orderCoupons = new List<OrderCoupon>();

        foreach (var order in ordersWithCoupons)
        {
            // Вибираємо випадковий купон
            var coupon = coupons.OrderBy(c => Guid.NewGuid()).First();

            orderCoupons.Add(new OrderCoupon(
                orderId: order.Id,
                couponId: coupon.Id
            ));
        }

        // Додавання до бази
        _context.OrderCoupons.AddRange(orderCoupons);
        _context.SaveChanges();

        Console.WriteLine($"Created {orderCoupons.Count} order coupons.");
    }

    private void CreatePaymentsIfNotExist(List<Order> orders)
    {
        // Видаляємо всі існуючі платежі для повного перестворення
        var existingPayments = _context.Payments.ToList();
        if (existingPayments.Any())
        {
            Console.WriteLine($"Removing {existingPayments.Count} existing payments to recreate them with correct enum values.");
            _context.Payments.RemoveRange(existingPayments);
            _context.SaveChanges();
        }

        Console.WriteLine($"Creating payments for all {orders.Count} orders.");

        // Створюємо платежі для замовлень без платежів
        var payments = new List<Payment>();

        var faker = new Faker();

        foreach (var order in orders)
        {
            // Генеруємо випадковий метод оплати відповідно до плану: Картка, Готівка, PayPal
            var paymentMethods = new[] { PaymentMethod.Card, PaymentMethod.BankTransfer, PaymentMethod.PayPal };
            var paymentMethod = faker.PickRandom(paymentMethods);

            // Генеруємо статус оплати залежно від статусу замовлення
            var paymentStatus = order.Status switch
            {
                OrderStatus.Delivered => PaymentStatus.Completed,  // Доставлені замовлення завжди оплачені
                OrderStatus.Shipped => faker.Random.WeightedRandom(
                    new[] { PaymentStatus.Completed, PaymentStatus.Pending },
                    new[] { 0.8f, 0.2f }
                ),
                OrderStatus.Cancelled => faker.Random.WeightedRandom(
                    new[] { PaymentStatus.Refunded, PaymentStatus.Failed, PaymentStatus.Pending },
                    new[] { 0.6f, 0.3f, 0.1f }  // Більшість скасованих замовлень повернуті
                ),
                OrderStatus.Processing => faker.Random.WeightedRandom(
                    new[] { PaymentStatus.Pending, PaymentStatus.Completed },
                    new[] { 0.7f, 0.3f }
                ),
                _ => faker.PickRandom<PaymentStatus>()
            };

            payments.Add(new Payment(
                orderId: order.Id,
                paymentMethod: paymentMethod,
                amount: order.TotalPrice,
                paymentStatus: paymentStatus
            ));
        }

        // Встановлюємо дати створення та оновлення відносно дати замовлення
        foreach (var payment in payments)
        {
            var order = orders.First(o => o.Id == payment.OrderId);

            // Платіж створюється в той же день або через кілька днів після замовлення
            payment.CreatedAt = order.CreatedAt.AddDays(faker.Random.Int(0, 3));

            // Якщо платіж оновлювався (статус змінювався)
            if (faker.Random.Int(100) < 30) // 30% платежів оновлені
            {
                payment.UpdatedAt = payment.CreatedAt.AddDays(faker.Random.Int(1, 7));
            }
        }

        // Додавання до бази
        _context.Payments.AddRange(payments);
        _context.SaveChanges();

        Console.WriteLine($"Created {payments.Count} payments.");
    }

    private List<Wishlist> CreateWishlistsIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже списки бажань
        var existingCount = _context.Wishlists.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} wishlists.");
            return _context.Wishlists.ToList();
        }

        // Створюємо додаткові списки бажань, якщо потрібно
        var wishlistsToCreate = count - existingCount;
        Console.WriteLine($"Creating {wishlistsToCreate} additional wishlists.");

        // Налаштування генератора фейкових даних для Wishlist
        var wishlistFaker = new Faker<Wishlist>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача для прив'язки списку бажань
                var randomUser = f.PickRandom(users);

                return new Wishlist(
                    userId: randomUser.Id
                );
            });

        // Генерація даних
        var newWishlists = wishlistFaker.Generate(wishlistsToCreate);

        // Додавання до бази
        _context.Wishlists.AddRange(newWishlists);
        _context.SaveChanges();

        Console.WriteLine($"Created {wishlistsToCreate} wishlists.");

        // Повертаємо всі списки бажань
        return _context.Wishlists.ToList();
    }

    private void CreateWishlistItemsIfNotExist(List<Wishlist> wishlists, List<Product> products, int count)
    {
        // Перевірка, чи є вже елементи списків бажань
        var existingCount = _context.WishlistItems.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} wishlist items.");
            return;
        }

        // Створюємо додаткові елементи списків бажань, якщо потрібно
        var wishlistItemsToCreate = count - existingCount;
        Console.WriteLine($"Creating {wishlistItemsToCreate} additional wishlist items.");

        // Налаштування генератора фейкових даних для WishlistItem
        var wishlistItemFaker = new Faker<WishlistItem>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий список бажань та продукт
                var randomWishlist = f.PickRandom(wishlists);
                var randomProduct = f.PickRandom(products);

                return new WishlistItem(
                    wishlistId: randomWishlist.Id,
                    productId: randomProduct.Id
                );
            });

        // Генерація даних
        var newWishlistItems = new List<WishlistItem>();

        // Створюємо елементи списків бажань, уникаючи дублікатів
        for (int i = 0; i < wishlistItemsToCreate; i++)
        {
            var wishlistItem = wishlistItemFaker.Generate();

            // Перевіряємо, чи вже існує такий елемент
            var exists = _context.WishlistItems.Any(wi =>
                wi.WishlistId == wishlistItem.WishlistId &&
                wi.ProductId == wishlistItem.ProductId);

            if (!exists)
            {
                newWishlistItems.Add(wishlistItem);
            }
            else
            {
                i--; // Повторюємо спробу
            }
        }

        // Додавання до бази
        _context.WishlistItems.AddRange(newWishlistItems);
        _context.SaveChanges();

        Console.WriteLine($"Created {newWishlistItems.Count} wishlist items.");
    }

    private List<Chat> CreateChatsIfNotExist(List<User> users, List<Company> companies, int count)
    {
        // Перевірка, чи є вже чати
        var existingCount = _context.Chats.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} chats.");
            return _context.Chats.ToList();
        }

        // Створюємо додаткові чати, якщо потрібно
        var chatsToCreate = count - existingCount;
        Console.WriteLine($"Creating {chatsToCreate} additional chats.");

        // Налаштування генератора фейкових даних для Chat
        var chatFaker = new Faker<Chat>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкових користувачів для покупця та продавця
                var buyer = f.PickRandom(users.Where(u => u.Role == Role.Buyer).ToList());
                var seller = f.PickRandom(users.Where(u => u.Role == Role.Seller || u.Role == Role.Admin).ToList());

                // Вибираємо випадкову компанію (для деяких чатів)
                Company company = null;
                Guid? companyId = null;

                if (f.Random.Bool(0.7f)) // 70% чатів пов'язані з компанією
                {
                    company = f.PickRandom(companies);
                    companyId = company.Id;
                }

                var chat = new Chat(
                    sellerId: seller.Id,
                    buyerId: buyer.Id
                );

                // Встановлюємо компанію, якщо вона вибрана
                if (companyId.HasValue)
                {
                    chat.CompanyId = companyId;
                }

                return chat;
            });

        // Генерація даних
        var newChats = chatFaker.Generate(chatsToCreate);

        // Встановлюємо дати створення (5 місяців)
        foreach (var chat in newChats)
        {
            chat.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 150));
        }

        // Додавання до бази
        _context.Chats.AddRange(newChats);
        _context.SaveChanges();

        Console.WriteLine($"Created {chatsToCreate} chats.");

        // Повертаємо всі чати
        return _context.Chats.ToList();
    }

    private void CreateMessagesIfNotExist(List<Chat> chats, List<User> users, int count)
    {
        // Перевірка, чи є вже повідомлення
        var existingCount = _context.Messages.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} messages.");
            return;
        }

        // Створюємо додаткові повідомлення, якщо потрібно
        var messagesToCreate = count - existingCount;
        Console.WriteLine($"Creating {messagesToCreate} additional messages.");

        // Налаштування генератора фейкових даних для Message
        var messageFaker = new Faker<Message>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий чат
                var chat = f.PickRandom(chats);

                // Визначаємо відправника (покупець або продавець)
                var sender = f.Random.Bool() ?
                    users.FirstOrDefault(u => u.Id == chat.BuyerId) :
                    users.FirstOrDefault(u => u.Id == chat.SellerId);

                if (sender == null)
                {
                    // Якщо не знайдено, вибираємо випадкового користувача
                    sender = f.PickRandom(users);
                }

                // Створюємо повідомлення, використовуючи конструктор
                var message = new Message(
                    chatId: chat.Id,
                    senderId: sender.Id,
                    messageText: f.Lorem.Sentence()
                );

                // Встановлюємо додаткові властивості (5 місяців)
                message.IsRead = f.Random.Bool();
                message.CreatedAt = DateTime.UtcNow.AddDays(-f.Random.Int(1, 150));
                message.UpdatedAt = DateTime.UtcNow.AddDays(-f.Random.Int(0, 30));

                return message;
            });

        // Генерація даних
        var newMessages = messageFaker.Generate(messagesToCreate);

        // Додавання до бази
        _context.Messages.AddRange(newMessages);
        _context.SaveChanges();

        Console.WriteLine($"Created {messagesToCreate} messages.");
    }

    private void CreateNotificationsIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже сповіщення
        var existingCount = _context.Notifications.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} notifications.");
            return;
        }

        // Створюємо додаткові сповіщення, якщо потрібно
        var notificationsToCreate = count - existingCount;
        Console.WriteLine($"Creating {notificationsToCreate} additional notifications.");

        // Список можливих заголовків сповіщень
        var notificationTitles = new List<string>
        {
            "Нове замовлення", "Знижка на товар", "Статус замовлення оновлено",
            "Новий відгук", "Товар знову в наявності", "Спеціальна пропозиція",
            "Підтвердження реєстрації", "Зміна пароля", "Нове повідомлення"
        };

        // Налаштування генератора фейкових даних для Notification
        var notificationFaker = new Faker<Notification>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача
                var user = f.PickRandom(users);

                // Вибираємо випадковий заголовок
                var title = f.PickRandom(notificationTitles);

                // Генеруємо текст сповіщення (не більше 100 символів)
                var sentence = f.Lorem.Sentence();
                var text = sentence.Length <= 100 ? sentence : sentence.Substring(0, 100);

                return new Notification(
                    userId: user.Id,
                    title: title,
                    text: text
                );
            });

        // Генерація даних
        var newNotifications = notificationFaker.Generate(notificationsToCreate);

        // Встановлюємо дати створення та статус прочитання
        foreach (var notification in newNotifications)
        {
            notification.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            notification.IsRead = new Random().Next(100) < 50; // 50% сповіщень прочитані
        }

        // Додавання до бази
        _context.Notifications.AddRange(newNotifications);
        _context.SaveChanges();

        Console.WriteLine($"Created {notificationsToCreate} notifications.");
    }

    private void CreateSellerRequestsIfNotExist(List<User> users, int count)
    {
        // Отримуємо тільки Buyer користувачів для створення заявок (звичайні користувачі подають заявки)
        var buyerUsers = users.Where(u => u.Role == Role.Buyer).ToList();

        if (!buyerUsers.Any())
        {
            Console.WriteLine("No buyer users found. Skipping seller request creation.");
            return;
        }

        // Перевірка, чи є вже SellerRequest
        var existingCount = _context.SellerRequests.Count();

        // Створюємо заявки: 30 схвалених (для компаній), 10 pending, 15 rejected
        var totalRequestsNeeded = 55; // 30 + 10 + 15
        var requestsToCreate = Math.Min(buyerUsers.Count, totalRequestsNeeded) - existingCount;

        if (requestsToCreate <= 0)
        {
            Console.WriteLine($"Already have {existingCount} seller requests.");

            // Перевіряємо чи всі схвалені заявки мають правильні ролі користувачів
            var approvedRequests = _context.SellerRequests
                .Where(sr => sr.Status == RequestStatus.Approved)
                .ToList();

            foreach (var approvedRequest in approvedRequests)
            {
                var user = users.FirstOrDefault(u => u.Id == approvedRequest.UserId);
                if (user != null && user.Role != Role.SellerOwner)
                {
                    Console.WriteLine($"Updating user {user.Id} role to SellerOwner for approved request");
                    user.UpdateRole(Role.SellerOwner);
                }
            }

            if (approvedRequests.Any())
            {
                _context.SaveChanges();
            }

            return;
        }

        Console.WriteLine($"Creating {requestsToCreate} seller requests.");

        var newRequests = new List<SellerRequest>();
        var admin = users.FirstOrDefault(u => u.Role == Role.Admin);
        var faker = new Faker("uk");

        for (int i = 0; i < requestsToCreate && i < buyerUsers.Count; i++)
        {
            var buyer = buyerUsers[i];

            // Створюємо дані для запиту
            var companyName = faker.Company.CompanyName();
            var slugValue = Regex.Replace(companyName.ToLower(), @"[^a-z0-9-]", "-");
            slugValue = Regex.Replace(slugValue, @"-+", "-").Trim('-');
            if (string.IsNullOrEmpty(slugValue))
            {
                slugValue = "company-" + faker.Random.AlphaNumeric(5).ToLower();
            }

            var companyRequestData = new CompanyRequestData(
                name: companyName,
                slug: slugValue,
                description: faker.Lorem.Paragraph(),
                imageUrl: null, // Не встановлюємо для тестування функціональності завантаження
                contactEmail: faker.Internet.Email(),
                contactPhone: "+380" + faker.Random.Number(10, 99) + faker.Random.Number(1000000, 9999999),
                addressRegion: faker.Address.State(),
                addressCity: faker.Address.City(),
                addressStreet: faker.Address.StreetName(),
                addressPostalCode: faker.Address.ZipCode("#####"),
                metaTitle: companyName + " - Офіційний магазин",
                metaDescription: faker.Lorem.Sentence(),
                metaImageUrl: null // Не встановлюємо для тестування функціональності завантаження
            );

            var financeRequestData = new FinanceRequestData(
                bankAccount: faker.Finance.Account(20),
                bankName: faker.Company.CompanyName() + " Банк",
                bankCode: faker.Random.Number(10000, 99999).ToString(),
                taxId: faker.Random.Number(*********, *********).ToString(),
                paymentDetails: faker.Lorem.Sentence()
            );

            var scheduleRequestData = new ScheduleRequestData(
                new List<DayScheduleData>
                {
                    new DayScheduleData(Day.Monday, TimeSpan.FromHours(9), TimeSpan.FromHours(18), false),
                    new DayScheduleData(Day.Tuesday, TimeSpan.FromHours(9), TimeSpan.FromHours(18), false),
                    new DayScheduleData(Day.Wednesday, TimeSpan.FromHours(9), TimeSpan.FromHours(18), false),
                    new DayScheduleData(Day.Thursday, TimeSpan.FromHours(9), TimeSpan.FromHours(18), false),
                    new DayScheduleData(Day.Friday, TimeSpan.FromHours(9), TimeSpan.FromHours(18), false),
                    new DayScheduleData(Day.Saturday, TimeSpan.FromHours(10), TimeSpan.FromHours(16), false),
                    new DayScheduleData(Day.Sunday, TimeSpan.FromHours(0), TimeSpan.FromHours(0), true)
                }
            );

            var request = new SellerRequest(
                userId: buyer.Id,
                companyRequestData: companyRequestData,
                financeRequestData: financeRequestData,
                scheduleRequestData: scheduleRequestData,
                additionalInfo: faker.Lorem.Paragraph()
            );

            // Розподіл статусів: перші 30 - Approved (для компаній), наступні 10 - Pending, останні 15 - Rejected
            if (i < 30)
            {
                request.Status = RequestStatus.Approved;
                request.ApprovedAt = DateTime.UtcNow.AddDays(-faker.Random.Int(1, 150));
                request.ApprovedByUser = admin?.Id;

                // Змінюємо роль користувача на SellerOwner після схвалення заявки (тільки якщо ще не SellerOwner)
                if (buyer.Role != Role.SellerOwner)
                {
                    buyer.UpdateRole(Role.SellerOwner);
                    Console.WriteLine($"Updated user {buyer.Id} role to SellerOwner");
                }
            }
            else if (i < 40) // 10 pending заявок
            {
                request.Status = RequestStatus.Pending;
                // Користувач залишається Buyer поки заявка не схвалена
            }
            else // 15 rejected заявок
            {
                request.Status = RequestStatus.Rejected;
                request.RejectedAt = DateTime.UtcNow.AddDays(-faker.Random.Int(1, 150));
                request.RejectedByUser = admin?.Id;
                request.RejectionReason = faker.PickRandom(new[] {
                    "Недостатньо документів для підтвердження",
                    "Невірні банківські реквізити",
                    "Неповна інформація про компанію",
                    "Не відповідає вимогам платформи"
                });
                // Користувач залишається Buyer після відхилення заявки
            }

            newRequests.Add(request);
        }

        // Додавання до бази
        _context.SellerRequests.AddRange(newRequests);
        _context.SaveChanges();

        // Оновлюємо користувачів після збереження заявок
        _context.SaveChanges();

        var approvedCount = newRequests.Count(r => r.Status == RequestStatus.Approved);
        var pendingCount = newRequests.Count(r => r.Status == RequestStatus.Pending);
        var rejectedCount = newRequests.Count(r => r.Status == RequestStatus.Rejected);

        Console.WriteLine($"Created {newRequests.Count} seller requests: {approvedCount} approved, {pendingCount} pending, {rejectedCount} rejected.");
    }



    private void CreateCompanySchedulesIfNotExist(List<Company> companies)
    {
        // Перевірка, чи є вже розклади компаній
        var existingCount = _context.CompanySchedules.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} company schedules.");
            return;
        }

        Console.WriteLine("Creating company schedules.");

        // Створюємо розклади для всіх компаній
        var companySchedules = new List<CompanySchedule>();

        foreach (var company in companies)
        {
            // Створюємо розклад для кожного дня тижня
            foreach (Day day in Enum.GetValues<Day>())
            {
                // Визначаємо, чи компанія працює в цей день
                bool isClosed = day == Day.Saturday || day == Day.Sunday || new Random().Next(100) < 10; // Вихідні + 10% випадкових днів

                // Встановлюємо години роботи
                TimeSpan openTime = TimeSpan.FromHours(9); // За замовчуванням 9:00
                TimeSpan closeTime = TimeSpan.FromHours(18); // За замовчуванням 18:00

                // Для робочих днів генеруємо випадкові години
                if (!isClosed)
                {
                    int openHour = new Random().Next(7, 11); // Від 7:00 до 10:00
                    int closeHour = new Random().Next(17, 22); // Від 17:00 до 21:00

                    // Переконуємося, що час закриття більший за час відкриття
                    if (closeHour <= openHour)
                    {
                        closeHour = openHour + 8; // Гарантуємо, що компанія працює принаймні 8 годин
                    }

                    openTime = TimeSpan.FromHours(openHour);
                    closeTime = TimeSpan.FromHours(closeHour);
                }

                // Створюємо об'єкт CompanySchedule, використовуючи конструктор
                var schedule = new CompanySchedule(
                    companyId: company.Id,
                    openTime: openTime,
                    closeTime: closeTime,
                    isClosed: isClosed,
                    day: day
                );

                companySchedules.Add(schedule);
            }
        }

        // Додавання до бази
        _context.CompanySchedules.AddRange(companySchedules);
        _context.SaveChanges();

        Console.WriteLine($"Created {companySchedules.Count} company schedules.");
    }

    private void CreateCompanyUsersIfNotExist(List<Company> companies, List<User> users)
    {
        // Перевірка, чи є вже зв'язки компаній з користувачами
        var existingCount = _context.CompanyUsers.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} company users.");
            return;
        }

        Console.WriteLine("Creating company users.");

        // Створюємо зв'язки для кожної компанії
        var companyUsers = new List<CompanyUser>();

        // Отримуємо схвалені заявки для знаходження власників компаній
        var approvedRequests = _context.SellerRequests
            .Where(sr => sr.Status == RequestStatus.Approved)
            .ToList();

        // Отримуємо Seller користувачів для додаткових працівників
        var sellers = users.Where(u => u.Role == Role.Seller).ToList();

        foreach (var company in companies)
        {
            // Знаходимо схвалену заявку для цієї компанії
            var approvedRequest = approvedRequests.FirstOrDefault(ar =>
                ar.CompanyRequestData.Name == company.Name);

            if (approvedRequest != null)
            {
                // Знаходимо користувача який подав заявку (тепер він SellerOwner)
                var owner = users.FirstOrDefault(u => u.Id == approvedRequest.UserId);
                if (owner != null && owner.Role == Role.SellerOwner)
                {
                    companyUsers.Add(new CompanyUser(
                        companyId: company.Id,
                        userId: owner.Id,
                        isOwner: true
                    ));
                }
            }

            // Додаємо додаткових Seller працівників тільки для деяких компаній (60% шансів)
            if (_random.NextDouble() < 0.6) // 60% компаній матимуть додаткових працівників
            {
                int additionalUserCount = _random.Next(1, 4); // 1-3 додаткових працівників
                var availableSellers = sellers
                    .Where(s => !companyUsers.Any(cu => cu.UserId == s.Id)) // Уникаємо дублювання
                    .OrderBy(u => Guid.NewGuid())
                    .Take(additionalUserCount)
                    .ToList();

                foreach (var user in availableSellers)
                {
                    companyUsers.Add(new CompanyUser(
                        companyId: company.Id,
                        userId: user.Id,
                        isOwner: false
                    ));
                }
            }
        }

        // Додавання до бази
        _context.CompanyUsers.AddRange(companyUsers);
        _context.SaveChanges();

        Console.WriteLine($"Created {companyUsers.Count} company users.");
    }

    private List<Cart> CreateCartsIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже кошики
        var existingCount = _context.Carts.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} carts.");
            return _context.Carts.ToList();
        }

        // Створюємо додаткові кошики, якщо потрібно
        var cartsToCreate = count - existingCount;
        Console.WriteLine($"Creating {cartsToCreate} additional carts.");

        // Отримуємо користувачів, які ще не мають кошиків
        var existingUserIds = _context.Carts.Select(c => c.UserId).ToList();
        var usersWithoutCarts = users.Where(u => !existingUserIds.Contains(u.Id)).ToList();

        // Якщо немає користувачів без кошиків, повертаємо існуючі кошики
        if (usersWithoutCarts.Count == 0)
        {
            Console.WriteLine("All users already have carts.");
            return _context.Carts.ToList();
        }

        // Налаштування генератора фейкових даних для Cart
        var cartFaker = new Faker<Cart>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача без кошика
                var user = f.PickRandom(usersWithoutCarts);
                usersWithoutCarts.Remove(user); // Видаляємо користувача зі списку, щоб уникнути дублікатів

                return new Cart(
                    userId: user.Id
                );
            });

        // Генерація даних
        var newCarts = cartFaker.Generate(Math.Min(cartsToCreate, usersWithoutCarts.Count));

        // Встановлюємо дати створення та оновлення
        foreach (var cart in newCarts)
        {
            cart.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 50) // 50% кошиків оновлені
            {
                cart.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Carts.AddRange(newCarts);
        _context.SaveChanges();

        Console.WriteLine($"Created {newCarts.Count} carts.");

        // Повертаємо всі кошики
        return _context.Carts.ToList();
    }

    private void CreateCartItemsIfNotExist(List<Cart> carts, List<Product> products, int count)
    {
        // Перевірка, чи є вже елементи кошиків
        var existingCount = _context.CartItems.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} cart items.");
            return;
        }

        // Створюємо додаткові елементи кошиків, якщо потрібно
        var cartItemsToCreate = count - existingCount;
        Console.WriteLine($"Creating {cartItemsToCreate} additional cart items.");

        // Налаштування генератора фейкових даних для CartItem
        var cartItemFaker = new Faker<CartItem>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий кошик та продукт
                var cart = f.PickRandom(carts);
                var product = f.PickRandom(products);

                // Генеруємо кількість
                var quantity = (uint)f.Random.Number(1, 10);

                return new CartItem(
                    cartId: cart.Id,
                    productId: product.Id,
                    quantity: quantity
                );
            });

        // Генерація даних
        var newCartItems = new List<CartItem>();

        // Створюємо елементи кошиків, уникаючи дублікатів
        for (int i = 0; i < cartItemsToCreate; i++)
        {
            var cartItem = cartItemFaker.Generate();

            // Перевіряємо, чи вже існує такий елемент
            var exists = _context.CartItems.Any(ci =>
                ci.CartId == cartItem.CartId &&
                ci.ProductId == cartItem.ProductId);

            if (!exists)
            {
                newCartItems.Add(cartItem);
            }
            else
            {
                i--; // Повторюємо спробу
            }
        }

        // Встановлюємо дати створення та оновлення
        foreach (var cartItem in newCartItems)
        {
            cartItem.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 30) // 30% елементів кошиків оновлені
            {
                cartItem.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.CartItems.AddRange(newCartItems);
        _context.SaveChanges();

        Console.WriteLine($"Created {newCartItems.Count} cart items.");
    }

    private void CreateFavoritesIfNotExist(List<User> users, List<Product> products, int count)
    {
        // Перевірка, чи є вже улюблені
        var existingCount = _context.Favorites.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} favorites.");
            return;
        }

        // Створюємо додаткові улюблені, якщо потрібно
        var favoritesToCreate = count - existingCount;
        Console.WriteLine($"Creating {favoritesToCreate} additional favorites.");

        // Налаштування генератора фейкових даних для Favorite
        var favoriteFaker = new Faker<Favorite>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача та продукт
                var user = f.PickRandom(users);
                var product = f.PickRandom(products);

                return new Favorite(
                    userId: user.Id,
                    productId: product.Id
                );
            });

        // Генерація даних
        var newFavorites = favoriteFaker.Generate(favoritesToCreate);

        // Додавання до бази
        _context.Favorites.AddRange(newFavorites);
        _context.SaveChanges();

        Console.WriteLine($"Created {favoritesToCreate} favorites.");
    }

    private void CreateCompanyFinancesIfNotExist(List<Company> companies)
    {
        // Перевірка, чи є вже фінансові налаштування компаній
        var existingCount = _context.CompanyFinances.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} company finances.");
            return;
        }

        Console.WriteLine("Creating company finances.");

        var companyFinances = new List<CompanyFinance>();
        var faker = new Faker("uk");

        foreach (var company in companies)
        {
            // Створюємо фінансові налаштування для кожної компанії
            var companyFinance = new CompanyFinance(
                companyId: company.Id,
                bankAccount: faker.Finance.Account(20),
                bankName: faker.Company.CompanyName() + " Банк",
                bankCode: faker.Random.Number(10000, 99999).ToString(),
                taxId: faker.Random.Number(*********, *********).ToString(),
                paymentDetails: faker.Lorem.Sentence()
            );

            // Встановлюємо дати створення та оновлення
            companyFinance.CreatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 30));
            if (_random.Next(100) < 30) // 30% фінансових налаштувань оновлені
            {
                companyFinance.UpdatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 10));
            }

            companyFinances.Add(companyFinance);
        }

        // Додавання до бази
        _context.CompanyFinances.AddRange(companyFinances);
        _context.SaveChanges();

        Console.WriteLine($"Created {companyFinances.Count} company finances.");
    }


}
