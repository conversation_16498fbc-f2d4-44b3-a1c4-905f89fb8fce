using MediatR;

namespace Marketplace.Application.Queries.Company;

public record GetCompanyRatingQuery(Guid CompanyId) : IRequest<CompanyRatingResponse>;

public record GetCompanyRatingBySlugQuery(string Slug) : IRequest<CompanyRatingResponse>;

public class CompanyRatingResponse
{
    public double AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
}
