import{_ as q,g as m,h as J,f as Q,i as X,c as n,a,k as v,d as o,b as M,w as x,r as Z,t as i,n as g,F as aa,p as sa,m as ea,e as ta,o as l}from"./index-L-hJxM_5.js";import{u as O}from"./users-D6yG63l9.js";import{getRoleKey as L,ROLE_KEYS as f,ROLE_DISPLAY_NAMES as ia}from"./roles-D6TbD4pL.js";const da={class:"admin-page"},na={class:"admin-page-header"},la={class:"admin-page-actions"},oa={key:0,class:"admin-loading-state"},ra={key:1,class:"admin-alert admin-alert-danger"},ma={key:2,class:"admin-user-detail-content"},ca={class:"admin-card"},ua={class:"admin-card-content"},va={class:"admin-user-detail-grid"},ga={class:"admin-detail-section admin-avatar-section"},pa={class:"admin-avatar-display"},ba=["src","alt"],fa={key:1,class:"fas fa-user avatar-placeholder-large"},_a={class:"admin-avatar-info"},ya={class:"admin-avatar-status"},ha={class:"admin-detail-section"},ka={class:"admin-detail-row"},Ca={class:"admin-detail-item"},Pa={class:"admin-detail-value"},Na={class:"admin-detail-item"},wa={class:"admin-detail-value"},Aa={class:"admin-detail-row"},Ua={class:"admin-detail-item"},Ea={class:"admin-detail-value"},Sa={class:"admin-detail-row"},Ra={class:"admin-detail-item"},Da={class:"admin-detail-value"},Ma={class:"admin-detail-item"},xa={class:"admin-detail-value"},Oa={class:"admin-detail-section"},La={class:"admin-detail-grid"},Fa={class:"admin-detail-item"},Ia={class:"admin-detail-value"},Ba={class:"admin-detail-item"},Va={class:"admin-detail-value"},Ta={class:"admin-detail-item"},$a={class:"admin-detail-value"},Ka={class:"admin-badge admin-badge-info"},za={key:0,class:"admin-detail-item"},Ya={class:"admin-detail-value"},Ga={class:"admin-detail-section"},Ha={class:"admin-detail-grid"},Wa={class:"admin-detail-item"},ja={class:"admin-detail-value"},qa={class:"admin-detail-item"},Ja={class:"admin-detail-value"},Qa={class:"admin-badge admin-badge-secondary"},Xa={class:"admin-detail-section"},Za={class:"admin-detail-grid"},as={class:"admin-detail-item"},ss={class:"admin-detail-value"},es={class:"admin-card"},ts={class:"admin-card-header"},is={class:"admin-card-actions"},ds={class:"admin-results-count"},ns={class:"admin-card-content"},ls={key:0,class:"admin-loading-state"},os={key:1,class:"admin-orders-list"},rs={class:"admin-order-header"},ms={class:"admin-order-info"},cs=["title"],us={class:"admin-order-date"},vs={class:"admin-order-statuses"},gs={class:"admin-order-status"},ps={class:"admin-payment-status"},bs={class:"admin-order-details"},fs={class:"admin-order-amount"},_s={class:"admin-order-actions"},ys={key:0,class:"admin-pagination"},hs=["disabled"],ks={class:"admin-pagination-info"},Cs=["disabled"],Ps={key:2,class:"admin-empty-state"},Ns={class:"modal-content"},ws={class:"modal-header"},As={class:"modal-title"},Us={class:"modal-body text-center"},Es={key:0,class:"avatar-viewer"},Ss=["src","alt"],Rs={class:"mt-3"},Ds={class:"text-muted"},Ms={__name:"UserDetail",setup(xs){const F=Q(),I=ta(),h=m(!1),_=m(null),t=m(null),p=m(0),c=m([]),k=m(!1),d=m({page:1,limit:10,total:0,totalPages:1}),C=m(!1),y=J(()=>F.params.id),w=async()=>{h.value=!0,_.value=null;try{const e=await O.getUserById(y.value);t.value=e,await P()}catch(e){console.error("Error loading user:",e),_.value="Failed to load user data. Please try again."}finally{h.value=!1}},P=async(e=1)=>{k.value=!0;try{console.log("Loading orders for user:",y.value);const s=await O.getUserOrders(y.value,{page:e,pageSize:d.value.limit,orderBy:"CreatedAt",descending:!0});console.log("📦 Orders response:",s),s&&s.orders&&s.pagination?(c.value=s.orders||[],d.value={page:s.pagination.page||1,limit:s.pagination.limit||10,total:s.pagination.total||0,totalPages:s.pagination.totalPages||1},p.value=s.pagination.total||0,console.log("✅ Orders loaded successfully:",{ordersCount:c.value.length,pagination:d.value,totalOrders:p.value})):(console.log("⚠️ Unexpected response format, setting defaults"),c.value=[],d.value={page:1,limit:10,total:0,totalPages:1},p.value=0)}catch(s){console.error("Error loading orders:",s),c.value=[],d.value={page:1,limit:10,total:0,totalPages:1},p.value=0}finally{k.value=!1}},B=()=>{I.push("/admin/users")},A=()=>{var e;(e=t.value)!=null&&e.avatarUrl&&(C.value=!0)},U=()=>{C.value=!1},V=e=>{e.target.style.display="none";const s=e.target.nextElementSibling;s&&(s.style.display="block")},T=e=>{const s=L(e);return{[f.ADMIN]:"admin-badge-danger",[f.MODERATOR]:"admin-badge-warning",[f.SELLER_OWNER]:"admin-badge-info",[f.SELLER]:"admin-badge-primary",[f.BUYER]:"admin-badge-secondary"}[s]||"admin-badge-secondary"},$=e=>{const s=L(e);return ia[s]||e},K=e=>({0:"Male",1:"Female",null:"Not specified",undefined:"Not specified"})[e]||"Not specified",z=e=>({0:"Ukrainian",1:"English",2:"Russian",null:"Not specified",undefined:"Not specified"})[e]||"Not specified",N=e=>e?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(e)):"N/A",Y=e=>({0:"admin-badge-info",1:"admin-badge-warning",2:"admin-badge-primary",3:"admin-badge-success",4:"admin-badge-danger",Processing:"admin-badge-info",Pending:"admin-badge-warning",Shipped:"admin-badge-primary",Delivered:"admin-badge-success",Cancelled:"admin-badge-danger"})[e]||"admin-badge-secondary",G=e=>({0:"Processing",1:"Pending",2:"Shipped",3:"Delivered",4:"Cancelled",Processing:"Processing",Pending:"Pending",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled"})[e]||e||"Unknown",H=e=>({0:"admin-badge-warning",1:"admin-badge-success",2:"admin-badge-info",3:"admin-badge-danger",Pending:"admin-badge-warning",Completed:"admin-badge-success",Refunded:"admin-badge-info",Failed:"admin-badge-danger"})[e]||"admin-badge-secondary",W=e=>({0:"Pending",1:"Completed",2:"Refunded",3:"Failed",Pending:"Pending",Completed:"Completed",Refunded:"Refunded",Failed:"Failed"})[e]||e||"Pending",j=e=>{var u;const s=e.totalPriceAmount||e.totalAmount||e.total||((u=e.totalPrice)==null?void 0:u.amount)||e.totalPrice||0;return Number(s).toFixed(2)};return X(()=>{w()}),(e,s)=>{var E,S,R,D;const u=Z("router-link");return l(),n("div",da,[a("div",na,[a("div",{class:"admin-page-title-section"},[a("button",{onClick:B,class:"admin-btn admin-btn-ghost admin-btn-sm"},s[3]||(s[3]=[a("i",{class:"fas fa-arrow-left"},null,-1),o(" Back to Users ")])),s[4]||(s[4]=a("h1",{class:"admin-page-title"},[a("i",{class:"fas fa-user admin-page-icon"}),o(" User Details ")],-1)),s[5]||(s[5]=a("p",{class:"admin-page-subtitle"},"View and manage user information",-1))]),a("div",la,[M(u,{to:`/admin/users/${y.value}/edit`,class:"admin-btn admin-btn-primary"},{default:x(()=>s[6]||(s[6]=[a("i",{class:"fas fa-edit"},null,-1),o(" Edit User ")])),_:1},8,["to"])])]),h.value?(l(),n("div",oa,s[7]||(s[7]=[a("div",{class:"admin-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-loading-text"},"Loading user data...",-1)]))):_.value?(l(),n("div",ra,[s[10]||(s[10]=a("i",{class:"fas fa-exclamation-triangle"},null,-1)),a("div",null,[s[8]||(s[8]=a("strong",null,"Error loading user data",-1)),a("p",null,i(_.value),1)]),a("button",{onClick:w,class:"admin-btn admin-btn-sm admin-btn-danger"},s[9]||(s[9]=[a("i",{class:"fas fa-retry"},null,-1),o(" Retry ")]))])):t.value?(l(),n("div",ma,[a("div",ca,[s[30]||(s[30]=a("div",{class:"admin-card-header"},[a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-user-circle"}),o(" User Information ")])],-1)),a("div",ua,[a("div",va,[a("div",ga,[s[12]||(s[12]=a("h4",{class:"admin-detail-section-title"},[a("i",{class:"fas fa-user-circle me-2"}),o(" User Avatar ")],-1)),a("div",pa,[a("div",{class:g(["admin-user-avatar-large",{"has-image":t.value.avatarUrl}]),onClick:A},[t.value.avatarUrl?(l(),n("img",{key:0,src:t.value.avatarUrl,alt:`${t.value.username} avatar`,class:"avatar-image-large",onError:V},null,40,ba)):(l(),n("i",fa))],2),a("div",_a,[a("p",ya,i(t.value.avatarUrl?"Avatar uploaded":"No avatar"),1),t.value.avatarUrl?(l(),n("button",{key:0,class:"admin-btn admin-btn-sm admin-btn-secondary",onClick:A},s[11]||(s[11]=[a("i",{class:"fas fa-eye me-1"},null,-1),o(" View Full Size ")]))):v("",!0)])])]),a("div",ha,[s[29]||(s[29]=a("h4",{class:"admin-detail-section-title"},"Basic Information",-1)),a("div",ka,[a("div",Ca,[s[13]||(s[13]=a("label",{class:"admin-detail-label"},"Username",-1)),a("div",Pa,i(t.value.username),1)]),a("div",Na,[s[14]||(s[14]=a("label",{class:"admin-detail-label"},"Email",-1)),a("div",wa,i(t.value.email),1)])]),a("div",Aa,[a("div",Ua,[s[15]||(s[15]=a("label",{class:"admin-detail-label"},"Role",-1)),a("div",Ea,[a("span",{class:g(["admin-badge",T(t.value.role)])},i($(t.value.role)),3)])]),s[16]||(s[16]=a("div",{class:"admin-detail-item"},[a("label",{class:"admin-detail-label"},"Status"),a("div",{class:"admin-detail-value"},[a("span",{class:"admin-badge admin-badge-success"},"Active")])],-1))]),a("div",Sa,[a("div",Ra,[s[17]||(s[17]=a("label",{class:"admin-detail-label"},"Email Confirmed",-1)),a("div",Da,[a("span",{class:g(["admin-badge",t.value.emailConfirmed?"admin-badge-success":"admin-badge-warning"])},i(t.value.emailConfirmed?"Confirmed":"Not Confirmed"),3)])]),a("div",Ma,[s[18]||(s[18]=a("label",{class:"admin-detail-label"},"Approved",-1)),a("div",xa,[a("span",{class:g(["admin-badge",t.value.isApproved?"admin-badge-success":"admin-badge-warning"])},i(t.value.isApproved?"Approved":"Not Approved"),3)])])]),a("div",Oa,[s[23]||(s[23]=a("h4",{class:"admin-detail-section-title"},[a("i",{class:"fas fa-user"}),o(" Personal Information ")],-1)),a("div",La,[a("div",Fa,[s[19]||(s[19]=a("label",{class:"admin-detail-label"},"First Name",-1)),a("div",Ia,i(t.value.firstName||"Not specified"),1)]),a("div",Ba,[s[20]||(s[20]=a("label",{class:"admin-detail-label"},"Last Name",-1)),a("div",Va,i(t.value.lastName||"Not specified"),1)]),a("div",Ta,[s[21]||(s[21]=a("label",{class:"admin-detail-label"},"Gender",-1)),a("div",$a,[a("span",Ka,i(K(t.value.gender)),1)])]),t.value.birthday?(l(),n("div",za,[s[22]||(s[22]=a("label",{class:"admin-detail-label"},"Birthday",-1)),a("div",Ya,i(N(t.value.birthday)),1)])):v("",!0)])]),a("div",Ga,[s[26]||(s[26]=a("h4",{class:"admin-detail-section-title"},[a("i",{class:"fas fa-address-book"}),o(" Contact Information ")],-1)),a("div",Ha,[a("div",Wa,[s[24]||(s[24]=a("label",{class:"admin-detail-label"},"Phone",-1)),a("div",ja,i(t.value.phone||"Not specified"),1)]),a("div",qa,[s[25]||(s[25]=a("label",{class:"admin-detail-label"},"Language",-1)),a("div",Ja,[a("span",Qa,i(z(t.value.language)),1)])])])]),a("div",Xa,[s[28]||(s[28]=a("h4",{class:"admin-detail-section-title"},[a("i",{class:"fas fa-cog"}),o(" System Information ")],-1)),a("div",Za,[a("div",as,[s[27]||(s[27]=a("label",{class:"admin-detail-label"},"Registered",-1)),a("div",ss,i(N(t.value.createdAt||t.value.emailConfirmedAt)),1)])])])])])])]),a("div",es,[a("div",ts,[s[31]||(s[31]=a("h3",{class:"admin-card-title"},[a("i",{class:"fas fa-shopping-cart"}),o(" Order History ")],-1)),a("div",is,[a("span",ds,i(p.value)+" orders found",1)])]),a("div",ns,[k.value?(l(),n("div",ls,s[32]||(s[32]=[a("div",{class:"admin-spinner"},[a("i",{class:"fas fa-spinner fa-pulse"})],-1),a("p",{class:"admin-loading-text"},"Loading orders...",-1)]))):c.value&&c.value.length>0?(l(),n("div",os,[(l(!0),n(aa,null,sa(c.value,r=>(l(),n("div",{key:r.id,class:"admin-order-item"},[a("div",rs,[a("div",ms,[a("h4",{class:"admin-order-number",title:r.id},"#"+i(r.id.substring(0,8))+"...",9,cs),a("span",us,i(N(r.createdAt)),1)]),a("div",vs,[a("div",gs,[s[33]||(s[33]=a("span",{class:"admin-status-label"},"Order Status:",-1)),a("span",{class:g(["admin-badge",Y(r.status)])},i(G(r.status)),3)]),a("div",ps,[s[34]||(s[34]=a("span",{class:"admin-status-label"},"Payment:",-1)),a("span",{class:g(["admin-badge",H(r.paymentStatus)])},i(W(r.paymentStatus)),3)])])]),a("div",bs,[a("div",fs,[s[35]||(s[35]=a("span",{class:"admin-amount-label"},"Total:",-1)),a("strong",null,"$"+i(j(r)),1)]),a("div",_s,[M(u,{to:`/admin/orders/${r.id}`,class:"admin-btn admin-btn-sm admin-btn-primary"},{default:x(()=>s[36]||(s[36]=[a("i",{class:"fas fa-eye"},null,-1),o(" View Details ")])),_:2},1032,["to"])])])]))),128)),d.value&&d.value.totalPages>1?(l(),n("div",ys,[a("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",disabled:!d.value||d.value.page<=1,onClick:s[0]||(s[0]=r=>{var b;return P((((b=d.value)==null?void 0:b.page)||1)-1)})}," Previous ",8,hs),a("span",ks," Page "+i(((E=d.value)==null?void 0:E.page)||1)+" of "+i(((S=d.value)==null?void 0:S.totalPages)||1),1),a("button",{class:"admin-btn admin-btn-sm admin-btn-secondary",disabled:!d.value||d.value.page>=d.value.totalPages,onClick:s[1]||(s[1]=r=>{var b;return P((((b=d.value)==null?void 0:b.page)||1)+1)})}," Next ",8,Cs)])):v("",!0)])):(l(),n("div",Ps,s[37]||(s[37]=[a("div",{class:"admin-empty-icon"},[a("i",{class:"fas fa-shopping-cart"})],-1),a("h3",{class:"admin-empty-title"},"No Orders Found",-1),a("p",{class:"admin-empty-message"}," This user hasn't placed any orders yet. ",-1)])))])])])):v("",!0),C.value?(l(),n("div",{key:3,class:"modal fade show d-block",onClick:U},[a("div",{class:"modal-dialog modal-lg",onClick:s[2]||(s[2]=ea(()=>{},["stop"]))},[a("div",Ns,[a("div",ws,[a("h5",As,[s[38]||(s[38]=a("i",{class:"fas fa-user me-2"},null,-1)),o(" "+i((R=t.value)==null?void 0:R.username)+" - Avatar ",1)]),a("button",{type:"button",class:"btn-close",onClick:U})]),a("div",Us,[(D=t.value)!=null&&D.avatarUrl?(l(),n("div",Es,[a("img",{src:t.value.avatarUrl,alt:`${t.value.username} avatar`,class:"img-fluid rounded",style:{"max-height":"500px"}},null,8,Ss),a("div",Rs,[a("h6",null,i(t.value.username),1),a("small",Ds,i(t.value.email),1)])])):v("",!0)])])])])):v("",!0)])}}},Is=q(Ms,[["__scopeId","data-v-5f5171cc"]]);export{Is as default};
