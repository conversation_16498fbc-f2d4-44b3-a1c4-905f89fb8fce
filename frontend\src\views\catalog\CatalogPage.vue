<template>
  <div class="electronics-page">
    <!-- Breadcrumbs -->
    <div class="breadcrumbs">
      <span class="fas fa-house"></span>
      <span class="separator">/</span>
      <span v-if="isSearchMode">Пошук</span>
      <template v-else>
        <span v-for="parent in categoryParents" :key="parent.id" >{{ parent.name }}
        <span class="separator">/</span>
        </span>
        <span v-if="categoryName">{{ categoryName }}</span>
      </template>
    </div>
    
    <!-- Page Header -->
    <h1 v-if="isSearchMode" class="page-title">Результати пошуку: "{{ searchQuery }}"</h1>
    <h1 v-else-if="categoryName" class="page-title">{{ categoryName }}</h1>
    <div class="page-stats">
      <span v-if="filteredCount > 0">
        Знайдено {{ filteredCount }} товарів
        <span v-if="Object.keys(activeFilters).length > 0" class="filter-indicator">
          з {{ allProducts.length }} (з фільтрами)
        </span>
      </span>
      <span v-else-if="Object.keys(activeFilters).length > 0" class="no-results">
        Немає товарів за вибраними фільтрами
      </span>

      <!-- Активні фільтри -->
      <div v-if="Object.keys(activeFilters).length > 0" class="active-filters-display">
        <span class="active-filters-label">Активні фільтри:</span>
        <div class="active-filters-tags">
          <template v-for="(values, category) in activeFilters" :key="category">
            <span
              v-for="value in values"
              :key="`${category}-${value}`"
              class="filter-tag"
            >
              {{ category }}: {{ value }}
              <button @click="removeFilter(category, value)" class="remove-filter-btn">×</button>
            </span>
          </template>
        </div>
      </div>

      <div class="sort-container">
        <span>За популярністю</span>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 9l6 6 6-6"></path>
        </svg>
      </div>
    </div>
    
    <!-- Filters and Products -->
    <div class="content-container">
        <div class="filters-section">
          <div class="filters-header" v-if="Object.keys(activeFilters).length > 0">
            <span class="active-filters-count">
              Активних фільтрів: {{ Object.keys(activeFilters).length }}
            </span>
            <button @click="clearAllFilters" class="clear-filters-btn">
              Очистити всі фільтри
            </button>
          </div>
          <FiltersSidebar
          :filterList="filters"
          @filter-changed="handleFilterChange"
          ref="filtersSidebar"
          />
        </div>

      <!-- Products Grid -->
      <div class="products-grid" :class="{ 'search-mode': isSearchMode }">
        <ProductGrid
          :products="products"
          :loading="loading"
          :error="error"
          :show-pagination="true"
          :current-page="currentPage"
          :total-items="totalCount"
          :page-size="itemsPerPage"
          :grid-columns="4"
          :auto-fetch="false"
          @page-changed="handlePageChange"
          @product-added-to-cart="handleProductAddedToCart"
          @product-added-to-wishlist="handleProductAddedToWishlist"
        />
      </div>
    </div>

    <section class="recommended-products-section">
      <div class="container">
        <RecommendedProducts
          title="Рекомендовані товари"
          :fetch-params="{ type: 'recommended', status: 1, pageSize: 8 }"
        />
      </div>
    </section>
    
    <!-- Category Reviews Section -->
    <section class="category-reviews-section">
      <div class="container">
        <h2 class="section-title">Відгуки покупців</h2>
        <ReviewGrid
          :api-endpoint="`/api/categories/${categorySlug}/reviews`"
          :auto-fetch="true"
          :show-pagination="true"
          :page-size="6"
          :show-user-info="true"
          empty-message="Немає відгуків для товарів цієї категорії"
        />
      </div>
    </section>
 </div>
</template>

<script setup>
import ProductGrid from '../../components/common/ProductGrid.vue';
import FiltersSidebar from '../../components/catalog/FiltersSidebar.vue';
import RecommendedProducts from '../../components/home/<USER>';
import ReviewGrid from '../../components/common/RatingGrid.vue';
import RandomProducts from '../../components/home/<USER>';
</script>

<script>
import categoryService from '@/services/category.service';
import productService from '@/services/product.service';
import reviewService from '@/services/review.service';

export default
{
  components: {
    ProductGrid,
    FiltersSidebar,
    RecommendedProducts,
    ReviewGrid,
    RandomProducts
  },
  data()
  {
    return {
      products: [],
      allProducts: [], // Зберігаємо всі продукти для фільтрації
      recommendedProducts: [],
      categoryParents: [],
      filters: {},
      activeFilters: {}, // Активні фільтри
      currentPage: 1,
      itemsPerPage: 24,
      totalCount: 0,
      filteredCount: 0, // Кількість відфільтрованих продуктів
      categorySlug: this.$route.params.slug,
      categoryName: '',
      parent: '',
      error: null,
      loading: false,
      randomMode: false,
      searchQuery: this.$route.query.q || '',
    };
  },
  computed: {
    isSearchMode() {
      return this.$route.name === 'Search' && this.searchQuery;
    }
  },
  watch: {
    '$route'(to, from) {
      // Оновлюємо дані при зміні маршруту
      this.categorySlug = to.params.slug;
      this.searchQuery = to.query.q || '';

      if (to.name === 'Search' && this.searchQuery) {
        this.performSearch();
      } else if (to.name === 'Catalog') {
        this.fetchProducts();
        this.fetchAllCount();
        this.fetchFilters();
        this.fetchCategoryInfo();
      }
    }
  },
  async mounted()
  {
    if (this.isSearchMode) {
      await this.performSearch();
    } else {
      await this.fetchProducts();
      await this.fetchAllCount();
      await this.fetchFilters();
      await this.fetchCategoryInfo();
    }
    await this.fetchRecommendedProducts();
    //await this.fetchCategoryReviews();
    // if(this.error != null)
    // {
    //   this.$router.push("/");
    // }
  },
  methods: {
    async fetchAllCount(params = {status: 1})
    {
      // Цей метод тепер не потрібен, оскільки кількість обчислюється в applyFilters
      // Залишаємо для сумісності
      try {
        // Кількість буде оновлена в applyFilters
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load product count. Please try again.';
        console.error(error);
      }
    },
    async fetchCategoryInfo(params) {
      try {
        const response = await categoryService.getBySlug(this.categorySlug, params);
        console.log(response);
        this.categoryName = response.data.name;
        if(response.data.parentId != null)
        {
          this.getParents(response.data.parentId);
        }
        this.error = null;
      } catch(error) {
        this.error = 'Failed to load category info. Please try again.';
        console.error(error);
      }
    },
    async getParents(parentId)
    {
        try {
          const response = await categoryService.getById(parentId);
          this.categoryParents.unshift(response.data);
          this.error = null;
          if(response.data.parentId != null)
          {
            this.getParents(response.data.parentId);
          }
        } catch(error) {
          this.error = 'Failed to load parent category. Please try again.';
          console.error(error);
        }
    },
    async fetchProducts(params = {page: 1, pageSize: 1000, status:1}) {
      try {
        this.loading = true;
        this.error = null;

        const response = await categoryService.getProducts(this.categorySlug, params);
        console.log('Products response:', response);

        // Зберігаємо всі продукти для фільтрації
        this.allProducts = response.data.data || [];

        // Застосовуємо фільтри
        this.applyFilters();

      } catch (error) {
        this.error = 'Failed to load products. Please try again.';
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    async fetchFilters() {
      try {
        console.log('Fetching filters for category:', this.categorySlug);
        const response = await productService.getFilters(this.categorySlug);
        console.log('Filters API response:', response);
        console.log('Response status:', response.status);
        console.log('Response data:', response.data);

        this.filters = response.data || {};
        console.log('Filters data assigned:', this.filters);
        console.log('Filters keys:', Object.keys(this.filters));
        this.error = null;
      } catch(error) {
        this.error = 'Failed to load filters. Please try again.';
        console.error('Filters fetch error:', error);
        console.error('Error response:', error.response);
      }
    },
    async fetchRecommendedProducts(params = {page: 1, pageSize: 4}) {
      try {
        const response = await categoryService.getProducts(this.categorySlug, params);
        this.recommendedProducts = response.data.data;
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load products. Please try again.';
        console.error(error);
      }
    },
    async fetchCategoryReviews(params = {page: 1, pageSize: 5}) {
      try {
        const response = await reviewService.getCategoryReviews(this.categorySlug, params);
        this.categoryReviews = response.data.data || [];
        this.error = null;
      } catch (error) {
        this.error = 'Failed to load category reviews. Please try again.';
        console.error(error);
      }
    },
    handlePageChange(newPage) {
      this.currentPage = newPage;
      if (this.isSearchMode) {
        this.performSearch();
      } else {
        this.applyFilters();
      }
    },

    handleFilterChange(activeFilters) {
      console.log('=== HANDLE FILTER CHANGE ===');
      console.log('Active filters received:', activeFilters);
      console.log('Previous activeFilters:', this.activeFilters);
      this.activeFilters = activeFilters;
      console.log('New activeFilters set to:', this.activeFilters);
      this.currentPage = 1; // Скидаємо на першу сторінку при зміні фільтрів
      this.applyFilters();
    },

    applyFilters() {
      console.log('=== APPLY FILTERS START ===');
      console.log('allProducts:', this.allProducts.length);
      console.log('activeFilters:', this.activeFilters);
      console.log('activeFilters keys:', Object.keys(this.activeFilters));

      let filteredProducts = [...this.allProducts];
      console.log('Initial filteredProducts:', filteredProducts.length);

      // Застосовуємо фільтри
      if (Object.keys(this.activeFilters).length > 0) {
        console.log('Applying filters:', this.activeFilters);

        filteredProducts = filteredProducts.filter(product => {
          const matches = this.matchesFilters(product, this.activeFilters);
          console.log(`Product "${product.name}" matches filters:`, matches);
          return matches;
        });

        console.log('After filtering:', filteredProducts.length);
      } else {
        console.log('No active filters, showing all products');
      }

      // Зберігаємо загальну кількість відфільтрованих продуктів
      this.filteredCount = filteredProducts.length;
      this.totalCount = filteredProducts.length;

      // Застосовуємо пагінацію
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      this.products = filteredProducts.slice(startIndex, endIndex);

      console.log(`Final result: ${this.filteredCount} total, showing ${this.products.length} on page ${this.currentPage}`);
      console.log('Final products array:', this.products.map(p => p.name));
      console.log('=== APPLY FILTERS END ===');
    },

    matchesFilters(product, filters) {
      // Перевіряємо кожен активний фільтр
      for (const [filterCategory, selectedValues] of Object.entries(filters)) {
        if (selectedValues.length === 0) continue;

        let productMatches = false;

        if (filterCategory === 'Компанії') {
          // Спеціальна логіка для компаній
          productMatches = selectedValues.includes(product.company?.name);
          console.log(`Company filter: ${product.company?.name} in [${selectedValues.join(', ')}] = ${productMatches}`);
        } else {
          // Загальна логіка для атрибутів
          if (product.attributes && product.attributes[filterCategory]) {
            const productValue = product.attributes[filterCategory];
            productMatches = selectedValues.includes(productValue);
            console.log(`Attribute filter ${filterCategory}: ${productValue} in [${selectedValues.join(', ')}] = ${productMatches}`);
          } else {
            console.log(`Product has no attribute: ${filterCategory}`);
          }
        }

        // Якщо продукт не відповідає хоча б одному фільтру, виключаємо його
        if (!productMatches) {
          console.log(`Product ${product.name} excluded by filter ${filterCategory}`);
          return false;
        }
      }

      return true;
    },

    clearAllFilters() {
      this.activeFilters = {};
      this.currentPage = 1;

      // Очищаємо фільтри в компоненті FiltersSidebar
      if (this.$refs.filtersSidebar) {
        this.$refs.filtersSidebar.clearAllFilters();
      }

      this.applyFilters();
    },

    removeFilter(category, value) {
      if (this.activeFilters[category]) {
        const updatedValues = this.activeFilters[category].filter(v => v !== value);

        if (updatedValues.length === 0) {
          // Видаляємо категорію повністю, якщо немає значень
          const updatedFilters = { ...this.activeFilters };
          delete updatedFilters[category];
          this.activeFilters = updatedFilters;
        } else {
          // Оновлюємо значення для категорії
          this.activeFilters = {
            ...this.activeFilters,
            [category]: updatedValues
          };
        }

        // Оновлюємо FiltersSidebar
        if (this.$refs.filtersSidebar) {
          this.$refs.filtersSidebar.updateSelectedFilters(this.activeFilters);
        }

        this.applyFilters();
      }
    },

    toggleRandomMode() {
      this.randomMode = !this.randomMode;
    },

    async performSearch() {
      try {
        this.loading = true;
        this.error = null;

        // Завантажуємо всі продукти для пошуку (без фільтрів)
        const searchResponse = await productService.searchProducts({
          query: this.searchQuery,
          page: 1,
          pageSize: 1000, // Завантажуємо багато продуктів для фільтрації
          status: 1
        });

        this.allProducts = searchResponse.data.data || [];

        // Завантажуємо глобальні фільтри для пошуку
        await this.fetchSearchFilters();

        // Застосовуємо фільтри до результатів пошуку
        this.applyFilters();

      } catch (error) {
        this.error = 'Помилка при пошуку товарів. Спробуйте ще раз.';
        console.error('Search error:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchSearchFilters() {
      try {
        // Для пошуку створюємо фільтри на основі знайдених продуктів
        const filters = {};

        // Збираємо унікальні категорії з результатів пошуку
        const categories = [...new Set(this.allProducts.map(p => p.categoryName).filter(Boolean))];
        if (categories.length > 0) {
          filters['Категорія'] = categories.map(name => ({
            name,
            count: this.allProducts.filter(p => p.categoryName === name).length
          }));
        }

        // Збираємо унікальні бренди
        const brands = [...new Set(this.allProducts.map(p => p.brand).filter(Boolean))];
        if (brands.length > 0) {
          filters['Бренд'] = brands.map(name => ({
            name,
            count: this.allProducts.filter(p => p.brand === name).length
          }));
        }

        // Створюємо ціновий діапазон
        const prices = this.allProducts.map(p => p.price).filter(p => p > 0);
        if (prices.length > 0) {
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);
          filters['Ціна'] = {
            min: minPrice,
            max: maxPrice,
            type: 'range'
          };
        }

        this.filters = filters;

      } catch (error) {
        console.error('Error fetching search filters:', error);
        this.filters = {};
      }
    },

    handleProductAddedToCart(product) {
      console.log('Product added to cart:', product);
    },

    handleProductAddedToWishlist(product) {
      console.log('Product added to wishlist:', product);
    }
  }
};
</script>

<style scoped>

.recommended-products-section {
  margin-top: 48px;
  margin-bottom: 48px;
}

.category-reviews-section {
  margin-top: 48px;
  margin-bottom: 48px;
}

.products-grid {
  border-left: solid #ABAAAA 2px;
  padding: 15px;
}

.electronics-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.breadcrumbs {
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.breadcrumbs a {
  color: #666;
  text-decoration: none;
}

.page-title {
  font-size: 28px;
  margin-bottom: 10px;
}

.page-stats {
  display: flex;
  border-bottom: solid #ABAAAA 2px;
  justify-content: space-between;
  margin-bottom: 20px;
  font-size: 14px;
}

.filter-indicator {
  color: #666;
  font-style: italic;
  font-size: 12px;
}

.no-results {
  color: #999;
  font-style: italic;
}

.sort-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.content-container {
  display: flex;
  gap: 20px;
}

.filters-section {
  width: 250px;
  flex-shrink: 0;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.active-filters-count {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.clear-filters-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 9px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-filters-btn:hover {
  background-color: #c82333;
}

.active-filters-display {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.active-filters-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.active-filters-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  background-color: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.remove-filter-btn {
  background: none;
  border: none;
  color: white;
  margin-left: 4px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-filter-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.filters-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.filter-group {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.filter-title {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: bold;
}

.filter-dropdown select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
}

.filter-search input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.price-inputs input {
  width: 70px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.ok-btn {
  padding: 6px 10px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.price-slider {
  position: relative;
  height: 20px;
  margin-bottom: 10px;
}

.price-range {
  font-size: 14px;
  color: #666;
}

.new-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ff7700;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.separator {
  margin: 0 8px;
  color: #ccc;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 12px;
}

.stars {
  color: #ff7700;
}

.reviews-count {
  color: #666;
}

.pagination {
  grid-column: 1 / -1;
  border-top: solid #ABAAAA 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  gap: 8px;
  margin-top: 30px;
}

.page-btn, .prev-btn, .next-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn.active {
  background: #ff7700;
  color: white;
  border-color: #ff7700;
}

/* Products Header */
.products-header {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.random-mode-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.random-mode-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.random-mode-btn.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.random-mode-btn.active:hover {
  box-shadow: 0 6px 20px rgba(245, 87, 108, 0.4);
}

/* Search Mode Styles */
.products-grid.search-mode {
  width: 100%;
  max-width: none;
}

.content-container:has(.products-grid.search-mode) {
  display: block;
}

.content-container:has(.products-grid.search-mode) .products-grid {
  margin-left: 0;
}
</style>
