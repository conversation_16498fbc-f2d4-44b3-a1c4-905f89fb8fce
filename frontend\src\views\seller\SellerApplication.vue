<template>
  <div class="seller-application">
    <div class="container">
      <!-- Header -->
      <div class="application-header">
        <h1 class="application-title">
          <i class="fas fa-store"></i>
          Заявка на становлення продавцем
        </h1>
        <p class="application-subtitle">
          Заповніть форму нижче, щоб подати заявку на отримання статусу продавця на нашій платформі
        </p>
      </div>

      <!-- Existing Request Notice -->
      <div v-if="existingRequest" class="existing-request-notice">
        <div class="notice-content">
          <i class="fas fa-info-circle"></i>
          <div class="notice-text">
            <h3>У вас вже є активна заявка</h3>
            <p>Статус: <span :class="getStatusClass(existingRequest.status)">{{ getStatusText(existingRequest.status) }}</span></p>
            <p>Подано: {{ formatDate(existingRequest.createdAt) }}</p>
          </div>
        </div>
        <button v-if="existingRequest.status === 'Rejected'" @click="showForm = true" class="btn btn-primary">
          Подати нову заявку
        </button>
      </div>

      <!-- Application Form -->
      <div v-if="!existingRequest || showForm" class="application-form">
        <form @submit.prevent="submitApplication">
          <!-- Company Information -->
          <div class="form-section">
            <h2 class="section-title">
              <i class="fas fa-building"></i>
              Інформація про компанію
            </h2>
            
            <div class="form-grid">
              <div class="form-group full-width">
                <label for="companyName" class="form-label">Назва компанії *</label>
                <input
                  id="companyName"
                  v-model="form.companyName"
                  type="text"
                  class="form-input"
                  required
                  placeholder="Введіть назву вашої компанії"
                />
              </div>

              <!-- <div class="form-group">
                <label for="companySlug" class="form-label">URL компанії *</label>
                <input
                  id="companySlug"
                  v-model="form.companySlug"
                  type="text"
                  class="form-input"
                  required
                  placeholder="company-url"
                  @input="generateSlug"
                />
                <small class="form-hint">Буде використовуватися в URL: /companies/{{ form.companySlug }}</small>
              </div> -->

              <div class="form-group full-width">
                <label for="companyDescription" class="form-label">Опис компанії *</label>
                <textarea
                  id="companyDescription"
                  v-model="form.companyDescription"
                  class="form-textarea"
                  rows="4"
                  required
                  placeholder="Розкажіть про вашу компанію, її діяльність та цілі"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="companyLogo" class="form-label">Логотип компанії</label>
                <div class="file-upload-container">
                  <input
                    ref="companyLogoInput"
                    id="companyLogo"
                    type="file"
                    accept="image/*"
                    @change="handleLogoUpload"
                    class="file-input"
                  />
                  <button
                    type="button"
                    @click="$refs.companyLogoInput.click()"
                    class="file-upload-btn"
                  >
                    <i class="fas fa-upload"></i>
                    {{ form.companyLogoFile ? 'Змінити логотип' : 'Вибрати логотип' }}
                  </button>
                  <span v-if="form.companyLogoFile" class="file-name">
                    {{ form.companyLogoFile.name }}
                  </span>
                </div>
                <small class="form-hint">Завантажте зображення логотипу компанії (JPG, PNG, макс. 5MB)</small>
              </div>

              <div class="form-group">
                <label for="metaTitle" class="form-label">Meta Title *</label>
                <input
                  id="metaTitle"
                  v-model="form.metaTitle"
                  type="text"
                  class="form-input"
                  :required="form.isCompanyRepresentative"
                  maxlength="100"
                  placeholder="SEO заголовок для сторінки компанії"
                />
                <small class="form-hint">Максимум 100 символів</small>
              </div>

              <div class="form-group">
                <label for="metaDescription" class="form-label">Meta Description *</label>
                <textarea
                  id="metaDescription"
                  v-model="form.metaDescription"
                  class="form-textarea"
                  rows="3"
                  :required="form.isCompanyRepresentative"
                  maxlength="200"
                  placeholder="SEO опис для сторінки компанії"
                ></textarea>
                <small class="form-hint">Максимум 200 символів</small>
              </div>

              <div class="form-group">
                <label for="metaImage" class="form-label">Meta зображення</label>
                <div class="file-upload-container">
                  <input
                    ref="metaImageInput"
                    id="metaImage"
                    type="file"
                    accept="image/*"
                    @change="handleMetaImageUpload"
                    class="file-input"
                  />
                  <button
                    type="button"
                    @click="$refs.metaImageInput.click()"
                    class="file-upload-btn"
                  >
                    <i class="fas fa-upload"></i>
                    {{ form.metaImageFile ? 'Змінити зображення' : 'Вибрати зображення' }}
                  </button>
                  <span v-if="form.metaImageFile" class="file-name">
                    {{ form.metaImageFile.name }}
                  </span>
                </div>
                <small class="form-hint">Завантажте зображення для соціальних мереж (JPG, PNG, макс. 5MB)</small>
              </div>

              <div class="form-group">
                <label for="contactEmail" class="form-label">Контактний email *</label>
                <input
                  id="contactEmail"
                  v-model="form.contactEmail"
                  type="email"
                  class="form-input"
                  required
                  placeholder="<EMAIL>"
                />
              </div>

              <div class="form-group">
                <label for="contactPhone" class="form-label">Контактний телефон *</label>
                <input
                  id="contactPhone"
                  v-model="form.contactPhone"
                  type="tel"
                  class="form-input"
                  required
                  placeholder="+380XXXXXXXXX"
                />
              </div>
            </div>
          </div>

          <!-- Address Information -->
          <div class="form-section">
            <h2 class="section-title">
              <i class="fas fa-map-marker-alt"></i>
              Адреса компанії
            </h2>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="addressRegion" class="form-label">Область *</label>
                <input
                  id="addressRegion"
                  v-model="form.addressRegion"
                  type="text"
                  class="form-input"
                  required
                  placeholder="Київська область"
                />
              </div>

              <div class="form-group">
                <label for="addressCity" class="form-label">Місто *</label>
                <input
                  id="addressCity"
                  v-model="form.addressCity"
                  type="text"
                  class="form-input"
                  required
                  placeholder="Київ"
                />
              </div>

              <div class="form-group">
                <label for="addressStreet" class="form-label">Вулиця та номер будинку *</label>
                <input
                  id="addressStreet"
                  v-model="form.addressStreet"
                  type="text"
                  class="form-input"
                  required
                  placeholder="вул. Хрещатик, 1"
                />
              </div>

              <div class="form-group">
                <label for="addressPostalCode" class="form-label">Поштовий індекс *</label>
                <input
                  id="addressPostalCode"
                  v-model="form.addressPostalCode"
                  type="text"
                  class="form-input"
                  required
                  placeholder="01001"
                />
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="form-section">
            <h2 class="section-title">
              <i class="fas fa-credit-card"></i>
              Фінансова інформація
            </h2>
            
            <div class="form-grid">
              <div class="form-group">
                <label for="bankAccount" class="form-label">Номер банківського рахунку *</label>
                <input
                  id="bankAccount"
                  v-model="form.bankAccount"
                  type="text"
                  class="form-input"
                  required
                  placeholder="*****************************"
                />
              </div>

              <div class="form-group">
                <label for="bankName" class="form-label">Назва банку *</label>
                <input
                  id="bankName"
                  v-model="form.bankName"
                  type="text"
                  class="form-input"
                  required
                  placeholder="ПриватБанк"
                />
              </div>

              <div class="form-group">
                <label for="bankCode" class="form-label">МФО банку *</label>
                <input
                  id="bankCode"
                  v-model="form.bankCode"
                  type="text"
                  class="form-input"
                  required
                  placeholder="305299"
                />
              </div>

              <div class="form-group">
                <label for="taxId" class="form-label">Податковий номер *</label>
                <input
                  id="taxId"
                  v-model="form.taxId"
                  type="text"
                  class="form-input"
                  required
                  placeholder="**********"
                />
              </div>

              <div class="form-group full-width">
                <label for="paymentDetails" class="form-label">Деталі оплати</label>
                <textarea
                  id="paymentDetails"
                  v-model="form.paymentDetails"
                  class="form-textarea"
                  rows="3"
                  maxlength="500"
                  placeholder="Додаткові деталі для оплати, умови, тощо"
                ></textarea>
                <small class="form-hint">Максимум 500 символів</small>
              </div>
            </div>
          </div>

          <!-- Work Schedule (показується тільки якщо чекбокс активний) -->
          <div v-if="form.isCompanyRepresentative" class="form-section">
            <h2 class="section-title">
              <i class="fas fa-clock"></i>
              Розклад роботи
            </h2>

            <div class="schedule-container">
              <div v-for="(day, index) in workSchedule" :key="day.day" class="schedule-day">
                <div class="day-header">
                  <h4>{{ getDayName(day.day) }}</h4>
                  <label class="checkbox-group">
                    <input
                      v-model="day.isClosed"
                      type="checkbox"
                      class="form-checkbox"
                    />
                    <span class="checkbox-label">Вихідний</span>
                  </label>
                </div>

                <div v-if="!day.isClosed" class="time-inputs">
                  <div class="time-group">
                    <label :for="`openTime-${index}`" class="time-label">Відкриття:</label>
                    <input
                      :id="`openTime-${index}`"
                      v-model="day.openTime"
                      type="time"
                      class="time-input"
                    />
                  </div>
                  <div class="time-group">
                    <label :for="`closeTime-${index}`" class="time-label">Закриття:</label>
                    <input
                      :id="`closeTime-${index}`"
                      v-model="day.closeTime"
                      type="time"
                      class="time-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="form-section">
            <h2 class="section-title">
              <i class="fas fa-info-circle"></i>
              Додаткова інформація
            </h2>
            
            <div class="form-group">
              <label for="additionalInfo" class="form-label">Додаткова інформація</label>
              <textarea
                id="additionalInfo"
                v-model="form.additionalInfo"
                class="form-textarea"
                rows="4"
                placeholder="Розкажіть про ваш досвід, плани розвитку бізнесу або інші важливі деталі"
              ></textarea>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-large" :disabled="submitting">
              <i v-if="submitting" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-paper-plane"></i>
              {{ submitting ? 'Подача заявки...' : 'Подати заявку' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue';
import { useToast } from '@/composables/useToast';
import sellerRequestService from '@/services/seller-request.service';
import imageService from '@/services/image.service';

export default {
  name: 'SellerApplication',
  setup() {
    const { showToast } = useToast();
    
    const existingRequest = ref(null);
    const showForm = ref(false);
    const submitting = ref(false);
    
    const form = reactive({
      companyName: '',
      companySlug: '',
      companyDescription: '',
      contactEmail: '',
      contactPhone: '',
      addressRegion: '',
      addressCity: '',
      addressStreet: '',
      addressPostalCode: '',
      bankAccount: '',
      bankName: '',
      bankCode: '',
      taxId: '',
      additionalInfo: '',
      // Нові поля відповідно до StoreSellerRequestCommand
      isCompanyRepresentative: false,
      companyImageUrl: '',
      metaTitle: '',
      metaDescription: '',
      metaImageUrl: '',
      paymentDetails: '',
      // Файли для завантаження
      companyLogoFile: null,
      metaImageFile: null
    });

    // Розклад роботи
    const workSchedule = ref([
      { day: 'Monday', openTime: '09:00', closeTime: '18:00', isClosed: false },
      { day: 'Tuesday', openTime: '09:00', closeTime: '18:00', isClosed: false },
      { day: 'Wednesday', openTime: '09:00', closeTime: '18:00', isClosed: false },
      { day: 'Thursday', openTime: '09:00', closeTime: '18:00', isClosed: false },
      { day: 'Friday', openTime: '09:00', closeTime: '18:00', isClosed: false },
      { day: 'Saturday', openTime: '10:00', closeTime: '16:00', isClosed: false },
      { day: 'Sunday', openTime: '00:00', closeTime: '00:00', isClosed: true }
    ]);

    const getDayName = (day) => {
      const dayNames = {
        'Monday': 'Понеділок',
        'Tuesday': 'Вівторок',
        'Wednesday': 'Середа',
        'Thursday': 'Четвер',
        'Friday': "П'ятниця",
        'Saturday': 'Субота',
        'Sunday': 'Неділя'
      };
      return dayNames[day] || day;
    };

    // Функції для обробки завантаження файлів
    const handleLogoUpload = (event) => {
      const file = event.target.files[0];
      if (file) {
        // Валідація файлу
        if (!file.type.startsWith('image/')) {
          showToast('Будь ласка, виберіть файл зображення', 'error');
          return;
        }
        if (file.size > 5 * 1024 * 1024) { // 5MB
          showToast('Розмір файлу не повинен перевищувати 5MB', 'error');
          return;
        }
        form.companyLogoFile = file;
      }
    };

    const handleMetaImageUpload = (event) => {
      const file = event.target.files[0];
      if (file) {
        // Валідація файлу
        if (!file.type.startsWith('image/')) {
          showToast('Будь ласка, виберіть файл зображення', 'error');
          return;
        }
        if (file.size > 5 * 1024 * 1024) { // 5MB
          showToast('Розмір файлу не повинен перевищувати 5MB', 'error');
          return;
        }
        form.metaImageFile = file;
      }
    };

    const generateSlug = () => {
      if (form.companyName && !form.companySlug) {
        form.companySlug = form.companyName
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
      }

      // Автозаповнення meta полів
      if (form.companyName && !form.metaTitle) {
        form.metaTitle = form.companyName;
      }
      if (form.companyDescription && !form.metaDescription) {
        form.metaDescription = form.companyDescription.substring(0, 200);
      }
    };

    const getStatusClass = (status) => {
      const statusClasses = {
        'Pending': 'status-pending',
        'Approved': 'status-approved',
        'Rejected': 'status-rejected'
      };
      return statusClasses[status] || 'status-pending';
    };

    const getStatusText = (status) => {
      const statusTexts = {
        'Pending': 'На розгляді',
        'Approved': 'Схвалено',
        'Rejected': 'Відхилено'
      };
      return statusTexts[status] || status;
    };

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const checkExistingRequest = async () => {
      try {
        const response = await sellerRequestService.getUserSellerRequests({ pageSize: 1 });
        if (response.requests && response.requests.length > 0) {
          const latestRequest = response.requests[0];
          if (latestRequest.status === 'Pending' || latestRequest.status === 'Approved') {
            existingRequest.value = latestRequest;
          }
        }
      } catch (error) {
        console.error('Error checking existing request:', error);
      }
    };

    const submitApplication = async () => {
      submitting.value = true;

      try {
        // Підготовка розкладу роботи
        const daySchedules = workSchedule.value.map(day => ({
          day: day.day,
          openTime: day.isClosed ? '00:00:00' : `${day.openTime}:00`,
          closeTime: day.isClosed ? '00:00:00' : `${day.closeTime}:00`,
          isClosed: day.isClosed
        }));

        // Спочатку створюємо заявку без зображень
        const applicationData = {
          companyName: form.companyName,
          companySlug: form.companySlug,
          companyDescription: form.companyDescription,
          companyImageUrl: '', // Буде оновлено після завантаження
          contactEmail: form.contactEmail,
          contactPhone: form.contactPhone,
          addressRegion: form.addressRegion,
          addressCity: form.addressCity,
          addressStreet: form.addressStreet,
          addressPostalCode: form.addressPostalCode,
          metaTitle: form.metaTitle || form.companyName,
          metaDescription: form.metaDescription || form.companyDescription,
          metaImageUrl: '', // Буде оновлено після завантаження
          bankAccount: form.bankAccount,
          bankName: form.bankName,
          bankCode: form.bankCode,
          taxId: form.taxId,
          paymentDetails: form.paymentDetails || `${form.bankName} - ${form.bankAccount}`,
          daySchedules: daySchedules,
          additionalInfo: form.additionalInfo +
            (form.isCompanyRepresentative ?
              '\n\nТип заявки: Представник компанії'
              : '\n\nТип заявки: Індивідуальний підприємець/фізична особа')
        };

        // Створюємо заявку
        const response = await sellerRequestService.createSellerRequest(applicationData);
        const requestId = response.id;

        // Завантажуємо файли, якщо вони є
        if (form.companyLogoFile || form.metaImageFile) {
          showToast('Завантажуємо зображення...', 'info');

          try {
            if (form.companyLogoFile) {
              // Завантажуємо логотип компанії в тимчасову папку
              const logoResponse = await imageService.uploadImage('seller-request', requestId, form.companyLogoFile);
              console.log('Logo uploaded:', logoResponse);
            }

            if (form.metaImageFile) {
              // Завантажуємо meta зображення в тимчасову папку
              const metaResponse = await imageService.uploadMetaImage('seller-request', requestId, form.metaImageFile);
              console.log('Meta image uploaded:', metaResponse);
            }

            showToast('Зображення успішно завантажено!', 'success');
          } catch (error) {
            console.error('Error uploading files:', error);
            showToast('Помилка завантаження зображень. Заявка створена, але зображення не завантажено.', 'warning');
          }
        }

        showToast('Заявку успішно подано! Очікуйте на розгляд адміністратором.', 'success');

        // Перевіряємо наявність нової заявки
        await checkExistingRequest();
        showForm.value = false;
        
      } catch (error) {
        console.error('Error submitting application:', error);
        showToast('Помилка при подачі заявки. Спробуйте ще раз.', 'error');
      } finally {
        submitting.value = false;
      }
    };

    onMounted(() => {
      checkExistingRequest();
    });

    // Автозаповнення meta полів при зміні назви або опису
    watch(() => form.companyName, (newName) => {
      if (newName && !form.metaTitle) {
        form.metaTitle = newName;
      }
      generateSlug();
    });

    watch(() => form.companyDescription, (newDescription) => {
      if (newDescription && !form.metaDescription) {
        form.metaDescription = newDescription.substring(0, 200);
      }
    });

    return {
      existingRequest,
      showForm,
      submitting,
      form,
      workSchedule,
      generateSlug,
      getDayName,
      handleLogoUpload,
      handleMetaImageUpload,
      getStatusClass,
      getStatusText,
      formatDate,
      submitApplication
    };
  }
};
</script>

<style scoped>
.seller-application {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.application-header {
  text-align: center;
  margin-bottom: 40px;
}

.application-title {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 16px;
  font-weight: 700;
}

.application-title i {
  color: #007bff;
  margin-right: 12px;
}

.application-subtitle {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Existing Request Notice */
.existing-request-notice {
  background: #fff;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notice-content i {
  font-size: 2rem;
  color: #2196f3;
}

.notice-text h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.2rem;
}

.notice-text p {
  margin: 4px 0;
  color: #666;
}

.status-pending {
  color: #ff9800;
  font-weight: 600;
}

.status-approved {
  color: #4caf50;
  font-weight: 600;
}

.status-rejected {
  color: #f44336;
  font-weight: 600;
}

/* Form */
.application-form {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 40px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title i {
  color: #007bff;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.form-input,
.form-textarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-hint {
  color: #666;
  font-size: 0.85rem;
  margin-top: 4px;
}

/* Actions */
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 2px solid #e9ecef;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-large {
  padding: 16px 32px;
  font-size: 1.1rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive */
@media (max-width: 768px) {
  .application-title {
    font-size: 2rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .existing-request-notice {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .application-form {
    padding: 24px;
  }

  .container {
    padding: 0 15px;
  }
}

/* Стилі для розкладу роботи */
.schedule-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schedule-day {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  background: #f8f9fa;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.day-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.time-inputs {
  display: flex;
  gap: 20px;
  align-items: center;
}

.time-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.time-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.time-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Стилі для завантаження файлів */
.file-upload-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-input {
  display: none;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.file-upload-btn:hover {
  background: #e9ecef;
  border-color: #007bff;
  color: #007bff;
}

.file-upload-btn i {
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #28a745;
  font-weight: 500;
  padding: 4px 8px;
  background: #d4edda;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
}
</style>
