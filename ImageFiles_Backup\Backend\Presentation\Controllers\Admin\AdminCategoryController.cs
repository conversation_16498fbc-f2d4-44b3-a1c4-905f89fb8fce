﻿using Marketplace.Application.Commands.Category;
using Marketplace.Application.Commands.File;
using Marketplace.Application.Commands.Meta;
using Marketplace.Application.Commands.Product;
using Marketplace.Application.Queries.Category;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Admin;

[ApiController]
[Authorize(Roles = "Admin")]
[Route("api/admin/categories")]
public class AdminCategoryController : BasicApiController
{
    private readonly IMediator _mediator;

    public AdminCategoryController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllCategories(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetAllCategoryQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize);

        var response = await _mediator.Send(query, cancellationToken);
        return Ok(response);
    }

    [HttpGet("stats")]
    public async Task<IActionResult> GetCategoryStats(CancellationToken cancellationToken)
    {
        var query = new GetCategoryStatsQuery();
        var response = await _mediator.Send(query, cancellationToken);
        return Ok(response);
    }

    [HttpGet("tree")]
    public async Task<IActionResult> GetCategoryTree(
        [FromQuery] string? filter = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetCategoryTreeQuery(filter);
        var response = await _mediator.Send(query, cancellationToken);
        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetCategoryById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetCategoryQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(response) : NotFound();
    }

    [HttpPost]
    public async Task<IActionResult> CreateCategory([FromBody] StoreCategoryCommand command, CancellationToken cancellationToken)
    {
        var response = await _mediator.Send(command, cancellationToken);

        return response != null ? CreatedAtAction(nameof(GetCategoryById), new { id = response }, new { id = response, success = true }) : BadRequest();
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateCategory([FromRoute] Guid id, [FromBody] UpdateCategoryCommand command, CancellationToken cancellationToken)
    {
        // Переконуємося, що ID з маршруту використовується для команди
        var commandWithId = command with { Id = id };
        await _mediator.Send(commandWithId, cancellationToken);

        return Ok(new { id, success = true });
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteCategory([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var command = new DeleteCategoryCommand(id);
            var result = await _mediator.Send(command, cancellationToken);

            return result ? NoContent() : NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/images")]
    public async Task<IActionResult> UploadImage([FromRoute] Guid id, IFormFile image, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи продукт існує
        var categoryQuery = new GetCategoryQuery(id);
        var category = await _mediator.Send(categoryQuery, cancellationToken);

        if (category == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено."));
        }

        // Створюємо команду для завантаження зображення
        var command = new UploadImageCommand("category", id, image);
        var result = await _mediator.Send(command, cancellationToken);

        return result != null
            ? Ok(ApiResponse<Guid>.SuccessWithData(result.Id, "Зображення успішно завантажено."))
            : BadRequest(ApiResponse.Failure("Не вдалося завантажити зображення."));
    }

    [HttpDelete("{categoryId}/images/{imageId}")]
    public async Task<IActionResult> DeleteImage([FromRoute] Guid categoryId, [FromRoute] Guid imageId, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи продукт існує
        var categoryQuery = new GetCategoryQuery(categoryId);
        var category = await _mediator.Send(categoryQuery, cancellationToken);

        if (category == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено."));
        }

        // Видаляємо зображення
        var command = new DeleteImageCommand("category", imageId);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound(ApiResponse.Failure("Зображення не знайдено."));
    }


    [HttpPost("{id}/meta-image")]
    public async Task<IActionResult> UploadMetaImage([FromRoute] Guid id, IFormFile image, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи категорія існує
        var categoryQuery = new GetCategoryQuery(id);
        var category = await _mediator.Send(categoryQuery, cancellationToken);

        if (category == null)
        {
            return NotFound();
        }

        // Зчитуємо файл у масив байтів
        using var memoryStream = new MemoryStream();
        await image.CopyToAsync(memoryStream, cancellationToken);
        var imageBytes = memoryStream.ToArray();

        // Створюємо команду для завантаження зображення
        var command = new UploadMetaImageCommand("category", id, image);
        //var command = new UploadCategoryMetaImageCommand(id, imageBytes, image.FileName, image.ContentType);
        var result = await _mediator.Send(command, cancellationToken);

        return result != null ? Ok(new { success = true }) : BadRequest();
    }

    [HttpDelete("{id}/meta-image")]
    public async Task<IActionResult> DeleteMetaImage([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи категорія існує
        var categoryQuery = new GetCategoryQuery(id);
        var category = await _mediator.Send(categoryQuery, cancellationToken);

        if (category == null)
        {
            return NotFound();
        }

        // Видаляємо мета-зображення
        var command = new DeleteMetaImageCommand("category", id);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound();
    }

    [HttpPost("{id}/bulk-update-products-category")]
    public async Task<IActionResult> BulkUpdateProductsCategory(
        [FromRoute] Guid id,
        [FromBody] BulkUpdateProductCategoryRequest request,
        CancellationToken cancellationToken)
    {
        try
        {
            var command = new BulkUpdateProductCategoryCommand(id, request.ToCategoryId);
            var updatedCount = await _mediator.Send(command, cancellationToken);

            return Ok(new { updatedCount, success = true, message = $"Успішно оновлено категорію для {updatedCount} продуктів." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}

public class BulkUpdateProductCategoryRequest
{
    public Guid ToCategoryId { get; set; }
}
