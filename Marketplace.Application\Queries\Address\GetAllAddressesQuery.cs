using Marketplace.Application.Responses;
using MediatR;

namespace Marketplace.Application.Queries.Address;

public record GetAllAddressesQuery(
    string? Filter = null,
    string? OrderBy = null,
    bool Descending = false,
    int? Page = null,
    int? PageSize = null,
    string? Region = null,
    string? City = null,
    string? PostalCode = null,
    Guid? UserId = null,
    string? UserEmail = null,
    bool? IsActive = null,
    DateTime? CreatedAfter = null,
    DateTime? CreatedBefore = null
    ) : IRequest<PagedResponse<AddressResponse>>;
