<template>
  <div class="hierarchy-item" :class="{ 'has-children': hasChildren }">
    <!-- Category Card -->
    <div class="modern-hierarchy-card" :style="{ marginLeft: `${level * 2}rem` }">
      <div class="hierarchy-card-content">
        <!-- Left Section: Image + Info -->
        <div class="hierarchy-left">
          <div class="hierarchy-image">
            <img v-if="category.image" :src="category.image" :alt="category.name" class="category-img">
            <div v-else class="category-img-placeholder">
              <i class="fas fa-folder"></i>
            </div>
          </div>

          <div class="hierarchy-info">
            <div class="category-header">
              <h3 class="category-name">{{ category.name }}</h3>
              <div class="category-badges">
                <span v-if="level === 0" class="badge badge-primary">
                  <i class="fas fa-layer-group"></i>
                  Root
                </span>
                <span v-else class="badge badge-info">
                  <i class="fas fa-sitemap"></i>
                  Level {{ level + 1 }}
                </span>
                <span v-if="hasChildren" class="badge badge-success">
                  <i class="fas fa-code-branch"></i>
                  {{ category.children.length }} children
                </span>
              </div>
            </div>

            <div class="category-slug">{{ category.slug }}</div>

            <div class="category-stats">
              <span class="stat-item" :class="getProductCountBadgeClass(category.productCount)">
                <i class="fas fa-cube"></i>
                {{ category.productCount || 0 }} products
              </span>
            </div>

            <p v-if="category.description" class="category-description">
              {{ truncateText(category.description, 100) }}
            </p>
          </div>
        </div>

        <!-- Right Section: Actions -->
        <div class="hierarchy-actions">
          <button
            class="action-btn action-btn-edit"
            @click="$emit('edit', category)"
            title="Edit category">
            <i class="fas fa-edit"></i>
          </button>
          <button
            class="action-btn action-btn-view"
            @click="$emit('view-products', category)"
            title="View products"
            :disabled="!category.productCount">
            <i class="fas fa-box"></i>
          </button>
          <button
            class="action-btn action-btn-add"
            @click="$emit('add-child', category)"
            title="Add subcategory">
            <i class="fas fa-plus"></i>
          </button>
          <button
            class="action-btn action-btn-delete"
            @click="$emit('delete', category)"
            :title="getDeleteTooltip(category)"
            :disabled="!canDeleteCategory(category)">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Expand/Collapse Button -->
    <div v-if="hasChildren" class="expand-toggle" :style="{ marginLeft: `${level * 2}rem` }">
      <button
        class="expand-btn"
        @click="toggleExpanded">
        <i class="fas" :class="expanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
        <span>{{ expanded ? 'Collapse' : 'Expand' }} ({{ category.children.length }})</span>
      </button>
    </div>

    <!-- Children (recursive) -->
    <div v-if="hasChildren && expanded" class="children">
      <CategoryHierarchyItem
        v-for="child in category.children"
        :key="child.id"
        :category="child"
        :level="level + 1"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @add-child="$emit('add-child', $event)"
        @view-products="$emit('view-products', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  }
});

defineEmits(['edit', 'delete', 'add-child', 'view-products']);

const expanded = ref(false); // Start collapsed - show only root categories

const hasChildren = computed(() => {
  return props.category.children && props.category.children.length > 0;
});

const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

const getProductCountBadgeClass = (count) => {
  if (!count || count === 0) return 'stat-item-light';
  if (count < 5) return 'stat-item-warning';
  if (count < 20) return 'stat-item-info';
  return 'stat-item-success';
};

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// Check if category can be deleted
const canDeleteCategory = (category) => {
  const hasProducts = (category.productCount || 0) > 0;
  const hasChildCategories = hasChildren.value;
  return !hasProducts && !hasChildCategories;
};

// Get tooltip text for delete button
const getDeleteTooltip = (category) => {
  const hasProducts = (category.productCount || 0) > 0;
  const hasChildCategories = hasChildren.value;

  if (hasProducts && hasChildCategories) {
    return 'Cannot delete: category has products and subcategories';
  } else if (hasProducts) {
    return 'Cannot delete: category has products';
  } else if (hasChildCategories) {
    return 'Cannot delete: category has subcategories';
  }
  return 'Delete category';
};
</script>

<style scoped>
/* Modern Hierarchy Card */
.modern-hierarchy-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  overflow: hidden;
}

.modern-hierarchy-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-left-color: #667eea;
}

.hierarchy-card-content {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

/* Left Section */
.hierarchy-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.hierarchy-image {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-img-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1.5rem;
}

/* Info Section */
.hierarchy-info {
  flex: 1;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.category-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.category-badges {
  display: flex;
  gap: 0.5rem;
}

.category-slug {
  color: #6b7280;
  font-size: 0.875rem;
  font-family: 'Courier New', monospace;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 0.5rem;
}

.category-stats {
  margin-bottom: 0.5rem;
}

.stat-item {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-item-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.stat-item-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.stat-item-info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
}

.stat-item-light {
  background: #f3f4f6;
  color: #6b7280;
}

.category-description {
  color: #9ca3af;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.badge-info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
}

.badge-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* Actions */
.hierarchy-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn-edit {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.action-btn-edit:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.action-btn-view {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
}

.action-btn-view:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.4);
}

.action-btn-add {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.action-btn-add:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.action-btn-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.action-btn-delete:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Expand Toggle */
.expand-toggle {
  text-align: center;
  margin-bottom: 1rem;
}

.expand-btn {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.expand-btn:hover {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  color: #374151;
  transform: translateY(-1px);
}

/* Children */
.children {
  position: relative;
}

.children::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 1rem;
  width: 2px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
  opacity: 0.3;
  border-radius: 1px;
}

/* Hierarchy Item */
.hierarchy-item {
  position: relative;
}

.hierarchy-item.has-children::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
  opacity: 0.2;
  border-radius: 2px;
}

/* Responsive */
@media (max-width: 768px) {
  .hierarchy-card-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .hierarchy-left {
    width: 100%;
  }

  .hierarchy-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}
</style>
