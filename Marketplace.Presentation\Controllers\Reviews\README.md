# API Controllers Documentation

Цей каталог містить документацію для контролерів системи Marketplace.

## Review Controllers

Контролери для роботи з відгуками в системі Marketplace.

## ReviewController

Публічний контролер для відгуків, доступний всім користувачам (включно з неавторизованими).

### Маршрути

#### 1. Останні відгуки
```
GET /api/reviews/recent
```
Повертає останні відгуки за останні 30 днів.

**Параметри запиту:**
- `filter` (string, optional) - Фільтр для пошуку
- `orderBy` (string, optional) - Поле для сортування (за замовчуванням: CreatedAt)
- `descending` (bool, optional) - Сортування за спаданням (за замовчуванням: true)
- `page` (int, optional) - Номер сторінки (за замовчуванням: 1)
- `pageSize` (int, optional) - Розмір сторінки (за замовчуванням: 10)

#### 2. Відгуки товару
```
GET /api/reviews/products/{productSlug}
```
Повертає відгуки для конкретного товару за його slug.

#### 3. Відгуки категорії
```
GET /api/reviews/categories/{categorySlug}
```
Повертає відгуки для товарів конкретної категорії за її slug.

#### 4. Конкретний відгук
```
GET /api/reviews/{id}
```
Повертає конкретний відгук за його ID.

## AlternativeReviewController

Додатковий контролер для альтернативних маршрутів, які відповідають структурі URL фронтенду.

### Маршрути

#### 1. Відгуки товару (альтернативний маршрут)
```
GET /api/products/{productSlug}/reviews
```

#### 2. Відгуки категорії (альтернативний маршрут)
```
GET /api/categories/{categorySlug}/reviews
```

## Відповіді

Всі ендпоінти повертають дані у форматі `ApiResponse<PaginatedResponse<ReviewResponse>>`:

```json
{
  "success": true,
  "message": null,
  "data": {
    "total": 100,
    "perPage": 10,
    "currentPage": 1,
    "lastPage": 10,
    "firstPageUrl": "http://localhost:5296/api/reviews/recent?page=1",
    "lastPageUrl": "http://localhost:5296/api/reviews/recent?page=10",
    "nextPageUrl": "http://localhost:5296/api/reviews/recent?page=2",
    "prevPageUrl": null,
    "path": "http://localhost:5296/api/reviews/recent",
    "from": 1,
    "to": 10,
    "data": [
      {
        "id": "guid",
        "productId": "guid",
        "productName": "Назва товару",
        "userId": "guid",
        "userName": "Ім'я користувача",
        "comment": "Текст відгуку",
        "rating": {
          "service": 5,
          "deliveryTime": 4,
          "accuracy": 5
        },
        "averageRating": 4.7,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

## Використання у фронтенді

Ці ендпоінти використовуються компонентом `ReviewGrid` на наступних сторінках:

1. **Головна сторінка** - `/api/reviews/recent`
2. **Сторінка товару** - `/api/products/{slug}/reviews`
3. **Сторінка категорії** - `/api/categories/{slug}/reviews`
4. **Профіль користувача** - `/api/users/me/ratings` (інший контролер)

## Безпека

Всі ендпоінти в цих контролерах є публічними і не вимагають авторизації. Це дозволяє неавторизованим користувачам переглядати відгуки на публічних сторінках сайту.

## Пов'язані файли

### Queries (Application Layer)
- `GetProductReviewsQuery.cs` / `GetProductReviewsQueryHandler.cs`
- `GetCategoryReviewsQuery.cs` / `GetCategoryReviewsQueryHandler.cs`
- `GetRecentReviewsQuery.cs` / `GetRecentReviewsQueryHandler.cs`
- `GetReviewQuery.cs` / `GetReviewQueryHandler.cs`

### Repositories (Domain Layer)
- `IReviewRepository.cs`
- `IProductRepository.cs`
- `ICategoryRepository.cs`

### Responses (Application Layer)
- `ReviewResponse.cs`
- `PaginatedResponse.cs`
