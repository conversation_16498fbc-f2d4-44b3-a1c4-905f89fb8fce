using Marketplace.Domain.Entities;

namespace Marketplace.Application.Responses;

public class LogResponse
{
    public Guid Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserEmail { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public string? AdditionalData { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }

    public static LogResponse FromEntity(Domain.Entities.Log log)
    {
        return new LogResponse
        {
            Id = log.Id,
            Timestamp = log.Timestamp,
            Level = log.Level.ToString(),
            Category = log.Category,
            Message = log.Message,
            UserId = log.UserId,
            UserName = log.User?.Username,
            UserEmail = log.User?.Email,
            EntityId = log.EntityId,
            EntityType = log.EntityType,
            AdditionalData = log.AdditionalData,
            IpAddress = log.IpAddress,
            UserAgent = log.UserAgent
        };
    }
}
