<!-- components/Pagination.vue -->
<template>
  <nav class="pagination" aria-label="Pagination">
    <button
      class="pagination-btn-prev disable-styles"
      :disabled="currentPage === 1"
      @click="goToPage(currentPage - 1)"
      aria-label="Previous page"
    >
      <span class="fas fa-arrow-left"></span>
    </button>

    <button
      v-if="visiblePages.indexOf(1) == -1"
      class="pagination-btn"
      @click="goToPage(1)"
      :aria-label="`Go to page ${page}`"
    >
    1
    </button>
    <button
      v-if="visiblePages.indexOf(1) == -1"
      class="pagination-btn"
      @click="goToPage(1)"
      :aria-label="`Go to page ${page}`"
    >
    <span>...</span>
    </button>
    <!-- Номери сторінок -->
    <button
      v-for="page in visiblePages"
      :key="page"
      class="pagination-btn"
      :class="{ active: page === currentPage }"
      @click="goToPage(page)"
      :aria-current="page === currentPage ? 'page' : null"
      :aria-label="`Go to page ${page}`"
    >
      {{ page }}
    </button>

    <button
      v-if="visiblePages.indexOf(totalPages) == -1"
      class="pagination-btn"
      @click="goToPage(1)"
      :aria-label="`Go to page ${page}`"
    >
    <span>...</span>
    </button>
    <button
      v-if="visiblePages.indexOf(totalPages) == -1"
      class="pagination-btn"
      @click="goToPage(totalPages)"
      :aria-label="`Go to page ${page}`"
    >
    {{ totalPages }}
    </button>

    <button
      class="pagination-btn-next disable-styles"
      :disabled="currentPage === totalPages"
      @click="goToPage(currentPage + 1)"
      aria-label="Next page"
    >
      <span class="fas fa-arrow-right"></span>
    </button>
  </nav>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    currentPage: {
      type: Number,
      required: true,
    },
    totalItems: {
      type: Number,
      required: true,
    },
    itemsPerPage: {
      type: Number,
      default: 10,
    },
    maxVisiblePages: {
      type: Number,
      default: 5, // Максимальна кількість видимих номерів сторінок
    },
  },
  computed: {
    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage) || 1;
    },
    visiblePages() {
      const pages = [];
      const half = Math.floor(this.maxVisiblePages / 2);
      let start = Math.max(1, this.currentPage - half);
      const end = Math.min(this.totalPages, start + this.maxVisiblePages - 1);

      if (end - start + 1 < this.maxVisiblePages) {
        start = Math.max(1, end - this.maxVisiblePages + 1);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    },
  },
  methods: {
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.$emit('page-changed', page);
        //console.log("emitted update:currentPage");
      }
    },
  },
};
</script>

<style scoped>
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
}

.pagination-btn-next, .pagination-btn-prev {
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s;
}

.pagination-btn-next:enabled, .pagination-btn-prev:enabled {
  color:#2F7CDF;
}

.pagination-btn-next:disabled, .pagination-btn-prev:disabled {
  color:#ABAAAA;
}

.pagination-btn {
  padding: 8px 16px;
  font-size: large;
  border: 2px solid #2F7CDF;
  color: #2F7CDF;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: background 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f0f0f0;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  color: #ABAAAA;
  border-color: #ABAAAA;
}

@media (max-width: 600px) {
  .pagination-btn {
    padding: 6px 10px;
    font-size: 0.9em;
  }
}

.disable-styles {
  border: none !important;
  background: none !important;
  /* Add other resets as needed */
}
</style>