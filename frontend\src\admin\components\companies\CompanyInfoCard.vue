<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-building admin-card-icon"></i>
        Company Information
      </h3>
    </div>
    <div class="admin-card-content">
      <!-- Company Image -->
      <div v-if="displayImageUrl" class="company-image-section">
        <div class="image-container">
          <div class="image-wrapper">
            <img :src="displayImageUrl" :alt="company.name" class="company-image" />
            <div class="image-overlay">
              <button
                @click="openImageModal(displayImageUrl, company.name + ' Logo')"
                class="image-action-btn"
                title="View full size">
                <i class="fas fa-expand"></i>
              </button>
              <a
                :href="displayImageUrl"
                target="_blank"
                class="image-action-btn"
                title="Open in new tab">
                <i class="fas fa-external-link-alt"></i>
              </a>
            </div>
          </div>
          <div class="image-info">
            <span class="image-label">
              <i class="fas fa-image"></i>
              Company Logo
            </span>
          </div>
        </div>
      </div>

      <!-- Basic Information -->
      <div class="admin-info-grid">
        <div class="admin-info-group">
          <label class="admin-info-label">Company Name</label>
          <div class="admin-info-value">{{ company.name || 'N/A' }}</div>
        </div>
        
        <div class="admin-info-group">
          <label class="admin-info-label">Slug</label>
          <div class="admin-info-value admin-info-value-code">{{ company.slug || 'N/A' }}</div>
        </div>
        
        <div class="admin-info-group">
          <label class="admin-info-label">Contact Email</label>
          <div class="admin-info-value">
            <a v-if="company.contactEmail" :href="`mailto:${company.contactEmail}`" class="admin-link">
              <i class="fas fa-envelope"></i>
              {{ company.contactEmail }}
            </a>
            <span v-else class="admin-text-muted">N/A</span>
          </div>
        </div>
        
        <div class="admin-info-group">
          <label class="admin-info-label">Contact Phone</label>
          <div class="admin-info-value">
            <a v-if="company.contactPhone" :href="`tel:${company.contactPhone}`" class="admin-link">
              <i class="fas fa-phone"></i>
              {{ company.contactPhone }}
            </a>
            <span v-else class="admin-text-muted">N/A</span>
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="admin-info-group admin-info-group-full">
        <label class="admin-info-label">Description</label>
        <div class="admin-info-value admin-info-value-multiline">
          {{ company.description || 'No description provided' }}
        </div>
      </div>

      <!-- Address -->
      <div class="admin-info-group admin-info-group-full">
        <label class="admin-info-label">Address</label>
        <div class="admin-info-value">
          <div v-if="hasAddress" class="address-display">
            <div v-if="company.addressStreet" class="address-line">
              <i class="fas fa-map-marker-alt"></i>
              {{ company.addressStreet }}
            </div>
            <div v-if="cityRegionPostal" class="address-line">
              <i class="fas fa-city"></i>
              {{ cityRegionPostal }}
            </div>
          </div>
          <span v-else class="admin-text-muted">No address provided</span>
        </div>
      </div>

      <!-- Meta Information -->
      <div v-if="company.metaTitle || company.metaDescription" class="meta-section">
        <h4 class="meta-section-title">
          <i class="fas fa-tags"></i>
          SEO Meta Information
        </h4>
        
        <div class="admin-info-grid">
          <div v-if="company.metaTitle" class="admin-info-group">
            <label class="admin-info-label">Meta Title</label>
            <div class="admin-info-value">{{ company.metaTitle }}</div>
          </div>
          
          <div v-if="company.metaDescription" class="admin-info-group admin-info-group-full">
            <label class="admin-info-label">Meta Description</label>
            <div class="admin-info-value admin-info-value-multiline">{{ company.metaDescription }}</div>
          </div>
          
          <div v-if="displayMetaImageUrl" class="admin-info-group admin-info-group-full">
            <label class="admin-info-label">Meta Image (SEO)</label>
            <div class="admin-info-value">
              <div class="meta-image-container">
                <div class="meta-image-wrapper">
                  <img :src="displayMetaImageUrl" :alt="company.name + ' Meta Image'" class="meta-image" />
                  <div class="meta-image-overlay">
                    <button
                      @click="openImageModal(displayMetaImageUrl, company.name + ' Meta Image')"
                      class="image-action-btn"
                      title="View full size">
                      <i class="fas fa-expand"></i>
                    </button>
                    <a
                      :href="displayMetaImageUrl"
                      target="_blank"
                      class="image-action-btn"
                      title="Open in new tab">
                      <i class="fas fa-external-link-alt"></i>
                    </a>
                  </div>
                </div>
                <div class="meta-image-info">
                  <span class="image-label">
                    <i class="fas fa-tags"></i>
                    SEO Meta Image
                  </span>
                  <span class="image-description">Used for social media sharing and search results</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Badges -->
      <div class="status-badges">
        <span class="admin-badge" :class="company.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
          <i class="fas" :class="company.isApproved ? 'fa-check' : 'fa-clock'"></i>
          {{ company.isApproved ? 'Approved' : 'Pending Approval' }}
        </span>
        
        <span v-if="company.isFeatured" class="admin-badge admin-badge-info">
          <i class="fas fa-star"></i>
          Featured
        </span>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="image-modal-overlay" @click="closeImageModal">
      <div class="image-modal" @click.stop>
        <div class="image-modal-header">
          <h3 class="image-modal-title">{{ modalImageTitle }}</h3>
          <button class="image-modal-close" @click="closeImageModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="image-modal-content">
          <img :src="modalImageUrl" :alt="modalImageTitle" class="modal-image" />
        </div>
        <div class="image-modal-footer">
          <a :href="modalImageUrl" target="_blank" class="admin-btn admin-btn-secondary">
            <i class="fas fa-external-link-alt"></i>
            Open in New Tab
          </a>
          <button class="admin-btn admin-btn-primary" @click="closeImageModal">
            <i class="fas fa-times"></i>
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  company: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

// Modal state
const showImageModal = ref(false);
const modalImageUrl = ref('');
const modalImageTitle = ref('');

const hasAddress = computed(() => {
  return props.company.addressStreet || 
         props.company.addressCity || 
         props.company.addressRegion || 
         props.company.addressPostalCode;
});

const cityRegionPostal = computed(() => {
  const parts = [
    props.company.addressCity,
    props.company.addressRegion,
    props.company.addressPostalCode
  ].filter(Boolean);

  return parts.join(', ');
});

const displayImageUrl = computed(() => {
  // Don't show images from example.com (fake data)
  if (props.company.imageUrl && !props.company.imageUrl.includes('example.com')) {
    return props.company.imageUrl;
  }
  return null;
});

const displayMetaImageUrl = computed(() => {
  // Backend повертає MetaImage, а не metaImageUrl
  const metaImageUrl = props.company.metaImage || props.company.metaImageUrl;
  // Don't show images from example.com (fake data)
  if (metaImageUrl && !metaImageUrl.includes('example.com')) {
    return metaImageUrl;
  }
  return null;
});

// Modal functions
const openImageModal = (imageUrl, title) => {
  modalImageUrl.value = imageUrl;
  modalImageTitle.value = title;
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  modalImageUrl.value = '';
  modalImageTitle.value = '';
};
</script>

<style scoped>
.company-image-section {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
}

.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.image-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.image-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.company-image {
  width: 200px;
  height: 120px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.image-action-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 6px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-text-primary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.image-action-btn:hover {
  background: white;
  transform: scale(1.1);
  color: var(--admin-primary);
}

.image-info {
  text-align: center;
}

.image-label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  background: var(--admin-bg-tertiary);
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
}

.meta-image-container {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.meta-image-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  flex-shrink: 0;
}

.meta-image-wrapper:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.meta-image {
  width: 150px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.meta-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.meta-image-wrapper:hover .meta-image-overlay {
  opacity: 1;
}

.meta-image-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.image-description {
  font-size: 0.8rem;
  color: var(--admin-text-muted);
  font-style: italic;
}

.admin-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-info-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-info-group-full {
  grid-column: 1 / -1;
}

.admin-info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-info-value {
  font-size: 0.9rem;
  color: var(--admin-text-primary);
  line-height: 1.5;
}

.admin-info-value-code {
  font-family: 'Courier New', monospace;
  background: var(--admin-bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.admin-info-value-multiline {
  white-space: pre-wrap;
  word-break: break-word;
}

.admin-link {
  color: var(--admin-link);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.2s ease;
}

.admin-link:hover {
  color: var(--admin-link-hover);
  text-decoration: underline;
}

.address-display {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.address-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.address-line i {
  color: var(--admin-text-muted);
  width: 16px;
}

.meta-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--admin-border-light);
}

.meta-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-section-title i {
  color: var(--admin-text-muted);
}

.status-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--admin-border-light);
}

.admin-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-badge-success {
  background: var(--admin-success);
  color: white;
}

.admin-badge-warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-badge-info {
  background: var(--admin-info);
  color: white;
}

.admin-text-muted {
  color: var(--admin-text-muted);
  font-style: italic;
}

/* Image Modal Styles */
.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.image-modal {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.image-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--admin-border-light);
}

.image-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0;
}

.image-modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--admin-text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.image-modal-close:hover {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-primary);
}

.image-modal-content {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  overflow: hidden;
}

.modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--admin-border-light);
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-primary:hover {
  background: var(--admin-primary-dark);
}

.admin-btn-secondary {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-secondary);
}

.admin-btn-secondary:hover {
  background: var(--admin-border);
  color: var(--admin-text-primary);
}
</style>
