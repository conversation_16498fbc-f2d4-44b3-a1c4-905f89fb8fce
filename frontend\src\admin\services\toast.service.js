import { createApp, h } from 'vue';
import Toast from '../components/common/Toast.vue';

let toastInstance = null;

// Create a toast container if it doesn't exist
const createToastContainer = () => {
  if (toastInstance) return toastInstance;
  
  // Create a div to mount the toast component
  const toastContainer = document.createElement('div');
  toastContainer.id = 'toast-container';
  document.body.appendChild(toastContainer);
  
  // Create and mount the toast component
  const app = createApp({
    render: () => h(Toast)
  });
  
  toastInstance = app.mount(toastContainer);
  return toastInstance;
};

// Show a toast message
export const showToast = (message, type = 'info', duration = 5000) => {
  const toast = createToastContainer();
  return toast.addToast(message, type, duration);
};

// Remove a specific toast
export const removeToast = (id) => {
  if (toastInstance) {
    toastInstance.removeToast(id);
  }
};

export default {
  showToast,
  removeToast
};