﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Marketplace.Infrastructure.Persistence.Repositories;

public class CompanyRepository : Repository<Company>, ICompanyRepository
{
    private readonly DbSet<Company> _dbSet;
    private readonly MarketplaceDbContext _context;

    public CompanyRepository(MarketplaceDbContext context) : base(context)
    {
        _dbSet = context.Set<Company>();
        _context = context;
    }

    public async Task<Company?> GetBySlugAsync(string slug, CancellationToken cancellationToken = default, params Expression<Func<Company, object>>[] includes)
    {
        IQueryable<Company> query = _dbSet;
        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        // Оскільки Slug - це ValueObject, нам потрібно завантажити дані і фільтрувати в пам'яті
        var companies = await query.ToListAsync(cancellationToken);
        return companies.FirstOrDefault(c => c.Slug.Value == slug);
    }

    public async Task<Guid?> GetIdBySlugAsync(string slug, CancellationToken cancellationToken = default)
    {
        // Більш ефективний метод - завантажуємо тільки ID та Slug
        var companies = await _context.Companies
            .Select(c => new { c.Id, c.Slug })
            .ToListAsync(cancellationToken);

        return companies.FirstOrDefault(c => c.Slug.Value == slug)?.Id;
    }

    public async Task<IEnumerable<Company>> GetApprovedCompaniesAsync(
        string? filter = null,
        string? orderBy = null,
        bool descending = false,
        int? page = null,
        int? pageSize = null,
        CancellationToken cancellationToken = default,
        params Expression<Func<Company, object>>[] includes)
    {
        // Починаємо з запиту для затверджених компаній
        IQueryable<Company> query = _dbSet.Where(c => c.ApprovedAt != null);

        // Додаємо включення (includes)
        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        // Додаємо фільтрацію, якщо потрібно
        if (!string.IsNullOrEmpty(filter))
        {
            // Фільтруємо за доступними полями
            query = query.Where(c =>
                c.Name.Contains(filter) ||
                c.Description.Contains(filter) ||
                c.Address.City.Contains(filter) ||
                c.Address.Region.Contains(filter) ||
                c.Address.Street.Contains(filter) ||
                c.Address.PostalCode.Contains(filter));
        }

        // Додаємо сортування, якщо потрібно
        if (!string.IsNullOrEmpty(orderBy))
        {
            // Перевіряємо, чи існує властивість для сортування
            switch (orderBy.ToLower())
            {
                case "name":
                    query = descending
                        ? query.OrderByDescending(c => c.Name)
                        : query.OrderBy(c => c.Name);
                    break;
                case "city":
                    query = descending
                        ? query.OrderByDescending(c => c.Address.City)
                        : query.OrderBy(c => c.Address.City);
                    break;
                case "region":
                    query = descending
                        ? query.OrderByDescending(c => c.Address.Region)
                        : query.OrderBy(c => c.Address.Region);
                    break;
                default:
                    // За замовчуванням сортуємо за ім'ям
                    query = query.OrderBy(c => c.Name);
                    break;
            }
        }
        else
        {
            // За замовчуванням сортуємо за ім'ям
            query = query.OrderBy(c => c.Name);
        }

        // Додаємо пагінацію, якщо потрібно
        if (page.HasValue && pageSize.HasValue)
        {
            query = query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Company>> GetPendingCompaniesAsync(
        string? filter = null,
        string? orderBy = null,
        bool descending = false,
        int? page = null,
        int? pageSize = null,
        CancellationToken cancellationToken = default,
        params Expression<Func<Company, object>>[] includes)
    {
        // Починаємо з запиту для неперевірених компаній
        IQueryable<Company> query = _dbSet.Where(c => c.ApprovedAt == null);

        // Додаємо включення (includes)
        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        // Додаємо фільтрацію, якщо потрібно
        if (!string.IsNullOrEmpty(filter))
        {
            // Фільтруємо за доступними полями
            query = query.Where(c =>
                c.Name.Contains(filter) ||
                c.Description.Contains(filter) ||
                c.Address.City.Contains(filter) ||
                c.Address.Region.Contains(filter) ||
                c.Address.Street.Contains(filter) ||
                c.Address.PostalCode.Contains(filter));
        }

        // Додаємо сортування, якщо потрібно
        if (!string.IsNullOrEmpty(orderBy))
        {
            // Перевіряємо, чи існує властивість для сортування
            switch (orderBy.ToLower())
            {
                case "name":
                    query = descending
                        ? query.OrderByDescending(c => c.Name)
                        : query.OrderBy(c => c.Name);
                    break;
                case "city":
                    query = descending
                        ? query.OrderByDescending(c => c.Address.City)
                        : query.OrderBy(c => c.Address.City);
                    break;
                case "region":
                    query = descending
                        ? query.OrderByDescending(c => c.Address.Region)
                        : query.OrderBy(c => c.Address.Region);
                    break;
                default:
                    // За замовчуванням сортуємо за ім'ям
                    query = query.OrderBy(c => c.Name);
                    break;
            }
        }
        else
        {
            // За замовчуванням сортуємо за ім'ям
            query = query.OrderBy(c => c.Name);
        }

        // Додаємо пагінацію, якщо потрібно
        if (page.HasValue && pageSize.HasValue)
        {
            query = query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task ApproveAsync(Guid id, Guid userId, CancellationToken cancellationToken)
    {
        var company = await _dbSet.FindAsync(new object[] { id }, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {id} не знайдено");

        company.Approve(userId);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BulkApproveAsync(List<Guid> ids, Guid userId, CancellationToken cancellationToken)
    {
        foreach (var id in ids)
        {
            await ApproveAsync(id, userId, cancellationToken);
        }
    }

    public async Task RejectAsync(Guid id, CancellationToken cancellationToken)
    {
        var company = await _dbSet.FindAsync(new object[] { id }, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {id} не знайдено");

        // Скидаємо статус схвалення
        company.ApprovedAt = null;
        company.ApprovedByUserId = null;

        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BulkRejectAsync(List<Guid> ids, CancellationToken cancellationToken)
    {
        foreach (var id in ids)
        {
            await RejectAsync(id, cancellationToken);
        }
    }
}
