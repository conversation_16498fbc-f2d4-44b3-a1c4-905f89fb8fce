<template>
  <div class="order-edit-new">
    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
      </div>
      <p class="loading-text">Loading order...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error && !order" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="error-title">Error Loading Order</h3>
      <p class="error-message">{{ error }}</p>
      <button class="retry-btn" @click="fetchOrder(route.params.id)">
        <i class="fas fa-redo"></i>
        Try Again
      </button>
    </div>

    <!-- Order Not Found -->
    <div v-else-if="!order && !loading" class="not-found-state">
      <div class="not-found-icon">
        <i class="fas fa-shopping-cart"></i>
      </div>
      <h3 class="not-found-title">Order Not Found</h3>
      <p class="not-found-message">The requested order does not exist or has been deleted.</p>
      <router-link to="/admin/orders" class="back-btn">
        <i class="fas fa-arrow-left"></i>
        Back to Orders
      </router-link>
    </div>

    <!-- Edit Form -->
    <div v-else class="edit-content">
      <!-- Header -->
      <div class="edit-header">
        <div class="header-content">
          <nav class="breadcrumb">
            <router-link to="/admin" class="breadcrumb-item">Dashboard</router-link>
            <span class="breadcrumb-separator">/</span>
            <router-link to="/admin/orders" class="breadcrumb-item">Orders</router-link>
            <span class="breadcrumb-separator">/</span>
            <router-link :to="`/admin/orders/${order.id}`" class="breadcrumb-item">Order #{{ order.id }}</router-link>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item breadcrumb-current">Edit</span>
          </nav>
          <h1 class="edit-title">Edit Order #{{ order.id }}</h1>
          <p class="edit-subtitle">Update order information and manage items</p>
        </div>
        <div class="header-actions">
          <button
            type="button"
            class="action-btn action-btn-primary"
            @click.prevent="openUpdateStatusModal">
            <i class="fas fa-edit"></i>
            Update Status
          </button>
          <router-link :to="`/admin/orders/${order.id}`" class="action-btn action-btn-secondary">
            <i class="fas fa-eye"></i>
            View Order
          </router-link>
          <router-link to="/admin/orders" class="action-btn action-btn-light">
            <i class="fas fa-arrow-left"></i>
            Back to Orders
          </router-link>
        </div>
      </div>

      <!-- Messages -->
      <div v-if="error" class="message message-error">
        <div class="message-content">
          <i class="fas fa-exclamation-circle"></i>
          <span>{{ error }}</span>
        </div>
        <button class="message-close" @click="error = ''">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div v-if="success" class="message message-success">
        <div class="message-content">
          <i class="fas fa-check-circle"></i>
          <span>{{ success }}</span>
        </div>
        <button class="message-close" @click="success = ''">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="submitForm" class="edit-form">
        <!-- Order Information Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-info-circle"></i>
              Order Information
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">Order ID</label>
                <input 
                  class="form-input form-input-readonly" 
                  type="text" 
                  :value="order.id"
                  readonly>
              </div>

              <div class="form-field">
                <label class="form-label">Customer</label>
                <input 
                  class="form-input form-input-readonly" 
                  type="text" 
                  :value="customerName"
                  readonly>
              </div>
            </div>

            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">Order Status</label>
                <select v-model="form.status" class="form-select">
                  <option value="processing">Processing</option>
                  <option value="pending">Pending</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div class="form-field">
                <label class="form-label">Payment Status</label>
                <select v-model="form.paymentStatus" class="form-select">
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="refunded">Refunded</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
            </div>

            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">Total Price</label>
                <input
                  class="form-input form-input-readonly"
                  type="text"
                  :value="formatCurrency(order.totalPriceAmount || order.totalPrice?.amount || 0)"
                  readonly>
                <small class="field-note">Total price is calculated automatically from order items</small>
              </div>

              <div class="form-field">
                <label class="form-label">Created At</label>
                <input 
                  class="form-input form-input-readonly" 
                  type="text" 
                  :value="formatDateTime(order.createdAt)"
                  readonly>
              </div>
            </div>
          </div>
        </div>

        <!-- Customer Information Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-user"></i>
              Customer Information
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">Customer Name</label>
                <input
                  class="form-input form-input-readonly"
                  type="text"
                  :value="order.customerName || 'N/A'"
                  readonly>
              </div>

              <div class="form-field">
                <label class="form-label">Customer Email</label>
                <input
                  class="form-input form-input-readonly"
                  type="text"
                  :value="order.customerEmail || 'N/A'"
                  readonly>
              </div>
            </div>
          </div>
        </div>

        <!-- Shipping Information Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-shipping-fast"></i>
              Shipping Information
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">Address Line</label>
                <input
                  class="form-input"
                  type="text"
                  v-model="form.shippingAddressLine"
                  placeholder="Enter shipping address">
              </div>

              <div class="form-field">
                <label class="form-label">Country</label>
                <input
                  class="form-input"
                  type="text"
                  v-model="form.shippingCountry"
                  placeholder="Enter country">
              </div>
            </div>

            <div class="form-grid">
              <div class="form-field">
                <label class="form-label">City</label>
                <input
                  class="form-input"
                  type="text"
                  v-model="form.shippingCity"
                  placeholder="Enter city">
              </div>

              <div class="form-field">
                <label class="form-label">Shipping Method</label>
                <select v-model="form.shippingMethodId" class="form-select">
                  <option value="">Select shipping method</option>
                  <option
                    v-for="method in shippingMethods"
                    :key="method.id"
                    :value="method.id">
                    {{ method.name }} - {{ method.priceAmount }} {{ method.priceCurrency }} ({{ method.estimatedDays }} days)
                  </option>
                </select>
              </div>
            </div>

            <div class="form-field">
              <label class="form-label">Postal Code</label>
              <input
                class="form-input"
                type="text"
                v-model="form.postalCode"
                placeholder="Enter postal code">
            </div>
          </div>
        </div>

        <!-- Notes Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-sticky-note"></i>
              Order Notes
            </h3>
          </div>
          <div class="form-card-content">
            <div class="form-field">
              <label class="form-label">Notes</label>
              <textarea
                v-model="form.notes"
                class="form-textarea"
                placeholder="Add notes about this order..."
                rows="4">
              </textarea>
              <small class="field-note">Internal notes about this order (not visible to customer)</small>
            </div>
          </div>
        </div>

        <!-- Order Items Card -->
        <div class="form-card">
          <div class="form-card-header">
            <h3 class="form-card-title">
              <i class="fas fa-box"></i>
              Order Items ({{ orderItems.length }})
            </h3>
            <div class="form-card-actions">
              <button
                type="button"
                class="action-btn action-btn-primary action-btn-sm"
                @click.prevent="openAddProductModal">
                <i class="fas fa-plus"></i>
                Add Product
              </button>
            </div>
          </div>
          <div class="form-card-content">
            <!-- Items Loading -->
            <div v-if="loadingItems" class="items-loading">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <p class="loading-text">Loading order items...</p>
            </div>

            <!-- No Items -->
            <div v-else-if="orderItems.length === 0" class="no-items">
              <div class="no-items-icon">
                <i class="fas fa-box-open"></i>
              </div>
              <h4 class="no-items-title">No Items in Order</h4>
              <p class="no-items-message">Add products to this order using the button above.</p>
            </div>

            <!-- Items Table -->
            <div v-else class="items-table-wrapper">
              <div class="items-table">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Unit Price</th>
                      <th>Quantity</th>
                      <th>Total Price</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in orderItems" :key="item.id || index" class="table-row">
                      <td class="product-cell">
                        <div class="product-info">
                          <div class="product-image" v-if="item.productImage">
                            <img :src="item.productImage" :alt="item.productName" @error="handleProductImageError">
                          </div>
                          <div class="product-details">
                            <h4 class="product-name">{{ item.productName || 'Unknown Product' }}</h4>
                            <p class="product-id">Product ID: {{ item.productId }}</p>
                          </div>
                        </div>
                      </td>
                      <td class="price-cell">
                        <span class="price-display">{{ formatCurrency(item.price || item.priceAmount || 0) }}</span>
                        <small class="price-note">Price is set in product</small>
                      </td>
                      <td class="quantity-cell">
                        <div class="quantity-controls">
                          <button 
                            type="button"
                            class="quantity-btn quantity-btn-minus"
                            @click="decreaseQuantity(item)"
                            :disabled="item.quantity <= 1">
                            <i class="fas fa-minus"></i>
                          </button>
                          <input 
                            class="quantity-input" 
                            type="number" 
                            min="1"
                            v-model.number="item.quantity"
                            @input="updateItemTotal(item)">
                          <button 
                            type="button"
                            class="quantity-btn quantity-btn-plus"
                            @click="increaseQuantity(item)">
                            <i class="fas fa-plus"></i>
                          </button>
                        </div>
                      </td>
                      <td class="total-cell">
                        <span class="total-price">{{ formatCurrency(item.total || (item.price * item.quantity) || 0) }}</span>
                      </td>
                      <td class="actions-cell">
                        <div class="action-buttons">
                          <button
                            type="button"
                            class="action-btn action-btn-sm action-btn-danger"
                            @click="removeItem(index)"
                            title="Remove Item">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Order Total -->
              <div class="order-total">
                <div class="total-row">
                  <span class="total-label">Order Total:</span>
                  <span class="total-value">{{ formatCurrency(calculateOrderTotal()) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button 
            type="submit" 
            class="action-btn action-btn-primary action-btn-large"
            :disabled="saving"
            :class="{ 'action-btn-loading': saving }">
            <i class="fas fa-spinner fa-spin" v-if="saving"></i>
            <i class="fas fa-save" v-else></i>
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
          <router-link 
            :to="`/admin/orders/${order.id}`" 
            class="action-btn action-btn-secondary action-btn-large">
            <i class="fas fa-times"></i>
            Cancel
          </router-link>
        </div>
      </form>
    </div>

    <!-- Add Product Modal -->
    <div class="modal" :class="{ 'modal-active': showAddProductModal }">
      <div class="modal-backdrop" @click="closeAddProductModal"></div>
      <div class="modal-content modal-content-large">
        <div class="modal-header">
          <h3 class="modal-title">Add Product to Order</h3>
          <button class="modal-close" @click="closeAddProductModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <ProductSearchSelector
            v-model="selectedProduct"
            @product-selected="handleProductSelected"
          />
          
          <div v-if="selectedProduct" class="selected-product-info">
            <h4>Selected Product</h4>
            <div class="product-preview">
              <div class="product-image" v-if="selectedProduct.image">
                <img :src="selectedProduct.image" :alt="selectedProduct.name">
              </div>
              <div class="product-details">
                <h5>{{ selectedProduct.name }}</h5>
                <p>Price: {{ formatCurrency(selectedProduct.price) }}</p>
              </div>
            </div>
            
            <div class="form-field">
              <label class="form-label">Quantity</label>
              <input 
                class="form-input" 
                type="number" 
                min="1"
                v-model.number="newItemQuantity"
                placeholder="1">
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="action-btn action-btn-primary"
            @click="addProductToOrder"
            :disabled="!selectedProduct || !newItemQuantity">
            <i class="fas fa-plus"></i>
            Add to Order
          </button>
          <button class="action-btn action-btn-secondary" @click="closeAddProductModal">Cancel</button>
        </div>
      </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal" :class="{ 'modal-active': showUpdateStatusModal }">
      <div class="modal-backdrop" @click="closeUpdateStatusModal"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Update Order Status</h3>
          <button class="modal-close" @click="closeUpdateStatusModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-field">
            <label class="form-label">Order Status</label>
            <select v-model="statusForm.status" class="form-select">
              <option value="processing">Processing</option>
              <option value="pending">Pending</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div class="form-field">
            <label class="form-label">Payment Status</label>
            <select v-model="statusForm.paymentStatus" class="form-select">
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="refunded">Refunded</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="action-btn action-btn-primary"
            @click="updateOrderStatus"
            :disabled="updatingStatus">
            <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': updatingStatus }"></i>
            Update Status
          </button>
          <button class="action-btn action-btn-secondary" @click="closeUpdateStatusModal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ordersService } from '@/admin/services/orders';
import { shippingMethodsService } from '@/admin/services/shippingMethods';
import ProductSearchSelector from '@/admin/components/products/ProductSearchSelector.vue';
import {
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass
} from '@/admin/utils/orderConstants';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(false);
const loadingItems = ref(false);
const saving = ref(false);
const error = ref('');
const success = ref('');
const order = ref(null);
const orderItems = ref([]);
const shippingMethods = ref([]);

// UI state
const showAddProductModal = ref(false);
const showUpdateStatusModal = ref(false);
const selectedProduct = ref(null);
const newItemQuantity = ref(1);
const updatingStatus = ref(false);

// Form data
const form = reactive({
  status: '',
  paymentStatus: '',
  totalPrice: 0,
  notes: '',
  shippingAddressLine: '',
  shippingCountry: '',
  shippingCity: '',
  shippingMethodId: '',
  postalCode: ''
});

// Status update form
const statusForm = reactive({
  status: '',
  paymentStatus: ''
});

// Computed properties
const customerName = computed(() => {
  return order.value?.customerName || order.value?.customer?.name || 'Unknown Customer';
});

const orderId = computed(() => route.params.id);

// Methods
const fetchOrder = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    const orderData = await ordersService.getById(id);
    order.value = orderData;

    // Populate form with order data - keep status as text for dropdown
    form.status = getOrderStatusText(orderData.status).toLowerCase() || 'processing';
    form.paymentStatus = getPaymentStatusText(orderData.paymentStatus).toLowerCase() || 'pending';
    form.totalPrice = orderData.totalPriceAmount || orderData.totalPrice?.amount || 0;
    form.notes = orderData.notes || '';
    form.shippingAddressLine = orderData.shippingAddress?.address1 || orderData.shippingAddressLine || '';
    form.shippingCountry = orderData.shippingAddress?.country || orderData.shippingCountry || '';
    form.shippingCity = orderData.shippingAddress?.city || orderData.shippingCity || '';
    form.shippingMethodId = orderData.shippingMethodId || '';
    form.postalCode = orderData.postalCode || '';

    // Initialize status form
    statusForm.status = form.status;
    statusForm.paymentStatus = form.paymentStatus;

    await fetchOrderItems(orderData.id);

  } catch (err) {
    console.error('Error fetching order:', err);
    if (err.name !== 'CanceledError' && err.code !== 'ERR_CANCELED') {
      error.value = 'Failed to load order. Please try again.';
    }
  } finally {
    loading.value = false;
  }
};

const fetchShippingMethods = async () => {
  try {
    const response = await shippingMethodsService.getShippingMethods({ pageSize: 100 });
    shippingMethods.value = response.data || [];
  } catch (err) {
    console.error('Error fetching shipping methods:', err);
    // Fallback to empty array if API fails
    shippingMethods.value = [];
  }
};

const fetchOrderItems = async (orderId) => {
  loadingItems.value = true;
  try {
    const response = await ordersService.getOrderItems(orderId);
    orderItems.value = response.data || response || [];
  } catch (err) {
    console.error('Error fetching order items:', err);
    orderItems.value = [];
  } finally {
    loadingItems.value = false;
  }
};



// Utility methods
const formatCurrency = (amount) => {
  if (amount == null) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(amount);
};

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString('uk-UA');
};

const calculateOrderTotal = () => {
  return orderItems.value.reduce((total, item) => {
    const itemPrice = item.price || item.priceAmount || 0;
    const itemQuantity = item.quantity || 0;
    return total + (itemPrice * itemQuantity);
  }, 0);
};

// Item management methods
const updateItemTotal = (item) => {
  const itemPrice = item.price || item.priceAmount || 0;
  item.total = itemPrice * (item.quantity || 0);
  // Update form total price
  form.totalPrice = calculateOrderTotal();
};

const increaseQuantity = (item) => {
  item.quantity = (item.quantity || 0) + 1;
  updateItemTotal(item);
};

const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    item.quantity = item.quantity - 1;
    updateItemTotal(item);
  }
};

const removeItem = (index) => {
  if (confirm('Are you sure you want to remove this item from the order?')) {
    orderItems.value.splice(index, 1);
    form.totalPrice = calculateOrderTotal();
  }
};

// Product selection methods
const handleProductSelected = (product) => {
  selectedProduct.value = product;
  newItemQuantity.value = 1;
};

const addProductToOrder = () => {
  if (!selectedProduct.value || !newItemQuantity.value) return;

  const itemPrice = selectedProduct.value.price || 0;
  const newItem = {
    id: Date.now(), // Temporary ID for new items
    productId: selectedProduct.value.id,
    productName: selectedProduct.value.name,
    price: itemPrice,
    priceAmount: itemPrice,
    quantity: newItemQuantity.value,
    total: itemPrice * newItemQuantity.value
  };

  orderItems.value.push(newItem);
  form.totalPrice = calculateOrderTotal();

  closeAddProductModal();
};

const openAddProductModal = () => {
  console.log('Opening add product modal...');
  showAddProductModal.value = true;
  console.log('showAddProductModal.value:', showAddProductModal.value);
};

const closeAddProductModal = () => {
  showAddProductModal.value = false;
  selectedProduct.value = null;
  newItemQuantity.value = 1;
};

// Update Status Modal methods
const openUpdateStatusModal = () => {
  console.log('Opening update status modal...');
  statusForm.status = form.status;
  statusForm.paymentStatus = form.paymentStatus;
  showUpdateStatusModal.value = true;
  console.log('showUpdateStatusModal.value:', showUpdateStatusModal.value);
};

const closeUpdateStatusModal = () => {
  showUpdateStatusModal.value = false;
  updatingStatus.value = false;
};

const updateOrderStatus = async () => {
  if (updatingStatus.value) return;

  try {
    updatingStatus.value = true;
    error.value = '';

    await ordersService.updateOrderStatus(route.params.id, {
      status: statusForm.status,
      paymentStatus: statusForm.paymentStatus
    });

    // Update local form data
    form.status = statusForm.status;
    form.paymentStatus = statusForm.paymentStatus;

    // Update order data
    if (order.value) {
      order.value.status = statusForm.status;
      order.value.paymentStatus = statusForm.paymentStatus;
    }

    success.value = 'Order status updated successfully!';
    closeUpdateStatusModal();

  } catch (err) {
    console.error('Error updating order status:', err);
    error.value = err.response?.data?.message || 'Failed to update order status';
  } finally {
    updatingStatus.value = false;
  }
};

// Image error handlers
const handleProductImageError = (event) => {
  event.target.style.display = 'none';
};

const submitForm = async () => {
  saving.value = true;
  error.value = '';
  success.value = '';

  try {
    // Prepare form data
    const formData = {
      status: form.status,
      paymentStatus: form.paymentStatus,
      shippingAddress: form.shippingAddressLine,
      shippingMethodId: form.shippingMethodId,
      postalCode: form.postalCode,
      notes: form.notes,
      items: orderItems.value.map(item => ({
        id: item.id,
        quantity: item.quantity,
        priceAmount: item.price || item.priceAmount || 0,
        priceCurrency: 'UAH'
      }))
    };

    // Update order
    await ordersService.update(orderId.value, formData);

    success.value = 'Order updated successfully!';

    // Refresh order data with a small delay to avoid DOM issues
    setTimeout(async () => {
      await fetchOrder(orderId.value);
    }, 100);

  } catch (err) {
    console.error('Error updating order:', err);
    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to update order. Please try again.';
    }
  } finally {
    saving.value = false;
  }
};

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchOrder(newId);
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  // Load shipping methods first
  await fetchShippingMethods();

  const id = route.params.id;
  if (id) {
    fetchOrder(id);
  }
});
</script>

<style scoped>
/* Base Styles */
.order-edit-new {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* Loading States */
.loading-state,
.items-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  font-size: 2rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1rem;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.retry-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Not Found State */
.not-found-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.not-found-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.not-found-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.not-found-message {
  color: #6b7280;
  margin-bottom: 2rem;
}

.back-btn {
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

/* Header */
.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

.breadcrumb-current {
  color: #1f2937;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d1d5db;
}

.edit-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.edit-subtitle {
  color: #6b7280;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-secondary {
  background: #6b7280;
  color: white;
}

.action-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-light {
  background: #f3f4f6;
  color: #374151;
}

.action-btn-light:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
  color: #374151;
  text-decoration: none;
}

.action-btn-danger {
  background: #ef4444;
  color: white;
}

.action-btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.action-btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}

.action-btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.action-btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Messages */
.message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.message-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.message-success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.message-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Form */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-card-header {
  background: #f8fafc;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-card-title i {
  color: #6b7280;
}

.form-card-actions {
  display: flex;
  gap: 0.5rem;
}

.form-card-content {
  padding: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.form-field:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-readonly {
  background: #f8fafc;
  color: #6b7280;
  cursor: not-allowed;
}

/* No Items State */
.no-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.no-items-icon {
  font-size: 2.5rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.no-items-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.no-items-message {
  color: #6b7280;
}

/* Items Table */
.items-table-wrapper {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.items-table {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f8fafc;
  padding: 1rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
}

.table td {
  padding: 1rem 1.5rem;
  vertical-align: middle;
}

/* Product Cell */
.product-cell {
  min-width: 250px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.product-id {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Price Cell */
.price-input {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  width: 100px;
  transition: border-color 0.2s ease;
}

.price-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Quantity Cell */
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
}

.quantity-btn:hover {
  background: #e5e7eb;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  width: 60px;
  text-align: center;
  transition: border-color 0.2s ease;
}

.quantity-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Total Cell */
.total-price {
  font-size: 1rem;
  font-weight: 600;
  color: #059669;
}

/* Actions Cell */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Order Total */
.order-total {
  background: #f8fafc;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.total-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #059669;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-active {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.modal-content-large {
  max-width: 700px;
}

.modal-active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

/* Product Selection */
.selected-product-info {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.selected-product-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.product-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.product-preview .product-image {
  width: 60px;
  height: 60px;
}

.product-preview .product-details h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.product-preview .product-details p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Form Textarea */
.form-textarea {
  width: 100%;
  min-height: 100px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Field Notes */
.field-note {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

/* Price Display */
.price-display {
  font-weight: 600;
  color: #059669;
  font-size: 0.875rem;
}

.price-note {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .order-edit-new {
    padding: 1.5rem;
  }

  .edit-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-btn-large {
    justify-content: center;
  }

  .quantity-controls {
    flex-direction: column;
    gap: 0.25rem;
  }

  .quantity-input {
    width: 80px;
  }
}

@media (max-width: 768px) {
  .order-edit-new {
    padding: 1rem;
  }

  .edit-header {
    padding: 1.5rem;
  }

  .edit-title {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    justify-content: center;
  }

  .form-card-content {
    padding: 1rem;
  }

  .form-actions {
    padding: 1.5rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 1rem;
  }

  .product-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .product-image {
    width: 40px;
    height: 40px;
  }

  .price-input {
    width: 80px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn-sm {
    padding: 0.5rem;
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    flex-wrap: wrap;
  }

  .edit-title {
    font-size: 1.25rem;
  }

  .form-card-title {
    font-size: 1rem;
  }

  .form-card-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .form-card-content {
    padding: 1rem;
  }

  .form-actions {
    padding: 1rem;
  }

  .items-table {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.75rem;
  }

  .product-name {
    font-size: 0.875rem;
  }

  .product-id {
    font-size: 0.75rem;
  }

  .total-label {
    font-size: 1rem;
  }

  .total-value {
    font-size: 1.25rem;
  }
}

/* Notes Styles */
.notes-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.note-item {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  margin-bottom: 0.5rem;
  border-radius: 4px;
}

.note-item:last-child {
  margin-bottom: 0;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.note-author {
  font-weight: 600;
  color: #374151;
}

.note-date {
  color: #6b7280;
}

.note-content {
  color: #1f2937;
  line-height: 1.5;
  white-space: pre-wrap;
}
</style>
