﻿﻿using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Product;

public class GetProductsByCompanySlugQueryHandler :
    PaginatedQueryHandler<GetProductsByCompanySlugQuery, Domain.Entities.Product, ProductResponse>,
    IRequestHandler<GetProductsByCompanySlugQuery, PaginatedResponse<ProductResponse>>
{
    private readonly IProductRepository _productRepository;
    private readonly ICompanyRepository _companyRepository;

    public GetProductsByCompanySlugQueryHandler(
        IProductRepository productRepository,
        ICompanyRepository companyRepository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _productRepository = productRepository;
        _companyRepository = companyRepository;
    }

    public async Task<PaginatedResponse<ProductResponse>> Handle(GetProductsByCompanySlugQuery request, CancellationToken cancellationToken)
    {
        // Отримуємо ID компанії за slug (більш ефективно)
        var companyId = await _companyRepository.GetIdBySlugAsync(request.Slug, cancellationToken);
        if (companyId == null)
            throw new InvalidOperationException($"Company with slug '{request.Slug}' not found.");

        // Визначаємо параметри для включення (includes)
        var includes = new List<Expression<Func<Domain.Entities.Product, object>>>();

        // Додаємо включення на основі параметра include
        if (!string.IsNullOrEmpty(request.Include))
        {
            var includeParams = request.Include.Split(',');
            foreach (var param in includeParams)
            {
                switch (param.Trim().ToLower())
                {
                    case "company":
                        includes.Add(p => p.Company);
                        break;
                    case "category":
                        includes.Add(p => p.Category);
                        break;
                    case "images":
                        //includes.Add(p => p.Images);
                        break;
                }
            }
        }

        // Створюємо фільтр для продуктів компанії
        Expression<Func<Domain.Entities.Product, bool>> filter = p => p.CompanyId == companyId.Value;

        // Додаємо фільтрацію за текстом, якщо вказано
        if (!string.IsNullOrEmpty(request.Filter))
        {
            filter = p => p.CompanyId == companyId.Value &&
                (p.Name.Contains(request.Filter) || p.Description.Contains(request.Filter));
        }

        // Отримуємо пагіновані дані
        var pagedResult = await _productRepository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy,
            descending: request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 15,
            cancellationToken: cancellationToken,
            includes: includes.ToArray());

        // Створюємо пагіновану відповідь
        return CreatePaginatedResponse(request, pagedResult, $"companies/{request.Slug}/products");
    }
}
