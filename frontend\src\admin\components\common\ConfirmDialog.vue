<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="cancel"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">{{ title }}</p>
        <button class="delete" aria-label="close" @click="cancel"></button>
      </header>
      <section class="modal-card-body">
        <p>{{ message }}</p>
      </section>
      <footer class="modal-card-foot">
        <button
          class="button"
          :class="cancelButtonClass"
          @click="cancel">
          {{ cancelText }}
        </button>
        <button
          class="button"
          :class="confirmButtonClass"
          @click="confirm">
          {{ confirmText }}
        </button>
      </footer>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: 'Confirm Action'
  },
  message: {
    type: String,
    default: 'Are you sure you want to perform this action?'
  },
  confirmText: {
    type: String,
    default: 'Confirm'
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  },
  confirmButtonClass: {
    type: String,
    default: 'is-danger'
  },
  cancelButtonClass: {
    type: String,
    default: 'is-light'
  }
});

const emit = defineEmits(['confirm', 'cancel']);

const confirm = () => {
  emit('confirm');
};

const cancel = () => {
  emit('cancel');
};
</script>

<style scoped>
/* Modal positioning fix */
.modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: none !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal.is-active {
  display: flex !important;
}

.modal-background {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal-card {
  position: relative !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  margin: 0 !important;
  z-index: 1001 !important;
}

.modal-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 1.5rem !important;
  border-bottom: none !important;
}

.modal-card-title {
  font-weight: 600 !important;
  color: white !important;
  margin: 0 !important;
  font-size: 1.25rem !important;
}

.modal-card-body {
  padding: 2rem !important;
  background: white !important;
  color: #333 !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
}

.modal-card-foot {
  justify-content: flex-end !important;
  padding: 1.5rem !important;
  background: #f8f9fa !important;
  border-top: 1px solid #e9ecef !important;
  gap: 0.75rem !important;
}

.delete {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
}

.delete:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

.button {
  padding: 0.75rem 1.5rem !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.button.is-danger {
  background-color: #dc3545 !important;
  color: white !important;
}

.button.is-danger:hover {
  background-color: #c82333 !important;
}

.button.is-primary {
  background-color: #007bff !important;
  color: white !important;
}

.button.is-primary:hover {
  background-color: #0056b3 !important;
}

.button.is-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.button.is-warning:hover {
  background-color: #e0a800 !important;
}

.button.is-success {
  background-color: #28a745 !important;
  color: white !important;
}

.button.is-success:hover {
  background-color: #218838 !important;
}

.button.is-light {
  background-color: #f8f9fa !important;
  color: #495057 !important;
  border: 1px solid #dee2e6 !important;
}

.button.is-light:hover {
  background-color: #e2e6ea !important;
}
</style>
