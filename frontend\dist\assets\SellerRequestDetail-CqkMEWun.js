import{_ as ks,g as v,h as p,f as qs,i as Rs,c as i,a as s,b as _,d as o,w as k,k as u,T as ss,t as l,r as Ds,s as Cs,F as es,p as as,n as q,x as Ss,D as Ns,e as ws,M as R,o as n}from"./index-L-hJxM_5.js";import{s as N}from"./seller-requests-CWXKDmna.js";import{S as ts}from"./StatusBadge-CWG2IOJy.js";import{C as As}from"./ConfirmDialog-hd0r6dWx.js";/* empty css                                                                    */const js={class:"admin-page"},xs={class:"admin-page-header"},Is={class:"admin-page-actions"},Ts={key:"pending-actions",class:"admin-action-buttons"},Bs=["disabled"],Ps=["disabled"],Fs={key:"approved-status",class:"admin-status-badge admin-status-approved"},Ms={key:"rejected-status",class:"admin-status-badge admin-status-rejected"},Es={key:0,class:"admin-loading-state"},Us={key:1,class:"admin-alert admin-alert-danger"},$s={key:2,class:"admin-alert admin-alert-warning"},Ls={key:3,class:"admin-seller-request-content"},Os={class:"admin-seller-request-layout"},Ws={class:"admin-card admin-seller-request-section"},zs={class:"admin-card-content"},Hs={class:"applicant-profile-section"},Gs={class:"applicant-avatar"},Js={class:"applicant-info"},Ks={class:"applicant-name"},Qs={class:"applicant-email"},Xs={class:"applicant-badges"},Ys={class:"admin-badge admin-badge-info"},Zs={class:"admin-info-grid admin-info-grid-2"},Vs={class:"admin-info-group"},se={class:"admin-info-value"},ee={class:"admin-info-group"},ae={class:"admin-info-value"},te={key:0,class:"fas fa-check-circle admin-text-success"},ne={key:1,class:"fas fa-times-circle admin-text-danger"},ie={class:"admin-info-group"},le={class:"admin-info-value"},oe={class:"admin-info-group"},de={class:"admin-info-value"},re={class:"admin-card-actions"},ce={class:"admin-card admin-seller-request-section"},ue={class:"admin-card-content"},me={key:0,class:"company-image-section"},fe=["src","alt"],ve={class:"admin-info-grid admin-info-grid-2"},pe={class:"admin-info-group"},_e={class:"admin-info-value"},ye={class:"admin-info-group"},ge={class:"admin-info-value"},be=["href"],he={key:1,class:"admin-text-muted"},ke={class:"admin-info-group"},qe={class:"admin-info-value"},Re=["href"],De={key:1,class:"admin-text-muted"},Ce={class:"admin-info-group"},Se={class:"admin-info-value"},Ne={class:"admin-info-group"},we={class:"admin-info-value admin-info-value-multiline"},Ae={key:1,class:"admin-info-group"},je={class:"admin-info-value"},xe={key:2,class:"admin-info-group"},Ie={class:"admin-info-value admin-info-value-multiline"},Te={class:"admin-card",style:{"grid-area":"finance"}},Be={class:"admin-card-content"},Pe={class:"finance-section"},Fe={class:"admin-info-grid admin-info-grid-2"},Me={class:"admin-info-group"},Ee={class:"admin-info-value"},Ue={class:"admin-info-group"},$e={class:"admin-info-value"},Le={class:"admin-info-group"},Oe={class:"admin-info-value admin-info-value-code"},We={class:"admin-info-group"},ze={class:"admin-info-value admin-info-value-code"},He={key:0,class:"finance-section"},Ge={class:"admin-info-group"},Je={class:"admin-info-value admin-info-value-multiline"},Ke={class:"admin-card admin-seller-request-section"},Qe={class:"admin-card-content"},Xe={key:0,class:"schedule-table-wrapper"},Ye={class:"admin-table schedule-table"},Ze={class:"day-cell"},Ve={class:"day-info"},sa={class:"day-name"},ea={class:"status-cell"},aa={class:"time-cell"},ta={key:0,class:"time-range"},na={class:"time-start"},ia={class:"time-end"},la={key:1,class:"time-closed"},oa={key:1,class:"admin-empty-state"},da={class:"admin-card admin-seller-request-section"},ra={class:"admin-card-content"},ca={key:0,class:"admin-info-group"},ua={class:"admin-info-value admin-info-value-multiline"},ma={key:1,class:"admin-empty-state"},fa={key:0,class:"admin-card admin-seller-request-section"},va={class:"admin-card-content"},pa={class:"admin-documents-list"},_a={class:"admin-document-icon"},ya={class:"admin-document-info"},ga={class:"admin-document-title"},ba={class:"admin-document-type"},ha={class:"admin-document-actions"},ka=["href"],qa={key:"rejection-reason",class:"admin-card admin-card-danger admin-seller-request-section"},Ra={class:"admin-card-content"},Da={class:"admin-info-group"},Ca={class:"admin-info-value admin-info-value-multiline"},Sa={class:"modal-card"},Na={class:"modal-card-body"},wa={class:"admin-form-group admin-mt-4"},Aa={class:"modal-card-foot"},ja=["disabled"],xa={key:0,class:"fas fa-spinner fa-spin"},Ia={key:1,class:"fas fa-times"},Ta={__name:"SellerRequestDetail",setup(Ba){const ns=qs(),is=ws(),D=v(!0),m=v(null),t=v({}),c=v(!1),g=v(!1),b=v(!1),f=v(""),C=p(()=>ns.params.id),y=p(()=>{var a;return ms((a=t.value)==null?void 0:a.status)}),ls=p(()=>y.value==="pending"),os=p(()=>y.value==="approved"),w=p(()=>y.value==="rejected"),ds=p(()=>{var a;return w.value&&((a=t.value)==null?void 0:a.rejectionReason)}),rs=()=>{is.push("/admin/seller-requests")},cs=async()=>{D.value=!0,m.value=null;try{const a=await N.getSellerRequest(C.value);t.value=a.data}catch(a){console.error("Error fetching seller request:",a),m.value="Failed to load seller request data. Please try again."}finally{D.value=!1}},A=()=>{var d;const a=(d=t.value)==null?void 0:d.companyRequestData;if(!a)return"N/A";const e=[a.addressStreet,a.addressCity,a.addressRegion,a.addressPostalCode].filter(Boolean);return e.length>0?e.join(", "):"N/A"},us=a=>{if(a==null)return"Not specified";if(typeof a=="string")return{Monday:"Monday",Tuesday:"Tuesday",Wednesday:"Wednesday",Thursday:"Thursday",Friday:"Friday",Saturday:"Saturday",Sunday:"Sunday"}[a]||a;const e=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];let d=typeof a=="string"?parseInt(a,10):a;return isNaN(d)?"Not specified":d>=1&&d<=7?(d=d===7?0:d,e[d]||"Invalid day"):d>=0&&d<=6&&e[d]||"Invalid day"},ms=a=>{if(typeof a=="string")return a.toLowerCase();switch(a){case 0:return"pending";case 1:return"approved";case 2:return"rejected";default:return"pending"}},j=a=>a?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(a)):"",fs=a=>a?(a=a.toLowerCase(),a.includes("pdf")?"fas fa-file-pdf":a.includes("word")||a.includes("doc")?"fas fa-file-word":a.includes("excel")||a.includes("xls")?"fas fa-file-excel":a.includes("image")||a.includes("jpg")||a.includes("png")?"fas fa-file-image":"fas fa-file"):"fas fa-file",vs=a=>a?a.replace(/\s/g,"").replace(/(.{4})/g,"$1 ").trim():"N/A",ps=()=>{g.value=!0},_s=()=>{f.value="",b.value=!0},h=()=>{g.value=!1,b.value=!1,f.value=""},ys=async()=>{if(!c.value){c.value=!0;try{await N.approveSellerRequest(C.value),g.value=!1,await R();const a={...t.value,status:1,approvedAt:new Date().toISOString()};t.value=a,await R()}catch(a){console.error("Error approving seller request:",a),m.value="Failed to approve seller request. Please try again."}finally{c.value=!1}}},gs=async()=>{if(!c.value){c.value=!0;try{await N.rejectSellerRequest(C.value,f.value);const a=f.value||"";b.value=!1,f.value="",await R();const e={...t.value,status:2,rejectedAt:new Date().toISOString(),rejectionReason:a};t.value=e,await R()}catch(a){console.error("Error rejecting seller request:",a),m.value="Failed to reject seller request. Please try again."}finally{c.value=!1}}},bs=a=>a?a.substring(0,2).toUpperCase():"?";return Rs(()=>{cs()}),(a,e)=>{var S,x,I,T,B,P,F,M,E,U,$,L,O,W,z,H,G,J,K,Q,X,Y,Z,V;const d=Ds("router-link");return n(),i("div",js,[s("div",xs,[s("div",{class:"admin-page-title-section"},[s("button",{onClick:rs,class:"admin-btn admin-btn-ghost admin-btn-sm"},e[2]||(e[2]=[s("i",{class:"fas fa-arrow-left"},null,-1),o(" Back to Seller Requests ")])),e[3]||(e[3]=s("h1",{class:"admin-page-title"},[s("i",{class:"fas fa-store admin-page-icon"}),o(" Seller Request Details ")],-1)),e[4]||(e[4]=s("p",{class:"admin-page-subtitle"},"Review and manage seller application",-1))]),s("div",Is,[_(ss,{name:"fade",mode:"out-in"},{default:k(()=>[ls.value?(n(),i("div",Ts,[s("button",{class:"admin-btn admin-btn-success",onClick:ps,disabled:c.value},e[5]||(e[5]=[s("i",{class:"fas fa-check"},null,-1),o(" Approve Request ")]),8,Bs),s("button",{class:"admin-btn admin-btn-danger",onClick:_s,disabled:c.value},e[6]||(e[6]=[s("i",{class:"fas fa-times"},null,-1),o(" Reject Request ")]),8,Ps)])):os.value?(n(),i("div",Fs,e[7]||(e[7]=[s("i",{class:"fas fa-check-circle"},null,-1),o(" Approved ")]))):w.value?(n(),i("div",Ms,e[8]||(e[8]=[s("i",{class:"fas fa-times-circle"},null,-1),o(" Rejected ")]))):u("",!0)]),_:1})])]),D.value?(n(),i("div",Es,e[9]||(e[9]=[s("div",{class:"admin-spinner"},[s("i",{class:"fas fa-spinner fa-pulse"})],-1),s("p",{class:"admin-loading-text"},"Loading seller request details...",-1)]))):m.value?(n(),i("div",Us,[s("button",{class:"admin-alert-close",onClick:e[0]||(e[0]=r=>m.value=null)},e[10]||(e[10]=[s("i",{class:"fas fa-times"},null,-1)])),o(" "+l(m.value),1)])):t.value.id?(n(),i("div",Ls,[s("div",Os,[s("div",Ws,[e[19]||(e[19]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-user admin-card-icon"}),o(" Applicant Information ")])],-1)),s("div",zs,[s("div",Hs,[s("div",Gs,l(bs((S=t.value.user)==null?void 0:S.username)),1),s("div",Js,[s("h4",Ks,l(((x=t.value.user)==null?void 0:x.username)||"Unknown User"),1),s("p",Qs,[e[13]||(e[13]=s("i",{class:"fas fa-envelope"},null,-1)),o(" "+l(((I=t.value.user)==null?void 0:I.email)||"No email provided"),1)]),s("div",Xs,[s("span",Ys,l(((T=t.value.user)==null?void 0:T.role)||"User"),1),_(ts,{status:y.value,type:"seller-request"},null,8,["status"])])])]),s("div",Zs,[s("div",Vs,[e[14]||(e[14]=s("label",{class:"admin-info-label"},"Registered Since",-1)),s("div",se,l(j((B=t.value.user)==null?void 0:B.createdAt)),1)]),s("div",ee,[e[15]||(e[15]=s("label",{class:"admin-info-label"},"Email Status",-1)),s("div",ae,[(P=t.value.user)!=null&&P.emailConfirmed?(n(),i("i",te)):(n(),i("i",ne)),o(" "+l((F=t.value.user)!=null&&F.emailConfirmed?"Confirmed":"Not Confirmed"),1)])]),s("div",ie,[e[16]||(e[16]=s("label",{class:"admin-info-label"},"Request Submitted",-1)),s("div",le,l(j(t.value.createdAt)),1)]),s("div",oe,[e[17]||(e[17]=s("label",{class:"admin-info-label"},"Request Status",-1)),s("div",de,[_(ts,{status:y.value,type:"seller-request"},null,8,["status"])])])]),s("div",re,[t.value.userId?(n(),Cs(d,{key:0,to:`/admin/users/${t.value.userId}`,class:"admin-btn admin-btn-secondary admin-btn-sm"},{default:k(()=>e[18]||(e[18]=[s("i",{class:"fas fa-external-link-alt"},null,-1),o(" View Full Profile ")])),_:1},8,["to"])):u("",!0)])])]),s("div",ce,[e[30]||(e[30]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-building admin-card-icon"}),o(" Company Information ")])],-1)),s("div",ue,[(M=t.value.companyRequestData)!=null&&M.imageUrl?(n(),i("div",me,[s("img",{src:t.value.companyRequestData.imageUrl,alt:(E=t.value.companyRequestData)==null?void 0:E.name,class:"company-image"},null,8,fe)])):u("",!0),s("div",ve,[s("div",pe,[e[20]||(e[20]=s("label",{class:"admin-info-label"},"Company Name",-1)),s("div",_e,l(((U=t.value.companyRequestData)==null?void 0:U.name)||"N/A"),1)]),s("div",ye,[e[22]||(e[22]=s("label",{class:"admin-info-label"},"Contact Email",-1)),s("div",ge,[($=t.value.companyRequestData)!=null&&$.contactEmail?(n(),i("a",{key:0,href:`mailto:${t.value.companyRequestData.contactEmail}`,class:"admin-link"},[e[21]||(e[21]=s("i",{class:"fas fa-envelope"},null,-1)),o(" "+l(t.value.companyRequestData.contactEmail),1)],8,be)):(n(),i("span",he,"N/A"))])]),s("div",ke,[e[24]||(e[24]=s("label",{class:"admin-info-label"},"Contact Phone",-1)),s("div",qe,[(L=t.value.companyRequestData)!=null&&L.contactPhone?(n(),i("a",{key:0,href:`tel:${t.value.companyRequestData.contactPhone}`,class:"admin-link"},[e[23]||(e[23]=s("i",{class:"fas fa-phone"},null,-1)),o(" "+l(t.value.companyRequestData.contactPhone),1)],8,Re)):(n(),i("span",De,"N/A"))])]),s("div",Ce,[e[25]||(e[25]=s("label",{class:"admin-info-label"},"Meta Title",-1)),s("div",Se,l(((O=t.value.companyRequestData)==null?void 0:O.metaTitle)||"N/A"),1)])]),s("div",Ne,[e[26]||(e[26]=s("label",{class:"admin-info-label"},"Description",-1)),s("div",we,l(((W=t.value.companyRequestData)==null?void 0:W.description)||"No description provided"),1)]),A()?(n(),i("div",Ae,[e[28]||(e[28]=s("label",{class:"admin-info-label"},"Address",-1)),s("div",je,[e[27]||(e[27]=s("i",{class:"fas fa-map-marker-alt"},null,-1)),o(" "+l(A()),1)])])):u("",!0),(z=t.value.companyRequestData)!=null&&z.metaDescription?(n(),i("div",xe,[e[29]||(e[29]=s("label",{class:"admin-info-label"},"Meta Description",-1)),s("div",Ie,l(t.value.companyRequestData.metaDescription),1)])):u("",!0)])]),s("div",Te,[e[39]||(e[39]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-credit-card admin-card-icon"}),o(" Financial Information ")])],-1)),s("div",Be,[s("div",Pe,[e[35]||(e[35]=s("h4",{class:"finance-section-title"},[s("i",{class:"fas fa-university"}),o(" Banking Details ")],-1)),s("div",Fe,[s("div",Me,[e[31]||(e[31]=s("label",{class:"admin-info-label"},"Bank Name",-1)),s("div",Ee,l(((H=t.value.financeRequestData)==null?void 0:H.bankName)||"N/A"),1)]),s("div",Ue,[e[32]||(e[32]=s("label",{class:"admin-info-label"},"Bank Code",-1)),s("div",$e,l(((G=t.value.financeRequestData)==null?void 0:G.bankCode)||"N/A"),1)]),s("div",Le,[e[33]||(e[33]=s("label",{class:"admin-info-label"},"Bank Account",-1)),s("div",Oe,l(vs((J=t.value.financeRequestData)==null?void 0:J.bankAccount)),1)]),s("div",We,[e[34]||(e[34]=s("label",{class:"admin-info-label"},"Tax ID",-1)),s("div",ze,l(((K=t.value.financeRequestData)==null?void 0:K.taxId)||"N/A"),1)])])]),(Q=t.value.financeRequestData)!=null&&Q.paymentDetails?(n(),i("div",He,[e[37]||(e[37]=s("h4",{class:"finance-section-title"},[s("i",{class:"fas fa-file-invoice-dollar"}),o(" Payment Information ")],-1)),s("div",Ge,[e[36]||(e[36]=s("label",{class:"admin-info-label"},"Payment Details",-1)),s("div",Je,l(t.value.financeRequestData.paymentDetails),1)])])):u("",!0),e[38]||(e[38]=s("div",{class:"security-notice"},[s("i",{class:"fas fa-shield-alt"}),s("span",null,"Financial information is encrypted and securely stored")],-1))])]),s("div",Ke,[e[43]||(e[43]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-clock admin-card-icon"}),o(" Schedule Information ")])],-1)),s("div",Qe,[(X=t.value.scheduleRequestData)!=null&&X.daySchedules&&t.value.scheduleRequestData.daySchedules.length>0?(n(),i("div",Xe,[s("table",Ye,[e[41]||(e[41]=s("thead",null,[s("tr",null,[s("th",{class:"day-column"},"Day"),s("th",{class:"status-column"},"Status"),s("th",{class:"time-column"},"Working Hours")])],-1)),s("tbody",null,[(n(!0),i(es,null,as(t.value.scheduleRequestData.daySchedules,r=>(n(),i("tr",{key:r.day,class:"schedule-row"},[s("td",Ze,[s("div",Ve,[s("span",sa,l(us(r.day)),1)])]),s("td",ea,[s("span",{class:q(["status-badge",r.isClosed?"status-closed":"status-open"])},[s("i",{class:q(["fas",r.isClosed?"fa-times":"fa-check"])},null,2),o(" "+l(r.isClosed?"Closed":"Open"),1)],2)]),s("td",aa,[r.isClosed?(n(),i("span",la,"-")):(n(),i("div",ta,[s("span",na,l(r.openTime||"Not set"),1),e[40]||(e[40]=s("span",{class:"time-separator"},"-",-1)),s("span",ia,l(r.closeTime||"Not set"),1)]))])]))),128))])])])):(n(),i("div",oa,e[42]||(e[42]=[s("i",{class:"fas fa-clock admin-empty-icon"},null,-1),s("p",{class:"admin-empty-text"},"No schedule information provided",-1)])))])]),s("div",da,[e[46]||(e[46]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-info-circle admin-card-icon"}),o(" Additional Information ")])],-1)),s("div",ra,[t.value.additionalInformation?(n(),i("div",ca,[e[44]||(e[44]=s("label",{class:"admin-info-label"},"Additional Notes",-1)),s("div",ua,l(t.value.additionalInformation),1)])):(n(),i("div",ma,e[45]||(e[45]=[s("i",{class:"fas fa-info-circle admin-empty-icon"},null,-1),s("p",{class:"admin-empty-text"},"No additional information provided",-1)])))])]),t.value.documents&&t.value.documents.length>0?(n(),i("div",fa,[e[48]||(e[48]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-file-alt admin-card-icon"}),o(" Documents ")])],-1)),s("div",va,[s("div",pa,[(n(!0),i(es,null,as(t.value.documents,(r,hs)=>(n(),i("div",{key:hs,class:"admin-document-item"},[s("div",_a,[s("i",{class:q(fs(r.type))},null,2)]),s("div",ya,[s("h4",ga,l(r.name),1),s("p",ba,l(r.type),1)]),s("div",ha,[s("a",{href:r.url,target:"_blank",rel:"noopener noreferrer",class:"admin-btn admin-btn-sm admin-btn-primary"},e[47]||(e[47]=[s("i",{class:"fas fa-download"},null,-1),o(" Download ")]),8,ka)])]))),128))])])])):u("",!0),_(ss,{name:"fade",mode:"out-in"},{default:k(()=>[ds.value?(n(),i("div",qa,[e[50]||(e[50]=s("div",{class:"admin-card-header"},[s("h3",{class:"admin-card-title"},[s("i",{class:"fas fa-times-circle admin-card-icon"}),o(" Rejection Reason ")])],-1)),s("div",Ra,[s("div",Da,[e[49]||(e[49]=s("label",{class:"admin-info-label"},"Reason",-1)),s("div",Ca,l(t.value.rejectionReason||"No reason provided"),1)])])])):u("",!0)]),_:1})])])):(n(),i("div",$s,[e[12]||(e[12]=s("p",null,"Seller request not found.",-1)),_(d,{to:"/admin/seller-requests",class:"admin-btn admin-btn-primary"},{default:k(()=>e[11]||(e[11]=[o(" Back to Seller Requests ")])),_:1})])),_(As,{"is-open":g.value,title:"Approve Seller Request",message:`Are you sure you want to approve ${((Y=t.value.user)==null?void 0:Y.firstName)||"this user"} ${((Z=t.value.user)==null?void 0:Z.lastName)||""}'s seller request for '${t.value.storeName||"this store"}'?`,"confirm-text":"Approve","cancel-text":"Cancel","confirm-button-class":"is-success",onConfirm:ys,onCancel:h},null,8,["is-open","message"]),s("div",{class:q(["modal",{"is-active":b.value}])},[s("div",{class:"modal-background",onClick:h}),s("div",Sa,[s("header",{class:"modal-card-head"},[e[51]||(e[51]=s("p",{class:"modal-card-title"},"Reject Seller Request",-1)),s("button",{class:"delete","aria-label":"close",onClick:h})]),s("section",Na,[s("p",null,"Are you sure you want to reject "+l(((V=t.value.user)==null?void 0:V.firstName)||"this user")+"'s seller request for '"+l(t.value.storeName||"this store")+"'?",1),s("div",wa,[e[52]||(e[52]=s("label",{class:"admin-form-label"},"Reason for Rejection (Optional)",-1)),Ss(s("textarea",{class:"admin-form-textarea","onUpdate:modelValue":e[1]||(e[1]=r=>f.value=r),placeholder:"Provide a reason for rejection",rows:"3"},"            ",512),[[Ns,f.value]])])]),s("footer",Aa,[s("button",{class:"admin-btn admin-btn-danger",onClick:gs,disabled:c.value},[c.value?(n(),i("i",xa)):(n(),i("i",Ia)),o(" "+l(c.value?"Rejecting...":"Reject"),1)],8,ja),s("button",{class:"admin-btn admin-btn-secondary",onClick:h},e[53]||(e[53]=[s("i",{class:"fas fa-times"},null,-1),o(" Cancel ")]))])])],2)])}}},$a=ks(Ta,[["__scopeId","data-v-011ca09f"]]);export{$a as default};
