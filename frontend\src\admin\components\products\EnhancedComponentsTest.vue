<template>
  <div class="enhanced-components-test">
    <div class="admin-page-header">
      <h1 class="admin-page-title">Enhanced Components Test</h1>
      <p class="admin-page-description">Test the new enhanced product components</p>
    </div>

    <div class="test-sections">
      <!-- Company Selector Test -->
      <div class="test-section">
        <h2>Enhanced Company Selector</h2>
        <div class="test-form">
          <EnhancedCompanySelector
            v-model="selectedCompany"
            label="Test Company Selector"
            placeholder="Search and select a company..."
            help-text="This selector shows all companies with search functionality"
            @change="handleCompanyChange"
            @select="handleCompanySelect"
          />
          <div v-if="selectedCompany" class="test-result">
            <strong>Selected Company ID:</strong> {{ selectedCompany }}
          </div>
        </div>
      </div>

      <!-- Category Selector Test -->
      <div class="test-section">
        <h2>Enhanced Category Selector</h2>
        <div class="test-form">
          <EnhancedCategorySelector
            v-model="selectedCategory"
            label="Test Category Selector"
            placeholder="Search and select a category..."
            help-text="This selector shows all categories with hierarchy"
            @change="handleCategoryChange"
            @select="handleCategorySelect"
          />
          <div v-if="selectedCategory" class="test-result">
            <strong>Selected Category ID:</strong> {{ selectedCategory }}
          </div>
        </div>
      </div>

      <!-- Attributes Editor Test -->
      <div class="test-section">
        <h2>Enhanced Product Attributes Editor</h2>
        <div class="test-form">
          <EnhancedProductAttributesEditor
            v-model="productAttributes"
            @update:model-value="handleAttributesChange"
          />
          <div v-if="Object.keys(productAttributes).length > 0" class="test-result">
            <strong>Current Attributes:</strong>
            <pre>{{ JSON.stringify(productAttributes, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- Combined Test -->
      <div class="test-section">
        <h2>Combined Test</h2>
        <div class="test-form">
          <div class="form-row">
            <div class="form-col">
              <EnhancedCompanySelector
                v-model="testProduct.companyId"
                label="Company"
                required
              />
            </div>
            <div class="form-col">
              <EnhancedCategorySelector
                v-model="testProduct.categoryId"
                label="Category"
                required
              />
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <label class="admin-form-label">Product Name</label>
              <input
                v-model="testProduct.name"
                type="text"
                class="admin-form-input"
                placeholder="Enter product name"
              />
            </div>
          </div>

          <EnhancedProductAttributesEditor
            v-model="testProduct.attributes"
          />

          <div class="test-result">
            <strong>Test Product Data:</strong>
            <pre>{{ JSON.stringify(testProduct, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import EnhancedCompanySelector from './EnhancedCompanySelector.vue';
import EnhancedCategorySelector from './EnhancedCategorySelector.vue';
import EnhancedProductAttributesEditor from './EnhancedProductAttributesEditor.vue';

// Test data
const selectedCompany = ref(null);
const selectedCategory = ref(null);
const productAttributes = ref({
  Color: ['Red', 'Blue', 'Green'],
  Size: ['S', 'M', 'L', 'XL'],
  Material: ['Cotton']
});

const testProduct = ref({
  name: '',
  companyId: null,
  categoryId: null,
  attributes: {}
});

// Event handlers
const handleCompanyChange = (companyId) => {
  console.log('Company changed:', companyId);
};

const handleCompanySelect = (company) => {
  console.log('Company selected:', company);
};

const handleCategoryChange = (categoryId) => {
  console.log('Category changed:', categoryId);
};

const handleCategorySelect = (category) => {
  console.log('Category selected:', category);
};

const handleAttributesChange = (attributes) => {
  console.log('Attributes changed:', attributes);
};
</script>

<style scoped>
.enhanced-components-test {
  padding: var(--admin-space-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.admin-page-header {
  margin-bottom: var(--admin-space-xl);
}

.admin-page-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-page-description {
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-base);
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
}

.test-section {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  padding: var(--admin-space-lg);
}

.test-section h2 {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
  padding-bottom: var(--admin-space-sm);
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--admin-space-md);
}

.form-col {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-form-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-form-input {
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
}

.admin-form-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.test-result {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  margin-top: var(--admin-space-md);
}

.test-result pre {
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-sm);
  padding: var(--admin-space-sm);
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  overflow-x: auto;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .enhanced-components-test {
    padding: var(--admin-space-md);
  }
}
</style>
