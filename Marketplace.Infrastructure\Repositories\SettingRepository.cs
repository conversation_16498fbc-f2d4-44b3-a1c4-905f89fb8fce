using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Infrastructure.Repositories;

public class SettingRepository : Repository<Setting>, ISettingRepository
{
    private readonly MarketplaceDbContext _marketplaceContext;

    public SettingRepository(MarketplaceDbContext context) : base(context)
    {
        _marketplaceContext = context;
    }

    public async Task<Setting?> GetByKeyAsync(string key, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Settings
            .FirstOrDefaultAsync(s => s.Key == key, cancellationToken);
    }

    public async Task<List<Setting>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Settings
            .Where(s => s.Category == category)
            .OrderBy(s => s.Key)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Setting>> GetPublicSettingsAsync(CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Settings
            .Where(s => s.IsPublic)
            .OrderBy(s => s.Category)
            .ThenBy(s => s.Key)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, string>> GetSettingsDictionaryAsync(string? category = null, CancellationToken cancellationToken = default)
    {
        var query = _marketplaceContext.Settings.AsQueryable();

        if (!string.IsNullOrEmpty(category))
        {
            query = query.Where(s => s.Category == category);
        }

        return await query
            .ToDictionaryAsync(s => s.Key, s => s.Value, cancellationToken);
    }

    public async Task<bool> ExistsByKeyAsync(string key, CancellationToken cancellationToken = default)
    {
        return await _marketplaceContext.Settings
            .AnyAsync(s => s.Key == key, cancellationToken);
    }

    public async Task UpsertAsync(string key, string value, string category, SettingType type = SettingType.String, string? description = null, bool isPublic = false, CancellationToken cancellationToken = default)
    {
        var existingSetting = await GetByKeyAsync(key, cancellationToken);
        
        if (existingSetting != null)
        {
            existingSetting.Update(value, category, type, description, isPublic);
            await UpdateAsync(existingSetting, cancellationToken);
        }
        else
        {
            var newSetting = new Setting(key, value, category, type, description, isPublic);
            await AddAsync(newSetting, cancellationToken);
        }
    }

    public async Task DeleteByKeyAsync(string key, CancellationToken cancellationToken = default)
    {
        var setting = await GetByKeyAsync(key, cancellationToken);
        if (setting != null)
        {
            await DeleteAsync(setting.Id, cancellationToken);
        }
    }
}
