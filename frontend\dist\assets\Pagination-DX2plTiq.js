import{_ as C,h as g,c as i,o,a as l,k as d,F as N,p as $,n as w,t as k}from"./index-L-hJxM_5.js";const F={class:"modern-pagination",role:"navigation","aria-label":"pagination"},M=["disabled"],V={class:"pagination-numbers"},B={key:1,class:"pagination-ellipsis"},E=["onClick"],L={key:2,class:"pagination-ellipsis"},q=["disabled"],z={__name:"Pagination",props:{currentPage:{type:Number,required:!0},totalPages:{type:Number,required:!0},maxVisiblePages:{type:Number,default:5}},emits:["page-changed"],setup(a,{emit:f}){const n=a,h=f,r=g(()=>{const{currentPage:s,totalPages:t,maxVisiblePages:e}=n,P=[];let c=Math.max(1,s-Math.floor(e/2)),v=Math.min(t,c+e-1);v-c+1<e&&(c=Math.max(1,v-e+1));for(let m=c;m<=v;m++)P.push(m);return P}),b=g(()=>n.totalPages>1&&!r.value.includes(1)),p=g(()=>n.totalPages>1&&!r.value.includes(n.totalPages)),x=g(()=>b.value&&r.value[0]>2),y=g(()=>p.value&&r.value[r.value.length-1]<n.totalPages-1),u=s=>{s>=1&&s<=n.totalPages&&s!==n.currentPage&&h("page-changed",s)};return(s,t)=>(o(),i("nav",F,[l("button",{class:"pagination-btn pagination-prev",disabled:a.currentPage<=1,onClick:t[0]||(t[0]=e=>u(a.currentPage-1))},t[4]||(t[4]=[l("i",{class:"fas fa-chevron-left"},null,-1),l("span",null,"Previous",-1)]),8,M),l("div",V,[b.value?(o(),i("button",{key:0,class:"pagination-number",onClick:t[1]||(t[1]=e=>u(1))}," 1 ")):d("",!0),x.value?(o(),i("span",B,"...")):d("",!0),(o(!0),i(N,null,$(r.value,e=>(o(),i("button",{key:e,class:w(["pagination-number",{active:e===a.currentPage}]),onClick:P=>u(e)},k(e),11,E))),128)),y.value?(o(),i("span",L,"...")):d("",!0),p.value?(o(),i("button",{key:3,class:"pagination-number",onClick:t[2]||(t[2]=e=>u(a.totalPages))},k(a.totalPages),1)):d("",!0)]),l("button",{class:"pagination-btn pagination-next",disabled:a.currentPage>=a.totalPages,onClick:t[3]||(t[3]=e=>u(a.currentPage+1))},t[5]||(t[5]=[l("span",null,"Next",-1),l("i",{class:"fas fa-chevron-right"},null,-1)]),8,q)]))}},I=C(z,[["__scopeId","data-v-bc352ad8"]]);export{I as P};
