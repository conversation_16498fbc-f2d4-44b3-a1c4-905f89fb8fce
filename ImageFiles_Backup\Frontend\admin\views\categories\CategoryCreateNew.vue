<template>
  <div class="category-create-new">
    <!-- Header -->
    <div class="create-header">
      <div class="header-content">
        <nav class="breadcrumb">
          <router-link to="/admin" class="breadcrumb-item">Dashboard</router-link>
          <span class="breadcrumb-separator">/</span>
          <router-link to="/admin/categories" class="breadcrumb-item">Categories</router-link>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-item breadcrumb-current">Create New</span>
        </nav>
        <h1 class="create-title">Create New Category</h1>
        <p class="create-subtitle">Add a new category to organize your products</p>
      </div>
      <div class="header-actions">
        <router-link to="/admin/categories" class="action-btn action-btn-light">
          <i class="fas fa-arrow-left"></i>
          Back to Categories
        </router-link>
      </div>
    </div>

    <!-- Messages -->
    <div v-if="error" class="message message-error">
      <div class="message-content">
        <i class="fas fa-exclamation-circle"></i>
        <span>{{ error }}</span>
      </div>
      <button class="message-close" @click="error = ''">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div v-if="success" class="message message-success">
      <div class="message-content">
        <i class="fas fa-check-circle"></i>
        <span>{{ success }}</span>
      </div>
      <button class="message-close" @click="success = ''">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Form -->
    <form @submit.prevent="submitForm" class="create-form">
      <!-- Basic Information Card -->
      <div class="form-card">
        <div class="form-card-header">
          <h3 class="form-card-title">
            <i class="fas fa-info-circle"></i>
            Basic Information
          </h3>
        </div>
        <div class="form-card-content">
          <div class="form-grid">
            <div class="form-field">
              <label class="form-label">
                Name <span class="required">*</span>
              </label>
              <input 
                class="form-input" 
                type="text" 
                placeholder="Category name" 
                v-model="form.name"
                @input="generateSlug"
                required>
            </div>

            <div class="form-field">
              <label class="form-label">
                Slug <span class="required">*</span>
              </label>
              <input 
                class="form-input form-input-code" 
                type="text" 
                placeholder="category-slug" 
                v-model="form.slug"
                required>
              <p class="form-help">URL-friendly version of the name. Auto-generated but can be edited.</p>
            </div>
          </div>

          <div class="form-field">
            <label class="form-label">Description</label>
            <textarea 
              class="form-textarea" 
              placeholder="Category description" 
              v-model="form.description"
              rows="3"></textarea>
          </div>
        </div>
      </div>

      <!-- Parent Category Card -->
      <div class="form-card">
        <div class="form-card-header">
          <h3 class="form-card-title">
            <i class="fas fa-sitemap"></i>
            Category Hierarchy
          </h3>
        </div>
        <div class="form-card-content">
          <div class="form-field">
            <label class="form-label">Parent Category</label>
            <EnhancedCategorySelector
              v-model="form.parentId"
              label=""
              placeholder="Search for parent category (leave empty for top level)..."
              help-text="Select a parent category or leave empty to make this a root category"
              @change="handleParentCategoryChange"
            />
          </div>
        </div>
      </div>

      <!-- Images Card -->
      <div class="form-card">
        <div class="form-card-header">
          <h3 class="form-card-title">
            <i class="fas fa-images"></i>
            Images
          </h3>
        </div>
        <div class="form-card-content">
          <!-- Category Image -->
          <div class="form-field">
            <CategoryImageUploader
              ref="imageUploader"
              label="Category Image"
              upload-text="Upload Category Image"
              :current-image-url="null"
              entity-id="temp-id"
              image-type="logo"
              @image-changed="handleImageChanged"
            />
            <p class="form-help">Upload an image to represent this category. Recommended size: 300x300 pixels.</p>
          </div>

          <!-- Meta Image -->
          <div class="form-field">
            <CategoryImageUploader
              ref="metaUploader"
              label="Meta Image (SEO)"
              upload-text="Upload Meta Image"
              :current-image-url="null"
              entity-id="temp-id"
              image-type="meta"
              :social-title="form.name || 'Category Name'"
              :social-description="form.description || 'Category Description'"
              @image-changed="handleMetaChanged"
            />
            <p class="form-help">Upload a meta image for SEO and social media sharing.</p>
          </div>
        </div>
      </div>

      <!-- SEO Card -->
      <div class="form-card">
        <div class="form-card-header">
          <h3 class="form-card-title">
            <i class="fas fa-search"></i>
            SEO Settings
          </h3>
        </div>
        <div class="form-card-content">
          <div class="form-grid">
            <div class="form-field">
              <label class="form-label">Meta Title</label>
              <input 
                class="form-input" 
                type="text" 
                placeholder="SEO title for search engines" 
                v-model="form.metaTitle"
                maxlength="60">
              <p class="form-help">
                <span :class="{ 'text-danger': form.metaTitle && form.metaTitle.length > 60 }">
                  {{ form.metaTitle ? form.metaTitle.length : 0 }}/60 characters
                </span>
              </p>
            </div>

            <div class="form-field">
              <label class="form-label">Display Order</label>
              <input 
                class="form-input" 
                type="number" 
                min="0" 
                placeholder="0" 
                v-model.number="form.displayOrder">
              <p class="form-help">Categories with lower numbers will be displayed first.</p>
            </div>
          </div>

          <div class="form-field">
            <label class="form-label">Meta Description</label>
            <textarea 
              class="form-textarea" 
              placeholder="SEO description for search engines" 
              v-model="form.metaDescription"
              maxlength="160"
              rows="3"></textarea>
            <p class="form-help">
              <span :class="{ 'text-danger': form.metaDescription && form.metaDescription.length > 160 }">
                {{ form.metaDescription ? form.metaDescription.length : 0 }}/160 characters
              </span>
            </p>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button 
          type="submit" 
          class="action-btn action-btn-primary action-btn-large"
          :disabled="saving"
          :class="{ 'action-btn-loading': saving }">
          <i class="fas fa-spinner fa-spin" v-if="saving"></i>
          <i class="fas fa-plus" v-else></i>
          {{ saving ? 'Creating...' : 'Create Category' }}
        </button>
        <router-link 
          to="/admin/categories" 
          class="action-btn action-btn-secondary action-btn-large">
          <i class="fas fa-times"></i>
          Cancel
        </router-link>
      </div>

      <!-- Upload Progress -->
      <div v-if="pendingUploads.length > 0" class="mt-3">
        <UploadProgress
          :uploads="pendingUploads"
          @retry-upload="retryUpload"
          @cancel-upload="cancelUpload"
        />
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoriesService } from '@/admin/services/categories';
import EnhancedCategorySelector from '@/admin/components/products/EnhancedCategorySelector.vue';
import CategoryImageUploader from '@/admin/components/category/CategoryImageUploader.vue';
import UploadProgress from '@/admin/components/common/UploadProgress.vue';
import imageService from '@/services/image.service';

// Router
const route = useRoute();
const router = useRouter();

// Reactive data
const saving = ref(false);
const error = ref('');
const success = ref('');

// Component refs
const imageUploader = ref(null);
const metaUploader = ref(null);

// Upload management
const pendingUploads = ref([]);
const pendingImageChange = ref(null);
const pendingMetaImageChange = ref(null);

// Form data
const form = reactive({
  name: '',
  slug: '',
  description: '',
  parentId: null,
  image: '',
  metaImage: '',
  metaTitle: '',
  metaDescription: '',
  displayOrder: 0
});

// Methods
const generateSlug = () => {
  if (form.name) {
    form.slug = form.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
};

const handleParentCategoryChange = (parentId) => {
  form.parentId = parentId;
};

// Image handlers
const handleImageChanged = (change) => {
  pendingImageChange.value = change;
  console.log('Category image changed:', change);
};

const handleMetaChanged = (change) => {
  pendingMetaImageChange.value = change;
  console.log('Category meta image changed:', change);
};

// Upload progress methods
const retryUpload = async (uploadId) => {
  const upload = pendingUploads.value.find(u => u.id === uploadId);
  if (upload) {
    upload.status = 'uploading';
    upload.progress = 0;
    upload.error = null;

    try {
      console.log('Retrying upload:', uploadId);
    } catch (error) {
      upload.status = 'error';
      upload.error = error.message;
    }
  }
};

const cancelUpload = (uploadId) => {
  const index = pendingUploads.value.findIndex(u => u.id === uploadId);
  if (index !== -1) {
    pendingUploads.value.splice(index, 1);
  }
};

const submitForm = async () => {
  saving.value = true;
  error.value = '';
  success.value = '';

  try {
    // Prepare form data (exclude images as they will be uploaded separately)
    const formData = {
      name: form.name,
      slug: form.slug,
      description: form.description,
      parentId: form.parentId || null,
      metaTitle: form.metaTitle,
      metaDescription: form.metaDescription,
      displayOrder: form.displayOrder
    };

    console.log('Creating category with data:', formData);

    // Create category
    const response = await categoriesService.createCategory(formData);
    const newCategoryId = response.id || response.data?.id;

    console.log('Category created with ID:', newCategoryId);

    if (newCategoryId) {
      // Process pending image operations if any
      const imageResults = {};

      if (imageUploader.value && imageUploader.value.hasChanges()) {
        console.log('Processing main image changes...');
        // Update entity ID for the uploader
        imageUploader.value.entityId = newCategoryId;
        const result = await imageUploader.value.processChanges();
        if (result.success) {
          imageResults.image = result.url;
          console.log('Main image processed successfully:', result.url);
        } else {
          console.warn('Image upload failed:', result.error);
        }
      }

      if (metaUploader.value && metaUploader.value.hasChanges()) {
        console.log('Processing meta image changes...');
        // Update entity ID for the uploader
        metaUploader.value.entityId = newCategoryId;
        const result = await metaUploader.value.processChanges();
        if (result.success) {
          imageResults.metaImage = result.url;
          console.log('Meta image processed successfully:', result.url);
        } else {
          console.warn('Meta image upload failed:', result.error);
        }
      }

      // If we have image results, update the category
      if (imageResults.image || imageResults.metaImage) {
        console.log('Updating category with image URLs:', imageResults);
        const updateData = {};
        if (imageResults.image) updateData.image = imageResults.image;
        if (imageResults.metaImage) updateData.metaImage = imageResults.metaImage;

        await categoriesService.updateCategory(newCategoryId, updateData);
      }

      success.value = 'Category created successfully!';

      // Redirect to the new category's detail page
      setTimeout(() => {
        router.push(`/admin/categories/${newCategoryId}`);
      }, 1500);
    } else {
      throw new Error('Category created but ID not returned');
    }

  } catch (err) {
    console.error('Error creating category:', err);
    if (err.response && err.response.data && err.response.data.message) {
      error.value = err.response.data.message;
    } else {
      error.value = 'Failed to create category. Please try again.';
    }
  } finally {
    saving.value = false;
  }
};

// Initialize form with query parameters if provided
onMounted(() => {
  if (route.query.parentId) {
    form.parentId = route.query.parentId;
  }
});
</script>

<style scoped>
/* Base Styles */
.category-create-new {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header */
.create-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #3b82f6;
}

.breadcrumb-current {
  color: #1f2937;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d1d5db;
}

.create-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.create-subtitle {
  color: #6b7280;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.action-btn-primary {
  background: #3b82f6;
  color: white;
}

.action-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-secondary {
  background: #6b7280;
  color: white;
}

.action-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
  color: white;
  text-decoration: none;
}

.action-btn-light {
  background: #f3f4f6;
  color: #374151;
}

.action-btn-light:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
  color: #374151;
  text-decoration: none;
}

.action-btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
}

.action-btn-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Messages */
.message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.message-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.message-success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.message-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Form */
.create-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-card-header {
  background: #f8fafc;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.form-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-card-title i {
  color: #6b7280;
}

.form-card-content {
  padding: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.form-field:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.form-input,
.form-textarea {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-code {
  font-family: 'Courier New', monospace;
  background: #f8fafc;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
}

.text-danger {
  color: #ef4444;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .category-create-new {
    padding: 1.5rem;
  }

  .create-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-btn-large {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .category-create-new {
    padding: 1rem;
  }

  .create-header {
    padding: 1.5rem;
  }

  .create-title {
    font-size: 1.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-btn {
    justify-content: center;
  }

  .form-card-content {
    padding: 1rem;
  }

  .form-actions {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb {
    flex-wrap: wrap;
  }

  .create-title {
    font-size: 1.25rem;
  }

  .form-card-title {
    font-size: 1rem;
  }

  .form-card-header {
    padding: 1rem;
  }

  .form-card-content {
    padding: 1rem;
  }

  .form-actions {
    padding: 1rem;
  }
}
</style>
