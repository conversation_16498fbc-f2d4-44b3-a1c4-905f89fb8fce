using Marketplace.Domain.Entities;

namespace Marketplace.Domain.Repositories;

public interface ILogRepository : IRepository<Log>
{
    Task<List<Log>> GetByLevelAsync(LogLevel level, CancellationToken cancellationToken = default);
    Task<List<Log>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<List<Log>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<Log>> GetByDateRangeAsync(DateTime from, DateTime to, CancellationToken cancellationToken = default);
    Task<List<Log>> GetFilteredLogsAsync(
        LogLevel? level = null,
        string? category = null,
        Guid? userId = null,
        DateTime? from = null,
        DateTime? to = null,
        int? page = null,
        int? pageSize = null,
        CancellationToken cancellationToken = default);
    Task<int> GetLogCountAsync(
        LogLevel? level = null,
        string? category = null,
        Guid? userId = null,
        DateTime? from = null,
        DateTime? to = null,
        CancellationToken cancellationToken = default);
    Task CleanupOldLogsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}
