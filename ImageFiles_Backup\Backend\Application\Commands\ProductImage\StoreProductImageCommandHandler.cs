﻿using AutoMapper;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class StoreProductImageCommandHandler : IRequestHandler<StoreProductImageCommand, Guid>
{
    private readonly IProductImageRepository _repository;
    private readonly IMapper _mapper;

    public StoreProductImageCommandHandler(IProductImageRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<Guid> Handle(StoreProductImageCommand request, CancellationToken cancellationToken)
    {
        var item = _mapper.Map<Domain.Entities.ProductImage>(request);
        await _repository.AddAsync(item, cancellationToken);
        return item.Id;
    }
}

