<template>
  <div class="api-test">
    <h1 class="title">API Test Page</h1>
    
    <div class="buttons">
      <button class="button is-primary" @click="testProductsAPI">Test Products API</button>
      <button class="button is-info" @click="testCategoriesAPI">Test Categories API</button>
    </div>

    <div v-if="loading" class="notification is-info">
      Loading...
    </div>

    <div v-if="result" class="content">
      <h3>API Response:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>

    <div v-if="error" class="notification is-danger">
      <strong>Error:</strong> {{ error }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';

const loading = ref(false);
const result = ref(null);
const error = ref(null);

const testProductsAPI = async () => {
  loading.value = true;
  error.value = null;
  result.value = null;

  try {
    const response = await productsService.getProducts({
      page: 1,
      pageSize: 5
    });
    result.value = response;
    console.log('Products API test result:', response);
  } catch (err) {
    error.value = err.message;
    console.error('Products API test error:', err);
  } finally {
    loading.value = false;
  }
};

const testCategoriesAPI = async () => {
  loading.value = true;
  error.value = null;
  result.value = null;

  try {
    const response = await categoriesService.getCategories();
    result.value = response;
    console.log('Categories API test result:', response);
  } catch (err) {
    error.value = err.message;
    console.error('Categories API test error:', err);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.api-test {
  padding: 2rem;
}

pre {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
}
</style>
