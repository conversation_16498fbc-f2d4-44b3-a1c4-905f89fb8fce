import{_ as E,g as u,i as S,H as $,c as o,a as s,k as b,b as R,w as C,d as h,r as F,n,t as i,F as w,p as g,x as I,D as M,f as B,e as L,o as d}from"./index-L-hJxM_5.js";import{r as D}from"./reviews-C_F7wLvS.js";const U={class:"review-detail"},H={class:"level"},P={class:"level-left"},z={class:"level-item"},j={class:"breadcrumb"},q={class:"level-right"},G={class:"level-item"},J={key:0,class:"has-text-centered"},K={key:1,class:"notification is-danger"},O={key:2},Q={class:"columns"},W={class:"column is-8"},X={class:"card"},Y={class:"card-content"},Z={class:"columns"},ss={class:"column is-6"},es={class:"field"},ls={class:"info-value"},ts={class:"field"},as={class:"info-value"},is={class:"field"},os={class:"info-value"},ds={class:"stars"},ns={class:"ml-2"},cs={class:"column is-6"},rs={class:"field"},vs={class:"info-value"},us={key:0,class:"field"},ms={class:"info-value"},_s={key:0,class:"field"},fs={class:"content"},ps={key:0,class:"card mt-4"},bs={class:"card-content"},hs={class:"columns"},ws={class:"column is-4"},gs={class:"field"},ys={class:"stars"},ks={class:"ml-2"},Rs={class:"column is-4"},Cs={class:"field"},Ds={class:"stars"},As={class:"ml-2"},xs={class:"column is-4"},Ns={class:"field"},Vs={class:"stars"},Ts={class:"ml-2"},Es={class:"column is-4"},Ss={class:"card"},$s={class:"card-content"},Fs={class:"buttons is-fullwidth"},Is=["disabled"],Ms=["disabled"],Bs={class:"card mt-4"},Ls={class:"card-content"},Us={class:"field"},Hs={class:"info-value"},Ps={class:"field"},zs={class:"info-value"},js={class:"modal-card"},qs={class:"modal-card-body"},Gs={class:"field"},Js={class:"control"},Ks={class:"help"},Os={class:"modal-card-foot"},Qs=["disabled"],Ws={class:"modal-card"},Xs={class:"modal-card-head"},Ys={class:"modal-card-foot"},Zs={__name:"ReviewDetail",setup(se){const x=B(),N=L(),l=u({}),_=u(!1),v=u(null),c=u(!1),m=u(!1),f=u(!1),r=u(""),y=async()=>{_.value=!0,v.value=null;try{const a=await D.getReviewById(x.params.id);l.value=a}catch(a){v.value=a.message||"Failed to load review details"}finally{_.value=!1}},V=async()=>{c.value=!0;try{await D.updateReview(l.value.id,{comment:r.value}),await y(),p()}catch(a){v.value=a.message||"Failed to update review"}finally{c.value=!1}},T=async()=>{c.value=!0;try{await D.deleteReview(l.value.id),N.push({name:"AdminReviews"})}catch(a){v.value=a.message||"Failed to delete review"}finally{c.value=!1,m.value=!1}},p=()=>{f.value=!1,r.value=l.value.comment||""},A=a=>new Date(a).toLocaleString();return S(()=>{y()}),$(()=>l.value.comment,a=>{a&&!f.value&&(r.value=a)},{immediate:!0}),(a,e)=>{const k=F("router-link");return d(),o("div",U,[s("div",H,[s("div",P,[s("div",z,[s("nav",j,[s("ul",null,[s("li",null,[R(k,{to:"/admin/reviews"},{default:C(()=>e[7]||(e[7]=[h("Reviews")])),_:1})]),e[8]||(e[8]=s("li",{class:"is-active"},[s("a",null,"Review Details")],-1))])])])]),s("div",q,[s("div",G,[s("button",{class:n(["button is-primary",{"is-loading":_.value}]),onClick:y},e[9]||(e[9]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Refresh",-1)]),2)])])]),_.value&&!l.value.id?(d(),o("div",J,e[10]||(e[10]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading review details...",-1)]))):v.value?(d(),o("div",K,[s("button",{class:"delete",onClick:e[0]||(e[0]=t=>v.value=null)}),h(" "+i(v.value),1)])):l.value.id?(d(),o("div",O,[s("div",Q,[s("div",W,[s("div",X,[e[17]||(e[17]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Review Information")],-1)),s("div",Y,[s("div",Z,[s("div",ss,[s("div",es,[e[11]||(e[11]=s("label",{class:"label"},"Product",-1)),s("p",ls,[R(k,{to:{name:"AdminProductDetail",params:{id:l.value.productId}}},{default:C(()=>[h(i(l.value.productName),1)]),_:1},8,["to"])])]),s("div",ts,[e[12]||(e[12]=s("label",{class:"label"},"User",-1)),s("p",as,[R(k,{to:{name:"AdminUserDetail",params:{id:l.value.userId}}},{default:C(()=>[h(i(l.value.userName),1)]),_:1},8,["to"])])]),s("div",is,[e[13]||(e[13]=s("label",{class:"label"},"Rating",-1)),s("p",os,[s("div",ds,[(d(),o(w,null,g(5,t=>s("span",{key:t,class:n(["star",{"is-filled":t<=l.value.rating}])}," ★ ",2)),64)),s("span",ns,"("+i(l.value.rating)+"/5)",1)])])])]),s("div",cs,[s("div",rs,[e[14]||(e[14]=s("label",{class:"label"},"Created At",-1)),s("p",vs,i(A(l.value.createdAt)),1)]),l.value.updatedAt?(d(),o("div",us,[e[15]||(e[15]=s("label",{class:"label"},"Updated At",-1)),s("p",ms,i(A(l.value.updatedAt)),1)])):b("",!0)])]),l.value.comment?(d(),o("div",_s,[e[16]||(e[16]=s("label",{class:"label"},"Comment",-1)),s("div",fs,[s("p",null,i(l.value.comment),1)])])):b("",!0)])]),l.value.rating?(d(),o("div",ps,[e[21]||(e[21]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Detailed Rating")],-1)),s("div",bs,[s("div",hs,[s("div",ws,[s("div",gs,[e[18]||(e[18]=s("label",{class:"label"},"Service",-1)),s("div",ys,[(d(),o(w,null,g(5,t=>s("span",{key:t,class:n(["star",{"is-filled":t<=l.value.serviceRating}])}," ★ ",2)),64)),s("span",ks,"("+i(l.value.serviceRating||"N/A")+")",1)])])]),s("div",Rs,[s("div",Cs,[e[19]||(e[19]=s("label",{class:"label"},"Delivery Time",-1)),s("div",Ds,[(d(),o(w,null,g(5,t=>s("span",{key:t,class:n(["star",{"is-filled":t<=l.value.deliveryTimeRating}])}," ★ ",2)),64)),s("span",As,"("+i(l.value.deliveryTimeRating||"N/A")+")",1)])])]),s("div",xs,[s("div",Ns,[e[20]||(e[20]=s("label",{class:"label"},"Accuracy",-1)),s("div",Vs,[(d(),o(w,null,g(5,t=>s("span",{key:t,class:n(["star",{"is-filled":t<=l.value.accuracyRating}])}," ★ ",2)),64)),s("span",Ts,"("+i(l.value.accuracyRating||"N/A")+")",1)])])])])])])):b("",!0)]),s("div",Es,[s("div",Ss,[e[24]||(e[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Actions")],-1)),s("div",$s,[s("div",Fs,[s("button",{class:"button is-primary is-fullwidth",onClick:e[1]||(e[1]=t=>f.value=!0),disabled:c.value},e[22]||(e[22]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit Comment",-1)]),8,Is),s("button",{class:"button is-danger is-fullwidth",onClick:e[2]||(e[2]=t=>m.value=!0),disabled:c.value},e[23]||(e[23]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete Review",-1)]),8,Ms)])])]),s("div",Bs,[e[27]||(e[27]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Statistics")],-1)),s("div",Ls,[s("div",Us,[e[25]||(e[25]=s("label",{class:"label"},"Helpful Votes",-1)),s("p",Hs,i(l.value.helpfulVotes||0),1)]),s("div",Ps,[e[26]||(e[26]=s("label",{class:"label"},"Total Votes",-1)),s("p",zs,i(l.value.totalVotes||0),1)])])])])])])):b("",!0),s("div",{class:n(["modal",{"is-active":f.value}])},[s("div",{class:"modal-background",onClick:p}),s("div",js,[s("header",{class:"modal-card-head"},[e[28]||(e[28]=s("p",{class:"modal-card-title"},"Edit Review Comment",-1)),s("button",{class:"delete",onClick:p})]),s("section",qs,[s("div",Gs,[e[29]||(e[29]=s("label",{class:"label"},"Comment",-1)),s("div",Js,[I(s("textarea",{class:"textarea","onUpdate:modelValue":e[3]||(e[3]=t=>r.value=t),placeholder:"Enter review comment...",rows:"4",maxlength:"1000"},"              ",512),[[M,r.value]])]),s("p",Ks,i(r.value.length)+"/1000 characters",1)])]),s("footer",Os,[s("button",{class:n(["button is-primary",{"is-loading":c.value}]),onClick:V,disabled:!r.value.trim()||r.value.length>1e3}," Save Changes ",10,Qs),s("button",{class:"button",onClick:p},"Cancel")])])],2),s("div",{class:n(["modal",{"is-active":m.value}])},[s("div",{class:"modal-background",onClick:e[4]||(e[4]=t=>m.value=!1)}),s("div",Ws,[s("header",Xs,[e[30]||(e[30]=s("p",{class:"modal-card-title"},"Delete Review",-1)),s("button",{class:"delete",onClick:e[5]||(e[5]=t=>m.value=!1)})]),e[31]||(e[31]=s("section",{class:"modal-card-body"},[s("p",null,"Are you sure you want to delete this review? This action cannot be undone.")],-1)),s("footer",Ys,[s("button",{class:n(["button is-danger",{"is-loading":c.value}]),onClick:T}," Delete Review ",2),s("button",{class:"button",onClick:e[6]||(e[6]=t=>m.value=!1)},"Cancel")])])],2)])}}},te=E(Zs,[["__scopeId","data-v-7e3f472f"]]);export{te as default};
