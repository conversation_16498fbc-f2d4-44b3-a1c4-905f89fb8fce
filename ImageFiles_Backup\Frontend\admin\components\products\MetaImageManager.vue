<template>
  <div class="meta-image-manager">
    <div class="admin-section-header">
      <h4 class="admin-section-title">
        <i class="fas fa-image"></i>
        Meta Image (SEO)
      </h4>
      <p class="admin-section-description">
        Upload a meta image for SEO and social media sharing. This image will be used when your product is shared on social platforms.
      </p>
    </div>

    <!-- Current Meta Image -->
    <div v-if="currentMetaImage" class="current-meta-image mb-4">
      <div class="meta-image-card">
        <div class="meta-image-wrapper">
          <img 
            :src="currentMetaImage" 
            alt="Product meta image"
            class="meta-image-preview"
            @click="openImageModal"
            @error="handleImageError"
          />
          <div class="meta-image-overlay">
            <button 
              class="admin-btn admin-btn-sm admin-btn-secondary"
              @click="openImageModal"
              title="View full size"
            >
              <i class="fas fa-search-plus"></i>
            </button>
            <button 
              class="admin-btn admin-btn-sm admin-btn-danger"
              @click="confirmDelete"
              title="Delete meta image"
              :disabled="isDeleting"
            >
              <i class="fas fa-trash" v-if="!isDeleting"></i>
              <i class="fas fa-spinner fa-spin" v-else></i>
            </button>
          </div>
        </div>
        <div class="meta-image-info">
          <span class="meta-image-label">Current Meta Image</span>
        </div>
      </div>
    </div>

    <!-- Pending Meta Image -->
    <div v-if="pendingMetaImage && !currentMetaImage" class="pending-meta-image mb-4">
      <div class="notification is-warning is-light">
        <span class="icon-text">
          <span class="icon">
            <i class="fas fa-info-circle"></i>
          </span>
          <span>This meta image will be uploaded when you save the product.</span>
        </span>
      </div>
      
      <div class="meta-image-card">
        <div class="meta-image-wrapper">
          <img 
            :src="pendingMetaImage.preview" 
            alt="Pending meta image"
            class="meta-image-preview"
          />
          <div class="meta-image-overlay">
            <button 
              class="admin-btn admin-btn-sm admin-btn-danger"
              @click="removePendingImage"
              title="Remove pending image"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="meta-image-info">
          <span class="meta-image-label">{{ pendingMetaImage.name }}</span>
          <span class="meta-image-size">{{ formatFileSize(pendingMetaImage.size) }}</span>
        </div>
      </div>
    </div>

    <!-- Upload Area -->
    <div v-if="!currentMetaImage && !pendingMetaImage" class="upload-area">
      <div
        class="drop-zone"
        :class="{ 'drop-zone--dragover': isDragOver, 'drop-zone--disabled': isUploading }"
        @drop="handleDrop"
        @dragover.prevent="handleDragOver"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          @change="handleFileSelect"
          :disabled="isUploading"
          style="display: none;"
        />
        
        <div class="drop-content">
          <i class="fas fa-cloud-upload-alt upload-icon" v-if="!isUploading"></i>
          <i class="fas fa-spinner fa-spin upload-icon" v-else></i>
          <p class="upload-text">
            <strong v-if="!isUploading">Drop meta image here or click to browse</strong>
            <strong v-else>Uploading...</strong>
          </p>
          <p class="upload-subtext">
            Supports: JPG, PNG, GIF, WebP (max 5MB)
          </p>
        </div>
      </div>
    </div>

    <!-- Replace Button -->
    <div v-if="currentMetaImage || pendingMetaImage" class="replace-section">
      <button 
        class="admin-btn admin-btn-secondary"
        @click="triggerFileInput"
        :disabled="isUploading"
      >
        <i class="fas fa-sync-alt"></i>
        Replace Meta Image
      </button>
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="handleFileSelect"
        :disabled="isUploading"
        style="display: none;"
      />
    </div>

    <!-- Upload Progress -->
    <div v-if="isUploading" class="upload-progress">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
      </div>
      <p class="progress-text">Uploading meta image... {{ uploadProgress }}%</p>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="modal is-active">
      <div class="modal-background" @click="closeImageModal"></div>
      <div class="modal-content">
        <p class="image">
          <img :src="modalImageUrl" alt="Meta image preview" />
        </p>
      </div>
      <button class="modal-close is-large" @click="closeImageModal"></button>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal is-active">
      <div class="modal-background" @click="cancelDelete"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Confirm Delete</p>
          <button class="delete" @click="cancelDelete"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete the meta image? This action cannot be undone.</p>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" @click="deleteMetaImage" :disabled="isDeleting">
            <span v-if="!isDeleting">Delete</span>
            <span v-else>
              <i class="fas fa-spinner fa-spin"></i>
              Deleting...
            </span>
          </button>
          <button class="button" @click="cancelDelete">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { productsService } from '@/admin/services/products';

// Props
const props = defineProps({
  productId: {
    type: String,
    default: null
  },
  metaImage: {
    type: String,
    default: null
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'meta-image-updated',
  'meta-image-uploaded',
  'meta-image-deleted',
  'pending-meta-image-changed'
]);

// State
const currentMetaImage = ref(props.metaImage);
const pendingMetaImage = ref(null);
const isUploading = ref(false);
const isDeleting = ref(false);
const uploadProgress = ref(0);
const isDragOver = ref(false);
const showImageModal = ref(false);
const showDeleteModal = ref(false);
const modalImageUrl = ref('');
const fileInput = ref(null);

// Methods
const triggerFileInput = () => {
  if (!isUploading.value) {
    fileInput.value?.click();
  }
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (file) {
    processFile(file);
  }
  event.target.value = '';
};

const handleDrop = (event) => {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = false;

  if (isUploading.value) return;
  
  const files = Array.from(event.dataTransfer.files).filter(file => 
    file.type.startsWith('image/')
  );
  
  if (files.length > 0) {
    processFile(files[0]);
  }
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = true;
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const processFile = async (file) => {
  // Validate file size (5MB)
  if (file.size > 5 * 1024 * 1024) {
    alert('File is too large. Maximum size is 5MB.');
    return;
  }

  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    alert('Invalid file type. Please select a valid image file.');
    return;
  }

  // Always store for later upload (both create and edit modes)
  const reader = new FileReader();
  reader.onload = (e) => {
    pendingMetaImage.value = {
      file: file,
      name: file.name,
      size: file.size,
      preview: e.target.result
    };
    emit('pending-meta-image-changed', pendingMetaImage.value);
  };
  reader.readAsDataURL(file);
};

const uploadMetaImage = async (file) => {
  if (!props.productId) return;

  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    // Simulate progress
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10;
      }
    }, 100);

    const response = await productsService.uploadProductMetaImage(props.productId, file);
    
    clearInterval(progressInterval);
    uploadProgress.value = 100;

    if (response && response.success) {
      currentMetaImage.value = response.data;
      pendingMetaImage.value = null;
      emit('meta-image-uploaded', response.data);
      emit('meta-image-updated');
    }
  } catch (error) {
    console.error('Error uploading meta image:', error);
    alert('Failed to upload meta image. Please try again.');
  } finally {
    isUploading.value = false;
    uploadProgress.value = 0;
  }
};

const confirmDelete = () => {
  showDeleteModal.value = true;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
};

const deleteMetaImage = async () => {
  if (!props.productId) return;

  isDeleting.value = true;

  try {
    await productsService.deleteProductMetaImage(props.productId);
    currentMetaImage.value = null;
    showDeleteModal.value = false;
    emit('meta-image-deleted');
    emit('meta-image-updated');
  } catch (error) {
    console.error('Error deleting meta image:', error);
    alert('Failed to delete meta image. Please try again.');
  } finally {
    isDeleting.value = false;
  }
};

const removePendingImage = () => {
  pendingMetaImage.value = null;
  emit('pending-meta-image-changed', null);
};

const openImageModal = () => {
  modalImageUrl.value = currentMetaImage.value;
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  modalImageUrl.value = '';
};

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.png';
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Upload pending image when productId becomes available
const uploadPendingImage = async () => {
  if (pendingMetaImage.value && props.productId && !props.isCreate) {
    await uploadMetaImage(pendingMetaImage.value.file);
  }
};

// Watch for changes
watch(() => props.metaImage, (newValue) => {
  currentMetaImage.value = newValue;
});

watch(() => props.productId, () => {
  uploadPendingImage();
});

// Expose method for parent components
defineExpose({
  uploadPendingImage: () => {
    if (pendingMetaImage.value && props.productId) {
      return uploadMetaImage(pendingMetaImage.value.file);
    }
    return Promise.resolve();
  },
  getPendingImage: () => pendingMetaImage.value
});
</script>

<style scoped>
.meta-image-manager {
  width: 100%;
}

.admin-section-header {
  margin-bottom: var(--admin-space-lg);
}

.admin-section-title {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin: 0 0 var(--admin-space-xs) 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-section-description {
  margin: 0;
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
  line-height: 1.5;
}

.meta-image-card {
  display: inline-block;
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
  background: var(--admin-bg-primary);
}

.meta-image-wrapper {
  position: relative;
  width: 300px;
  height: 200px;
  overflow: hidden;
}

.meta-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform var(--admin-transition-base);
}

.meta-image-preview:hover {
  transform: scale(1.05);
}

.meta-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-sm);
  opacity: 0;
  transition: opacity var(--admin-transition-base);
}

.meta-image-wrapper:hover .meta-image-overlay {
  opacity: 1;
}

.meta-image-info {
  padding: var(--admin-space-sm);
  text-align: center;
  border-top: 1px solid var(--admin-border-light);
}

.meta-image-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
  margin-bottom: 2px;
}

.meta-image-size {
  display: block;
  font-size: var(--admin-text-xs);
  color: var(--admin-text-secondary);
}

.upload-area {
  margin-bottom: var(--admin-space-md);
}

.drop-zone {
  border: 2px dashed var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--admin-transition-base);
  background: var(--admin-bg-secondary);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover:not(.drop-zone--disabled) {
  border-color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.05);
}

.drop-zone--dragover {
  border-color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.1);
}

.drop-zone--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-icon {
  font-size: 3rem;
  color: var(--admin-text-secondary);
  margin-bottom: var(--admin-space-md);
}

.upload-text {
  font-size: var(--admin-text-base);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.upload-subtext {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}

.replace-section {
  margin-top: var(--admin-space-md);
  text-align: center;
}

.upload-progress {
  margin-top: var(--admin-space-md);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--admin-bg-secondary);
  border-radius: var(--admin-radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--admin-primary);
  transition: width var(--admin-transition-base);
}

.progress-text {
  text-align: center;
  margin-top: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-xs);
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-base);
  text-decoration: none;
}

.admin-btn-sm {
  padding: 4px 8px;
  font-size: var(--admin-text-xs);
}

.admin-btn-secondary {
  background: var(--admin-bg-secondary);
  color: var(--admin-text-primary);
  border-color: var(--admin-border-light);
}

.admin-btn-secondary:hover {
  background: var(--admin-bg-tertiary);
  border-color: var(--admin-border-medium);
}

.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
}

.admin-btn-danger:hover {
  background: #dc2626;
}

.admin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Notification styles */
.notification {
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  margin-bottom: var(--admin-space-md);
}

.notification.is-warning.is-light {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.icon-text {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  margin: 5% auto;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content img {
  max-width: 100%;
  max-height: 100%;
  border-radius: var(--admin-radius-md);
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:before {
  content: '×';
  font-size: 24px;
  line-height: 1;
}

.modal-card {
  position: relative;
  margin: 10% auto;
  background: white;
  border-radius: var(--admin-radius-md);
  max-width: 500px;
  width: 90%;
}

.modal-card-head {
  padding: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-card-title {
  margin: 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
}

.modal-card-body {
  padding: var(--admin-space-md);
}

.modal-card-foot {
  padding: var(--admin-space-md);
  border-top: 1px solid var(--admin-border-light);
  display: flex;
  gap: var(--admin-space-sm);
  justify-content: flex-end;
}

.delete {
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
}

.delete:before,
.delete:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 16px;
  background: var(--admin-text-secondary);
  transform: translate(-50%, -50%) rotate(45deg);
}

.delete:after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.button {
  padding: 8px 16px;
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-sm);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  cursor: pointer;
  font-size: var(--admin-text-sm);
  transition: all var(--admin-transition-base);
}

.button.is-danger {
  background: var(--admin-danger);
  color: white;
  border-color: var(--admin-danger);
}

.button:hover {
  background: var(--admin-bg-secondary);
}

.button.is-danger:hover {
  background: #dc2626;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
