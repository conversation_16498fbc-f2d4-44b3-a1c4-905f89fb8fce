using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;

namespace Marketplace.Application.Queries.Review;

public record GetCategoryReviewsQuery(
    string CategorySlug,
    string? Filter = null,
    string? OrderBy = null,
    bool Descending = false,
    int? Page = null,
    int? PageSize = null
    ) : PaginatedQuery<ReviewResponse>(Filter, OrderBy, Descending, Page, PageSize);
