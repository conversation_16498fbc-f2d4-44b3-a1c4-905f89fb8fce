# Admin Design System

Комплексна система дизайну для адміністративної панелі, заснована на дизайні сторінки Reports.

## 📁 Структура файлів

```
admin/
├── admin.css                 # Головний файл з імпортами
├── README.md                # Документація
├── core/                    # Основні стилі
│   ├── variables.css        # CSS змінні та токени дизайну
│   ├── typography.css       # Типографіка
│   └── layout.css          # Базові макети
├── components/             # Компоненти
│   ├── cards.css          # Картки та контейнери
│   ├── buttons.css        # Кнопки
│   ├── tables.css         # Таблиці
│   ├── forms.css          # Форми
│   └── alerts.css         # Сповіщення
└── pages/                 # Сторінки
    ├── dashboard.css      # Дашборд
    ├── users.css          # Користувачі
    ├── seller-requests.css # Заявки продавців
    ├── companies.css      # Компанії
    ├── products.css       # Продукти
    └── orders.css         # Замовлення
```

## 🎨 Дизайн токени

### Кольори
- **Primary**: `#667eea` - Основний колір
- **Secondary**: `#764ba2` - Вторинний колір
- **Success**: `#10b981` - Успіх
- **Warning**: `#f59e0b` - Попередження
- **Danger**: `#ef4444` - Помилка
- **Info**: `#3b82f6` - Інформація

### Типографіка
- **Font Family**: Inter, system-ui, sans-serif
- **Font Sizes**: 12px - 48px (xs - 5xl)
- **Font Weights**: 400, 500, 600, 700, 800

### Відступи
- **Space Scale**: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px, 48px, 64px

### Радіуси
- **Border Radius**: 4px, 6px, 8px, 12px, 16px, 9999px

### Тіні
- **Shadows**: sm, md, lg, xl з різними рівнями глибини

## 🧩 Компоненти

### Кнопки
```css
.admin-btn                 /* Базова кнопка */
.admin-btn-primary         /* Основна кнопка */
.admin-btn-secondary       /* Вторинна кнопка */
.admin-btn-success         /* Кнопка успіху */
.admin-btn-warning         /* Кнопка попередження */
.admin-btn-danger          /* Кнопка помилки */
.admin-btn-sm              /* Маленька кнопка */
.admin-btn-lg              /* Велика кнопка */
```

### Картки
```css
.admin-card                /* Базова картка */
.admin-card-header         /* Заголовок картки */
.admin-card-content        /* Контент картки */
.admin-card-footer         /* Підвал картки */
```

### Таблиці
```css
.admin-table               /* Базова таблиця */
.admin-table-striped       /* Смугаста таблиця */
.admin-table-hover         /* Таблиця з hover ефектом */
.admin-table-compact       /* Компактна таблиця */
```

### Форми
```css
.admin-form-group          /* Група полів форми */
.admin-form-label          /* Мітка поля */
.admin-form-input          /* Поле вводу */
.admin-form-select         /* Випадаючий список */
.admin-form-textarea       /* Текстова область */
.admin-form-checkbox       /* Чекбокс */
.admin-form-radio          /* Радіо кнопка */
```

### Сповіщення
```css
.admin-alert               /* Базове сповіщення */
.admin-alert-success       /* Сповіщення успіху */
.admin-alert-warning       /* Сповіщення попередження */
.admin-alert-danger        /* Сповіщення помилки */
.admin-alert-info          /* Інформаційне сповіщення */
```

## 📄 Сторінки

### Загальна структура
```html
<div class="admin-[page-name]">
  <div class="admin-page-header">
    <h1 class="admin-page-title">Назва сторінки</h1>
  </div>
  
  <div class="admin-[component]-filters">
    <!-- Фільтри -->
  </div>
  
  <div class="admin-[component]-table">
    <!-- Таблиця -->
  </div>
  
  <div class="pagination-wrapper">
    <!-- Пагінація -->
  </div>
</div>
```

### Специфічні класи сторінок

#### Dashboard
- `.admin-dashboard` - Основний контейнер
- `.admin-stat-card` - Картка статистики
- `.admin-chart-container` - Контейнер графіків

#### Users
- `.admin-users` - Основний контейнер
- `.admin-user-filters` - Фільтри користувачів
- `.admin-user-table` - Таблиця користувачів
- `.admin-user-actions` - Дії над користувачами

#### Products
- `.admin-products` - Основний контейнер
- `.admin-product-filters` - Фільтри продуктів
- `.admin-product-table` - Таблиця продуктів
- `.admin-product-image` - Зображення продукту

#### Orders
- `.admin-orders` - Основний контейнер
- `.admin-order-filters` - Фільтри замовлень
- `.admin-order-table` - Таблиця замовлень
- `.admin-order-status` - Статус замовлення

## 🎯 Використання

### Імпорт стилів
```css
@import '@/assets/css/admin/admin.css';
```

### Приклад використання в Vue компоненті
```vue
<template>
  <div class="admin-users">
    <div class="admin-page-header">
      <h1 class="admin-page-title">
        <i class="fas fa-users"></i>
        Користувачі
      </h1>
    </div>
    
    <div class="admin-user-filters">
      <div class="admin-form-group">
        <label class="admin-form-label">Пошук</label>
        <input class="admin-form-input" type="text" placeholder="Введіть ім'я або email">
      </div>
      <div class="admin-form-actions">
        <button class="admin-btn admin-btn-primary">Застосувати</button>
        <button class="admin-btn admin-btn-secondary">Скинути</button>
      </div>
    </div>
    
    <div class="admin-user-table">
      <table class="admin-table">
        <!-- Таблиця користувачів -->
      </table>
    </div>
  </div>
</template>
```

## 🔧 Налаштування

### Кастомізація кольорів
Змініть значення CSS змінних у файлі `core/variables.css`:

```css
:root {
  --admin-primary: #your-color;
  --admin-secondary: #your-color;
  /* ... інші кольори */
}
```

### Додавання нових компонентів
1. Створіть новий файл у папці `components/`
2. Додайте імпорт у `admin.css`
3. Використовуйте префікс `.admin-` для всіх класів

## 📱 Адаптивність

Система підтримує адаптивний дизайн з точками перелому:
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## ♿ Доступність

- Підтримка високого контрасту
- Зменшення анімацій для користувачів з обмеженнями
- Правильні focus стилі
- Семантична розмітка

## 🚀 Продуктивність

- Мінімальний CSS footprint
- Оптимізовані селектори
- Ефективне використання CSS змінних
- Підтримка tree-shaking

## 📝 Конвенції

1. **Префікси**: Всі класи починаються з `admin-`
2. **BEM методологія**: Використовується для складних компонентів
3. **Семантичні назви**: Класи мають зрозумілі назви
4. **Модульність**: Кожен компонент в окремому файлі

## 🔄 Міграція

Для міграції існуючих компонентів:
1. Замініть Bulma класи на admin класи
2. Оновіть структуру HTML відповідно до нових патернів
3. Перевірте адаптивність на різних екранах
4. Протестуйте доступність

## 📚 Додаткові ресурси

- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)
- [BEM Methodology](http://getbem.com/)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
