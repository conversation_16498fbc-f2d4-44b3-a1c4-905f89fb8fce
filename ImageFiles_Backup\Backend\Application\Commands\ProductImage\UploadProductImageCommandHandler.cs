﻿using Marketplace.Domain.Services;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.ProductImage;

/// <summary>
/// Обробник команди для завантаження зображення для Product
/// </summary>
public class UploadProductImageCommandHandler : IRequestHandler<UploadProductImageCommand, FileUploadResult>
{
    private readonly string _baseUrl;
    private readonly IProductRepository _productRepository;
    private readonly IProductImageRepository _productImageRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<UploadProductImageCommandHandler> _logger;

    public UploadProductImageCommandHandler(
        IConfiguration configuration,
        IProductRepository productRepository,
        IProductImageRepository productImageRepository,
        IFileService fileService,
        ILogger<UploadProductImageCommandHandler> logger)
    {
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
        _productRepository = productRepository;
        _productImageRepository = productImageRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadProductImageCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation($"Processing upload for product {request.productId}");
            _logger.LogInformation($"File: {request.File.FileName}, Size: {request.File.Length}");

            // Перевіряємо, чи продукт існує
            var product = await _productRepository.GetByIdAsync(request.productId, cancellationToken);
            if (product == null)
            {
                throw new InvalidOperationException($"Продукт з ID {request.productId} не знайдено.");
            }

            // Отримуємо поточні зображення для визначення порядку
            var existingImages = await _productImageRepository.GetByProductIdAsync(request.productId, cancellationToken);
            var nextOrder = existingImages.Any() ? existingImages.Max(pi => pi.Order) + 1 : 0;
            var hasMainImage = existingImages.Any(pi => pi.IsMain);

            // Завантажуємо файл
            var fileUrl = await _fileService.SaveFileAsync(
                request.File.OpenReadStream(),
                request.File.FileName,
                request.File.ContentType,
                $"images/products/{request.productId}",
                cancellationToken);

            var imageUrl = $"{_baseUrl}/uploads/{fileUrl}";

            // Створюємо нове зображення продукту
            var productImage = new Domain.Entities.ProductImage(
                request.productId,
                new Url(imageUrl),
                nextOrder,
                !hasMainImage, // Перше зображення стає головним, якщо немає головного
                request.File.FileName
            );

            await _productImageRepository.AddAsync(productImage, cancellationToken);

            _logger.LogInformation($"Successfully uploaded image {productImage.Id} for product {request.productId}");

            // Повертаємо результат
            return new FileUploadResult
            {
                Id = productImage.Id,
                FileUrl = imageUrl,
                FileName = request.File.FileName,
                ContentType = request.File.ContentType,
                Size = request.File.Length
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error uploading image for product {request.productId}");
            throw;
        }
    }
}
