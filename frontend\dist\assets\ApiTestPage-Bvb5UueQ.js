import{_ as k,g as t,c as r,a as e,k as a,t as o,x as T,D as b,o as n}from"./index-L-hJxM_5.js";import{p as C}from"./products-Bpq90UOX.js";const h={class:"api-test-page"},E={class:"test-section"},w=["disabled"],S={key:0,class:"result"},D={key:1,class:"error"},R={class:"test-section"},x=["disabled"],N={key:0,class:"result"},B={key:1,class:"error"},U={class:"test-section"},J=["disabled"],L={key:0,class:"result"},O={key:1,class:"error"},V={__name:"ApiTestPage",setup(z){const d=t(!1),P=t(null),c=t(null),v=t(!1),m=t(null),g=t(null),p=t(!1),I=t(null),u=t(null),i=t(""),A=async()=>{try{d.value=!0,c.value=null,console.log("Testing companies API...");const l=await C.getCompanies({search:"",pageSize:10});console.log("Companies API result:",l),P.value=l}catch(l){console.error("Companies API error:",l),c.value=l.message||"Unknown error"}finally{d.value=!1}},f=async()=>{try{v.value=!0,g.value=null,console.log("Testing categories API...");const l=await C.getCategories({search:"",pageSize:10});console.log("Categories API result:",l),m.value=l}catch(l){console.error("Categories API error:",l),g.value=l.message||"Unknown error"}finally{v.value=!1}},y=async()=>{if(!i.value){u.value="Please enter a product ID";return}try{p.value=!0,u.value=null,console.log("Testing product API for ID:",i.value);const l=await C.getProductById(i.value);console.log("Product API result:",l),I.value=l}catch(l){console.error("Product API error:",l),u.value=l.message||"Unknown error"}finally{p.value=!1}};return(l,s)=>(n(),r("div",h,[s[10]||(s[10]=e("h1",null,"API Test Page",-1)),e("div",E,[s[3]||(s[3]=e("h2",null,"Companies API Test",-1)),e("button",{onClick:A,disabled:d.value},o(d.value?"Loading...":"Test Companies API"),9,w),P.value?(n(),r("div",S,[s[1]||(s[1]=e("h3",null,"Companies Result:",-1)),e("pre",null,o(JSON.stringify(P.value,null,2)),1)])):a("",!0),c.value?(n(),r("div",D,[s[2]||(s[2]=e("h3",null,"Companies Error:",-1)),e("pre",null,o(c.value),1)])):a("",!0)]),e("div",R,[s[6]||(s[6]=e("h2",null,"Categories API Test",-1)),e("button",{onClick:f,disabled:v.value},o(v.value?"Loading...":"Test Categories API"),9,x),m.value?(n(),r("div",N,[s[4]||(s[4]=e("h3",null,"Categories Result:",-1)),e("pre",null,o(JSON.stringify(m.value,null,2)),1)])):a("",!0),g.value?(n(),r("div",B,[s[5]||(s[5]=e("h3",null,"Categories Error:",-1)),e("pre",null,o(g.value),1)])):a("",!0)]),e("div",U,[s[9]||(s[9]=e("h2",null,"Product API Test",-1)),T(e("input",{"onUpdate:modelValue":s[0]||(s[0]=_=>i.value=_),placeholder:"Enter Product ID"},null,512),[[b,i.value]]),e("button",{onClick:y,disabled:p.value},o(p.value?"Loading...":"Test Product API"),9,J),I.value?(n(),r("div",L,[s[7]||(s[7]=e("h3",null,"Product Result:",-1)),e("pre",null,o(JSON.stringify(I.value,null,2)),1)])):a("",!0),u.value?(n(),r("div",O,[s[8]||(s[8]=e("h3",null,"Product Error:",-1)),e("pre",null,o(u.value),1)])):a("",!0)])]))}},q=k(V,[["__scopeId","data-v-d1ac274a"]]);export{q as default};
