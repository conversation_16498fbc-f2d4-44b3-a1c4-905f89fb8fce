<template>
  <div class="admin-products">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-box admin-page-icon"></i>
          Products Management
        </h1>
        <p class="admin-page-subtitle">Manage product catalog, inventory, and approvals</p>
      </div>
      <div class="admin-page-actions">
        <router-link to="/admin/products/create" class="admin-btn admin-btn-primary">
          <i class="fas fa-plus"></i>
          Add Product
        </router-link>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-product-filters">
      <search-and-filters
        :search="filters.search"
        :filters="filters"
        :filter-fields="filterOptions"
        :loading="loading"
        @search-changed="handleSearchChange"
        @filter-changed="handleFilterChange"
        @reset-filters="resetFilters" />
    </div>

    <!-- Products Table -->
    <div class="card">
      <div class="card-content">
        <!-- Loading State -->
        <div v-if="loading && (!products || !products.length)" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading products...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="!loading && products && products.length === 0" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-box fa-2x"></i>
          </span>
          <p class="mt-2">No products found</p>
          <p class="mt-2">Try adjusting your filters or add a new product</p>
          <div class="mt-4">
            <router-link to="/admin/products/create" class="button is-primary">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Add Product</span>
            </router-link>
          </div>
        </div>

        <!-- Products Table -->
        <div v-else-if="!loading && products && products.length > 0">
          <div class="table-container">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th>Sales</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="product in products"
                  v-if="product && product.id"
                  :key="`product-${product.id}`"
                  @click="console.log('🔍 Product clicked:', product)">
                  <td class="image-cell">
                    <figure
                      class="image is-64x64 product-image-preview"
                      @click="openImageViewer(product)"
                      :title="hasImages(product) ? 'Click to view images' : 'No images'"
                    >
                      <img
                        :src="getImageSrc(product) || (console.log('🖼️ Image src is:', getImageSrc(product)), getPlaceholderImage())"
                        :alt="product.name || 'Product Image'"
                        @error="handleImageError($event, product)"
                        loading="lazy">
                      <div v-if="hasMultipleImages(product)" class="image-count-badge">
                        {{ getImageCount(product) }}
                      </div>
                    </figure>
                  </td>
                  <td>
                    <div class="product-name">{{ product.name || 'Unnamed Product' }}</div>
                    <div class="product-sku">SKU: {{ product.sku || 'N/A' }}</div>
                  </td>
                  <td>{{ getCategoryName(product.categoryId) }}</td>
                  <td>{{ formatCurrency(product.priceAmount || product.price || 0) }}</td>
                  <td>
                    <span
                      class="tag"
                      :class="getStockClass(product.stock || 0)">
                      {{ product.stock || 0 }}
                    </span>
                  </td>
                  <td>
                    <span class="tag is-success">
                      {{ product.sales || 0 }}
                    </span>
                  </td>
                  <td>
                    <status-badge
                      v-if="product.status !== undefined"
                      :status="product.status"
                      type="product" />
                    <span v-else class="tag is-light">Unknown</span>
                  </td>
                  <td>
                    <div class="buttons are-small" v-if="product && product.id">
                      <router-link
                        :to="`/admin/products/${product.id}`"
                        class="button is-info"
                        title="View Product"
                        :key="`view-${product.id}`">
                        <span class="icon is-small">
                          <i class="fas fa-eye"></i>
                        </span>
                      </router-link>
                      <router-link
                        :to="`/admin/products/${product.id}/edit`"
                        class="button is-primary"
                        title="Edit Product"
                        :key="`edit-${product.id}`">
                        <span class="icon is-small">
                          <i class="fas fa-edit"></i>
                        </span>
                      </router-link>

                      <button
                        class="button is-danger"
                        @click="confirmDelete(product)"
                        title="Delete Product"
                        :disabled="loading"
                        :key="`delete-${product.id}`">
                        <span class="icon is-small">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <pagination 
            :current-page="currentPage" 
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete Product"
      :message="`Are you sure you want to delete '${productToDelete?.name}'? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteProduct"
      @cancel="cancelDelete" />


  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';

// Enhanced state management with caching
const products = ref([]);
const categories = ref([]);

// Computed loading state
const loading = computed(() =>
  loadingStates.products || loadingStates.categories || loadingStates.delete
);

// Pagination state
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(10);

// Cache management
const cache = reactive({
  categories: null,
  categoriesTimestamp: null,
  products: new Map(), // Cache products by filter combination
  lastFilters: null
});

// Cache utilities
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const isCacheValid = (type, timestamp) => {
  if (!timestamp) return false;
  return Date.now() - timestamp < CACHE_DURATION;
};

const getCacheKey = (filters) => {
  return JSON.stringify({
    search: filters.search || '',
    categoryId: filters.categoryId || '',
    status: filters.status || '',
    stock: filters.stock || ''
  });
};

// Optimized computed properties
const hasProducts = computed(() =>
  Array.isArray(products.value) && products.value.length > 0
);

const hasCategories = computed(() =>
  Array.isArray(categories.value) && categories.value.length > 0
);

const isDataReady = computed(() =>
  !loadingStates.products && !loadingStates.categories
);

const hasActiveFilters = computed(() =>
  filters.search || filters.categoryId || filters.status || filters.stock
);

// Memoized category lookup for performance
const categoryMap = computed(() => {
  const map = new Map();
  if (hasCategories.value) {
    categories.value.forEach(category => {
      if (category && category.id) {
        map.set(category.id, category.name);
      }
    });
  }
  return map;
});

// Enhanced AbortController management
const requestControllers = reactive({
  products: null,
  categories: null,
  delete: null
});

// Request management utilities
const createAbortController = (type) => {
  // Cancel previous request if exists
  if (requestControllers[type]) {
    requestControllers[type].abort();
  }

  // Create new controller
  requestControllers[type] = new AbortController();
  return requestControllers[type];
};

const cleanupController = (type) => {
  if (requestControllers[type]) {
    requestControllers[type] = null;
  }
};

// UI state
const showDeleteModal = ref(false);
const productToDelete = ref(null);
const searchTimeout = ref(null);

// Loading states for different operations
const loadingStates = reactive({
  products: false,
  categories: false,
  delete: false
});

// Error management
const errors = reactive({
  products: null,
  categories: null,
  delete: null,
  general: null
});

// Centralized error handling
const handleError = (type, error, context = '') => {
  console.error(`Error in ${type}${context ? ` (${context})` : ''}:`, error);

  // Store error for UI display
  errors[type] = {
    message: error.message || 'An unexpected error occurred',
    code: error.code || 'UNKNOWN_ERROR',
    timestamp: Date.now(),
    context
  };

  // Auto-clear error after 10 seconds
  setTimeout(() => {
    if (errors[type] && errors[type].timestamp === errors[type].timestamp) {
      errors[type] = null;
    }
  }, 10000);
};

// Clear specific error
const clearError = (type) => {
  errors[type] = null;
};

// Check if error should trigger retry
const shouldRetry = (error) => {
  return error.code === 'NETWORK_ERROR' ||
         error.code === 'TIMEOUT' ||
         error.message?.includes('timeout') ||
         error.message?.includes('network');
};

// Filters
const filters = reactive({
  search: '',
  categoryId: '',
  status: '',
  stock: ''
});

// Filter options for SearchAndFilters component
const filterOptions = computed(() => [
  {
    key: 'categoryId',
    label: 'Category',
    type: 'select',
    options: [
      { value: '', label: 'All Categories' },
      ...categories.value.map(category => ({
        value: category.id,
        label: category.name || 'Unknown Category'
      }))
    ]
  },
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { value: '', label: 'All Statuses' },
      { value: 'Pending', label: 'Pending' },
      { value: 'Approved', label: 'Approved' },
      { value: 'Rejected', label: 'Rejected' }
    ]
  },
  {
    key: 'stock',
    label: 'Stock',
    type: 'select',
    options: [
      { value: '', label: 'All' },
      { value: 'in_stock', label: 'In Stock' },
      { value: 'low_stock', label: 'Low Stock' },
      { value: 'out_of_stock', label: 'Out of Stock' }
    ]
  }
]);

// Search and filter handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
  debouncedSearch();
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
  fetchProducts(1);
};

// Enhanced fetch products with better error handling
const fetchProducts = async (page = 1, retryCount = 0) => {
  const maxRetries = 2;

  try {
    // Create new abort controller for this request
    const controller = createAbortController('products');

    loadingStates.products = true;
    currentPage.value = page;

    // Add delay for rapid requests to prevent race conditions
    if (retryCount === 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const response = await productsService.getProducts({
      page: currentPage.value,
      pageSize: itemsPerPage.value,
      limit: itemsPerPage.value,
      signal: controller.signal,
      ...filters
    });

    console.log('Products API response:', response);

    // Enhanced response handling with validation
    if (response && typeof response === 'object') {
      const productsData = response.data || response.products || response.items || [];

      // Validate products array
      if (Array.isArray(productsData)) {
        products.value = productsData.filter(product =>
          product && typeof product === 'object' && product.id
        );
        console.log('Products loaded:', products.value.length);

        // Log first product structure for debugging
        if (products.value.length > 0) {
          console.log('🔍 First product structure:', products.value[0]);
          console.log('🔍 First product keys:', Object.keys(products.value[0]));
          console.log('🔍 First product productImages:', products.value[0].productImages);
          console.log('🔍 First product images:', products.value[0].images);
        }
      } else {
        products.value = [];
        console.warn('Invalid products data format:', productsData);
      }

      // Handle pagination with validation
      if (response.pagination && typeof response.pagination === 'object') {
        totalPages.value = Math.max(1, response.pagination.totalPages || 1);
        totalItems.value = Math.max(0, response.pagination.total || 0);
        currentPage.value = Math.max(1, response.pagination.page || 1);
        console.log('Pagination:', response.pagination);
      }
    } else {
      products.value = [];
      console.warn('Invalid response format:', response);
    }

  } catch (error) {
    // Handle different types of errors
    if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
      console.log('Products request was canceled');
      clearError('products'); // Clear any previous errors
      return;
    }

    // Retry logic for network errors
    if (retryCount < maxRetries && shouldRetry(error)) {
      console.log(`Retrying products fetch (attempt ${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return fetchProducts(page, retryCount + 1);
    }

    // Handle error with centralized system
    handleError('products', error, `page ${page}, retry ${retryCount}`);
    products.value = [];

    // Attempt graceful degradation
    if (retryCount === 0) {
      console.log('Attempting graceful degradation...');
      // Could show cached data or simplified view
    }

  } finally {
    loadingStates.products = false;
    cleanupController('products');
  }
};

// Enhanced fetch categories with caching and error handling
const fetchCategories = async (retryCount = 0, forceRefresh = false) => {
  const maxRetries = 2;

  // Check cache first
  if (!forceRefresh && cache.categories && isCacheValid('categories', cache.categoriesTimestamp)) {
    console.log('Using cached categories');
    categories.value = cache.categories;
    return;
  }

  try {
    // Create new abort controller for this request
    const controller = createAbortController('categories');

    loadingStates.categories = true;

    const response = await categoriesService.getCategories({
      signal: controller.signal
    });
    console.log('Categories API response:', response);

    // Enhanced response handling with validation
    if (response && typeof response === 'object') {
      const categoriesData = response.categories || response.data ||
                           (Array.isArray(response) ? response : []);

      // Validate categories array
      if (Array.isArray(categoriesData)) {
        const validCategories = categoriesData.filter(category =>
          category && typeof category === 'object' && category.id
        );

        categories.value = validCategories;

        // Cache the categories
        cache.categories = validCategories;
        cache.categoriesTimestamp = Date.now();

        console.log('Categories loaded and cached:', categories.value.length);
      } else {
        categories.value = [];
        console.warn('Invalid categories data format:', categoriesData);
      }
    } else {
      categories.value = [];
      console.warn('Invalid categories response format:', response);
    }

  } catch (error) {
    // Handle different types of errors
    if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
      console.log('Categories request was canceled');
      return;
    }

    // Retry logic for network errors
    if (retryCount < maxRetries && (
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT' ||
      error.message?.includes('timeout')
    )) {
      console.log(`Retrying categories fetch (attempt ${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return fetchCategories(retryCount + 1);
    }

    console.error('Error fetching categories:', error);
    categories.value = [];
  } finally {
    loadingStates.categories = false;
    cleanupController('categories');
  }
};

// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.categoryId = '';
  filters.status = '';
  filters.stock = '';
  fetchProducts(1);
};

// Handle page change
const handlePageChange = (page) => {
  fetchProducts(page);
};

// Debounced search
const debouncedSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  searchTimeout.value = setTimeout(() => {
    fetchProducts(1);
    searchTimeout.value = null;
  }, 500); // 500ms debounce
};

// Optimized category name lookup using memoized map
const getCategoryName = (categoryId) => {
  if (!categoryId) return 'Unknown';
  return categoryMap.value.get(categoryId) || 'Unknown';
};

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Get stock class
const getStockClass = (stock) => {
  if (stock <= 0) {
    return 'is-danger';
  } else if (stock <= 5) {
    return 'is-warning';
  } else {
    return 'is-success';
  }
};

// Get image source with fallback
const getImageSrc = (product) => {
  console.log('🚀 getImageSrc function called!', product?.name || 'Unknown product');
  if (!product) {
    console.log('❌ No product provided to getImageSrc');
    return getPlaceholderImage();
  }

  // Debug: Log for all products to see structure (temporary)
  console.log('🔍 getImageSrc called for product:', {
    name: product.name,
    id: product.id,
    productImages: product.productImages,
    images: product.images,
    image: product.image,
    metaImage: product.metaImage,
    allKeys: Object.keys(product)
  });

  // Log each productImage in detail
  if (product.productImages && Array.isArray(product.productImages)) {
    console.log(`📸 Found ${product.productImages.length} productImages for ${product.name}`);
    product.productImages.forEach((img, index) => {
      console.log(`🖼️ ProductImage ${index}:`, img);
      console.log(`🖼️ Keys in image ${index}:`, Object.keys(img));
    });
  } else {
    console.log('❌ No productImages array found for', product.name);
  }

  // Helper function to extract image URL from various possible field names
  const extractImageUrl = (imageObj) => {
    if (!imageObj) return null;

    // Log the image object structure for debugging
    if (product.name && product.name.toLowerCase().includes('asus')) {
      console.log('🔍 Extracting URL from image object:', imageObj);
      console.log('🔍 Available keys:', Object.keys(imageObj));
    }

    const url = imageObj.image || imageObj.Image || imageObj.imageUrl || imageObj.ImageUrl ||
                imageObj.url || imageObj.Url || imageObj.src || imageObj.Src ||
                imageObj.path || imageObj.Path || imageObj.filePath || imageObj.FilePath;

    if (product.name && product.name.toLowerCase().includes('asus')) {
      console.log('🔍 Extracted URL:', url);
    }

    return url;
  };

  // Helper function to check if image URL is valid
  const isValidImageUrl = (url) => {
    return url && typeof url === 'string' && url.trim() !== '' && !url.toLowerCase().includes('meta');
  };

  // 1. Check for ProductImages array (most common structure)
  if (product.productImages && Array.isArray(product.productImages) && product.productImages.length > 0) {
    console.log('📸 Found productImages:', product.productImages);

    // Look for main image first
    const mainImage = product.productImages.find(img =>
      img.isMain === true || img.IsMain === true || img.main === true ||
      img.isMain === 1 || img.IsMain === 1 || img.main === 1
    );
    if (mainImage) {
      console.log('✅ Found main productImage:', mainImage);
      const imageUrl = extractImageUrl(mainImage);
      if (isValidImageUrl(imageUrl)) {
        console.log('✅ Using main productImage URL:', imageUrl);
        return imageUrl;
      }
    }

    // If no main image, use first available image
    const firstImage = product.productImages[0];
    const imageUrl = extractImageUrl(firstImage);
    if (isValidImageUrl(imageUrl)) {
      console.log('📷 Using first productImage URL:', imageUrl);
      return imageUrl;
    }
  }

  // 2. Check for images array (alternative structure)
  if (product.images && Array.isArray(product.images) && product.images.length > 0) {
    console.log('📸 Found images:', product.images);

    // Look for main image first
    const mainImage = product.images.find(img =>
      img.isMain === true || img.IsMain === true || img.main === true ||
      img.isMain === 1 || img.IsMain === 1 || img.main === 1
    );
    if (mainImage) {
      console.log('✅ Found main image:', mainImage);
      const imageUrl = extractImageUrl(mainImage);
      if (isValidImageUrl(imageUrl)) {
        console.log('✅ Using main image URL:', imageUrl);
        return imageUrl;
      }
    }

    // If no main image, use first available image
    const firstImage = product.images[0];
    const imageUrl = extractImageUrl(firstImage);
    if (isValidImageUrl(imageUrl)) {
      console.log('📷 Using first image URL:', imageUrl);
      return imageUrl;
    }
  }

  // 3. Check for direct image field (legacy)
  if (isValidImageUrl(product.image)) {
    console.log('📷 Using direct image field:', product.image);
    return product.image;
  }

  // 4. Check for mainImage field
  if (isValidImageUrl(product.mainImage)) {
    console.log('📷 Using mainImage field:', product.mainImage);
    return product.mainImage;
  }

  // Return a data URL for a simple gray placeholder
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhWMjAiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+YXRoIGQ9Ik0yMCAyNEwyOCAyNCIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZ�IvPgo8L3N2Zz4K';
};

// Get placeholder image
const getPlaceholderImage = () => {
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhWMjAiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+YXRoIGQ9Ik0yMCAyNEwyOCAyNCIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K';
};

const hasImages = (product) => {
  return (product.images && product.images.length > 0) ||
         (product.productImages && product.productImages.length > 0) ||
         (product.image && product.image.trim() !== '');
};

// Handle image error
const handleImageError = (event, product) => {
  // Prevent infinite loop by checking if already using fallback
  if (!event.target.src.startsWith('data:image/svg+xml')) {
    console.log('🖼️ Image failed to load for product:', product.name);
    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhWMjAiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+YXRoIGQ9Ik0yMCAyNEwyOCAyNCIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K';
  }
};

// Confirm delete
const confirmDelete = (product) => {
  productToDelete.value = product;
  showDeleteModal.value = true;
};

// Delete product
const deleteProduct = async () => {
  if (!productToDelete.value) return;
  
  try {
    await productsService.deleteProduct(productToDelete.value.id);
    products.value = products.value.filter(p => p.id !== productToDelete.value.id);
    showDeleteModal.value = false;
    productToDelete.value = null;
  } catch (error) {
    console.error('Error deleting product:', error);
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  productToDelete.value = null;
};

// Enhanced lifecycle management
onMounted(async () => {
  try {
    // Ensure DOM is ready
    await nextTick();

    // Initialize data with proper sequencing
    console.log('ProductList: Starting data initialization');

    // Load categories first (needed for filters)
    await fetchCategories();

    // Then load products
    await fetchProducts();

    console.log('ProductList: Data initialization completed');

  } catch (error) {
    console.error('Error in ProductList onMounted:', error);

    // Attempt recovery
    try {
      console.log('ProductList: Attempting recovery...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      await fetchProducts();
    } catch (recoveryError) {
      console.error('ProductList: Recovery failed:', recoveryError);
    }
  }
});

// Enhanced cleanup with proper controller management
onUnmounted(() => {
  console.log('ProductList: Cleaning up...');

  // Cancel all ongoing requests
  Object.keys(requestControllers).forEach(type => {
    if (requestControllers[type]) {
      console.log(`Canceling ${type} request`);
      requestControllers[type].abort();
      requestControllers[type] = null;
    }
  });

  // Clear search timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
    searchTimeout.value = null;
  }

  // Reset loading states
  Object.keys(loadingStates).forEach(key => {
    loadingStates[key] = false;
  });

  console.log('ProductList: Cleanup completed');
});
</script>

<style scoped>
@import '@/assets/css/admin/pages/products.css';

/* Product image improvements */
.product-image-preview {
  cursor: pointer;
  transition: transform 0.2s ease;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.product-image-preview:hover {
  transform: scale(1.05);
  border-color: #3b82f6;
}

.product-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.image-cell {
  width: 80px;
  text-align: center;
}

.image-count-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: 600;
}
</style>
