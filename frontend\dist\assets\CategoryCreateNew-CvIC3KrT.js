import{_ as F,g as n,C as B,i as z,f as $,c,a,k as x,b as i,w as I,d as r,r as A,t as y,x as g,D as u,n as U,m as L,I as H,e as j,o as p}from"./index-L-hJxM_5.js";import{E as G}from"./EnhancedCategorySelector-v7wVF3eo.js";import{E as J}from"./EntityImageManager-DFnfDtsC.js";import{E as K}from"./EntityMetaImageManager-oIAk4B-a.js";import{U as Q}from"./UploadProgress-DTyUnfEW.js";import"./products-Bpq90UOX.js";import"./image.service-DOD4lHqw.js";const W={class:"category-create-new"},X={class:"create-header"},Y={class:"header-content"},Z={class:"breadcrumb"},ee={class:"header-actions"},ae={key:0,class:"message message-error"},se={class:"message-content"},te={key:1,class:"message message-success"},le={class:"message-content"},oe={class:"form-card"},re={class:"form-card-content"},ne={class:"form-grid"},ie={class:"form-field"},de={class:"form-field"},me={class:"form-field"},ce={class:"form-card"},ge={class:"form-card-content"},ue={class:"form-field"},pe={class:"form-card"},fe={class:"form-card-content"},ve={class:"form-field"},ye={class:"form-field"},be={class:"form-card"},Ce={class:"form-card-content"},Ie={class:"form-grid"},he={class:"form-field"},we={class:"form-help"},xe={class:"form-field"},Ue={class:"form-field"},De={class:"form-help"},Me={class:"form-actions"},ke=["disabled"],Ee={key:0,class:"fas fa-spinner fa-spin"},Se={key:1,class:"fas fa-plus"},_e={key:0,class:"mt-3"},Te={__name:"CategoryCreateNew",setup(Ve){const D=$(),M=j(),d=n(!1),m=n(""),f=n(""),b=n(null),C=n(null),v=n([]),h=n(null),w=n(null),s=B({name:"",slug:"",description:"",parentId:null,image:"",metaImage:"",metaTitle:"",metaDescription:"",displayOrder:0}),k=()=>{s.name&&(s.slug=s.name.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"))},E=t=>{s.parentId=t},S=t=>{s.image=t.fileUrl,h.value=null,console.log("Category image uploaded:",t)},_=()=>{s.image="",h.value=null,console.log("Category image deleted")},T=t=>{h.value=t,console.log("Category image changed (local):",t)},V=t=>{s.metaImage=t.fileUrl,w.value=null,console.log("Category meta image uploaded:",t)},O=()=>{s.metaImage="",w.value=null,console.log("Category meta image deleted")},N=t=>{w.value=t,console.log("Category meta image changed (local):",t)},P=async t=>{const e=v.value.find(o=>o.id===t);if(e){e.status="uploading",e.progress=0,e.error=null;try{console.log("Retrying upload:",t)}catch(o){e.status="error",e.error=o.message}}},R=t=>{const e=v.value.findIndex(o=>o.id===t);e!==-1&&v.value.splice(e,1)},q=async()=>{var t;d.value=!0,m.value="",f.value="";try{const e={name:s.name,slug:s.slug,description:s.description,parentId:s.parentId||null,metaTitle:s.metaTitle,metaDescription:s.metaDescription,displayOrder:s.displayOrder},o=await H.createCategory(e),l=o.id||((t=o.data)==null?void 0:t.id);if(l)b.value&&b.value.uploadPendingFile&&await b.value.uploadPendingFile(),C.value&&C.value.uploadPendingFile&&await C.value.uploadPendingFile(),f.value="Category created successfully!",setTimeout(()=>{M.push(`/admin/categories/${l}`)},1500);else throw new Error("Category created but ID not returned")}catch(e){console.error("Error creating category:",e),e.response&&e.response.data&&e.response.data.message?m.value=e.response.data.message:m.value="Failed to create category. Please try again."}finally{d.value=!1}};return z(()=>{D.query.parentId&&(s.parentId=D.query.parentId)}),(t,e)=>{const o=A("router-link");return p(),c("div",W,[a("div",X,[a("div",Y,[a("nav",Z,[i(o,{to:"/admin",class:"breadcrumb-item"},{default:I(()=>e[9]||(e[9]=[r("Dashboard")])),_:1}),e[11]||(e[11]=a("span",{class:"breadcrumb-separator"},"/",-1)),i(o,{to:"/admin/categories",class:"breadcrumb-item"},{default:I(()=>e[10]||(e[10]=[r("Categories")])),_:1}),e[12]||(e[12]=a("span",{class:"breadcrumb-separator"},"/",-1)),e[13]||(e[13]=a("span",{class:"breadcrumb-item breadcrumb-current"},"Create New",-1))]),e[14]||(e[14]=a("h1",{class:"create-title"},"Create New Category",-1)),e[15]||(e[15]=a("p",{class:"create-subtitle"},"Add a new category to organize your products",-1))]),a("div",ee,[i(o,{to:"/admin/categories",class:"action-btn action-btn-light"},{default:I(()=>e[16]||(e[16]=[a("i",{class:"fas fa-arrow-left"},null,-1),r(" Back to Categories ")])),_:1})])]),m.value?(p(),c("div",ae,[a("div",se,[e[17]||(e[17]=a("i",{class:"fas fa-exclamation-circle"},null,-1)),a("span",null,y(m.value),1)]),a("button",{class:"message-close",onClick:e[0]||(e[0]=l=>m.value="")},e[18]||(e[18]=[a("i",{class:"fas fa-times"},null,-1)]))])):x("",!0),f.value?(p(),c("div",te,[a("div",le,[e[19]||(e[19]=a("i",{class:"fas fa-check-circle"},null,-1)),a("span",null,y(f.value),1)]),a("button",{class:"message-close",onClick:e[1]||(e[1]=l=>f.value="")},e[20]||(e[20]=[a("i",{class:"fas fa-times"},null,-1)]))])):x("",!0),a("form",{onSubmit:L(q,["prevent"]),class:"create-form"},[a("div",oe,[e[25]||(e[25]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-info-circle"}),r(" Basic Information ")])],-1)),a("div",re,[a("div",ne,[a("div",ie,[e[21]||(e[21]=a("label",{class:"form-label"},[r(" Name "),a("span",{class:"required"},"*")],-1)),g(a("input",{class:"form-input",type:"text",placeholder:"Category name","onUpdate:modelValue":e[2]||(e[2]=l=>s.name=l),onInput:k,required:""},null,544),[[u,s.name]])]),a("div",de,[e[22]||(e[22]=a("label",{class:"form-label"},[r(" Slug "),a("span",{class:"required"},"*")],-1)),g(a("input",{class:"form-input form-input-code",type:"text",placeholder:"category-slug","onUpdate:modelValue":e[3]||(e[3]=l=>s.slug=l),required:""},null,512),[[u,s.slug]]),e[23]||(e[23]=a("p",{class:"form-help"},"URL-friendly version of the name. Auto-generated but can be edited.",-1))])]),a("div",me,[e[24]||(e[24]=a("label",{class:"form-label"},"Description",-1)),g(a("textarea",{class:"form-textarea",placeholder:"Category description","onUpdate:modelValue":e[4]||(e[4]=l=>s.description=l),rows:"3"},null,512),[[u,s.description]])])])]),a("div",ce,[e[27]||(e[27]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-sitemap"}),r(" Category Hierarchy ")])],-1)),a("div",ge,[a("div",ue,[e[26]||(e[26]=a("label",{class:"form-label"},"Parent Category",-1)),i(G,{modelValue:s.parentId,"onUpdate:modelValue":e[5]||(e[5]=l=>s.parentId=l),label:"",placeholder:"Search for parent category (leave empty for top level)...","help-text":"Select a parent category or leave empty to make this a root category",onChange:E},null,8,["modelValue"])])])]),a("div",pe,[e[32]||(e[32]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-images"}),r(" Images ")])],-1)),a("div",fe,[a("div",ve,[e[28]||(e[28]=a("label",{class:"form-label"},"Category Image",-1)),i(J,{ref_key:"categoryImageManager",ref:b,"entity-type":"category","entity-id":null,"current-image":null,"image-alt":"Category Image","local-mode":!0,onImageUploaded:S,onImageRemoved:_,onImageChanged:T},null,512),e[29]||(e[29]=a("p",{class:"form-help"},"Upload an image to represent this category. Recommended size: 300x300 pixels.",-1))]),a("div",ye,[e[30]||(e[30]=a("label",{class:"form-label"},"Meta Image (SEO)",-1)),i(K,{ref_key:"metaImageManager",ref:C,"entity-type":"category","entity-id":null,"current-image":null,"social-preview-title":s.name||"Category Name","social-preview-description":s.description||"Category Description","social-preview-url":`https://marketplace.com/categories/${s.slug||"category-slug"}`,"local-mode":!0,onMetaImageUploaded:V,onMetaImageRemoved:O,onMetaImageChanged:N},null,8,["social-preview-title","social-preview-description","social-preview-url"]),e[31]||(e[31]=a("p",{class:"form-help"},"Upload a meta image for SEO and social media sharing.",-1))])])]),a("div",be,[e[37]||(e[37]=a("div",{class:"form-card-header"},[a("h3",{class:"form-card-title"},[a("i",{class:"fas fa-search"}),r(" SEO Settings ")])],-1)),a("div",Ce,[a("div",Ie,[a("div",he,[e[33]||(e[33]=a("label",{class:"form-label"},"Meta Title",-1)),g(a("input",{class:"form-input",type:"text",placeholder:"SEO title for search engines","onUpdate:modelValue":e[6]||(e[6]=l=>s.metaTitle=l),maxlength:"60"},null,512),[[u,s.metaTitle]]),a("p",we,[a("span",{class:U({"text-danger":s.metaTitle&&s.metaTitle.length>60})},y(s.metaTitle?s.metaTitle.length:0)+"/60 characters ",3)])]),a("div",xe,[e[34]||(e[34]=a("label",{class:"form-label"},"Display Order",-1)),g(a("input",{class:"form-input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":e[7]||(e[7]=l=>s.displayOrder=l)},null,512),[[u,s.displayOrder,void 0,{number:!0}]]),e[35]||(e[35]=a("p",{class:"form-help"},"Categories with lower numbers will be displayed first.",-1))])]),a("div",Ue,[e[36]||(e[36]=a("label",{class:"form-label"},"Meta Description",-1)),g(a("textarea",{class:"form-textarea",placeholder:"SEO description for search engines","onUpdate:modelValue":e[8]||(e[8]=l=>s.metaDescription=l),maxlength:"160",rows:"3"},null,512),[[u,s.metaDescription]]),a("p",De,[a("span",{class:U({"text-danger":s.metaDescription&&s.metaDescription.length>160})},y(s.metaDescription?s.metaDescription.length:0)+"/160 characters ",3)])])])]),a("div",Me,[a("button",{type:"submit",class:U(["action-btn action-btn-primary action-btn-large",{"action-btn-loading":d.value}]),disabled:d.value},[d.value?(p(),c("i",Ee)):(p(),c("i",Se)),r(" "+y(d.value?"Creating...":"Create Category"),1)],10,ke),i(o,{to:"/admin/categories",class:"action-btn action-btn-secondary action-btn-large"},{default:I(()=>e[38]||(e[38]=[a("i",{class:"fas fa-times"},null,-1),r(" Cancel ")])),_:1})]),v.value.length>0?(p(),c("div",_e,[i(Q,{uploads:v.value,onRetryUpload:P,onCancelUpload:R},null,8,["uploads"])])):x("",!0)],32)])}}},ze=F(Te,[["__scopeId","data-v-5035171f"]]);export{ze as default};
