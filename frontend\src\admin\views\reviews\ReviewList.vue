<template>
  <div class="review-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Reviews Management</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button
              class="button is-danger"
              @click="bulkDelete"
              :disabled="selectedReviews.length === 0 || actionLoading"
              v-if="selectedReviews.length > 0"
            >
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
              <span>Delete Selected ({{ selectedReviews.length }})</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Reviews"
      search-placeholder="Search by comment, product name, or user name..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="reviews"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading reviews...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchData">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Reviews Table -->
    <div class="admin-table-container" v-else>
      <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
        <table class="admin-table admin-table-striped">
            <thead>
              <tr>
                <th style="width: 50px;">
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="allSelected"
                      :indeterminate="someSelected"
                    />
                  </label>
                </th>
                <th style="width: 200px;">Product</th>
                <th style="width: 150px;">User</th>
                <th style="width: 120px;">Rating</th>
                <th style="width: 300px;">Comment</th>
                <th style="width: 120px;">Created</th>
                <th style="width: 150px;">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="review in reviews" :key="review.id" class="review-row">
                <td>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      :value="review.id"
                      v-model="selectedReviews"
                    />
                  </label>
                </td>
                <td>
                  <div class="product-info">
                    <strong class="product-name">{{ review.productName || 'Unknown Product' }}</strong>
                    <div class="product-slug" v-if="review.productSlug">
                      <small class="has-text-grey">{{ review.productSlug }}</small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="user-info">
                    <div class="user-name">{{ review.userName || 'Unknown User' }}</div>
                    <div class="user-email" v-if="review.userEmail">
                      <small class="has-text-grey">{{ review.userEmail }}</small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="rating-info">
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= Math.round(review.averageRating) }">
                        ★
                      </span>
                    </div>
                    <div class="rating-details">
                      <small class="has-text-grey">
                        {{ review.averageRating ? review.averageRating.toFixed(1) : '0.0' }}/5
                      </small>
                    </div>
                    <div class="rating-breakdown" v-if="review.serviceRating">
                      <small class="has-text-grey">
                        S:{{ review.serviceRating }} D:{{ review.deliveryTimeRating }} A:{{ review.accuracyRating }}
                      </small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="comment-content">
                    <span class="comment-text">
                      {{ truncateText(review.comment, 80) }}
                    </span>
                  </div>
                </td>
                <td>
                  <div class="date-info">
                    <div>{{ formatDate(review.createdAt) }}</div>
                    <div v-if="review.updatedAt && review.updatedAt !== review.createdAt">
                      <small class="has-text-grey">Updated: {{ formatDate(review.updatedAt) }}</small>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="buttons is-small">
                    <router-link
                      :to="{ name: 'AdminReviewDetail', params: { id: review.id } }"
                      class="button is-small is-info"
                      title="View Details">
                      <span class="icon">
                        <i class="fas fa-eye"></i>
                      </span>
                    </router-link>
                    <a
                      v-if="review.productSlug"
                      :href="`/products/${review.productSlug}`"
                      target="_blank"
                      class="button is-small is-link"
                      title="View Product">
                      <span class="icon">
                        <i class="fas fa-external-link-alt"></i>
                      </span>
                    </a>
                    <button
                      class="button is-small is-danger"
                      @click="deleteReview(review.id)"
                      :disabled="actionLoading"
                      title="Delete Review">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-changed="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import reviewsService from '@/admin/services/reviews';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import { useAdminSearch } from '@/composables/useAdminSearch';

// Filter configuration
const filterFields = [
  {
    key: 'minRating',
    label: 'Min Rating',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: '', label: 'Any' },
      { value: '1', label: '1+' },
      { value: '2', label: '2+' },
      { value: '3', label: '3+' },
      { value: '4', label: '4+' },
      { value: '5', label: '5' }
    ]
  },
  {
    key: 'maxRating',
    label: 'Max Rating',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: '', label: 'Any' },
      { value: '1', label: '1' },
      { value: '2', label: '2' },
      { value: '3', label: '3' },
      { value: '4', label: '4' },
      { value: '5', label: '5' }
    ]
  },
  {
    key: 'productSlug',
    label: 'Product',
    type: 'text',
    columnClass: 'is-2',
    placeholder: 'Product slug...'
  },
  {
    key: 'userEmail',
    label: 'User Email',
    type: 'text',
    columnClass: 'is-2',
    placeholder: 'User email...'
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: 'CreatedAt', label: 'Date Created' },
      { value: 'UpdatedAt', label: 'Date Updated' },
      { value: 'AverageRating', label: 'Rating' },
      { value: 'ProductName', label: 'Product' },
      { value: 'UserName', label: 'User' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-2',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items: reviews,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: reviewsService.getReviewsWithFilters,
  defaultFilters: {
    minRating: '',
    maxRating: '',
    productSlug: '',
    userEmail: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Additional reactive data
const actionLoading = ref(false);
const selectedReviews = ref([]);

// Computed
const allSelected = computed(() => {
  return reviews.value.length > 0 && selectedReviews.value.length === reviews.value.length;
});

const someSelected = computed(() => {
  return selectedReviews.value.length > 0 && selectedReviews.value.length < reviews.value.length;
});

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else {
      filters[key] = filterFields.find(f => f.key === key)?.options?.[0]?.value || '';
    }
  });
  fetchData(1);
};

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedReviews.value = [];
  } else {
    selectedReviews.value = reviews.value.map(review => review.id);
  }
};

const deleteReview = async (id) => {
  if (!confirm('Are you sure you want to delete this review?')) {
    return;
  }

  actionLoading.value = true;
  try {
    await reviewsService.deleteReview(id);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete review';
  } finally {
    actionLoading.value = false;
  }
};

const bulkDelete = async () => {
  if (!confirm(`Are you sure you want to delete ${selectedReviews.value.length} selected reviews?`)) {
    return;
  }

  actionLoading.value = true;
  try {
    await reviewsService.bulkDeleteReviews(selectedReviews.value);
    await fetchData(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete reviews';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
};

const truncateText = (text, length) => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

// Більше не потрібно, оскільки ми отримуємо averageRating безпосередньо з API
// Залишаємо для зворотної сумісності
const getAverageRating = (review) => {
  if (review.averageRating !== undefined) {
    return Math.round(review.averageRating);
  }

  if (!review.rating) return 0;
  // Calculate average from service, deliveryTime, and accuracy
  const { service = 0, deliveryTime = 0, accuracy = 0 } = review.rating;
  return Math.round((service + deliveryTime + accuracy) / 3);
};
</script>

<style scoped>
.review-list {
  padding: 1rem;
}

.title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.table th {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.table td {
  border-color: var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: rgba(50, 115, 220, 0.1);
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1.2em;
}

.star.is-filled {
  color: #ffd700;
}

.checkbox input[type="checkbox"] {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

.button.is-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.button.is-danger:hover {
  background-color: var(--danger-color-dark);
}

.button.is-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.button.is-info:hover {
  background-color: var(--info-color-dark);
}

.level {
  margin-bottom: 1.5rem;
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}

/* Покращені стилі для таблиці */
.review-row {
  transition: background-color 0.2s ease;
}

.review-row:hover {
  background-color: rgba(50, 115, 220, 0.1) !important;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-name {
  font-weight: 600;
  color: var(--text-primary);
}

.product-slug {
  font-size: 0.85em;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
}

.user-email {
  font-size: 0.85em;
}

.rating-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
}

.rating-details {
  font-size: 0.85em;
  font-weight: 500;
}

.rating-breakdown {
  font-size: 0.75em;
  line-height: 1.2;
}

.comment-content {
  max-width: 300px;
}

.comment-text {
  color: var(--text-secondary);
  line-height: 1.4;
  word-break: break-word;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.9em;
}

.buttons.is-small {
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
}

.button.is-link {
  background-color: var(--link-color);
  border-color: var(--link-color);
  color: white;
}

.button.is-link:hover {
  background-color: var(--link-color-dark);
}

/* Responsive design */
@media (max-width: 1200px) {
  .table-container {
    font-size: 0.9em;
  }

  .comment-content {
    max-width: 200px;
  }

  .rating-breakdown {
    display: none;
  }
}

@media (max-width: 768px) {
  .table-container {
    font-size: 0.8em;
  }

  .product-slug,
  .user-email,
  .rating-details {
    display: none;
  }

  .comment-content {
    max-width: 150px;
  }

  .buttons.is-small .button span:not(.icon) {
    display: none;
  }
}
</style>
