using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Image;

/// <summary>
/// Обробник універсальної команди оновлення зображення
/// </summary>
public class UpdateUniversalImageCommandHandler : IRequestHandler<UpdateUniversalImageCommand, FileUploadResult>
{
    private readonly IImageService _imageService;
    private readonly ILogger<UpdateUniversalImageCommandHandler> _logger;

    public UpdateUniversalImageCommandHandler(
        IImageService imageService,
        ILogger<UpdateUniversalImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UpdateUniversalImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Updating {request.ImageType} image for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.UpdateImageAsync(
                request.EntityType,
                request.EntityId,
                request.File,
                request.ImageType,
                request.ImageId,
                cancellationToken);

            _logger.LogInformation($"Successfully updated {request.ImageType} image for {request.EntityType} {request.EntityId}");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to update {request.ImageType} image for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
