import{_ as w,g as c,h as f,i as x,c as o,a as s,b as V,w as U,d as _,r as I,k as S,t as d,m as F,x as u,y as N,F as E,p as M,D as v,n as q,l as B,e as D,o as i}from"./index-L-hJxM_5.js";import{a as P}from"./addresses-DL07ASCS.js";import{u as z}from"./users-D6yG63l9.js";const L={class:"address-create"},R={class:"level"},T={class:"level-left"},j={class:"level-item"},O={class:"breadcrumb"},Y={class:"columns"},$={class:"column is-8"},G={class:"card"},H={class:"card-content"},J={key:0,class:"notification is-danger"},K={class:"field"},Q={class:"control"},W={class:"select is-fullwidth"},X=["value"],Z={class:"field"},ss={class:"control"},es={class:"field"},ts={class:"control"},as={class:"field"},ls={class:"control"},os={class:"field"},ds={class:"control"},is={class:"field is-grouped"},rs={class:"control"},ns=["disabled"],us={class:"control"},cs=["disabled"],vs={class:"column is-4"},ps={class:"card"},ms={class:"card-content"},bs={class:"content"},fs={class:"box has-background-light"},_s={key:0},gs={key:1,class:"has-text-grey"},ys={__name:"AddressCreate",setup(hs){const b=D(),r=c(!1),n=c(null),p=c([]),t=c({userId:"",region:"",city:"",street:"",postalCode:""}),g=f(()=>t.value.region.trim()&&t.value.city.trim()&&t.value.street.trim()),m=f(()=>t.value.userId?p.value.find(a=>a.id===t.value.userId):null),y=async()=>{try{const a=await z.getUsers({pageSize:1e3});p.value=a.data||[]}catch(a){console.error("Failed to load users:",a)}},h=async()=>{r.value=!0,n.value=null;try{const a={userId:t.value.userId||null,region:t.value.region.trim(),city:t.value.city.trim(),street:t.value.street.trim(),postalCode:t.value.postalCode.trim()||null};await P.createAddress(a),b.push({name:"AdminAddresses"})}catch(a){n.value=a.message||"Failed to create address"}finally{r.value=!1}},C=()=>{b.push({name:"AdminAddresses"})},A=()=>{const a=[];return t.value.street.trim()&&a.push(t.value.street.trim()),t.value.city.trim()&&a.push(t.value.city.trim()),t.value.region.trim()&&a.push(t.value.region.trim()),t.value.postalCode.trim()&&a.push(t.value.postalCode.trim()),a.length>0?a.join(", "):"Enter address details..."};return x(()=>{y()}),(a,e)=>{const k=I("router-link");return i(),o("div",L,[s("div",R,[s("div",T,[s("div",j,[s("nav",O,[s("ul",null,[s("li",null,[V(k,{to:"/admin/addresses"},{default:U(()=>e[6]||(e[6]=[_("Addresses")])),_:1})]),e[7]||(e[7]=s("li",{class:"is-active"},[s("a",null,"Create Address")],-1))])])])])]),s("div",Y,[s("div",$,[s("div",G,[e[17]||(e[17]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Create New Address")],-1)),s("div",H,[n.value?(i(),o("div",J,[s("button",{class:"delete",onClick:e[0]||(e[0]=l=>n.value=null)}),_(" "+d(n.value),1)])):S("",!0),s("form",{onSubmit:F(h,["prevent"])},[s("div",K,[e[9]||(e[9]=s("label",{class:"label"},"User (Optional)",-1)),s("div",Q,[s("div",W,[u(s("select",{"onUpdate:modelValue":e[1]||(e[1]=l=>t.value.userId=l)},[e[8]||(e[8]=s("option",{value:""},"Select a user (optional)",-1)),(i(!0),o(E,null,M(p.value,l=>(i(),o("option",{key:l.id,value:l.id},d(l.name)+" ("+d(l.email)+") ",9,X))),128))],512),[[N,t.value.userId]])])]),e[10]||(e[10]=s("p",{class:"help"},"Leave empty to create an unassigned address",-1))]),s("div",Z,[e[11]||(e[11]=s("label",{class:"label"},"Region *",-1)),s("div",ss,[u(s("input",{class:"input",type:"text","onUpdate:modelValue":e[2]||(e[2]=l=>t.value.region=l),placeholder:"Enter region",required:""},null,512),[[v,t.value.region]])])]),s("div",es,[e[12]||(e[12]=s("label",{class:"label"},"City *",-1)),s("div",ts,[u(s("input",{class:"input",type:"text","onUpdate:modelValue":e[3]||(e[3]=l=>t.value.city=l),placeholder:"Enter city",required:""},null,512),[[v,t.value.city]])])]),s("div",as,[e[13]||(e[13]=s("label",{class:"label"},"Street *",-1)),s("div",ls,[u(s("input",{class:"input",type:"text","onUpdate:modelValue":e[4]||(e[4]=l=>t.value.street=l),placeholder:"Enter street address",required:""},null,512),[[v,t.value.street]])])]),s("div",os,[e[14]||(e[14]=s("label",{class:"label"},"Postal Code",-1)),s("div",ds,[u(s("input",{class:"input",type:"text","onUpdate:modelValue":e[5]||(e[5]=l=>t.value.postalCode=l),placeholder:"Enter postal code"},null,512),[[v,t.value.postalCode]])])]),s("div",is,[s("div",rs,[s("button",{type:"submit",class:q(["button is-primary",{"is-loading":r.value}]),disabled:r.value||!g.value},e[15]||(e[15]=[s("span",{class:"icon"},[s("i",{class:"fas fa-save"})],-1),s("span",null,"Create Address",-1)]),10,ns)]),s("div",us,[s("button",{type:"button",class:"button",onClick:C,disabled:r.value},e[16]||(e[16]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Cancel",-1)]),8,cs)])])],32)])])]),s("div",vs,[s("div",ps,[e[20]||(e[20]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Address Preview")],-1)),s("div",ms,[s("div",bs,[e[18]||(e[18]=s("p",null,[s("strong",null,"Full Address:")],-1)),s("div",fs,[s("p",null,d(A()),1)]),e[19]||(e[19]=s("p",null,[s("strong",null,"Assigned User:")],-1)),m.value?(i(),o("p",_s,d(m.value.name)+" ("+d(m.value.email)+") ",1)):(i(),o("p",gs,"No user assigned"))])])]),e[21]||(e[21]=B('<div class="card mt-4" data-v-29aba0bf><div class="card-header" data-v-29aba0bf><p class="card-header-title" data-v-29aba0bf>Instructions</p></div><div class="card-content" data-v-29aba0bf><div class="content" data-v-29aba0bf><ul data-v-29aba0bf><li data-v-29aba0bf>Fields marked with * are required</li><li data-v-29aba0bf>You can create an address without assigning it to a user</li><li data-v-29aba0bf>Postal code is optional but recommended</li><li data-v-29aba0bf>Make sure the address information is accurate</li></ul></div></div></div>',1))])])])}}},ws=w(ys,[["__scopeId","data-v-29aba0bf"]]);export{ws as default};
