import{q as B,_ as ls,g as h,H as ns,c as n,o as i,a as s,k as E,x as w,D as M,F as Z,p as ss,n as T,t as c,d as g,C as es,h as os,f as vs,i as fs,z as ys,b as x,w as q,r as gs,m as Y,y as Q,l as as,e as hs}from"./index-L-hJxM_5.js";import{o as J,a as bs,c as _s}from"./orders-DGdj4xZm.js";import{p as ks}from"./products-Bpq90UOX.js";const Ss={async getShippingMethods(P={}){var y,v;try{return(await B.get("/api/admin/shipping-methods",{params:P})).data}catch(d){throw console.error("Error fetching shipping methods:",d),new Error(((v=(y=d.response)==null?void 0:y.data)==null?void 0:v.message)||"Failed to load shipping methods")}},async getShippingMethodById(P){var y,v;try{return(await B.get(`/api/admin/shipping-methods/${P}`)).data}catch(d){throw console.error("Error fetching shipping method:",d),new Error(((v=(y=d.response)==null?void 0:y.data)==null?void 0:v.message)||"Failed to load shipping method details")}},async createShippingMethod(P){var y,v;try{return(await B.post("/api/admin/shipping-methods",P)).data}catch(d){throw console.error("Error creating shipping method:",d),new Error(((v=(y=d.response)==null?void 0:y.data)==null?void 0:v.message)||"Failed to create shipping method")}},async updateShippingMethod(P,y){var v,d;try{return(await B.put(`/api/admin/shipping-methods/${P}`,y)).data}catch(b){throw console.error("Error updating shipping method:",b),new Error(((d=(v=b.response)==null?void 0:v.data)==null?void 0:d.message)||"Failed to update shipping method")}},async deleteShippingMethod(P){var y,v;try{return(await B.delete(`/api/admin/shipping-methods/${P}`)).data}catch(d){throw console.error("Error deleting shipping method:",d),new Error(((v=(y=d.response)==null?void 0:y.data)==null?void 0:v.message)||"Failed to delete shipping method")}}},Cs={class:"product-search-selector"},ws={class:"search-input-wrapper"},Ps={class:"search-input-group"},$s={key:0,class:"search-results"},As={key:0,class:"search-loading"},Is={key:1,class:"no-results"},Es={key:2,class:"results-list"},Os=["onClick"],Us={key:0,class:"product-image"},xs=["src","alt"],Ms={key:1,class:"product-placeholder"},Vs={class:"product-info"},Ns={class:"product-name"},qs={key:0,class:"product-sku"},Ts={class:"product-price"},Fs={class:"product-actions"},Ls={key:0,class:"fas fa-check"},Ds={key:1,class:"fas fa-plus"},Rs={key:3,class:"search-pagination"},Bs=["disabled"],Qs={class:"pagination-info"},zs=["disabled"],Hs={key:1,class:"selected-product"},Ks={class:"selected-item"},js={key:0,class:"product-image"},Gs=["src","alt"],Js={key:1,class:"product-placeholder"},Ws={class:"product-info"},Xs={class:"product-name"},Ys={key:0,class:"product-sku"},Zs={class:"product-price"},st=10,tt={__name:"ProductSearchSelector",props:{modelValue:{type:Object,default:null}},emits:["update:modelValue","product-selected"],setup(P,{emit:y}){const v=P,d=y,b=h(""),S=h(!1),$=h(!1),u=h([]),p=h(v.modelValue),A=h(1),O=h(1);let V=null;const k=()=>{clearTimeout(V),V=setTimeout(()=>{b.value.trim()?I():u.value=[]},300)},I=async(r=1)=>{if(b.value.trim()){S.value=!0;try{const l=await ks.searchProducts({query:b.value.trim(),page:r,pageSize:st});u.value=l.data||[],A.value=l.currentPage||1,O.value=l.totalPages||1}catch(l){console.error("Error searching products:",l),u.value=[]}finally{S.value=!1}}},U=r=>{p.value=r,d("update:modelValue",r),d("product-selected",r),$.value=!1},a=()=>{p.value=null,d("update:modelValue",null),d("product-selected",null)},C=()=>{b.value="",u.value=[],$.value=!1},z=r=>{r>=1&&r<=O.value&&(A.value=r,I(r))},D=r=>r==null?"N/A":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(r),F=r=>r===0?"stock-danger":r<10?"stock-warning":"stock-success",W=r=>{r.target.style.display="none"};ns(()=>v.modelValue,r=>{p.value=r});const X=r=>{r.target.closest(".product-search-selector")||($.value=!1)};return typeof window<"u"&&document.addEventListener("click",X),(r,l)=>(i(),n("div",Cs,[l[14]||(l[14]=s("div",{class:"search-header"},[s("h4",{class:"search-title"},"Search Products"),s("p",{class:"search-description"},"Search and select a product to add to the order")],-1)),s("div",ws,[s("div",Ps,[w(s("input",{class:"search-input",type:"text",placeholder:"Search products by name, SKU, or description...","onUpdate:modelValue":l[0]||(l[0]=m=>b.value=m),onInput:k,onFocus:l[1]||(l[1]=m=>$.value=!0)},null,544),[[M,b.value]]),l[4]||(l[4]=s("div",{class:"search-input-icon"},[s("i",{class:"fas fa-search"})],-1))]),b.value?(i(),n("button",{key:0,class:"search-clear-btn",onClick:C,title:"Clear search"},l[5]||(l[5]=[s("i",{class:"fas fa-times"},null,-1)]))):E("",!0)]),$.value&&b.value?(i(),n("div",$s,[S.value?(i(),n("div",As,l[6]||(l[6]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Searching products...",-1)]))):u.value.length===0?(i(),n("div",Is,l[7]||(l[7]=[s("div",{class:"no-results-icon"},[s("i",{class:"fas fa-search"})],-1),s("h4",{class:"no-results-title"},"No Products Found",-1),s("p",{class:"no-results-message"},"Try adjusting your search terms",-1)]))):(i(),n("div",Es,[(i(!0),n(Z,null,ss(u.value,m=>{var L,H,K;return i(),n("div",{key:m.id,class:T(["result-item",{"result-item-selected":((L=p.value)==null?void 0:L.id)===m.id}]),onClick:ts=>U(m)},[m.image?(i(),n("div",Us,[s("img",{src:m.image,alt:m.name,onError:W},null,40,xs)])):(i(),n("div",Ms,l[8]||(l[8]=[s("i",{class:"fas fa-box"},null,-1)]))),s("div",Vs,[s("h5",Ns,c(m.name),1),m.sku?(i(),n("p",qs,"SKU: "+c(m.sku),1)):E("",!0),s("p",Ts,c(D(m.price)),1),s("p",{class:T(["product-stock",F(m.stock)])}," Stock: "+c(m.stock||0),3)]),s("div",Fs,[s("button",{class:T(["select-btn",{"select-btn-selected":((H=p.value)==null?void 0:H.id)===m.id}])},[((K=p.value)==null?void 0:K.id)===m.id?(i(),n("i",Ls)):(i(),n("i",Ds))],2)])],10,Os)}),128))])),u.value.length>0&&O.value>1?(i(),n("div",Rs,[s("button",{class:"pagination-btn",disabled:A.value<=1,onClick:l[2]||(l[2]=m=>z(A.value-1))},l[9]||(l[9]=[s("i",{class:"fas fa-chevron-left"},null,-1),g(" Previous ")]),8,Bs),s("span",Qs," Page "+c(A.value)+" of "+c(O.value),1),s("button",{class:"pagination-btn",disabled:A.value>=O.value,onClick:l[3]||(l[3]=m=>z(A.value+1))},l[10]||(l[10]=[g(" Next "),s("i",{class:"fas fa-chevron-right"},null,-1)]),8,zs)])):E("",!0)])):E("",!0),p.value?(i(),n("div",Hs,[l[13]||(l[13]=s("h4",{class:"selected-title"},"Selected Product",-1)),s("div",Ks,[p.value.image?(i(),n("div",js,[s("img",{src:p.value.image,alt:p.value.name},null,8,Gs)])):(i(),n("div",Js,l[11]||(l[11]=[s("i",{class:"fas fa-box"},null,-1)]))),s("div",Ws,[s("h5",Xs,c(p.value.name),1),p.value.sku?(i(),n("p",Ys,"SKU: "+c(p.value.sku),1)):E("",!0),s("p",Zs,c(D(p.value.price)),1)]),s("button",{class:"remove-btn",onClick:a,title:"Remove selection"},l[12]||(l[12]=[s("i",{class:"fas fa-times"},null,-1)]))])])):E("",!0)]))}},et=ls(tt,[["__scopeId","data-v-fd5e4443"]]),ot={class:"order-edit-new"},at={key:0,class:"loading-state"},lt={key:1,class:"error-state"},nt={class:"error-message"},it={key:2,class:"not-found-state"},rt={key:3,class:"edit-content"},dt={class:"edit-header"},ut={class:"header-content"},ct={class:"breadcrumb"},pt={class:"edit-title"},mt={class:"header-actions"},vt={key:0,class:"message message-error"},ft={class:"message-content"},yt={key:1,class:"message message-success"},gt={class:"message-content"},ht={class:"form-card"},bt={class:"form-card-content"},_t={class:"form-grid"},kt={class:"form-field"},St=["value"],Ct={class:"form-field"},wt=["value"],Pt={class:"form-grid"},$t={class:"form-field"},At={class:"form-field"},It={class:"form-grid"},Et={class:"form-field"},Ot=["value"],Ut={class:"form-field"},xt=["value"],Mt={class:"form-card"},Vt={class:"form-card-content"},Nt={class:"form-grid"},qt={class:"form-field"},Tt=["value"],Ft={class:"form-field"},Lt=["value"],Dt={class:"form-card"},Rt={class:"form-card-content"},Bt={class:"form-grid"},Qt={class:"form-field"},zt={class:"form-field"},Ht={class:"form-grid"},Kt={class:"form-field"},jt={class:"form-field"},Gt=["value"],Jt={class:"form-field"},Wt={class:"form-card"},Xt={class:"form-card-content"},Yt={class:"form-field"},Zt={class:"form-card"},se={class:"form-card-header"},te={class:"form-card-title"},ee={class:"form-card-actions"},oe={class:"form-card-content"},ae={key:0,class:"items-loading"},le={key:1,class:"no-items"},ne={key:2,class:"items-table-wrapper"},ie={class:"items-table"},re={class:"table"},de={class:"product-cell"},ue={class:"product-info"},ce={key:0,class:"product-image"},pe=["src","alt"],me={class:"product-details"},ve={class:"product-name"},fe={class:"product-id"},ye={class:"price-cell"},ge={class:"price-display"},he={class:"quantity-cell"},be={class:"quantity-controls"},_e=["onClick","disabled"],ke=["onUpdate:modelValue","onInput"],Se=["onClick"],Ce={class:"total-cell"},we={class:"total-price"},Pe={class:"actions-cell"},$e={class:"action-buttons"},Ae=["onClick"],Ie={class:"order-total"},Ee={class:"total-row"},Oe={class:"total-value"},Ue={class:"form-actions"},xe=["disabled"],Me={key:0,class:"fas fa-spinner fa-spin"},Ve={key:1,class:"fas fa-save"},Ne={class:"modal-content modal-content-large"},qe={class:"modal-body"},Te={key:0,class:"selected-product-info"},Fe={class:"product-preview"},Le={key:0,class:"product-image"},De=["src","alt"],Re={class:"product-details"},Be={class:"form-field"},Qe={class:"modal-footer"},ze=["disabled"],He={class:"modal-content"},Ke={class:"modal-body"},je={class:"form-field"},Ge={class:"form-field"},Je={class:"modal-footer"},We=["disabled"],Xe={__name:"OrderEditNew",setup(P){const y=vs();hs();const v=h(!1),d=h(!1),b=h(!1),S=h(""),$=h(""),u=h(null),p=h([]),A=h([]),O=h(!1),V=h(!1),k=h(null),I=h(1),U=h(!1),a=es({status:"",paymentStatus:"",totalPrice:0,notes:"",shippingAddressLine:"",shippingCountry:"",shippingCity:"",shippingMethodId:"",postalCode:""}),C=es({status:"",paymentStatus:""}),z=os(()=>{var e,t,_;return((e=u.value)==null?void 0:e.customerName)||((_=(t=u.value)==null?void 0:t.customer)==null?void 0:_.name)||"Unknown Customer"}),D=os(()=>y.params.id),F=async e=>{var t,_,N,o;v.value=!0,S.value="";try{const f=await J.getById(e);u.value=f,a.status=bs(f.status).toLowerCase()||"processing",a.paymentStatus=_s(f.paymentStatus).toLowerCase()||"pending",a.totalPrice=f.totalPriceAmount||((t=f.totalPrice)==null?void 0:t.amount)||0,a.notes=f.notes||"",a.shippingAddressLine=((_=f.shippingAddress)==null?void 0:_.address1)||f.shippingAddressLine||"",a.shippingCountry=((N=f.shippingAddress)==null?void 0:N.country)||f.shippingCountry||"",a.shippingCity=((o=f.shippingAddress)==null?void 0:o.city)||f.shippingCity||"",a.shippingMethodId=f.shippingMethodId||"",a.postalCode=f.postalCode||"",C.status=a.status,C.paymentStatus=a.paymentStatus,await X(f.id)}catch(f){console.error("Error fetching order:",f),f.name!=="CanceledError"&&f.code!=="ERR_CANCELED"&&(S.value="Failed to load order. Please try again.")}finally{v.value=!1}},W=async()=>{try{const e=await Ss.getShippingMethods({pageSize:100});A.value=e.data||[]}catch(e){console.error("Error fetching shipping methods:",e),A.value=[]}},X=async e=>{d.value=!0;try{const t=await J.getOrderItems(e);p.value=t.data||t||[]}catch(t){console.error("Error fetching order items:",t),p.value=[]}finally{d.value=!1}},r=e=>e==null?"N/A":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(e),l=e=>e?new Date(e).toLocaleString("uk-UA"):"N/A",m=()=>p.value.reduce((e,t)=>{const _=t.price||t.priceAmount||0,N=t.quantity||0;return e+_*N},0),L=e=>{const t=e.price||e.priceAmount||0;e.total=t*(e.quantity||0),a.totalPrice=m()},H=e=>{e.quantity=(e.quantity||0)+1,L(e)},K=e=>{e.quantity>1&&(e.quantity=e.quantity-1,L(e))},ts=e=>{confirm("Are you sure you want to remove this item from the order?")&&(p.value.splice(e,1),a.totalPrice=m())},is=e=>{k.value=e,I.value=1},rs=()=>{if(!k.value||!I.value)return;const e=k.value.price||0,t={id:Date.now(),productId:k.value.id,productName:k.value.name,price:e,priceAmount:e,quantity:I.value,total:e*I.value};p.value.push(t),a.totalPrice=m(),j()},ds=()=>{console.log("Opening add product modal..."),O.value=!0,console.log("showAddProductModal.value:",O.value)},j=()=>{O.value=!1,k.value=null,I.value=1},us=()=>{console.log("Opening update status modal..."),C.status=a.status,C.paymentStatus=a.paymentStatus,V.value=!0,console.log("showUpdateStatusModal.value:",V.value)},G=()=>{V.value=!1,U.value=!1},cs=async()=>{var e,t;if(!U.value)try{U.value=!0,S.value="",await J.updateOrderStatus(y.params.id,{status:C.status,paymentStatus:C.paymentStatus}),a.status=C.status,a.paymentStatus=C.paymentStatus,u.value&&(u.value.status=C.status,u.value.paymentStatus=C.paymentStatus),$.value="Order status updated successfully!",G()}catch(_){console.error("Error updating order status:",_),S.value=((t=(e=_.response)==null?void 0:e.data)==null?void 0:t.message)||"Failed to update order status"}finally{U.value=!1}},ps=e=>{e.target.style.display="none"},ms=async()=>{b.value=!0,S.value="",$.value="";try{const e={status:a.status,paymentStatus:a.paymentStatus,shippingAddress:a.shippingAddressLine,shippingMethodId:a.shippingMethodId,postalCode:a.postalCode,notes:a.notes,items:p.value.map(t=>({id:t.id,quantity:t.quantity,priceAmount:t.price||t.priceAmount||0,priceCurrency:"UAH"}))};await J.update(D.value,e),$.value="Order updated successfully!",setTimeout(async()=>{await F(D.value)},100)}catch(e){console.error("Error updating order:",e),e.response&&e.response.data&&e.response.data.message?S.value=e.response.data.message:S.value="Failed to update order. Please try again."}finally{b.value=!1}};return ns(()=>y.params.id,e=>{e&&F(e)},{immediate:!0}),fs(async()=>{await W();const e=y.params.id;e&&F(e)}),(e,t)=>{var N;const _=gs("router-link");return i(),n("div",ot,[v.value?(i(),n("div",at,t[15]||(t[15]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Loading order...",-1)]))):S.value&&!u.value?(i(),n("div",lt,[t[17]||(t[17]=s("div",{class:"error-icon"},[s("i",{class:"fas fa-exclamation-triangle"})],-1)),t[18]||(t[18]=s("h3",{class:"error-title"},"Error Loading Order",-1)),s("p",nt,c(S.value),1),s("button",{class:"retry-btn",onClick:t[0]||(t[0]=o=>F(ys(y).params.id))},t[16]||(t[16]=[s("i",{class:"fas fa-redo"},null,-1),g(" Try Again ")]))])):!u.value&&!v.value?(i(),n("div",it,[t[20]||(t[20]=s("div",{class:"not-found-icon"},[s("i",{class:"fas fa-shopping-cart"})],-1)),t[21]||(t[21]=s("h3",{class:"not-found-title"},"Order Not Found",-1)),t[22]||(t[22]=s("p",{class:"not-found-message"},"The requested order does not exist or has been deleted.",-1)),x(_,{to:"/admin/orders",class:"back-btn"},{default:q(()=>t[19]||(t[19]=[s("i",{class:"fas fa-arrow-left"},null,-1),g(" Back to Orders ")])),_:1})])):(i(),n("div",rt,[s("div",dt,[s("div",ut,[s("nav",ct,[x(_,{to:"/admin",class:"breadcrumb-item"},{default:q(()=>t[23]||(t[23]=[g("Dashboard")])),_:1}),t[25]||(t[25]=s("span",{class:"breadcrumb-separator"},"/",-1)),x(_,{to:"/admin/orders",class:"breadcrumb-item"},{default:q(()=>t[24]||(t[24]=[g("Orders")])),_:1}),t[26]||(t[26]=s("span",{class:"breadcrumb-separator"},"/",-1)),x(_,{to:`/admin/orders/${u.value.id}`,class:"breadcrumb-item"},{default:q(()=>[g("Order #"+c(u.value.id),1)]),_:1},8,["to"]),t[27]||(t[27]=s("span",{class:"breadcrumb-separator"},"/",-1)),t[28]||(t[28]=s("span",{class:"breadcrumb-item breadcrumb-current"},"Edit",-1))]),s("h1",pt,"Edit Order #"+c(u.value.id),1),t[29]||(t[29]=s("p",{class:"edit-subtitle"},"Update order information and manage items",-1))]),s("div",mt,[s("button",{type:"button",class:"action-btn action-btn-primary",onClick:Y(us,["prevent"])},t[30]||(t[30]=[s("i",{class:"fas fa-edit"},null,-1),g(" Update Status ")])),x(_,{to:`/admin/orders/${u.value.id}`,class:"action-btn action-btn-secondary"},{default:q(()=>t[31]||(t[31]=[s("i",{class:"fas fa-eye"},null,-1),g(" View Order ")])),_:1},8,["to"]),x(_,{to:"/admin/orders",class:"action-btn action-btn-light"},{default:q(()=>t[32]||(t[32]=[s("i",{class:"fas fa-arrow-left"},null,-1),g(" Back to Orders ")])),_:1})])]),S.value?(i(),n("div",vt,[s("div",ft,[t[33]||(t[33]=s("i",{class:"fas fa-exclamation-circle"},null,-1)),s("span",null,c(S.value),1)]),s("button",{class:"message-close",onClick:t[1]||(t[1]=o=>S.value="")},t[34]||(t[34]=[s("i",{class:"fas fa-times"},null,-1)]))])):E("",!0),$.value?(i(),n("div",yt,[s("div",gt,[t[35]||(t[35]=s("i",{class:"fas fa-check-circle"},null,-1)),s("span",null,c($.value),1)]),s("button",{class:"message-close",onClick:t[2]||(t[2]=o=>$.value="")},t[36]||(t[36]=[s("i",{class:"fas fa-times"},null,-1)]))])):E("",!0),s("form",{onSubmit:Y(ms,["prevent"]),class:"edit-form"},[s("div",ht,[t[46]||(t[46]=s("div",{class:"form-card-header"},[s("h3",{class:"form-card-title"},[s("i",{class:"fas fa-info-circle"}),g(" Order Information ")])],-1)),s("div",bt,[s("div",_t,[s("div",kt,[t[37]||(t[37]=s("label",{class:"form-label"},"Order ID",-1)),s("input",{class:"form-input form-input-readonly",type:"text",value:u.value.id,readonly:""},null,8,St)]),s("div",Ct,[t[38]||(t[38]=s("label",{class:"form-label"},"Customer",-1)),s("input",{class:"form-input form-input-readonly",type:"text",value:z.value,readonly:""},null,8,wt)])]),s("div",Pt,[s("div",$t,[t[40]||(t[40]=s("label",{class:"form-label"},"Order Status",-1)),w(s("select",{"onUpdate:modelValue":t[3]||(t[3]=o=>a.status=o),class:"form-select"},t[39]||(t[39]=[as('<option value="processing" data-v-5886ec0f>Processing</option><option value="pending" data-v-5886ec0f>Pending</option><option value="shipped" data-v-5886ec0f>Shipped</option><option value="delivered" data-v-5886ec0f>Delivered</option><option value="cancelled" data-v-5886ec0f>Cancelled</option>',5)]),512),[[Q,a.status]])]),s("div",At,[t[42]||(t[42]=s("label",{class:"form-label"},"Payment Status",-1)),w(s("select",{"onUpdate:modelValue":t[4]||(t[4]=o=>a.paymentStatus=o),class:"form-select"},t[41]||(t[41]=[s("option",{value:"pending"},"Pending",-1),s("option",{value:"completed"},"Completed",-1),s("option",{value:"refunded"},"Refunded",-1),s("option",{value:"failed"},"Failed",-1)]),512),[[Q,a.paymentStatus]])])]),s("div",It,[s("div",Et,[t[43]||(t[43]=s("label",{class:"form-label"},"Total Price",-1)),s("input",{class:"form-input form-input-readonly",type:"text",value:r(u.value.totalPriceAmount||((N=u.value.totalPrice)==null?void 0:N.amount)||0),readonly:""},null,8,Ot),t[44]||(t[44]=s("small",{class:"field-note"},"Total price is calculated automatically from order items",-1))]),s("div",Ut,[t[45]||(t[45]=s("label",{class:"form-label"},"Created At",-1)),s("input",{class:"form-input form-input-readonly",type:"text",value:l(u.value.createdAt),readonly:""},null,8,xt)])])])]),s("div",Mt,[t[49]||(t[49]=s("div",{class:"form-card-header"},[s("h3",{class:"form-card-title"},[s("i",{class:"fas fa-user"}),g(" Customer Information ")])],-1)),s("div",Vt,[s("div",Nt,[s("div",qt,[t[47]||(t[47]=s("label",{class:"form-label"},"Customer Name",-1)),s("input",{class:"form-input form-input-readonly",type:"text",value:u.value.customerName||"N/A",readonly:""},null,8,Tt)]),s("div",Ft,[t[48]||(t[48]=s("label",{class:"form-label"},"Customer Email",-1)),s("input",{class:"form-input form-input-readonly",type:"text",value:u.value.customerEmail||"N/A",readonly:""},null,8,Lt)])])])]),s("div",Dt,[t[56]||(t[56]=s("div",{class:"form-card-header"},[s("h3",{class:"form-card-title"},[s("i",{class:"fas fa-shipping-fast"}),g(" Shipping Information ")])],-1)),s("div",Rt,[s("div",Bt,[s("div",Qt,[t[50]||(t[50]=s("label",{class:"form-label"},"Address Line",-1)),w(s("input",{class:"form-input",type:"text","onUpdate:modelValue":t[5]||(t[5]=o=>a.shippingAddressLine=o),placeholder:"Enter shipping address"},null,512),[[M,a.shippingAddressLine]])]),s("div",zt,[t[51]||(t[51]=s("label",{class:"form-label"},"Country",-1)),w(s("input",{class:"form-input",type:"text","onUpdate:modelValue":t[6]||(t[6]=o=>a.shippingCountry=o),placeholder:"Enter country"},null,512),[[M,a.shippingCountry]])])]),s("div",Ht,[s("div",Kt,[t[52]||(t[52]=s("label",{class:"form-label"},"City",-1)),w(s("input",{class:"form-input",type:"text","onUpdate:modelValue":t[7]||(t[7]=o=>a.shippingCity=o),placeholder:"Enter city"},null,512),[[M,a.shippingCity]])]),s("div",jt,[t[54]||(t[54]=s("label",{class:"form-label"},"Shipping Method",-1)),w(s("select",{"onUpdate:modelValue":t[8]||(t[8]=o=>a.shippingMethodId=o),class:"form-select"},[t[53]||(t[53]=s("option",{value:""},"Select shipping method",-1)),(i(!0),n(Z,null,ss(A.value,o=>(i(),n("option",{key:o.id,value:o.id},c(o.name)+" - "+c(o.priceAmount)+" "+c(o.priceCurrency)+" ("+c(o.estimatedDays)+" days) ",9,Gt))),128))],512),[[Q,a.shippingMethodId]])])]),s("div",Jt,[t[55]||(t[55]=s("label",{class:"form-label"},"Postal Code",-1)),w(s("input",{class:"form-input",type:"text","onUpdate:modelValue":t[9]||(t[9]=o=>a.postalCode=o),placeholder:"Enter postal code"},null,512),[[M,a.postalCode]])])])]),s("div",Wt,[t[59]||(t[59]=s("div",{class:"form-card-header"},[s("h3",{class:"form-card-title"},[s("i",{class:"fas fa-sticky-note"}),g(" Order Notes ")])],-1)),s("div",Xt,[s("div",Yt,[t[57]||(t[57]=s("label",{class:"form-label"},"Notes",-1)),w(s("textarea",{"onUpdate:modelValue":t[10]||(t[10]=o=>a.notes=o),class:"form-textarea",placeholder:"Add notes about this order...",rows:"4"},"              ",512),[[M,a.notes]]),t[58]||(t[58]=s("small",{class:"field-note"},"Internal notes about this order (not visible to customer)",-1))])])]),s("div",Zt,[s("div",se,[s("h3",te,[t[60]||(t[60]=s("i",{class:"fas fa-box"},null,-1)),g(" Order Items ("+c(p.value.length)+") ",1)]),s("div",ee,[s("button",{type:"button",class:"action-btn action-btn-primary action-btn-sm",onClick:Y(ds,["prevent"])},t[61]||(t[61]=[s("i",{class:"fas fa-plus"},null,-1),g(" Add Product ")]))])]),s("div",oe,[d.value?(i(),n("div",ae,t[62]||(t[62]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Loading order items...",-1)]))):p.value.length===0?(i(),n("div",le,t[63]||(t[63]=[s("div",{class:"no-items-icon"},[s("i",{class:"fas fa-box-open"})],-1),s("h4",{class:"no-items-title"},"No Items in Order",-1),s("p",{class:"no-items-message"},"Add products to this order using the button above.",-1)]))):(i(),n("div",ne,[s("div",ie,[s("table",re,[t[68]||(t[68]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Unit Price"),s("th",null,"Quantity"),s("th",null,"Total Price"),s("th",null,"Actions")])],-1)),s("tbody",null,[(i(!0),n(Z,null,ss(p.value,(o,f)=>(i(),n("tr",{key:o.id||f,class:"table-row"},[s("td",de,[s("div",ue,[o.productImage?(i(),n("div",ce,[s("img",{src:o.productImage,alt:o.productName,onError:ps},null,40,pe)])):E("",!0),s("div",me,[s("h4",ve,c(o.productName||"Unknown Product"),1),s("p",fe,"Product ID: "+c(o.productId),1)])])]),s("td",ye,[s("span",ge,c(r(o.price||o.priceAmount||0)),1),t[64]||(t[64]=s("small",{class:"price-note"},"Price is set in product",-1))]),s("td",he,[s("div",be,[s("button",{type:"button",class:"quantity-btn quantity-btn-minus",onClick:R=>K(o),disabled:o.quantity<=1},t[65]||(t[65]=[s("i",{class:"fas fa-minus"},null,-1)]),8,_e),w(s("input",{class:"quantity-input",type:"number",min:"1","onUpdate:modelValue":R=>o.quantity=R,onInput:R=>L(o)},null,40,ke),[[M,o.quantity,void 0,{number:!0}]]),s("button",{type:"button",class:"quantity-btn quantity-btn-plus",onClick:R=>H(o)},t[66]||(t[66]=[s("i",{class:"fas fa-plus"},null,-1)]),8,Se)])]),s("td",Ce,[s("span",we,c(r(o.total||o.price*o.quantity||0)),1)]),s("td",Pe,[s("div",$e,[s("button",{type:"button",class:"action-btn action-btn-sm action-btn-danger",onClick:R=>ts(f),title:"Remove Item"},t[67]||(t[67]=[s("i",{class:"fas fa-trash"},null,-1)]),8,Ae)])])]))),128))])])]),s("div",Ie,[s("div",Ee,[t[69]||(t[69]=s("span",{class:"total-label"},"Order Total:",-1)),s("span",Oe,c(r(m())),1)])])]))])]),s("div",Ue,[s("button",{type:"submit",class:T(["action-btn action-btn-primary action-btn-large",{"action-btn-loading":b.value}]),disabled:b.value},[b.value?(i(),n("i",Me)):(i(),n("i",Ve)),g(" "+c(b.value?"Saving...":"Save Changes"),1)],10,xe),x(_,{to:`/admin/orders/${u.value.id}`,class:"action-btn action-btn-secondary action-btn-large"},{default:q(()=>t[70]||(t[70]=[s("i",{class:"fas fa-times"},null,-1),g(" Cancel ")])),_:1},8,["to"])])],32)])),s("div",{class:T(["modal",{"modal-active":O.value}])},[s("div",{class:"modal-backdrop",onClick:j}),s("div",Ne,[s("div",{class:"modal-header"},[t[72]||(t[72]=s("h3",{class:"modal-title"},"Add Product to Order",-1)),s("button",{class:"modal-close",onClick:j},t[71]||(t[71]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",qe,[x(et,{modelValue:k.value,"onUpdate:modelValue":t[11]||(t[11]=o=>k.value=o),onProductSelected:is},null,8,["modelValue"]),k.value?(i(),n("div",Te,[t[74]||(t[74]=s("h4",null,"Selected Product",-1)),s("div",Fe,[k.value.image?(i(),n("div",Le,[s("img",{src:k.value.image,alt:k.value.name},null,8,De)])):E("",!0),s("div",Re,[s("h5",null,c(k.value.name),1),s("p",null,"Price: "+c(r(k.value.price)),1)])]),s("div",Be,[t[73]||(t[73]=s("label",{class:"form-label"},"Quantity",-1)),w(s("input",{class:"form-input",type:"number",min:"1","onUpdate:modelValue":t[12]||(t[12]=o=>I.value=o),placeholder:"1"},null,512),[[M,I.value,void 0,{number:!0}]])])])):E("",!0)]),s("div",Qe,[s("button",{class:"action-btn action-btn-primary",onClick:rs,disabled:!k.value||!I.value},t[75]||(t[75]=[s("i",{class:"fas fa-plus"},null,-1),g(" Add to Order ")]),8,ze),s("button",{class:"action-btn action-btn-secondary",onClick:j},"Cancel")])])],2),s("div",{class:T(["modal",{"modal-active":V.value}])},[s("div",{class:"modal-backdrop",onClick:G}),s("div",He,[s("div",{class:"modal-header"},[t[77]||(t[77]=s("h3",{class:"modal-title"},"Update Order Status",-1)),s("button",{class:"modal-close",onClick:G},t[76]||(t[76]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",Ke,[s("div",je,[t[79]||(t[79]=s("label",{class:"form-label"},"Order Status",-1)),w(s("select",{"onUpdate:modelValue":t[13]||(t[13]=o=>C.status=o),class:"form-select"},t[78]||(t[78]=[as('<option value="processing" data-v-5886ec0f>Processing</option><option value="pending" data-v-5886ec0f>Pending</option><option value="shipped" data-v-5886ec0f>Shipped</option><option value="delivered" data-v-5886ec0f>Delivered</option><option value="cancelled" data-v-5886ec0f>Cancelled</option>',5)]),512),[[Q,C.status]])]),s("div",Ge,[t[81]||(t[81]=s("label",{class:"form-label"},"Payment Status",-1)),w(s("select",{"onUpdate:modelValue":t[14]||(t[14]=o=>C.paymentStatus=o),class:"form-select"},t[80]||(t[80]=[s("option",{value:"pending"},"Pending",-1),s("option",{value:"completed"},"Completed",-1),s("option",{value:"refunded"},"Refunded",-1),s("option",{value:"failed"},"Failed",-1)]),512),[[Q,C.paymentStatus]])])]),s("div",Je,[s("button",{class:"action-btn action-btn-primary",onClick:cs,disabled:U.value},[s("i",{class:T(["fas fa-save",{"fa-spinner fa-pulse":U.value}])},null,2),t[82]||(t[82]=g(" Update Status "))],8,We),s("button",{class:"action-btn action-btn-secondary",onClick:G},"Cancel")])])],2)])}}},to=ls(Xe,[["__scopeId","data-v-5886ec0f"]]);export{to as default};
