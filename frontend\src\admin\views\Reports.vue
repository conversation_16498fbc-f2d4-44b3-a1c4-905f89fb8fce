<template>
  <div class="admin-reports">
    <h1 class="title">Reports & Analytics</h1>
    
    <!-- Date Range Selector -->
    <div class="box">
      <div class="columns">
        <div class="column is-3">
          <div class="field">
            <label class="label">Start Date</label>
            <div class="control">
              <input type="date" class="input" v-model="startDate">
            </div>
          </div>
        </div>
        <div class="column is-3">
          <div class="field">
            <label class="label">End Date</label>
            <div class="control">
              <input type="date" class="input" v-model="endDate">
            </div>
          </div>
        </div>
        <div class="column is-3">
          <div class="field">
            <label class="label">Report Type</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="reportType">
                  <option value="sales">Sales Report</option>
                  <option value="products">Product Performance</option>
                  <option value="customers">Customer Analytics</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="column is-3">
          <div class="field">
            <label class="label">&nbsp;</label>
            <div class="control">
              <button
                class="button is-primary is-fullwidth"
                @click="generateReport"
                :class="{ 'is-loading': loading }"
                :disabled="loading"
              >
                Generate Report
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="box has-text-centered" v-if="loading">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Generating report...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="generateReport">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Report Content -->
    <div class="box" v-else-if="reportGenerated">
      <h2 class="subtitle">{{ reportTitle }}</h2>
      
      <!-- Sales Report -->
      <div v-if="reportType === 'sales'" class="report-content">
        <div class="columns">
          <div class="column is-8">
            <div class="chart-container">
              <canvas ref="salesChart"></canvas>
            </div>
          </div>
          <div class="column is-4">
            <div class="summary-box">
              <div class="summary-item">
                <span class="summary-label">Total Sales:</span>
                <span class="summary-value">{{ formatCurrency(salesSummary.total) }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Average Order Value:</span>
                <span class="summary-value">{{ formatCurrency(salesSummary.averageOrder) }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Total Orders:</span>
                <span class="summary-value">{{ salesSummary.orderCount }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Conversion Rate:</span>
                <span class="summary-value">{{ salesSummary.conversionRate }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Product Performance Report -->
      <div v-if="reportType === 'products'" class="report-content">
        <div class="table-container">
          <table class="table is-fullwidth">
            <thead>
              <tr>
                <th>Product</th>
                <th>Units Sold</th>
                <th>Revenue</th>
                <th>Profit</th>
                <th>Conversion Rate</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(product, index) in productPerformance" :key="index">
                <td>{{ product.name }}</td>
                <td>{{ product.unitsSold }}</td>
                <td>{{ formatCurrency(product.revenue) }}</td>
                <td>{{ formatCurrency(product.profit) }}</td>
                <td>{{ product.conversionRate }}%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Customer Analytics Report -->
      <div v-if="reportType === 'customers'" class="report-content">
        <div class="columns">
          <div class="column is-6">
            <div class="chart-container">
              <canvas ref="customerChart"></canvas>
            </div>
          </div>
          <div class="column is-6">
            <div class="summary-box">
              <div class="summary-item">
                <span class="summary-label">New Customers:</span>
                <span class="summary-value">{{ customerAnalytics.newCustomers }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Returning Customers:</span>
                <span class="summary-value">{{ customerAnalytics.returningCustomers }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Average Lifetime Value:</span>
                <span class="summary-value">{{ formatCurrency(customerAnalytics.averageLifetimeValue) }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Churn Rate:</span>
                <span class="summary-value">{{ customerAnalytics.churnRate }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Export Buttons -->
      <div class="buttons is-right mt-4">
        <button
          class="button is-info"
          @click="exportPDF"
          :class="{ 'is-loading': loading }"
          :disabled="loading"
        >
          <span class="icon"><i class="fas fa-file-pdf"></i></span>
          <span>Export as PDF</span>
        </button>
        <button
          class="button is-success"
          @click="exportCSV"
          :class="{ 'is-loading': loading }"
          :disabled="loading"
        >
          <span class="icon"><i class="fas fa-file-csv"></i></span>
          <span>Export as CSV</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import Chart from 'chart.js/auto';
import { reportsService } from '@/admin/services/reports';

// Date range
const startDate = ref(new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0]);
const endDate = ref(new Date().toISOString().split('T')[0]);
const reportType = ref('sales');
const reportGenerated = ref(false);
const loading = ref(false);
const error = ref(null);

// Chart references
const salesChart = ref(null);
const customerChart = ref(null);

// Real data from API
const salesSummary = ref({
  total: 0,
  averageOrder: 0,
  orderCount: 0,
  conversionRate: 0
});

const productPerformance = ref([]);

const customerAnalytics = ref({
  newCustomers: 0,
  returningCustomers: 0,
  averageLifetimeValue: 0,
  churnRate: 0
});

const salesChartData = ref({
  labels: [],
  data: []
});

// Computed properties
const reportTitle = computed(() => {
  const typeLabels = {
    'sales': 'Sales Report',
    'products': 'Product Performance Report',
    'customers': 'Customer Analytics Report'
  };
  
  return `${typeLabels[reportType.value]} (${formatDate(startDate.value)} - ${formatDate(endDate.value)})`;
});

// Methods
const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

const generateReport = async () => {
  loading.value = true;
  error.value = null;

  try {
    const dateRange = {
      startDate: startDate.value,
      endDate: endDate.value
    };

    // Fetch data based on report type
    switch (reportType.value) {
      case 'sales':
        await fetchSalesData(dateRange);
        break;
      case 'products':
        await fetchProductData(dateRange);
        break;
      case 'customers':
        await fetchCustomerData(dateRange);
        break;
    }

    reportGenerated.value = true;

    // Wait for DOM update before rendering charts
    setTimeout(() => {
      if (reportType.value === 'sales' && salesChart.value) {
        renderSalesChart();
      } else if (reportType.value === 'customers' && customerChart.value) {
        renderCustomerChart();
      }
    }, 100);
  } catch (err) {
    error.value = err.message || 'Failed to generate report';
    console.error('Error generating report:', err);
  } finally {
    loading.value = false;
  }
};

const fetchSalesData = async (dateRange) => {
  const [summary, chartData] = await Promise.all([
    reportsService.getSalesSummary(dateRange),
    reportsService.getSalesChart(dateRange)
  ]);

  salesSummary.value = summary.data || summary;
  salesChartData.value = chartData.data || chartData;
};

const fetchProductData = async (dateRange) => {
  const response = await reportsService.getProductPerformance(dateRange);
  productPerformance.value = response.data || response;
};

const fetchCustomerData = async (dateRange) => {
  const response = await reportsService.getCustomerAnalytics(dateRange);
  customerAnalytics.value = response.data || response;
};

const renderSalesChart = () => {
  const ctx = salesChart.value.getContext('2d');

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: salesChartData.value.labels || [],
      datasets: [{
        label: 'Sales',
        data: salesChartData.value.data || [],
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.3,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            color: '#f3f4f6'
          }
        }
      },
      scales: {
        x: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          ticks: {
            color: '#f3f4f6'
          }
        },
        y: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          ticks: {
            color: '#f3f4f6',
            callback: function(value) {
              return '$' + value;
            }
          }
        }
      }
    }
  });
};

const renderCustomerChart = () => {
  const ctx = customerChart.value.getContext('2d');
  
  new Chart(ctx, {
    type: 'pie',
    data: {
      labels: ['New Customers', 'Returning Customers'],
      datasets: [{
        data: [customerAnalytics.value.newCustomers, customerAnalytics.value.returningCustomers],
        backgroundColor: ['#3B82F6', '#10B981'],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            color: '#f3f4f6'
          }
        }
      }
    }
  });
};

const exportPDF = async () => {
  try {
    loading.value = true;
    const dateRange = {
      startDate: startDate.value,
      endDate: endDate.value,
      reportType: reportType.value
    };

    const response = await reportsService.exportReportPDF(dateRange);

    // Create download link
    const blob = new Blob([response], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${reportType.value}-report-${startDate.value}-${endDate.value}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (err) {
    error.value = err.message || 'Failed to export PDF';
    console.error('Error exporting PDF:', err);
  } finally {
    loading.value = false;
  }
};

const exportCSV = async () => {
  try {
    loading.value = true;
    const dateRange = {
      startDate: startDate.value,
      endDate: endDate.value,
      reportType: reportType.value
    };

    const response = await reportsService.exportReportCSV(dateRange);

    // Create download link
    const blob = new Blob([response], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${reportType.value}-report-${startDate.value}-${endDate.value}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (err) {
    error.value = err.message || 'Failed to export CSV';
    console.error('Error exporting CSV:', err);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // Initialize with default report
  generateReport();
});
</script>

<style scoped>
.admin-reports {
  padding: 1rem;
}

.chart-container {
  height: 300px;
  position: relative;
}

.summary-box {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1.5rem;
  height: 100%;
}

.summary-item {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
}

.summary-label {
  color: #9ca3af;
  font-weight: 500;
}

.summary-value {
  font-weight: 600;
  color: #f3f4f6;
}

.mt-4 {
  margin-top: 1.5rem;
}
</style>
