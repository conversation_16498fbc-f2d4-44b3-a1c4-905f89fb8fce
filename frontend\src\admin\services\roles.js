/**
 * Модуль для роботи з ролями користувачів
 * Забезпечує єдиний інтерфейс для роботи з ролями в усьому додатку
 */

// Числові значення ролей, які використовуються на бекенді
export const ROLE_VALUES = {
  BUYER: 0,
  SELLER: 1,
  SELLER_OWNER: 2,
  MODERATOR: 3,
  ADMIN: 4
};

// Рядкові ідентифікатори ролей, які використовуються на фронтенді
// Відповідають enum Role на бекенді: Buyer, Seller, SellerOwner, Moderator, Admin
export const ROLE_KEYS = {
  BUYER: 'Buyer',
  SELLER: 'Seller',
  SELLER_OWNER: 'SellerOwner',
  MODERATOR: 'Moderator',
  ADMIN: 'Admin'
};

// Відображувані назви ролей
export const ROLE_DISPLAY_NAMES = {
  [ROLE_KEYS.BUYER]: 'Buyer',
  [ROLE_KEYS.SELLER]: 'Seller',
  [ROLE_KEYS.SELLER_OWNER]: 'Seller Owner',
  [ROLE_KEYS.MODERATOR]: 'Moderator',
  [ROLE_KEYS.ADMIN]: 'Admin',
  'unknown': 'Unknown'
};

// Мапа для перетворення числових значень ролей у рядкові ідентифікатори
export const ROLE_NUMBER_TO_KEY = {
  [ROLE_VALUES.BUYER]: ROLE_KEYS.BUYER,
  [ROLE_VALUES.SELLER]: ROLE_KEYS.SELLER,
  [ROLE_VALUES.SELLER_OWNER]: ROLE_KEYS.SELLER_OWNER,
  [ROLE_VALUES.MODERATOR]: ROLE_KEYS.MODERATOR,
  [ROLE_VALUES.ADMIN]: ROLE_KEYS.ADMIN
};

// Мапа для перетворення рядкових ідентифікаторів ролей у числові значення
export const ROLE_KEY_TO_NUMBER = {
  [ROLE_KEYS.BUYER]: ROLE_VALUES.BUYER,
  [ROLE_KEYS.SELLER]: ROLE_VALUES.SELLER,
  [ROLE_KEYS.SELLER_OWNER]: ROLE_VALUES.SELLER_OWNER,
  [ROLE_KEYS.MODERATOR]: ROLE_VALUES.MODERATOR,
  [ROLE_KEYS.ADMIN]: ROLE_VALUES.ADMIN
};

// Додаткові мапи для зворотної сумісності з нижнім регістром
export const ROLE_LOWERCASE_TO_BACKEND = {
  'buyer': ROLE_KEYS.BUYER,
  'seller': ROLE_KEYS.SELLER,
  'sellerowner': ROLE_KEYS.SELLER_OWNER,
  'moderator': ROLE_KEYS.MODERATOR,
  'admin': ROLE_KEYS.ADMIN
};

// Порядок сортування ролей (менше значення = вищий пріоритет)
// Admin-Moderator-Seller Owner-Seller-Buyer
export const ROLE_ORDER = {
  [ROLE_KEYS.ADMIN]: 1,
  [ROLE_KEYS.MODERATOR]: 2,
  [ROLE_KEYS.SELLER_OWNER]: 3,
  [ROLE_KEYS.SELLER]: 4,
  [ROLE_KEYS.BUYER]: 5,
  'unknown': 6
};

// CSS-класи для відображення ролей
export const ROLE_CLASSES = {
  [ROLE_KEYS.ADMIN]: 'is-danger',
  [ROLE_KEYS.MODERATOR]: 'is-warning',
  [ROLE_KEYS.SELLER]: 'is-info',
  [ROLE_KEYS.SELLER_OWNER]: 'is-info',
  [ROLE_KEYS.BUYER]: 'is-success',
  'unknown': 'is-light'
};

/**
 * Перетворює роль у рядковий ідентифікатор
 * @param {number|string|object} role - Роль користувача
 * @returns {string} - Рядковий ідентифікатор ролі
 */
export function getRoleKey(role) {
  if (role === undefined || role === null) {
    return 'unknown';
  }

  // Якщо роль - це число, перетворюємо його на рядковий ідентифікатор
  if (typeof role === 'number') {
    return ROLE_NUMBER_TO_KEY[role] || 'unknown';
  }

  // Якщо роль - це рядок, перевіряємо, чи це валідний ідентифікатор
  if (typeof role === 'string') {
    // Спочатку перевіряємо точну відповідність з бекенд форматом
    if (Object.values(ROLE_KEYS).includes(role)) {
      return role;
    }

    // Потім перевіряємо нижній регістр
    const roleLower = role.toLowerCase();
    const backendRole = ROLE_LOWERCASE_TO_BACKEND[roleLower];
    if (backendRole) {
      return backendRole;
    }

    // Перевіряємо, чи це відображуване ім'я ролі
    const roleKey = Object.keys(ROLE_DISPLAY_NAMES).find(
      key => ROLE_DISPLAY_NAMES[key].toLowerCase() === roleLower
    );

    if (roleKey) {
      return roleKey;
    }

    return 'unknown';
  }

  // Якщо роль - це об'єкт, намагаємося витягнути роль з нього
  if (typeof role === 'object') {
    if (role.hasOwnProperty('name')) {
      return getRoleKey(role.name);
    }
    
    if (role.hasOwnProperty('value')) {
      return getRoleKey(role.value);
    }
  }

  return 'unknown';
}

/**
 * Перетворює роль у числове значення
 * @param {number|string|object} role - Роль користувача
 * @returns {number} - Числове значення ролі
 */
export function getRoleValue(role) {
  const roleKey = getRoleKey(role);
  return ROLE_KEY_TO_NUMBER[roleKey] !== undefined 
    ? ROLE_KEY_TO_NUMBER[roleKey] 
    : -1;
}

/**
 * Повертає відображуване ім'я ролі
 * @param {number|string|object} role - Роль користувача
 * @returns {string} - Відображуване ім'я ролі
 */
export function getRoleDisplayName(role) {
  const roleKey = getRoleKey(role);
  return ROLE_DISPLAY_NAMES[roleKey] || 'Unknown';
}

/**
 * Перетворює роль у формат для бекенду
 * @param {string|number} role - роль користувача
 * @returns {string} роль у форматі бекенду
 */
export const getRoleForBackend = (role) => {
  return getRoleKey(role);
};

/**
 * Повертає CSS клас для ролі
 * @param {string|number} role - роль користувача
 * @returns {string} CSS клас для ролі
 */
export const getRoleClass = (role) => {
  const roleKey = getRoleKey(role);

  switch (roleKey) {
    case ROLE_KEYS.ADMIN:
      return 'is-admin';
    case ROLE_KEYS.MODERATOR:
      return 'is-moderator';
    case ROLE_KEYS.SELLER_OWNER:
      return 'is-sellerowner';
    case ROLE_KEYS.SELLER:
      return 'is-seller';
    case ROLE_KEYS.BUYER:
      return 'is-buyer';
    default:
      return 'is-buyer'; // За замовчуванням
  }
};

/**
 * Порядок ролей для сортування (від найвищої до найнижчої)
 */
const ROLE_SORT_ORDER = {
  [ROLE_KEYS.ADMIN]: 0,
  [ROLE_KEYS.MODERATOR]: 1,
  [ROLE_KEYS.SELLER_OWNER]: 2,
  [ROLE_KEYS.SELLER]: 3,
  [ROLE_KEYS.BUYER]: 4
};

/**
 * Порівнює дві ролі для сортування
 * @param {string|number} roleA - перша роль
 * @param {string|number} roleB - друга роль
 * @returns {number} результат порівняння (-1, 0, 1)
 */
export const compareRoles = (roleA, roleB) => {
  const keyA = getRoleKey(roleA);
  const keyB = getRoleKey(roleB);

  const orderA = ROLE_SORT_ORDER[keyA] ?? 999;
  const orderB = ROLE_SORT_ORDER[keyB] ?? 999;

  return orderA - orderB;
};

// Дублікати функцій видалено - використовуємо ті, що визначені вище

/**
 * Повертає список всіх можливих ролей
 * @returns {Array} - Масив об'єктів з ключем, значенням та відображуваним ім'ям ролі
 */
export function getAllRoles() {
  return Object.values(ROLE_KEYS).map(key => ({
    key,
    value: ROLE_KEY_TO_NUMBER[key],
    displayName: ROLE_DISPLAY_NAMES[key]
  }));
}

/**
 * Перевіряє, чи є роль адміністратором
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - адміністратор
 */
export function isAdmin(role) {
  return getRoleKey(role) === ROLE_KEYS.ADMIN;
}

/**
 * Перевіряє, чи є роль модератором
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - модератор
 */
export function isModerator(role) {
  return getRoleKey(role) === ROLE_KEYS.MODERATOR;
}

/**
 * Перевіряє, чи є роль продавцем
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - продавець
 */
export function isSeller(role) {
  const key = getRoleKey(role);
  return key === ROLE_KEYS.SELLER || key === ROLE_KEYS.SELLER_OWNER;
}

/**
 * Перевіряє, чи є роль покупцем
 * @param {number|string|object} role - Роль користувача
 * @returns {boolean} - true, якщо роль - покупець
 */
export function isBuyer(role) {
  return getRoleKey(role) === ROLE_KEYS.BUYER;
}
