<template>
  <div class="admin-company-edit">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <nav class="admin-breadcrumb">
            <router-link to="/admin/companies" class="admin-breadcrumb-item">
              <i class="fas fa-building"></i>
              Companies
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <router-link 
              v-if="company.id" 
              :to="`/admin/companies/${company.id}`" 
              class="admin-breadcrumb-item">
              {{ company.name || 'Company Details' }}
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <span class="admin-breadcrumb-item admin-breadcrumb-current">
              Edit
            </span>
          </nav>
          <h1 class="admin-page-title">
            <i class="fas fa-edit"></i>
            Edit Company
          </h1>
          <p class="admin-page-subtitle">
            Update company information, financial details, and settings
          </p>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-secondary" @click="resetForm" :disabled="saving">
            <i class="fas fa-undo"></i>
            <span>Reset</span>
          </button>
          <button class="admin-btn admin-btn-primary" @click="saveCompany" :disabled="saving">
            <i class="fas fa-save" :class="{ 'fa-spin': saving }"></i>
            <span>{{ saving ? 'Saving...' : 'Save Changes' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-state" v-if="loading && !company.id">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading company data...</p>
    </div>

    <!-- Error State -->
    <div class="admin-alert admin-alert-danger" v-else-if="error">
      <div class="admin-alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-alert-content">
        <div class="admin-alert-message">{{ error }}</div>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Edit Form -->
    <div v-else-if="company.id" class="admin-company-content">
      <form @submit.prevent="saveCompany" class="admin-company-form">
        
        <!-- Basic Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-building admin-card-icon"></i>
              Basic Information
            </h3>
            <p class="admin-card-subtitle">Company name, description, and contact details</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Company Name *
                </label>
                <input 
                  type="text" 
                  v-model="form.company.name"
                  class="admin-form-control"
                  placeholder="Enter company name"
                  required
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Contact Email *
                </label>
                <input 
                  type="email" 
                  v-model="form.company.contactEmail"
                  class="admin-form-control"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Contact Phone
                </label>
                <input 
                  type="tel" 
                  v-model="form.company.contactPhone"
                  class="admin-form-control"
                  placeholder="+380 XX XXX XXXX"
                />
              </div>



              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Description
                </label>
                <textarea 
                  v-model="form.company.description"
                  class="admin-form-control"
                  rows="4"
                  placeholder="Describe the company..."
                ></textarea>
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Featured Company
                </label>
                <div class="admin-form-checkbox">
                  <input 
                    type="checkbox" 
                    id="featured"
                    v-model="form.company.isFeatured"
                    class="admin-checkbox"
                  />
                  <label for="featured" class="admin-checkbox-label">
                    Mark as featured company
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-map-marker-alt admin-card-icon"></i>
              Address Information
            </h3>
            <p class="admin-card-subtitle">Company location and address details</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Region
                </label>
                <input 
                  type="text" 
                  v-model="form.company.addressRegion"
                  class="admin-form-control"
                  placeholder="e.g., Kyiv Oblast"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  City
                </label>
                <input 
                  type="text" 
                  v-model="form.company.addressCity"
                  class="admin-form-control"
                  placeholder="e.g., Kyiv"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Postal Code
                </label>
                <input 
                  type="text" 
                  v-model="form.company.addressPostalCode"
                  class="admin-form-control"
                  placeholder="e.g., 01001"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Street Address
                </label>
                <input 
                  type="text" 
                  v-model="form.company.addressStreet"
                  class="admin-form-control"
                  placeholder="e.g., 123 Main Street, Building A"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Company Images -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-images admin-card-icon"></i>
              Company Images
            </h3>
            <p class="admin-card-subtitle">Upload company logo and meta image for SEO</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">Company Logo</label>
                <EntityImageManager
                  entity-type="company"
                  :entity-id="company.id"
                  :current-image="form.company.imageUrl"
                  image-alt="Company Logo"
                  :local-mode="true"
                  @image-uploaded="handleImageUploaded"
                  @image-removed="handleImageRemoved"
                  @image-changed="handleImageChanged"
                  ref="logoImageManager"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">Meta Image (SEO)</label>
                <EntityMetaImageManager
                  entity-type="company"
                  :entity-id="company.id"
                  :current-image="form.company.metaImageUrl"
                  :social-preview-title="form.company.name || 'Company Name'"
                  :social-preview-description="form.company.description || 'Company Description'"
                  :social-preview-url="`https://marketplace.com/companies/${form.company.slug || company.id}`"
                  :local-mode="true"
                  @meta-image-uploaded="handleMetaImageUploaded"
                  @meta-image-removed="handleMetaImageRemoved"
                  @meta-image-changed="handleMetaImageChanged"
                  ref="metaImageManager"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Financial Information -->
        <div class="admin-card admin-form-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-credit-card admin-card-icon"></i>
              Financial Information
            </h3>
            <p class="admin-card-subtitle">Banking and tax details for payments</p>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid">
              <div class="admin-form-group">
                <label class="admin-form-label">
                  Bank Name
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.bankName"
                  class="admin-form-control"
                  placeholder="e.g., PrivatBank"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Bank Code
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.bankCode"
                  class="admin-form-control"
                  placeholder="e.g., 305299"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Bank Account Number
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.bankAccount"
                  class="admin-form-control"
                  placeholder="e.g., 2600600**********123456789"
                />
              </div>

              <div class="admin-form-group">
                <label class="admin-form-label">
                  Tax ID
                </label>
                <input 
                  type="text" 
                  v-model="form.finance.taxId"
                  class="admin-form-control"
                  placeholder="e.g., **********"
                />
              </div>

              <div class="admin-form-group admin-form-group-full">
                <label class="admin-form-label">
                  Payment Details
                </label>
                <textarea 
                  v-model="form.finance.paymentDetails"
                  class="admin-form-control"
                  rows="3"
                  placeholder="Additional payment information..."
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="admin-form-actions">
          <router-link 
            :to="`/admin/companies/${company.id}`"
            class="admin-btn admin-btn-secondary">
            <i class="fas fa-times"></i>
            <span>Cancel</span>
          </router-link>
          
          <button 
            type="button"
            @click="resetForm"
            class="admin-btn admin-btn-secondary"
            :disabled="saving">
            <i class="fas fa-undo"></i>
            <span>Reset</span>
          </button>
          
          <button 
            type="submit"
            class="admin-btn admin-btn-primary"
            :disabled="saving">
            <i class="fas fa-save" :class="{ 'fa-spin': saving }"></i>
            <span>{{ saving ? 'Saving...' : 'Save Changes' }}</span>
          </button>
        </div>

      </form>
    </div>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { companiesService } from '@/admin/services/companies';
import EntityImageManager from '@/admin/components/common/EntityImageManager.vue';
import EntityMetaImageManager from '@/admin/components/common/EntityMetaImageManager.vue';
import imageService from '@/services/image.service';
import api from '@/services/api';

const route = useRoute();
const router = useRouter();

// Reactive data
const company = ref({});
const loading = ref(false);
const saving = ref(false);
const error = ref(null);

// Form data
const form = reactive({
  company: {
    name: '',
    description: '',
    contactEmail: '',
    contactPhone: '',
    addressRegion: '',
    addressCity: '',
    addressStreet: '',
    addressPostalCode: '',
    imageUrl: '',
    metaImageUrl: '',
    isFeatured: false
  },
  finance: {
    bankName: '',
    bankAccount: '',
    bankCode: '',
    taxId: '',
    paymentDetails: ''
  }
});

// Image handling
const pendingImageFile = ref(null);
const pendingMetaImageFile = ref(null);
const originalImageUrl = ref('');
const originalMetaImageUrl = ref('');
const logoImageManager = ref(null);
const metaImageManager = ref(null);

// Original data for reset functionality
const originalData = reactive({
  company: {},
  finance: {}
});

// Methods
const fetchCompany = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;
    
    // Populate form with current data
    populateForm(response.data);
    
    // Store original data for reset
    storeOriginalData(response.data);
  } catch (err) {
    error.value = err.message || 'Failed to load company data';
  } finally {
    loading.value = false;
  }
};

const populateForm = (data) => {
  // Company data
  Object.assign(form.company, {
    name: data.name || '',
    description: data.description || '',
    contactEmail: data.contactEmail || '',
    contactPhone: data.contactPhone || '',
    addressRegion: data.addressRegion || '',
    addressCity: data.addressCity || '',
    addressStreet: data.addressStreet || '',
    addressPostalCode: data.addressPostalCode || '',
    imageUrl: data.imageUrl || '',
    metaImageUrl: data.metaImage || data.metaImageUrl || '',
    isFeatured: data.isFeatured || false
  });

  // Store original image URLs
  originalImageUrl.value = data.imageUrl || '';
  originalMetaImageUrl.value = data.metaImage || data.metaImageUrl || '';

  // Finance data
  if (data.finance) {
    Object.assign(form.finance, {
      bankName: data.finance.bankName || '',
      bankAccount: data.finance.bankAccount || '',
      bankCode: data.finance.bankCode || '',
      taxId: data.finance.taxId || '',
      paymentDetails: data.finance.paymentDetails || ''
    });
  }
};

const storeOriginalData = (data) => {
  originalData.company = { ...form.company };
  originalData.finance = { ...form.finance };
};

const resetForm = () => {
  Object.assign(form.company, originalData.company);
  Object.assign(form.finance, originalData.finance);
  pendingImageFile.value = null;
  pendingMetaImageFile.value = null;

  // Скидаємо локальні зміни в компонентах зображень
  if (logoImageManager.value && logoImageManager.value.resetLocalChanges) {
    logoImageManager.value.resetLocalChanges();
  }
  if (metaImageManager.value && metaImageManager.value.resetLocalChanges) {
    metaImageManager.value.resetLocalChanges();
  }
};

// Image handling methods
const handleImageUploaded = (data) => {
  console.log('Logo image uploaded successfully:', data);
  // Оновлюємо URL логотипу після успішного завантаження
  const newImageUrl = data.imageUrl || data.url || data.fileUrl;
  form.company.imageUrl = newImageUrl;
  originalImageUrl.value = newImageUrl; // Оновлюємо оригінальне значення
  console.log('Logo URL updated to:', newImageUrl);
};

const handleImageChanged = (data) => {
  console.log('Logo image changed:', data);

  if (data && data.type === 'removal') {
    // Локальний режим - позначаємо для відкладеного видалення
    console.log('Logo marked for deferred removal');
    // Не змінюємо form.company.imageUrl відразу - це буде оброблено при збереженні
  } else if (data && data.type === 'upload' && data.previewUrl) {
    // Показуємо превью нового зображення
    console.log('Logo preview updated');
    // Можна показати превью, але не оновлювати form до збереження
  }
};

const handleImageRemoved = () => {
  // Викликається після успішного видалення логотипу
  console.log('Logo successfully removed');
  form.company.imageUrl = null;
  originalImageUrl.value = null;
};

const handleMetaImageUploaded = (data) => {
  console.log('Meta image uploaded successfully:', data);
  // Оновлюємо URL мета-зображення після успішного завантаження
  const newMetaImageUrl = data.fileUrl || data.imageUrl || data.url;
  form.company.metaImageUrl = newMetaImageUrl;
  originalMetaImageUrl.value = newMetaImageUrl; // Оновлюємо оригінальне значення
  console.log('Meta image URL updated to:', newMetaImageUrl);
};

const handleMetaImageChanged = (change) => {
  console.log('Meta image changed:', change);

  if (change && change.type === 'removal') {
    // Локальний режим - позначаємо для відкладеного видалення
    console.log('Meta image marked for deferred removal');
    // Не змінюємо form.company.metaImageUrl відразу - це буде оброблено при збереженні
  } else if (change && change.type === 'upload' && change.previewUrl) {
    // Показуємо превью нового мета-зображення
    console.log('Meta image preview updated');
    // Можна показати превью, але не оновлювати form до збереження
  }
};

const handleMetaImageRemoved = () => {
  // Викликається після успішного видалення мета-зображення
  console.log('Meta image successfully removed');
  form.company.metaImageUrl = null;
  originalMetaImageUrl.value = null;
};

// Методи для перевірки незбережених змін
const hasUnsavedChanges = () => {
  // Перевіряємо зміни в зображеннях
  const logoChanges = logoImageManager.value?.hasChanges || false;
  const metaImageChanges = metaImageManager.value?.hasChanges || false;

  // Можна додати перевірку інших полів форми якщо потрібно
  // const formChanges = /* логіка перевірки змін в формі */;

  return logoChanges || metaImageChanges;
};

// Методи для отримання стану змін зображень
const getImageChangeStates = () => {
  return {
    logo: logoImageManager.value?.getChangeState() || { hasChanges: false },
    metaImage: metaImageManager.value?.getChangeState() || { hasChanges: false }
  };
};

const uploadImage = async (companyId) => {
  if (!pendingImageFile.value) return;

  const formData = new FormData();

  // Sanitize filename like in ProductImageManager
  const sanitizedFileName = sanitizeFileName(pendingImageFile.value.name);
  const fileWithSanitizedName = new File([pendingImageFile.value], sanitizedFileName, {
    type: pendingImageFile.value.type,
    lastModified: pendingImageFile.value.lastModified
  });

  formData.append('file', fileWithSanitizedName);

  await api.post(`/api/admin/companies/${companyId}/images`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 30000
  });
};

const uploadMetaImage = async (companyId) => {
  if (!pendingMetaImageFile.value) return;

  const formData = new FormData();

  // Sanitize filename like in ProductImageManager
  const sanitizedFileName = sanitizeFileName(pendingMetaImageFile.value.name);
  const fileWithSanitizedName = new File([pendingMetaImageFile.value], sanitizedFileName, {
    type: pendingMetaImageFile.value.type,
    lastModified: pendingMetaImageFile.value.lastModified
  });

  formData.append('file', fileWithSanitizedName);

  await api.post(`/api/admin/companies/${companyId}/meta-image`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 30000
  });
};

// Видалення зображень тепер відбувається через updateData з null значеннями
// Ці функції більше не потрібні

// Helper function to sanitize filenames (copied from ProductImageManager)
const sanitizeFileName = (fileName) => {
  const sanitized = fileName
    .replace(/[а-яё]/gi, (match) => {
      const cyrillicToLatin = {
        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
        'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
        'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
        'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
        'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
      };
      return cyrillicToLatin[match.toLowerCase()] || match;
    })
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');

  return sanitized || 'image';
};

const saveCompany = async () => {
  saving.value = true;
  error.value = null;

  try {
    // FIRST: Clean up any blob URLs before processing
    console.log('Cleaning up blob URLs before processing...');
    if (form.company.imageUrl && (form.company.imageUrl.startsWith('data:') || form.company.imageUrl.startsWith('blob:'))) {
      console.log('Found blob/data URL in imageUrl, replacing with original:', originalImageUrl.value);
      form.company.imageUrl = originalImageUrl.value;
    }

    if (form.company.metaImageUrl && (form.company.metaImageUrl.startsWith('data:') || form.company.metaImageUrl.startsWith('blob:'))) {
      console.log('Found blob/data URL in metaImageUrl, replacing with original:', originalMetaImageUrl.value);
      form.company.metaImageUrl = originalMetaImageUrl.value;
    }

    // Process pending image operations
    console.log('Processing pending image operations...');

    // Process logo image operations
    if (logoImageManager.value && logoImageManager.value.processPendingOperations) {
      try {
        const logoResults = await logoImageManager.value.processPendingOperations();
        console.log('Logo operations completed:', logoResults);

        // Оновлюємо form.company.imageUrl на основі результатів
        if (logoResults.removed && !logoResults.uploaded) {
          form.company.imageUrl = null;
          originalImageUrl.value = null;
        }
        // URL нового зображення буде оновлено через подію image-uploaded в handleImageUploaded

        if (logoResults.errors && logoResults.errors.length > 0) {
          console.warn('Logo operation errors:', logoResults.errors);
        }
      } catch (logoError) {
        console.error('Error processing logo operations:', logoError);
        // Не блокуємо збереження компанії через помилки з логотипом
      }
    }

    // Process meta image operations
    if (metaImageManager.value && metaImageManager.value.processPendingOperations) {
      try {
        const metaResults = await metaImageManager.value.processPendingOperations();
        console.log('Meta image operations completed:', metaResults);

        // Оновлюємо form.company.metaImageUrl на основі результатів
        if (metaResults.removed && !metaResults.uploaded) {
          form.company.metaImageUrl = null;
          originalMetaImageUrl.value = null;
        }
        // URL нового мета-зображення буде оновлено через подію meta-image-uploaded в handleMetaImageUploaded

        if (metaResults.errors && metaResults.errors.length > 0) {
          console.warn('Meta image operation errors:', metaResults.errors);
        }
      } catch (metaError) {
        console.error('Error processing meta image operations:', metaError);
        // Не блокуємо збереження компанії через помилки з мета-зображенням
      }
    }

    // Prepare company data AFTER processing pending operations
    const companyData = { ...form.company };

    // URLs already cleaned up earlier in the process

    // Convert empty strings to null for proper validation and normalize URLs
    if (companyData.imageUrl === '' || companyData.imageUrl === null) {
      companyData.imageUrl = null;
      console.log('Setting imageUrl to null for deletion');
    } else if (companyData.imageUrl) {
      companyData.imageUrl = imageService.normalizeImageUrl(companyData.imageUrl);
      console.log('Normalizing imageUrl:', companyData.imageUrl);
    }

    if (companyData.metaImageUrl === '' || companyData.metaImageUrl === null) {
      companyData.metaImageUrl = null;
      console.log('Setting metaImageUrl to null for deletion');
    } else if (companyData.metaImageUrl) {
      companyData.metaImageUrl = imageService.normalizeImageUrl(companyData.metaImageUrl);
      console.log('Normalizing metaImageUrl:', companyData.metaImageUrl);
    }

    // Update company data with correct structure (nested, as expected by backend)
    const updateData = {
      company: companyData, // Вкладена структура, як очікує бекенд
      finance: form.finance,
      schedule: form.schedule || []
    };

    console.log('Sending updateData to backend:', updateData);
    console.log('imageUrl in updateData.company:', updateData.company?.imageUrl);
    console.log('metaImageUrl in updateData.company:', updateData.company?.metaImageUrl);

    try {
      console.log('Calling companiesService.updateDetailedCompany...');
      const result = await companiesService.updateDetailedCompany(route.params.id, updateData);
      console.log('updateDetailedCompany result:', result);
    } catch (error) {
      console.error('updateDetailedCompany error:', error);
      throw error;
    }

    // Redirect to company detail page
    router.push(`/admin/companies/${route.params.id}`);
  } catch (err) {
    error.value = err.message || 'Failed to update company';
  } finally {
    saving.value = false;
  }
};

// Initialize
onMounted(() => {
  fetchCompany();
});
</script>

<style scoped>
.admin-company-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.admin-form-section {
  margin-bottom: 0;
}

.admin-card-subtitle {
  font-size: 0.875rem;
  color: var(--admin-text-muted);
  margin-top: 0.25rem;
}

.admin-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.admin-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-form-group-full {
  grid-column: 1 / -1;
}

.admin-form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  margin-bottom: 0.25rem;
}

.admin-form-control {
  padding: 0.75rem;
  border: 1px solid var(--admin-border-light);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
}

.admin-form-control:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(50, 115, 220, 0.1);
}

.admin-form-control::placeholder {
  color: var(--admin-text-light);
}

.admin-form-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--admin-primary);
}

.admin-checkbox-label {
  font-size: 0.9rem;
  color: var(--admin-text-primary);
  cursor: pointer;
  margin: 0;
}

.admin-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem 0;
  border-top: 1px solid var(--admin-border-light);
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-form-grid {
    grid-template-columns: 1fr;
  }

  .admin-form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .admin-form-actions .admin-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 1200px) {
  .admin-form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Form Validation States */
.admin-form-control:invalid {
  border-color: var(--admin-danger);
}

.admin-form-control:invalid:focus {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 3px rgba(241, 70, 104, 0.1);
}

/* Enhanced form styling */
.admin-form-control[type="email"],
.admin-form-control[type="tel"],
.admin-form-control[type="url"] {
  font-family: 'Courier New', monospace;
}

textarea.admin-form-control {
  resize: vertical;
  min-height: 100px;
}

/* Loading and disabled states */
.admin-form-control:disabled {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
  cursor: not-allowed;
}

.admin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Success state for saved forms */
.form-saved {
  border-color: var(--admin-success);
  background: rgba(72, 199, 116, 0.05);
}

.form-saved:focus {
  border-color: var(--admin-success);
  box-shadow: 0 0 0 3px rgba(72, 199, 116, 0.1);
}
</style>
