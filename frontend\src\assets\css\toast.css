/* Toast notifications */
.toast {
  position: fixed;
  z-index: 9999;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  min-width: 250px;
  max-width: 350px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  opacity: 0;
}

.toast-success {
  background-color: #48c774;
  color: #fff;
}

.toast-error {
  background-color: #f14668;
  color: #fff;
}

.toast-warning {
  background-color: #ffdd57;
  color: #333;
}

.toast-info {
  background-color: #3298dc;
  color: #fff;
}

/* Toast positions */
.toast-top-right {
  top: 20px;
  right: 20px;
}

.toast-top-left {
  top: 20px;
  left: 20px;
}

.toast-top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.toast-bottom-right {
  bottom: 20px;
  right: 20px;
}

.toast-bottom-left {
  bottom: 20px;
  left: 20px;
}

.toast-bottom-center {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}
