<template>
  <section class="categories-section">
    <div class="container">
      <div v-if="categories" class="categories-grid">
          <div v-for="category in categories" :key="category.id" class="category-item">
            <div class="category-icon">
              <img
                :src="getCategoryImageUrl(category)"
                :alt="category.name"
                @error="handleImageError($event, category)"
                loading="lazy"
              >
            </div>
            <a :href="`/catalog/${category.slug}`" class="category-name" v-if="category.name">{{ category.name }}</a>
          </div>
        </div>
      <div v-else>Loading categories...</div>
  </div>
  </section>
</template>

<script>
export default {
  props: {
    categories: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
      placeholderImage: '/placeholder-category.svg',
      imageCache: new Map(), // Кеш для URL зображень категорій
      loadingImages: new Set() // Множина ID категорій, які завантажуються
    }
  },
  watch: {
    // Watcher для categories для завантаження зображень нових категорій
    categories: {
      handler(newCategories) {
        if (newCategories && newCategories.length > 0) {
          this.$nextTick(() => {
            this.preloadCategoryImages();
          });
        }
      },
      deep: true
    }
  },
  mounted() {
    // Завантажуємо зображення для існуючих категорій
    this.$nextTick(() => {
      this.preloadCategoryImages();
    });
  },
  methods: {
    // Безпечний метод для отримання URL зображення категорії
    getCategoryImageUrl(category) {
      if (!category.id) {
        return this.placeholderImage;
      }

      // Якщо є пряме посилання на зображення, використовуємо його
      if (category.image) {
        return category.image;
      }

      // Перевіряємо кеш
      const cacheKey = `category_${category.id}`;
      if (this.imageCache.has(cacheKey)) {
        return this.imageCache.get(cacheKey);
      }

      // Fallback на placeholder
      return this.placeholderImage;
    },

    // Асинхронно завантажує зображення категорії
    async loadCategoryImage(categoryId) {
      if (!categoryId) return;

      const cacheKey = `category_${categoryId}`;

      // Перевіряємо, чи компонент ще існує
      if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
        return;
      }

      if (this.loadingImages.has(cacheKey) || this.imageCache.has(cacheKey)) {
        return; // Вже завантажується або завантажено
      }

      this.loadingImages.add(cacheKey);

      try {
        const apiUrl = import.meta.env.VUE_APP_API_URL || 'http://localhost:5296';
        const response = await fetch(`${apiUrl}/api/universal/images/category/${categoryId}/url?imageType=main`);

        // Перевіряємо, чи компонент ще існує після асинхронної операції
        if (!this.$el || this._isDestroyed || this._isBeingDestroyed) {
          return;
        }

        if (response.ok) {
          const data = await response.json();

          // Backend повертає JSON з структурою { success: true, data: "url" }
          let imageUrl = null;
          if (data.success && data.data) {
            imageUrl = data.data;
          } else if (data.data) {
            imageUrl = data.data;
          } else if (typeof data === 'string') {
            imageUrl = data;
          }

          if (imageUrl) {
            this.imageCache.set(cacheKey, imageUrl);
            // Примусово оновлюємо компонент для показу нового зображення
            this.$forceUpdate();
          } else {
            // Якщо зображення не знайдено, кешуємо placeholder
            this.imageCache.set(cacheKey, this.placeholderImage);
          }
        } else {
          console.warn(`HomeCategories - Image not found for category ${categoryId}: ${response.status}`);
          // Якщо помилка API, кешуємо placeholder
          this.imageCache.set(cacheKey, this.placeholderImage);
        }
      } catch (error) {
        console.error('HomeCategories - Error loading category image:', categoryId, error);
        // При помилці кешуємо placeholder
        this.imageCache.set(cacheKey, this.placeholderImage);
      } finally {
        this.loadingImages.delete(cacheKey);
      }
    },

    // Попередньо завантажує зображення для всіх категорій
    preloadCategoryImages() {
      if (this.categories && this.categories.length > 0) {
        this.categories.forEach(category => {
          if (category.id && !category.image) {
            // Завантажуємо зображення тільки якщо немає прямого URL
            setTimeout(() => {
              this.loadCategoryImage(category.id);
            }, 0);
          }
        });
      }
    },

    // Обробка помилок завантаження зображень
    handleImageError(event, category) {
      //console.log('HomeCategories - Image loading error for category:', category.id, event.target.src);

      // Якщо зображення не завантажилося, встановлюємо placeholder
      if (event.target.src !== this.placeholderImage) {
        //console.log('HomeCategories - Setting placeholder image for category:', category.id);
        event.target.src = this.placeholderImage;
      }
    }
  }
}
</script>

<style scoped>

.categories-section {
  margin-bottom: 48px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  margin: 0 auto;
  max-width: 1200px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 16px;
}

.category-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.category-icon img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.category-name {
  font-size: 15px;
  font-weight: 500;
  color: black;
  max-width: 120px;
  line-height: 1.2;
}

.category-name:hover {
  color: #ABAAAA;
}

@media (max-width: 1024px) {
  .categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .categories-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>