using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;

namespace Marketplace.Application.Commands.SellerRequest;

public class MoveSellerRequestFilesToCompanyCommandHandler : IRequestHandler<MoveSellerRequestFilesToCompanyCommand, bool>
{
    private readonly ILogger<MoveSellerRequestFilesToCompanyCommandHandler> _logger;
    private readonly ICompanyRepository _companyRepository;
    private readonly string _uploadsPath;

    public MoveSellerRequestFilesToCompanyCommandHandler(
        ILogger<MoveSellerRequestFilesToCompanyCommandHandler> logger,
        ICompanyRepository companyRepository,
        IConfiguration configuration)
    {
        _logger = logger;
        _companyRepository = companyRepository;
        _uploadsPath = configuration["FileStorage:StoragePath"] ?? "wwwroot/uploads";
    }

    public async Task<bool> Handle(MoveSellerRequestFilesToCompanyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var company = await _companyRepository.GetByIdAsync(request.CompanyId, cancellationToken);
            if (company == null)
            {
                _logger.LogWarning("Company {CompanyId} not found", request.CompanyId);
                return false;
            }

            // Шляхи до тимчасових файлів
            var tempLogoPath = Path.Combine(_uploadsPath, "temp", "seller-requests", request.SellerRequestId.ToString(), "logo");
            var tempMetaPath = Path.Combine(_uploadsPath, "temp", "seller-requests", request.SellerRequestId.ToString(), "meta");

            // Шляхи до постійних папок
            var companyLogoPath = Path.Combine(_uploadsPath, "images", "company", request.CompanyId.ToString());
            var companyMetaPath = Path.Combine(_uploadsPath, "meta", "company", request.CompanyId.ToString());

            // Створюємо папки для компанії, якщо вони не існують
            Directory.CreateDirectory(companyLogoPath);
            Directory.CreateDirectory(companyMetaPath);

            var moved = false;

            // Переміщуємо логотип
            if (Directory.Exists(tempLogoPath))
            {
                var logoFiles = Directory.GetFiles(tempLogoPath);
                foreach (var logoFile in logoFiles)
                {
                    var fileName = Path.GetFileName(logoFile);
                    var newFileName = $"logo_{DateTime.UtcNow:yyyyMMdd_HHmmss}{Path.GetExtension(fileName)}";
                    var destinationPath = Path.Combine(companyLogoPath, newFileName);
                    
                    System.IO.File.Move(logoFile, destinationPath);
                    
                    // Оновлюємо URL логотипу в компанії
                    var logoUrl = $"/uploads/images/company/{request.CompanyId}/{newFileName}";
                    company.UpdateImage(new Url(logoUrl));
                    moved = true;
                    
                    _logger.LogInformation("Moved logo from {Source} to {Destination}", logoFile, destinationPath);
                }
            }

            // Переміщуємо meta зображення
            if (Directory.Exists(tempMetaPath))
            {
                var metaFiles = Directory.GetFiles(tempMetaPath);
                foreach (var metaFile in metaFiles)
                {
                    var fileName = Path.GetFileName(metaFile);
                    var newFileName = $"meta_{DateTime.UtcNow:yyyyMMdd_HHmmss}{Path.GetExtension(fileName)}";
                    var destinationPath = Path.Combine(companyMetaPath, newFileName);
                    
                    System.IO.File.Move(metaFile, destinationPath);
                    
                    // Оновлюємо URL meta зображення в компанії
                    var metaUrl = $"/uploads/meta/company/{request.CompanyId}/{newFileName}";
                    var updatedMeta = new Marketplace.Domain.ValueObjects.Meta(
                        company.Meta.Title,
                        company.Meta.Description,
                        new Url(metaUrl));
                    company.UpdateMeta(updatedMeta);
                    moved = true;
                    
                    _logger.LogInformation("Moved meta image from {Source} to {Destination}", metaFile, destinationPath);
                }
            }

            // Зберігаємо зміни в компанії
            if (moved)
            {
                await _companyRepository.UpdateAsync(company, cancellationToken);
            }

            // Видаляємо тимчасові папки
            var tempRequestPath = Path.Combine(_uploadsPath, "temp", "seller-requests", request.SellerRequestId.ToString());
            if (Directory.Exists(tempRequestPath))
            {
                Directory.Delete(tempRequestPath, true);
                _logger.LogInformation("Deleted temporary folder {TempPath}", tempRequestPath);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving files from seller request {RequestId} to company {CompanyId}",
                request.SellerRequestId, request.CompanyId);
            return false;
        }
    }
}
