using Marketplace.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Presentation.Services;

/// <summary>
/// Background service для щомісячного скидання статистики відвідувань категорій
/// </summary>
public class CategoryVisitsResetService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CategoryVisitsResetService> _logger;
    private readonly TimeSpan _checkInterval = TimeSpan.FromHours(1); // Перевіряємо кожну годину

    public CategoryVisitsResetService(
        IServiceProvider serviceProvider,
        ILogger<CategoryVisitsResetService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("CategoryVisitsResetService запущено");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CheckAndResetCategoryVisitsAsync();
                await Task.Delay(_checkInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Нормальне завершення сервісу
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Помилка в CategoryVisitsResetService");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Чекаємо 5 хвилин перед повторною спробою
            }
        }

        _logger.LogInformation("CategoryVisitsResetService зупинено");
    }

    private async Task CheckAndResetCategoryVisitsAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<MarketplaceDbContext>();

        try
        {
            var now = DateTime.UtcNow;
            
            // Знаходимо користувачів, яким потрібно скинути статистику
            var usersToReset = await dbContext.Users
                .Where(u => 
                    // Користувачі, які ніколи не скидали статистику
                    u.LastCategoryVisitsReset == null ||
                    // Або користувачі, у яких минув місяць з останнього скидання
                    (u.LastCategoryVisitsReset.Value.Month != now.Month || 
                     u.LastCategoryVisitsReset.Value.Year != now.Year))
                .ToListAsync();

            if (usersToReset.Any())
            {
                _logger.LogInformation("Скидання статистики відвідувань для {Count} користувачів", usersToReset.Count);

                foreach (var user in usersToReset)
                {
                    user.CheckAndResetCategoryVisitsIfNeeded();
                }

                await dbContext.SaveChangesAsync();
                
                _logger.LogInformation("Статистику відвідувань скинуто для {Count} користувачів", usersToReset.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при скиданні статистики відвідувань категорій");
        }
    }
}
