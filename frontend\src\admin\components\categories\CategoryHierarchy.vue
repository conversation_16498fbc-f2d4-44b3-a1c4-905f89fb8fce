<template>
  <div class="modern-hierarchy-container">
    <!-- Empty State -->
    <div v-if="categories.length === 0" class="hierarchy-empty">
      <div class="empty-state">
        <i class="fas fa-folder-open"></i>
        <h3>No categories found</h3>
        <p>Create your first category to get started with organizing your products.</p>
      </div>
    </div>

    <!-- Hierarchy Content -->
    <div v-else class="hierarchy-content">
      <CategoryHierarchyItem
        v-for="category in rootCategories"
        :key="category.id"
        :category="category"
        :level="0"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @add-child="$emit('add-child', $event)"
        @view-products="$emit('view-products', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import CategoryHierarchyItem from './CategoryHierarchyItem.vue';

const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  }
});

defineEmits(['edit', 'delete', 'add-child', 'view-products']);

// Build hierarchy recursively
const buildHierarchy = (categories, parentId = null) => {
  return categories
    .filter(cat => cat.parentId === parentId)
    .map(cat => ({
      ...cat,
      children: buildHierarchy(categories, cat.id)
    }));
};

const rootCategories = computed(() => {
  return buildHierarchy(props.categories);
});
</script>

<style scoped>
/* Modern Hierarchy Container */
.modern-hierarchy-container {
  min-height: 400px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
}

/* Hierarchy Content */
.hierarchy-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Empty State */
.hierarchy-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-state {
  text-align: center;
  color: #6b7280;
}

.empty-state i {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  margin: 0;
  color: #9ca3af;
  font-size: 1rem;
}
</style>
