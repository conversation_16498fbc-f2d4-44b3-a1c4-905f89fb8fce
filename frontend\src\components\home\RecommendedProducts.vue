<template>
  <section class="recommended-products-section">
    <h2 class="section-title">{{ title }}</h2>
    <ProductGrid
      :fetch-params="fetchParams"
      :grid-columns="gridColumns"
      :empty-message="emptyMessage"
      @product-added-to-cart="$emit('product-added-to-cart', $event)"
      @product-added-to-wishlist="$emit('product-added-to-wishlist', $event)"
    />
  </section>
</template>

<script>
import ProductGrid from '@/components/common/ProductGrid.vue';

export default {
  name: 'RecommendedProducts',
  components: {
    ProductGrid
  },
  props: {
    title: {
      type: String,
      default: 'Рекомендації на основі ваших переглядів'
    },
    fetchParams: {
      type: Object,
      default: () => ({ type: 'recommended', status: 1, pageSize: 8 })
    },
    gridColumns: {
      type: Number,
      default: 4
    },
    emptyMessage: {
      type: String,
      default: 'Рекомендовані товари поки що недоступні'
    }
  }
}
</script>

<style scoped>
.recommended-products-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #000;
  text-align: left;
}
</style>