using Marketplace.Domain.Services;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Infrastructure.Services;

/// <summary>
/// Універсальна реалізація сервісу для роботи з зображеннями
/// </summary>
public class ImageService : IImageService
{
    private readonly IFileService _fileService;
    private readonly IProductRepository _productRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IUserRepository _userRepository;
    private readonly IProductImageRepository _productImageRepository;
    private readonly ILogger<ImageService> _logger;
    private readonly string _baseUrl;
    private readonly MarketplaceDbContext _context;

    public ImageService(
        IFileService fileService,
        IProductRepository productRepository,
        ICategoryRepository categoryRepository,
        ICompanyRepository companyRepository,
        IUserRepository userRepository,
        IProductImageRepository productImageRepository,
        IConfiguration configuration,
        ILogger<ImageService> logger,
        MarketplaceDbContext context)
    {
        _fileService = fileService;
        _productRepository = productRepository;
        _categoryRepository = categoryRepository;
        _companyRepository = companyRepository;
        _userRepository = userRepository;
        _productImageRepository = productImageRepository;
        _logger = logger;
        _baseUrl = configuration["Frontend:BaseUrl"] ?? "http://localhost:5000";
        _context = context;
    }

    public async Task<FileUploadResult> UploadImageAsync(
        string entityType, 
        Guid entityId, 
        IFormFile file, 
        string imageType = "main",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation($"Uploading {imageType} image for {entityType} {entityId}");

            // Завантажуємо файл
            var fileUrl = await _fileService.SaveFileAsync(
                file.OpenReadStream(),
                file.FileName,
                file.ContentType,
                $"images/{entityType.ToLower()}/{entityId}",
                cancellationToken);

            // Формуємо повний URL для зображення
            var fullImageUrl = fileUrl.StartsWith("http") ? fileUrl : $"{_baseUrl}/uploads/{fileUrl}";

            _logger.LogInformation($"File URL from service: {fileUrl}");
            _logger.LogInformation($"Base URL: {_baseUrl}");
            _logger.LogInformation($"Full image URL: {fullImageUrl}");

            // Оновлюємо сутність в залежності від типу
            await UpdateEntityImageAsync(entityType, entityId, fullImageUrl, imageType, cancellationToken);

            return new FileUploadResult
            {
                Id = Guid.NewGuid(),
                FileUrl = fullImageUrl,
                FileName = file.FileName,
                ContentType = file.ContentType,
                Size = file.Length
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error uploading {imageType} image for {entityType} {entityId}");
            throw;
        }
    }

    public async Task<List<FileUploadResult>> UploadMultipleImagesAsync(
        string entityType, 
        Guid entityId, 
        IFormFileCollection files,
        CancellationToken cancellationToken = default)
    {
        var results = new List<FileUploadResult>();

        if (entityType.ToLower() != "product")
        {
            throw new InvalidOperationException("Multiple images are only supported for products");
        }

        // Перевіряємо, чи продукт існує
        var product = await _productRepository.GetByIdAsync(entityId, cancellationToken);
        if (product == null)
        {
            throw new InvalidOperationException($"Product with ID {entityId} not found");
        }

        // Отримуємо поточні зображення для визначення порядку
        var existingImages = await _productImageRepository.GetByProductIdAsync(entityId, cancellationToken);
        var nextOrder = existingImages.Any() ? existingImages.Max(pi => pi.Order) + 1 : 0;
        var hasMainImage = existingImages.Any(pi => pi.IsMain);

        foreach (var file in files)
        {
            if (file == null || file.Length == 0) continue;

            try
            {
                // Завантажуємо файл
                var fileUrl = await _fileService.SaveFileAsync(
                    file.OpenReadStream(),
                    file.FileName,
                    file.ContentType,
                    $"images/products/{entityId}",
                    cancellationToken);

                var fullImageUrl = $"{_baseUrl}/uploads/{fileUrl}";

                // Створюємо нове зображення продукту
                var productImage = new ProductImage(
                    entityId,
                    new Url(fullImageUrl),
                    nextOrder,
                    !hasMainImage && results.Count == 0, // Перше зображення стає головним, якщо немає головного
                    file.FileName
                );

                await _productImageRepository.AddAsync(productImage, cancellationToken);

                results.Add(new FileUploadResult
                {
                    Id = productImage.Id,
                    FileUrl = fullImageUrl,
                    FileName = file.FileName,
                    ContentType = file.ContentType,
                    Size = file.Length
                });

                nextOrder++;
                hasMainImage = true; // Після першого зображення вже є головне
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading file {file.FileName} for product {entityId}");
                // Продовжуємо з наступним файлом
            }
        }

        return results;
    }

    public async Task<bool> DeleteImageAsync(
        string entityType, 
        Guid entityId, 
        string imageType = "main",
        Guid? imageId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation($"Deleting {imageType} image for {entityType} {entityId}");

            if (entityType.ToLower() == "product" && imageType == "collection" && imageId.HasValue)
            {
                // Видаляємо конкретне зображення з колекції ProductImage
                var productImage = await _productImageRepository.GetByIdAsync(imageId.Value, cancellationToken);
                if (productImage == null) return false;

                // Видаляємо файл
                await _fileService.DeleteFileAsync(productImage.Image.Value, cancellationToken);
                
                // Видаляємо запис з БД
                await _productImageRepository.DeleteAsync(productImage.Id, cancellationToken);
                return true;
            }

            // Для інших типів зображень
            return await DeleteEntityImageAsync(entityType, entityId, imageType, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting {imageType} image for {entityType} {entityId}");
            return false;
        }
    }

    public async Task<FileUploadResult> UpdateImageAsync(
        string entityType, 
        Guid entityId, 
        IFormFile file, 
        string imageType = "main",
        Guid? imageId = null,
        CancellationToken cancellationToken = default)
    {
        // Спочатку видаляємо старе зображення
        await DeleteImageAsync(entityType, entityId, imageType, imageId, cancellationToken);
        
        // Потім завантажуємо нове
        return await UploadImageAsync(entityType, entityId, file, imageType, cancellationToken);
    }

    public async Task<string?> GetImageUrlAsync(
        string entityType, 
        Guid entityId, 
        string imageType = "main",
        Guid? imageId = null,
        CancellationToken cancellationToken = default)
    {
        return await GetEntityImageUrlAsync(entityType, entityId, imageType, imageId, cancellationToken);
    }

    public async Task<List<object>> GetAllImagesAsync(
        string entityType, 
        Guid entityId,
        CancellationToken cancellationToken = default)
    {
        var images = new List<object>();

        switch (entityType.ToLower())
        {
            case "product":
                var productImages = await _productImageRepository.GetByProductIdAsync(entityId, cancellationToken);
                images.AddRange(productImages.Select(pi => new
                {
                    Id = pi.Id,
                    Url = pi.Image.Value,
                    Order = pi.Order,
                    IsMain = pi.IsMain,
                    AltText = pi.AltText,
                    CreatedAt = pi.CreatedAt,
                    Type = "collection"
                }));

                // Додаємо мета-зображення
                var product = await _productRepository.GetByIdAsync(entityId, cancellationToken);
                if (product?.Meta.Image != null)
                {
                    images.Add(new
                    {
                        Id = Guid.Empty,
                        Url = product.Meta.Image.Value,
                        Order = -1,
                        IsMain = false,
                        AltText = "Meta image",
                        CreatedAt = DateTime.MinValue,
                        Type = "meta"
                    });
                }
                break;

            case "category":
                var category = await _categoryRepository.GetByIdAsync(entityId, cancellationToken);
                if (category != null)
                {
                    if (category.Image != null)
                    {
                        images.Add(new
                        {
                            Id = Guid.Empty,
                            Url = category.Image.Value,
                            Order = 0,
                            IsMain = true,
                            AltText = "Category image",
                            CreatedAt = DateTime.MinValue,
                            Type = "main"
                        });
                    }
                    if (category.Meta.Image != null)
                    {
                        images.Add(new
                        {
                            Id = Guid.Empty,
                            Url = category.Meta.Image.Value,
                            Order = -1,
                            IsMain = false,
                            AltText = "Meta image",
                            CreatedAt = DateTime.MinValue,
                            Type = "meta"
                        });
                    }
                }
                break;

            case "company":
                var company = await _companyRepository.GetByIdAsync(entityId, cancellationToken);
                if (company != null)
                {
                    images.Add(new
                    {
                        Id = Guid.Empty,
                        Url = company.Image.Value,
                        Order = 0,
                        IsMain = true,
                        AltText = "Company image",
                        CreatedAt = DateTime.MinValue,
                        Type = "main"
                    });
                    if (company.Meta?.Image != null)
                    {
                        images.Add(new
                        {
                            Id = Guid.Empty,
                            Url = company.Meta.Image.Value,
                            Order = -1,
                            IsMain = false,
                            AltText = "Meta image",
                            CreatedAt = DateTime.MinValue,
                            Type = "meta"
                        });
                    }
                }
                break;

            case "user":
                var user = await _userRepository.GetByIdAsync(entityId, cancellationToken);
                if (user?.AvatarUrl != null)
                {
                    images.Add(new
                    {
                        Id = Guid.Empty,
                        Url = user.AvatarUrl,
                        Order = 0,
                        IsMain = true,
                        AltText = "User avatar",
                        CreatedAt = DateTime.MinValue,
                        Type = "avatar"
                    });
                }
                break;
        }

        return images;
    }

    public async Task<bool> SetMainImageAsync(
        string entityType, 
        Guid entityId, 
        Guid imageId,
        CancellationToken cancellationToken = default)
    {
        if (entityType.ToLower() != "product")
        {
            throw new InvalidOperationException("Setting main image is only supported for products");
        }

        try
        {
            // Отримуємо всі зображення продукту
            var productImages = await _productImageRepository.GetByProductIdAsync(entityId, cancellationToken);
            
            // Знімаємо позначку головного з усіх зображень
            foreach (var image in productImages)
            {
                image.Update(isMain: false);
                await _productImageRepository.UpdateAsync(image, cancellationToken);
            }

            // Встановлюємо нове головне зображення
            var mainImage = productImages.FirstOrDefault(pi => pi.Id == imageId);
            if (mainImage != null)
            {
                mainImage.Update(isMain: true);
                await _productImageRepository.UpdateAsync(mainImage, cancellationToken);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error setting main image {imageId} for product {entityId}");
            return false;
        }
    }

    #region Private Methods

    private async Task UpdateEntityImageAsync(
        string entityType,
        Guid entityId,
        string imageUrl,
        string imageType,
        CancellationToken cancellationToken)
    {
        switch (entityType.ToLower())
        {
            case "product":
                if (imageType == "meta")
                {
                    await UpdateProductMetaImageAsync(entityId, imageUrl, cancellationToken);
                }
                else
                {
                    await AddProductImageAsync(entityId, imageUrl, cancellationToken);
                }
                break;

            case "category":
                if (imageType == "meta")
                {
                    await UpdateCategoryMetaImageAsync(entityId, imageUrl, cancellationToken);
                }
                else
                {
                    await UpdateCategoryImageAsync(entityId, imageUrl, cancellationToken);
                }
                break;

            case "company":
                if (imageType == "meta")
                {
                    await UpdateCompanyMetaImageAsync(entityId, imageUrl, cancellationToken);
                }
                else
                {
                    await UpdateCompanyImageAsync(entityId, imageUrl, cancellationToken);
                }
                break;

            case "user":
                await UpdateUserAvatarAsync(entityId, imageUrl, cancellationToken);
                break;

            default:
                throw new InvalidOperationException($"Unknown entity type: {entityType}");
        }
    }

    private async Task<bool> DeleteEntityImageAsync(
        string entityType,
        Guid entityId,
        string imageType,
        CancellationToken cancellationToken)
    {
        switch (entityType.ToLower())
        {
            case "product":
                if (imageType == "meta")
                {
                    return await DeleteProductMetaImageAsync(entityId, cancellationToken);
                }
                break;

            case "category":
                if (imageType == "meta")
                {
                    return await DeleteCategoryMetaImageAsync(entityId, cancellationToken);
                }
                else
                {
                    return await DeleteCategoryImageAsync(entityId, cancellationToken);
                }

            case "company":
                if (imageType == "meta")
                {
                    return await DeleteCompanyMetaImageAsync(entityId, cancellationToken);
                }
                break;

            case "user":
                return await DeleteUserAvatarAsync(entityId, cancellationToken);

            default:
                throw new InvalidOperationException($"Unknown entity type: {entityType}");
        }

        return false;
    }

    private async Task<string?> GetEntityImageUrlAsync(
        string entityType,
        Guid entityId,
        string imageType,
        Guid? imageId,
        CancellationToken cancellationToken)
    {
        switch (entityType.ToLower())
        {
            case "product":
                if (imageType == "meta")
                {
                    var product = await _productRepository.GetByIdAsync(entityId, cancellationToken);
                    return product?.Meta.Image?.Value;
                }
                else if (imageType == "collection" && imageId.HasValue)
                {
                    var productImage = await _productImageRepository.GetByIdAsync(imageId.Value, cancellationToken);
                    return productImage?.Image.Value;
                }
                break;

            case "category":
                var category = await _categoryRepository.GetByIdAsync(entityId, cancellationToken);
                if (imageType == "meta")
                {
                    return category?.Meta.Image?.Value;
                }
                else
                {
                    return category?.Image?.Value;
                }

            case "company":
                var company = await _companyRepository.GetByIdAsync(entityId, cancellationToken);
                if (imageType == "meta")
                {
                    return company?.Meta.Image?.Value;
                }
                else
                {
                    return company?.Image.Value;
                }

            case "user":
                var user = await _userRepository.GetByIdAsync(entityId, cancellationToken);
                return user?.AvatarUrl;

            default:
                throw new InvalidOperationException($"Unknown entity type: {entityType}");
        }

        return null;
    }

    // Product methods
    private async Task UpdateProductMetaImageAsync(Guid productId, string imageUrl, CancellationToken cancellationToken)
    {
        var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
        if (product == null)
            throw new InvalidOperationException($"Product with ID {productId} not found");

        _logger.LogInformation($"Updating product {productId} meta image to '{imageUrl}'");
        _logger.LogInformation($"Product loaded: Name='{product.Name}', Description='{product.Description}'");

        try
        {
            // Перевіряємо, чи існує Meta, якщо ні - створюємо з дефолтними значеннями
            string metaTitle;
            string metaDescription;
            string oldMetaImageUrl = null;

            if (product.Meta != null)
            {
                _logger.LogInformation($"Product has existing Meta: Title='{product.Meta.Title}', Description='{product.Meta.Description}'");
                metaTitle = product.Meta.Title;
                metaDescription = product.Meta.Description;
                oldMetaImageUrl = product.Meta.Image?.Value;
            }
            else
            {
                _logger.LogInformation("Product has no Meta, creating default values");
                metaTitle = product.Name;
                metaDescription = product.Description ?? "Product description";
            }

            _logger.LogInformation($"Using meta: Title='{metaTitle}', Description='{metaDescription}', OldImage='{oldMetaImageUrl}'");

            var updatedMeta = new Meta(
                metaTitle,
                metaDescription,
                new Url(imageUrl));

            _logger.LogInformation("Created new Meta object, calling UpdateMeta...");
            product.UpdateMeta(updatedMeta);
            _logger.LogInformation("UpdateMeta completed successfully");

            // Спробуємо пряме оновлення через SQL, якщо EF не може оновити Meta
            try
            {
                await _productRepository.UpdateAsync(product, cancellationToken);
                _logger.LogInformation("Product updated successfully through repository");
            }
            catch (Exception efEx)
            {
                _logger.LogWarning(efEx, "Failed to update through EF, trying direct SQL update");

                // Пряме оновлення через SQL
                await _context.Database.ExecuteSqlRawAsync(
                    "UPDATE \"Products\" SET \"MetaTitle\" = {0}, \"MetaDescription\" = {1}, \"MetaImage\" = {2} WHERE \"Id\" = {3}",
                    metaTitle, metaDescription, imageUrl, productId);

                _logger.LogInformation("Product meta updated successfully through direct SQL");
            }

            _logger.LogInformation($"Product {productId} meta image successfully updated in database from '{oldMetaImageUrl}' to '{imageUrl}'");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating product {productId} meta image: {ex.Message}");
            throw;
        }
    }

    private async Task AddProductImageAsync(Guid productId, string imageUrl, CancellationToken cancellationToken)
    {
        var existingImages = await _productImageRepository.GetByProductIdAsync(productId, cancellationToken);
        var nextOrder = existingImages.Any() ? existingImages.Max(pi => pi.Order) + 1 : 0;
        var hasMainImage = existingImages.Any(pi => pi.IsMain);

        var productImage = new ProductImage(
            productId,
            new Url(imageUrl),
            nextOrder,
            !hasMainImage
        );

        await _productImageRepository.AddAsync(productImage, cancellationToken);
    }

    private async Task<bool> DeleteProductMetaImageAsync(Guid productId, CancellationToken cancellationToken)
    {
        var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
        if (product?.Meta?.Image == null) return false;

        _logger.LogInformation($"Deleting product {productId} meta image: '{product.Meta.Image.Value}'");

        try
        {
            await _fileService.DeleteFileAsync(product.Meta.Image.Value, cancellationToken);

            var updatedMeta = new Meta(
                product.Meta.Title,
                product.Meta.Description,
                null);

            product.UpdateMeta(updatedMeta);
            await _productRepository.UpdateAsync(product, cancellationToken);

            _logger.LogInformation($"Product {productId} meta image successfully deleted");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting product {productId} meta image: {ex.Message}");
            throw;
        }
    }

    // Category methods
    private async Task UpdateCategoryImageAsync(Guid categoryId, string imageUrl, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Category with ID {categoryId} not found");

        category.UpdateImage(new Url(imageUrl));
        await _categoryRepository.UpdateAsync(category, cancellationToken);
    }

    private async Task UpdateCategoryMetaImageAsync(Guid categoryId, string imageUrl, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Category with ID {categoryId} not found");

        _logger.LogInformation($"Updating category {categoryId} meta image to '{imageUrl}'");
        _logger.LogInformation($"Category loaded: Name='{category.Name}', Description='{category.Description}'");

        try
        {
            // Перевіряємо, чи існує Meta, якщо ні - створюємо з дефолтними значеннями
            string metaTitle;
            string metaDescription;
            string oldMetaImageUrl = null;

            if (category.Meta != null)
            {
                _logger.LogInformation($"Category has existing Meta: Title='{category.Meta.Title}', Description='{category.Meta.Description}'");
                metaTitle = category.Meta.Title;
                metaDescription = category.Meta.Description;
                oldMetaImageUrl = category.Meta.Image?.Value;
            }
            else
            {
                _logger.LogInformation("Category has no Meta, creating default values");
                metaTitle = category.Name;
                metaDescription = category.Description ?? "Category description";
            }

            _logger.LogInformation($"Using meta: Title='{metaTitle}', Description='{metaDescription}', OldImage='{oldMetaImageUrl}'");

            var updatedMeta = new Meta(
                metaTitle,
                metaDescription,
                new Url(imageUrl));

            _logger.LogInformation("Created new Meta object, calling UpdateMeta...");
            category.UpdateMeta(updatedMeta);
            _logger.LogInformation("UpdateMeta completed successfully");

            // Спробуємо пряме оновлення через SQL, якщо EF не може оновити Meta
            try
            {
                await _categoryRepository.UpdateAsync(category, cancellationToken);
                _logger.LogInformation("Category updated successfully through repository");
            }
            catch (Exception efEx)
            {
                _logger.LogWarning(efEx, "Failed to update through EF, trying direct SQL update");

                // Пряме оновлення через SQL
                await _context.Database.ExecuteSqlRawAsync(
                    "UPDATE \"Categories\" SET \"MetaTitle\" = {0}, \"MetaDescription\" = {1}, \"MetaImage\" = {2} WHERE \"Id\" = {3}",
                    metaTitle, metaDescription, imageUrl, categoryId);

                _logger.LogInformation("Category meta updated successfully through direct SQL");
            }

            _logger.LogInformation($"Category {categoryId} meta image successfully updated in database from '{oldMetaImageUrl}' to '{imageUrl}'");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating category {categoryId} meta image: {ex.Message}");
            throw;
        }
    }

    private async Task<bool> DeleteCategoryImageAsync(Guid categoryId, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category?.Image == null) return false;

        await _fileService.DeleteFileAsync(category.Image.Value, cancellationToken);
        category.UpdateImage((Url?)null);
        await _categoryRepository.UpdateAsync(category, cancellationToken);
        return true;
    }

    private async Task<bool> DeleteCategoryMetaImageAsync(Guid categoryId, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category?.Meta?.Image == null) return false;

        _logger.LogInformation($"Deleting category {categoryId} meta image: '{category.Meta.Image.Value}'");

        try
        {
            await _fileService.DeleteFileAsync(category.Meta.Image.Value, cancellationToken);

            var updatedMeta = new Meta(
                category.Meta.Title,
                category.Meta.Description,
                null);

            category.UpdateMeta(updatedMeta);
            await _categoryRepository.UpdateAsync(category, cancellationToken);

            _logger.LogInformation($"Category {categoryId} meta image successfully deleted");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting category {categoryId} meta image: {ex.Message}");
            throw;
        }
    }

    // Company methods
    private async Task UpdateCompanyImageAsync(Guid companyId, string imageUrl, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Company with ID {companyId} not found");

        _logger.LogInformation($"Updating company {companyId} image from '{company.Image?.Value}' to '{imageUrl}'");

        var oldImageUrl = company.Image?.Value;
        company.UpdateImage(new Url(imageUrl));

        _logger.LogInformation($"Company image after update: '{company.Image?.Value}'");

        // Явно позначаємо сутність як змінену для EF
        _context.Entry(company).Property(c => c.Image).IsModified = true;
        _logger.LogInformation($"Marked Image property as modified for company {companyId}");

        await _companyRepository.UpdateAsync(company, cancellationToken);

        _logger.LogInformation($"Company {companyId} image successfully updated in database from '{oldImageUrl}' to '{imageUrl}'");
    }

    private async Task UpdateCompanyMetaImageAsync(Guid companyId, string imageUrl, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Company with ID {companyId} not found");

        _logger.LogInformation($"Updating company {companyId} meta image to '{imageUrl}'");
        _logger.LogInformation($"Company loaded: Name='{company.Name}', Description='{company.Description}'");

        try
        {
            // Перевіряємо, чи існує Meta, якщо ні - створюємо з дефолтними значеннями
            string metaTitle;
            string metaDescription;
            string oldMetaImageUrl = null;

            if (company.Meta != null)
            {
                _logger.LogInformation($"Company has existing Meta: Title='{company.Meta.Title}', Description='{company.Meta.Description}'");
                metaTitle = company.Meta.Title;
                metaDescription = company.Meta.Description;
                oldMetaImageUrl = company.Meta.Image?.Value;
            }
            else
            {
                _logger.LogInformation("Company has no Meta, creating default values");
                metaTitle = company.Name;
                metaDescription = company.Description ?? "Company description";
            }

            _logger.LogInformation($"Using meta: Title='{metaTitle}', Description='{metaDescription}', OldImage='{oldMetaImageUrl}'");

            var updatedMeta = new Meta(
                metaTitle,
                metaDescription,
                new Url(imageUrl));

            _logger.LogInformation("Created new Meta object, calling UpdateMeta...");
            company.UpdateMeta(updatedMeta);
            _logger.LogInformation("UpdateMeta completed successfully");

            // Спробуємо пряме оновлення через SQL, якщо EF не може оновити Meta
            try
            {
                await _companyRepository.UpdateAsync(company, cancellationToken);
                _logger.LogInformation("Company updated successfully through repository");
            }
            catch (Exception efEx)
            {
                _logger.LogWarning(efEx, "Failed to update through EF, trying direct SQL update");

                // Пряме оновлення через SQL
                await _context.Database.ExecuteSqlRawAsync(
                    "UPDATE \"Companies\" SET \"MetaTitle\" = {0}, \"MetaDescription\" = {1}, \"MetaImage\" = {2} WHERE \"Id\" = {3}",
                    metaTitle, metaDescription, imageUrl, companyId);

                _logger.LogInformation("Company meta updated successfully through direct SQL");
            }

            _logger.LogInformation($"Company {companyId} meta image successfully updated in database from '{oldMetaImageUrl}' to '{imageUrl}'");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating company {companyId} meta image: {ex.Message}");
            throw;
        }
    }

    private async Task<bool> DeleteCompanyMetaImageAsync(Guid companyId, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
        {
            _logger.LogInformation($"Company {companyId} not found");
            return false;
        }

        if (company.Meta?.Image == null)
        {
            _logger.LogInformation($"Company {companyId} has no meta image to delete");
            return false;
        }

        var oldMetaImageUrl = company.Meta.Image.Value;
        _logger.LogInformation($"Deleting company {companyId} meta image: '{oldMetaImageUrl}'");

        try
        {
            await _fileService.DeleteFileAsync(company.Meta.Image.Value, cancellationToken);
            _logger.LogInformation($"Meta image file '{oldMetaImageUrl}' successfully deleted");
        }
        catch (Exception ex)
        {
            _logger.LogWarning($"Failed to delete meta image file '{oldMetaImageUrl}': {ex.Message}");
            // Продовжуємо виконання, щоб очистити посилання в БД
        }

        var updatedMeta = new Meta(
            company.Meta.Title,
            company.Meta.Description,
            null);

        company.UpdateMeta(updatedMeta);

        // Явно позначаємо сутність як змінену для EF
        _context.Entry(company).Property(c => c.Meta).IsModified = true;
        _logger.LogInformation($"Marked Meta property as modified for company {companyId}");

        await _companyRepository.UpdateAsync(company, cancellationToken);

        _logger.LogInformation($"Company {companyId} meta image successfully removed from database");
        return true;
    }

    // User methods
    private async Task UpdateUserAvatarAsync(Guid userId, string imageUrl, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
        if (user == null)
            throw new InvalidOperationException($"User with ID {userId} not found");

        user.UpdateAvatarUrl(imageUrl);
        await _userRepository.UpdateAsync(user, cancellationToken);
    }

    private async Task<bool> DeleteUserAvatarAsync(Guid userId, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Attempting to delete avatar for user {userId}");

        var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning($"User {userId} not found");
            return false;
        }

        if (string.IsNullOrEmpty(user.AvatarUrl))
        {
            _logger.LogWarning($"User {userId} has no avatar to delete");
            return false;
        }

        _logger.LogInformation($"Deleting avatar file: {user.AvatarUrl}");
        await _fileService.DeleteFileAsync(user.AvatarUrl, cancellationToken);

        user.UpdateAvatarUrl(null);
        await _userRepository.UpdateAsync(user, cancellationToken);

        _logger.LogInformation($"Successfully deleted avatar for user {userId}");
        return true;
    }

    #endregion
}
