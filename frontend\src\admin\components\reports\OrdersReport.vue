<template>
  <div class="orders-report">
    <!-- Order Metrics Grid -->
    <div class="metrics-section">
      <h3 class="section-title">
        <i class="fas fa-shopping-cart"></i>
        Order Analytics
      </h3>
      
      <div class="metrics-grid">
        <div 
          v-for="metric in orderMetrics" 
          :key="metric.key"
          class="metric-card"
          :class="metric.trend"
        >
          <div class="metric-header">
            <div class="metric-icon">
              <i :class="metric.icon"></i>
            </div>
            <div class="metric-change" v-if="metric.changePercentage">
              <i :class="getChangeIcon(metric.changePercentage)"></i>
              <span>{{ Math.abs(metric.changePercentage).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="metric-content">
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ formatMetricValue(metric.value, metric.type) }}</div>
            <div class="metric-comparison" v-if="metric.previousValue">
              vs {{ formatMetricValue(metric.previousValue, metric.type) }} last period
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Trends Charts -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- Order Volume Trend -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Order Volume Trend</h3>
            <div class="chart-controls">
              <select v-model="chartPeriod" @change="updateChart" class="chart-select">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </div>
          
          <div class="chart-content">
            <canvas ref="volumeChartCanvas" id="volume-chart"></canvas>
          </div>
        </div>

        <!-- Order Status Distribution -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Order Status</h3>
          </div>
          
          <div class="chart-content">
            <canvas ref="statusChartCanvas" id="status-chart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Status Overview -->
    <div class="status-section">
      <h3 class="section-title">
        <i class="fas fa-tasks"></i>
        Order Status Overview
      </h3>
      
      <div class="status-grid">
        <div 
          v-for="status in orderStatuses" 
          :key="status.status"
          class="status-card"
          :class="status.status.toLowerCase().replace(' ', '-')"
        >
          <div class="status-icon">
            <i :class="status.icon"></i>
          </div>
          <div class="status-info">
            <div class="status-label">{{ status.status }}</div>
            <div class="status-count">{{ formatNumber(status.count) }}</div>
            <div class="status-percentage">{{ status.percentage.toFixed(1) }}%</div>
          </div>
          <div class="status-trend" v-if="status.trend">
            <i :class="getTrendIcon(status.trend)"></i>
            <span>{{ Math.abs(status.trend).toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Value Analysis -->
    <div class="value-section">
      <h3 class="section-title">
        <i class="fas fa-chart-bar"></i>
        Order Value Analysis
      </h3>
      
      <div class="value-grid">
        <!-- Average Order Value -->
        <div class="value-card">
          <div class="value-header">
            <h4>Average Order Value</h4>
            <i class="fas fa-calculator"></i>
          </div>
          <div class="value-content">
            <div class="value-amount">{{ formatCurrency(averageOrderValue) }}</div>
            <div class="value-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label">This Month:</span>
                <span class="breakdown-value">{{ formatCurrency(currentMonthAOV) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label">Last Month:</span>
                <span class="breakdown-value">{{ formatCurrency(lastMonthAOV) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Value Distribution -->
        <div class="value-card">
          <div class="value-header">
            <h4>Order Value Distribution</h4>
            <i class="fas fa-chart-pie"></i>
          </div>
          <div class="value-ranges">
            <div 
              v-for="range in orderValueRanges" 
              :key="range.range"
              class="value-range"
            >
              <div class="range-label">{{ range.range }}</div>
              <div class="range-bar">
                <div 
                  class="range-fill" 
                  :style="{ width: range.percentage + '%' }"
                ></div>
              </div>
              <div class="range-stats">
                <span class="range-count">{{ range.count }} orders</span>
                <span class="range-percentage">{{ range.percentage.toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Methods -->
        <div class="value-card">
          <div class="value-header">
            <h4>Payment Methods</h4>
            <i class="fas fa-credit-card"></i>
          </div>
          <div class="payment-methods">
            <div 
              v-for="method in paymentMethods" 
              :key="method.method"
              class="payment-method"
            >
              <div class="method-icon">
                <i :class="method.icon"></i>
              </div>
              <div class="method-info">
                <div class="method-name">{{ method.method }}</div>
                <div class="method-count">{{ formatNumber(method.count) }} orders</div>
                <div class="method-percentage">{{ method.percentage.toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Fulfillment -->
    <div class="fulfillment-section">
      <h3 class="section-title">
        <i class="fas fa-shipping-fast"></i>
        Order Fulfillment
      </h3>
      
      <div class="fulfillment-grid">
        <!-- Processing Time -->
        <div class="fulfillment-card">
          <div class="fulfillment-header">
            <h4>Average Processing Time</h4>
            <i class="fas fa-clock"></i>
          </div>
          <div class="processing-stats">
            <div class="processing-time">
              <span class="time-value">{{ averageProcessingTime }}</span>
              <span class="time-label">hours</span>
            </div>
            <div class="processing-breakdown">
              <div class="breakdown-item">
                <span class="breakdown-label">Same Day:</span>
                <span class="breakdown-value">{{ sameDayProcessing }}%</span>
              </div>
              <div class="breakdown-item">
                <span class="breakdown-label">Next Day:</span>
                <span class="breakdown-value">{{ nextDayProcessing }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Delivery Performance -->
        <div class="fulfillment-card">
          <div class="fulfillment-header">
            <h4>Delivery Performance</h4>
            <i class="fas fa-truck"></i>
          </div>
          <div class="delivery-stats">
            <div class="delivery-rate">
              <span class="rate-value">{{ onTimeDeliveryRate }}%</span>
              <span class="rate-label">on-time delivery</span>
            </div>
            <div class="delivery-breakdown">
              <div class="delivery-item">
                <span class="delivery-period">Early:</span>
                <span class="delivery-percentage">{{ earlyDelivery }}%</span>
              </div>
              <div class="delivery-item">
                <span class="delivery-period">On Time:</span>
                <span class="delivery-percentage">{{ onTimeDelivery }}%</span>
              </div>
              <div class="delivery-item">
                <span class="delivery-period">Late:</span>
                <span class="delivery-percentage">{{ lateDelivery }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Insights -->
    <div class="insights-section">
      <h3 class="section-title">
        <i class="fas fa-lightbulb"></i>
        Order Insights
      </h3>
      
      <div class="insights-grid">
        <div 
          v-for="insight in orderInsights" 
          :key="insight.id"
          class="insight-card"
          :class="insight.type"
        >
          <div class="insight-icon">
            <i :class="insight.icon"></i>
          </div>
          <div class="insight-content">
            <div class="insight-title">{{ insight.title }}</div>
            <div class="insight-description">{{ insight.description }}</div>
            <div class="insight-action" v-if="insight.action">
              <button class="action-btn" @click="handleInsightAction(insight)">
                {{ insight.action }}
              </button>
            </div>
          </div>
          <div class="insight-priority">
            <div class="priority-indicator" :class="`priority-${insight.priority}`">
              {{ insight.priority }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Orders Table -->
    <div class="orders-table-section">
      <h3 class="section-title">
        <i class="fas fa-table"></i>
        Order Details
      </h3>
      
      <div class="table-controls">
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input 
            v-model="orderSearch" 
            type="text" 
            placeholder="Search orders..."
            class="search-input"
          />
        </div>
        
        <div class="filter-controls">
          <select v-model="statusFilter" class="filter-select">
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
          </select>
          
          <select v-model="paymentFilter" class="filter-select">
            <option value="">All Payment Methods</option>
            <option value="card">Credit Card</option>
            <option value="paypal">PayPal</option>
            <option value="bank">Bank Transfer</option>
          </select>
        </div>
      </div>
      
      <div class="table-container">
        <table class="orders-table">
          <thead>
            <tr>
              <th @click="sortBy('orderNumber')" class="sortable">
                Order #
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('customer')" class="sortable">
                Customer
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('date')" class="sortable">
                Date
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('total')" class="sortable">
                Total
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('items')" class="sortable">
                Items
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th @click="sortBy('status')" class="sortable">
                Status
                <i class="fas fa-sort sort-icon"></i>
              </th>
              <th>Payment</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in paginatedOrders" :key="order.id" class="order-row">
              <td>
                <span class="order-number">{{ order.orderNumber }}</span>
              </td>
              <td>
                <div class="customer-cell">
                  <div class="customer-name">{{ order.customer }}</div>
                  <div class="customer-email">{{ order.customerEmail }}</div>
                </div>
              </td>
              <td>
                <span class="order-date">{{ formatDate(order.date) }}</span>
              </td>
              <td>
                <span class="order-total">{{ formatCurrency(order.total) }}</span>
              </td>
              <td>
                <span class="items-count">{{ order.items }} items</span>
              </td>
              <td>
                <span class="status-badge" :class="order.status">
                  {{ order.status }}
                </span>
              </td>
              <td>
                <span class="payment-method">{{ order.paymentMethod }}</span>
              </td>
              <td>
                <div class="action-buttons">
                  <button @click="viewOrder(order)" class="action-btn view-btn">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button @click="editOrder(order)" class="action-btn edit-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="currentPage = 1" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-left"></i>
        </button>
        <button 
          @click="currentPage--" 
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          <i class="fas fa-angle-left"></i>
        </button>
        
        <span class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        
        <button 
          @click="currentPage++" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-right"></i>
        </button>
        <button 
          @click="currentPage = totalPages" 
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          <i class="fas fa-angle-double-right"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { reportsService } from '@/services/reports.service'

export default {
  name: 'OrdersReport',
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // Refs
    const volumeChartCanvas = ref(null)
    const statusChartCanvas = ref(null)
    const chartPeriod = ref('daily')
    const orderSearch = ref('')
    const statusFilter = ref('')
    const paymentFilter = ref('')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const sortField = ref('date')
    const sortDirection = ref('desc')

    // Chart instances
    let volumeChart = null
    let statusChart = null

    // Computed properties
    const orderMetrics = computed(() => {
      return props.data?.metrics?.items || []
    })

    const orderInsights = computed(() => {
      return props.data?.insights || []
    })

    const ordersData = computed(() => {
      return props.data?.table?.data || []
    })

    const orderStatuses = computed(() => {
      const statuses = [
        { status: 'Pending', icon: 'fas fa-clock', filter: (order) => order.status === 'pending' },
        { status: 'Processing', icon: 'fas fa-cog', filter: (order) => order.status === 'processing' },
        { status: 'Shipped', icon: 'fas fa-shipping-fast', filter: (order) => order.status === 'shipped' },
        { status: 'Delivered', icon: 'fas fa-check-circle', filter: (order) => order.status === 'delivered' },
        { status: 'Cancelled', icon: 'fas fa-times-circle', filter: (order) => order.status === 'cancelled' }
      ]

      const total = ordersData.value.length

      return statuses.map(status => {
        const count = ordersData.value.filter(status.filter).length

        return {
          ...status,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0,
          trend: Math.random() * 20 - 10 // Mock trend data
        }
      })
    })

    const averageOrderValue = computed(() => {
      const orders = ordersData.value
      if (orders.length === 0) return 0

      const total = orders.reduce((sum, order) => sum + (order.total || 0), 0)
      return total / orders.length
    })

    const currentMonthAOV = computed(() => {
      return averageOrderValue.value * 1.05 // Mock current month being 5% higher
    })

    const lastMonthAOV = computed(() => {
      return averageOrderValue.value * 0.95 // Mock last month being 5% lower
    })

    const orderValueRanges = computed(() => {
      const ranges = [
        { range: '< ₴500', min: 0, max: 500 },
        { range: '₴500 - ₴1,500', min: 500, max: 1500 },
        { range: '₴1,500 - ₴3,000', min: 1500, max: 3000 },
        { range: '₴3,000 - ₴5,000', min: 3000, max: 5000 },
        { range: '> ₴5,000', min: 5000, max: Infinity }
      ]

      const total = ordersData.value.length

      return ranges.map(range => {
        const count = ordersData.value.filter(order => {
          const total = order.total || 0
          return total >= range.min && total < range.max
        }).length

        return {
          ...range,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const paymentMethods = computed(() => {
      const methods = [
        { method: 'Credit Card', icon: 'fas fa-credit-card', filter: (order) => order.paymentMethod === 'card' },
        { method: 'PayPal', icon: 'fab fa-paypal', filter: (order) => order.paymentMethod === 'paypal' },
        { method: 'Bank Transfer', icon: 'fas fa-university', filter: (order) => order.paymentMethod === 'bank' }
      ]

      const total = ordersData.value.length

      return methods.map(method => {
        const count = ordersData.value.filter(method.filter).length

        return {
          ...method,
          count,
          percentage: total > 0 ? (count / total) * 100 : 0
        }
      })
    })

    const averageProcessingTime = computed(() => {
      return '18.5' // Mock average processing time in hours
    })

    const sameDayProcessing = computed(() => {
      return '45' // Mock percentage
    })

    const nextDayProcessing = computed(() => {
      return '35' // Mock percentage
    })

    const onTimeDeliveryRate = computed(() => {
      return '92.3' // Mock on-time delivery rate
    })

    const earlyDelivery = computed(() => {
      return '15' // Mock percentage
    })

    const onTimeDelivery = computed(() => {
      return '77' // Mock percentage
    })

    const lateDelivery = computed(() => {
      return '8' // Mock percentage
    })

    const filteredOrders = computed(() => {
      let filtered = ordersData.value

      // Apply search filter
      if (orderSearch.value) {
        const query = orderSearch.value.toLowerCase()
        filtered = filtered.filter(order =>
          order.orderNumber?.toLowerCase().includes(query) ||
          order.customer?.toLowerCase().includes(query) ||
          order.customerEmail?.toLowerCase().includes(query)
        )
      }

      // Apply status filter
      if (statusFilter.value) {
        filtered = filtered.filter(order => order.status === statusFilter.value)
      }

      // Apply payment filter
      if (paymentFilter.value) {
        filtered = filtered.filter(order => order.paymentMethod === paymentFilter.value)
      }

      // Apply sorting
      filtered.sort((a, b) => {
        const aVal = a[sortField.value]
        const bVal = b[sortField.value]

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return sortDirection.value === 'asc' ? aVal - bVal : bVal - aVal
        }

        if (sortField.value === 'date') {
          const aDate = new Date(aVal)
          const bDate = new Date(bVal)
          return sortDirection.value === 'asc' ? aDate - bDate : bDate - aDate
        }

        const aStr = String(aVal).toLowerCase()
        const bStr = String(bVal).toLowerCase()

        if (sortDirection.value === 'asc') {
          return aStr.localeCompare(bStr)
        } else {
          return bStr.localeCompare(aStr)
        }
      })

      return filtered
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredOrders.value.length / itemsPerPage.value)
    })

    const paginatedOrders = computed(() => {
      const start = (currentPage.value - 1) * itemsPerPage.value
      const end = start + itemsPerPage.value
      return filteredOrders.value.slice(start, end)
    })

    // Methods
    const formatMetricValue = (value, type) => {
      switch (type) {
        case 'currency':
          return reportsService.formatCurrency(value)
        case 'percentage':
          return reportsService.formatPercentage(value)
        case 'number':
          return reportsService.formatNumber(value)
        default:
          return String(value)
      }
    }

    const formatCurrency = (value) => {
      return reportsService.formatCurrency(value)
    }

    const formatNumber = (value) => {
      return reportsService.formatNumber(value)
    }

    const formatDate = (value) => {
      return new Date(value).toLocaleDateString('uk-UA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const getChangeIcon = (changePercentage) => {
      if (changePercentage > 0) return 'fas fa-arrow-up'
      if (changePercentage < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const getTrendIcon = (trend) => {
      if (trend > 0) return 'fas fa-arrow-up'
      if (trend < 0) return 'fas fa-arrow-down'
      return 'fas fa-minus'
    }

    const sortBy = (field) => {
      if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
      } else {
        sortField.value = field
        sortDirection.value = 'asc'
      }
      currentPage.value = 1
    }

    const updateChart = () => {
      createVolumeChart()
    }

    const viewOrder = (order) => {
      // TODO: Navigate to order details
      console.log('View order:', order)
    }

    const editOrder = (order) => {
      // TODO: Navigate to order edit
      console.log('Edit order:', order)
    }

    const handleInsightAction = (insight) => {
      // TODO: Handle insight actions
      console.log('Handle insight action:', insight)
    }

    const createVolumeChart = async () => {
      await nextTick()

      if (volumeChart) {
        volumeChart.destroy()
      }

      if (!volumeChartCanvas.value || !props.data?.charts?.primary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = volumeChartCanvas.value.getContext('2d')

        volumeChart = new Chart(ctx, {
          type: 'line',
          data: props.data.charts.primary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + formatNumber(context.parsed.y) + ' orders'
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: 'Orders'
                },
                ticks: {
                  callback: function(value) {
                    return formatNumber(value)
                  }
                }
              }
            },
            interaction: {
              mode: 'nearest',
              axis: 'x',
              intersect: false
            }
          }
        })
      } catch (error) {
        console.error('Error creating volume chart:', error)
      }
    }

    const createStatusChart = async () => {
      await nextTick()

      if (statusChart) {
        statusChart.destroy()
      }

      if (!statusChartCanvas.value || !props.data?.charts?.secondary) return

      try {
        const { Chart } = await import('chart.js/auto')
        const ctx = statusChartCanvas.value.getContext('2d')

        statusChart = new Chart(ctx, {
          type: 'doughnut',
          data: props.data.charts.secondary.data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const total = context.dataset.data.reduce((sum, value) => sum + value, 0)
                    const percentage = ((context.parsed / total) * 100).toFixed(1)
                    return context.label + ': ' + formatNumber(context.parsed) + ' orders (' + percentage + '%)'
                  }
                }
              }
            }
          }
        })
      } catch (error) {
        console.error('Error creating status chart:', error)
      }
    }

    // Watchers
    watch(() => props.data, () => {
      nextTick(() => {
        createVolumeChart()
        createStatusChart()
      })
    }, { deep: true })

    watch([orderSearch, statusFilter, paymentFilter], () => {
      currentPage.value = 1
    })

    // Lifecycle
    onMounted(() => {
      nextTick(() => {
        createVolumeChart()
        createStatusChart()
      })
    })

    onUnmounted(() => {
      if (volumeChart) {
        volumeChart.destroy()
      }
      if (statusChart) {
        statusChart.destroy()
      }
    })

    return {
      volumeChartCanvas,
      statusChartCanvas,
      chartPeriod,
      orderSearch,
      statusFilter,
      paymentFilter,
      currentPage,
      itemsPerPage,
      orderMetrics,
      orderInsights,
      orderStatuses,
      averageOrderValue,
      currentMonthAOV,
      lastMonthAOV,
      orderValueRanges,
      paymentMethods,
      averageProcessingTime,
      sameDayProcessing,
      nextDayProcessing,
      onTimeDeliveryRate,
      earlyDelivery,
      onTimeDelivery,
      lateDelivery,
      filteredOrders,
      totalPages,
      paginatedOrders,
      formatMetricValue,
      formatCurrency,
      formatNumber,
      formatDate,
      getChangeIcon,
      getTrendIcon,
      sortBy,
      updateChart,
      viewOrder,
      editOrder,
      handleInsightAction
    }
  }
}
</script>

<style scoped>
.orders-report {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Reuse styles from other reports */
.metrics-section,
.charts-section,
.insights-section,
.orders-table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
}

.metric-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.metric-card.positive {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #f8fafc 100%);
}

.metric-card.negative {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #f8fafc 100%);
}

.metric-card.neutral {
  border-left: 4px solid #6b7280;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.positive .metric-change {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.negative .metric-change {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.neutral .metric-change {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-comparison {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Charts */
.charts-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
}

.chart-container {
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
}

.chart-content {
  height: 300px;
  position: relative;
}

/* Status Grid */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.status-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.status-card.pending {
  border-left: 4px solid #f59e0b;
}

.status-card.processing {
  border-left: 4px solid #3b82f6;
}

.status-card.shipped {
  border-left: 4px solid #8b5cf6;
}

.status-card.delivered {
  border-left: 4px solid #10b981;
}

.status-card.cancelled {
  border-left: 4px solid #ef4444;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.pending .status-icon { background: #f59e0b; }
.processing .status-icon { background: #3b82f6; }
.shipped .status-icon { background: #8b5cf6; }
.delivered .status-icon { background: #10b981; }
.cancelled .status-icon { background: #ef4444; }

.status-info {
  flex: 1;
}

.status-label {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.status-count {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
}

.status-percentage {
  font-size: 0.875rem;
  color: #6b7280;
}

.status-trend {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
}

@media (max-width: 768px) {
  .metrics-grid,
  .status-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .chart-content {
    height: 250px;
  }

  .metric-value,
  .status-count {
    font-size: 1.5rem;
  }
}
</style>
