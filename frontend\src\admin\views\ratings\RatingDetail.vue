<template>
  <div class="rating-detail">
    <!-- Enhanced Breadcrumbs -->
    <nav class="breadcrumb has-arrow-separator" aria-label="breadcrumbs">
      <ul>
        <li><router-link to="/admin">Dashboard</router-link></li>
        <li><router-link to="/admin/ratings">Ratings</router-link></li>
        <li class="is-active">
          <a aria-current="page">
            {{ rating.id ? `Rating #${rating.id.substring(0, 8)}...` : 'Rating Details' }}
          </a>
        </li>
      </ul>
    </nav>

    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Rating Details</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button
              v-if="rating.productSlug"
              class="button is-link"
              @click="viewProduct"
              :disabled="actionLoading">
              <span class="icon"><i class="fas fa-external-link-alt"></i></span>
              <span>View Product</span>
            </button>
            <button class="button is-primary" @click="fetchRating" :class="{ 'is-loading': loading }">
              <span class="icon"><i class="fas fa-sync-alt"></i></span>
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="has-text-centered" v-if="loading && !rating.id">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-3x"></i>
      </span>
      <p class="mt-3">Loading rating details...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- Rating Details -->
    <div v-else-if="rating.id">
      <div class="columns">
        <!-- Main Info -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Rating Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Product</label>
                    <p class="info-value">
                      <router-link :to="{ name: 'AdminProductDetail', params: { id: rating.productId } }">
                        {{ rating.productName }}
                      </router-link>
                    </p>
                  </div>
                  <div class="field">
                    <label class="label">User</label>
                    <p class="info-value">
                      <router-link :to="{ name: 'AdminUserDetail', params: { id: rating.userId } }">
                        {{ rating.userName }}
                      </router-link>
                    </p>
                  </div>
                  <div class="field">
                    <label class="label">Overall Rating</label>
                    <p class="info-value">
                      <div class="stars">
                        <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= averageRating }">
                          ★
                        </span>
                        <span class="ml-2">({{ averageRating.toFixed(1) }}/5)</span>
                      </div>
                    </p>
                  </div>
                </div>
                <div class="column is-6">
                  <div class="field">
                    <label class="label">Created At</label>
                    <p class="info-value">{{ formatDateTime(rating.createdAt) }}</p>
                  </div>
                  <div class="field" v-if="rating.updatedAt">
                    <label class="label">Updated At</label>
                    <p class="info-value">{{ formatDateTime(rating.updatedAt) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Ratings -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Detailed Ratings</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column is-4">
                  <div class="field">
                    <label class="label">Service Quality</label>
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.service }">
                        ★
                      </span>
                      <span class="ml-2">({{ rating.service }}/5)</span>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="field">
                    <label class="label">Delivery Time</label>
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.deliveryTime }">
                        ★
                      </span>
                      <span class="ml-2">({{ rating.deliveryTime }}/5)</span>
                    </div>
                  </div>
                </div>
                <div class="column is-4">
                  <div class="field">
                    <label class="label">Product Accuracy</label>
                    <div class="stars">
                      <span v-for="i in 5" :key="i" class="star" :class="{ 'is-filled': i <= rating.accuracy }">
                        ★
                      </span>
                      <span class="ml-2">({{ rating.accuracy }}/5)</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="field" v-if="rating.comment">
                <label class="label">Comment</label>
                <div class="content">
                  <p>{{ rating.comment }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Actions</p>
            </div>
            <div class="card-content">
              <div class="buttons is-fullwidth">
                <button 
                  class="button is-primary is-fullwidth"
                  @click="showEditModal = true"
                  :disabled="actionLoading">
                  <span class="icon"><i class="fas fa-edit"></i></span>
                  <span>Edit Rating</span>
                </button>
                <button 
                  class="button is-danger is-fullwidth"
                  @click="showDeleteModal = true"
                  :disabled="actionLoading">
                  <span class="icon"><i class="fas fa-trash"></i></span>
                  <span>Delete Rating</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Rating Stats -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Statistics</p>
            </div>
            <div class="card-content">
              <div class="field">
                <label class="label">Average Rating</label>
                <p class="info-value">{{ averageRating.toFixed(2) }}/5</p>
              </div>
              <div class="field">
                <label class="label">Rating Breakdown</label>
                <div class="rating-breakdown">
                  <div class="rating-item">
                    <span class="rating-label">Service:</span>
                    <span class="rating-value">{{ rating.service }}/5</span>
                  </div>
                  <div class="rating-item">
                    <span class="rating-label">Delivery:</span>
                    <span class="rating-value">{{ rating.deliveryTime }}/5</span>
                  </div>
                  <div class="rating-item">
                    <span class="rating-label">Accuracy:</span>
                    <span class="rating-value">{{ rating.accuracy }}/5</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Rating Modal -->
    <div class="modal" :class="{ 'is-active': showEditModal }">
      <div class="modal-background" @click="closeEditModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Edit Rating</p>
          <button class="delete" @click="closeEditModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Service Quality (1-5)</label>
            <div class="control">
              <input 
                class="input" 
                type="number" 
                v-model.number="editedRating.service" 
                min="1" 
                max="5">
            </div>
          </div>
          <div class="field">
            <label class="label">Delivery Time (1-5)</label>
            <div class="control">
              <input 
                class="input" 
                type="number" 
                v-model.number="editedRating.deliveryTime" 
                min="1" 
                max="5">
            </div>
          </div>
          <div class="field">
            <label class="label">Product Accuracy (1-5)</label>
            <div class="control">
              <input 
                class="input" 
                type="number" 
                v-model.number="editedRating.accuracy" 
                min="1" 
                max="5">
            </div>
          </div>
          <div class="field">
            <label class="label">Comment</label>
            <div class="control">
              <textarea 
                class="textarea" 
                v-model="editedRating.comment" 
                placeholder="Enter rating comment..."
                rows="3"
                maxlength="500">
              </textarea>
            </div>
            <p class="help">{{ (editedRating.comment || '').length }}/500 characters</p>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-primary" 
            @click="updateRating"
            :class="{ 'is-loading': actionLoading }"
            :disabled="!isValidRating">
            Save Changes
          </button>
          <button class="button" @click="closeEditModal">Cancel</button>
        </footer>
      </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="showDeleteModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Delete Rating</p>
          <button class="delete" @click="showDeleteModal = false"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete this rating? This action cannot be undone.</p>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger" 
            @click="deleteRating"
            :class="{ 'is-loading': actionLoading }">
            Delete Rating
          </button>
          <button class="button" @click="showDeleteModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ratingsService from '@/admin/services/ratings';

const route = useRoute();
const router = useRouter();

// Reactive data
const rating = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showDeleteModal = ref(false);
const showEditModal = ref(false);
const editedRating = ref({
  service: 1,
  deliveryTime: 1,
  accuracy: 1,
  comment: ''
});

// Computed
const averageRating = computed(() => {
  if (!rating.value.service) return 0;
  return (rating.value.service + rating.value.deliveryTime + rating.value.accuracy) / 3;
});

const isValidRating = computed(() => {
  return editedRating.value.service >= 1 && editedRating.value.service <= 5 &&
         editedRating.value.deliveryTime >= 1 && editedRating.value.deliveryTime <= 5 &&
         editedRating.value.accuracy >= 1 && editedRating.value.accuracy <= 5 &&
         (editedRating.value.comment || '').length <= 500;
});

// Methods
const fetchRating = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await ratingsService.getRatingById(route.params.id);
    rating.value = response;
  } catch (err) {
    error.value = err.message || 'Failed to load rating details';
  } finally {
    loading.value = false;
  }
};

const viewProduct = () => {
  if (rating.value.productSlug) {
    // Відкриваємо сторінку продукту в новій вкладці
    window.open(`/products/${rating.value.productSlug}`, '_blank');
  }
};

const updateRating = async () => {
  actionLoading.value = true;
  try {
    await ratingsService.updateRating(rating.value.id, editedRating.value);
    // Refresh rating data
    await fetchRating();
    closeEditModal();
  } catch (err) {
    error.value = err.message || 'Failed to update rating';
  } finally {
    actionLoading.value = false;
  }
};

const deleteRating = async () => {
  actionLoading.value = true;
  try {
    await ratingsService.deleteRating(rating.value.id);
    router.push({ name: 'AdminRatings' });
  } catch (err) {
    error.value = err.message || 'Failed to delete rating';
  } finally {
    actionLoading.value = false;
    showDeleteModal.value = false;
  }
};

const closeEditModal = () => {
  showEditModal.value = false;
  editedRating.value = {
    service: rating.value.service || 1,
    deliveryTime: rating.value.deliveryTime || 1,
    accuracy: rating.value.accuracy || 1,
    comment: rating.value.comment || ''
  };
};

// Utility methods
const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
  fetchRating();
});

// Watch for rating changes to update editedRating
watch(() => rating.value, (newRating) => {
  if (newRating && newRating.id && !showEditModal.value) {
    editedRating.value = {
      service: newRating.service || 1,
      deliveryTime: newRating.deliveryTime || 1,
      accuracy: newRating.accuracy || 1,
      comment: newRating.comment || ''
    };
  }
}, { immediate: true, deep: true });
</script>

<style scoped>
.rating-detail {
  padding: 1rem;
}

.info-value {
  font-weight: 500;
  color: #363636;
}

.stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 1.2em;
}

.star.is-filled {
  color: #ffd700;
}

.content p {
  white-space: pre-wrap;
}

.rating-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating-label {
  font-weight: 500;
}

.rating-value {
  color: #ffd700;
  font-weight: bold;
}
</style>
