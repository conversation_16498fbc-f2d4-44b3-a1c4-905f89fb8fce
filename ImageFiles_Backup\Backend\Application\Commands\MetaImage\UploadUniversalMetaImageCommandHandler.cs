using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.MetaImage;

/// <summary>
/// Обробник універсальної команди завантаження мета-зображення
/// </summary>
public class UploadUniversalMetaImageCommandHandler : IRequestHandler<UploadUniversalMetaImageCommand, FileUploadResult>
{
    private readonly IImageService _imageService;
    private readonly ILogger<UploadUniversalMetaImageCommandHandler> _logger;

    public UploadUniversalMetaImageCommandHandler(
        IImageService imageService,
        ILogger<UploadUniversalMetaImageCommandHandler> logger)
    {
        _imageService = imageService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadUniversalMetaImageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Uploading meta image for {request.EntityType} {request.EntityId}");

        try
        {
            var result = await _imageService.UploadImageAsync(
                request.EntityType,
                request.EntityId,
                request.File,
                "meta",
                cancellationToken);

            _logger.LogInformation($"Successfully uploaded meta image for {request.EntityType} {request.EntityId}");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to upload meta image for {request.EntityType} {request.EntityId}");
            throw;
        }
    }
}
