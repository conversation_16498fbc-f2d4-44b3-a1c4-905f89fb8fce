using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Infrastructure.Persistence;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using AutoMapper;

namespace Marketplace.Application.Queries.Address;

public class GetAllAddressesQueryHandler : IRequestHandler<GetAllAddressesQuery, PagedResponse<AddressResponse>>
{
    private readonly MarketplaceDbContext _context;
    private readonly IMapper _mapper;

    public GetAllAddressesQueryHandler(MarketplaceDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<PagedResponse<AddressResponse>> Handle(GetAllAddressesQuery request, CancellationToken cancellationToken)
    {
        // Створюємо фільтр
        Expression<Func<Domain.Entities.Address, bool>>? filter = null;

        // Створюємо список умов фільтрації
        var filterConditions = new List<Expression<Func<Domain.Entities.Address, bool>>>();

        // Спрощений текстовий фільтр - тільки по адресі
        if (!string.IsNullOrEmpty(request.Filter))
        {
            filterConditions.Add(a => a.AddressVO.Region.Contains(request.Filter) ||
                    a.AddressVO.City.Contains(request.Filter) ||
                    a.AddressVO.Street.Contains(request.Filter) ||
                    a.AddressVO.PostalCode.Contains(request.Filter));
        }

        // Фільтр по регіону
        if (!string.IsNullOrEmpty(request.Region))
        {
            filterConditions.Add(a => a.AddressVO.Region.Contains(request.Region));
        }

        // Фільтр по місту
        if (!string.IsNullOrEmpty(request.City))
        {
            filterConditions.Add(a => a.AddressVO.City.Contains(request.City));
        }

        // Фільтр по поштовому коду
        if (!string.IsNullOrEmpty(request.PostalCode))
        {
            filterConditions.Add(a => a.AddressVO.PostalCode.Contains(request.PostalCode));
        }

        // Фільтр по користувачу (ID)
        if (request.UserId.HasValue)
        {
            filterConditions.Add(a => a.UserId == request.UserId.Value);
        }

        // Фільтр по користувачу (Email) - спрощений
        if (!string.IsNullOrEmpty(request.UserEmail))
        {
            filterConditions.Add(a => a.UserId != null);
        }

        // Фільтр по активності (базується на наявності замовлень)
        if (request.IsActive.HasValue)
        {
            if (request.IsActive.Value)
            {
                filterConditions.Add(a => _context.Orders.Any(o => o.ShippingAddressId == a.Id));
            }
            else
            {
                filterConditions.Add(a => !_context.Orders.Any(o => o.ShippingAddressId == a.Id));
            }
        }

        // Фільтр по даті створення (після)
        if (request.CreatedAfter.HasValue)
        {
            filterConditions.Add(a => a.CreatedAt >= request.CreatedAfter.Value);
        }

        // Фільтр по даті створення (до)
        if (request.CreatedBefore.HasValue)
        {
            filterConditions.Add(a => a.CreatedAt <= request.CreatedBefore.Value);
        }

        // Об'єднуємо всі умови через AND
        if (filterConditions.Any())
        {
            filter = filterConditions.Aggregate((expr1, expr2) => 
                Expression.Lambda<Func<Domain.Entities.Address, bool>>(
                    Expression.AndAlso(expr1.Body, expr2.Body),
                    expr1.Parameters));
        }

        // Створюємо запит
        var query = _context.Addresses
            .Include(a => a.User)
            .AsQueryable();

        // Застосовуємо фільтр
        if (filter != null)
        {
            query = query.Where(filter);
        }

        // Сортування
        if (!string.IsNullOrEmpty(request.OrderBy))
        {
            query = request.OrderBy.ToLower() switch
            {
                "region" => request.Descending ? query.OrderByDescending(a => a.AddressVO.Region) : query.OrderBy(a => a.AddressVO.Region),
                "city" => request.Descending ? query.OrderByDescending(a => a.AddressVO.City) : query.OrderBy(a => a.AddressVO.City),
                "street" => request.Descending ? query.OrderByDescending(a => a.AddressVO.Street) : query.OrderBy(a => a.AddressVO.Street),
                "postalcode" => request.Descending ? query.OrderByDescending(a => a.AddressVO.PostalCode) : query.OrderBy(a => a.AddressVO.PostalCode),
                "username" => request.Descending ? query.OrderByDescending(a => a.User != null ? a.User.Username : "") : query.OrderBy(a => a.User != null ? a.User.Username : ""),
                "createdat" => request.Descending ? query.OrderByDescending(a => a.CreatedAt) : query.OrderBy(a => a.CreatedAt),
                "updatedat" => request.Descending ? query.OrderByDescending(a => a.UpdatedAt) : query.OrderBy(a => a.UpdatedAt),
                _ => request.Descending ? query.OrderByDescending(a => a.CreatedAt) : query.OrderBy(a => a.CreatedAt)
            };
        }
        else
        {
            query = request.Descending ? query.OrderByDescending(a => a.CreatedAt) : query.OrderBy(a => a.CreatedAt);
        }

        // Пагінація
        var totalCount = await query.CountAsync(cancellationToken);
        var page = request.Page ?? 1;
        var pageSize = request.PageSize ?? 10;
        var skip = (page - 1) * pageSize;

        var addresses = await query
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        // Отримуємо статистику використання для кожної адреси
        var addressIds = addresses.Select(a => a.Id).ToList();
        var orderStats = await _context.Orders
            .Where(o => addressIds.Contains(o.ShippingAddressId))
            .GroupBy(o => o.ShippingAddressId)
            .Select(g => new { AddressId = g.Key, Count = g.Count(), LastUsed = g.Max(o => o.CreatedAt) })
            .ToListAsync(cancellationToken);

        // Мапимо результати
        var addressResponses = addresses.Select(address =>
        {
            var stats = orderStats.FirstOrDefault(s => s.AddressId == address.Id);
            return new AddressResponse(
                id: address.Id,
                region: address.AddressVO.Region,
                city: address.AddressVO.City,
                street: address.AddressVO.Street,
                postalCode: address.AddressVO.PostalCode,
                userId: address.UserId,
                userName: address.User?.Username ?? string.Empty,
                userEmail: address.User?.Email?.Value ?? string.Empty,
                createdAt: address.CreatedAt,
                updatedAt: address.UpdatedAt,
                ordersCount: stats?.Count ?? 0,
                lastUsedAt: stats?.LastUsed,
                isActive: stats != null
            );
        }).ToList();

        return new PagedResponse<AddressResponse>
        {
            Data = addressResponses,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
}
