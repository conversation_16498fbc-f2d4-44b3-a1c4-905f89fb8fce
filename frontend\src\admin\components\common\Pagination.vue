<template>
  <nav class="modern-pagination" role="navigation" aria-label="pagination">
    <!-- Previous Button -->
    <button
      class="pagination-btn pagination-prev"
      :disabled="currentPage <= 1"
      @click="goToPage(currentPage - 1)"
    >
      <i class="fas fa-chevron-left"></i>
      <span>Previous</span>
    </button>

    <!-- Page Numbers -->
    <div class="pagination-numbers">
      <!-- First Page -->
      <button
        v-if="showFirstPage"
        class="pagination-number"
        @click="goToPage(1)"
      >
        1
      </button>

      <!-- First Ellipsis -->
      <span v-if="showFirstEllipsis" class="pagination-ellipsis">...</span>

      <!-- Visible Pages -->
      <button
        v-for="page in visiblePages"
        :key="page"
        class="pagination-number"
        :class="{ 'active': page === currentPage }"
        @click="goToPage(page)"
      >
        {{ page }}
      </button>

      <!-- Last Ellipsis -->
      <span v-if="showLastEllipsis" class="pagination-ellipsis">...</span>

      <!-- Last Page -->
      <button
        v-if="showLastPage"
        class="pagination-number"
        @click="goToPage(totalPages)"
      >
        {{ totalPages }}
      </button>
    </div>

    <!-- Next Button -->
    <button
      class="pagination-btn pagination-next"
      :disabled="currentPage >= totalPages"
      @click="goToPage(currentPage + 1)"
    >
      <span>Next</span>
      <i class="fas fa-chevron-right"></i>
    </button>


  </nav>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  }
});

// Emits
const emit = defineEmits(['page-changed']);

// Computed
const visiblePages = computed(() => {
  const { currentPage, totalPages, maxVisiblePages } = props;
  const pages = [];
  
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
  
  // Adjust start page if we're near the end
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  
  return pages;
});

const showFirstPage = computed(() => {
  return props.totalPages > 1 && !visiblePages.value.includes(1);
});

const showLastPage = computed(() => {
  return props.totalPages > 1 && !visiblePages.value.includes(props.totalPages);
});

const showFirstEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2;
});

const showLastEllipsis = computed(() => {
  return showLastPage.value && visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1;
});

// Methods
const goToPage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-changed', page);
  }
};
</script>

<style scoped>
.modern-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-space-md, 1rem);
  margin: var(--admin-space-2xl, 2rem) 0;
  padding: var(--admin-space-lg, 1.5rem);
  background: var(--admin-white, white);
  border-radius: var(--admin-radius-xl, 12px);
  box-shadow: var(--admin-shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--admin-border-light, #f3f4f6);
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm, 0.5rem);
  padding: var(--admin-space-sm, 0.5rem) var(--admin-space-lg, 1rem);
  background: var(--admin-white, white);
  border: 1px solid var(--admin-border-color, #e5e7eb);
  border-radius: var(--admin-radius-md, 6px);
  color: var(--admin-gray-700, #374151);
  font-size: var(--admin-text-sm, 0.875rem);
  font-weight: var(--admin-font-medium, 500);
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
  text-decoration: none;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--admin-gray-50, #f9fafb);
  border-color: var(--admin-primary, #3b82f6);
  color: var(--admin-primary, #3b82f6);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--admin-gray-100, #f3f4f6);
  color: var(--admin-gray-400, #9ca3af);
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs, 0.25rem);
}

.pagination-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--admin-white, white);
  border: 1px solid var(--admin-border-color, #e5e7eb);
  border-radius: var(--admin-radius-md, 6px);
  color: var(--admin-gray-700, #374151);
  font-size: var(--admin-text-sm, 0.875rem);
  font-weight: var(--admin-font-medium, 500);
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
  text-decoration: none;
}

.pagination-number:hover {
  background: var(--admin-gray-50, #f9fafb);
  border-color: var(--admin-primary, #3b82f6);
  color: var(--admin-primary, #3b82f6);
  transform: translateY(-1px);
}

.pagination-number.active {
  background: var(--admin-primary, #3b82f6);
  border-color: var(--admin-primary, #3b82f6);
  color: var(--admin-white, white);
  box-shadow: var(--admin-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--admin-gray-400, #9ca3af);
  font-size: var(--admin-text-sm, 0.875rem);
}



/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .modern-pagination {
    flex-direction: column;
    gap: var(--admin-space-md, 1rem);
    padding: var(--admin-space-md, 1rem);
  }

  .pagination-btn {
    padding: var(--admin-space-xs, 0.25rem) var(--admin-space-sm, 0.5rem);
    font-size: var(--admin-text-xs, 0.75rem);
  }

  .pagination-number {
    width: 36px;
    height: 36px;
    font-size: var(--admin-text-xs, 0.75rem);
  }

  .pagination-ellipsis {
    width: 36px;
    height: 36px;
    font-size: var(--admin-text-xs, 0.75rem);
  }

  .pagination-info {
    position: static;
    transform: none;
    text-align: center;
  }

  .page-text {
    font-size: var(--admin-text-xs, 0.75rem);
  }
}

@media screen and (max-width: 480px) {
  .pagination-numbers {
    flex-wrap: wrap;
    justify-content: center;
  }

  .pagination-btn span {
    display: none;
  }
}
</style>
