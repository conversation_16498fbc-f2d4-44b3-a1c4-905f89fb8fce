<template>
  <div class="product-grid-demo">
    <div class="container">
      <h1>ProductGrid Demo - Всі режими</h1>
      
      <!-- Top Products -->
      <section class="demo-section">
        <h2>1. ТОП товари</h2>
        <ProductGrid
          :fetch-params="{ type: 'top', status: 1, pageSize: 4 }"
          :grid-columns="4"
          empty-message="ТОП товари недоступні"
        />
      </section>

      <!-- Recommended Products -->
      <section class="demo-section">
        <h2>2. Рекомендовані товари</h2>
        <ProductGrid
          :fetch-params="{ type: 'recommended', status: 1, pageSize: 4 }"
          :grid-columns="4"
          empty-message="Рекомендовані товари недоступні"
        />
      </section>

      <!-- Category Products -->
      <section class="demo-section">
        <h2>3. Товари з категорії (побутова техніка)</h2>
        <ProductGrid
          :fetch-params="{ categorySlug: 'category-af40cfeb', status: 1, pageSize: 6 }"
          :grid-columns="3"
          empty-message="Товари з категорії недоступні"
        />
      </section>

      <!-- Random Products -->
      <section class="demo-section">
        <h2>4. Рандомні товари з категорії</h2>
        <ProductGrid
          :fetch-params="{ categorySlug: 'category-af40cfeb', status: 1, count: 6, random: true }"
          :grid-columns="3"
          empty-message="Рандомні товари недоступні"
        />
      </section>

      <!-- Random Products with Different Category -->
      <section class="demo-section">
        <h2>5. Рандомні товари з іншої категорії</h2>
        <RandomProducts
          title="Випадкові товари (компонент)"
          category-slug="category-af40cfeb"
          :count="4"
          :grid-columns="4"
        />
      </section>

      <!-- All Products with Pagination -->
      <section class="demo-section">
        <h2>6. Всі товари з пагінацією</h2>
        <ProductGrid
          :fetch-params="{ status: 1, pageSize: 8 }"
          :grid-columns="4"
          :show-pagination="true"
          empty-message="Товари недоступні"
        />
      </section>
    </div>
  </div>
</template>

<script>
import ProductGrid from '@/components/common/ProductGrid.vue';
import RandomProducts from '@/components/home/<USER>';

export default {
  name: 'ProductGridDemo',
  components: {
    ProductGrid,
    RandomProducts
  }
}
</script>

<style scoped>
.product-grid-demo {
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1 {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
  font-size: 32px;
}

.demo-section {
  margin-bottom: 60px;
  padding: 30px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  background: #fafafa;
}

.demo-section h2 {
  margin-bottom: 20px;
  color: #444;
  font-size: 24px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}
</style>
