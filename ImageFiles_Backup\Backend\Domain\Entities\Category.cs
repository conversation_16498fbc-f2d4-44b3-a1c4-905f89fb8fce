﻿using Marketplace.Domain.ValueObjects;

namespace Marketplace.Domain.Entities;

public class Category : IEntity
{
    private string _name;
    private Slug _slug;
    private string _description;
    private Url? _image;
    private Guid? _parentId = null;
    private Meta _meta;
    public Guid Id { get; set; }
    public string Name
    {
        get => _name;
        set => UpdateName(value);
    }
    public Slug Slug
    {
        get => _slug;
        set => UpdateSlug(value);
    }
    public string Description
    {
        get => _description;
        set => UpdateDescription(value);
    }
    public Url? Image
    {
        get => _image;
        set => UpdateImage(value);
    }
    public Guid? ParentId
    {
        get => _parentId;
        set => UpdateParentId(value);
    }
    public Category? Parent { get; set; }
    public ICollection<Category>? Children { get; set; }
    public Meta Meta
    {
        get => _meta;
        set => UpdateMeta(value);
    }

    private Category() { } // Для EF Core

    public Category(string name, Slug slug, string description, Meta meta)
    {
        ValidateCreation(name, slug, description, meta);

        Id = Guid.NewGuid();
        Name = name;
        Slug = slug;
        Description = description;
        Meta = meta;
    }

    public Category(string name, Slug slug, string description, Guid? parentId, Meta meta)
    {
        ValidateCreation(name, slug, description, meta);

        Id = Guid.NewGuid();
        Name = name;
        Slug = slug;
        Description = description;
        ParentId = parentId;
        Meta = meta;
    }

    // Метод для часткового оновлення
    public void Update(
        string? name = null,
        Slug? slug = null,
        string? description = null,
        Url? image = null,
        Guid? parentId = null,
        string? metaTitle = null,
        string? metaDescription = null,
        Url? metaImage = null)
    {
        if (name != null) UpdateName(name);
        if (slug != null) UpdateSlug(slug);
        if (description != null) UpdateDescription(description);
        // Дозволяємо встановлювати Image в null
        UpdateImage(image);
        // Дозволяємо встановлювати ParentId в null
        UpdateParentId(parentId);

        if (metaTitle != null || metaDescription != null || metaImage != null)
        {
            UpdateMeta(new Meta(
                metaTitle ?? Meta.Title,
                metaDescription ?? Meta.Description,
                metaImage ?? Meta.Image));
        }
    }

    // Приватні методи для валідації та оновлення
    public void UpdateName(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
        if (name.Length > 100) throw new ArgumentException("Ім'я не має перевищувати 100 символів.", nameof(name));
        _name = name;
    }

    public void UpdateSlug(Slug slug)
    {
        _slug = slug ?? throw new ArgumentNullException(nameof(slug));
    }

    public void UpdateDescription(string description)
    {
        if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
        if (description.Length > 500) throw new ArgumentException("Опис не має перевищувати 500 символів.", nameof(description));
        _description = description;
    }

    public void UpdateImage(string image)
    {
        _image = new Url(image); // Дозволяємо null для зображення
    }
    public void UpdateImage(Url? image)
    {
        _image = image; // Дозволяємо null для зображення
    }

    public void UpdateParentId(Guid? parentId)
    {
        _parentId = parentId; // Дозволяємо null для кореневих категорій
    }

    public void UpdateMeta(Meta meta)
    {
        _meta = meta ?? throw new ArgumentNullException(nameof(meta));
    }

    public void ValidateCreation(string name, Slug slug, string description, Meta meta)
    {
        if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
        if (name.Length > 100) throw new ArgumentException("Ім'я не має перевищувати 100 символів.", nameof(name));
        if (slug == null) throw new ArgumentNullException(nameof(slug));
        if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
        if (description.Length > 500) throw new ArgumentException("Опис не має перевищувати 500 символів.", nameof(description));
        if (meta == null) throw new ArgumentNullException(nameof(meta));
    }
}

