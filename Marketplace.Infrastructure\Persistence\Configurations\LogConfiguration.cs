using Marketplace.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Marketplace.Infrastructure.Persistence.Configurations;

public class LogConfiguration : IEntityTypeConfiguration<Log>
{
    public void Configure(EntityTypeBuilder<Log> builder)
    {
        builder.HasKey(l => l.Id);

        builder.Property(l => l.Timestamp)
            .IsRequired();

        builder.Property(l => l.Level)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(l => l.Category)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(l => l.Message)
            .HasMaxLength(2000)
            .IsRequired();

        builder.Property(l => l.UserId);

        builder.Property(l => l.EntityId);

        builder.Property(l => l.EntityType)
            .HasMaxLength(100);

        builder.Property(l => l.AdditionalData)
            .HasMaxLength(4000);

        builder.Property(l => l.IpAddress)
            .HasMaxLength(45); // IPv6 max length

        builder.Property(l => l.UserAgent)
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(l => l.Timestamp);
        builder.HasIndex(l => l.Level);
        builder.HasIndex(l => l.Category);
        builder.HasIndex(l => l.UserId);

        // Foreign key relationship
        builder.HasOne(l => l.User)
            .WithMany()
            .HasForeignKey(l => l.UserId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
