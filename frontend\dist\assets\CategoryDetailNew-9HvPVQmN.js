import{_ as es,g as c,h as z,H as ts,i as as,f as os,c as l,b as m,a as s,k as f,t as i,d,z as ls,w as p,r as ns,n as h,F as R,p as H,x as is,y as rs,m as ds,I as k,e as cs,o as n}from"./index-L-hJxM_5.js";import{C as us}from"./ConfirmDialog-hd0r6dWx.js";const vs={class:"category-detail-new"},fs={key:0,class:"loading-state"},gs={key:1,class:"error-state"},ms={class:"error-message"},ps={key:2,class:"not-found-state"},bs={key:3,class:"category-content"},ys={class:"category-header"},_s={class:"header-content"},hs={class:"breadcrumb"},ks={class:"breadcrumb-item breadcrumb-current"},Cs={class:"category-title"},ws={class:"info-section"},Is={class:"info-grid"},Ds={class:"info-card info-card-main"},Ps={class:"info-card-content"},xs={class:"info-row"},Es={class:"info-item"},Ms={class:"info-value"},As={class:"info-item"},Ns={class:"info-value"},Ss={class:"info-row"},$s={class:"info-item"},Ts={class:"info-value info-value-code"},Us={key:0,class:"info-item"},Bs={key:0,class:"info-row"},Vs={class:"info-item info-item-full"},Fs={class:"info-value info-value-text"},Ls={class:"info-card"},zs={class:"info-card-content"},Rs={class:"category-image-wrapper"},Hs=["src","alt"],js={key:1,class:"no-image"},qs={key:0,class:"info-card"},Os={class:"info-card-content"},Gs={key:0,class:"info-item"},Js={class:"info-value"},Ks={key:1,class:"info-item"},Qs={class:"info-value info-value-text"},Ws={key:2,class:"info-item"},Xs={class:"meta-image-wrapper"},Ys=["src","alt"],Zs={class:"products-section"},se={class:"section-header"},ee={class:"section-title"},te={class:"section-actions"},ae=["disabled"],oe={key:0,class:"products-loading"},le={key:1,class:"no-products"},ne={key:2,class:"products-table-wrapper"},ie={class:"table-header"},re={class:"table-info"},de={class:"table-count"},ce={class:"table-actions"},ue=["disabled"],ve={class:"products-table"},fe={class:"table"},ge={class:"product-cell"},me={class:"product-info"},pe={key:0,class:"product-image"},be=["src","alt"],ye={class:"product-details"},_e={class:"product-name"},he={class:"product-sku"},ke={class:"price-cell"},Ce={class:"price-tag"},we={class:"stock-cell"},Ie={class:"status-cell"},De={class:"actions-cell"},Pe={class:"action-buttons"},xe={class:"modal-content"},Ee={class:"modal-body"},Me={class:"form-field"},Ae=["value"],Ne={class:"form-help"},Se={class:"modal-footer"},$e=["disabled"],Te={key:0,class:"fas fa-spinner fa-spin"},Ue={key:1,class:"fas fa-exchange-alt"},Be={class:"modal-content"},Ve={class:"modal-header"},Fe={class:"modal-title"},Le={class:"modal-body text-center"},ze={class:"image-viewer"},Re=["src","alt"],He={__name:"CategoryDetailNew",setup(je){const D=os(),T=cs(),P=c(!1),b=c(!1),u=c(""),a=c(null),v=c([]),y=c([]),x=c(!1),E=c(!1),g=c(""),_=c(!1),M=c(!1),A=c(""),C=c(""),U=z(()=>{var e;if(!((e=a.value)!=null&&e.parentId)||!y.value.length)return"";const t=y.value.find(r=>r.id===a.value.parentId);return(t==null?void 0:t.name)||"Unknown"}),B=z(()=>y.value.filter(t=>{var e;return t.id!==((e=a.value)==null?void 0:e.id)})),N=async t=>{P.value=!0,u.value="";try{const e=await k.getById(t);a.value=e,await Promise.all([S(e.id),j()])}catch(e){console.error("Error fetching category:",e),e.name!=="CanceledError"&&e.code!=="ERR_CANCELED"&&(u.value="Failed to load category. Please try again.")}finally{P.value=!1}},S=async t=>{b.value=!0;try{const e=await k.getCategoryProducts(t,{pageSize:50});v.value=e.data||[]}catch(e){console.error("Error fetching products:",e),v.value=[]}finally{b.value=!1}},j=async()=>{try{const t=await k.getAll({pageSize:1e3});y.value=t.data||[]}catch(t){console.error("Error fetching all categories:",t),y.value=[]}},q=()=>{var t;(t=a.value)!=null&&t.id&&S(a.value.id)},O=t=>t&&t.priceAmount&&t.priceCurrency?`${t.priceAmount} ${t.priceCurrency}`:t&&t.price?`${t.price} UAH`:"N/A",G=t=>t===0?"stock-badge-danger":t<10?"stock-badge-warning":"stock-badge-success",V=t=>{if(typeof t=="string")return t;switch(t){case 0:return"Draft";case 1:return"Active";case 2:return"Inactive";case 3:return"Pending";default:return"Unknown"}},J=t=>{if(!t||typeof t!="string")return"status-badge-light";switch(t.toLowerCase()){case"active":case"approved":return"status-badge-success";case"inactive":case"rejected":return"status-badge-danger";case"draft":case"pending":return"status-badge-warning";default:return"status-badge-light"}},K=t=>{t.target.style.display="none"},Q=t=>{t.target.style.display="none"},F=(t,e)=>{A.value=t,C.value=e,M.value=!0},L=()=>{M.value=!1,A.value="",C.value=""},W=t=>{t.target.style.display="none"},X=()=>{T.push(`/admin/categories/${a.value.id}/edit`)},Y=()=>{x.value=!0},$=()=>{x.value=!1},Z=async()=>{try{await k.delete(a.value.id),$(),T.push("/admin/categories")}catch(t){console.error("Error deleting category:",t),$(),t.response&&t.response.data&&t.response.data.message?u.value=t.response.data.message:u.value="Failed to delete category. Please try again."}},w=()=>{E.value=!1,g.value=""},ss=async()=>{var t,e;if(!(!g.value||!((t=a.value)!=null&&t.id))){_.value=!0;try{const r=await k.bulkUpdateProductsCategory(a.value.id,g.value);w();const I=((e=B.value.find(o=>o.id===g.value))==null?void 0:e.name)||"selected category";alert(`Successfully moved ${r.updatedCount} products to ${I}`),await S(a.value.id)}catch(r){console.error("Error updating products category:",r),r.response&&r.response.data&&r.response.data.message?u.value=r.response.data.message:u.value="Failed to move products. Please try again."}finally{_.value=!1}}};return ts(()=>D.params.id,t=>{t&&N(t)},{immediate:!0}),as(()=>{const t=D.params.id;t&&N(t)}),(t,e)=>{var I;const r=ns("router-link");return n(),l("div",vs,[P.value?(n(),l("div",fs,e[6]||(e[6]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Loading category details...",-1)]))):u.value?(n(),l("div",gs,[e[8]||(e[8]=s("div",{class:"error-icon"},[s("i",{class:"fas fa-exclamation-triangle"})],-1)),e[9]||(e[9]=s("h3",{class:"error-title"},"Error Loading Category",-1)),s("p",ms,i(u.value),1),s("button",{class:"retry-btn",onClick:e[0]||(e[0]=o=>N(ls(D).params.id))},e[7]||(e[7]=[s("i",{class:"fas fa-redo"},null,-1),d(" Try Again ")]))])):a.value?(n(),l("div",bs,[s("div",ys,[s("div",_s,[s("nav",hs,[m(r,{to:"/admin",class:"breadcrumb-item"},{default:p(()=>e[14]||(e[14]=[d("Dashboard")])),_:1}),e[16]||(e[16]=s("span",{class:"breadcrumb-separator"},"/",-1)),m(r,{to:"/admin/categories",class:"breadcrumb-item"},{default:p(()=>e[15]||(e[15]=[d("Categories")])),_:1}),e[17]||(e[17]=s("span",{class:"breadcrumb-separator"},"/",-1)),s("span",ks,i(a.value.name),1)]),s("h1",Cs,i(a.value.name),1),e[18]||(e[18]=s("p",{class:"category-subtitle"},"Category Details & Products",-1))]),s("div",{class:"header-actions"},[s("button",{class:"action-btn action-btn-primary",onClick:X},e[19]||(e[19]=[s("i",{class:"fas fa-edit"},null,-1),d(" Edit Category ")])),s("button",{class:"action-btn action-btn-danger",onClick:Y},e[20]||(e[20]=[s("i",{class:"fas fa-trash"},null,-1),d(" Delete ")]))])]),s("div",ws,[e[33]||(e[33]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},[s("i",{class:"fas fa-info-circle"}),d(" Category Information ")])],-1)),s("div",Is,[s("div",Ds,[e[26]||(e[26]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},"Basic Information")],-1)),s("div",Ps,[s("div",xs,[s("div",Es,[e[21]||(e[21]=s("label",{class:"info-label"},"ID",-1)),s("span",Ms,i(a.value.id),1)]),s("div",As,[e[22]||(e[22]=s("label",{class:"info-label"},"Name",-1)),s("span",Ns,i(a.value.name),1)])]),s("div",Ss,[s("div",$s,[e[23]||(e[23]=s("label",{class:"info-label"},"Slug",-1)),s("span",Ts,i(a.value.slug),1)]),U.value?(n(),l("div",Us,[e[24]||(e[24]=s("label",{class:"info-label"},"Parent Category",-1)),m(r,{to:`/admin/categories/${a.value.parentId}`,class:"info-value info-value-link"},{default:p(()=>[d(i(U.value),1)]),_:1},8,["to"])])):f("",!0)]),a.value.description?(n(),l("div",Bs,[s("div",Vs,[e[25]||(e[25]=s("label",{class:"info-label"},"Description",-1)),s("p",Fs,i(a.value.description),1)])])):f("",!0)])]),s("div",Ls,[e[28]||(e[28]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},"Category Image")],-1)),s("div",zs,[s("div",Rs,[a.value.image?(n(),l("img",{key:0,src:a.value.image,alt:a.value.name,class:"category-image clickable",onClick:e[1]||(e[1]=o=>F(a.value.image,a.value.name+" - Category Image")),onError:K,title:"Click to view full size"},null,40,Hs)):(n(),l("div",js,e[27]||(e[27]=[s("i",{class:"fas fa-image"},null,-1),s("p",null,"No Images",-1)])))])])]),a.value.metaTitle||a.value.metaDescription||a.value.metaImage?(n(),l("div",qs,[e[32]||(e[32]=s("div",{class:"info-card-header"},[s("h3",{class:"info-card-title"},[s("i",{class:"fas fa-search"}),d(" SEO Information ")])],-1)),s("div",Os,[a.value.metaTitle?(n(),l("div",Gs,[e[29]||(e[29]=s("label",{class:"info-label"},"Meta Title",-1)),s("span",Js,i(a.value.metaTitle),1)])):f("",!0),a.value.metaDescription?(n(),l("div",Ks,[e[30]||(e[30]=s("label",{class:"info-label"},"Meta Description",-1)),s("p",Qs,i(a.value.metaDescription),1)])):f("",!0),a.value.metaImage?(n(),l("div",Ws,[e[31]||(e[31]=s("label",{class:"info-label"},"Meta Image",-1)),s("div",Xs,[s("img",{src:a.value.metaImage,alt:a.value.name+" meta image",class:"meta-image clickable",onClick:e[2]||(e[2]=o=>F(a.value.metaImage,a.value.name+" - Meta Image")),onError:Q,title:"Click to view full size"},null,40,Ys)])])):f("",!0)])])):f("",!0)])]),s("div",Zs,[s("div",se,[s("h2",ee,[e[34]||(e[34]=s("i",{class:"fas fa-box"},null,-1)),d(" Products ("+i(v.value.length)+") ",1)]),s("div",te,[s("button",{class:"action-btn action-btn-secondary",onClick:q,disabled:b.value},[s("i",{class:h(["fas fa-sync-alt",{"fa-spin":b.value}])},null,2),e[35]||(e[35]=d(" Refresh "))],8,ae)])]),b.value?(n(),l("div",oe,e[36]||(e[36]=[s("div",{class:"loading-spinner"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("p",{class:"loading-text"},"Loading products...",-1)]))):v.value.length===0?(n(),l("div",le,e[37]||(e[37]=[s("div",{class:"no-products-icon"},[s("i",{class:"fas fa-box-open"})],-1),s("h3",{class:"no-products-title"},"No Products Found",-1),s("p",{class:"no-products-message"},"This category doesn't have any products yet.",-1)]))):(n(),l("div",ne,[s("div",ie,[s("div",re,[s("span",de,i(v.value.length)+" products in this category",1)]),s("div",ce,[s("button",{class:"action-btn action-btn-warning",onClick:e[3]||(e[3]=o=>E.value=!0),disabled:v.value.length===0},e[38]||(e[38]=[s("i",{class:"fas fa-exchange-alt"},null,-1),d(" Move All Products ")]),8,ue)])]),s("div",ve,[s("table",fe,[e[41]||(e[41]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Price"),s("th",null,"Stock"),s("th",null,"Status"),s("th",null,"Actions")])],-1)),s("tbody",null,[(n(!0),l(R,null,H(v.value,o=>(n(),l("tr",{key:o.id,class:"table-row"},[s("td",ge,[s("div",me,[o.image?(n(),l("div",pe,[s("img",{src:o.image,alt:o.name,onError:W},null,40,be)])):f("",!0),s("div",ye,[s("h4",_e,i(o.name),1),s("p",he,i(o.sku),1)])])]),s("td",ke,[s("span",Ce,i(O(o)),1)]),s("td",we,[s("span",{class:h(["stock-badge",G(o.stock)])},i(o.stock),3)]),s("td",Ie,[s("span",{class:h(["status-badge",J(V(o.status))])},i(V(o.status)),3)]),s("td",De,[s("div",Pe,[m(r,{to:`/admin/products/${o.id}/view`,class:"action-btn action-btn-sm action-btn-info",title:"View Product"},{default:p(()=>e[39]||(e[39]=[s("i",{class:"fas fa-eye"},null,-1)])),_:2},1032,["to"]),m(r,{to:`/admin/products/${o.id}/edit`,class:"action-btn action-btn-sm action-btn-warning",title:"Edit Product"},{default:p(()=>e[40]||(e[40]=[s("i",{class:"fas fa-edit"},null,-1)])),_:2},1032,["to"])])])]))),128))])])])]))])])):(n(),l("div",ps,[e[11]||(e[11]=s("div",{class:"not-found-icon"},[s("i",{class:"fas fa-folder-open"})],-1)),e[12]||(e[12]=s("h3",{class:"not-found-title"},"Category Not Found",-1)),e[13]||(e[13]=s("p",{class:"not-found-message"},"The requested category does not exist or has been deleted.",-1)),m(r,{to:"/admin/categories",class:"back-btn"},{default:p(()=>e[10]||(e[10]=[s("i",{class:"fas fa-arrow-left"},null,-1),d(" Back to Categories ")])),_:1})])),m(us,{"is-open":x.value,title:"Delete Category",message:`Are you sure you want to delete '${(I=a.value)==null?void 0:I.name}'? This action cannot be undone.`,"confirm-text":"Delete","confirm-button-class":"is-danger",onConfirm:Z,onCancel:$},null,8,["is-open","message"]),s("div",{class:h(["modal",{"modal-active":E.value}])},[s("div",{class:"modal-backdrop",onClick:w}),s("div",xe,[s("div",{class:"modal-header"},[e[43]||(e[43]=s("h3",{class:"modal-title"},"Move All Products to Another Category",-1)),s("button",{class:"modal-close",onClick:w},e[42]||(e[42]=[s("i",{class:"fas fa-times"},null,-1)]))]),s("div",Ee,[s("div",Me,[e[45]||(e[45]=s("label",{class:"form-label"},"Select Target Category",-1)),is(s("select",{"onUpdate:modelValue":e[4]||(e[4]=o=>g.value=o),class:"form-select"},[e[44]||(e[44]=s("option",{value:""},"Choose a category...",-1)),(n(!0),l(R,null,H(B.value,o=>(n(),l("option",{key:o.id,value:o.id},i(o.name),9,Ae))),128))],512),[[rs,g.value]]),s("p",Ne,"All "+i(v.value.length)+" products will be moved to the selected category.",1)])]),s("div",Se,[s("button",{class:h(["action-btn action-btn-warning",{"action-btn-loading":_.value}]),onClick:ss,disabled:!g.value||_.value},[_.value?(n(),l("i",Te)):(n(),l("i",Ue)),e[46]||(e[46]=d(" Move Products "))],10,$e),s("button",{class:"action-btn action-btn-secondary",onClick:w},"Cancel")])])],2),M.value?(n(),l("div",{key:4,class:"modal fade show d-block",onClick:L},[s("div",{class:"modal-dialog modal-lg",onClick:e[5]||(e[5]=ds(()=>{},["stop"]))},[s("div",Be,[s("div",Ve,[s("h5",Fe,[e[47]||(e[47]=s("i",{class:"fas fa-image me-2"},null,-1)),d(" "+i(C.value),1)]),s("button",{type:"button",class:"btn-close",onClick:L})]),s("div",Le,[s("div",ze,[s("img",{src:A.value,alt:C.value,class:"img-fluid rounded",style:{"max-height":"500px"}},null,8,Re)])])])])])):f("",!0)])}}},Ge=es(He,[["__scopeId","data-v-d6a3063a"]]);export{Ge as default};
