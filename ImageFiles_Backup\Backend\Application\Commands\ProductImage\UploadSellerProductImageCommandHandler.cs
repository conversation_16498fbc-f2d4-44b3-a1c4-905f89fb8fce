﻿﻿using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using Marketplace.Domain.ValueObjects;
using MediatR;

namespace Marketplace.Application.Commands.ProductImage;

public class UploadSellerProductImageCommandHandler : IRequestHandler<UploadSellerProductImageCommand, string>
{
    private readonly IProductImageRepository _productImageRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly IFileService _fileService;

    public UploadSellerProductImageCommandHandler(
        IProductImageRepository productImageRepository,
        IProductRepository productRepository,
        ICompanyUserRepository companyUserRepository,
        IFileService fileService)
    {
        _productImageRepository = productImageRepository;
        _productRepository = productRepository;
        _companyUserRepository = companyUserRepository;
        _fileService = fileService;
    }

    public async Task<string> Handle(UploadSellerProductImageCommand request, CancellationToken cancellationToken)
    {
        // Отримуємо компанії, до яких належить продавець
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.SellerId,
            cancellationToken: cancellationToken
        );

        if (!companyUsers.Any())
            throw new InvalidOperationException("Ви не є продавцем жодної компанії.");

        // Отримуємо ID компаній продавця
        var companyIds = companyUsers.Select(cu => cu.CompanyId).ToList();

        // Отримуємо продукт
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
            throw new InvalidOperationException($"Продукт з ID {request.ProductId} не знайдено.");

        // Перевіряємо, чи продукт належить компанії продавця
        if (!companyIds.Contains(product.CompanyId))
            throw new InvalidOperationException("Ви не маєте доступу до цього продукту.");

        // Перевіряємо, чи файл існує
        if (request.File == null || request.File.Length == 0)
            throw new InvalidOperationException("Файл не вибрано або він порожній.");

        // Завантажуємо файл
        var fileUrl = await _fileService.SaveFileAsync(
            request.File.OpenReadStream(),
            request.File.FileName,
            request.File.ContentType,
            $"products/{request.ProductId}",
            cancellationToken);

        // Створюємо нове зображення продукту
        var productImage = new Domain.Entities.ProductImage(
            request.ProductId,
            new Url(fileUrl)
        );

        // Встановлюємо порядок
        productImage.Update(order: request.Order);

        await _productImageRepository.AddAsync(productImage, cancellationToken);
        return fileUrl;
    }
}
