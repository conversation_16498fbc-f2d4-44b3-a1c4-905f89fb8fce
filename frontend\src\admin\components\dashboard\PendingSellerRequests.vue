<template>
  <div class="admin-card admin-pending-requests">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-store"></i>
        Pending Seller Requests
      </h3>
      <router-link to="/admin/seller-requests" class="admin-btn admin-btn-primary admin-btn-sm">
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </router-link>
    </div>
    <div class="admin-card-content">
      <div v-if="loading" class="admin-loading-state">
        <div class="admin-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-loading-text">Loading requests...</p>
      </div>
      <div v-else-if="!requests || requests.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-store"></i>
        </div>
        <p class="admin-empty-text">No pending seller requests</p>
      </div>
      <div v-else class="admin-requests-list">
        <div class="admin-request-item" v-for="request in requests" :key="request.id">
          <div class="admin-request-header">
            <div class="admin-seller-info">
              <h4 class="admin-seller-name">{{ request.userName }}</h4>
              <p class="admin-store-name">{{ request.companyName }}</p>
            </div>
            <div class="admin-request-date">
              {{ formatDate(request.createdAt) }}
            </div>
          </div>
          <div class="admin-request-actions">
            <router-link :to="`/admin/seller-requests/${request.id}`" class="admin-btn admin-btn-ghost admin-btn-sm">
              <i class="fas fa-eye"></i>
              <span>View</span>
            </router-link>
            <button
              class="admin-btn admin-btn-success admin-btn-sm"
              @click="approveRequest(request.id)">
              <i class="fas fa-check"></i>
              <span>Approve</span>
            </button>
            <button
              class="admin-btn admin-btn-danger admin-btn-sm"
              @click="rejectRequest(request.id)">
              <i class="fas fa-times"></i>
              <span>Reject</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  requests: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['approve', 'reject']);

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);

  // If less than a minute ago
  if (diffSec < 60) {
    return 'Just now';
  }

  // If less than an hour ago
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }

  // If less than a day ago
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }

  // If less than a week ago
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }

  // Otherwise, return formatted date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Approve request
const approveRequest = (id) => {
  emit('approve', id);
};

// Reject request
const rejectRequest = (id) => {
  emit('reject', id);
};
</script>

<style scoped>
.admin-pending-requests {
  height: 100%;
}

.admin-requests-list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
}

.admin-request-item {
  padding: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  background: var(--admin-gray-25);
  transition: all var(--admin-transition-fast);
}

.admin-request-item:hover {
  background: var(--admin-white);
  border-color: var(--admin-border-color);
  box-shadow: var(--admin-shadow-sm);
}

.admin-request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--admin-space-md);
}

.admin-seller-info {
  flex: 1;
}

.admin-seller-name {
  font-size: var(--admin-text-base);
  font-weight: var(--admin-font-semibold);
  margin-bottom: var(--admin-space-xs);
  color: var(--admin-gray-900);
  margin: 0 0 var(--admin-space-xs) 0;
}

.admin-store-name {
  font-size: var(--admin-text-sm);
  color: var(--admin-gray-600);
  font-weight: var(--admin-font-medium);
  margin: 0;
}

.admin-request-date {
  font-size: var(--admin-text-xs);
  color: var(--admin-gray-500);
  font-weight: var(--admin-font-medium);
  text-align: right;
}

.admin-request-actions {
  display: flex;
  gap: var(--admin-space-sm);
  flex-wrap: wrap;
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-600);
}

.admin-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-sm);
  margin: 0;
}

.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-lg);
}

.admin-empty-text {
  font-size: var(--admin-text-base);
  margin: 0;
  text-align: center;
}

@media (max-width: 768px) {
  .admin-request-header {
    flex-direction: column;
    gap: var(--admin-space-sm);
  }

  .admin-request-date {
    text-align: left;
  }

  .admin-request-actions {
    justify-content: stretch;
  }

  .admin-request-actions .admin-btn {
    flex: 1;
    min-width: 0;
  }
}
</style>
