<template>
  <div class="upload-progress-container">
    <!-- Загальний прогрес -->
    <div v-if="showOverallProgress" class="overall-progress mb-3">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <h6 class="mb-0">
          <i class="fas fa-cloud-upload-alt me-2"></i>
          Завантаження зображень
        </h6>
        <span class="badge bg-primary">
          {{ completedUploads }}/{{ totalUploads }}
        </span>
      </div>
      
      <div class="progress mb-2">
        <div 
          class="progress-bar"
          :class="overallProgressClass"
          :style="{ width: overallProgress + '%' }"
          role="progressbar"
        ></div>
      </div>
      
      <div class="d-flex justify-content-between">
        <small class="text-muted">{{ overallProgressText }}</small>
        <small class="text-muted">{{ overallProgress }}%</small>
      </div>
    </div>

    <!-- Індивідуальні прогреси -->
    <div class="individual-uploads">
      <div 
        v-for="upload in uploads" 
        :key="upload.id"
        class="upload-item"
        :class="{ 'upload-completed': upload.status === 'completed', 'upload-error': upload.status === 'error' }"
      >
        <div class="upload-header">
          <div class="upload-info">
            <i 
              class="upload-icon"
              :class="getUploadIcon(upload)"
            ></i>
            <div class="upload-details">
              <div class="upload-filename">{{ upload.filename }}</div>
              <div class="upload-meta">
                {{ formatFileSize(upload.size) }} • {{ upload.type }}
              </div>
            </div>
          </div>
          
          <div class="upload-actions">
            <button 
              v-if="upload.status === 'error' && upload.retryable"
              type="button"
              class="btn btn-sm btn-outline-primary"
              @click="retryUpload(upload.id)"
              :disabled="upload.retrying"
            >
              <i class="fas fa-redo"></i>
            </button>
            
            <button 
              v-if="upload.status !== 'completed'"
              type="button"
              class="btn btn-sm btn-outline-danger"
              @click="cancelUpload(upload.id)"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Прогрес бар -->
        <div v-if="upload.status === 'uploading'" class="upload-progress">
          <div class="progress">
            <div 
              class="progress-bar progress-bar-striped progress-bar-animated"
              :style="{ width: upload.progress + '%' }"
            ></div>
          </div>
          <div class="progress-details">
            <small class="text-muted">
              {{ upload.progress }}% • {{ formatSpeed(upload.speed) }}
            </small>
            <small class="text-muted">
              {{ formatTimeRemaining(upload.timeRemaining) }}
            </small>
          </div>
        </div>

        <!-- Статус -->
        <div v-else class="upload-status">
          <div 
            class="status-message"
            :class="getStatusClass(upload.status)"
          >
            {{ getStatusMessage(upload) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Дії -->
    <div v-if="uploads.length > 0" class="upload-actions-bar mt-3">
      <button 
        v-if="hasFailedUploads"
        type="button"
        class="btn btn-outline-primary btn-sm me-2"
        @click="retryAllFailed"
        :disabled="retryingAll"
      >
        <i class="fas fa-redo me-1"></i>
        Повторити невдалі
      </button>
      
      <button 
        v-if="hasActiveUploads"
        type="button"
        class="btn btn-outline-danger btn-sm me-2"
        @click="cancelAllUploads"
      >
        <i class="fas fa-stop me-1"></i>
        Зупинити всі
      </button>
      
      <button 
        type="button"
        class="btn btn-outline-secondary btn-sm"
        @click="clearCompleted"
      >
        <i class="fas fa-trash me-1"></i>
        Очистити завершені
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UploadProgress',
  props: {
    uploads: {
      type: Array,
      default: () => []
    },
    showOverallProgress: {
      type: Boolean,
      default: true
    }
  },
  emits: ['retry-upload', 'cancel-upload', 'retry-all-failed', 'cancel-all', 'clear-completed'],
  data() {
    return {
      retryingAll: false
    };
  },
  computed: {
    totalUploads() {
      return this.uploads.length;
    },
    
    completedUploads() {
      return this.uploads.filter(upload => upload.status === 'completed').length;
    },
    
    overallProgress() {
      if (this.totalUploads === 0) return 0;
      
      const totalProgress = this.uploads.reduce((sum, upload) => {
        if (upload.status === 'completed') return sum + 100;
        if (upload.status === 'uploading') return sum + upload.progress;
        return sum;
      }, 0);
      
      return Math.round(totalProgress / this.totalUploads);
    },
    
    overallProgressClass() {
      if (this.hasFailedUploads) return 'bg-warning';
      if (this.completedUploads === this.totalUploads) return 'bg-success';
      return 'bg-primary';
    },
    
    overallProgressText() {
      if (this.hasFailedUploads) return 'Деякі завантаження не вдалися';
      if (this.completedUploads === this.totalUploads) return 'Всі завантаження завершені';
      return 'Завантаження в процесі...';
    },
    
    hasFailedUploads() {
      return this.uploads.some(upload => upload.status === 'error');
    },
    
    hasActiveUploads() {
      return this.uploads.some(upload => 
        upload.status === 'uploading' || upload.status === 'pending'
      );
    }
  },
  methods: {
    getUploadIcon(upload) {
      switch (upload.status) {
        case 'completed':
          return 'fas fa-check-circle text-success';
        case 'error':
          return 'fas fa-exclamation-circle text-danger';
        case 'uploading':
          return 'fas fa-spinner fa-spin text-primary';
        case 'pending':
          return 'fas fa-clock text-muted';
        default:
          return 'fas fa-file-image text-muted';
      }
    },
    
    getStatusClass(status) {
      switch (status) {
        case 'completed':
          return 'text-success';
        case 'error':
          return 'text-danger';
        case 'pending':
          return 'text-muted';
        default:
          return 'text-muted';
      }
    },
    
    getStatusMessage(upload) {
      switch (upload.status) {
        case 'completed':
          return 'Завантажено успішно';
        case 'error':
          return upload.error || 'Помилка завантаження';
        case 'pending':
          return 'Очікує завантаження';
        case 'cancelled':
          return 'Скасовано';
        default:
          return 'Невідомий статус';
      }
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatSpeed(bytesPerSecond) {
      if (!bytesPerSecond) return '';
      return this.formatFileSize(bytesPerSecond) + '/s';
    },
    
    formatTimeRemaining(seconds) {
      if (!seconds || seconds === Infinity) return '';
      
      if (seconds < 60) {
        return `${Math.round(seconds)}с`;
      } else if (seconds < 3600) {
        return `${Math.round(seconds / 60)}хв`;
      } else {
        return `${Math.round(seconds / 3600)}год`;
      }
    },
    
    retryUpload(uploadId) {
      this.$emit('retry-upload', uploadId);
    },
    
    cancelUpload(uploadId) {
      this.$emit('cancel-upload', uploadId);
    },
    
    async retryAllFailed() {
      this.retryingAll = true;
      this.$emit('retry-all-failed');
      
      // Симулюємо затримку для UX
      setTimeout(() => {
        this.retryingAll = false;
      }, 1000);
    },
    
    cancelAllUploads() {
      this.$emit('cancel-all');
    },
    
    clearCompleted() {
      this.$emit('clear-completed');
    }
  }
};
</script>

<style scoped>
.upload-progress-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.upload-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  transition: all 0.3s;
}

.upload-item.upload-completed {
  border-color: #28a745;
  background-color: #f8fff9;
}

.upload-item.upload-error {
  border-color: #dc3545;
  background-color: #fff8f8;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.upload-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.upload-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.upload-details {
  flex: 1;
}

.upload-filename {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.upload-meta {
  font-size: 0.875rem;
  color: #6c757d;
}

.upload-actions {
  display: flex;
  gap: 0.25rem;
}

.upload-progress .progress {
  height: 6px;
  margin-bottom: 0.5rem;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
}

.upload-status {
  margin-top: 0.25rem;
}

.status-message {
  font-size: 0.875rem;
  font-weight: 500;
}

.upload-actions-bar {
  border-top: 1px solid #dee2e6;
  padding-top: 0.75rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.overall-progress .progress {
  height: 8px;
}
</style>
