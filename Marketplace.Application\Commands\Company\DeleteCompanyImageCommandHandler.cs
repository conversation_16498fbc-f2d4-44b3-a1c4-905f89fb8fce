using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.Company;

/// <summary>
/// Обробник команди для видалення основного зображення компанії
/// </summary>
public class DeleteCompanyImageCommandHandler : IRequestHandler<DeleteCompanyImageCommand, bool>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<DeleteCompanyImageCommandHandler> _logger;

    public DeleteCompanyImageCommandHandler(
        ICompanyRepository companyRepository,
        IFileService fileService,
        ILogger<DeleteCompanyImageCommandHandler> logger)
    {
        _companyRepository = companyRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteCompanyImageCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Отримуємо компанію
            var company = await _companyRepository.GetByIdAsync(request.CompanyId, cancellationToken);
            if (company == null)
            {
                _logger.LogWarning($"Компанію з ID {request.CompanyId} не знайдено.");
                return false;
            }

            // Перевіряємо, чи є зображення для видалення
            if (company.Image == null || string.IsNullOrEmpty(company.Image.Value))
            {
                _logger.LogInformation($"У компанії {request.CompanyId} немає зображення для видалення.");
                return true; // Вважаємо це успішним результатом
            }

            // Видаляємо файл з файлової системи
            try
            {
                await _fileService.DeleteFileAsync(company.Image.Value, cancellationToken);
                _logger.LogInformation($"Файл зображення {company.Image.Value} успішно видалено.");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Не вдалося видалити файл {company.Image.Value}: {ex.Message}");
                // Продовжуємо виконання, щоб очистити посилання в БД
            }

            // Очищуємо посилання на зображення в БД
            company.UpdateImage(null);

            // Зберігаємо зміни
            await _companyRepository.UpdateAsync(company, cancellationToken);

            _logger.LogInformation($"Зображення компанії {request.CompanyId} успішно видалено.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Помилка при видаленні зображення компанії {request.CompanyId}");
            return false;
        }
    }
}
