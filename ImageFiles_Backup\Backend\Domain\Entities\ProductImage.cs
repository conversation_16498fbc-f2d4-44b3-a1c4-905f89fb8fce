﻿using Marketplace.Domain.ValueObjects;

namespace Marketplace.Domain.Entities;

public class ProductImage : IEntity
{
    private Url _image;
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public Product Product { get; set; }
    public int Order { get; set; }
    public bool IsMain { get; set; }
    public string? AltText { get; set; }
    public DateTime CreatedAt { get; set; }
    public Url Image
    {
        get => _image;
        set => UpdateImage(value);
    }

    private ProductImage() { }

    public ProductImage(Guid productId, Url image, int order = 0, bool isMain = false, string? altText = null)
    {
        ValidateCreation(productId, image);

        Id = Guid.NewGuid();
        ProductId = productId;
        Image = image;
        Order = order;
        IsMain = isMain;
        AltText = altText;
        CreatedAt = DateTime.UtcNow;
    }

    public void Update(Url? image = null, int? order = null, bool? isMain = null, string? altText = null)
    {
        if (image != null) UpdateImage(image);
        if (order != null) UpdateOrder(order.Value);
        if (isMain != null) UpdateIsMain(isMain.Value);
        if (altText != null) UpdateAltText(altText);
    }


    private void UpdateOrder(int order)
    {
        Order = order;
    }

    private void UpdateIsMain(bool isMain)
    {
        IsMain = isMain;
    }

    private void UpdateAltText(string? altText)
    {
        AltText = altText;
    }

    private void UpdateImage(Url image)
    {
        _image = image ?? throw new ArgumentNullException(nameof(image));
    }

    private void ValidateCreation(Guid productId, Url image)
    {
        if (productId == Guid.Empty) throw new ArgumentNullException(nameof(productId));
        if (image == null) throw new ArgumentNullException(nameof(image));
    }
}
