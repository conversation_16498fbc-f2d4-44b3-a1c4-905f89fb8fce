import axios from 'axios';

class NovaPoshtaService {
  constructor() {
    // API ключ Нової Пошти (в реальному проекті має бути в .env файлі)
    this.apiKey = '44e5c8e5e4b1329def640dc95fb5aece'; // Тестовий ключ
    this.baseURL = 'https://api.novaposhta.ua/v2.0/json/';

    // Демо-дані для fallback
    this.demoCities = [
      { ref: 'e71abb60-4b33-11e4-ab6d-005056801329', description: 'Київ', area: 'Київська область' },
      { ref: 'db5c88e0-391c-11dd-90d9-001a92567626', description: 'Харків', area: 'Харківська область' },
      { ref: 'db5c88de-391c-11dd-90d9-001a92567626', description: 'Одеса', area: 'Одеська область' },
      { ref: 'db5c88d0-391c-11dd-90d9-001a92567626', description: 'Дніпро', area: 'Дніпропетровська область' },
      { ref: 'db5c88ce-391c-11dd-90d9-001a92567626', description: 'Донецьк', area: 'Донецька область' },
      { ref: 'db5c88f4-391c-11dd-90d9-001a92567626', description: 'Запоріжжя', area: 'Запорізька область' },
      { ref: 'db5c88d2-391c-11dd-90d9-001a92567626', description: 'Львів', area: 'Львівська область' },
      { ref: 'db5c88d4-391c-11dd-90d9-001a92567626', description: 'Кривий Ріг', area: 'Дніпропетровська область' },
      { ref: 'db5c88d6-391c-11dd-90d9-001a92567626', description: 'Миколаїв', area: 'Миколаївська область' },
      { ref: 'db5c88d8-391c-11dd-90d9-001a92567626', description: 'Маріуполь', area: 'Донецька область' },
      { ref: 'db5c88da-391c-11dd-90d9-001a92567626', description: 'Луганськ', area: 'Луганська область' },
      { ref: 'db5c88dc-391c-11dd-90d9-001a92567626', description: 'Вінниця', area: 'Вінницька область' },
      { ref: 'db5c88e2-391c-11dd-90d9-001a92567626', description: 'Макіївка', area: 'Донецька область' },
      { ref: 'db5c88e4-391c-11dd-90d9-001a92567626', description: 'Сімферополь', area: 'АР Крим' },
      { ref: 'db5c88e6-391c-11dd-90d9-001a92567626', description: 'Херсон', area: 'Херсонська область' },
      { ref: 'db5c88e8-391c-11dd-90d9-001a92567626', description: 'Полтава', area: 'Полтавська область' },
      { ref: 'db5c88ea-391c-11dd-90d9-001a92567626', description: 'Чернігів', area: 'Чернігівська область' },
      { ref: 'db5c88ec-391c-11dd-90d9-001a92567626', description: 'Черкаси', area: 'Черкаська область' },
      { ref: 'db5c88ee-391c-11dd-90d9-001a92567626', description: 'Житомир', area: 'Житомирська область' },
      { ref: 'db5c88f0-391c-11dd-90d9-001a92567626', description: 'Суми', area: 'Сумська область' }
    ];
  }

  /**
   * Отримує список міст
   * @param {string} cityName - Назва міста для пошуку
   * @returns {Promise} - Список міст
   */
  async getCities(cityName = '') {
    try {
      // Спочатку спробуємо реальний API
      try {
        const response = await axios.post(this.baseURL, {
          apiKey: this.apiKey,
          modelName: 'Address',
          calledMethod: 'getCities',
          methodProperties: {
            FindByString: cityName,
            Limit: 20
          }
        });

        if (response.data.success && response.data.data) {
          return response.data.data.map(city => ({
            ref: city.Ref,
            description: city.Description,
            descriptionRu: city.DescriptionRu,
            area: city.Area,
            areaDescription: city.AreaDescription
          }));
        }
      } catch (apiError) {
        console.warn('Nova Poshta API недоступний, використовуємо демо-дані:', apiError.message);
      }

      // Fallback на демо-дані
      let filteredCities = this.demoCities;

      if (cityName && cityName.length > 0) {
        const searchTerm = cityName.toLowerCase();
        filteredCities = this.demoCities.filter(city =>
          city.description.toLowerCase().includes(searchTerm)
        );
      }

      // Симулюємо затримку API
      await new Promise(resolve => setTimeout(resolve, 300));

      return filteredCities.map(city => ({
        ref: city.ref,
        description: city.description,
        areaDescription: city.area
      }));

    } catch (error) {
      console.error('Nova Poshta getCities error:', error);
      throw error;
    }
  }

  /**
   * Отримує список відділень у місті
   * @param {string} cityRef - Референс міста
   * @returns {Promise} - Список відділень
   */
  async getWarehouses(cityRef) {
    try {
      // Спочатку спробуємо реальний API
      try {
        const response = await axios.post(this.baseURL, {
          apiKey: this.apiKey,
          modelName: 'Address',
          calledMethod: 'getWarehouses',
          methodProperties: {
            CityRef: cityRef,
            Limit: 50
          }
        });

        if (response.data.success && response.data.data) {
          return response.data.data.map(warehouse => ({
            ref: warehouse.Ref,
            description: warehouse.Description,
            descriptionRu: warehouse.DescriptionRu,
            shortAddress: warehouse.ShortAddress,
            phone: warehouse.Phone,
            typeOfWarehouse: warehouse.TypeOfWarehouse,
            number: warehouse.Number,
            schedule: warehouse.Schedule
          }));
        }
      } catch (apiError) {
        console.warn('Nova Poshta API недоступний, використовуємо демо-дані:', apiError.message);
      }

      // Fallback на демо-дані відділень
      const demoWarehouses = [
        {
          ref: `${cityRef}_warehouse_1`,
          number: '1',
          shortAddress: 'вул. Центральна, 10',
          phone: '+380 44 123-45-67',
          schedule: 'Пн-Пт: 8:00-20:00, Сб-Нд: 9:00-18:00'
        },
        {
          ref: `${cityRef}_warehouse_2`,
          number: '2',
          shortAddress: 'пр. Миру, 25',
          phone: '+380 44 123-45-68',
          schedule: 'Пн-Пт: 8:00-20:00, Сб: 9:00-17:00'
        },
        {
          ref: `${cityRef}_warehouse_3`,
          number: '3',
          shortAddress: 'вул. Шевченка, 45',
          phone: '+380 44 123-45-69',
          schedule: 'Пн-Пт: 9:00-19:00, Сб-Нд: 10:00-16:00'
        },
        {
          ref: `${cityRef}_warehouse_4`,
          number: '4',
          shortAddress: 'вул. Незалежності, 78',
          phone: '+380 44 123-45-70',
          schedule: 'Пн-Сб: 8:00-20:00, Нд: 10:00-18:00'
        },
        {
          ref: `${cityRef}_warehouse_5`,
          number: '5',
          shortAddress: 'пр. Перемоги, 120',
          phone: '+380 44 123-45-71',
          schedule: 'Пн-Пт: 8:30-19:30, Сб-Нд: 9:00-17:00'
        }
      ];

      // Симулюємо затримку API
      await new Promise(resolve => setTimeout(resolve, 500));

      return demoWarehouses;

    } catch (error) {
      console.error('Nova Poshta getWarehouses error:', error);
      throw error;
    }
  }

  /**
   * Розраховує вартість доставки
   * @param {Object} params - Параметри для розрахунку
   * @returns {Promise} - Вартість доставки
   */
  async calculateDeliveryCost(params) {
    try {
      const {
        citySender,
        cityRecipient,
        weight = 1, // кг
        serviceType = 'WarehouseWarehouse', // Склад-Склад
        cost = 100, // Оціночна вартість
        cargoType = 'Cargo' // Тип вантажу
      } = params;

      const response = await axios.post(this.baseURL, {
        apiKey: this.apiKey,
        modelName: 'InternetDocument',
        calledMethod: 'getDocumentPrice',
        methodProperties: {
          CitySender: citySender,
          CityRecipient: cityRecipient,
          Weight: weight,
          ServiceType: serviceType,
          Cost: cost,
          CargoType: cargoType
        }
      });

      if (response.data.success && response.data.data.length > 0) {
        const result = response.data.data[0];
        return {
          cost: parseFloat(result.Cost),
          costRedelivery: parseFloat(result.CostRedelivery || 0),
          assessedCost: parseFloat(result.AssessedCost || 0),
          costPack: parseFloat(result.CostPack || 0)
        };
      }
      
      throw new Error('Помилка розрахунку вартості доставки Нової Пошти');
    } catch (error) {
      console.error('Nova Poshta calculateDeliveryCost error:', error);
      // Повертаємо стандартну ціну у випадку помилки
      return { cost: 50, costRedelivery: 0, assessedCost: 0, costPack: 0 };
    }
  }

  /**
   * Отримує інформацію про відстеження посилки
   * @param {string} documentNumber - Номер накладної
   * @returns {Promise} - Інформація про відстеження
   */
  async trackDocument(documentNumber) {
    try {
      const response = await axios.post(this.baseURL, {
        apiKey: this.apiKey,
        modelName: 'TrackingDocument',
        calledMethod: 'getStatusDocuments',
        methodProperties: {
          Documents: [
            {
              DocumentNumber: documentNumber,
              Phone: ''
            }
          ]
        }
      });

      if (response.data.success && response.data.data.length > 0) {
        const tracking = response.data.data[0];
        return {
          number: tracking.Number,
          status: tracking.Status,
          statusCode: tracking.StatusCode,
          city: tracking.CityRecipient,
          warehouse: tracking.WarehouseRecipient,
          actualDeliveryDate: tracking.ActualDeliveryDate,
          estimatedDeliveryDate: tracking.EstimatedDeliveryDate
        };
      }
      
      throw new Error('Документ не знайдено');
    } catch (error) {
      console.error('Nova Poshta trackDocument error:', error);
      throw error;
    }
  }

  /**
   * Форматує адресу відділення
   * @param {Object} warehouse - Об'єкт відділення
   * @returns {string} - Форматована адреса
   */
  formatWarehouseAddress(warehouse) {
    return `№${warehouse.number} - ${warehouse.shortAddress}`;
  }

  /**
   * Перевіряє доступність API
   * @returns {Promise<boolean>} - Статус доступності
   */
  async checkApiAvailability() {
    try {
      await this.getCities('Київ');
      return true;
    } catch (error) {
      console.warn('Nova Poshta API недоступний:', error.message);
      return false;
    }
  }
}

export default new NovaPoshtaService();
