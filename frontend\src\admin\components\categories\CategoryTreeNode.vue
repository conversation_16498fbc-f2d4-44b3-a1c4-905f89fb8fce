<template>
  <ul class="tree-children">
    <li v-for="category in categories" :key="category.id" class="tree-item">
      <div class="tree-node">
        <div class="node-content">
          <span class="icon node-toggle" @click="toggleNode(category.id)">
            <i 
              v-if="category.children && category.children.length > 0" 
              class="fas" 
              :class="expandedNodes.includes(category.id) ? 'fa-chevron-down' : 'fa-chevron-right'">
            </i>
            <i v-else class="fas fa-circle fa-xs"></i>
          </span>
          <figure class="image is-24x24 mr-2">
            <img 
              :src="category.image || 'https://via.placeholder.com/24'" 
              :alt="category.name"
              @error="handleImageError">
          </figure>
          <span class="node-name">{{ category.name }}</span>
          <span class="node-count">({{ category.productCount || 0 }})</span>
        </div>
        <div class="node-actions">
          <div class="buttons are-small">
            <router-link 
              :to="`/admin/categories/${category.id}`" 
              class="button is-info is-small" 
              title="View">
              <span class="icon is-small">
                <i class="fas fa-eye"></i>
              </span>
            </router-link>
            <router-link 
              :to="`/admin/categories/${category.id}/edit`" 
              class="button is-primary is-small" 
              title="Edit">
              <span class="icon is-small">
                <i class="fas fa-edit"></i>
              </span>
            </router-link>
            <button 
              class="button is-danger is-small" 
              @click="deleteCategory(category)"
              title="Delete">
              <span class="icon is-small">
                <i class="fas fa-trash"></i>
              </span>
            </button>
          </div>
        </div>
      </div>
      <div v-if="expandedNodes.includes(category.id) && category.children && category.children.length > 0">
        <category-tree-node 
          :categories="category.children" 
          :expanded-nodes="expandedNodes"
          @toggle-node="toggleNode"
          @delete-category="deleteCategory" />
      </div>
    </li>
  </ul>
</template>

<script setup>
const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  expandedNodes: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['toggle-node', 'delete-category']);

// Toggle node expansion
const toggleNode = (categoryId) => {
  emit('toggle-node', categoryId);
};

// Delete category
const deleteCategory = (category) => {
  emit('delete-category', category);
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/24?text=No+Image';
};
</script>

<style scoped>
.tree-children {
  list-style: none;
  padding-left: 2rem;
  margin-top: 0.5rem;
}

.tree-item {
  margin-bottom: 0.5rem;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.tree-node:hover {
  background-color: #f5f5f5;
}

.node-content {
  display: flex;
  align-items: center;
}

.node-toggle {
  cursor: pointer;
  width: 20px;
  display: flex;
  justify-content: center;
}

.node-name {
  font-weight: 500;
  margin-right: 0.5rem;
}

.node-count {
  color: #7a7a7a;
  font-size: 0.9rem;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.mr-2 {
  margin-right: 0.5rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}
</style>
