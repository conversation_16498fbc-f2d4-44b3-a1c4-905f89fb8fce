﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Marketplace.Infrastructure.Persistence.Repositories;

public class ProductImageRepository : Repository<ProductImage>, IProductImageRepository
{
    private readonly DbContext _context;
    private readonly DbSet<ProductImage> _dbSet;

    public ProductImageRepository(MarketplaceDbContext context) : base(context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _dbSet = context.Set<ProductImage>();
    }

    public async Task<List<ProductImage>> GetByProductIdAsync(Guid productId, CancellationToken cancellationToken, params Expression<Func<ProductImage, object>>[] includes)
    {
        IQueryable<ProductImage> query = _dbSet;
        foreach (var include in includes)
        {
            query = query.Include(include);
        }
        return await query.Where(e => EF.Property<Guid>(e, "ProductId") == productId).ToListAsync(cancellationToken);
    }
}