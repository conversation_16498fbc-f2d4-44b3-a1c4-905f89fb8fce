import{q as c}from"./index-L-hJxM_5.js";const d=new Map,p=5*60*1e3,l=(e,o)=>{const t=Object.keys(o||{}).sort().map(r=>`${r}=${o[r]}`).join("&");return`${e}?${t}`},f=e=>e&&Date.now()-e.timestamp<p,g=async(e,o)=>{const t=d.get(e);if(f(t))return console.log("Returning cached data for:",e),t.data;console.log("Fetching fresh data for:",e);const r=await o();return d.set(e,{data:r,timestamp:Date.now()}),r},u=e=>{if(e)for(const o of d.keys())o.includes(e)&&d.delete(o);else d.clear()},m={async getCompanies(e={}){var o,t,r;try{console.log("Requesting companies with params:",e);const a={};e.page&&(a.page=e.page),e.pageSize&&(a.pageSize=e.pageSize),e.sortBy?a.orderBy=e.sortBy:e.orderBy&&(a.orderBy=e.orderBy),e.sortOrder?a.descending=e.sortOrder==="desc":e.descending!==void 0&&(a.descending=e.descending),e.search&&e.search.trim()!==""?a.filter=e.search.trim():e.filter&&e.filter.trim()!==""&&(a.filter=e.filter.trim()),e.featured&&e.featured.trim()!==""&&e.featured!=="all"&&(a.isFeatured=e.featured==="true"),console.log("Final API params for companies:",a);const i=await c.get("/api/admin/companies",{params:a});if(console.log("Admin companies API response:",i.data),console.log("API params sent:",a),i.data&&i.data.success&&i.data.data)return console.log("Returning paginated data:",i.data.data),console.log("Total items:",i.data.data.total),console.log("Current page:",i.data.data.currentPage),console.log("Total pages:",i.data.data.lastPage),console.log("Items count:",(o=i.data.data.data)==null?void 0:o.length),i.data.data;throw console.error("Invalid response format:",i.data),new Error("Invalid response format from server")}catch(a){throw console.error("Error fetching companies:",a),new Error(((r=(t=a.response)==null?void 0:t.data)==null?void 0:r.message)||"Failed to load companies")}},async getPendingCompanies(e={}){var o,t;try{console.log("Requesting pending companies with params:",e);const r={};e.page&&(r.page=e.page),e.pageSize&&(r.pageSize=e.pageSize),e.orderBy&&(r.orderBy=e.orderBy),e.descending!==void 0&&(r.descending=e.descending),e.search&&e.search.trim()!==""?r.filter=e.search.trim():e.filter&&e.filter.trim()!==""&&(r.filter=e.filter.trim()),console.log("Final API params for pending companies:",r);const a=await c.get("/api/admin/companies/pending",{params:r});return console.log("Pending companies API response:",a.data),a.data}catch(r){throw console.error("Error fetching pending companies:",r),new Error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to load pending companies")}},async getCompanyById(e){var o,t;try{return(await c.get(`/api/admin/companies/${e}`)).data}catch(r){throw console.error("Error fetching company:",r),new Error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to load company details")}},async approveCompany(e){var o,t;try{return(await c.post(`/api/admin/companies/${e}/approve`)).data}catch(r){throw console.error("Error approving company:",r),new Error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to approve company")}},async rejectCompany(e,o=""){var t,r;try{return(await c.post(`/api/admin/companies/${e}/reject`,{reason:o})).data}catch(a){throw console.error("Error rejecting company:",a),new Error(((r=(t=a.response)==null?void 0:t.data)==null?void 0:r.message)||"Failed to reject company")}},async deleteCompany(e){var o,t;try{return(await c.delete(`/api/admin/companies/${e}`)).data}catch(r){throw console.error("Error deleting company:",r),new Error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to delete company")}},async getCompany(e){var o,t;try{return(await c.get(`/api/admin/companies/${e}`)).data}catch(r){throw console.error("Error getting company:",r),new Error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to get company")}},async getDetailedCompany(e){var o,t;try{return(await c.get(`/api/admin/companies/${e}/detailed`)).data}catch(r){throw console.error("Error getting detailed company:",r),new Error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.message)||"Failed to get detailed company")}},async updateCompany(e,o){var t,r;try{return(await c.put(`/api/admin/companies/${e}`,o)).data}catch(a){throw console.error("Error updating company:",a),new Error(((r=(t=a.response)==null?void 0:t.data)==null?void 0:r.message)||"Failed to update company")}},async updateDetailedCompany(e,o){var t,r;try{return(await c.put(`/api/admin/companies/${e}/detailed`,o)).data}catch(a){throw console.error("Error updating detailed company:",a),new Error(((r=(t=a.response)==null?void 0:t.data)==null?void 0:r.message)||"Failed to update detailed company")}},async getCompanyUsers(e,o={}){const t=l(`/api/admin/companies/${e}/users`,o);return g(t,async()=>{var r,a,i;try{console.log("Requesting company users with params:",o);const n={};o.page&&(n.page=o.page),o.pageSize&&(n.pageSize=o.pageSize),o.orderBy&&(n.orderBy=o.orderBy),o.descending!==void 0&&(n.descending=o.descending),o.search&&o.search.trim()!==""?n.filter=o.search.trim():o.filter&&o.filter.trim()!==""&&(n.filter=o.filter.trim()),console.log("Final API params for company users:",n);const s=await c.get(`/api/admin/companies/${e}/users`,{params:n});if(console.log("Admin company users API response:",s.data),s.data&&s.data.success&&s.data.data)return console.log("Returning paginated users data:",s.data.data),console.log("Total users:",s.data.data.total),console.log("Current page:",s.data.data.currentPage),console.log("Total pages:",s.data.data.lastPage),console.log("Users count:",(r=s.data.data.data)==null?void 0:r.length),s.data;throw console.error("Invalid response format for users:",s.data),new Error("Invalid response format from server")}catch(n){throw console.error("Error getting company users:",n),new Error(((i=(a=n.response)==null?void 0:a.data)==null?void 0:i.message)||"Failed to get company users")}})},async getCompanyProducts(e,o={}){const t=l(`/api/admin/companies/${e}/products`,o);return g(t,async()=>{var r,a,i;try{console.log("Requesting company products with params:",o);const n={};o.page&&(n.page=o.page),o.pageSize&&(n.pageSize=o.pageSize),o.orderBy&&(n.orderBy=o.orderBy),o.descending!==void 0&&(n.descending=o.descending),o.search&&o.search.trim()!==""?n.filter=o.search.trim():o.filter&&o.filter.trim()!==""&&(n.filter=o.filter.trim()),console.log("Final API params for company products:",n);const s=await c.get(`/api/admin/companies/${e}/products`,{params:n});if(console.log("Admin company products API response:",s.data),s.data&&s.data.success&&s.data.data)return console.log("Returning paginated products data:",s.data.data),console.log("Total products:",s.data.data.total),console.log("Current page:",s.data.data.currentPage),console.log("Total pages:",s.data.data.lastPage),console.log("Products count:",(r=s.data.data.data)==null?void 0:r.length),s.data;throw console.error("Invalid response format for products:",s.data),new Error("Invalid response format from server")}catch(n){throw console.error("Error getting company products:",n),new Error(((i=(a=n.response)==null?void 0:a.data)==null?void 0:i.message)||"Failed to get company products")}})},clearCache(e){u(e)}};export{m as c};
