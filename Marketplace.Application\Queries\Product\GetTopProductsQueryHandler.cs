using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Product;

public class GetTopProductsQueryHandler : IRequestHandler<GetTopProductsQuery, List<ProductResponse>>
{
    private readonly IProductRepository _productRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IMapper _mapper;

    public GetTopProductsQueryHandler(
        IProductRepository productRepository,
        ICategoryRepository categoryRepository,
        IMapper mapper)
    {
        _productRepository = productRepository;
        _categoryRepository = categoryRepository;
        _mapper = mapper;
    }

    public async Task<List<ProductResponse>> Handle(GetTopProductsQuery request, CancellationToken cancellationToken)
    {
        // Створюємо фільтр
        Expression<Func<Domain.Entities.Product, bool>>? filter = null;

        // Якщо вказано категорію, фільтруємо по ній
        if (!string.IsNullOrEmpty(request.CategorySlug))
        {
            var category = await _categoryRepository.GetBySlugAsync(request.CategorySlug, cancellationToken);
            if (category != null)
            {
                if (!string.IsNullOrEmpty(request.Status) && int.TryParse(request.Status, out var statusValue))
                {
                    filter = p => p.CategoryId == category.Id && (int)p.Status == statusValue;
                }
                else
                {
                    filter = p => p.CategoryId == category.Id;
                }
            }
        }
        else if (!string.IsNullOrEmpty(request.Status) && int.TryParse(request.Status, out var statusValue))
        {
            filter = p => (int)p.Status == statusValue;
        }

        // Отримуємо топ товари
        var products = await _productRepository.GetAllAsync(
            filter: filter,
            orderBy: request.OrderBy ?? "Sales",
            descending: request.Descending,
            page: 1,
            pageSize: request.Limit ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Product, object>>[] { p => p.Category, p => p.Company }
        );

        return _mapper.Map<List<ProductResponse>>(products);
    }
}
